import React, { Component } from "react";
import { Row, Col, Button, Modal, Input, Radio, message as Message } from "antd";
import "./location-modal.less";

import propTypes from "prop-types";
const RadioGroup = Radio.Group;

class LocationModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ls: null,
      searchResult: [],
      keyword: "",
      current: {}
    }
  }
  componentWillReceiveProps(props) {
    const { inputData } = this.props;
    if (inputData && inputData.lat && inputData.lng && inputData.address) {
      this.setState({
        current: inputData,
        keyword: inputData.address
      });
    }
  }
  componentDidMount() {
    const { inputData } = this.props;
    if (inputData && inputData.lat && inputData.lng && inputData.address) {
      this.setState({
        current: inputData,
        keyword: inputData.address
      });
    }
    if (window) {
      window.mapReady = () => {
        // console.log("地图就绪");
        if (this.iframe && this.iframe.contentWindow) {
          const { inputData } = this.props;
          const ls = this.iframe.contentWindow.ls;
          this.setState({
            ls
          });

          if (inputData && inputData.lat && inputData.lng && inputData.address) {
            ls.searchByKeyword(inputData.address);
            ls.setPoint({ lng: inputData.lng, lat: inputData.lat });
          }
        }
      }
      window.searchLocation = (data) => {
        // console.log("查询结果", data);
        if (data.type === "complete" && data.info === "OK") {
          this.setState({
            searchResult: data.poiList.pois || []
          });
        } else {
          // console.log(data);
          Message.error("找不到相关地址");
          this.setState({
            searchResult: []
          });
        }
      }
    }
  }
  changeKeywordHandler(e) {
    const target = e.target;
    // console.log(target);
    this.setState({
      keyword: target.value
    });
  }
  searchByKeyword() {
    const { ls, keyword } = this.state;
    if (keyword) {
      ls.searchByKeyword(keyword);
    }
  }
  selectLocation(e) {
    // console.log(e)
    const { ls } = this.state;
    const target = e.target;
    if (target.data) {
      const data = target.data;
      const location = data.location || {};
      // console.log(data);
      ls.setPoint(location);
      this.setState({
        current: data,
        keyword: `${data.name}${data.address}`
      });
    }
  }
  saveHandler() {
    // console.log("保存");
    const { onChange, hideModal } = this.props;
    const { current } = this.state;
    onChange(current);
    hideModal();
  }
  cleanHandler() {
    // console.log("清空");
    const { ls } = this.state;
    this.setState({
      current: {},
      searchResult: [],
      keyword: ""
    });
    ls.cleanKeyword();
  }
  render() {
    const { searchResult, keyword } = this.state;
    const { visible, hideModal } = this.props;
    const renderSearchResult = (searchResult) => {
      if (searchResult && Array.isArray(searchResult) && searchResult.length !== 0) {
        // return searchResult.map((item, index) => {

        // });
        return (
          <RadioGroup onChange={(e) => this.selectLocation(e)}>
            {
              searchResult.map((item, index) => {
                return (
                  <div key={index}>
                    {renderResultItem(item)}
                  </div>
                )
              })
            }
          </RadioGroup>
        )
      } else {
        return (
          <div className="no-data">
            暂无数据
          </div>
        )
      }
    }
    const renderResultItem = (data) => {
      return (
        <div className="result-item-wrapper">
          <Radio value={data.id} data={data}>
            {data.name}
          </Radio>
          <div className="item-detaile">{data.address}</div>
        </div>
      )
    }
    const modalProps = {
      maskClosable: true,
      onCancel: hideModal,
      title: "地图定位",
      width: 700,
      visible,
      footer: null,
      wrapClassName: "location-modal"
    }
    return (
      <Modal {...modalProps}>
        <div className="search-wrapper">
          <Row type="flex" align="middle">
            <Col span={11}>
              <Input placeholder="请输入地址查询"
                value={keyword}
                onPressEnter={() => this.searchByKeyword()}
                onChange={(e) => this.changeKeywordHandler(e)}
              />
            </Col>
            <Col style={{ marginLeft: "16px" }} span={2}>
              <Button type="primary" onClick={() => this.searchByKeyword()}>查询</Button>
            </Col>
          </Row>
        </div>
        <div className="map-container">
          <iframe
            className="map-wrapper"
            ref={ref => this.iframe = ref}
            src="/plugin/location-selector" frameBorder="0"></iframe>
          <div className="list-wrapper">
            {
              renderSearchResult(searchResult)
            }
          </div>
        </div>
        <div className="buttons-wrapper">
          <Button type="primary" onClick={() => this.saveHandler()}>保存</Button>
          <Button onClick={() => this.cleanHandler()} > 清空</Button>
        </div>
      </Modal>
    )
  }
}

LocationModal.propTypes = {
  visible: propTypes.bool,
  hideModal: propTypes.func,
  onChange: propTypes.func
}

LocationModal.defaultProps = {
  visible: false,
  hideModal: () => { },
  onChange: () => { }
}

export default LocationModal;


