

import React, { Component } from "react";
import PropTypes from "prop-types";
import { Button, Modal, Form, Tabs, message } from "antd";

const TabPane = Tabs.TabPane;
import { connect } from 'dva';
import SearchHeader from 'components/search-header';

import MatrixList from "./matrix-list";
import SendList from "./send-list";
import ReceiveList from "./receive-list";

import { fetch, fetchList, fetchOP } from "tool/axios";

import ColumnSelector from "./components/column-selector";
import "./index.less";

const propTypes = {
  activitysMatrix: PropTypes.object,
  dispatch: PropTypes.func,
  form: PropTypes.object,
  // foo : PropTypes.string
};
const defaultProps = {
  // foo : "barrrr"
};

//  Form.create({})(
export default connect(({ activitysMatrix }) => ({ activitysMatrix }))(

  Object.assign(class ActivitysMatrix extends Component {

    constructor(props) {
      super(props);
      this.state = {
        columnSelectorVisible: false,
        currentRow: null
      }
      this.changeTabs = this.changeTabs.bind(this);
      this.cloneNewActivity = this.cloneNewActivity.bind(this);
      this.setVisibility = this.setVisibility.bind(this);
    }


    changeTabs(key) {
      const { dispatch } = this.props;
      dispatch({
        type: 'activitysMatrix/update', payload: {
          list_type: parseInt(key)
        }
      })
      dispatch({ type: 'activitysMatrix/getMatrixList' })
    }

    componentDidMount() {
      const { dispatch } = this.props;
      dispatch({ type: 'activitysMatrix/getMatrixList' })
    }


    cloneNewActivity(activity) {
      const { currentRow } = this.state;
      const { history } = this.props;
      const { type, activity_id } = activity;

      const pathMap = {
        1: 'new-vote',
        2: 'questionnaire',
        3: 'contest',
        4: 'physical',
        5: 'contribute'
      }
      if (pathMap[type]) {
        history.push({
          pathname: pathMap[type],
          query: { id: activity_id, action: 'clone' }
        });
      }

      // console.log("发起同类互动：", activity_column_id, activity)
    }

    //参数当前互动数据，是否显示（1|0），栏目ID
    setVisibility(data, visibility, cid) {
      if (cid === 0) {
        message.error('请选择一个栏目');
        return;
      }
      const { dispatch } = this.props;
      dispatch({
        type: 'activitysMatrix/show', payload: {
          activity_push_id: data.activity_push_id,
          visibility,
          cid
        }
      }).then(data => {
        fetchOP(data);
        dispatch({ type: 'activitysMatrix/getMatrixList' })
        this.setState({
          columnSelectorVisible: false
        })
      })

    }

    render() {
      const { dispatch, activitysMatrix, history } = this.props;


      //点击互动列表编辑功能
      const editActivity = (row) => {
        // let currentUserId = window ? parseInt(window.sessionStorage.getItem('_uid')) : '';
        let isPreview = 1,
          queryObject = { id: row.activity_id, isPreview, action: 'view' };
        // isMyActivity(row.create_user) &&
        // (
        //     (row.type !== 4 && row.status !== 1 && row.status !== 4) ||
        //     (row.type === 4 && row.status !== 4)
        // ) ? '编辑' : '查看'
        // if (currentUserId !== row.create_user || (row.type !== 4 && (row.status === 1 || row.status === 4)) || (row.type !== 4 && row.status === 4)) {
        //     // message.error('当前互动不是你发起的，只能预览，无法编辑');
        //     queryObject.isPreview = isPreview;
        //     // return;
        // }

        if (row.type === 1) {
          history.push({
            pathname: 'new-vote',
            query: queryObject
          });
        } else if (row.type === 2) {
          history.push({
            pathname: 'questionnaire',
            query: queryObject
          })
        } else if (row.type === 3) {
          history.push({
            pathname: 'contest',
            query: queryObject
          })
        } else if (row.type === 4) {
          history.push({
            pathname: 'physical',
            query: queryObject
          })
        } else if (row.type === 5) {
          history.push({
            pathname: 'contribute',
            query: queryObject
          })

        }
      };

      const showColumnSelector = (rowData => {
        this.setState({
          currentRow: rowData,
          columnSelectorVisible: true
        })
      }).bind(this);

      //判断互动是否为当前登录用户发布，若是，显示编辑；若不是，显示查看
      const isMyActivity = (user_id) => {
        let currentUserId = window ? parseInt(window.sessionStorage.getItem('_uid')) : '';
        return currentUserId === user_id;
      }

      const listProps = {
        activitysMatrix,
        dispatch,
        history,
        editActivity,
        isMyActivity,
        showColumnSelector,
        setVisibility: this.setVisibility,
        cloneNewActivity: this.cloneNewActivity
      }

      // console.log(activitysMatrix.list_type)

      return (
        <div className="matrix-list-panel">

          <SearchHeader>
            <div className='header'>互动矩阵</div>
          </SearchHeader>
          <div className="main">

            <div style={{ display: 'none' }}>&nbsp;</div>

            <Tabs size="large" defaultActiveKey={activitysMatrix.list_type + ""} onChange={this.changeTabs}>
              <TabPane tab="推送列表" key="1">
                <SendList {...listProps} />
              </TabPane>
              <TabPane tab="接收列表" key="2">
                <ReceiveList {...listProps} />
              </TabPane>
              <TabPane tab="互动矩阵" key="3">
                <MatrixList {...listProps} />
              </TabPane>
            </Tabs>


            <ColumnSelector
              currentRow={this.state.currentRow}
              visible={this.state.columnSelectorVisible}
              onChange={this.setVisibility}
              onCancel={() => this.setState({ columnSelectorVisible: false })}
            />

          </div>
        </div>
      )
    }

  }, { propTypes, defaultProps }))
