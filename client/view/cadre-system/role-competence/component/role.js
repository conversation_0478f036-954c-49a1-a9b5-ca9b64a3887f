import React, { Component } from "react";
import {
  Modal,
  Form,
  Input,
  Tree,
  Tabs,
  Button,
  Radio,
  message,
  Checkbox,
} from "antd";
const TabPane = Tabs.TabPane;
const RadioGroup = Radio.Group;
const { TreeNode } = Tree;
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
import InputTips from "components/activity-form/sub-components/InputTips";
import { addUpdateRole } from "client/apis/cadre-portrait";
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const renderTreeNodes = (data) => {
  return data.map((item) => {
    if (item.children) {
      return (
        <TreeNode title={item.title} key={item.menu_id} dataRef={item}>
          {renderTreeNodes(item.children)}
        </TreeNode>
      );
    }
    return <TreeNode {...item} />;
  });
};
const CustomizedForm = Form.create({
  name: "user-group-role",
  onFieldsChange(props, changedFields) {
    props.onValuesChange(changedFields);
  },
  mapPropsToFields(props) {
    return {
      name: Form.createFormField({
        value: props.name,
      }),
      remark: Form.createFormField({
        value: props.remark,
      }),
    };
  },
  onValuesChange(_, values) {
    console.log(values);
  },
})((props) => {
  const { getFieldDecorator, resetFields } = props.form;
  return (
    <Form>
      <Form.Item {...formItemLayout} label="角色名称">
        <InputTips max={10} text={props.name}>
          {getFieldDecorator("name", {
            initialValue: "",
            rules: [{ required: true, message: "Username is required!" }],
          })(<Input />)}
        </InputTips>
      </Form.Item>
      <Form.Item {...formItemLayout} label="描述">
        <InputTips max={20} text={props.remark}>
          {getFieldDecorator("remark", {
            initialValue: "",
            rules: [{ required: true, message: "Username is required!" }],
          })(<Input />)}
        </InputTips>
      </Form.Item>
      {true && (
        <Form.Item {...formItemLayout} label="设置权限">
          {getFieldDecorator("quanxian", {
            rules: [{ required: true, message: "Username is required!" }],
          })(
            <div>
              <RadioGroup onChange={props.onChangeRadio} value={props.belong}>
                <Radio value={1}>PC端权限设置</Radio>
              </RadioGroup>
              <div style={{ marginTop: 20 }}>
                {props.belong === 1 ? (
                  <div style={{ background: "#F7F8F9", marginTop: "-16px" }}>
                    {/* <Checkbox
                    onChange={props.checkboxOnchange("pcQuan")}
                    style={{ marginLeft: 26 }}
                    checked={props.pcQuan}
                  >
                    全选
                  </Checkbox> */}
                    <Tree
                      checkable
                      onCheck={props.onCheck}
                      checkedKeys={props.pcCheckedKeys}
                      checkStrictly
                    >
                      {renderTreeNodes(props.tree)}
                    </Tree>
                  </div>
                ) : (
                  <div style={{ background: "#F7F8F9", marginTop: "-16px" }}>
                    {/* <Checkbox
                    onChange={props.checkboxOnchange("mobileQuan")}
                    checked={props.mobileQuan}
                    style={{ marginLeft: 26 }}
                  >
                    全选
                  </Checkbox> */}
                    <Tree
                      checkable
                      onCheck={props.onCheck}
                      checkedKeys={props.mobildCheckedKeys}
                      checkStrictly
                    >
                      {renderTreeNodes(props.tree)}
                    </Tree>
                  </div>
                )}
              </div>
            </div>
          )}
        </Form.Item>
      )}
    </Form>
  );
});

export default class role extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible,
      tree: [],
      pcCheckedKeys: [],
      mobildCheckedKeys: [],
      checkedKeys: [],
      belong: 1,
      pcTreeSource: [],
      mobileTreeSource: [],
      pcQuan: false,
      mobileQuan: false,
      info: {
        name: "",
        remark: "",
      },
    };
    this.unflatten = this.unflatten.bind(this);
    this.handleCancel = this.handleCancel.bind(this);
    this.onValuesChange = this.onValuesChange.bind(this);
    this.getRoleMenuList = this.getRoleMenuList.bind(this);
    this.onCheck = this.onCheck.bind(this);
    this.onChangeRadio = this.onChangeRadio.bind(this);
    this.edit = this.edit.bind(this);
    this.save = this.save.bind(this);
    this.checkboxOnchange = this.checkboxOnchange.bind(this);
  }

  componentDidMount() {
    const info = Object.assign(this.props.activeRole);
    this.setState({
      visible: this.props.visible,
      info,
    });
    this.getMenuList({
      belong: info.belong ? info.belong : this.state.belong,
    });
    if (info.role_id) {
      this.getRoleMenuList(info.role_id);
    }
  }
  checkboxOnchange(name) {
    const self = this;
    return (e) => {
      const keys = {
        pcCheckedKeys: [],
        mobildCheckedKeys: [],
      };
      if (self.state.belong == 1) {
        if (e.target.checked) {
          keys.pcCheckedKeys = Object.assign(self.state.tree).map(
            (d) => d.menu_id
          );
        } else {
          keys.pcCheckedKeys = [];
        }
      } else {
        if (e.target.checked) {
          keys.mobildCheckedKeys = Object.assign(self.state.tree).map(
            (d) => d.menu_id
          );
        } else {
          keys.mobildCheckedKeys = [];
        }
      }
      this.setState(
        {
          ...keys,
          [name]: e.target.checked,
        },
        () => {
          console.log(this.state);
        }
      );
    };
  }
  onChangeRadio(e) {
    const belong = e.target.value;
    this.setState(
      {
        belong,
        pcQuan: false,
        mobileQuan: false,
        pcCheckedKeys: [],
        mobildCheckedKeys: [],
      },
      () => {
        this.getMenuList({ belong });
        belong === this.state.info.belong &&
          this.getRoleMenuList(this.state.info.role_id);
      }
    );
  }
  onCheck(checkedKeys, e) {
    const keys = {
      pcCheckedKeys: [],
      mobildCheckedKeys: [],
    };
    if (this.state.belong == 1) {
      keys.pcCheckedKeys = checkedKeys.checked;
    } else {
      keys.mobildCheckedKeys = checkedKeys.checked;
    }
    this.setState({
      ...keys,
    });
  }
  /**
   * 对象转为数组
   * @param {object} arr
   */
  unflatten(arr) {
    let tree = [],
      mappedArr = {},
      arrElem,
      mappedElem;

    // First map the nodes of the array to an object -> create a hash table.
    for (let i = 0, len = arr.length; i < len; i++) {
      arrElem = arr[i];
      mappedArr[arrElem["menu_id"]] = arrElem;
      mappedArr[arrElem["menu_id"]]["title"] = arrElem.name;
      mappedArr[arrElem["menu_id"]]["children"] = [];
    }
    for (let id in mappedArr) {
      if (mappedArr.hasOwnProperty(id)) {
        mappedElem = mappedArr[id];
        // If the element is not at the root level, add it to its parent array of children.
        if (
          mappedElem["parent_id"] &&
          mappedArr.hasOwnProperty(mappedElem["parent_id"])
        ) {
          mappedArr[mappedElem["parent_id"]]["children"].push(mappedElem);
        }
        // If the element is at the root level, add it to first level elements array.
        else {
          tree.push(mappedElem);
        }
      }
    }
    return tree;
  }
  /**
   * 根据传递的参数来拉取pc端还是手机端的权限树,
   * belong 1 PC端
   * belong 2 移动端
   * @param {object} value
   */
  getMenuList(value) {
    http.get(`${host}/uc/menu/all`, value).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(_data.message);
      }
      if (value.belong == 1) {
        this.setState(
          {
            tree: this.unflatten(_data.data),
            pcTreeSource: _data.data,
          },
          () => {
            console.log(this.state);
          }
        );
      } else {
        this.setState(
          {
            tree: this.unflatten(_data.data),
            mobileTreeSource: _data.data,
          },
          () => {
            console.log(this.state);
          }
        );
      }
    });
  }

  edit() {
    const postData = {
      name: this.state.info.name,
      role_id: this.state.info.role_id,
      menu_list:
        this.state.belong == 1
          ? this.state.pcCheckedKeys
          : this.state.mobildCheckedKeys,
      belong: this.state.belong,
      remark: this.state.info.remark,
    };
    http.post(`${host}/uc/role/update-role`, postData).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        message.success("保存成功");
        this.handleCancel();
      }
    });
  }
  save() {
    const postData = {
      role_id: this.state.info.role_id,
      name: this.state.info.name,
      menu_list:
        this.state.belong == 1
          ? this.state.pcCheckedKeys
          : this.state.mobildCheckedKeys,
      belong: this.state.belong,
      remark: this.state.info.remark,
    };
    // console.log(this.props);
    addUpdateRole(postData).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        message.success(this.state.info.role_id ? "编辑成功" : "新增成功!");

        this.props.reload();

        this.handleCancel();
      }
    });
  }
  getRoleMenuList(rid) {
    http
      .get(`${host}/uc/role/find-menu-by-role`, { role_id: rid })
      .then((d) => {
        const _data = d.data;
        if (_data.code != 0) {
          message.error(d.message);
        }
        const checkedKeys = {
          pcCheckedKeys: [],
          mobildCheckedKeys: [],
        };
        if (_data.data.belong == 1) {
          checkedKeys.pcCheckedKeys = _data.data.menu_list;
        } else {
          checkedKeys.mobildCheckedKeys = _data.data.menu_list;
        }
        this.setState({
          ...checkedKeys,
          belong: _data.data.belong || 1,
        });
      });
  }
  onValuesChange(data) {
    const info = Object.assign(this.state.info);
    Object.keys(data).forEach((key) => {
      info[key] = data[key].value;
    });
    this.setState({
      info,
    });
  }
  handleCancel() {
    this.setState(
      {
        visible: false,
      },
      () => {
        this.formRef && this.formRef.resetFields();
      }
    );
    this.props.hideRoleVisible();
  }
  render() {
    return (
      <React.Fragment>
        <Modal
          title={`${this.state.info.role_id ? "编辑角色" : "新增角色"}`}
          visible={this.state.visible}
          onCancel={this.handleCancel}
          width={900}
          height={688}
          footer={null}
        >
          <CustomizedForm
            ref={(ref) => {
              this.formRef = ref;
            }}
            name={this.state.info.name}
            remark={this.state.info.remark}
            tree={this.state.tree}
            onValuesChange={this.onValuesChange}
            onCheck={this.onCheck}
            pcCheckedKeys={this.state.pcCheckedKeys}
            mobildCheckedKeys={this.state.mobildCheckedKeys}
            onChangeRadio={this.onChangeRadio}
            belong={this.state.belong}
            checkboxOnchange={this.checkboxOnchange}
            pcQuan={this.state.pcQuan}
            mobileQuan={this.state.mobileQuan}
          />
          <div style={{ textAlign: "center" }}>
            <Button
              type="primary"
              style={{ width: 115, height: 36 }}
              onClick={this.save}
            >
              确定
            </Button>
            <Button
              style={{ width: 115, height: 36, marginLeft: 45 }}
              onClick={this.handleCancel}
            >
              取消
            </Button>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
