import { EditableFormRow, EditableCell } from "./components/EditableRow";
import DropText from "./components/DropText";
import NewUser from "./components/NewUser";

const { message } = Ant;

export default function ShiguanTable(props) {
  const { record, index, onUpdateData } = props;

  const [visible, setVisible] = React.useState(false);

  const formRef = React.useRef(null);

  const handleDelete = (user) => {
    const { user_id } = user;
    const newData = record.data.filter((item) => item.user_id != user_id);
    onUpdateData({ ...record, data: newData }, index);
  };

  const handleNumChange = (value) => {
    const newData = { ...record, num: value };
    onUpdateData(newData, index);
  };

  const handleAdd = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        const maxSeq = record.data.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 1);
        }, 1);
        const dataSource = [...record.data];
        dataSource.push({
          ...values,
          seq: maxSeq + 1,
        });
        onUpdateData({ ...record, data: dataSource }, index);
        setVisible(false);
      } else {
        message.error("请先选择要添加的测评人员");
      }
    });
  };

  const handleSave = (row) => {
    const newData = [...record.data];
    // if (newData.find((item) => item.seq.toString() === row.seq.toString())) {
    //   message.warning("该测评序号已存在");
    //   return;
    // }
    const idx = newData.findIndex((item) => row.org_id === item.org_id);
    if (idx > -1) {
      newData[idx] = row;
    }
    onUpdateData({ ...record, data: newData }, index);
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "20%",
      title: "姓名",
    },
    {
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      title: "测评职务类别",
      render(_, record) {
        const option = [
          { label: "县管正职", key: 1 },
          { label: "县管副职", key: 2 },
          { label: "改非干部", key: 3 },
          { label: "新提拔中层干部", key: 4 },
        ];
        if (![3, 4].includes(Number(_))) {
          option.splice(2, 2);
        }
        return (
          <DropText
            value={_}
            disabled={[3, 4].includes(Number(_))}
            disableList={[3, 4]}
            option={option}
            onChange={(key) => {
              dataSource.forEach((item) => {
                if (item.user_id === record.user_id) {
                  item.user_type = key;
                }
              });

              onUpdateData &&
                onUpdateData({ org_id, user_list: dataSource, index });
            }}
          />
        );
      },
    },
    {
      dataIndex: "job",
      key: "job",
      editable: true,
      title: "测评职务",
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (text, record) => (
        <Ant.Popconfirm
          title="确认删除?"
          onConfirm={() => handleDelete(record)}
          okText="是"
          cancelText="否"
        >
          <Ant.Button type="link">删除</Ant.Button>
        </Ant.Popconfirm>
      ),
    },
  ];

  const newColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave,
      }),
    };
  });

  return (
    <div style={{ marginBottom: 20 }}>
      <Ant.Row type="flex" align="middle" style={{ marginBottom: 10 }}>
        <div style={{ fontSize: 16, fontWeight: 800 }}>
          {record.sequence_name}
        </div>
        <div style={{ marginLeft: 52, marginRight: 20 }}>
          好等次个数：
          <Ant.InputNumber
            value={record.num}
            precision={0}
            size="small"
            onChange={handleNumChange}
          />
        </div>
        <Ant.Button
          type="primary"
          size="small"
          onClick={() => setVisible(true)}
        >
          <Ant.Icon type="plus" />
          添加
        </Ant.Button>
      </Ant.Row>
      <Ant.Table
        bordered
        components={{
          body: {
            row: EditableFormRow,
            cell: EditableCell,
          },
        }}
        columns={newColumns}
        dataSource={record.data}
        scroll={{ y: 300 }}
        pagination={false}
      />

      <Ant.Modal
        title="添加测评人员"
        visible={visible}
        onOk={handleAdd}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        <NewUser ref={formRef} />
      </Ant.Modal>
    </div>
  );
}
