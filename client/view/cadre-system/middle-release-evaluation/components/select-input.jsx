import React from "react";
import { Dropdown, Input, Menu } from "antd";
function SelectInput({
  value,
  onSearch,
  onSelect,
  onInput,
  onChange,
  option = [],
  ...props
}) {
  console.log("🚀 ~ option:", option);
  const menu = (
    <Menu>
      {option.map((item) => {
        return (
          <Menu.Item
            key={item.key}
            onClick={() => {
              onChange(item.user_name || item.org_name);

              onSelect(item);
            }}
          >
            {item.user_name || item.org_name} {item.job ? `-${item.job}` : ""}
          </Menu.Item>
        );
      })}
    </Menu>
  );
  return (
    <Dropdown overlay={menu}>
      <Input
        value={value}
        onChange={(e) => {
          const value = e.target.value;
          console.log(value);
          onInput && onInput(value);
          onChange && onChange(value);
          onSearch && onSearch(value);
        }}
        {...props}
      />
    </Dropdown>
  );
}

export default SelectInput;
