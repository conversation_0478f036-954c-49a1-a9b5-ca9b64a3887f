// 添加新的任务

import React from "react";
import "./index.less";
import PropTypes from "prop-types";
import { Modal, Form, Input, Radio, Button } from "antd";
import InputTips from "components/activity-form/sub-components/InputTips";
import moment from "moment";

const FormItem = Form.Item,
  TextArea = Input.TextArea,
  RadioGroup = Radio.Group;

class AddMeetingTask extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      maxSize: 5000,
    };
  }

  submitHandler(values) {
    const { onChange, index, isEdit, state, updateState } = this.props;
    const { tlt } = values;
    const endTypes = ["month", "quarter", "year"];
    values.start_time = moment().format("YYYY-MM-DD");
    values.end_time = endTypes[tlt - 1] ? moment().endOf(endTypes[tlt - 1]).format("YYYY-MM-DD") : moment().format("YYYY-MM-DD");
    // console.log("提交", values);
    // 自有的，自行添加任务
    values.status = 2;
    if (!isEdit) {
      values.contents = [{
        // 文本内容
        type: 1,
        name: values.name,
        ans_cnt: ""
      }];
      onChange(values);
    }
    else {
      onChange(Object.assign({}, state, values), index);
      updateState({
        currentEditItem: {},
        isEdit: false
      });
    }
  }

  render() {
    const { maxSize } = this.state;
    const { visible, form, updateState, onChange, state = {}, title } = this.props;
    const { getFieldDecorator, getFieldValue, validateFields, resetFields } = form;
    const modalProps = {
      visible,
      title,
      width: 600,
      onCancel: () => {
        resetFields();
        updateState({
          addMeetingTaskVisible: false
        });
      },
      footer: null
    }
    const formItemLayout = {
      labelCol: {
        span: 6
      },
      wrapperCol: {
        span: 14
      }
    }
    // console.log(state);
    return (
      <Modal {...modalProps}>
        <div className="add-meeting-task-container">
          <Form hideRequiredMark onSubmit={(e) => {
            e.preventDefault();
            validateFields((error, values) => {
              if (!error) {
                this.submitHandler(values);
                modalProps.onCancel();
              }
            });
          }}>
            <FormItem label="内容标题" {...formItemLayout}>
              <InputTips max={100} text={getFieldValue("name")}>
                {
                  getFieldDecorator("name", {
                    initialValue: state.name || "",
                    rules: [
                      { required: true, message: "请输入内容标题" },
                      { max: 100, message: "最多输入100字" }
                    ]
                  })(
                    <Input placeholder="请输入" />
                  )
                }
              </InputTips>
            </FormItem>
            {/* <FormItem label="任务备注" {...formItemLayout}>
              <InputTips max={maxSize} text={getFieldValue("description")}>
                {
                  getFieldDecorator("description", {
                    initialValue: state.description || "",
                    rules: [
                      { max: maxSize, message: `最多输入${maxSize}字` }
                    ]
                  })(
                    <TextArea rows={5} placeholder="请输入" />
                  )
                }
              </InputTips>
            </FormItem> */}
            {/* <FormItem label="任务有效期" {...formItemLayout}>
              {
                getFieldDecorator("tlt", {
                  initialValue: state.tlt || 1
                })(
                  <RadioGroup>
                    <Radio value={1}>本月内</Radio>
                    <Radio value={2}>本季度内</Radio>
                    <Radio value={3}>本年内</Radio>
                  </RadioGroup>
                )
              }
            </FormItem> */}
            <div className="buttons-wrapper">
              <Button className="buttons" htmlType="submit" type="primary">
                确定
              </Button>
              <Button className="buttons" onClick={() => {
                modalProps.onCancel();
              }}>
                取消
              </Button>
            </div>
          </Form>
        </div>
      </Modal>

    )
  }
}

AddMeetingTask.propTypes = {
  visible: PropTypes.bool,
  form: PropTypes.object,
  updateState: PropTypes.func,
  onChange: PropTypes.func,
  state: PropTypes.object,
  title: PropTypes.string
}

AddMeetingTask.defaultProps = {
  visible: false,
  updateState: () => { },
  onChange: () => { },
  state: {},
  title: "添加新的活动"
}

export default Form.create()(AddMeetingTask);