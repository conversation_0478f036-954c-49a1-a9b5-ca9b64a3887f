import React, { Component } from "react";

class Clip extends Component {
  constructor() {
    super();
  }
  componentDidMount() {
    // 注册window方法
    this.registerFunc()
    // iframe加载完成
    this.onload()
  }
  registerFunc() {
    const {onCancel, onSubmit} = this.props
    const cancel = () => {
      onCancel()
    }
    const submit = (base64) => {
      return onSubmit(base64)
    }
    window.cancel = cancel;
    window.submit = submit;
  }
  onload() {
    console.log(this.props)
    const self = this;
    const $iframe = this.iframe;
    $iframe.onload = () => {
      $iframe.contentWindow.init({
        width: self.props.size.w,
        height: self.props.size.h,
        img: self.props.src,
        title: self.props.title
      });
    };
  }
  render() {
    return (
      <div className="clipping">
        <iframe
          src="/plugin/html/clipping.html"
          ref={e => (this.iframe = e)}
        />
      </div>
    );
  }
}
export default Clip;
