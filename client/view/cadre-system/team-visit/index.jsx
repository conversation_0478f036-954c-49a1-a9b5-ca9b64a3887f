import React, { useRef, useState, useEffect } from "react";
import "./index.less";
import {
  Form,
  Input,
  Popconfirm,
  Button,
  Table,
  Modal,
  message,
  Radio,
  Select,
  Tooltip
} from "antd";
import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";
import {
  queryTeamVisit,
  addOrUpdateTeamVisit,
  deleteTeamVisit,
  exportTeamVisit,
  queryByCode,
  getTeamVisitList,
  saveTeamVisit,
  deleteTeamVisit1,
} from "client/apis/cadre-portrait";

function index({ form, history }) {
  const { TextArea } = Input;
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);
  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
  const [tabs, setTabs] = useState([
    {
      op_key: "tb",
      op_value: "提拔",
    },
    {
      op_key: "zy",
      op_value: "重用",
    },
    {
      op_key: "zg",
      op_value: "转岗",
    },
    {
      op_key: "jl",
      op_value: "交流",
    },
    {
      op_key: "lr",
      op_value: "留任",
    },
    {
      op_key: "qt",
      op_value: "其他",
    },
  ]);
  const [visitID, setvisitID] = useState("");

  useEffect(() => {
    loadData();
    // initCode(2001, "tabs");
  }, []);

  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();
    console.log("fields===", fields);

    const params = {
      // ...getFieldsValue(),
      ...fields,
      page: _page || page,
      org_id: org_id || org_ids[0],
    };
    const res = await getTeamVisitList(params);
    if (res.data.code === 0) {
      const data = res.data.data.content;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.totalElements,
        };
      });
    } else {
      message.error(res.data.message);
    }
    setLoading(false);
  };

  const initCode = async (code, values) => {
    queryByCode({ code }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        setTabs(data);
      }
    });
  };

  const onChange = (orgs, org) => {
    setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);
    const params = getFieldsValue();
    const res = await exportTeamVisit({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);
    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=17&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteTeamVisit1({
      pms_return_visit_id: record.pms_return_visit_id,
    });
    if (res.data.code === 0) {
      loadData();
    } else {
      message.error(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");
    setvisitID(record.pms_return_visit_id);
    queueMicrotask(() => {
      const _ = { ...record };
      _.user_name = _.user_name ? _.user_name : undefined;
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "标题",
      dataIndex: "title",
      key: "title",
      width: 200,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: 80,
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 200,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "评价描述",
      dataIndex: "assessment",
      key: "assessment",
      width: 300,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '400px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )

    },
    {
      title: "回访组排序",
      dataIndex: "group_rank",
      key: "group_rank",
      width: 100,
      align: "center",
    },
    {
      title: "一把手排序",
      dataIndex: "leader_rank",
      key: "leader_rank",
      width: 100,
      align: "center",
    },
    {
      title: "是否重点关注",
      dataIndex: "attention",
      key: "attention",
      width: 100,
      align: "center",
      render(text) {
        return text == 1 ? "是" : text == 2 ? "否" : "-";
      },
    },
    {
      title: "个人意愿",
      dataIndex: "will_list",
      key: "will_list",
      width: 100,
      align: "center",
      render(text, record) {
        const will = tabs.filter(tab => record.will_list.includes(tab.op_key)).map(tab => tab.op_value);
        return will.length ? will.join("、") : "-";
      },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据
    setPage(1);
    loadData({ page: 1 });
  };

  // 等参数
  const changeTabs = (key) => { };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name, start_time: _start_time } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;
          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        if (visitID) {
          values.pms_return_visit_id = visitID;
        }
        const res = await saveTeamVisit({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);
          message.success("操作成功");
          loadData();
          setvisitID('')
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
    setvisitID('')
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } =
      form;
    const formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("marriage_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(<DateSingle
              placeholder="请选择"
              type="month"
              allowClear={false}
              isWrap={false}
            />)}
        </Form.Item> */}
        <Form.Item label="标题">
          {getFieldDecorator("title", {
            rules: [{ required: true, message: "请输入标题" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="时任职务">
          {getFieldDecorator("job", {
            rules: [{ required: false, message: "请输入时任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="评价描述">
          {getFieldDecorator("assessment", {
            rules: [
              { required: true, message: "请输入评价描述" },
              { max: 150, message: "评价描述长度不能超过 150 个字符" }
            ],
          })(<TextArea maxLength={150} />)}
        </Form.Item>
        <Form.Item label="回访组排序">
          {getFieldDecorator("group_rank", {
            rules: [{ required: false, message: "请输入回访组排序" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="一把手排序">
          {getFieldDecorator("leader_rank", {
            rules: [{ required: false, message: "请输入一把手排序" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="是否重点关注">
          {getFieldDecorator("attention", {
            rules: [{ required: false }],
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={2}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="个人意愿">
          {getFieldDecorator("will_list", {
            rules: [{ required: false, message: "请选择个人意愿" }],
          })(
            <Select
              placeholder="请选择"
              // value={desire[0] || []}
              mode="multiple"
              style={{ width: "200px" }}
              onChange={(op_key) => {
                changeTabs(op_key);
              }}
            >
              {tabs &&
                tabs.length > 0 &&
                tabs.map((item) => {
                  return (
                    <Select.Option value={item.op_key}>
                      {item.op_value}
                    </Select.Option>
                  );
                })}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="具体描述">
          {getFieldDecorator("description", {
            rules: [{ required: false, message: "请输入一把手排序" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="team-visit">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="个人意愿">
              {getFieldDecorator("will")(
                <Select
                  placeholder="请选择"
                  // value={desire[0] || []}
                  mode="multiple"
                  style={{ width: "200px" }}
                  onChange={(op_key) => {
                    changeTabs(op_key);
                  }}
                >
                  {tabs &&
                    tabs.length > 0 &&
                    tabs.map((item) => {
                      return (
                        <Select.Option value={item.op_key}>
                          {item.op_value}
                        </Select.Option>
                      );
                    })}
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "班子回访"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
