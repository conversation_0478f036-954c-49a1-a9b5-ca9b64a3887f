import {
    fetchActivityBase,
    fetchActivityCheck,
    fetchActivityPlan,
    fetchActivityReward,
    fetchOfflineSiderUsers,
    fetchMultipleDimensions,
    audit,
    promote,
    award
} from 'client/apis/offline-activity';
import { downloadResult } from 'client/apis/activity';
//为表格数据添加key属性
import { addKeyToTableDataSource } from 'client/tool/util';
import parseTreeToList from 'client/tool/parseTreeToList';

const transformDataList = (dataList, rowspan) => {
    // console.log(dataList, rowspan);
    let result = [], row = {};
    if (Array.isArray(dataList) && dataList.length !== 0) {
        for (let i = 0, len = dataList.length, item = null; i < len; i++) {
            item = dataList[i];
            if (!item) {
                continue;
            }
            row = {
                index: i,
                team_task_id: item.team_task_id,
                team_name: item.team_name,
                offline_ticket_name: item.offline_ticket_name,
                serial_num: item.serial_num,
                status: item.status,
                org_name: item.org_name,
                party_org_name: item.party_org_name,
            };
            const team_users = item.team_users;
            // console.log(team_users);
            if (Array.isArray(team_users) && team_users.length !== 0) {
                for (let index = 0, usersLen = team_users.length, user = null; index < usersLen; index++) {
                    if (!rowspan[item.team_task_id] && item.team_users) {
                        // console.log('跨行');
                        rowspan[item.team_task_id] = item.team_users.length;
                        row.rowspan = item.team_users.length;
                    } else {
                        // console.log('不跨行');
                        row.rowspan = 0;
                    }
                    user = team_users[index];
                    row.offline_user_id = user.offline_user_id;
                    row.user_name = user.user_name;
                    row.phone = user.phone;
                    row.offline_sign_in_log_id = user.offline_sign_in_log_id;
                    row.org_name = user.org_name;
                    row.party_org_name = user.party_org_name;
                    result.push(JSON.parse(JSON.stringify(row)));
                }
            }
        }
    }
    // console.log(result);
    return result;
}

export default {
    //命名空间，唯一
    namespace: 'activityDetails',
    //转换为props的状态属性
    state: {
        title: '活动详情',
        //当前线下活动分类: 1.个人活动; 2.团体活动
        //1.单人；2.团队
        activityType: 1,
        //当前线下活动，侧边栏查看人员分类: 1.待审核; 2.预计参加名单; 3.实际签到名单; 4.颁奖
        //1.待审核人员名单;2.预计参赛人员名单;3.实际签到人员名单;4.颁奖人员名单
        //主要用于控制表单列
        activityNameListType: 1,
        //审批信息表格
        summaryTableDataSource: [],
        //日程信息表格
        programmeTableDataSource: [],
        //颁奖明细表格
        prizeTableDataSource: [],
        //多维度表格统计
        peopleTableDataSource: [],
        //侧边栏人员列表
        peopleOverviewDataSource: [],
        //侧边栏控制符，控制是否折叠
        collapsed: true,
        isHideWrap: false,
        //模态框控制符，控制是否显示模态框
        modalVisible: false,
        modalTitle: '查看人员',
        //筛选头部表单
        // formData: {
        //     name: null,
        //     phone: null,
        //     team: null,
        //     ticket: null
        // },
        //颁发奖品表单
        awardFormData: {
            //奖项名称
            reward_name: null,
            //奖励积分勾选项目
            awardPoints: false,
            //奖励积分数量
            score: 0,
            //奖励物品勾选项目
            awardCommodity: false
        },
        //选择物品模态框可见控制
        commodityModalVisible: false,
        //暂存选中的物品
        commodityList: [],
        //线下活动基础信息
        baseInfo: {
            //活动名称
            title: null,
            //活动报名开始时间
            sign_up_time: null,
            //活动开始时间
            start_time: null,
            //活动类型
            type: null,
            // 所属组织id
            org_id: null,
        },
        //当前活动id
        aid: '',
        //当前日程id
        pid: '',
        //审批目标
        //晋级目标
        //颁奖目标
        awardTarget: null,
        awardTargetIndex: null,
        //所有维度
        allCountTableDataSource: [],
        //年龄维度
        ageTableDataSource: [],
        //性别维度
        genderTableDataSource: [],
        //学历维度
        educationDegreeTableDataSource: [],
        //民族维度
        nationTableDataSource: [],
        //其他维度
        otherTableDataSource: [],
        loadingVisible: true,
        //暂存当前日程
        currentPlan: null,
        downloadUrl: null,
    },
    //异步操作
    effects: {
        * fetchMultipleDimensions({ payload }, { put }) {
            yield put({ type: 'save', payload: { loadingVisible: true } });
            const { aid } = payload;
            const response = yield fetchMultipleDimensions(aid);
            yield put({ type: 'save', payload: { loadingVisible: false } });
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }
            const {
                activity_title,
                activity_type,
                count_all,
                count_dimension
            } = data;
            // console.log('开始处理多维度数据，全部统计', count_all);
            if (count_all && Array.isArray(count_all) && count_all.length !== 0) {
                count_all.map((item, index) => {
                    if (typeof item === 'object') {
                        if (item.children && item.children.length === 0) {
                            delete item.children;
                        }
                    }
                    item.key = index;
                    return item;
                });
            }
            yield put({ type: 'save', payload: { allCountTableDataSource: count_all } });
            // console.log('开始处理多维度数据，多维度统计', count_dimension);
            let target;
            if (count_dimension && count_dimension.length !== 0) {
                let opts = [];

                for (let i = 0, len = count_dimension.length, item = null; i < len; i++) {
                    item = count_dimension[i];
                    if (item && item.children && item.children[0]) {
                        opts = item.children[0].opts;
                        target = parseTreeToList(opts);
                        if (target && Array.isArray(target) && target.length !== 0) {
                            target.map((item, index) => {
                                if (typeof item === 'object') {
                                    if (item.children && item.children.length === 0) {
                                        delete item.children;
                                    }
                                }
                                item.key = index;
                                return item;
                            });
                        }
                    } else {
                        target = [];
                    }
                    // console.log(target);
                    if (item.type === 1) {
                        //年龄维度
                        yield put({
                            type: 'save',
                            payload: { ageTableDataSource: target }
                        });
                    } else if (item.type === 2) {
                        //性别
                        yield put({
                            type: 'save',
                            payload: { genderTableDataSource: target }
                        });
                    } else if (item.type === 3) {
                        //文化程度
                        yield put({
                            type: 'save',
                            payload: { educationDegreeTableDataSource: target }
                        });
                    } else if (item.type === 4) {
                        //民族
                        yield put({
                            type: 'save',
                            payload: { nationTableDataSource: target }
                        })

                    } else if (item.type === 99) {
                        //其他维度
                        yield put({
                            type: 'save',
                            payload: { otherTableDataSource: target }
                        })
                    }
                }
            } else {
                yield put({
                    type: 'save',
                    payload: {
                        ageTableDataSource: [],
                        genderTableDataSource: [],
                        educationDegreeTableDataSource: [],
                        nationTableDataSource: [],
                        otherTableDataSource: []
                    }
                });
            }

        },
        //获取线下活动基本信息接口
        * fetchActivityBase({ payload }, { put }) {
            const { aid } = payload;
            const response = yield fetchActivityBase(aid);
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }
            //活动报名类型: 1.单人；2.团队
            const { type } = data;
            yield put({
                type: 'save',
                payload: {
                    baseInfo: data,
                    activityType: type
                }
            })
        },
        //获取线下活动审批信息接口
        * fetchActivityCheck({ payload }, { put }) {
            const { aid } = payload;
            const response = yield fetchActivityCheck(aid);
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }
            let target = addKeyToTableDataSource(data);
            yield put({
                type: 'save',
                payload: {
                    summaryTableDataSource: target
                }
            })
        },
        //获取线下活动日程信息接口
        * fetchActivityPlan({ payload }, { put }) {
            const { aid } = payload;
            const response = yield fetchActivityPlan(aid);
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }
            let target = addKeyToTableDataSource(data);
            yield put({
                type: 'save',
                payload: {
                    programmeTableDataSource: target
                }
            })
        },
        //获取颁奖明细信息接口
        * fetchActivityReward({ payload }, { put }) {
            const { aid } = payload;
            const response = yield fetchActivityReward(aid);
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }
            let target = addKeyToTableDataSource(data);
            yield put({
                type: 'save',
                payload: {
                    prizeTableDataSource: target
                }
            });
            // console.log('刷新颁奖明细表格');
        },
        //获取侧边栏人员列表
        * fetchOfflineSiderUsers({ payload }, { put }) {
            const response = yield fetchOfflineSiderUsers(payload);
            const { data: body } = response;
            const { code, data, message } = body;
            if (code !== 0) {
                throw new Error(message || '未知错误');
            }

            const { sign_type, data: dataList, type } = data;
            let target = [];

            if (sign_type === 1) {
                //当为个人活动时
                // [{
                //     "offline_user_id":"string|必填|参与线下活动用户id",
                //     "offline_ticket_name": "string|必填|票名称",
                //     "serial_num": "string|必填|票券码",
                //     "user_name": "string|必填|用户名",
                //     "phone": "string|必填|电话号码"
                // }]
                target = dataList;
            } else if (sign_type === 2) {
                //当为团体活动时,需要对dataList进行处理
                // [{
                //     "team_name": "string|必填|队伍名称",
                //     "offline_ticket_name": "string|必填|票名称",
                //     "serial_num": "string|必填|票券码",
                //     "team_users": [{
                //         "offline_user_id": "string|必填|参与线下活动用户id",
                //         "user_name": "string|必填|用户名",
                //         "phone": "string|必填|电话号码"
                //     }]
                // }]
                if (type === 1) {
                    target = dataList;
                } else {
                    //记录行合并
                    let rowspan = {};
                    target = transformDataList(dataList, rowspan);
                    rowspan = null;
                }
            }
            target = addKeyToTableDataSource(target);
            // console.log(target);
            yield put({
                type: 'save',
                payload: {
                    peopleOverviewDataSource: target
                }
            })
        },
        //审核操作
        * audit({ payload }, { put }) {
            const { activity_id, offline_user_id, status } = payload;
            const response = yield audit(activity_id, offline_user_id, status);
            // console.log(response);
            const { data: body } = response;
            const { code, message } = body;
            if (code !== 0) {
                throw new Error(message);
            }
            return body;
        },
        //晋级操作
        * promote({ payload }, { put }) {
            const { postData } = payload;
            const response = yield promote(postData);
            // console.log(response);
            const { data: body } = response;
            const { code, message } = body;
            if (code !== 0) {
                throw new Error(message);
            }
            return body;
        },
        //颁奖操作
        * award({ payload }, { put }) {
            const { postData } = payload;
            let response = yield award(postData);
            const { data: body } = response;
            const { code, message } = body;
            if (code !== 0) {
                throw new Error(message);
            }
            return body;
        },
        //下载操作
        * download({ payload }, { put }) {
            const { activity_id, activity_type, dimension } = payload;
            let downloadUrl = yield downloadResult(activity_type, activity_id);
            downloadUrl = `${downloadUrl}?dimension=${dimension}`;
            yield put({
                type: 'save',
                payload: {
                    downloadUrl
                }
            });
            return downloadUrl;
        }
    },
    //对state的操作方法
    reducers: {
        save(state, { payload }) {
            return { ...state, ...payload };
        },
        toggleHideWrap(state) {
            return { ...state, isHideWrap: !state.isHideWrap };
        },
        toggleCollapsed(state, form) {
            //如果当前侧边栏为显示状态，在收起时将表单状态重置
            return { ...state, collapsed: !state.collapsed };
        },
        //重置表单状态
        // resetFormData(state, params) {
        //     let { form } = params;
        //     form.resetFields();
        //     return { ...state };
        // },
        toggleModalVisible(state) {
            return { ...state, modalVisible: !state.modalVisible };
        }
    }
}