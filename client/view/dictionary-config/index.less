.dictionaryData-management {
  .top-box {
    width: 100%;
    height: 88px;
    background-color: rgba(239, 239, 239, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .sm-t-left {
      display: flex;
      align-items: center;
      .ant-row {
        margin-bottom: 0px;
      }
      button {
        margin-left: 20px;
      }
    }
    .sm-r-right {
      display: flex;
      align-items: center;
      .label {
        margin-right: 10px;
        line-height: 25px;
        color: rgba(22, 132, 252, 1);
        font-size: 18px;
        text-align: left;
        font-family: PingFangSC-regular;
      }
    }
  }
  .table-box {
    margin-top: 10px;
    .operation {
      .editor {
        margin-right: 10px;
      }
    }
  }
}
.dictionaryData-management-modal-content {
  padding-left: 50px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  .name,
  .unit,
  .current {
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    text-align: left;
    font-family: SourceHanSansSC-regular;
  }
  .select {
    margin-top: 10px;
  }
}
