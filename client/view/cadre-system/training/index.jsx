import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import { Form, Input, Popconfirm, Button, Table, Modal, message, Radio, DatePicker, Select } from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryUserTrain,
  addOrUpdateUserTrain,
  deleteUserTrain,
  exportUserTrain,
  queryPatrol,
  checkPatrol,
  addOrUpdatePatrol,
  deletePatrol,
  exportPatrol,
  queryMajorEventDetail,
} from "client/apis/cadre-portrait";
import moment from "moment";
const { Option } = Select;
function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields, setFieldsValue } = form;
  const { RangePicker } = DatePicker;
  const modalFormRef = useRef(null);

  const [treeData, setTreeData] = useState([]);

  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });

  useEffect(() => {
    loadData();
  }, []);
  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();
    const params = {
      // ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.user_name,
      excel: fields.excel,
    };
    const res = await queryUserTrain(params);
    if (res.data.code === 0) {
      const data = res.data.data.data;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    } else {
      message.error(res.data.message);
    }

    setLoading(false);
  }

  const loadDataDesc = async (id) => {
    const res = await queryMajorEventDetail({
      performance_id: id,
    });

    if (res.data.code === 0) {
      modalFormRef.current.setFieldsValue(res.data.data);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportUserTrain({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=9&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteUserTrain({
      id: record.pms_user_train_id,
    });
    if (res.data.code === 0) {
      loadData();
    } else {
      message.error(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queueMicrotask(() => {
      // loadDataDesc(record.performance_id);
      // modalFormRef.current.setFieldsValue(record);
      const _ = { ...record };
      _.user_name = _.name ? _.name : undefined;
      // 拆分 rank 为分子和分母
      // if (_.rank) {
      //   const rankParts = _.rank.split('/');
      //   if (rankParts.length === 2) {
      //     _.numerator = rankParts[0];
      //     _.denominator = rankParts[1];
      //   }
      // }
      // console.log("record: ", _);
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      width: "5%",
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 200,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "班次",
      dataIndex: "classes",
      key: "classes",
      width: "13%",
      align: "center",
    },
    {
      title: "排名",
      dataIndex: "rank",
      key: "rank",
      width: "5%",
      align: "center",
    },
    {
      title: "是否优秀学员",
      dataIndex: "excellent_text",
      key: "excellent_text",
      width: "8%",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: "8%",
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name } = values;
        // console.log("Current form values:", form.getFieldsValue());

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        const res = await addOrUpdateUserTrain({
          ...values,
        });
        // console.log("values: ", values);
        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };

    const handleNumeratorChange = e => {
      const numerator = e.target.value;
      const denominator = form.getFieldValue("denominator");
      updateRank(numerator, denominator);
    };

    const handleDenominatorChange = e => {
      const denominator = e.target.value;
      const numerator = form.getFieldValue("numerator");
      updateRank(numerator, denominator);
    };

    const updateRank = (numerator, denominator) => {
      if (numerator && denominator) {
        setFieldsValue({
          rank: `${numerator}/${denominator}`,
        });
      } else {
        setFieldsValue({
          rank: '',
        });
      }
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("pms_user_train_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="时任职务">
          {getFieldDecorator("job", {
            rules: [{ required: true, message: "请输入时任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="班次">
          {getFieldDecorator("classes", {
            rules: [{ required: true, message: "请选择班次" }],
          })(
            // <Select>
            //   <Option value="2022年第1期青苗班">2022年第1期青苗班</Option>
            //   <Option value="2022年第2期青苗班">2022年第2期青苗班</Option>
            //   <Option value="2022年第二期新提拔县管副职领导干部培训班">2022年第二期新提拔县管副职领导干部培训班</Option>
            //   <Option value="2022年第一期新提拔县管副职领导干部培训班">2022年第一期新提拔县管副职领导干部培训班</Option>
            //   <Option value="2022年荣昌区中青年干部培训班">2022年荣昌区中青年干部培训班</Option>
            //   <Option value="2022年优秀县管副职领导干部主体培训班">2022年优秀县管副职领导干部主体培训班</Option>
            //   <Option value="2022年渝北区中青年干部培训班">2022年渝北区中青年干部培训班</Option>
            //   <Option value="2022年中青年干部培训班">2022年中青年干部培训班</Option>
            //   <Option value="2023年第3期青苗班">2023年第3期青苗班</Option>
            //   <Option value="2023年第4期青苗班">2023年第4期青苗班</Option>
            //   <Option value="2023年青年干部主体培训班">2023年青年干部主体培训班</Option>
            //   <Option value="2023年新提拔县管副职领导干部培训班(二班)">2023年新提拔县管副职领导干部培训班(二班)</Option>
            //   <Option value="2023年新提拔县管副职领导干部培训班(一班)">2023年新提拔县管副职领导干部培训班(一班)</Option>
            //   <Option value="2023年中青年干部主体培训班">2023年中青年干部主体培训班</Option>
            //   <Option value="2024年青年干部培训班">2024年青年干部培训班</Option>
            //   <Option value="2024年新提拔副职领导干部培训班">2024年新提拔副职领导干部培训班</Option>
            //   <Option value="2024年中青年干部培训班">2024年中青年干部培训班</Option>
            //   <Option value="第5期青苗班">第5期青苗班</Option>
            // </Select>
            <Input placeholder="请输入班次" />
          )}
        </Form.Item>
        {/* 分子和分母作为受控字段 */}
        <Form.Item label="排名">
          {getFieldDecorator("rank", {
            rules: [{ required: false, message: "请输入" }],
          })(<Input placeholder="请输入" />)}
          {/* {getFieldDecorator("numerator", {
            rules: [{ required: true, message: "请输入分子" }],
          })(
            <Input
              style={{ width: '4rem' }}
              onChange={handleNumeratorChange}
            />
          )}
          <span style={{ fontSize: '24px', margin: '0 10px' }}>/</span>
          {getFieldDecorator("denominator", {
            rules: [{ required: true, message: "请输入分母" }],
          })(
            <Input
              style={{ width: '4rem' }}
              onChange={handleDenominatorChange}
            />
          )} */}
        </Form.Item>
        {/* 隐藏的 rank 字段 */}
        {/* {getFieldDecorator("rank")(<input type="hidden" />)} */}
        <Form.Item label="优秀学员">
          {getFieldDecorator("excellent", {
            rules: [{ required: true, message: "请选择是或否" }],
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={2}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>

      </Form>
    );
  });
  return (
    <div className="petition-reporting">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            {/* <Form.Item label="事件">
              {getFieldDecorator("event")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item> */}
            <Form.Item label="是否优秀学员">
              {getFieldDecorator("excel", { initialValue: "0" })(
                <Select style={{ width: "150px" }}>
                  <Option value="0">全部</Option>
                  <Option value="1">是</Option>
                  <Option value="2">否</Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "培训情况"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
