import React, { Component } from "react";
import { <PERSON><PERSON>, Modal, Form, Input } from "antd";

import "./add-attend-users.less";

import propTypes from "prop-types";

const FormItem = Form.Item;

class AddAttendUsers extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false
    }
  }

  addToList(data = {}) {
    const { addToList } = this.props;
    if (addToList) {
      if (data) {
        addToList(data);
      } else {
        // console.log("获取要添加的数据失败");
      }
    } else {
      // console.error("父级组件没有传入addToList方法");
    }
  }

  showModal() {
    this.setState({
      visible: true
    })
  }

  hideModal() {
    this.setState({
      visible: false
    })
  }

  addHandler() {
    const { form, attendUserCount = 1 } = this.props;
    // console.log(attendUserCount);
    const { validateFields } = form;
    validateFields((error, values) => {
      if (!error) {
        // {
        //   "user_name": "张老三",
        //     "user_id": 52676,
        //       "phone": "150****6655",
        //         "cert_number": "500225********4717",
        //           "org_id": 3,
        //             "org_name": "重庆市级机关"
        // }
        values.user_id = -attendUserCount;
        values.cert_number = "";
        values.org_id = -attendUserCount;
        values.sign_status = 1;
        this.addToList(values);
        console.log('🚀 ~ file: add-attend-users.js ~ line 62 ~ values', values);
        this.resetForm();
        this.hideModal();
      }
    });
  }

  continueAddHandler() {
    const { form, attendUserCount = 1 } = this.props;
    const { validateFields } = form;
    validateFields((error, values) => {
      if (!error) {
        // {
        //   "user_name": "张老三",
        //     "user_id": 52676,
        //       "phone": "150****6655",
        //         "cert_number": "500225********4717",
        //           "org_id": 3,
        //             "org_name": "重庆市级机关"
        // }
        values.user_id = -attendUserCount;
        values.cert_number = "";
        values.org_id = -attendUserCount;
        values.sign_status = 1;
        this.addToList(values);
        this.resetForm();
      }
    });
  }

  cancelHandler() {
    this.resetForm();
    this.hideModal();
  }

  resetForm() {
    const { form } = this.props;
    const { resetFields } = form;
    resetFields();
  }

  render() {
    const This = this;
    const { visible } = this.state;
    const { form, notOrzPerson, modalName } = this.props;
    const { getFieldDecorator } = form;
    const modalProps = {
      title: `录入${modalName}`,
      visible,
      footer: null,
      wrapClassName: "add-attend-users-modal",
      width: 600,
      onCancel: () => {
        This.hideModal();
      }
    };
    const formLayout = {
      labelCol: {
        span: 6
      },
      wrapperCol: {
        span: 12
      }
    }
    return (
      <div className="add-attend-users">
        <a className="add-attend-users-trigger" onClick={() => this.showModal()} style={{ display: `${notOrzPerson ? "inline-block" : "none"}` }}>
          {/* 录入非组织内人员 */}
          录入非本组织内人员
        </a>
        {/* <Button icon="plus" style={{ fontSize: "16px", width: "160px", height: "40px" }} type="primary" onClick={() => this.showModal()}>录入更多人员</Button> */}
        <Modal {...modalProps}>
          <Form>
            <FormItem label="姓名" {...formLayout}>
              {
                getFieldDecorator("user_name", {
                  initialValue: "",
                  rules: [
                    { required: true, message: `请输入${modalName}姓名` }
                  ]
                })(
                  <Input placeholder={`请输入${modalName}姓名`} />
                )
              }
            </FormItem>
            <FormItem label="电话" {...formLayout}>
              {
                getFieldDecorator("phone", {
                  initialValue: ""
                })(
                  <Input placeholder="请输入联系电话" />
                )
              }
            </FormItem>
            <FormItem label="组织" {...formLayout}>
              {
                getFieldDecorator("org_name", {
                  initialValue: ""
                })(
                  <Input placeholder="请输入组织名称" />
                )
              }
            </FormItem>
          </Form>
          <div className="buttons-container">
            <Button className="button" type="primary" onClick={() => this.addHandler()}>保存</Button>
            <Button className="button" type="primary" onClick={() => this.continueAddHandler()}>保存并继续录入</Button>
            <Button className="button" onClick={() => this.cancelHandler()}>取消</Button>
          </div>
        </Modal>
      </div>
    )
  }
}

AddAttendUsers.propTypes = {
  modalName: propTypes.string,
  attendUserCount: propTypes.number,
  addToList: propTypes.func
}

AddAttendUsers.defaultProps = {
  modalName: "列席人员",
  attendUserCount: 1,
  addToList: () => { }
}

export default Form.create()(AddAttendUsers);
