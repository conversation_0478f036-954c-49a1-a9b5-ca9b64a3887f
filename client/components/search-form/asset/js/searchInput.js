import { Dropdown, Input, Menu } from 'antd';
import { useState } from 'react';

const SearchInput = ({ value, placeholder, options = [], onChange, onFocus }) => {
    const [dropdownVisible, setDropdownVisible] = useState(false);

    const handleChange = (e) => {
        // 处理输入框值的变化
        if (onChange) {
            onChange(e.target.value);
        }
    };

    const handleFocus = (e) => {
        // 处理输入框聚焦事件
        if (onFocus) {
            onFocus(e);
        }
        setDropdownVisible(true);
    };

    const handleSelect = (item) => {
        // 处理菜单项点击事件
        if (onChange) {
            onChange(item.value);
        }
        setDropdownVisible(false);
    };

    return (
        <Dropdown
            overlay={
                <Menu>
                    {options.map((item) => (
                        <Menu.Item key={item.value} onClick={() => handleSelect(item)}>
                            <span>{item.label}</span>
                        </Menu.Item>
                    ))}
                </Menu>
            }
            visible={dropdownVisible}
            onVisibleChange={setDropdownVisible}
        >
            <Input
                suffixIcon="search"
                placeholder={placeholder}
                value={value}
                onChange={handleChange}
                onFocus={handleFocus}
                style={{ width: '100%' }}
            />
        </Dropdown>
    );
};

export default SearchInput;