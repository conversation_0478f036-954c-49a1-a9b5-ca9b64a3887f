import React from "react";
import SearchHeader from "components/search-header";
import SelfIcon from "components/self-icon";
import "./style.less";

import { connect } from "dva";

const TaskManageBaseSetting = (props) => {
  const { userInfo = {} } = props,
    { menu } = userInfo;
  let child = [];
  const maps = {
    // "070301": {
    //   path: "/user-group-competence",
    //   icon: "yonghuzuguanli",
    //   name: "用户组管理",
    //   content: "通过用户组设置的组织权限，只能由设置者进行修改。"
    //   // name: "会议完成情况"
    // },
    "070302": {
      path: "/authority-management/role-competence",
      icon: "jiaoseguanli",
      // name: "上级任务完成情况"
      name: "角色管理",
      content: "创建角色，设置权限。",
      // name: "议题完成情况"
    },
    "070303": {
      path: "/authority-management/user-competence",
      icon: "yong<PERSON><PERSON><PERSON><PERSON>",
      // name: "上级任务完成情况"
      name: "用户权限管理",
      content: "对您所在组织及下级组织的人员设置权限。",
      // name: "议题完成情况"
    },
  };
  if (menu && Array.isArray(menu) && menu.length !== 0) {
    menu.forEach((item) => {
      if (item.parent_id === "0703" && maps[item.menu_id]) {
        child.push(maps[item.menu_id]);
      }
    });
  }

  const { history } = props;
  return (
    <div className="authorityManagement">
      <SearchHeader
        onBack={false}
        // title="任务管理"
        title="权限管理"
        style={{ fontWeight: "bold" }}
      />
      <div className="wrap">
        {child && Array.isArray(child) && child.length !== 0 ? (
          child.map((item, index) => {
            return (
              <a
                key={index}
                className="item"
                onClick={() => history.push(item.path)}
              >
                <p className="childIcon">
                  <SelfIcon
                    type={`gsg-${item.icon}`}
                    style={{ fontSize: "80px" }}
                  />
                </p>
                <div className="childName">{item.name}</div>
                <div className="childContent">{item.content}</div>
              </a>
            );
          })
        ) : (
          <span>无操作权限...</span>
        )}
      </div>
    </div>
  );
};

// 连接userInfo状态机，查找用户登录菜单权限
const mapStateToProps = ({ userInfo }) => ({ userInfo });
export default connect(mapStateToProps)(TaskManageBaseSetting);
