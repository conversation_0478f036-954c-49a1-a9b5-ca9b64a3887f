import { http } from "client/tool/axios";
import { getParam } from 'client/tool/util';
import { ucHost } from "./config";
// const md5 = require("js-md5");

const getCsrf = () => {
  return typeof window !== "undefined"
    ? window.sessionStorage.getItem("csrf")
    : "";
};

const option = {
  headers: {
    "Content-Type": "application/json",
    _tk: "-1",
    _uid: "-1",
    _un: "-1",
    _type: "-1",
    _oid: "-1",
    "x-csrf-token": getCsrf(),
    _region_id: getParam().region_id || process.env.region_id
  }
};

// 登录获取图片验证码
export const showCaptcha = data => {
  return `${ucHost}/show-captcha.png?uuid=${data}`;
};

// 验证组织标识码
export const checkUniqueCode = data => {
  return http.get(`${ucHost}/uc/org/check-unique-code`, data, option);
};

// 获取短信验证码
export const getPhoneCaptcha = data => {
  return http.post(`${ucHost}/uc/user/send?flag=2&phone=${data.phone}`);
};

// 获取数据字典
export const getDataDictionary = data => {
  return http.get(`${ucHost}/uc/op/list`, data, option);
};

// 获取数据字典列表  --2025.3.4
export const getDictionaryList = data => {
  return http.get(`http://*************:8889/pms-option/query_option_list`, data, option);
  // return http.get(`${ucHost}/pms-option/query_option_list`, data, option);

};
// 获取数据字典详情  --2025.3.4
export const getDictionaryDetail = data => {
  return http.get(`http://*************:8889/pms-option/query_option_detail`, data, option);
  // return http.get(`${ucHost}/pms-option/query_option_detail`, data, option);

};
// 获取数据字典添加  --2025.3.4
export const dictionaryAdd = data => {
  return http.post(`http://*************:8889/pms-option/update_option`, data, option);
  // return http.post(`${ucHost}/pms-option/update_option`, data, option);

};
/**
 * 获取数据字典(包含子集)
 * @param {*} data 
 */
export const getDataDictionarys = data => {
  return http.get(`${ucHost}/uc/op/list-child`, data);
};

// 激活组织-设置密码
// export const dealPassword = data => {
//   return http.post("/apis/node/activePassword", {
//     password: md5(data.password),
//     repeat_password: md5(data.repeat_password),
//     user_id: data.user_id
//   });
// };

// 激活组织
export const activeOrganization = data => {
  return http.post(`${ucHost}/org/enable`, data, option);
};

// 根据pid获取父级的下级区域列表
export const getArea = data => {
  return http.get(`${ucHost}/dict/area/list`, data, option);
};

// 根据区域码获取各个级别的城市列表
export const getAreaAll = data => {
  return http.get(`${ucHost}/dict/area/all`, data, option);
};

// 根据区域码获取单个区域的详细信息
export const getAreaDetail = data => {
  return http.get(`${ucHost}/dict/area/detail`, data, option);
};

// 注册人信息
export const dealRegistInfo = data => {
  return http.post(`${ucHost}/uc/user/deal-regist-info`, data, option);
};

// 查询组织树
export const getOrganizationTree = data => {
  return http.get(`${ucHost}/org/tree`, data, option);
};

// 定位组织树
export const locateOrgTree = data => {
  return http.get(`${ucHost}/org/locate`, data, option);
  // return http.get(`http://*************:19802/org/locate`, data, option);
}
