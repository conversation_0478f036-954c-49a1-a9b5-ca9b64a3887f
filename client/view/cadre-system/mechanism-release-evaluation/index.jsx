import React, { useState, useMemo, useEffect, useRef } from "react";
import "./index.less";
import SearchHeader from "client/components/search-header";
import { Form, Tabs, Input, Button, DatePicker, message } from "antd";
const { TabPane } = Tabs;

import SelectTable from "./components/select-table";
import moment from "moment";
import {
  findAppraisalUser3,
  initiateEvaluation4,
  getEvalDetail,
  // findAppraisalUser1,
  getEvalDetail1,
} from "client/apis/cadre-portrait";
/**
 * @description: 发布测评
 * @return {*}
 */
const index = ({ history, location }) => {
  const query = new URLSearchParams(location.search);

  const org_id = query.get("org_id");
  const evaluation_id = query.get("evaluation_id");
  const [pattern_sub, setPatternSub] = useState(query.get("pattern_sub"));

  const formInputRef = useRef(null);

  const [delEvaluationUserIds, setDelEvaluationUserIds] = useState([]); // 被删除的测评用户id数组

  const [delEvaluationOrgIds, setDelEvaluationOrgIds] = useState([]);

  const [formState, setFormState] = useState({ test_name: "", time: "" });

  const [dataSource, setDataSource] = useState([]);
  /**
   *org	array-EvaluationOrgInfo	是	班子名单
    principal	array-EvaluationOrgInfo	是	县管正职名单
    chair	array-EvaluationOrgInfo	是	人大主席名单
    */
  /**
   sequence_name	String	是	序列名称
   num	number	是	好等次个数
   job_type	number	否	1.书记 2.镇长（需要区分书记，镇长的才有此字段）
   orgs	array-EvaluationOrgInfoDetail	否	班子名单列表
   users	array-EvaluationUserInfoDetail	否	县管正职名单列表，人大主席列表
   */
  const [data, setData] = useState({
    "evaluation_id": undefined,
    "end_time": "",
    "eval_name": "",
    "org_id": 0,
    "org_list": []
  });
  /**
   * @description: 副作用
   * @return {*}
   */
  useEffect(() => {
    loadDetail();
  }, []);
  /**
   * @description: 加载数据
   * @return {*}
   */
  const loadData = () => {
    findAppraisalUser3({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setDataSource(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  /**
   * @description: 获取详情
   * @return {*}
   */
  const loadDetail = () => {
    findAppraisalUser3({ evaluation_id }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;

        const { eval_name, end_time } = data;

        formInputRef.current.setFieldsValue({
          eval_name,
          end_time: end_time && moment(end_time),
        });

        setData(data);
      }
    });
  };
  // 返回
  const handleBack = () => {
    history.goBack();
  };

  // 测试名输入
  const onDateChange = (value, dateString) => {
    console.log("Selected Time: ", value);
    console.log("Formatted Selected Time: ", dateString);
  };

  const onDateOk = (value) => {
    console.log("onOk: ", value);
  };
  /**
   * @description: 复选框
   * @param {*} org_id
   * @param {*} checked
   * @return {*}
   */
  const onCheckChange = ({ org_id, checked }) => {
    dataSource.forEach((item) => {
      if (item.org_id === org_id) {
        item.checked = checked;
        // 编辑时，取消勾选，删除用户
      }
    });

    setDataSource([...dataSource]);
  };

  const onSelectChange = ({ org_id, user_list, group }) => {
    const findIndex = dataSource.findIndex((item) => item.org_id === org_id);

    if (findIndex !== -1) {
      dataSource[findIndex].user_list = user_list;
      dataSource[findIndex].group = group;
      setDataSource([...dataSource]);
    }
  };
  /**
   * @description: 发布
   * @return {*}
   */
  const onSubmit = () => {
    /**
    evaluation_id	number	否	测评表id
    end_time	string	是	截至时间
    eval_name	string	是	测评名称
    pattern_sub	number	是	组织等级标识: 1:乡镇 2:一级部门 3:二级部门
    org_id	number	是	发起测评组织id
    user_list	list-UserInfo	是	测评用户
    del_evaluation_user_ids	list-number	否	被删除的测评用户id数组
   */

    const { getFieldsValue } = formInputRef.current;

    // 基础信息
    const evaBaseInfo = getFieldsValue();

    if (!evaBaseInfo.eval_name) {
      message.error("请输入测评名称");
      return;
    }

    if (!evaBaseInfo.end_time) {
      message.error("请选择测评截至时间");
      return;
    }
    // 处理数据
    const user_list = [];
    // 序列信息
    // sequence	number	是	机构序号
    // num	number	是	好等次个数
    // job_type	number	否	职务类型 1-书记；2-镇长
    // type	number	是	1-班子名单 2-县管正职名单 3-人大主席名单
    // seq	number	是	序号
    const sequence_list = [];


    // 组织列表
    const org_list = [];

    data.org_list.forEach((item) => {
      item.orgs.map((org) => {
        org_list.push({ ...org, sequence: item.sequence });
      });

      const { sequence, num, job_type, seq } = item;
      const _ = {
        sequence,
        num,
        type: 1,
        seq,
      };

      sequence_list.push(_);
    });

    const params = {
      eval_name: evaBaseInfo.eval_name,
      end_time: moment(evaBaseInfo.end_time).format("YYYY-MM-DD HH:mm:ss"),
      evaluation_id,
      org_id,
      org_list,
      del_evaluation_org_ids: delEvaluationOrgIds,
    };

    initiateEvaluation4(params).then((res) => {
      if (res.data.code === 0) {
        message.success("发布成功");

        history.goBack();
      } else {
        message.error(res.data.message);
      }
    });
  };

  /**
   * @description: 更新数据
   * @param {*} data  type 父字段，子字段
   * @return {*}
   */
  // const onUpdateData = (type, sub_type) => {
  //   return ({ sequence_name, user_list }) => {
  //     const findIndex = dataSource.findIndex((item) => item.org_id === org_id);

  //     if (findIndex !== -1) {
  //       dataSource[findIndex].user_list = user_list;

  //       setDataSource([...dataSource]);
  //     }
  //   };
  // };
  // 班子更新
  const onUpdateData = ({ sequence_name, user_list, sequence, index: _index }) => {

    const current_seq = data.org_list.find(item => item.sequence === sequence)

    current_seq.orgs = user_list


    setData({ ...data });
  }
  /**
   * @description: 更新次数
   * @param {*} number
   * @param {*} type
   * @return {*}
   */
  const onUpdateNumberChange = ({ org_id, number, sequence }) => {

    const current_seq = data.org_list.find(item => item.sequence === sequence)

    const current_org = current_seq.orgs.find(item => item.org_id === org_id)

    current_org.num = number;

    setData({ ...data });
  };
  // 删除
  const onDelete = (user_id) => {
    setDelEvaluationUserIds([...delEvaluationUserIds, user_id]);
  };
  // 班子删除id
  const onOrgDelete = (org_id) => {
    setDelEvaluationOrgIds([...delEvaluationOrgIds, org_id]);
  };
  // 职务类型
  const onSelectJobTypeChange = (type) => {
    return ({ sequence, value }) => {
      data[type].forEach((item) => {
        if (item.sequence === sequence) {
          item.job_type = value;
        }
      });

      setData({ ...data });
    };
  };

  const FormCustom = ({ form }) => {
    const { getFieldDecorator } = form;
    return (
      <Form layout="inline">
        <Form.Item label="测评名称">
          {getFieldDecorator("eval_name", {
            rules: [{ required: true, message: "请输入测评名称" }],
          })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
        </Form.Item>
        <Form.Item label="测评截至时间">
          {getFieldDecorator("end_time", {
            rules: [{ required: true, message: "请选择" }],
          })(
            <DatePicker
              style={{ width: "300px" }}
              showTime={false}
              placeholder="请选择"
              onChange={onDateChange}
              onOk={onDateOk}
            />
          )}
        </Form.Item>
      </Form>
    );
  };

  // 测试相关信息
  const FormInput = useMemo(() => Form.create()(FormCustom), []);

  return (
    <div className="middle-release-evaluation">
      <SearchHeader
        onBack={handleBack}
        title={evaluation_id ? "编辑测评" : "发布测评"}
      />
      <div className="evaluation-box">
        <FormInput ref={formInputRef} />
        <div className="table-list-box">
          {data.org_list.map(({ orgs, ...item }, index) => {
            return (
              <SelectTable
                type="1"
                {...item}
                key={index}
                index={index}
                dataSource={orgs}
                onDelete={onOrgDelete}
                onUpdateData={onUpdateData}
                onCheckChange={onCheckChange}
                onSelectChange={onSelectChange}
                onUpdateNumberChange={onUpdateNumberChange}
              />
            );
          })}
        </div>
      </div>
      <div className="button-box">
        <Button
          type="primary"
          className="button"
          size="large"
          onClick={onSubmit}
        >
          发布
        </Button>
      </div>
    </div>
  );
};

export default index;
