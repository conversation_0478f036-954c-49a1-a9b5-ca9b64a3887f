import { ucHost, evalHost, activityHost } from './config';
import { http, oId } from 'client/tool/axios';

export const fetchOrganisitionList = (name = '', page = 1, pageSize = 999, oid = '') => {
    return http.get(`${ucHost}/org/list`, { name, page, pagesize: pageSize, oid });
};

//http://************:8090/pages/viewpage.action?pageId=11764461
//获取组织树数据，load_root参数，是否加载根节点，默认为1,增加必传参数tree_type，默认值为1,父树
export const fetchOrganisitionTree = ({ org_id, org_type, tree_type = 2, load_root = 0 }) => {
    return http.get(`${ucHost}/org/tree`, { org_id, tree_type, load_root, org_type });
}

// 根据组织org_id查询人员
export const getUserListByOrgId = ({ orgId = 0, page = 1, pagesize = 10 }) => {
    return http.post(`${ucHost}/org/user/orgUserList`, { orgId, page, pagesize });
}


//http://************:8090/pages/viewpage.action?pageId=15630947
// 根据组织org_id 查询下级所有组织 包含子组织 和 后代组织
export const getAllOrgChildren = ({ org_id = 0, is_include = 0 }) => {
  return http.get(`${ucHost}/org/find-all-child-org`, { org_id, is_include });
}


//http://************:8090/pages/viewpage.action?pageId=16254117
//1030003040-根据组织ID查询所有上级组织
export const getAllOrgParents = (params) => {
    const defaultParams = { org_id: oId }

    return http.get(`${ucHost}/org/find-all-parent-org`, { ...defaultParams, ...params });
}

//http://************:8090/pages/viewpage.action?pageId=20644340
// 104001000804-分组列表
export const getGrouplist = () => {
    return http.get(`${evalHost}/group/list`);
}

//http://************:8090/pages/viewpage.action?pageId=20644347
// 104001000806-查询关联组织
export const getGroupOrglist = (group_ids = []) => {
    group_ids = group_ids.join(',')
    return http.get(`${evalHost}/group/orgs`, { group_ids });
}

// http://************:8090/pages/viewpage.action?pageId=22610522
// 1040020037006-党小组成员-添加党小组成员-查询所有选择成员
export const getOrgGroupMemberFindByUser = (queryparams = {}) => {
    return http.get(`${ucHost}/org-group-member/find-by-user`, queryparams);
}

// http://************:8090/pages/viewpage.action?pageId=22611453&tdsourcetag=s_pctim_aiomsg
// 1040020041-根据组织类型获取树类型
export const getFindTreeTypeByOp = (queryparams = {}) => {
    return http.get(`${ucHost}/find-tree-type-by-op`, queryparams);
}

// http://************:8090/pages/viewpage.action?pageId=22611720
// 1040020042-查询所有用户信息-领导班子
export const getFindAllUser = (queryparams = {}) => {
    return http.get(`${ucHost}/org/user/find-all-user`, queryparams);
}