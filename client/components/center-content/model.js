import {message} from 'antd';
import {getUserList} from 'apis/users';
import {getDepList} from 'apis/users';

export default {
    // 模型里面的子类
    namespace: 'centerContent',

    state: {
        dataList: [],
        departmentlist: [],
        listLoading: false,
        pageNew: {
            pageNum: '',
            pageSize: '',
            pages: '',
            total: ''
        },
        //标志位，标志当前选中人员是否发生变化，默认值为false
        selectChange: false
    },

    // 定义业务交互层
    effects: {
        // 加载数据
        * load({payload: {name = '', page = 1, depId = ''}}, {put, select, call}) {
            // 显示loading
            // console.log('page1' + page);
            yield put({type: 'save', payload: {listLoading: true}});
            // 加载列表数据
            const result = (yield getUserList({name, page, depId})).data;

            yield put({type: 'save', payload: {listLoading: false}});
            // 如果请求错误
            if (result.code !== 0) return message.error(result.message);
            yield put({
                type: 'setPage',
                payload: {
                    pageNum: result.pageNum,
                    pageSize: result.pageSize,
                    pages: result.pages,
                    total: result.total
                }
            });
            yield put({type: 'save', payload: {dataList: result.data, listLoading: false}});

        },
        //获取部门列表
        * loadDep({}, {put, select, call}) {
            // 加载列表数据
            const result = (yield getDepList()).data;

            // 如果请求错误
            if (result.code !== 0) return message.error(result.message);

            yield put({type: 'save', payload: {departmentlist: result.data}});
        }
        //查询人员
        // dispatch( { type: 'centerContent/searchData',payload:{name: values.name , depId:values.depId} } );
        // * searchData({payload: {name = '', page = 1, depId = ''}}, {put, select, call}) {
        //     // 显示loading
        //     yield put({type: 'save', payload: {listLoading: true}});
        //     // 加载列表数据
        //     const result = (yield getUserList({name, page, depId})).data;
        //
        //     yield put({type: 'save', payload: {listLoading: false}});
        //     // 如果请求错误
        //     if(result.code !== 0) return message.error(result.message);
        //     yield put( {
        //         type: 'setPage',
        //         payload: { pageNum:result.pageNum ,
        //             pageSize:result.pageSize ,
        //             pages:result.pages,
        //             total:result.total  }
        //     } );
        //     yield put({type: 'save', payload: {dataList: result.data, listLoading: false}});
        //
        // }
    },
    reducers: {
        save(state, {payload}) {
            return {...state, ...payload};
        },
        setPage(state, {payload}) {
            return {...state, pageNew: {...state.pageNew, ...payload}};
        }
    }
}

// var a = {obj : {a:1,b:2}}

//var {obj:{a=1,b=3}} = a;
