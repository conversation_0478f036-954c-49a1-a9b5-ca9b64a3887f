import QRCode from "qrcode.react";
import html2canvas from "html2canvas";
import SearchHeader from "client/components/search-header";
import DropText from "../create-evaluation/components/DropText";
import JSZip from "jszip";

import { cadreMobile } from "client/apis/config";
// import { setQrCodeInfo } from "client/apis/cadre-portrait";
import { getOpList } from "client/apis/cadre-portrait";
import {
  getRelationshipsDict,
  getSaasEvaluationQrcode,
} from "client/apis/evaluation-publish";

import "./anchor.less";

export default function SassXianguan(props) {
  const { history, location, orgId, evaluationId, type, evaluationName } =
    props;

  const MobileUrl =
    type === "8" ? `${cadreMobile}/banzi/` : `${cadreMobile}/zhongceng/`;

  const qrcodeRef = React.useRef([]);
  const [loading, setLoading] = React.useState(true);
  const [state, setState] = React.useState([]);
  const [ticketType, setTicketType] = React.useState([]);

  const loadData = () => {
    setLoading(true);
    getSaasEvaluationQrcode(evaluationId).then((res) => {
      if (res.data.code === 0) {
        setState(res.data.data);
        setLoading(false);
      } else {
        Ant.message.error(res.data.message);
      }
    });
  };

  // 获取打票类型字典
  const loadDictType = () => {
    getOpList({ code: 96180 }).then((res) => {
      if (res.data.code === 0) {
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.op_value,
            key: item.op_key,
          };
        });
        setTicketType(res.data.data);
      } else {
        Ant.message.error(res.data.message);
      }
    });
  };

  const loadDictTypeForRela = () => {
    getRelationshipsDict({ eval_type: type }).then((res) => {
      if (res.data.code === 0) {
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.value,
            key: item.key,
          };
        });
        setTicketType(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };

  React.useEffect(() => {
    loadData();
    // loadDictType();
    loadDictTypeForRela();
  }, []);

  // const onUpdateEvaluation = (params) => {
  //   setLoading(true);
  //   setQrCodeInfo(params).then((res) => {
  //     if (res.data.code === 0) {
  //       loadData();
  //     } else {
  //       Ant.message.error(res.data.message);
  //       setLoading(false);
  //     }
  //   });
  // };

  const option = [
    { label: "县管正职", key: 1 },
    { label: "县管副职", key: 2 },
    { label: "改非干部", key: 3 },
    { label: "新提拔中层干部", key: 4 },
    { label: "中层干部", key: 5 },
  ];
  const getValue = (value) => {
    const item = option.find((item) => Number(item.key) === Number(value));
    return item ? item.label : "";
  };

  const columns = [
    {
      title: "被测评人",
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "10%",
    },
    {
      title: "干部类别",
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      width: "10%",
      render: (t) => getValue(t),
    },
    {
      title: "职务",
      dataIndex: "job_name",
      key: "job_name",
      align: "left",
      editable: true,
      width: "25%",
    },
    {
      title: "打票类型",
      dataIndex: "relationship",
      key: "relationship",
      align: "center",
      width: "20%",
      render(value, { pms_evaluation_qrcode_id }) {
        return (
          <DropText
            value={value}
            disabled
            // onSelect={(value) => {
            //   onUpdateEvaluation({
            //     pms_evaluation_qrcode_id,
            //     relationship: value,
            //   });
            // }}
            option={ticketType}
          />
        );
      },
    },
    {
      title: "测评开关",
      dataIndex: "status",
      key: "status",
      align: "center",
      render(t, { pms_evaluation_qrcode_id }) {
        const status = Number(t) === 1 ? true : false;
        return (
          <Ant.Switch defaultChecked size="small" checked={status} disabled />
        );
      },
    },
    {
      title: "特征标签",
      dataIndex: "tag",
      key: "tag",
      align: "center",
      render(_, { pms_evaluation_qrcode_id }) {
        const status = Number(_) === 1 ? true : false;
        return (
          <Ant.Switch defaultChecked size="small" checked={status} disabled />
        );
      },
    },
    {
      title: "一言素描",
      dataIndex: "sketch",
      key: "sketch",
      align: "center",
      render(_, { pms_evaluation_qrcode_id }) {
        const status = Number(_) === 1 ? true : false;
        return (
          <Ant.Switch defaultChecked size="small" checked={status} disabled />
        );
      },
    },
  ];

  const onDownload = async (title, index) => {
    const el = qrcodeRef.current[index];
    if (!el) return;
    const canvas = await html2canvas(el);
    const imageURL = canvas.toDataURL("image/png");
    const tempLink = document.createElement("a");
    tempLink.id = "down-a";
    tempLink.download = title;
    tempLink.href = imageURL;
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
  };

  const [btnLoading, setBtnLoading] = React.useState(false);
  const multipleDownload = async () => {
    setBtnLoading(true);
    const zip = new JSZip();

    const imageUrls = state.map(async (item, i) => {
      const el = qrcodeRef.current[i];
      if (!el) return;
      const canvas = await html2canvas(el);
      const imageURL = canvas.toDataURL("image/png");
      return imageURL;
    });

    for (let i = 0; i < imageUrls.length; i++) {
      const name = state[i].qr_code_type_name || "image" + (i + 1);
      const url = await imageUrls[i];
      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error(`Failed to fetch image: ${url}`);
        const blob = await response.blob();
        zip.file(`${name}.png`, blob); // 根据需要修改文件名和格式
      } catch (error) {
        console.error(`Error downloading image ${url}:`, error);
      }
    }

    zip
      .generateAsync({ type: "blob" })
      .then((content) => {
        const a = document.createElement("a");
        const url = window.URL.createObjectURL(content);
        a.href = url;
        a.download = "测评码.zip";
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => console.error("Error generating zip:", err))
      .finally(() => {
        setBtnLoading(false);
      });
  };

  return (
    <div>
      <SearchHeader
        title="下载测评码"
        onBack={() => {
          history.goBack();
        }}
      />

      <div>
        {loading && (
          <Ant.Row type="flex" justify="center">
            <Ant.Spin tip="正在加载..." />
          </Ant.Row>
        )}

        <Ant.Anchor targetOffset={200} onClick={(e) => e.preventDefault()}>
          <Ant.Row
            type="flex"
            align="middle"
            justify="space-between"
            style={{ width: "100%" }}
          >
            <Ant.Col
              span={21}
              style={{
                display: "flex",
                flexWrap: "wrap",
                borderBottom: "1px solid #e8e8e8",
              }}
            >
              {state.map((item) => {
                return (
                  <Ant.Anchor.Link
                    href={"#acl" + item.qr_code_type}
                    title={
                      item.qr_code_type_name
                        ? item.qr_code_type_name
                        : item.qr_code_type
                    }
                  />
                );
              })}
            </Ant.Col>
            <Ant.Col>
              <Ant.Button
                type="primary"
                loading={btnLoading}
                onClick={multipleDownload}
              >
                批量下载测评码
              </Ant.Button>
            </Ant.Col>
          </Ant.Row>
        </Ant.Anchor>
        {state.map((item, index) => {
          return (
            <div
              style={{ padding: 16 }}
              key={item.qr_code_type}
              id={"acl" + item.qr_code_type}
            >
              <Ant.Row type="flex" align="middle" style={{ marginBottom: 12 }}>
                <div
                  style={{
                    marginRight: 8,
                    width: 4,
                    height: 16,
                    background: "#1890FF",
                  }}
                />
                <div style={{ fontSize: 16, fontWeight: 800 }}>
                  {item.qr_code_type_name}测评码
                </div>
                <div
                  style={{
                    margin: "0 8px",
                    width: 128,
                    borderBottom: "1px dotted #1890FF",
                  }}
                />
                <div>
                  回收票数：<span style={{ color: "#f58078" }}>-</span>
                </div>
              </Ant.Row>
              <Ant.Row type="flex">
                <Ant.Col style={{ flex: 1 }}>
                  <Ant.Table
                    tableLayout="fixed"
                    columns={columns}
                    dataSource={item.info_list}
                    bordered
                    scroll={{ y: 400 }}
                    pagination={false}
                  />
                </Ant.Col>
                <div
                  ref={(el) => (qrcodeRef.current[index] = el)}
                  style={{
                    width: 350,
                    height: 409,
                    border: "1px solid #F46E65",
                    borderRadius: 4,
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      height: 355,
                    }}
                  >
                    <QRCode
                      value={
                        item.qr_code_type === "management_code"
                          ? `${MobileUrl}managecode?evaluation_id=${evaluationId}&org_id=${orgId}&eval_partyment_id=${19}&title=${evaluationName}`
                          : `${MobileUrl}?evaluation_id=${evaluationId}&org_id=${orgId}&eval_partyment_id=${19}&qrcode_type=${
                              item.qr_code_type
                            }`
                      }
                      size={310}
                      fgColor="#000000"
                      style={{ margin: "auto" }}
                    />
                  </div>
                  <Ant.Row
                    type="flex"
                    justify="space-between"
                    align="middle"
                    style={{
                      padding: "0 16px",
                      height: 54,
                      background: "#F46E65",
                      color: "#fff",
                      fontSize: 18,
                    }}
                  >
                    <div>{item.qr_code_type_name}测评码</div>
                    <div
                      onClick={() => onDownload(item.qr_code_type_name, index)}
                      style={{ fontSize: 14, cursor: "pointer" }}
                    >
                      <Ant.Icon type="download" />
                      下载二维码
                    </div>
                  </Ant.Row>
                </div>
              </Ant.Row>
            </div>
          );
        })}
      </div>
    </div>
  );
}
