const { http } = require('client/tool/axios');
import { sas1Host, fileHost } from './config';

//4010202090301-检测组织类型(第一步) http://wiki.aidangqun.com/project/8?p=1704
export const getOrganization = (data) => {
    return http.get(`${sas1Host}/vital/check-org-type`, data);
}

//4010202090302-活力指数柱状图统计(顶级) http://wiki.aidangqun.com/project/8?p=1705
export const getHistogramTop = (data) => {
    return http.get(`${sas1Host}/vital/org-columnar-sta`, data);
}

//4010202090303-组织活力指数拆线图(顶级) http://wiki.aidangqun.com/project/8?p=1706
export const getLineChartTop = (data) => {
    return http.get(`${sas1Host}/vital/org-stripping-sta`, data);
}

//4010202090304-组织活力指数排名(顶级) http://wiki.aidangqun.com/project/8?p=1707
export const getOrganizationRankingTop = (data) => {
    return http.get(`${sas1Host}/vital/org-pc`, data);
}

//4010202090305-用户活力指数拆线图(顶级) http://wiki.aidangqun.com/project/8?p=1708
export const getUserRankingTop = (data) => {
    return http.get(`${sas1Host}/vital/user-stripping-sta`, data);
}
//4010202090306-党员活力指数排名(顶级) http://wiki.aidangqun.com/project/8?p=1709
export const getUserTop = (data) => {
    return http.get(`${sas1Host}/vital/party-member-pc`, data);
}


//4010202090316-党支部图 http://wiki.aidangqun.com/project/8?p=1736
export const getVitalityIndexRanking = (data) => {
    return http.get(`${sas1Host}/vital/select-basic-org`, data);
}
//4010202090314-查询基础信息 http://wiki.aidangqun.com/project/8?p=1726
export const getBasicInformation = (data) => {
    return http.get(`${sas1Host}/vital/select-basic`, data);
}
//4010202090317-全组织信息 http://wiki.aidangqun.com/project/8?p=1737
export const getSystemMean = (data) => {
    return http.get(`${sas1Host}/vital/select-all-org-info`, data);
}

//4010202090310-党员概况 http://wiki.aidangqun.com/project/8?p=1719
export const getUserRanking = (data) => {
    return http.get(`${sas1Host}/vital/select-user-avg`, data);
}
// 4010202090311-党员活力指数榜 http://wiki.aidangqun.com/project/8?p=1720
export const getUser = (data) => {
    return http.get(`${sas1Host}/vital/select-user-list`, data);
}
// 4010202090312-党员党组织指标党支部 http://wiki.aidangqun.com/project/8?p=1721
export const getBranchTarget = (data) => {
    return http.get(`${sas1Host}/vital/select-target`, data);
}
//4010202090309-组织活力排行榜 http://wiki.aidangqun.com/project/8?p=1718
export const getOrganizationalVitalityRanking = (data) => {
    return http.get(`${sas1Host}/vital/select-org-list`, data);
}
//4010202090308-组织活力指数图 http://wiki.aidangqun.com/project/8?p=1717
export const getstreetRanking = (data) => {
    return http.get(`${sas1Host}/vital/select-avg-org`, data);
}

//首页部分结束

//4010202090315-个人活力指数 http://wiki.aidangqun.com/project/8?p=1734
export const getPersonalVitalityRanking = (data) => {
    return http.get(`${sas1Host}/vital/sta-uservital-detail`, data);
}
//4010202090315-个人活力指数导出 http://wiki.aidangqun.com/project/8?p=1734
export const getPersonalVitalityRankingLeadingOut = (data) => {
    return http.get(`${sas1Host}/vital/sta-uservital-detail-download`, data);
}
//40302020202-查询通用上传结果(前端调用) http://wiki.aidangqun.com/project/9?p=1385
export const SearchToken = (payload) => {
    return http.get(`${fileHost}/file/upload/common/result`, payload);
};

// 4010202090307-组织活力指数榜pc端 http://wiki.aidangqun.com/project/8?p=1716
export const getOrganizationRanking = (payload) => {
    return http.get(`${sas1Host}/vital/sta-vital-index-list`, payload);
};
// 4010202090307-组织活力指数榜pc端导出 http://wiki.aidangqun.com/project/8?p=1716
export const getOrganizationRankingLeadingOut = (payload) => {
    return http.get(`${sas1Host}/vital/sta-vital-index-list-download`, payload);
};
// 4010202090318-党员活力指数榜 http://wiki.aidangqun.com/project/8?p=1761
export const getMemberRanking = (payload) => {
    return http.get(`${sas1Host}/vital/sta-user-vital-index-list`, payload);
};
// 4010202090318-党员活力指数榜导出 http://wiki.aidangqun.com/project/8?p=1761
export const getMemberRankingLeadingOut = (payload) => {
    return http.get(`${sas1Host}/vital/sta-user-vital-index-list-download`, payload);
};