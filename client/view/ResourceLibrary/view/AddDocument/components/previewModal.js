import React, { useState } from "react";
import { Modal, Carousel } from "antd";
import { CDN } from "apis/config";
import ShowUploadFileType from "../../../components/showUploadFileType";
import moment from "moment";
import fileIcon from "../../../image/file-icon.png";
import FileDownload from "client/components/file-download";

const tabName = [
  { label: "PC端", key: 1 },
  { label: "移动端", key: 2 },
];
const previewModal = (props) => {
  const {
    visible,
    onCancel,
    previewModalData: {
      tags,
      name,
      sources,
      summary,
      content,
      create_time,
      resource_files,
      provider_org_name,
    },
  } = props;

  const [tabKey, setTabKey] = useState(1);
  let imgList = [];
  let videoList = [];
  let docList = [];
  resource_files &&
    resource_files.map((item) => {
      if (/\.(jpg|jpeg|png)$/i.test(item.path)) {
        imgList.push(item);
      } else if (/\.(mp4|flv|mov|mkv|avi)$/i.test(item.path)) {
        videoList.push(item);
      } else {
        docList.push(item);
      }
    });

  console.log(props);
  return (
    <Modal
      title="预览"
      className="case-preview-modal"
      width={1513}
      maskClosable={false}
      visible={visible}
      footer={false}
      onCancel={onCancel}
    >
      <div className="preview-modal-tab">
        {tabName.map((item) => {
          return (
            <div
              key={item.key}
              className={`preview-modal-tab-item ${
                tabKey === item.key ? "active" : "inactive"
              }`}
              onClick={() => {
                if (tabKey !== item.key) {
                  setTabKey(item.key);
                }
              }}
            >
              {item.label}
            </div>
          );
        })}
      </div>
      <div className="preview-modal-content">
        {tabKey === 1 ? (
          <div className="preview-modal-content-pc">
            <div className="preview-modal-content-pc-title">{name}</div>
            <div className="preview-modal-content-pc-info">
              <span className="preview-modal-content-pc-info-v1">
                来自：{sources || "无"}
              </span>
              <span className="preview-modal-content-pc-info-v2">
                标签：
                {tags ? tags.replace(/,/g, "/") : "无"}
              </span>
              <span className="preview-modal-content-pc-info-v3">
                上传时间：
                {create_time
                  ? create_time
                  : moment().format("YYYY-MM-DD HH:mm")}
              </span>
            </div>
            {summary && (
              <div className="preview-modal-content-pc-abs">
                <span>摘要：</span>
                <span>{summary}</span>
              </div>
            )}
            {imgList && imgList.length !== 0 && (
              <div className="preview-modal-content-pc-carousel">
                <Carousel autoplay>
                  {imgList.map((item) => {
                    if (item) {
                      return (
                        <img
                          className="preview-modal-content-pc-carousel-item"
                          alt=""
                          src={`${CDN}/${item.path}`}
                        />
                      );
                    }
                  })}
                </Carousel>
              </div>
            )}
            <div
              className="preview-modal-content-pc-wrap"
              dangerouslySetInnerHTML={{
                __html: content,
              }}
            />
            <div className="preview-modal-content-pc-src">
              信息提供：{provider_org_name}
            </div>
            {resource_files && resource_files.length !== 0 && (
              <div className="preview-modal-content-pc-file">
                <div className="preview-modal-content-pc-file-label">
                  案例附件：
                </div>
                <ShowUploadFileType
                  canPreview={true}
                  data={resource_files}
                  isDelete={false}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="preview-modal-content-mobile">
            <div className="preview-modal-content-phone">
              <div className="preview-modal-content-phone-info">
                <div className="preview-modal-content-phone-info-v1">
                  {name}
                </div>
                <div className="preview-modal-content-phone-info-v2">
                  来源：{sources || "无"}
                </div>
                {tags && tags.split(",").length !== 0 && (
                  <div className="preview-modal-content-phone-info-v3">
                    {tags.split(",").map((item, index) => {
                      return <span key={index}>{item}</span>;
                    })}
                  </div>
                )}
              </div>
              {summary && (
                <div className="preview-modal-content-phone-abs">
                  <span>摘要：</span>
                  <span>{summary}</span>
                </div>
              )}
              {imgList && imgList.length !== 0 && (
                <div className="preview-modal-content-phone-img">
                  {imgList.map((item) => {
                    return (
                      <img
                        className="preview-modal-content-phone-img-item"
                        alt=""
                        src={`${CDN}/${item.path}`}
                      />
                    );
                  })}
                </div>
              )}
              <div
                className="preview-modal-content-phone-wrap"
                dangerouslySetInnerHTML={{
                  __html: content,
                }}
              />
              {videoList && videoList.length !== 0 && (
                <div className="preview-modal-content-phone-video">
                  <span>案例附件：</span>
                  <span>附件视频</span>
                  {videoList.map((item) => {
                    return (
                      <div className="video-item">
                        <video
                          className="video-item-container"
                          src={`${CDN}/${item.path}`}
                          controls="controls"
                          style={{ width: "100%" }}
                        >
                          您的浏览器不支持 video 标签。
                        </video>
                        <div className="video-item-title">{item.file_name}</div>
                      </div>
                    );
                  })}
                </div>
              )}
              {docList && docList.length !== 0 && (
                <div className="preview-modal-content-phone-doc">
                  <span>附件文件</span>
                  {docList.map((item, index) => {
                    return (
                      <div
                        className="preview-modal-content-phone-doc-item"
                        key={index}
                      >
                        <img src={fileIcon} />
                        <span>{`${item.file_name}(${(
                          item.size /
                          1024 /
                          1024
                        ).toFixed(2)}M)`}</span>
                        <FileDownload
                          filePath={`/file/file/download/${
                            item.id || item.file_id || item.name
                          }`}
                          fileName={
                            item.file_name || item.name || item.filename
                          }
                          type="link"
                          btnName="下载"
                        />
                      </div>
                    );
                  })}
                </div>
              )}
              <div className="preview-modal-content-phone-src">
                信息提供：{provider_org_name}
              </div>
              <div className="preview-modal-content-phone-date">
                上传时间：
                {create_time
                  ? create_time
                  : moment().format("YYYY-MM-DD HH:mm")}
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default previewModal;
