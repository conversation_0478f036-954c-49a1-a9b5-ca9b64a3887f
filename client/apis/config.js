//网关地址

/* --------微信中心  注意：需根据打包环境手动切换 -------------*/
// const wechatHost = `https://wechat.goodsogood.com`; //正式环境
const wechatHost = process.env.wechatUrl;
const isMaster = process.env.online_env === "master"
// 测试本地地址(加密)
// const Gateway = 'http://************:18080';
const Yapi = "http://*************:3000/mock/11";
// 测试本地地址(未加密)
const Gateway =
  isMaster
    ? `${window.location.origin}/owsz`
    : `${process.env.getway}`;
const Mobile = `${process.env.mobileUrl}`;
const CDN =
  isMaster
    ? window.location.origin + "/cdn/headers"
    : `${process.env.cdn}/cdn/headers`;
const BranchWindow = `${process.env.windowUrl}`;
const MsgCenter = `${process.env.msgCenter}`;
const cadreMobile = `${process.env.cadreMoblie}`;
// const mixmallHost =  `${process.env.mixmallHost}`;
// const Gateway = 'http://frp.2ba2.win:8019'
//  const Gateway =  'http://************:19802';

// 测试环境
// const Gateway = 'http://*************:8092';
// 生产环境
// const Gateway = 'https://ows.goodsogood.com/owsz';
// 生产环境(内网)
// const Gateway = 'http://zuul.goodsogood.online:8092';

//用户中心
const host = `${Gateway}/user`;
// const host = "http://**************:17106";
// http://**************:18108/owsz"
// const host = 'http://************:19802';

//登录
const loginHost = `${Gateway}/login`;

// 本地临时调试
const ucHost = `${Gateway}/user`;
// const ucHost = `http://*************:8106`;
// const ucHost = "http://*************:8080";
// 本地临时调试-干部画像 pms
const ucPms = `${Gateway}/pms`;
const ucPms1 = `${Gateway}/pms_eval`;
// 唐凯瑞
//const ucHost = "http://************:8080"

//工作流
const workflowHost = `${Gateway}/wf`;
// const workflowHost = 'http://************:19801';
//活动中心
const activityHost = `${Gateway}/ac`;
// const activityHost = 'http://************:19803';
//文件中心
const fileHost = `${Gateway}/file`;
const uploadHost = `${Gateway}/file`;
// const uploadHost = `${Gateway}/zuul/file`;
// const fileHost = 'http://************:19800';

//组织
// 本地临时调试
const orgHost = `${Gateway}/user`;
// const orgHost = "http://*************:8080";

// 积分中心
const crHost = `${Gateway}/cr`;
// 纪实系统
const mtHost = `${Gateway}/mt`;
// 会议类型管理
const mtmHost = `${Gateway}/mt`;

// 送服务
export const vlHost = `${Gateway}/vl`;

// 考核系统
const evalHost = `${Gateway}/eval`;

//执行任务
const taskHost = `${evalHost}/task`;
//审核任务
const approvalHost = `${evalHost}/approval`;

//物流接口
//const logisticsHost = 'http://*************:8013/gs_dicitem';
const logisticsHost = "https://tool.goodsogood.com/gs_dicitem";

//移动端地址配置信息
//开发环境
const mobileHost = `${Mobile}/ssr/sign-up-activity`;
//党务看板接口
const sasHost = `${Gateway}/sas/sas`;

const sas1Host = `${Gateway}/sas`;
// 推送中心
const pushHost = `${Gateway}/push`;
//党费相关接口
const party = `${Gateway}/pp`;
// const party = `http://*************:8141`;

//推送策略接口
const strategy = `${Gateway}/strategy`;
//云区
const cloud = `${Gateway}/ecp`;

//测试环境
// const mobileHost = 'http://***********:7002/ssr/sign-up-activity';
//九龙坡
// const mobileHost = 'https://owsj.goodsogood.com/ssr/sign-up-activity';
//市政府
// const mobileHost = 'https://ows.goodsogood.com/ssr/sign-up-activity';
//积分系统 key:'access_key':ows
const access_key = { access_key: "ows" };
const baweiyuzhenAccessKey = { access_key: "971ed796c576448ba66508c0582f75e0" };
const integrating = `${Gateway}/cr/score`;
//组织树查询
const organization = `${Gateway}/user/org`;
// click-spy
const csHost = `${Gateway}/click`;

export {
  Yapi,
  Gateway,
  Mobile,
  cadreMobile,
  CDN,
  MsgCenter,
  // mixmallHost,
  host,
  loginHost,
  ucHost,
  workflowHost,
  activityHost,
  fileHost,
  uploadHost,
  orgHost,
  wechatHost,
  crHost,
  mtHost,
  mtmHost,
  evalHost,
  taskHost,
  approvalHost,
  logisticsHost,
  mobileHost,
  sasHost,
  sas1Host,
  pushHost,
  party,
  strategy,
  organization,
  integrating,
  access_key,
  baweiyuzhenAccessKey,
  cloud,
  csHost,
  BranchWindow,
  ucPms,
  ucPms1,
};
