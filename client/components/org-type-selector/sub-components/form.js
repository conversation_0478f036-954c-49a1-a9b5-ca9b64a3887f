import React from "react";
import { Form, Select, Button } from "antd";

const { Option } = Select,
  FormItem = Form.Item;

export default Form.create()((props) => {
  const {
    form,
    typeList = [],
    selectedType,
    selectedTypeId,
    onChange,
    defaultSelectorOrgType,
    lockSelector
  } = props;
  const { getFieldDecorator, validateFields } = form;
  return (
    <div className="form-wrapper" >
      <div className="select-wrapper">
        <Form layout="inline" onSubmit={(e) => {
          e.preventDefault();
        }}>
          <FormItem label="所属类别">
            {
              getFieldDecorator("org_type", {
                initialValue: selectedType ? JSON.stringify(selectedType) : ""
              })(
                <Select
                  disabled={lockSelector}
                  style={{ width: 150 }}
                  placeholder="请选择"
                >
                  {
                    (typeList && Array.isArray(typeList)) &&
                    typeList.map((type, index) => {
                      return (
                        <Option key={type.op_key || index} value={JSON.stringify(type)}>
                          {type.op_value}
                        </Option>
                      )
                    })
                  }
                </Select>
              )
            }
          </FormItem>
        </Form>
      </div>
      <div className="button-wrapper">
        {
          !lockSelector &&
          <Button style={{ width: 90 }} type="primary" onClick={() => {
            validateFields((errors, values) => {
              // console.log(errors, values);
              if (!errors) {
                try {
                  const { org_type } = values;
                  const data = JSON.parse(org_type);
                  onChange && onChange(data)
                }
                catch (e) {
                  console.error(e);
                }
              }
            });
          }}>查  询</Button>
        }
      </div>
    </div>
  )
});
