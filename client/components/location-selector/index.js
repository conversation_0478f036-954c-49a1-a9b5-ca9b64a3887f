import React, { Component } from "react";
import { Input } from "antd";
import "./index.less";

import LocationModal from "./sub-component/location-modal";

import propTypes from "prop-types";

class LocationSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      location: "",
      address: "",
      current: {}
    }
  }
  componentDidMount() {
    // console.log(this.props);
    const { inputData } = this.props;
    
    // console.log(inputData);
    if (inputData) {
      this.setState({
        current: {
          location: inputData
        }
      });
      if (inputData.lat && inputData.lng) {
        this.setState({
          location: inputData.address || ""
        });
      } else {
        this.setState({
          address: inputData.address || ""
        })
      }
    }
  }
  componentWillReceiveProps(props) {
    // console.log(props);
    const { inputData } = props;
    if (inputData) {
      this.setState({
        current: {
          location: inputData
        }
      });
      if (inputData.lat && inputData.lng) {
        this.setState({
          location: inputData.address || ""
        });
      } else {
        this.setState({
          address: inputData.address || "",
          location: inputData.address || ""
        })
      }
    }
  }
  showModal() {
    this.setState({
      visible: true
    });
  }
  addressChangeHandler(e) {
    const { current } = this.state;
    const { onChange } = this.props;
    const { location } = current;
    const target = e.target;
    this.setState({
      location: target.value,
      address: target.value
    });
    const result = {
      address: target.value,
      // 高德导航
      // gps来源类型:1.高德/微信（GCJ02）、2.百度（百度坐标体系）、3.google（真实坐标）、4.gps设备（真实坐标）
      gps_type: 1,
      lng: (location && location.lng) ? String(location.lng) : "",
      lat: (location && location.lat) ? String(location.lat) : ""
    };
    onChange(result);
  }
  emitChange() {
    const { address, current } = this.state;
    const { onChange } = this.props;
    const { location } = current;
    const result = {
      address,
      // 高德导航
      // gps来源类型:1.高德/微信（GCJ02）、2.百度（百度坐标体系）、3.google（真实坐标）、4.gps设备（真实坐标）
      gps_type: 1,
      lng: (location && location.lng) ? String(location.lng) : "",
      lat: (location && location.lat) ? String(location.lat) : ""
    };
    onChange(result);
  }
  render() {
    const { visible, location, current } = this.state;
    const { location: local } = current;
    const { inputWidth, containerWidth, onChange, inputData, onlyText, onlySelector } = this.props;
    const locationModalProps = {
      inputData,
      visible,
      hideModal: () => {
        this.setState({
          visible: false
        });
      },
      onChange: (data) => {
        // console.log(data);
        // const { onChange } = this.props
        // 地图定位组件暴露出的所有属性
        const { location } = data;
        // const { address } = this.state;
        const result = {
          address: `${data.name || ""}${data.address || ""}`,
          // 高德导航
          // gps来源类型:1.高德/微信（GCJ02）、2.百度（百度坐标体系）、3.google（真实坐标）、4.gps设备（真实坐标）
          gps_type: 1,
          lng: location ? String(location.lng) : data.lng,
          lat: location ? String(location.lat) : data.lat
        };
        // debugger
        this.setState({
          address: result.address,
          current: data,
          location: result.address
        });
        onChange(result);
      }
    };
    return (
      <div className="location-selector" style={{ width: `${containerWidth}px` }}>
        {
          !onlyText &&
          <div className="location-status">
            <a onClick={() => this.showModal()}>定位</a>
            {/* {address || ""} */}
            {/* {
            JSON.stringify(current)
          }
          {
            JSON.stringify(local)
          } */}
            <span className="location-name">{(local && local.lng && local.lat) ? location : "(未定位)"}</span>
            <LocationModal {...locationModalProps} />
          </div>
        }
        {
          !onlySelector &&
          <div className="location-detail">
            <Input placeholder="请输入详细地点" style={{ width: `${inputWidth}px` }}
              value={location}
              onChange={(e) => { this.addressChangeHandler(e) }}
              onBlur={() => { this.emitChange() }}
            />
          </div>
        }
      </div>
    )
  }
}

LocationSelector.propTypes = {
  containerWidth: propTypes.number,
  inputWidth: propTypes.number,
  onChange: propTypes.func,
  // value: propTypes.string,
  inputData: propTypes.shape({
    gps_type: propTypes.oneOf([1, 2, 3, 4]),
    lng: propTypes.oneOfType([propTypes.string, propTypes.number]),
    lat: propTypes.oneOfType([propTypes.string, propTypes.number]),
    address: propTypes.string
  }),
  // 定位类型，默认为1，高德导航
  gps_type: propTypes.oneOf([1, 2, 3, 4]),
  // 经纬度
  lng: propTypes.oneOfType([propTypes.string, propTypes.number]),
  lat: propTypes.oneOfType([propTypes.string, propTypes.number]),
  // 文本框填写地址
  address: propTypes.string,
  // 只有文本信息
  onlyText: propTypes.bool,
  // 只有定位选择
  onlySelector: propTypes.bool
};

LocationSelector.defaultProps = {
  containerWidth: 400,
  inputWidth: 400,
  gps_type: 1,
  lng: "",
  lat: "",
  address: "",
  onChange: (data) => { console.log("外部方法收到的数据", data) },
  onlyText: false,
  onlySelector: false
};

export default LocationSelector;