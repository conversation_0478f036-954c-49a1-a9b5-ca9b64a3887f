import React, { Component } from "react";
import LeftSider from "client/components/left-sider";
import MainContent from "./main-content";
import { Layout, Spin } from "antd";

export default class Class extends Component {
  render() {
    const { mainProps, leftSiderProps } = this.props;
    const { orgTreeTabList, isInitLoading } = leftSiderProps;
    const isData = orgTreeTabList.length && orgTreeTabList[0].tree_id;
    let contentHTML = <center>暂无信息</center>;
    if (isData) {
      contentHTML = (
        <Layout className="member-manage-main">
          <LeftSider {...leftSiderProps} />
          <MainContent
            {...mainProps}
            onRef={(instance) => this.props.onRef(instance)}
            onTableRef={(instance) => this.props.onTableRef(instance)}
          />
        </Layout>
      );
    }
    return (
      <Spin size="large" spinning={isInitLoading} wrapperClassName="spin-wrap">
        {contentHTML}
      </Spin>
    );
  }
}
