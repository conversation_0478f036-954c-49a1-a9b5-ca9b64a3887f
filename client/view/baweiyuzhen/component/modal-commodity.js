import React, { Component } from 'react';
import { Modal, Form, Input, Button, Radio, message } from 'antd';
import { connect } from 'dva';
import Image from './upload-image';
import './index.less';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 4 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 20 },
    sm: { span: 20 },
  },
};

class ModalCommodity extends Component {
  componentDidMount() {}
  componentDidUpdate(prevProps) {
    const { baweiyuzhen, form } = this.props;
    const { show, index } = baweiyuzhen.status.commodity_list;
    if (show && show !== prevProps.baweiyuzhen.status.commodity_list.show) {
      if (index !== undefined) {
        const data = baweiyuzhen.formData.commodity_list[index];
        form.setFieldsValue(data);
      }
    }
  }

  onSubmit() {
    const { form, baweiyuzhen, dispatch } = this.props;
    form.validateFields((err, value) => {
      if (err) return;
      dispatch({
        type: 'baweiyuzhen/submitModal',
        payload: { data: value, type: 'commodity_list' },
      });
      this.onCancel();
    });
  }
  onCancel() {
    const { dispatch, form } = this.props;
    form.resetFields();
    dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: { commodity_list: { show: false } },
    });
  }

  render() {
    const { baweiyuzhen, form } = this.props;
    if (!baweiyuzhen) {
      return null;
    }
    const { commodity_list } = baweiyuzhen.status;
    const { index, show } = commodity_list;
    const { getFieldValue, getFieldDecorator } = form;
    return (
      <div>
        <Modal
          title={`${index === undefined ? '新增' : '编辑'}本期推荐商品`}
          visible={show}
          width={660}
          wrapClassName="modal-baweiyuzhen"
          bodyStyle={{ minHeight: 400, width: 660, margin: 'auto' }}
          destroyOnClose
          onCancel={this.onCancel.bind(this)}
          footer={null}>
          <Form>
            <FormItem {...formItemLayout} label="名称">
              {getFieldDecorator('name', {
                rules: [
                  {
                    required: true,
                    message: '请填写名称',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="图片">
              {getFieldDecorator('image_url', {
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<Image size={{ w: 300, h: 200 }} ratio={3} />)}
            </FormItem>
            <FormItem {...formItemLayout} label="价格">
              {getFieldDecorator('price', {
                rules: [
                  { required: true, message: '请填写价格' },
                  { pattern: new RegExp(/^-?[0-9]*(\.[0-9]{0,2})?$/, 'g'), message: '请输入正确的价格，最多两位小数' },
                ],
              })(<Input style={{ width: 450 }} suffix="元" placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="商品描述">
              {getFieldDecorator('description', {
                rules: [
                  {
                    required: true,
                    message: '请填写商品描述',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="底部标签">
              {getFieldDecorator('bottom_label', {
                rules: [
                  {
                    required: true,
                    message: '请填写底部标签',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>

            <FormItem {...formItemLayout} label="商品ID">
              {getFieldDecorator('commodity_id', {
                rules: [
                  {
                    required: true,
                    message: '请填写ID',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label={'商品链接'}>
              {getFieldDecorator('target_url', {
                rules: [
                  {
                    required: true,
                    message: '请填写链接地址',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="状态">
              {getFieldDecorator('is_show', {
                rules: [
                  {
                    required: true,
                    message: '请选择显示状态',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={1}>显示</Radio>
                  <Radio value={0}>隐藏</Radio>
                </Radio.Group>
              )}
            </FormItem>
          </Form>
          <div className="buttons-wrapper">
            <Button
              onClick={() => {
                this.onCancel();
              }}>
              取消
            </Button>
            <Button type="primary" onClick={() => this.onSubmit()}>
              确定
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}
export default connect(({ baweiyuzhen }) => ({ baweiyuzhen }))(Form.create()(ModalCommodity));
