import React, {Component} from 'react';
import {Table} from 'antd';
import ArrowLabel from 'client/components/arrow-label';

export default class Class extends Component {
	render() {
		const {
			loading,
			dataList = [],
			total,
			params: {
				page_no,
				page_size
			},
			history,
			onChange = undefined
		} = this.props;
		const {_oid = ''} = (window.sessionStorage || {});
		const columns = [
			{
				title: '组织名称',
				dataIndex: 'organization_name',
				align: 'center',
				// width: 470
			},
			{
				title: '状态',
				align: 'center',
				width: 200,
				render: ({status}) => {
					const map = {
						1: {text: '未启动', theme: 'blue'},
						2: {text: '已结束', theme: 'gray'},
						3: {text: '未开始', theme: 'blue'},
						4: {text: '活动中', theme: 'green'}
						/* 1: {text: '活动中', theme: 'green'},
						2: {text: '审批中', theme: 'gray'},
						3: {text: '未通过', theme: 'gray'},
						4: {text: '已结束', theme: 'gray'},
						5: {text: '未开始', theme: 'blue'},
						6: {text: '草稿', theme: 'gray'},
						7: {text: '未启动', theme: 'blue'} */
					};
					const {text, theme} = map[status];
					return <ArrowLabel theme={theme}>{text}</ArrowLabel>
				}
			},
			{
				title: '操作',
				align: 'center',
				width: 200,
				render: ({activity_id, organization_id}) => (
					<span>
						<a onClick={() => history.push({
							pathname: `/questionnaire-survey/${activity_id}?is_three=${_oid !== '3'}&activity_type=appraise&org_id=${organization_id}`,
						})}>互动情况</a>
					</span>
				)
			}
		];
		return (
			<div>
				<Table
					bordered
					loading={loading}
					columns={columns}
					dataSource={dataList.map((item, index) => ({...item, index}))}
					rowKey={({index}) => index}
					pagination={{
						size: 'small',
						showQuickJumper: true,
						pageSize: page_size,
						total,
						current: page_no,
						onChange: e => onChange && onChange(e),
						onShowSizeChange: e => onChange && onChange(e)
					}}
				/>
			</div>
		)
	}
}
