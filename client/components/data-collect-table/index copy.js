// 考核二期，数据采集表格
import React from 'react';
import './index.less';
import { Tabs, Input, Button } from 'antd';
import propTypes from 'prop-types';
import { fileDownload } from 'client/components/file-download';

const Search = Input.Search;
// import mockData from './mockData';

import Tab1 from './tab1';
import Tab2 from './tab2';
import Tab3 from './tab3';
import Tab4 from './tab4';
import Tab5 from './tab5';
import Tab6 from './tab6';
import Tab7 from './tab7';

const TabPane = Tabs.TabPane;

class Class extends React.Component {
  constructor(props) {
    super(props);
    const {dataSource} = this.props;
    const list = (dataSource && dataSource.list) ? dataSource.list : [];
    this.state = {
      list,
      tableData: list[0],
      searchParam: ''
    };
  }
  onSearchData(value, type_id){
    const {list,tableData } = this.state;
    let data = null;
    for (const val of list) {
      if(val.type_id == type_id){
        if(value){
          val.list_detail = val.list_detail.filter(org=>{
            return org.org_name.includes(value)
          })
        }
        data = val
      }
    }
    this.setState({
      searchParam: value,
      tableData:{
        ...tableData,
        data
      }
    })
  }
  onDownloadData(searchParam,type_id){
    const {list,tableData } = this.state;
    const { evalId , orgId } = this.props;
    //search_name  type_id
  }
  render() {
    // const { eval_detail_list = [], eval_option_list = [], time = [] } = mockData;
    const { isFinish, dataSource = {}, evalId , orgId } = this.props;
    const { list, tableData,searchParam } = this.state;
    // console.log(dataSource);

    let { evalCycle } = this.props;
    // 默认统计
    if (!evalCycle) {
      evalCycle = 1;
    }

    // const list = (dataSource && dataSource.list) ? dataSource.list : [];
    const eval_year = dataSource && dataSource.eval_year;
    const months = (dataSource && dataSource.months) ? dataSource.months : [];
    const eval_cycle = dataSource && dataSource.eval_cycle;

    const commonProps = {
      eval_year,
      eval_cycle: eval_cycle || evalCycle,
      months,
      isFinish
    }
    // 根据OptionId确认渲染的tab组件
    const renderTab = (tab = {}, months = []) => {
      const { listDetail = [], type_id } = tableData;
      console.log("TCL: ",tableData)
      switch (type_id) {
        case 1:
          return <Tab1 {...commonProps} data={tableData} />;
        case 2:
          return <Tab2 {...commonProps} data={tableData} />;
        case 3:
          return <Tab3 {...commonProps} data={tableData} />;
        case 4:
          return <Tab4 {...commonProps} data={tableData} />;
        case 5:
          return <Tab5 {...commonProps} data={tableData} />;
        case 6:
          return <Tab6 {...commonProps} data={tableData} />;
        case 7:
          return <Tab7 {...commonProps} data={tableData} />;
        default:
          return null;
      }
    }
    return (
      <div className="data-collect-table-container">
        <Tabs onChange={tab => this.onSearchData(null,tab)}>
          {
            list && list.map((tab, index) => {
              return (
                <TabPane tab={tab.type_name} key={tab.type_id}>
                  <div className="tab-search">
                    <Search
                      className="tab-search_el"
                      placeholder="请输入支部名称"
                      enterButton="搜索"
                      value={searchParam}
                      onChange={(e)=> this.setState({searchParam: e.target.value})}
                      onSearch={value => this.onSearchData(value,tab.type_id)}
                    />
                    <a
                      onClick={()=>{
                        fileDownload(`/eval/data-collect-deduction/view-detail-export?evalId=${evalId}&orgId=${orgId}&typeId=${tab.type_id}&searchName=${searchParam}`)
                      }}
                      href="javascript:void(0);"
                    >
                      <Button >导出</Button>
                    </a>
                  </div>
                  {renderTab(tab, months)}
                </TabPane>
              );
            })
          }
          {/* {
            body1 &&
            <TabPane tab="党员领导干部参加组织生活" key={1}>
              <Tab1 {...commonProps} data={body1} />
            </TabPane>
          }
          {
            body2 &&
            <TabPane tab="党员支部大会" key={2}>
              <Tab2 {...commonProps} data={body2} />
            </TabPane>
          }
          {
            body3 &&
            <TabPane tab="党支部委员会会议" key={3}>
              <Tab3 {...commonProps} data={body3} />
            </TabPane>
          }
          {
            body4 &&
            <TabPane tab="党小组会" key={4}>
              <Tab4 {...commonProps} data={body4} />
            </TabPane>
          }
          {
            body5 &&
            <TabPane tab="党课" key={5}>
              <Tab5 {...commonProps} data={body5} />
            </TabPane>
          }
          {
            body6 &&
            <TabPane tab="主题党日" key={6}>
              <Tab6 {...commonProps} data={body6} />
            </TabPane>
          }
          {
            body7 &&
            <TabPane tab="党费交纳" key={7}>
              <Tab7 {...commonProps} data={body7} />
            </TabPane>
          } */}
        </Tabs>
      </div>
    )
  }
}

Class.propTypes = {
  // 标记位，从外部传入，标记是否统计完成，两种状态之间，表格格式，请求接口不相同
  isFinish: propTypes.bool,
  dataSource: propTypes.object,
  evalCycle: propTypes.number,
  evalId: propTypes.number,
  orgId: propTypes.number
}

Class.defaultProps = {
  isFinish: true,
  dataSource: {},
  evalCycle: 1
}

export default Class;
