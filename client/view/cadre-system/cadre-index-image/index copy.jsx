import React, { useEffect, useState } from 'react'
import "./index.less"
import CadreIndex from './components/CadreIndex'
import Point from './components/Point'
import Evaluation from './components/Evaluation'
import html2canvas from 'html2canvas'
import { getInspectionIndexEvalCompare, getLeaderScreenPerformancePoint, getLeaderScreenPyramid } from 'client/apis/cadre-portrait';
import moment from 'moment'
import { toPng, toJpeg, toBlob, toPixelData, toSvg } from 'html-to-image';

// 添加工具函数
const base64ToBlob = (base64Data) => {
    // 移除base64头部信息
    const base64WithoutPrefix = base64Data.split(',')[1];

    // 将base64转换为二进制
    const binaryString = window.atob(base64WithoutPrefix);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);

    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    // 创建blob对象
    return new Blob([bytes], { type: 'image/png' });
};

function index({ user = {}, onImageGenerated, onError }) {
    const { user_id, name, org_id } = user || {}
    const [evaluationData, setEvaluationData] = useState(undefined)
    const [pointData, setPointData] = useState(undefined)
    const [pyramidData, setPyramidData] = useState(undefined)
    const loadDataEvaluation = async () => {
        if (!user_id) return;

        try {
            const { data: res } = await getInspectionIndexEvalCompare({ user_id });
            if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
                setEvaluationData(res.data);
            }
        } catch (error) {
            console.error('Failed to load data:', error);
            onError && onError({ name })
        }
    };
    const generateImage = async () => {
        try {
            const element = document.querySelector('.charts-box');
            if (!element) return null;

            // const canvas = await html2canvas(element, {
            //     logging: false,
            //     useCORS: true,
            // });
            const blob = await toBlob(element, {
                width: element.clientWidth,
                height: element.clientHeight,
                // canvasWidth: element.clientWidth * 3,
                // canvasHeight: element.clientHeight * 3,
                quality: 1,
                backgroundColor: '#fff',
                skip: false,
                // style: {
                //     transform: "scale(3)"
                // },
                pixelRatio: 2
            });
            // const imageData = canvas.toDataURL('image/png');
            // // 转换为blob
            // const blob = base64ToBlob(imageData);

            // 如果需要File对象，可以这样转换
            const file = new File([blob], `${moment().format("YYYY年MM月")}${name}同志干部履职评价指数反馈单.png`, { type: 'image/png' });

            onImageGenerated && onImageGenerated({ file, user_id, name, org_id });

            return file;
        } catch (error) {
            console.error('Generate image error:', error);
            onError && onError({ name })

        }

    };

    const loadDataPoint = async () => {
        if (!user_id) return;

        try {
            const { data: res } = await getLeaderScreenPerformancePoint({ user_id, flag: 2 });
            if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
                setPointData(res.data);
            }
        } catch (error) {
            console.error('Failed to load data:', error);
            onError && onError({ name })
        };
    }

    const loadDataPyramid = async () => {
        if (!user_id) return;

        try {
            const { data: res } = await getLeaderScreenPyramid({ user_id, flag: 2 });
            if (res.code === 0 && Object.keys(res.data || {}).length > 0) {
                setPyramidData(res.data);
            }
        } catch (error) {
            console.error('Failed to load data:', error);
            onError && onError({ name })
        }
    };
    const pageInit = () => ({ eva: false, point: false, pyramid: false })
    const [renderMap, setRenderMap] = useState(pageInit())

    const updateStatus = (key, value) => {
        setRenderMap(prev => ({ ...prev, [key]: value }))
    }

    useEffect(() => {
        if (renderMap.eva && renderMap.point && renderMap.pyramid) {
            generateImage();
        }
    }, [renderMap]);


    useEffect(() => {
        setRenderMap(pageInit())
        if (user_id) {
            loadDataEvaluation()
            loadDataPoint()
            loadDataPyramid()
        }
    }, [user_id])

    return (
        <div className='party-member-image'>
            <div className='charts-box' style={{
                // transform: "scale(3)"
            }}>
                <div className='feedback-form'>{moment().format("YYYY年MM月")}{name}同志干部履职评价指数反馈单</div>
                <div className='charts-container'>
                    <div className='charts-row'>
                        <div className='chart-box w-550'>
                            <CadreIndex data={pyramidData} updateStatus={updateStatus} />
                        </div>
                        <div className='chart-box w-550'>
                            <Point data={pointData} user_id={user_id} updateStatus={updateStatus} />
                        </div>
                    </div>
                    <div className='chart-box w-1500' style={{ width: "1200px", }}>
                        <Evaluation data={evaluationData} updateStatus={updateStatus} />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default index