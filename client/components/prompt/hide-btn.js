/**
 * 用于隐藏按钮,页面上主动触发方法
 * <AUTHOR>
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';


const HideBtn = (props) => {
  const { func } = props;
  return <div id="InvisibleSaveDraftBtn"
    onClick={func}
  ></ div>
};

// 参数校验 简单用法   官方文档 https://github.com/facebook/prop-types


HideBtn.propTypes = {
  func: PropTypes.func
}

HideBtn.defaultProps = {
  func: () => { }
}

export default HideBtn;