import { Prompt } from 'react-router-dom';
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import './index.less';
export default class componentName extends Component {
  constructor(props) {
    super(props)
    const { props1 } = props;
    const { history } = props1;
    const { location } = history;
    const { pathname, state: hsState } = location;
    this.state = {
      pathname,
      hsState,
      isDraft: false
    }
  }

  componentDidMount() {
    const { pathname, state } = window.location;
    if (pathname == '/questionnaire') {
      this.setState({
        isDraft: true
      })
    }
  }

  render() {
    const { isDraft } = this.state;
    return (
      <Prompt message="确定要离开？" when={isDraft} />
    )
  }
}


