import React, { Component } from "react";
import { Steps } from "antd";
import "./style.less";
import OrganizationCode from "./components/organization-code";
import RegisterUserInfo from "./components/register-user-info";
import SetupPassword from "./components/setup-password";
import OrganizationInfo from "./components/organization-info";
import { randomString } from "client/tool/util";

const Step = Steps.Step;

class ActiveOrganization extends Component {
  constructor(props) {
    super(props);

    this.state = {
      step: 0,
      uuid: randomString(), // 图片验证码uuid
      userId: "", // 用户id
      orgId: "", // 组织id
      parentOrgId: "", // 上级组织id

      organizationInfo: {}, // 组织信息
      passwordInfo: {}, // 密码
      registerUserInfo: {} // 注册人信息
    };
  }

  changeState(payload) {
    this.setState({ ...payload });
  }

  renderContent() {
    if (this.state.step === 0) {
      return (
        <OrganizationCode
          changeState={payload => this.changeState(payload)}
          state={this.state}
          history={this.props.history}
        />
      );
    } else if (this.state.step === 1) {
      return (
        <RegisterUserInfo
          changeState={payload => this.changeState(payload)}
          state={this.state}
        />
      );
    } else if (this.state.step === 2) {
      return (
        <SetupPassword
          changeState={payload => this.changeState(payload)}
          state={this.state}
        />
      );
    } else if (this.state.step === 3) {
      return (
        <OrganizationInfo
          changeState={payload => this.changeState(payload)}
          state={this.state}
        />
      );
    }
  }

  customDot(dot, { status, index }) {
    return (
      <span className={status === "wait" ? "step-waiting-item" : "active-item"}>
        {index + 1}
      </span>
    );
  }

  render() {
    return (
      <div className="active-wrapper">
        <Steps
          className="active-wrapper-step"
          current={this.state.step}
          progressDot={this.customDot}
        >
          <Step title="验证组织标识码" />
          <Step title="注册人信息" />
          <Step title="设置密码" />
          <Step title="确认组织信息" />
        </Steps>

        <div className="active-wrapper-content">{this.renderContent()}</div>
      </div>
    );
  }
}

export default ActiveOrganization;
