import React, { useEffect, useState } from "react";
import { Button, message, Modal, Input, Form } from "antd";
import { getContent, approveContent } from "apis/content-solicitation";
import SearchHeader from "components/search-header";
import ContentInfo from "../components/ContentInfo";
import ApproveRecord from "../components/ApproveRecord";
import InputTips from "components/activity-form/sub-components/InputTips";
import "./index.less";

const { TextArea } = Input;
const { Item } = Form;
const contentDetail = (props) => {
  const {
    history,
    location: { state },
    form: { getFieldValue, getFieldDecorator, getFieldsValue },
  } = props;

  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (!state.isApprove && !state.isEdit) {
      initData();
    } else {
      setDataSource(state.record);
    }
  }, [state]);

  const initData = () => {
    getContent({ content_personal_id: state.record.content_personal_id }).then(
      ({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        setDataSource(res.data);
      }
    );
  };

  const onHandleApprove = (isPass) => {
    const { comments } = getFieldsValue();
    const params = {
      comments,
      approve_type: state.approve_type,
      action: isPass,
      content_personal_id: state.record.content_personal_id,
    };
    Modal.confirm({
      title: isPass ? "确定审核通过？" : "确定审核不通过？",
      confirm: "",
      okText: "是",
      cancelText: "否",
      onOk() {
        approveContent(params).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error(res.message);
            return;
          }
          message.success("审核成功！");
          history.goBack();
        });
      },
    });
  };

  const contentProps = {
    ...state,
    dataSource,
    history,
  };

  return (
    <div className="content-detail">
      <SearchHeader
        title={
          state.isEdit ? "内容编辑" : state.isApprove ? "内容审核" : "内容详情"
        }
        onBack={() => history.goBack()}
      />
      <div className="wrap-content">
        {!state.isApprove && !state.isEdit && (
          <div className="second-header">基本信息</div>
        )}
        <ContentInfo {...contentProps} />
        {!state.isEdit && (
          <React.Fragment>
            {!state.isApprove ? (
              <React.Fragment>
                <div className="second-header">审核记录</div>
                <ApproveRecord dataSource={dataSource.workflow_log} />
                <div className="approve-btn">
                  <Button
                    type="default"
                    className="go-back-btn"
                    onClick={() => history.goBack()}
                  >
                    返回
                  </Button>
                </div>
              </React.Fragment>
            ) : (
              <React.Fragment>
                <Form>
                  <Item label="审核意见">
                    <InputTips max={500} text={getFieldValue("comments")}>
                      {getFieldDecorator("comments", {
                        initialValue: "",
                        rules: [
                          {
                            max: 500,
                            message: "输入不能超过500个字",
                          },
                        ],
                      })(
                        <TextArea
                          maxLength={500}
                          placeholder="请输入"
                          autoSize={{ minRows: 4, maxRows: 7 }}
                        />
                      )}
                    </InputTips>
                  </Item>
                </Form>
                <div className="btn">
                  <Button type="primary" onClick={() => onHandleApprove(1)}>
                    通过
                  </Button>
                  <Button type="default" onClick={() => onHandleApprove(0)}>
                    不通过
                  </Button>
                </div>
              </React.Fragment>
            )}
          </React.Fragment>
        )}
      </div>
    </div>
  );
};

export default Form.create()(contentDetail);
