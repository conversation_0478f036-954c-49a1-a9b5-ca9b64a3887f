/**
 * 
 *  考核系统
 * 
 */

const { http } = require('client/tool/axios');
import { taskHost, approvalHost, evalHost } from "./config";

// 任务填报/数据采集(两个分页查询公用一个接口)
export const getExecuteTask = data => {
  return http.post(`${taskHost}/execution/getExecuteTask`, data);
}
// 撤回修改接口
export const withdraw = data => {
  return http.post(`${taskHost}/execution/withdraw/${data}`);
}

//查看已填报的任务
export const getTask = data => {
  return http.get(`${taskHost}/execution/getTask/${data.id}`);
}
export const fillReport = data => {
  return http.post(`${taskHost}/execution/fillReport`, data);
}

//审批任务列表
export const taskList = data => {
  return http.get(`${approvalHost}/taskList`, data);
}
//任务详情
export const taskDetails = data => {
  return http.get(`${approvalHost}/taskDetails`, data);
}
export const taskOrNotList = data => {
  return http.get(`${approvalHost}/taskOrNotList`, data);
}
export const taskResult = data => {
  return http.post(`${approvalHost}/taskResult`, data);
}
//10500203-任务审核详情-未提交数量
export const getContNotCommitNumber = (queryparams = {}) => {
    return http.get(`${approvalHost}/cont-not-commit-number`, queryparams);
}
//10500203-任务审核详情-未提交
export const getContNotCommit = (queryparams = {}) => {
    return http.get(`${approvalHost}/cont-not-commit-orgs`, queryparams);
}

//查询执行组织执行流水
export const evalFlow = data => {
  return http.get(`${evalHost}/eval/flow/${data.eval_id}/${data.org_id}?is_commit=${data.is_commit}`);
}

//获取考核信息
export const getDataCollectDeduction = data => {
  return http.get(`${evalHost}/data-collect-deduction/view?eval_id=${data.eval_id}&org_id=${data.org_id}`);
}
export const getDataCollectDeductionDetail = data => {
  return http.get(`${evalHost}/data-collect-deduction/view-detail?eval_id=${data.eval_id}&org_id=${data.org_id}`,);
}
//10400100110006-数据采集(采集完成)
export const getEvalStatisList = (queryparams = {}) => {
    return http.post(`${evalHost}/eval-statis/list`, queryparams);
}