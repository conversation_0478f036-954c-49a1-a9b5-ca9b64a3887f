import React, { Component } from "react";
import { <PERSON><PERSON>, Button } from "antd";
import "./organize-modal.less";

import OrganizeList from "./organize-list";

import propTypes from "prop-types";

class OrganizeModal extends Component {
  constructor() {
    super();
    this.state = {};
    this.exportData = this.exportData.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.initData = this.initData.bind(this);
  }

  initData(dataSource) {
    // console.log(this);
    // console.log(this.organizeList);
    if (this.organizeList) {
      // 初始化数据
      this.organizeList.initData(dataSource);
    }
  }

  // 对外暴露数据接口
  exportData() {
    if (this.organizeList) {
      const { loadOrganizeData } = this.props;
      const organizeListData = this.organizeList.exportData();
      loadOrganizeData && loadOrganizeData(organizeListData);
    }
  }

  // 对外暴露移除一个组织的方法
  removeItem(index, org) {
    this.organizeList && this.organizeList.removeOrg(index, org);
    // this.organizeList.removeOrg(index, org);
  }

  render() {
    const { visible, hideModal, dataSource, radio, disabledOrgList, checkAll, disabledOrgType1, distinguishOwnerTree } = this.props;
    const { org_type, tree_type } = this.state;
    const modalProps = {
      footer: null,
      title: "通过分组选择组织",
      visible,
      wrapClassName: "group-organize-modal",
      width: 900,
      onCancel() {
        hideModal();
      }
    };



    const organizeListProps = {
      tree_type,
      disabledOrgList,
      dataSource,
      org_type,
      radio,
      checkAll,
      ref: ref => {
        this.organizeList = ref;
      }
    };
    return (
      <Modal {...modalProps}>
        <div className="organize-list-wrapper">
          <OrganizeList {...organizeListProps} />
        </div>
        <div className="buttons-wrapper">
          <Button type="primary" onClick={this.exportData}>
            确定
          </Button>
          <Button onClick={hideModal}>取消</Button>
        </div>
      </Modal>
    );
  }
}

OrganizeModal.propTypes = {
  visible: propTypes.bool,
  dataSource: propTypes.array,
  hideModal: propTypes.func,
  loadOrganizeData: propTypes.func,
  checkAll: propTypes.bool,
};

OrganizeModal.defaultProps = {
  dataSource: [],
  checkAll: true,
};

export default OrganizeModal;
