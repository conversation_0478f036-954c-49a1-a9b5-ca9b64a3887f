.document-manage {
  .document-manage-header-button {
    .ant-btn {
      height: 36px;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #666666;
    }

    .upload-btn {
      border: 1px solid #D5D5D5;
      margin-right: 24px;
    }

    .del-btn {
      border: none;
      background: #E2E2E2;
    }

    .active-del-btn {
      background: #F46E65;
      color: #ffffff;
    }
  }

  &-content {
    padding: 34px 31px;

    &-search {
      &-org {
        margin-bottom: 32px;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4A4A4A;

        .ant-btn-link {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #359AF7;
        }
      }

      &-form {
        .ant-form-item {
          margin-right: 24px;

          &-label {
            margin-bottom: 24px;

            label {
              font-size: 16px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #4A4A4A;
            }
          }
        }

        .ant-select-selection {
          min-width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-select-selection__rendered {
          line-height: 36px;
        }

        .ant-input {
          width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-btn-primary {
          height: 36px;
          background: #F46E65;
          border-radius: 4px;
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
        }
      }
    }

    &-table {
      margin-top: 33px;

      .ant-btn-link {
        color: #359AF7;
      }

      .ant-table-thead {
        th {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #3D3D3D;
          text-align: center;
          background: #F3F3F3;
        }
      }
      .document-manage-table-contactDetails{
         /*! autoprefixer: off */
         -webkit-box-orient: vertical;
         /* autoprefixer: on */
         display: -webkit-box;
         -webkit-line-clamp: 2;
         overflow: hidden;
         text-overflow: ellipsis; //文本溢出显示省略号
      }
      .document-manage-table-title {
        overflow: hidden;
        text-overflow: ellipsis; //文本溢出显示省略号
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
        /* autoprefixer: on */
        display: -webkit-box;
        -webkit-line-clamp: 2;
      }

      .ant-pagination {
        margin-top: 20px;
      }
    }
  }
}.tags{
  margin-right: 6px;
        padding: 4px 6px;
        background: rgba(217, 217, 217, 0.3);
        border-radius: 4px;
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
}