import { host } from './config';
import { http } from "client/tool/axios";

//VR场景管理
export const getVRData = (org_id) => {
    return http.post(`${host}/vr/add`, org_id);
  };
//VR场景每页展示
export const mien = (params) => {
  return http.get(`${host}/push-excellent/check/move`, params)
}
//VR场景更新
export const update = (params) => {
  return http.post(`${host}/vr/update`,params)
}
//根据组织查询单个VR场景
export const mienSingle = (params) => {
  return http.get(`${host}/vr/get-by-org/${params.org_id}`)
}
//VR场景删除(未完成)
export const delData = (params,{i}) => {
  return http.get(`${host}/vr/del/scenes_id=${i}`, params)
}
// 400020305060-查询单个VR场景 http://wiki.aidangqun.com/project/4?p=801
export const getVrScenesDetail = (params) => {
  return http.get(`${host}/vr/get/${params}`);
};
