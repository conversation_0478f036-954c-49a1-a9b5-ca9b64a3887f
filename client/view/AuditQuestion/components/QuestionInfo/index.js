import React, { useEffect, useState, useMemo } from "react";
import moment from "moment";
import { Row, Col, Form, Input, Select, Button, Icon, message } from "antd";
import InputTips from "components/activity-form/sub-components/InputTips";
import { saveMyQuestion } from "apis/audit-question";
import "./index.less";

const { Item } = Form;
const { TextArea } = Input;
const { Option } = Select;
const questionInfo = (props) => {
  const {
    history,
    dataSource,
    isEdit,
    form: { getFieldValue, getFieldDecorator, validateFields },
  } = props;

  const [optionData, setOptionData] = useState([]);
  const [answer, setAnswer] = useState("");

  useEffect(() => {
    const newOptions = dataSource.options && formatOptions(dataSource.options);
    const newAnswer = dataSource.answer && formatAnswer(newOptions);
    setOptionData(newOptions);
    setAnswer(newAnswer);
  }, [dataSource.options]);

  const getLetter = (val) => {
    let data = [];
    for (let i = 0; i < 26; i++) {
      data.push(String.fromCharCode(65 + i));
    }
    return data[val];
  };

  const onHandleSubmit = () => {
    validateFields((err, values) => {
      if (err) {
        return;
      }
      let answers = [];
      optionData.map((e) => {
        if (e.type === 1) {
          answers.push(e.value);
        }
      });
      const params = {
        ...values,
        action: 2,
        qp_id: dataSource.qp_id,
        options: JSON.stringify(
          optionData.map((e) => {
            return e.value;
          })
        ),
        answer: answers.join(","),
      };
      saveMyQuestion(params).then(({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        message.success("编辑成功！");
        history.goBack();
      });
    });
  };

  const formatOptions = (str) => {
    const options = JSON.parse(str);
    const answers = dataSource.answer.split(",");
    const newOptions = [];
    options.forEach((item, index) => {
      newOptions.push({
        value: item,
        type: answers.includes(item) ? 1 : 0,
        id: index,
      });
    });
    return newOptions;
  };

  const onAnswer = (item, index, type) => {
    // type 1删除 0勾选
    let _optionData;
    if (
      type === 0 &&
      (getFieldValue("type") === 1 || getFieldValue("type") === 3)
    ) {
      // 单选判断 和 判断题
      _optionData = optionData.map((x) => {
        if (item.id === x.id) {
          x.type = 1;
        } else {
          x.type = 0;
        }
        return x;
      });
    } else if (type === 0 && getFieldValue("type") === 2) {
      // 多选判断
      _optionData = optionData.map((x) => {
        if (item.id === x.id && item.type === 0) {
          x.type = 1;
        } else if (item.id === x.id && item.type === 1) {
          x.type = 0;
        }
        return x;
      });
    } else {
      // 删除
      _optionData = [...optionData];
      _optionData.splice(index, 1);
    }
    setOptionData(_optionData);
  };

  const onAddOption = (index) => {
    setOptionData([...optionData, { value: "", type: 0, id: index + 1 }]);
  };

  const formatAnswer = (data) => {
    const newAnswer = [];
    data.map((item, index) => {
      if (item.type === 1) {
        newAnswer.push(getLetter(index));
      }
    });
    return newAnswer.join("，");
  };

  const optionValue = (e, index) => {
    let optionDataCopy = optionData;
    optionDataCopy[index].value = e.target.value;
    setOptionData([...optionDataCopy]);
  };

  return (
    <Form className="question-info">
      <Row>
        <Col span={8}>
          <Item label="出题人">
            <span className="value">{dataSource.user_name}</span>
          </Item>
        </Col>
        <Col span={8}>
          <Item label="出题时间">
            <span className="value">
              {moment(dataSource.create_time).format("YYYY-MM-DD")}
            </span>
          </Item>
        </Col>
      </Row>
      <Row>
        <Item label="知识标题" required={isEdit}>
          {getFieldDecorator("knowledge_title", {
            initialValue: dataSource.knowledge_title
              ? dataSource.knowledge_title
              : "",
            rules: [
              {
                required: true,
                message: "知识标题不能为空",
              },
            ],
          })(
            <Input
              placeholder={isEdit ? "请输入" : "-"}
              maxLength={25}
              allowClear
              disabled={!isEdit}
            />
          )}
        </Item>
      </Row>
      <Row>
        <Item label="知识简述" required={isEdit}>
          {isEdit ? (
            <InputTips max={500} text={getFieldValue("knowledge")}>
              {getFieldDecorator("knowledge", {
                initialValue: dataSource.knowledge ? dataSource.knowledge : "",
                rules: [
                  { required: true, message: "知识简述不能为空" },
                  {
                    max: 500,
                    message: "输入不能超过500个字",
                  },
                ],
              })(
                <TextArea
                  maxLength={500}
                  autoSize={{ minRows: 3, maxRows: 8 }}
                  placeholder={"请输入"}
                  allowClear
                />
              )}
            </InputTips>
          ) : (
            <div className="value">{dataSource.knowledge}</div>
          )}
        </Item>
      </Row>
      <Row>
        <Item label="题目" required={isEdit}>
          {isEdit ? (
            <InputTips max={100} text={getFieldValue("knowledge")}>
              {getFieldDecorator("question", {
                initialValue: dataSource.question ? dataSource.question : "",
                rules: [
                  {
                    required: true,
                    message: "题目不能为空",
                  },
                  {
                    max: 100,
                    message: "输入不能超过100个字",
                  },
                ],
              })(
                <TextArea
                  maxLength={100}
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  placeholder={"请输入"}
                  allowClear
                />
              )}
            </InputTips>
          ) : (
            <div className="value">{dataSource.question}</div>
          )}
        </Item>
      </Row>
      <Row>
        <Item label="题型" required={isEdit}>
          {getFieldDecorator("type", {
            initialValue: dataSource.type ? dataSource.type : 1,
          })(
            <Select
              disabled={!isEdit}
              onChange={(val) => {
                if (val === 3) {
                  setOptionData(optionData.slice(0, 2));
                }
              }}
            >
              <Option value={1}>单选题</Option>
              <Option value={2}>多选题</Option>
              <Option value={3}>判断题</Option>
            </Select>
          )}
        </Item>
      </Row>
      <Row>
        <Item label="选项" required={isEdit}>
          {optionData &&
            optionData.length !== 0 &&
            optionData.map((item, index) => {
              return (
                <div className="options">
                  <div className="l-01">{getLetter(index)}</div>
                  <Input
                    maxLength={250}
                    className="l_02"
                    placeholder={isEdit ? "请输入" : "-"}
                    disabled={!isEdit}
                    value={item.value}
                    onChange={(e) => optionValue(e, index)}
                  />
                  {isEdit && (
                    <React.Fragment>
                      <Icon
                        type="check-circle"
                        className={item.type === 1 ? "l_03 on" : "l_03"}
                        onClick={() => onAnswer(item, index, 0)}
                      />
                      {getFieldValue("type") !== 3 ? (
                        <React.Fragment>
                          {optionData.length > 1 && (
                            <Icon
                              type="delete"
                              className="l_04"
                              onClick={() => onAnswer(item, index, 1)}
                            />
                          )}
                          {index === optionData.length - 1 &&
                            optionData.length < 10 && (
                              <Icon
                                type="plus-circle"
                                className="l_05"
                                onClick={() => onAddOption(index)}
                              />
                            )}
                        </React.Fragment>
                      ) : null}
                    </React.Fragment>
                  )}
                </div>
              );
            })}
        </Item>
      </Row>
      {!isEdit ? (
        <Row>
          <Item label="正确答案" required={isEdit}>
            <span className="value">{answer}</span>
          </Item>
        </Row>
      ) : (
        <Row className="btn">
          <Button type="primary" onClick={() => onHandleSubmit()}>
            确定
          </Button>
          <Button type="default" onClick={() => history.goBack()}>
            取消
          </Button>
        </Row>
      )}
    </Form>
  );
};
export default Form.create()(questionInfo);
