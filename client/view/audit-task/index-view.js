import React from 'react';
import moment from "moment";
import Header from 'components/search-header';
// import { Spin, Tree, TreeSelect, Form, Button, Row, Col, Icon } from 'antd';
import { Table } from 'antd';
import SearchHeader from './search-header';

export default function ({
     headerProps, isLoading = false, dataSource, dataPages,
     onOpen, onInitSearch, onSubmitSearch, onPageChange
    }) {
    const columns = [{
        title: '考核时间',
        dataIndex: 'eval_date',
        key: 'eval_date',
        align: 'center',
        width: 80,
        render: (text, record) => {
            const dateTypes = {
                    1:{
                            1:'第一季度',
                            2:'第二季度',
                            3:'第三季度',
                            4:'第四季度'
                    },
                    2:{
                            1:'第一月',
                            2:'第二月',
                            3:'第三月',
                            4:'第四月',
                            5:'第五月',
                            6:'第六月',
                            7:'第七月',
                            8:'第八月',
                            9:'第九月',
                            10:'第十月',
                            11:'第十一月',
                            12:'第十二月',
                    }
            }
            if ([1, 2].indexOf(record.eval_entity.eval_cycle) != -1) {
                    return dateTypes[record.eval_entity.eval_cycle][record.eval_entity.eval_date];
            } else {
                    return `${record.eval_entity.eval_year}年`
            }
        }
    }, {
        title: '任务名称',
        dataIndex: 'title',
        key: 'title',
        align: 'center',
        className: 'oneRow',
        render: (text, record) => {
            const title = record.eval_entity && record.eval_entity.title;
            if(title){
                return <span title={title}>{title}</span>
            }
            return null;
        }
    }, {
        title: '任务内容',
        dataIndex: 'content',
        key: 'content',
        align: 'center',
        className: 'oneRow',
        render: (text, record) => {
            const title = record.eval_entity && record.eval_entity.content;
            if(title){
                return <span title={title}>{title}</span>
            }
            return null;
        }
    }, {
        title: '填报截止时间',
        dataIndex: 'end_time',
        key: 'end_time',
        align: 'center',
        width: 190,
        render: (text) => {
            if(text){
                const time = new Date(text);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
            }
            return null;
        }
    }, {
        title: '审核截止时间',
        dataIndex: 'approval_time',
        key: 'approval_time',
        align: 'center',
        width: 190,
        render: (text) => {
            if(text){
                const time = new Date(text);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
            }
            return null;
        }
    }, {
        title: '考核组织的数量',
        dataIndex: 'total_org',
        key: 'total_org',
        align: 'center',
        width: 130
    }, {
        title: '任务状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        width: 90,
        render: (text, record) => {
            const eval_entity = record.eval_entity;
            switch(eval_entity.status){
                case 1:
                    const startTime = (new Date()).getTime();
                    const endTime = record.approval_time;

                    if(startTime >= endTime){
                        return (<span style={{color: '#999'}}>已结束</span>);
                    }
                    return (<span style={{color: '#8FC31F'}}>进行中</span>)
                case 2:
                    return (<span style={{color: '#999'}}>已结束</span>)
                case 3:
                    return (<span style={{color: '#B5B5B5'}}>已停用</span>)
                case 4:
                    return (<span style={{color: '#1790C9'}}>草稿</span>)
            }
        }
    }, {
        title: '任务进度',
        dataIndex: 'process',
        key: 'process',
        align: 'center',
        width: 145,
        render: (text, record) => {
            let submitted, approved;
            if(record.submitted){
                submitted = `${record.submitted}个组织已提交`;
            }
            if(record.approved){
                approved = `${record.approved}个组织已审核`;
            }
            return (
                <React.Fragment>
                    {submitted}{!!submitted && (<br />)}{approved}
                </React.Fragment>
            );
        }
    }, {
        title: '操作',
        dataIndex: 'oper',
        key: 'oper',
        align: 'center',
        width: 90,
        render: (text, record) => {
            return (
                <a onClick={() => {
                    onOpen(record);
                }}>详情</a>
            );
        }
    }];

    return (
        <div className="audit-task-main" >
            <Header {...headerProps} />
                    <div className="audit-task-content">
                        <SearchHeader
                            onInit={onInitSearch}
                            onSubmit={onSubmitSearch}
                        />
                        <Table
                            rowKey={(record) => {
                                return record.eval_entity.eval_id;
                            }}
                            style={{margin:'20px 0'}}
                            columns={columns}
                            dataSource={dataSource}
                            pagination={{
                                pageSize: dataPages.pageSize,
                                current: dataPages.pageNum,
                                total: dataPages.pageTotal,
                                onChange: (pageNum) => {
                                    onPageChange(pageNum);
                                },
                                showTotal: (total, range) => {
                                    return `数据总数：${total}条，当前显示${range.join('至')}条`;
                                }
                            }}
                            loading={isLoading}
                            bordered
                        />
            </div>
        </div>
    )

};