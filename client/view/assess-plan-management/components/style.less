.addAssessPlan {
  .body {
    width: 100%;
    padding: 32px;
    .buttonStyle {
      width: 120px;
      height: 40px;
    }
    .btnBg {
      background: #f46e65;
      border: none;
    }
  }
  .ant-col-2.ant-form-item-label {
    min-width: 100px;
  }
  .ant-row.ant-form-item.no-margin {
    .ant-form-item-control {
      margin-top: 0;
    }
  }

  .auto-send-type{
    margin-bottom: 30px;
    padding: 30px;
    background-color: #F1F5F8;

    .ant-form-item{
      margin-bottom: 14px;
    }
    .clearfloat{
      font-size: 1px;
      line-height: 1px;
      clear: both;
    }


    .ant-tag{
      border-radius: 0;
      height: 32px;
      line-height: 30px;
      overflow:hidden;
      white-space:nowrap;
      text-overflow:ellipsis;
      max-width: 170px;
      margin-right: 12px;
      margin-bottom: 4px;

      .anticon{
        font-size: 14px;
        color: #F4736A;
      }


      // &:hover{
      //   width:auto;
      //   max-width: inherit;
      // }
    }

  }
  .auto-send-type:after{
    content: " ";
    clear: both;
  }

  
}