import React, { Component } from 'react';
import { Spin } from 'antd'
import './index.less';
import PropTypes from 'prop-types';

const LoadingModal = ({ modalVisible, tip, noTip }) => {
    // console.log("=========================>",modalVisible);
    if (modalVisible) {
        return (
            <div className={'loading-modal'}>
                <span className={'spin-wrapper'}>
                   <Spin tip={noTip ? null : tip} />
                </span>
            </div>
        )
    }
    return null;
}

LoadingModal.propTypes = {
    //是否显示加载层控制标识符
    modalVisible: PropTypes.bool,
    //loading文字提示信息
    tip: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    noTip: PropTypes.bool
};
LoadingModal.defaultProps = {
    modalVisible: false,
    tip: <span>加载中.<br />请稍候...</span>,
    noTip: false
}

export default LoadingModal;

