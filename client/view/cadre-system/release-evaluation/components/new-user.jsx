import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Select, message } from "antd";
import { findByUser, initiateEvaluation } from "client/apis/cadre-portrait";
import SelectInput from "./select-input";

const ModalForm = ({ form }) => {
  const [option, setOption] = useState([]);

  const { getFieldDecorator, setFieldsValue } = form;

  const formItemLayout = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 20,
    },
  };
  /**
   * @description: 输入框联想
   * @param {*} userName
   * @return {*}
   */
  const findByUserInfo = (userName) => {
    findByUser({ user_name: userName }).then((res) => {
      if (res.data.code === 0) {
        setOption(res.data.data.slice(0, 10));
      } else {
        message.error(res.data.message);
      }
    });
  };
  const onSelect = ({ user_name, job, user_id }) => {
    setFieldsValue({
      job,
      user_id,
    });
  };
  return (
    <Form {...formItemLayout}>
      {getFieldDecorator("user_id", {})(<Input type="hidden" />)}

      <Form.Item label="姓名">
        {getFieldDecorator("user_name", {
          rules: [{ required: true, message: "请输入姓名" }],
        })(
          <SelectInput
            placeholder="请输入"
            onSearch={findByUserInfo}
            onSelect={onSelect}
            option={option}
          />
        )}
      </Form.Item>
      <Form.Item label="职务">
        {getFieldDecorator("job", {
          rules: [{ required: true, message: "请输入职务" }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="类别">
        {getFieldDecorator("user_type", {
          rules: [{ required: true, message: "请选择类别" }],
        })(
          <Select defaultValue="1">
            <Option value="1">县管正职</Option>
            <Option value="2">县管副职</Option>
            <Option value="3">改非干部</Option>
            <Option value="4">新提拔中层干部</Option>
          </Select>
        )}
      </Form.Item>
    </Form>
  );
};
export default Form.create()(ModalForm);
