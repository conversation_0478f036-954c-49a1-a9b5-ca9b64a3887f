import {message} from 'antd';
import {
    getOrgTree
} from 'apis/organize';

export default {
    // 模型里面的子类
    namespace: 'organizeContent',
    state: {
        dataSource: [],
        isLoading: false
    },
    // 定义业务交互层
    effects: {
        //加载组织树
        * loadOrgTree({ payload = {} }, {put, select, call}) {
            const { orgId } = payload;

            yield put({type: 'updateState', payload: {isLoading: true}});
            const result = (yield getOrgTree({
                org_id: orgId
            })).data;

            if(result.code != 0) {
                yield put({type: 'updateState', payload: {isLoading: false}});
                return message.error(result.message);
            }

            let params = {
                dataSource: result.data,
                isLoading: false
            };

            yield put({type: 'updateState', payload: params});
        },
    },
    reducers: {
        updateState(state, {payload}) {
            return {...state, ...payload};
        }
    }
}