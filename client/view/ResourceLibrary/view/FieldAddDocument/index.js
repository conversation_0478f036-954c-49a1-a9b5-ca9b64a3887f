import React, { useState, useEffect, useRef, useMemo } from "react";
import SearchHeader from "components/search-header";
import {
  Button,
  Form,
  Input,
  Radio,
  TreeSelect,
  Checkbox,
  Select,
  message,
  InputNumber,
  Icon
} from "antd";
import Editor from "client/components/RichTextEditor";
import InputTips from "components/activity-form/sub-components/InputTips";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import Image from "./components/image";
import { uploadBase64File } from "apis/file";
import { addResource, editResource, findById } from "apis/resource-library";
import PreviewModal from "./components/previewModal";
import "./index.less";
import ImageClip from "./components/ImageClip";
import { CDN } from "apis/config";
import moment from "moment";

/**
 * 上传资源
 * path: /Field-manage/Field-add
 */
const { Item } = Form;
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 10 },
};

const normFile = (e) => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};
const tagsList = [
  { label: "市内", value: "市内" },
  { label: "市外", value: "市外" },
];
const { name, oid, root_oid } = JSON.parse(
  sessionStorage.getItem("userInfo") || {}
);
const seq_number = [...Array(100)].map((_, index) => index + 1);
const methods = {
  1: addResource,
  2: editResource,
};
const FieldAddDocument = (props) => {
  const {
    history,
    form: {
      getFieldDecorator,
      getFieldValue,
      setFieldsValue,
      validateFieldsAndScroll,
    },
    location: {
      state: {
        type, // 1 新增 2编辑
        categoryList,
        resource_library_id,
      },
    },
  } = props;

  const editor = useRef(null);
  const [img, setImg] = useState([]);
  const [cover, setcover] = useState();
  const [isEdit, setisEdit] = useState([]);
  const [content, setContent] = useState(null);
  const [selOrg, setSelOrg] = useState(
    oid === root_oid
      ? []
      : [
        {
          org_name: name,
          org_id: oid,
        },
      ]);
  const [dataSource, setDataSource] = useState({});
  const [dataSourceTime, setDataSourceTime] = useState();
  const [catagoryId, setCategoryId] = useState(null);
  const [orgModalVisible, setOrgModalVisible] = useState(false);
  const [previewModalData, setPreviewModalData] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);

  useEffect(() => {
    if (resource_library_id) {
      findById({ resourceLibraryId: resource_library_id }).then(
        ({ data: res }) => {
          if (res.code !== 0) {
            message.error(res.message);
            return;
          }
          setCategoryId(res.data.resource_category_id);
          setSelOrg([
            {
              org_id: res.data.provider_org_id,
              org_name: res.data.provider_org_name,
            },
          ]);
          setContent(res.data.content);
          // editor.current.setEditorContent(res.data.content);
          setDataSource(res.data);
          setDataSourceTime(res.data.create_time)
          setImg(res.data.picture ? res.data.picture : '');
        }
      );
    }
  }, []);
  useEffect(() => {

  }, [img]);

  const onHandleSubmit = (isPreview = false) => {
    validateFieldsAndScroll((err, res) => {
      if (img.length == 0) {
        message.warn("请上传封面图片");
        return;
      }
      if (content === "<p><br></p>") {
        message.warn("请输入内容");
        return;
      }
      if (!err) {
        const { name, tags, sources, sort, seq, show_status, content, openTime, AppointmentTelephone, AppointmentTelephone2, address, fieldNub,create_time } = res;
        let tag = []
        tags ? tag = tags : tag = []
        let params = {
          open_time: openTime == "" ? null : openTime,//开发时间
          phone_number: AppointmentTelephone == '' ? null : AppointmentTelephone,//预约电话1
          phone_number_other: AppointmentTelephone2 == '' ? null : AppointmentTelephone2,//预约电话2
          contact_details: address,//场地地址
          venue_nums: fieldNub,//场地人数
          name,
          sources,
          show_status,
          content,//介绍
          resource_category_id: catagoryId,
          cover_url: img.length > 0 ? img[0].path : "",//封面图
          picture: img.length > 0 ? img : "",//包括封面图的全部图
          provider_org_id: selOrg[0].org_id,
          provider_org_name: selOrg[0].org_name,
          tags: tag.length > 0 ? tag.join(",") : null,
          resource_type: 4,
          create_time:dataSourceTime==undefined?moment().format("YYYY-MM-DD HH:MM"):dataSourceTime
        };

        if (sort === 2) {
          params.seq = seq;
        }
        if (resource_library_id) {
          params.resource_library_id = resource_library_id;
        }
        if (isPreview) {
          setPreviewModalData(params);
          setPreviewModalVisible(true);
        } else {
          methods[type](params).then(({ data: res }) => {
            if (res.code !== 0) {
              message.error(res.message);
              return;
            }
            message.success(`${type == 1 ? "新增" : "编辑"}成功`);
            history.goBack();
          });
        }
      }
    });
  };

  const checkSelect = (rule, value) => {
    return new Promise((resolve, reject) => {
      if (value && value.length > 3) {
        reject("标签最多选中3个");
      } else {
        resolve();
      }
    });
  };

  const clipEvent = () => {
    const upload = (base64) => {
      return new Promise((resolve, reject) => {
        uploadBase64File({
          upfile: base64,
          upType: "image",
        })
          .then(({ data: res }) => {
            if (res.code != 0) {
              message.error(res.message);
              reject("");
              return;
            } else {
              // setImg(res.data);
              resolve();
            }
          })
          .catch((e) => j(e));
      });
    };
    return { upload };
  };

  const previewModalProps = {
    previewModalData,
    visible: previewModalVisible,
    onCancel: () => setPreviewModalVisible(false),
  };

  const orgModalProps = {
    visible: orgModalVisible,
    dataSource: selOrg,
    hideModal: () => setOrgModalVisible(false),
    radio: true,
    hasRoot: false,
    onlyDirectSub: true,
    rootOrgId: oid,
    loadOrganizeData: (data) => {
      setOrgModalVisible(false);
      setSelOrg(data);
      setFieldsValue({ org_name: data[0].org_name });
    },
  };

  const PreviewModalMemo = useMemo(() => {
    return <PreviewModal {...previewModalProps} />;
  }, [previewModalProps]);


  let showCover = cover;
  let isShowPreviewImg = false;
  if (Object.prototype.toString.call(cover) === "[object Object]") {
    showCover = cover.path;
    isShowPreviewImg = true;
  }

  return (
    <div className="add-document">
      <SearchHeader title={type == 1 ? "上传资源" : "编辑资源"} onBack={() => history.goBack()} />
      <Form className="add-document-form" {...formItemLayout}>
        <Item label="场地分类" required>
          {getFieldDecorator("resource_category_id", {
            initialValue: catagoryId,
            rules: [
              {
                required: true,
                message: "请选择场地分类",
              },
            ],
          })(
            <TreeSelect
              style={{ width: "100%" }}
              dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
              treeData={categoryList}
              placeholder="请选择场地分类"
              treeDefaultExpandAll
              onChange={(val) => setCategoryId(val)}
            />
          )}
        </Item>
        {/* {console.log("数据回显", dataSource)} */}
        <Item label="场地名称" required>
          {getFieldDecorator("name", {
            initialValue: dataSource.name ? dataSource.name : "",
            rules: [
              {
                required: true,
                message: "请输入场地名称",
              },
            ],
          })(<Input allowClear placeholder="请输入场地名称" maxLength={50} />)}
        </Item>

        <Item label="场地地址" required>
          {getFieldDecorator("address", {
            initialValue: dataSource.contact_details ? dataSource.contact_details : "",
            rules: [
              {
                required: true,
                message: "请输入场地地址",
              },
            ],
          })(<Input allowClear placeholder="请输入场地地址" maxLength={50} />)}
        </Item>

        <Item
          name="upload"
          label="场地照片"
          // valuePropName="fileList"
          // getValueFromEvent={normFile}
          required
          wrapperCol={{ span: 18 }}
          className="preview-img"
        >
          <div className="preview-img-box">
            {img.map((item, index) => {
              return <div className="preview-img">
                <div className="img-wrap">
                  <div
                    className="img"
                    style={{
                      background: `url('${CDN}/${item.path}') no-repeat center/contain`,
                    }}
                  >
                  </div>
                  <div className="operation-btn">
                    <Icon type="delete" onClick={() => {
                      let imgs = img
                      imgs.splice(index, 1)
                      setImg([...imgs])
                    }} />
                  </div>
                </div>
              </div>
            })}
          </div>
          {img.length < 9 && <ImageClip
            title={"请上传图片"}
            onChange={(file) => {
              setImg([
                ...img,
                file
              ])
            }}
            size={{ w: 268, h: 156 }}
            src={{ url: "" }}
          />}
          <div className="add-document-form-imgTips">第一张照片为场地封面，请上传JPG、PNG格式图片，文件大小不超过10M</div>
        </Item>
        <Item label="预约电话">
          <span>
            <Item label="" className="phoneInput">
              {getFieldDecorator("AppointmentTelephone", {
                initialValue: dataSource.phone_number ? dataSource.phone_number : "",
                rules: [
                  {
                    pattern: /^((0\d{2,3}-?\d{7,8})|(1[3465789]\d{9}))$/,
                    message: "请输入正确的电话号码",
                  },
                ],
              })(
                <Input allowClear placeholder="请输入预约电话" maxLength={20} />
              )}
            </Item>
            <div className="phone-or">或</div>
            <Item label="" className="phoneInput">
              {getFieldDecorator("AppointmentTelephone2", {
                initialValue: dataSource.phone_number_other ? dataSource.phone_number_other : "",
                rules: [
                  {
                    pattern: /^((0\d{2,3}-?\d{7,8})|(1[3465789]\d{9}))$/,
                    message: "请输入正确的电话号码",
                  },
                ],
              })(
                <Input allowClear placeholder="请输入预约电话" maxLength={20} />
              )}
            </Item>
          </span>
        </Item>

        <Item label="开放时间">
          <InputTips max={60} text={getFieldValue("openTime")}>
            {getFieldDecorator("openTime", {
              initialValue: dataSource.open_time ? dataSource.open_time : "",
              rules: [
                {
                  max: 60,
                  message: "摘要最长60个字",
                },
              ],
            })(<Input.TextArea placeholder="请输入开放时间" rows={4} />)}
          </InputTips>
        </Item>

        <Item label="场地容纳人数" >
          {getFieldDecorator("fieldNub", {
            initialValue: dataSource.venue_nums ? dataSource.venue_nums : "",
          })(<InputNumber min={0} max={100000} />)}
        </Item>

        <Item label="信息提供" required className="select-org">
          {getFieldDecorator("org_name", {
            initialValue: selOrg[0] ? selOrg[0].org_name : "",
            rules: [
              {
                required: true,
                message: "请选择信息提供组织",
              },
            ],
          })(
            <Input
              disabled
              placeholder="这里是组织名称不可编辑，选择组织后自动展示"
            />
          )}
          <Button
            type="link"
            onClick={() => setOrgModalVisible(true)}
            disabled={oid !== root_oid}
          >
            选择组织
          </Button>
        </Item>

        <Item label="提供服务">
          {getFieldDecorator("tags", {
            initialValue: dataSource.tags ? dataSource.tags.split(",") : null,
            rules: [
              {
                validator: checkSelect,
              },
            ],
          })(<Checkbox.Group style={{ lineHeight: '40px' }} options={tagsList} />)}
        </Item>

        <Item label="排序" className="sort-line">
          {getFieldDecorator("sort", {
            initialValue: dataSource.seq ? 2 : 1,
          })(
            <Radio.Group style={{ lineHeight: '40px' }}>
              <Radio value={1}>默认排序</Radio>
              <Radio value={2}>TOP100</Radio>
            </Radio.Group>
          )}
        </Item>

        {getFieldValue("sort") === 2 && (
          <Item
            label=" "
            colon={false}
            wrapperCol={{ span: 6 }}
            className="seq-line"
          >
            {getFieldDecorator("seq", {
              initialValue: dataSource.seq ? dataSource.seq : 1,
            })(
              <Select showSearch>
                {seq_number.map((item) => {
                  return (
                    <Select.Option value={item} key={item}>
                      {item}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
          </Item>
        )}

        <Item label=" " colon={false} className="annotation">
          排序规则：默认排序按照上传时间排序，Top100按照排序值正序排序，数字范围1-100
        </Item>

        <Item label="介绍">
          <InputTips max={150} text={getFieldValue("content")}>
            {getFieldDecorator("content", {
              initialValue: dataSource.content ? dataSource.content : "",
              rules: [
                {
                  max: 150,
                  message: "介绍最长150个字",
                },
              ],
            })(<Input.TextArea placeholder="请输入介绍" rows={4} />)}
          </InputTips>
        </Item>

        {/* <Item label="内容" required>
          <div>
            {getFieldDecorator("content", {
              initialValue: content,
              rules: [
                {
                  required: true,
                  message: "请输入内容",
                },
              ],
            })(<Input style={{ display: "none" }} />)}
            <Editor
              height={301}
              onRef={editor}
              onChange={(html) => {
                setContent(html);
                setFieldsValue({ content: html });
              }}
            />
          </div>
        </Item> */}

        <Item label=" " colon={false} className="btn">
          <Button type="primary" onClick={() => onHandleSubmit()}>
            发布
          </Button>
          <Button type="default" onClick={() => history.goBack()}>
            返回
          </Button>
          <Button type="default" onClick={() => onHandleSubmit(true)}>
            预览
          </Button>
        </Item>
      </Form>
      <OrganizeModal {...orgModalProps} />
      {PreviewModalMemo}
    </div>
  );
};

export default Form.create()(FieldAddDocument);
