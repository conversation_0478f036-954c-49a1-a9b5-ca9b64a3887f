import React, { useState, useEffect } from "react";
import <PERSON>Header from "components/search-header";
import { Input, Icon, message, Carousel, Descriptions } from "antd";
import { findById, collectionEvent } from "apis/resource-library";
import "./index.less";
import moment from "moment";
import { CDN } from "apis/config";

const {
    user_id,
    user_name,
    oid: org_id,
    name: org_name,
    regionId: region_id,
} = JSON.parse(sessionStorage.getItem("UserInfo") || "{}");

const Particulars = (props) => {
    const {
        history,
        // location: {
        //     state: { resource_library_id, contact_details, keyWord },
        // },
    } = props;
    const { previewModalData, table = 0 } = props;
    const [dataSource, setDataSource] = useState({});
    const [data, setData] = useState();
    const [imgList, setImgList] = useState("");
    const [content, setContent] = useState("");
    const [tags, setTags] = useState([]);

    useEffect(() => {

        console.log(props);
        if (!table) {
            initDataSource();
            // 埋点
            collectionEvent({
                user_id,
                user_name,
                org_id,
                org_name,
                region_id,
                type: "L",
                object: props.location.state.resource_library_id,
                channal: "ResourceManager",
                transfer_time: new Date().getTime(),
                amount: 1,
            });
        }
        // if (keyWord) {
        //     setkeyword(keyWord);
        // }
    }, []);
    useEffect(() => {

    }, [dataSource]);
    const initDataSource = () => {
        findById({
            resourceLibraryId: props.location.state.resource_library_id,
        }).then(({ data: res }) => {
            if (res.code !== 0) {
                message.error(res.message);
                return;
            }
            setDataSource(res.data);
            setData(res.data)
            setContent(res.data.content);
            setTags(res.data.tags.split(","));
            console.log("res.data", res.data);
            const imgs =
                res.data.resource_files &&
                res.data.resource_files.map((item) => {
                    if (/\.(jpg|jpeg|png|JPG|PNG|JPEG)$/i.test(item.path)) {
                        return item;
                    }
                });
            setImgList(imgs);
        });
    };

    let openTime = (table ? previewModalData : dataSource).open_time

    if ((table ? previewModalData : dataSource).open_time !== undefined && (table ? previewModalData : dataSource).open_time !== false && (table ? previewModalData : dataSource).open_time !== null) {
        openTime = (table ? previewModalData : dataSource).open_time.replace(/\n/g, '<br>');
    }


    return (
        <div className="Particulars">
            {!table && (
                <SearchHeader title={"资源详情"} onBack={() => history.goBack()} />
            )}
            <div className="Particulars-information">
                {dataSource && dataSource.picture && dataSource.picture.length > 0 && (
                    <Carousel className="Particulars-information-Carousel" dots dotPosition="bottom">
                        {dataSource.picture.map((item, index) => {
                            return <img className="Particulars-information-Carousel-img" src={`${CDN}/${item.path}`} />
                        })}
                        {/* {(table ? previewModalData : dataSource).picture.forEach(item => {
                <img src={`${CDN}/${item.path}`}></img>
              })} */}
                    </Carousel>
                )}
                {/* <img className="Particulars-information-img" src={(table ? previewModalData : dataSource).cover_url} /> */}
                <div className="Particulars-information-content">
                    <div className="name">
                        {(table ? previewModalData : dataSource).name}
                    </div>
                    <div className="label">
                        {(table ? previewModalData.tags.split(",") : tags).map((item) => {
                            return <div className="tag">{item} </div>;
                        })}
                    </div>
                    <div className="technical-post">
                        <div>
                            开放时间：
                        </div>
                        {openTime ? <div dangerouslySetInnerHTML={{
                            __html: openTime,
                        }} /> : "-"}

                    </div>
                    <div className="technical-post">
                        <div>
                            预约电话：
                            {(table ? previewModalData : dataSource).phone_number!==undefined&&(table ? previewModalData : dataSource).phone_number_other!==undefined
                                ? `${(table ? previewModalData : dataSource).phone_number}、${(table ? previewModalData : dataSource).phone_number_other}`
                                : (table ? previewModalData : dataSource).phone_number
                                ?(table ? previewModalData : dataSource).phone_number
                                :(table ? previewModalData : dataSource).phone_number_other||(table ? previewModalData : dataSource).phone_number==undefined
                                ?(table ? previewModalData : dataSource).phone_number_other
                                :"-"}
                        </div>
                        {/* <div>
                            {(table ? previewModalData : dataSource).phone_number_other||(table ? previewModalData : dataSource).phone_number==undefined
                                ? `${(table ? previewModalData : dataSource).phone_number_other}`
                                : ""}
                        </div> */}
                    </div>
                    <div className="technical-post post2">
                        <div>
                            场地地址：
                        </div>
                        <div style={{ display: "inline-block", width: "80%" }}>{(table ? previewModalData : dataSource).contact_details}</div>
                    </div>
                    <div className="technical-post">
                        容纳人数：{(table ? previewModalData : dataSource).venue_nums == 0 ? "-" : `${(table ? previewModalData : dataSource).venue_nums}人`}
                    </div>
                    <div className="technical-post">
                        上传时间：{(table ? previewModalData : dataSource).create_time}
                    </div>
                </div>
            </div>
            <div
                className="Particulars-content"
            // style={{ marginLeft: table === 1 && 0 }}
            >
                {table ? previewModalData.content : content}
                <div className="lastMsg">
                    信息提供：{(table ? previewModalData : dataSource).provider_org_name}
                </div>
            </div>
        </div>
    );
};
export default Particulars;
