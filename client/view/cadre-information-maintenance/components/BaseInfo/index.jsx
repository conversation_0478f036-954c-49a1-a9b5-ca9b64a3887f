import { Col, DatePicker, Form, Input, Radio, Row, Select, Upload } from "antd";
import {
  queryByCode,
  queryJobInfo,
  uploadAvatar,
} from "client/apis/cadre-portrait";
import { CDN as cdn } from "client/apis/config";
import DateSingle from "components/date-single/DateSingle";
import { Fragment, useEffect, useRef, useState } from "react";
import avatarPng from "../../images/avatar.png";
import CustomCascader from "./Cascader.jsx";
import "./index.less";
const FormItem = Form.Item;
const { Option } = Select;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;
function index(props) {
  const { form, data, org_id, disabled } = props;

  const area = useRef();

  const { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue } =
    form;
  const [positionList, setPositionList] = useState([]);
  const [date, setDate] = useState(""); //日期默认不选
  const [codeMap, setCodeMap] = useState({
    cadreTypeOption: [],
    ethnicOption: [],
    politicalTypeOption: [],
    currentRankOption: [],
    currentLevelOption: [],
    sourceOption: [],
    identityOption: [],
    curSpecialtyLevelOption: [],
    cadreLevelOption: [],
    userTypeLevelOption: [],
  });

  const [areaTree, setAreaTree] = useState([]);

  const wrapperCol = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
  };

  useEffect(() => {
    if (!data) return;

    const {
      birthday,
      current_job_time,
      current_rank_time,
      joining_time,
      work_time,
      native,
      birthplace,
    } = data;
    form.setFieldsValue({
      ...data,

      // native: native
      //   ? native.split("-").map((item) => Number(item))
      //   : undefined,
      // birthplace: birthplace
      //   ? birthplace.split("-").map((item) => Number(item))
      //   : undefined,
      current_job_text: data.current_job,
    });
    setTimeout(() => {
      // 获取form中的值，如果form中有就不需要setFieldsValue, 如果没有，data中又有则需要重新赋值
      const formdata = form.getFieldsValue();
      const keys = Object.keys(formdata);
      const _fields = {};
      for (const key of keys) {
        if (!formdata[key] && data[key]) {
          _fields[key] = data[key];
        }
        if (key === "current_job_text") {
          _fields["current_job_text"] = data["current_job"];
        }
        // 处理多个任现职务时间
        if (data["current_job_time"].length !== 0) {
          data["current_job_time"].forEach((item, index) => {
            const key = Object.keys(item)[0];
            const value = item[key]; // 获取对应的值
            if (key === `current_job_time${index + 1}`) {
              _fields[`current_job_time${index + 1}`] = value;
            }
          });
        }
      }
      form.setFieldsValue(_fields);
    });
    //当data有值的时候，需要初始化
    if (data.user_id) {
      initAreaCode({
        code: 96300,
      });
    }
    console.log(data, form.getFieldsValue());
  }, [data]);

  useEffect(() => {
    initCode(2001, "cadreTypeOption");
    initCode(1004, "ethnicOption");
    initCode(1013, "politicalTypeOption");
    initCode(2003, "currentRankOption");
    initCode(96300, "currentLevelOption");
    initCode(96140, "sourceOption");
    initCode(2002, "identityOption");
    initCode(95300, "curSpecialtyLevelOption");
    initCode(96300, "cadreLevelOption");
    initCode(97100, "userTypeLevelOption");
    initPosition();
    if (!data.user_id) {
      //级联字典表
      initAreaCode({
        code: 96300,
      });
    }
  }, []);
  const initPosition = () => {
    queryJobInfo({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setPositionList(res.data.data);
      }
    });
  };
  const initAreaKey = async (native) => {
    await initAreaCode({
      type: 1,
    });
    if (!native) return;

    const nativeList = native.split("-");

    nativeList.forEach((key, index) => {
      loadData([
        {
          adcode: Number(key),
          type: index + 1,
        },
      ]);
    });
  };
  const initCode = async (code, key) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      setCodeMap((codeMap) => {
        codeMap[key] = res.data.data;
        return { ...codeMap };
      });
    }
  };

  //
  const initAreaCode = async ({ code }) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      const dataList = res.data.data.map((item) => ({
        ...item,
        isLeaf: !item.has_child,
        // children: item.has_child ? [] : undefined,
      }));
      setAreaTree(dataList);
      area.current = dataList;
      //如果是级联字典表 且回填有数据的话，需要加载子节点
      if (
        data &&
        Array.isArray(data.cadre_rank) &&
        data.cadre_rank.length > 1
      ) {
        // 假设这是你原来遍历调用 loadData 的地方，现在使用 for...of 改写
        traverseAndLoadData(data.cadre_rank);
        // data.cadre_rank.forEach((item, index) => {
        //   loadData(data.cadre_rank, true, index);
        // });
      }
    }
  };
  const traverseAndLoadData = async (optionsArray) => {
    for (const [index, options] of optionsArray.entries()) {
      await loadData(optionsArray, true, index);
    }
  };
  // 动态加载子节点
  const loadData = async (selectedOptions, echo = false, index) => {
    //最后一级不需要加载子节点
    if (echo && selectedOptions.length - 1 == index) {
      return;
    }
    const selectOption = selectedOptions[selectedOptions.length - 1];
    //如果是回填数据只会给到key,所以需要根据key去查询
    const res = await queryByCode({
      code: echo ? selectedOptions[index] : selectOption.op_key,
    });
    if (res.data.code === 0) {
      // 根据adcode找寻下级
      if (!res.data.data.length) {
        delete selectOption.isLeaf;
        setAreaTree([...areaTree]);
        return;
      }
      const findByCode = (areaTree, children) => {
        areaTree.forEach((item) => {
          if (item.children) {
            findByCode(item.children, children);
          } else {
            if (
              item.op_key ===
              (echo ? selectedOptions[index] : selectOption.op_key)
            ) {
              item.children = children.map((item) => {
                return { ...item, isLeaf: item.has_child ? false : true };
              });
            }
          }
        });
      };
      // console.log(area.current, "areaTree");

      findByCode(area.current, res.data.data);
      setAreaTree([...area.current]);
    }
  };
  const onUpload = async (file) => {
    const formdata = new FormData();
    formdata.append("avatar", file);
    formdata.append("head_url", data ? data.head_url : undefined);
    const res = await uploadAvatar(formdata);
    if (res.data.code === 0) {
      const data = res.data.data;
      setFieldsValue({
        head_url: data,
      });
    }

    return false;
  };

  const fieldNames = {
    label: "op_value",
    value: "op_key",
    children: "children",
  };

  const getAvatar = (head_url) => {
    return head_url ? `${cdn}/${head_url}` : avatarPng;
  };
  const children = positionList.map((item) => {
    return (
      <Option
        onClick={() => {
          const params = getFieldsValue();
          console.log(params);
          setFieldsValue({
            has_divided: item.has_divided,
            current_job_text: item.job_name,
            job_id: item.job_id,
          });
        }}
        value={String(item.job_id)}
      >
        {item.job_name}
      </Option>
    );
  });
  const cadreType = getFieldValue("category");
  const cadreTypeOption = codeMap.cadreTypeOption.find(
    (item) => item.op_key == cadreType
  );

  let IS_MIDDLE_CADRE = false;
  if (cadreTypeOption) {
    // has_child  1正职 2副职 3中层
    const { has_child } = cadreTypeOption;
    IS_MIDDLE_CADRE = has_child == 3;
  }
  // console.log(areaTree, "areaTree");

  return (
    <div className="base-info-box">
      <Row span={24}>
        {getFieldDecorator("user_id", {})(<Input type="hidden" />)}
        {getFieldDecorator("has_divided", {
          initialValue: IS_MIDDLE_CADRE
            ? 3
            : (data && data.has_divided) || undefined,
        })(<Input type="hidden" />)}
        {getFieldDecorator("head_url")(<Input type="hidden" />)}
        <Col span={18}>
          <Row span={24}>
            <Col span={12}>
              <FormItem label="姓名" {...wrapperCol} required colon>
                {getFieldDecorator("name", {
                  rules: [{ required: true, message: "请输入姓名" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="性别" {...wrapperCol} colon>
                {getFieldDecorator("gender", {
                  rules: [{ required: false, message: "请选择性别" }],
                })(
                  <Radio.Group placeholder="请输入" disabled={disabled}>
                    <Radio value={"1"}>男</Radio>
                    <Radio value={"2"}>女</Radio>
                  </Radio.Group>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="干部类别" {...wrapperCol} required colon>
                {getFieldDecorator("category", {
                  rules: [{ required: true, message: "请选择干部类别" }],
                })(
                  <Select
                    style={{ width: 120 }}
                    placeholder="请输入"
                    disabled={disabled}
                  >
                    {codeMap.cadreTypeOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="出生年月" {...wrapperCol} required colon>
                {getFieldDecorator("birthday", {
                  rules: [{ required: true, message: "请选择出生年月" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    allowClear={false}
                    disabled={disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={12} className="position-relative">
              <FormItem label="籍贯" {...wrapperCol} colon>
                {getFieldDecorator("native_text", {
                  rules: [{ required: false, message: "请选择籍贯" }],
                })(
                  <Input placeholder="请输入" disabled={disabled} />
                  // <CustomCascader
                  //   disabled={disabled}
                  //   placeholder="请输入"
                  //   fieldNames={fieldNames}
                  //   areaTree={areaTree}
                  //   loadData={(option) => {
                  //     loadData(option, 1);
                  //   }}
                  //   changeOnSelect
                  //   subText={data && data.native_text}
                  // />
                )}
              </FormItem>
            </Col>
            <Col span={12} className="position-relative">
              <FormItem label="出生地" {...wrapperCol} colon>
                {getFieldDecorator("birthplace_text", {
                  rules: [{ required: false, message: "请选择出生地" }],
                })(
                  // <CustomCascader
                  //   disabled={disabled}
                  //   placeholder="请输入"
                  //   fieldNames={fieldNames}
                  //   areaTree={areaTree}
                  //   subText={data && data.birthplace_text}
                  //   loadData={(option) => {
                  //     loadData(option, 2);
                  //   }}
                  //   changeOnSelect
                  // />
                  <Input placeholder="请输入" disabled={disabled} />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="民族" {...wrapperCol} colon>
                {getFieldDecorator("ethnic", {
                  rules: [{ required: false, message: "请选择民族" }],
                })(
                  <Select
                    style={{ width: 120 }}
                    placeholder="请输入"
                    disabled={disabled}
                  >
                    {codeMap.ethnicOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="政治面貌" {...wrapperCol} colon>
                {getFieldDecorator("political_type", {
                  rules: [{ required: false, message: "请选择政治面貌" }],
                })(
                  <Select
                    disabled={disabled}
                    style={{ width: 120 }}
                    placeholder="请选择"
                  >
                    {codeMap.politicalTypeOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="入党时间" {...wrapperCol} colon>
                {getFieldDecorator("joining_time", {
                  rules: [{ required: false, message: "请选择入党时间" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    disabled={disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="参加工作时间" {...wrapperCol} colon>
                {getFieldDecorator("work_time", {
                  rules: [{ required: false, message: "请选择参加工作时间" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    disabled={disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="熟悉专业和特长" {...wrapperCol} colon>
                {getFieldDecorator("profession_specialty", {
                  rules: [{ required: false, message: "请输入熟悉专业和特长" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="技能证书和称号" {...wrapperCol} colon>
                {getFieldDecorator("skill_info", {
                  rules: [{ required: false, message: "请输入技能证书和称号" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="专业技术职务" {...wrapperCol} colon>
                {getFieldDecorator("technical_position", {
                  rules: [{ required: false, message: "请输入专业技术职务" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="兴趣爱好" {...wrapperCol} colon>
                {getFieldDecorator("hobby", {
                  rules: [{ required: false, message: "请输入兴趣爱好" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
          </Row>
          <Row span={24}>
            <Col span={IS_MIDDLE_CADRE ? 12 : 24}>
              <FormItem
                label="现任职务"
                {...wrapperCol}
                labelCol={{
                  span: IS_MIDDLE_CADRE ? wrapperCol.labelCol.span : 4,
                }}
                wrapperCol={{
                  span: IS_MIDDLE_CADRE ? wrapperCol.wrapperCol.span : 20,
                }}
                colon
              >
                {getFieldDecorator("job_id")(<Input type="hidden" />)}
                {IS_MIDDLE_CADRE ? (
                  <Col span={24}>
                    {getFieldDecorator("current_job_text")(
                      <Input placeholder="请输入" disabled={disabled} />
                    )}
                  </Col>
                ) : (
                  <Fragment>
                    <Col span={6}>
                      <Form.Item>
                        {getFieldDecorator("current_job", {
                          rules: [
                            { required: false, message: "请选择现任职务" },
                          ],
                        })(
                          <Select
                            style={{ width: "100%" }}
                            placeholder="请选择"
                            disabled={disabled}
                            allowClear
                          >
                            {children}
                          </Select>
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={15} offset={1}>
                      {getFieldDecorator(
                        "current_job_text",
                        {}
                      )(<Input placeholder="请输入" disabled={disabled} />)}
                      <div>注：此处填写信息用于领导职务显示</div>
                    </Col>
                  </Fragment>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="任现职务时间" {...wrapperCol} colon>
                {/* 循环 data.current_job_time 的内容 */}
                {data &&
                  data.current_job_time.map((item, index) => (
                    <Fragment key={index}>
                      {getFieldDecorator(`current_job_time${index + 1}`, {
                        rules: [
                          { required: false, message: "请选择任现职务时间" },
                        ],
                      })(
                        <DateSingle
                          placeholder="请选择"
                          type="month"
                          disabled={disabled}
                        />
                      )}
                      <div className="box_gap"></div>
                    </Fragment>
                  ))}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="干部身份" {...wrapperCol} colon>
                {getFieldDecorator("identity", {
                  rules: [{ required: false, message: "请选择" }],
                })(
                  <Select placeholder="请输入" disabled={disabled} allowClear>
                    {codeMap.identityOption.map((item) => (
                      <Select.Option value={item.op_key}>
                        {item.op_value}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            {/* <Col span={12}>
              {
                IS_MIDDLE_CADRE ? <FormItem label={"现级别"} {...wrapperCol} colon>
                  {getFieldDecorator("current_rank", {
                    rules: [{ required: false, message: "请选择干部级别" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {codeMap.currentRankOption.map((item) => (
                        <Option value={item.op_key}>{item.op_value}</Option>
                      ))}
                    </Select>
                  )}
                </FormItem> : <FormItem label={"干部级别"} {...wrapperCol} colon>
                  {getFieldDecorator("current_rank", {
                    rules: [{ required: false, message: "请选择干部级别" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {codeMap.currentRankOption.map((item) => (
                        <Option value={item.op_key}>{item.op_value}</Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              }
            </Col>
            <Col span={12}>
              <FormItem label="现级别任职时间" {...wrapperCol} colon>
                {getFieldDecorator("current_rank_time", {
                  rules: [{ required: false, message: "请选择现级别任职时间" }],
                })(<DateSingle placeholder="请选择" type="month"  disabled={disabled} />)}
              </FormItem>
            </Col> */}
            <Col span={12}>
              <FormItem label="干部职务层次" {...wrapperCol} colon>
                {getFieldDecorator("current_rank", {
                  rules: [{ required: false, message: "请选择干部职务层次" }],
                })(
                  <Select placeholder="请选择" disabled={disabled} allowClear>
                    {codeMap.currentRankOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="现职务层次时间" {...wrapperCol} colon>
                {getFieldDecorator("current_rank_time", {
                  rules: [{ required: false, message: "请选择现职务层次时间" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    disabled={disabled}
                  />
                )}
              </FormItem>
            </Col>

            {IS_MIDDLE_CADRE && (
              <Col span={12}>
                <FormItem label="任中层干部时间" {...wrapperCol} colon>
                  {getFieldDecorator("mid_cadre_time", {
                    rules: [
                      { required: false, message: "请选择任中层干部时间" },
                    ],
                  })(
                    <DateSingle
                      placeholder="请选择"
                      type="month"
                      disabled={disabled}
                    />
                  )}
                </FormItem>
              </Col>
            )}

            {/* <Col span={12}>
              <FormItem label="干部职务层次" {...wrapperCol} colon>
                {getFieldDecorator("cadre_rank", {
                  rules: [{ required: false, message: "请选择干部职务层次" }],
                })(
                  <Select placeholder="请选择" disabled={disabled} allowClear>
                    {codeMap.currentLevelOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>

                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="现职务层次时间" {...wrapperCol} colon>
                {getFieldDecorator("cadre_rank_time", {
                  rules: [{ required: false, message: "请选择现职务层次时间" }],
                })(<DateSingle placeholder="请选择" type="month"  disabled={disabled} />)}
              </FormItem>
            </Col> */}
            {/* 2.27新增字段cadre_rank , 干部职级从下拉框变成多层级选择}
            {/* <Col span={12}>
              <FormItem label="干部职级" {...wrapperCol} colon>
                {getFieldDecorator("cadre_rank", {
                  rules: [{ required: false, message: "请选择干部职级" }],
                })(
                  <Select placeholder="请选择" disabled={disabled} allowClear>
                    {codeMap.currentLevelOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col> */}
            <Col span={12}>
              <FormItem label="干部职级" {...wrapperCol} colon>
                {getFieldDecorator("cadre_rank", {
                  initialValue: data.cadre_rank,
                })(
                  <CustomCascader
                    disabled={disabled}
                    placeholder="请输入"
                    fieldNames={fieldNames}
                    areaTree={areaTree}
                    loadData={(option) => {
                      loadData(option);
                    }}
                    changeOnSelect
                    // 自定义显示内容，显示完整路径
                    displayRender={(labels) => {
                      return labels[labels.length - 1];
                    }}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="现职级任职时间" {...wrapperCol} colon>
                {getFieldDecorator("cadre_rank_time", {
                  rules: [{ required: false, message: "请选择现职级任职时间" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    disabled={disabled}
                  />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="干部来源" {...wrapperCol} colon>
                {getFieldDecorator("source", {
                  rules: [{ required: false, message: "请选择干部来源" }],
                })(
                  <Select placeholder="请选择" disabled={disabled} allowClear>
                    {codeMap.sourceOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="干部分类" {...wrapperCol} required colon>
                {getFieldDecorator("user_type", {
                  rules: [{ required: true, message: "请选择干部分类" }],
                })(
                  <Select placeholder="请选择" disabled={disabled} allowClear>
                    {codeMap.userTypeLevelOption.map((item) => (
                      <Option value={Number(item.op_key)}>
                        {item.op_value}
                      </Option>
                    ))}
                  </Select>
                )}
              </FormItem>
            </Col>
            {IS_MIDDLE_CADRE && (
              <Fragment>
                <Col span={12}>
                  <FormItem label="现专技等级" {...wrapperCol} colon>
                    {getFieldDecorator("cur_specialty_level", {
                      rules: [{ required: false, message: "请选择现专技等级" }],
                    })(
                      <Select
                        placeholder="请选择"
                        disabled={disabled}
                        allowClear
                      >
                        {codeMap.curSpecialtyLevelOption.map((item) => (
                          <Option value={item.op_key}>{item.op_value}</Option>
                        ))}
                      </Select>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任现专技等级时间" {...wrapperCol} colon>
                    {getFieldDecorator("cur_specialty_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任专技九级时间" {...wrapperCol} colon>
                    {getFieldDecorator("specialty_nine_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任专技十级时间" {...wrapperCol} colon>
                    {getFieldDecorator("specialty_ten_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任专技十一级时间" {...wrapperCol} colon>
                    {getFieldDecorator("specialty_eleven_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任八级职员时间" {...wrapperCol} colon>
                    {getFieldDecorator("staff_eight_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任九级职员时间" {...wrapperCol} colon>
                    {getFieldDecorator("staff_nine_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="任四级主任科员" {...wrapperCol} colon>
                    {getFieldDecorator("section_member_four_level_time", {
                      rules: [{ required: false, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        disabled={disabled}
                      />
                    )}
                  </FormItem>
                </Col>
              </Fragment>
            )}
          </Row>
        </Col>

        <Col span={6}>
          <div className="avatar-box">
            <div className="title">头像</div>
            <div className="image">
              <Upload
                beforeUpload={onUpload}
                disabled={disabled}
                showUploadList={false}
              >
                <img src={getAvatar(getFieldValue("head_url"))} />
                <span className="upload">上传头像</span>
              </Upload>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
}

export default index;
