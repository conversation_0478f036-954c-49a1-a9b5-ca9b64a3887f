.upload-list {
  position: relative;
  .upload-trigger {
    margin-bottom: 10px;
  }
  .upload-list-container {
    .upload-list-wrapper {
      margin: 0;
      .upload-item-container {
        display: flex;
        align-items: center;
        &:not(:last-child) {
          margin-bottom: 10px;
        } // padding-left: 14px;
        .file-info-wrapper {
          flex-grow: 1;
          line-height: 20px;
          .file-name-wrapper {
            display: flex;
            align-items: center;
            .file-name {
              display: inline-block;
              max-width: 250px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-right: 20px;
              svg {
                width: 18px;
                height: 18px;
              }
            }
          }
          .file-size{
            color:#999;
            font-size:12px;
          }
        }
        .file-type-icon {
          margin-right: 10px;
          svg {
            width: 32px;
            height: 40px;
          }
        }
      }
    }
  }
}
