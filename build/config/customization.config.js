/*
 * @Description:
 * @Author: b<PERSON><PERSON>
 * @Date: 2021-02-03 11:35:31
 * @LastEditTime: 2024-07-23 10:21:56
 * @LastEditors: lhl
 */
const { resolve } = require("path");
const loginConfig = {
  documentTitle: JSON.stringify("智慧党建服务平台"), // 网站 title
  systemName: JSON.stringify("机关智慧党建"), // 菜单顶部显示
  loginIcp: JSON.stringify(
    "网站备案号：渝ICP备17015994号-1 | 邮 箱：<EMAIL>"
  ),
  customLoginBg: false, // 登录页面背景是否定制
  LoginFormPading: JSON.stringify("120px 110px 50px 0"),
};

/**
  登录页根据CustomizationConfig配置的key读取对应的背景图片及form表单背景图
  // 登录页form表单背景  扩展名：png
  loginFromBg: require(`../view/login/images/customization/login-from-bg${region || ""}.png`),
  // 登录页背景  扩展名：jpg
  loginBg: require(`../view/login/images/customization/login-bg${loginConfig.customLoginBg ? region || "" : ""}.jpg`),
 */
const CustomizationConfig = {
  // 未配置的系统会默认使用default配置
  // defalut之后的其他配置将覆盖default中的配置
  default: {
    // __dirname为当前文件所属文件夹路径: E:\WorkSpace\tog-admin\build\config
    // customView添加到webpack.config.js中resolve->alias
    customView: {
      CustomLoginView: resolve(
        __dirname,
        "../../client/view/login/login-view/LoginView"
      ),
    },
    // loginConfig在webpack.config.js  plugins->webpack.DefinePlugin中配置
    // DefinePlugin 下的---字符串---必须stringify
    loginConfig: {
      documentTitle: JSON.stringify("干部履职评价系统"), // 网站 title
      systemName: JSON.stringify("干部系统"), // 菜单顶部显示
      loginSubTitle: JSON.stringify(""),
      loginIcp: JSON.stringify("网站备案号：渝ICP备20008100号-1"),
      customLoginBg: false, //  登录页面背景是否定制
      LoginFormPading: JSON.stringify("120px 64px 50px 0"),
    },
    // 其他配置...
  },
};
module.exports = CustomizationConfig;
