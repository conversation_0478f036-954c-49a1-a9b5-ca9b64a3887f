import { useEffect, useState } from "react";
import SearchHeader from 'components/search-header';
import { Table, Select, Button, message } from "antd";
import "./index.less";
import { SettleInList } from "apis/organize";

const index = (props) => {
    const { history } = props
    const [selectStatus, setSelectStatus] = useState(null); // 选择的审核状态
    const [dataSource, setDataSource] = useState([]);
    const [total, setTotal] = useState(0);
    const [page_no, setPage_no] = useState(1);
    const [page_size, setPage_size] = useState(10);

    useEffect(() => {
        setPage_no(1)
        getList(1, page_size);
    }, [selectStatus]);

    const columns = [
        {
            title: "组织类别",
            key: "org_type",
            dataIndex: "org_type",
            align: "center",
        },
        {
            title: "组织名称",
            key: "org_name",
            dataIndex: "org_name",
        },
        {
            title: "挂靠党组织",
            key: "affiliation_name",
            dataIndex: "affiliation_name",
        },
        {
            title: "联系人",
            key: "user_name",
            dataIndex: "user_name",
            align: "center",
        },
        {
            title: "联系电话",
            key: "phone",
            dataIndex: "phone",
            align: "center",
        },
        {
            title: "状态",
            key: "audit_status",
            dataIndex: "audit_status",
            align: "center",
            render: (Item) => {
                return (
                    <span>{Item == 1 ? '待审核' : Item == 2 ? '审核通过':Item == 4 ? '已撤回' : '审核不通过'}</span>
                )
            }
        },
        {
            title: "操作",
            dataIndex: "action",
            align: "center",
            render: (Item, row) => {
                // todo 需要根据状态判断
                return (
                    <Button
                        type="link"
                        onClick={() =>
                            history.push("/ResidentAuditDetails", {
                                state: {
                                    isAudit: row.audit_status == 1 ? true : false, //是否审核
                                    org_id: row.org_expand_id,
                                    workflow_task_id: row.workflow_task_id,
                                },
                            })
                        }
                    >
                        {row.audit_status == 1 ? '审核' : '详情'}
                    </Button>
                );
            },
        },
    ];

    const handleChange = (value) => {
        setSelectStatus(value);
    };

    const getList = async (page, pageSize) => {
        const params = {
            audit_status: selectStatus,
            page_no: page ? page : page_no,
            page_size: pageSize ? pageSize : page_size
        }
        const { data } = await SettleInList(params);
        if (data.code === 0) {
            setTotal(data.total)
            setDataSource(data.data);
        } else {
            return message.error(data.message);
        }
    }

    return (
        <div className="CheckInAuditList">
            <SearchHeader title={'入驻审核'} />
            <div className="CheckInAuditList-select">
                <span>审核状态：</span>
                <Select defaultValue="" style={{ width: 120 }} onChange={handleChange}>
                    <Option value="">全部</Option>
                    <Option value={1}>待审核</Option>
                    <Option value={2}>审核通过</Option>
                    <Option value={3}>审核不通过</Option>
                    <Option value={4}>已撤回</Option>
                </Select>
            </div>
            <Table
                className="table"
                rowKey="org_id"
                // scroll={{ y: 400 }}
                columns={columns}
                dataSource={dataSource}
                bordered
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    total: total,
                    pageSize: page_size,
                    current: page_no,
                    showTotal: (total) => `总共 ${total} 条数据`,
                    onChange: (page, pageSize) => {
                        setPage_size(pageSize);
                        setPage_no(page);
                        getList(page, pageSize)
                    },
                    onShowSizeChange: (page, pageSize) => {
                        setPage_size(pageSize);
                        setPage_no(page);
                        getList(page, pageSize)
                    },
                }}
            />
        </div>
    );
};

export default index;
