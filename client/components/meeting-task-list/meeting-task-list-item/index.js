import React from "react";
import "./index.less";
import PropTypes from "prop-types";
import { Form, Input, Popconfirm } from "antd";
import InputTips from "components/activity-form/sub-components/InputTips";
import ShowUploadFileType from "components/show-upload-file-type";
import FileUpload from "components/file-upload";
import { numberToChinese } from "../../../tool/util";

const FormItem = Form.Item,
  TextArea = Input.TextArea;

const MeetingTaskListItem = (props) => {
  const {
    item,
    index,
    isPreviewDetail,
    isOnlyModifyPerson,
    isDirectlyWrite,
    canNotModifyBaseInfo,
    maxSize = 5000,
    form,
    onAnsCntChange,
    onFileChange,
    onDelete,
    onEdit,
    noTextArea,
    disabled
  } = props;
  const formItemLayout = {
    // labelCol: { span: 2 },
    // wrapperCol: { span: 10 }
  }
  const { getFieldDecorator, getFieldValue } = form;
  // console.log(item, index);

  const renderInitialValue = (item = {}) => {
    let result = "";
    const { contents = [] } = item;
    if (contents && Array.isArray(contents) && contents[0]) {
      result = contents[0].ans_cnt;
    }
    return result;
  }

  // console.log(noTextArea);
  return (
    <div className="meeting-task-list-item">
      <div className="item-header">
        <span className="item-name">
          <span className="item-index">{numberToChinese(index + 1)}</span>{item.name}
        </span>
        <span className="item-handler-wrapper">
          {
            (!item.topic_id && !isPreviewDetail && !isOnlyModifyPerson && !canNotModifyBaseInfo) &&
            <a href="javascript:void(0)" className="item-handler" onClick={() => {
              onEdit(item, index);
            }}>编辑</a>
          }
          {
            (!isPreviewDetail && !isOnlyModifyPerson && !canNotModifyBaseInfo) &&
            <Popconfirm
              title="确定删除"
              onConfirm={() => {
                onDelete(item, index);
              }}>
              <a href="javascript:void(0)" className="item-handler">删除</a>
            </Popconfirm>
          }
        </span>
      </div>
      {/* <div className="item-content">
        <span className="label">
          任务备注
          </span>
        <span className="content">
          {item.description}
        </span>
      </div> */}
      {
        !noTextArea &&
        <div className="item-content">
          {/* <span className="label">
            完成情况
          </span> */}
          <span className="content">
            <Form>
              <FormItem>
                <InputTips max={maxSize} text={getFieldValue("ans_cnt")}>
                  {
                    getFieldDecorator("ans_cnt", {
                      initialValue: renderInitialValue(item),
                      rules: [{
                        required: true, message: "请输入完成情况",
                        max: maxSize, message: `最多输入${maxSize}字`
                      }]
                    })(
                      <TextArea rows={5} autoSize={{ minRows: 3}} placeholder="请输入"
                        disabled={isPreviewDetail || isOnlyModifyPerson}
                        onChange={
                          (e) => {
                            const target = e.target,
                              value = target.value;
                            onAnsCntChange(value, index, item);
                          }
                          // () => ansCntChange()
                        } />
                    )
                  }
                </InputTips>
              </FormItem>
            </Form>
            {/* {
              !(isPreviewDetail || isOnlyModifyPerson) &&
              <FileUpload
                uploadedList={item.contents[0] ? item.contents[0].files : []}
                onChange={(data) => {
                  onFileChange(data, index, item);
                }}
              />
            } */}
            <ShowUploadFileType
              data={item.contents[0] ? item.contents[0].files : []}
              updateState={(data) => {
                onFileChange(data, index, item);
              }}
              loading={false}
              isDelete={!disabled}
            />
          </span>
        </div>
      }
    </div >
  )
}

MeetingTaskListItem.propTypes = {
  noTextArea: PropTypes.bool,
  item: PropTypes.object,
  index: PropTypes.number,
  maxSize: PropTypes.number,
  // 是否是直接填报
  isDirectlyWrite: PropTypes.bool,
  canNotModifyBaseInfo: PropTypes.bool,
  onFileChange: PropTypes.func,
  onAnsCntChange: PropTypes.func,
  onDelete: PropTypes.func,
  onEdit: PropTypes.func
}

MeetingTaskListItem.defaultProps = {
  noTextArea: false,
  item: {},
  index: -1,
  maxSize: 5000,
  isDirectlyWrite: false,
  canNotModifyBaseInfo: false,
  onFileChange: () => { },
  onAnsCntChange: () => { },
  onDelete: () => { },
  onEdit: () => { }
}

export default Form.create()(MeetingTaskListItem);