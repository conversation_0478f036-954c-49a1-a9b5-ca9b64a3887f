import React, { useState, useEffect } from "react";
import "./index.less";
import Header from "client/components/search-header";
import { Table, Modal, Select, Form, message, But<PERSON>, Popconfirm } from "antd";
import {
  queryGroupOrg,
  queryOrgGroupList,
  updateOrgGroup,
  deleteOrgGroup,
} from "client/apis/cadre-portrait";
import { getUrlQuery } from "client/tool/util";
import PersonModal from "./person-modal";
function index() {
  const { group_id } = getUrlQuery();

  const [visible, setVisible] = useState(false);
  const [visiblePerson, setVisiblePerson] = useState(false);
  const [loading, setLoading] = useState(false);
  const [groupList, setGroupList] = useState([]);
  const [current_group, setCurrentGroup] = useState(group_id);
  const [multiple, setMultiple] = useState(true);
  const [user, setUser] = useState({
    org_name: undefined,
    organization_id: undefined,
    institution_category_text: undefined,
  });

  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    loadData();

    loadGroupList();
  }, []);

  const loadData = async () => {
    setLoading(true);

    const res = await queryGroupOrg({ group_id });

    if (res.data.code === 0) {
      setDataSource(
        res.data.data.map((item, index) => {
          return {
            index: index + 1,
            ...item,
          };
        })
      );
    }

    setLoading(false);
  };

  const loadGroupList = async () => {
    const res = await queryOrgGroupList();

    if (res.data.code === 0) {
      setGroupList(res.data.data);
    }
  };
  // 1-县管正职 2-县管副职 3-中层干部

  const sequenceMap = {
    1: "县管正职",
    2: "县管副职",
    3: "中层干部",
  };

  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: "机构名称",
      dataIndex: "org_name",
      key: "org_name",
      align: "center",
    },
    // {
    //   title: "现任职务",
    //   dataIndex: "current_job",
    //   key: "current_job",
    //   align: "center",
    // },
    {
      title: "机构类别",
      dataIndex: "institution_category_text",
      key: "institution_category_text",
      align: "center",
      // render(_) {
      //   return sequenceMap[_];
      // },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      align: "center",
      render: (_, record) => (

        <div className="operation">
          <Popconfirm
            title="确定移除该序列?"
            onConfirm={() => {
              let obj = {
                org_id: record.organization_id,
                group_id: group_id
              }
              deleteOrgGroup(obj).then(res => {
                if (res.data.code === 0) {
                  message.success("移除成功")
                  loadData()
                }
              })
            }}
            okText="确认"
            cancelText="取消"
          >
            <a href="#" style={{ marginRight: '10px' }}>移除</a>
          </Popconfirm>
          <a
            className="editor"
            onClick={() => {
              setVisible(true);

              setUser(record);
            }}
          >
            修改序列
          </a>
        </div>
      ),
    },
  ];
  //   生成假数据
  const data = [];
  for (let i = 0; i < 10; i++) {
    data.push({
      index: i + 1,
      key: i,
      name: `Edward King ${i}`,
      current_job: 32,
      type: `London, Park Lane no. ${i}`,
    });
  }

  const onOk = async () => {
    const { organization_id } = user;

    const res = await updateOrgGroup({ org_id:organization_id, group_id: current_group });

    if (res.data.code === 0) {
      message.success("修改成功");

      loadData();

      setVisible(false);

      setCurrentGroup(undefined);

      setUser({});
    } else {
      message.error("修改失败");
    }
  };

  const onChange = (value) => {
    setCurrentGroup(value);
  };
  const hideModal = () => {
    loadData();
    setVisiblePerson(false);
  };
  const onChangeModal = (val) => {
    console.log("🚀 ~ onChangeModal ~ val:", val);
  };
  return (
    <div className="sequence-detail">
      <Header title="序列成员" onBack={() => history.back()} />
      {
        group_id &&
        <Button
          type="primary"
          onClick={() => {
            setVisiblePerson(true);
          }}
          style={{ margin: '10px' }}
        >
          添加成员
        </Button>
      }
      <Table
        dataSource={dataSource}
        columns={columns}
        bordered
        loading={loading}
      />
      <Modal
        visible={visible}
        title="修改序列"
        onOk={onOk}
        onCancel={() => {
          setVisible(false);
          setCurrentGroup(group_id);
          setUser({});
        }}
        destroyOnClose
      >
        <div className="sequence-detail-modal-content">
          <div className="name">{user.org_name}</div>
          {/* <div className="unit">{user.institution_category_text}</div> */}
          <div className="select">
            <Form.Item
              label="修改序列"
              colon
              labelCol={{
                span: 4,
              }}
              wrapperCol={{ span: 20 }}
            >
              <Select
                onChange={onChange}
                value={current_group ? Number(current_group) : undefined}
                style={{ width: 200 }}
                placeholder="请选择"
              >
                {groupList.map((item) => {
                  return (
                    <Select.Option value={Number(item.group_id)}>
                      {item.group_name}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
          </div>
        </div>
      </Modal>
      <PersonModal
        visible={visiblePerson}
        hideModal={hideModal}
        title={`添加成员`}
        onChange={onChangeModal}
        groupId={group_id}
      />
    </div>
  );
}

export default index;
