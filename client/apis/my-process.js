import { workflowHost } from './config';
const { http } = require( 'client/tool/axios' );

export const getTypeList = (data) => {
    return http.get(`${workflowHost}/type/list`, data);
}
export const getApprovalList = (data) => {
    return http.get(`${workflowHost}/approval/list`, data);
}
export const getApprovalId = (data) => {
    return http.get(`${workflowHost}/approval/get/${data.id}`, {cc: data.cc || 0});
}
export const operApproval = (data) => {
    return http.post(`${workflowHost}/approval/oper`, data);
}
export const undoApproval = (id) => {
    return http.delete(`${workflowHost}/approval/undo/${id}`);
}
export const commentApproval = (data) => {
    return http.post(`${workflowHost}/approval/comment`, data);
}