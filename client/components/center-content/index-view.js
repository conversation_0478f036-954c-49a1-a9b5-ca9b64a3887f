import React, { Component } from 'react';
import { Modal, Button, Form, Select, Input, Row, Col, Table } from 'antd';
import PropTypes from 'prop-types';
import './index.less';

/**
 *  弹出选择人员对话框
 * <AUTHOR>
 * @param { Boolean }       visible              控制是否显示 true 为显示
 * @param { Function }      handleOk             提交确认，返回值
 * @param { Function }      handleCancel         关闭显示，取消操作
 * @param { String   }      [title]              侧边栏主标题，默认为'选择人员'，可自己设置
 * @param {number }         [width]              可视区的宽度，默认为600px\
 * @param {Boolean}         singleSlec           控制选择人员是单选还是多选,true是多选
 * 打开
 * */
const centerContent = ({
    formValueName, props, dataList, selected, cancelSelect, departmentlist, selected1,
    searchSubmit, submitData, handleCancel, listLoading, changePage, pageNew
}) => {
    const columns = [{
        title: '姓名',
        dataIndex: 'name',
        width: '25%',
        key: 'name'
    }, {
        title: '电话',
        dataIndex: 'phone',
        width: '30%',
        key: 'title'
    },
    // }, {
    //     title: '工作部门',
    //     dataIndex: 'department_name',
    //     width: '25%',
    //     key: 'department_name'
    // }, {
    {
        title: '操作',
        width: '20%',
        render: (text, record) => {
            const { selectAdmin, centerContent } = props,
                { selectChange } = centerContent;
            let adminId = '', adminName = '';
            if (selectAdmin) {
                adminId = selectAdmin.adminId;
                adminName = selectAdmin.adminName;
            }
            const { editable } = record;
            if (props.singleSlec) {
                return (
                    <div>
                        {editable || (adminId === record.user_id && adminName === record.name && !selectChange) ?
                            <a onClick={() => cancelSelect(record)}>取消选择</a>
                            : <a onClick={() => selected(record)}>选择</a>
                        }
                    </div>
                )
            } else {
                return (
                    <div>
                        {editable || (adminId === record.user_id && adminName === record.name && !selectChange) ?
                            <a onClick={() => cancelSelect(record)}>取消选择</a>
                            : <a onClick={() => selected1(record)}>选择</a>
                        }
                    </div>
                )

            }
        }
    }];
    const { getFieldDecorator } = props.form,
        formItemLayout = {
            labelCol: { span: 4, offset: 1 },
            wrapperCol: { span: 16 }
        }
    return (
        <div>
            <Modal
                title={props.title || '选择人员'}
                visible={props.visible}
                onCancel={() => {
                    handleCancel(props.form);
                }}
                width={props.width || 600}
                footer={null}
            >
                <div id='content-form'>
                    <Form 
                    // onSubmit={searchSubmit}
                    >
                        <Row gutter={24}>
                            {/*<Col span={12}>*/}
                            {/*<Form.Item label='部门'  {...formItemLayout}>*/}
                            {/*{getFieldDecorator(formValueName.department, {*/}
                            {/*rules: [{required: false, message: ''}]*/}
                            {/*})(*/}
                            {/*<Select placeholder="请选择">*/}
                            {/*<Select.Option key={-1}>全部</Select.Option>*/}
                            {/*{departmentlist.map(val => (*/}
                            {/*<Select.Option key={val.department_id}>{val.name}</Select.Option>*/}
                            {/*))*/}
                            {/*}*/}
                            {/*</Select>*/}
                            {/*)}*/}
                            {/*</Form.Item>*/}
                            {/*</Col>*/}
                            <Col span={11}>
                                <Form.Item wrapperCol={{ span: 24 }}>
                                    {getFieldDecorator(formValueName.name, {
                                        initialValue: '',
                                        rules: [{ required: false, message: '请输入姓名查询' }]
                                    })(
                                        <Input placeholder='请输入姓名查询' style={{ width: '70%' }} />
                                    )}
                                    <Button onClick={
                                        searchSubmit
                                    }
                                        htmlType="submit" type="primary"
                                        style={{ width: '30%', borderRadius: '0' }}>查询</Button>
                                </Form.Item>
                            </Col>
                            {/*<Col span={4}>*/}
                            {/*<Form.Item>*/}

                            {/*</Form.Item>*/}
                            {/*</Col>*/}
                        </Row>
                    </Form>
                    <Table
                        columns={columns}
                        rowKey={record => `${record.user_id}-${record.department_id}`}
                        id='selectP'
                        title={() => (<span>查询结果</span>)}
                        dataSource={dataList}
                        loading={listLoading}
                        scroll={{ y: 200 }}
                        onChange={changePage}
                        pagination={{
                            pageSize: pageNew.pageSize,
                            total: pageNew.total,
                            current: pageNew.pageNum,
                            simple: true
                        }}
                    />

                    <Button type='primary' className='suerButton' onClick={submitData}
                        style={{ marginLeft: '45%', marginTop: '10px' }}>确定</Button>
                </div>
            </Modal>
        </div >
    );
}

centerContent.propTypes = {
    // 控制是否收起 true 为收起
    visible: PropTypes.bool.isRequired,
    //提交确认，返回值
    handleOk: PropTypes.func.isRequired,
    //关闭显示，取消操作
    handleCancel: PropTypes.func.isRequired,
    //当前弹框的题目 选择人员
    title: PropTypes.string,
    //当前弹框的宽度  600
    width: PropTypes.number,
    //控制选择人员是单选还是多选  true是多选
    singleSlec: PropTypes.bool.isRequired
};

centerContent.defaultProps = {
    visible: false,
    handleOk: () => { },
    title: "选择人员",
    handleCancel: () => { },
    width: 600,
    singleSlec: false
}

export default centerContent;