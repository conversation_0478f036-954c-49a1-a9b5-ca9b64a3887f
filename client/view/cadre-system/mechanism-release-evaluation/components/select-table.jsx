import React, { useState, useMemo, useRef } from "react";
import {
  <PERSON><PERSON>r,
  Button,
  Icon,
  Table,
  Modal,
  Select,
  Input,
  InputNumber,
  Form,
  Popconfirm,
  Checkbox,
  message,
} from "antd";
import DropText from "../../components/drop-text";
import { findByUser, initiateEvaluation } from "client/apis/cadre-portrait";
import NewUser from "./new-user";
import NewOrg from "./new-org";
import "./select-table.less";

const { Option } = Select;

const EditableContext = React.createContext();

const EditableRow = ({ form, index, ...props }) => (
  <EditableContext.Provider value={form}>
    <tr {...props} />
  </EditableContext.Provider>
);

const EditableFormRow = Form.create()(EditableRow);
class EditableCell extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      editing: false,
    };

    this.toggleEdit = this.toggleEdit.bind(this);
    this.save = this.save.bind(this);
    this.renderCell = this.renderCell.bind(this);
  }

  toggleEdit() {
    const editing = !this.state.editing;
    this.setState({ editing }, () => {
      if (editing) {
        this.input.focus();
      }
    });
  }

  save(e) {
    const { record, handleSave } = this.props;
    this.form.validateFields((error, values) => {
      if (error && error[e.currentTarget.id]) {
        return;
      }
      this.toggleEdit();
      handleSave({ ...record, ...values, o_seq: record.seq });
    });
  }
  renderCell(form) {
    this.form = form;
    const { children, dataIndex, record, title } = this.props;
    const { editing } = this.state;
    return editing ? (
      <Form.Item style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${title}为必填`,
            },
          ],
          initialValue: record[dataIndex],
        })(
          <Input
            ref={(node) => (this.input = node)}
            onPressEnter={this.save}
            onBlur={this.save}
          />
        )}
      </Form.Item>
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{ paddingRight: 24 }}
        onClick={this.toggleEdit}
      >
        {children}
      </div>
    );
  }
  render() {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      children,
      ...restProps
    } = this.props;
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    );
  }
}
export default function selectTable(props) {
  const {
    type = "1",
    num,
    org_id,
    org_name,
    parent_id,
    pattern_sub,
    group,
    job_type,
    sequence_name,
    sequence,
    checked,
    dataSource,
    onUpdateData,
    onDelete,
    onCheckChange,
    onSelectChange,
    onUpdateNumberChange, // 好的次数
    onSelectJobTypeChange,
    index
  } = props;
  const [visible, setVisible] = useState(false);

  const [orgVisible, setOrgVisible] = useState(false);

  const formRef = useRef(null);
  /**
   * @description: 二次确认
   * @param {*} e
   * @return {*}
   */
  const confirm = (user) => {
    const newData = dataSource.filter((item) => {
      if (user.user_id) {
        console.log("user_id");
        return item.user_id !== user.user_id;
      }
      if (user.evaluation_user_id) {
        return item.evaluation_user_id !== user.evaluation_user_id;
      }

      if (user.seq) {
        return user.seq !== item.seq;
      }
    });

    onUpdateData && onUpdateData({ org_id, user_list: newData, sequence, index });
    // 手动添加的用户不会被删除用户id列表
    if (user.type !== "new") {
      onDelete && onDelete(user.evaluation_user_id || user.user_id);
    }
  };

  const onOrgConfirm = (org) => {
    const { sequence } = props
    const { org_id, evaluation_org_id } = org;
    if (org_id) {
      const newData = dataSource.filter((item) => {
        return item.org_id !== org_id;
      });
      onUpdateData && onUpdateData({ sequence, user_list: newData, index });

      if (org.type !== "new") {
        onDelete && onDelete(evaluation_org_id || org_id);
      }
    }
  };
  /**
   * @description: 取消
   * @param {*} e
   * @return {*}
   */
  const cancel = (e) => {
    console.log(e);
  };

  /**
   * @description: 新增框确认
   * @return {*}
   */
  const handleOk = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        setVisible(false);
        // 找出最大数
        const maxSeq = dataSource.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 0);
        }, 0);

        onUpdateData({
          org_id,
          sequence,
          index,
          user_list: [
            ...dataSource,
            {
              ...formRef.current.getFieldsValue(),
              type: "add",
              seq: maxSeq + 1,
            },
          ],
        });
      }
    });
  };

  const orgHandleOk = () => {
    formRef.current.validateFields((errs, values) => {
      console.log(errs);
      if (!errs) {
        setOrgVisible(false);
        // 找出最大数
        const maxSeq = dataSource.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 0);
        }, 0);

        onUpdateData({
          sequence,
          index,
          user_list: [
            ...dataSource,
            {
              ...formRef.current.getFieldsValue(),
              type: "add",
              seq: maxSeq + 1,
            },
          ],
        });
      }
    });
  };

  const orgHandleCancel = () => {
    setOrgVisible(false);
  };

  //   取消
  const handleCancel = () => {
    setVisible(false);
  };
  /**
   * @description: 复选框选中
   * @return {*}
   */
  const checkChange = ({ target: { checked } }) => {
    onCheckChange && onCheckChange({ org_id, checked });
  };
  const TableTitle = ({ }) => {
    return (
      <div className="table-title">
        <div className="left-box"></div>
        <div className="right-box">
          <a
            className="add-btn"
            onClick={() => {
              type === "1" ? setOrgVisible(true) : setVisible(true);
            }}
          >
            +添加
          </a>
        </div>
      </div>
    );
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "20%",
      title: "姓名",
    },
    {
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      title: "测评职务类别",
      render(_, record) {
        const option = [
          {
            label: "县管正职",
            key: 1,
          },
          {
            label: "县管副职",
            key: 2,
          },
          {
            label: "改非干部",
            key: 3,
          },
          {
            label: "新提拔中层干部",
            key: 4,
          },
        ];
        if (![3, 4].includes(Number(_))) {
          option.splice(2, 2);
        }
        return (
          <DropText
            value={_}
            disabled={[3, 4].includes(Number(_))}
            disableList={[3, 4]}
            option={option}
            onChange={(key) => {
              dataSource.forEach((item) => {
                if (item.user_id === record.user_id) {
                  item.user_type = key;
                }
              });

              onUpdateData && onUpdateData({ org_id, user_list: dataSource, index });
            }}
          />
        );
      },
    },
    {
      dataIndex: "job",
      key: "job",
      editable: true,
      title: "测评职务",
      // render(_) {
      //   return <div style={{ paddingLeft: "80px" }}>{_}</div>;
      // },
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (text, record) => (
        <Popconfirm
          title="确认删除?"
          onConfirm={() => {
            confirm(record);
          }}
          onCancel={cancel}
          okText="是"
          cancelText="否"
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      ),
    },
  ];

  const columns1 = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "org_name",
      key: "org_name",
      align: "center",
      width: "20%",
      title: "班子名称",
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (text, record) => (
        <Popconfirm
          title="确认删除?"
          onConfirm={() => {
            onOrgConfirm(record);
          }}
          onCancel={cancel}
          okText="是"
          cancelText="否"
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      ),
    },
  ];
  const components = {
    body: {
      row: EditableFormRow,
      cell: EditableCell,
    },
  };

  const selectChange = (value) => {
    dataSource.forEach((item) => {
      item.group = value;
    });
    onSelectChange &&
      onSelectChange({ org_id, group: value, user_list: dataSource });
  };

  const onInputNumberChange = (value) => {
    if (Number(value) < 0) {
      return;
    }

    onUpdateNumberChange({ org_id, number: value, sequence });
  };

  const handleSave = (row) => {
    const newData = [...dataSource];
    let _index = -1;
    // 没有user_id的是手动添加的
    if (!row.user_id) {
      _index = newData.findIndex((item) => item.seq === row.o_seq);
    } else {
      _index = newData.findIndex((item) => row.user_id === item.user_id);
    }
    // 有修改才更新
    if (row.seq != dataSource[_index].seq || row.job !== dataSource[_index].job) {
      delete row.o_seq;

      _index !== -1 && newData.splice(_index, 1, row);

      onUpdateData && onUpdateData({ org_id, user_list: newData, sequence, index });
    }
  };

  const orgHandleSave = (row) => {
    const newData = [...dataSource];
    let index = -1;
    // 没有user_id的是手动添加的
    if (!row.org_id) {
      index = newData.findIndex((item) => item.seq === row.o_seq);
    } else {
      index = newData.findIndex((item) => row.org_id === item.org_id);
    }
    // 有修改才更新
    if (row.seq != dataSource[index].seq) {
      delete row.o_seq;

      index !== -1 && newData.splice(index, 1, row);

      onUpdateData && onUpdateData({ org_id, user_list: newData, sequence, index });
    }
  };

  const newColumns = (type === "1" ? columns1 : columns).map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave: type === "1" ? orgHandleSave : handleSave,
      }),
    };
  });
  return (
    <div className="middle-select-table">
      <div className="org-name">
        <div className="org-name-box margin-right-20">
          <span className="name">{sequence_name}</span>
        </div>

        {type === "2" && job_type && (
          <div className="org-name-box flex-1 select-box">
            <Select
              value={String(job_type || "")}
              style={{ width: "150px" }}
              placeholder="请选择"
              onChange={(value) => {
                onSelectJobTypeChange({ org_id, sequence, value });
              }}
            >
              <Option value="1">书记</Option>
              <Option value="2">镇长</Option>
            </Select>
          </div>
        )}
      </div>
      <div className="table-box">
        <Table
          bordered
          components={components}
          columns={newColumns}
          dataSource={dataSource}
          scroll={{ y: 300 }}
          title={TableTitle}
          pagination={false}
        />
      </div>
      <Modal
        title="添加测评人员"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
      >
        <NewUser ref={formRef} />
      </Modal>

      <Modal
        title="添加班子"
        visible={orgVisible}
        onOk={orgHandleOk}
        onCancel={orgHandleCancel}
        destroyOnClose
      >
        <NewOrg ref={formRef} />
      </Modal>
    </div>
  );
}
