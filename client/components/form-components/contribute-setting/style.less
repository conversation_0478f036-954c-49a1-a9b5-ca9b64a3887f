.contribute-setting-container {
  .contribute-setting-wrapper {
    display: flex;
    align-items: center;
  }
  .contribute-setting-wrapper.ant-radio-group {
    display: flex;
    align-items: center;
  }
  .quota-contribute-wrapper {
    margin-left: 25px;
  }
  .quota-input-wrapper {
    display: flex;
    align-items: center;
    .quota-input-label {
      white-space: nowrap;
      margin-right: 5px;
    }
    .ant-input-number {
      min-width: 120px;
    }
  }
}

.quota-contribute-modal {
  .ant-modal-body {
    // padding-left: 110px;
    // padding-right: 110px;
  }
  .content-wrapper {
    padding-left: 95px;
    padding-right: 95px;
    .add-item-wrapper {
      padding-left: 10px;
    }
    .list-wrapper {
      display: flex;
      flex-wrap: wrap;
      flex-grow: 1;
      margin: 20px 0 10px 0;
      .item-wrapper {
        display: inline-flex;
        align-items: center;
        position: relative;
        margin: 10px;
        width: 100px;
        .icon-wrapper {
          position: absolute;
          top: -13px;
          right: -9px;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
    .ant-input,
    .ant-input-number {
      width: 100%;
      border-radius: unset;
    }
  }
  .buttons-wrapper {
    margin: 50px 0 26px 0;
    text-align: center;
    .ant-btn {
      margin: 0 10px;
      width: 115px;
      height: 36px;
    }
  }
}