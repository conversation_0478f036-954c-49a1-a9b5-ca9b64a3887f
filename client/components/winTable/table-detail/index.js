import React from "react";
import {
  Table,
  Pagination
} from "antd";

/**
 * @description 假翻页(测试)
 * @param array
 * @param pageSize
 * @param pageNo
 * @returns {{totalData: Array, data: *, pageNo: number, pageSize: number}}
 */
function createPagesData (array = [], pageSize = 1, pageNo = 1) {
  const { length } = array;
  const total = Math.ceil(length / pageSize);
  const data = [];
  for (let i = 0; i < total; i++) {
    data.push(array.slice(i * pageSize, pageSize * (i + 1)));
  }
  return data[pageNo - 1]
}

/**
 * @description 党费未交情况--弹框
 */
class TableDetail extends React.Component {

  /**
   * @description 翻页
   * @param pageNo
   */
  page (pageNo) {
    this.props.onPage(pageNo)
  }

  render () {
    const {
      modalData = [],
      pageNo = 0
    } = this.props;
    const data = createPagesData(modalData, 5, pageNo);
    const columns = [
      { title: "序号", dataIndex: "sort", key: "sort", align: "center" },
      { title: "姓名", dataIndex: "name", key: "name", align: "center" },
      { title: "所在组织", dataIndex: "organ", key: "organ", align: "center" },
      { title: "应交金额", dataIndex: "amount", key: "amount", align: "center" }
    ];
    let dataList = [];
    if (!!data) {
      data.forEach((item, index) => {
        const {
          type,
          org_name,
          user_name,
          pay_should
        } = item;
        dataList.push({
          key: index,
          sort: index + 1,
          name: user_name,
          organ: org_name,
          amount: type === 4 ? "未设置" : pay_should || "--"
        })
      })
    }

    const pages = () => {
      return (
        <Pagination
          style={{ marginTop: "20px" }}
          showQuickJumper
          current={ pageNo }
          pageSize={ 5 }
          total={ modalData.length }
          onChange={ this.page.bind(this) }
        />
      )
    };

    return (
      <div className="t-wrap">
        <div
          className="t-wrap-table"
          style={{ marginTop: 0 }}
        >
          <Table
            className="t-table-el"
            columns={ columns }
            dataSource={ dataList }
            bordered
          />
        </div>
        {
          (!!data && data.length) && pages()
        }
      </div>
    )
  }
}

export default TableDetail;
