import { Di<PERSON><PERSON>, Modal } from "antd";
import { CDN } from "apis/config";
import { Base64 } from "client/tool/util";
import { useState, useEffect } from "react";
import { SettleInDetail } from "apis/organize";
// import { getDataDictionary } from "@/apis/active-organization";
import SectionTitle from "components/activity-form/sub-components/SectionTitle";
import moment from "moment";

const index = (props) => {
  const [previewImg, setPreviewImg] = useState();
  const [orgid, setOrgid] = useState(props.state.org_id);
  const [detailItem, setDetailItem] = useState([]);

  useEffect(() => {
    getSettleInDetail()
  }, [props])

  const getSettleInDetail = async () => {
    const params = {
      id: props.state.org_id
    }
    const { data } = await SettleInDetail(params);
    let arrName = data.data.user_list.map(f => f.user_name + ' - ' + f.phone).join('，')
    if (data.code == 0) {
      setDetailItem([
        { label: "组织名称", value: data.data.org_name },
        { label: "负责人", value: `${data.data.charge} - ${data.data.charge_phone} - ${data.data.charge_political}` },
        { label: "是否完成民政局登记", value: data.data.has_register == 1 ? '是' : '否' },
        { label: "成立日期", value: `${moment(data.data.org_create_time).format("YYYY年MM月DD日")}` },
        { label: "地址", value: `${data.data.org_address}` },
        { label: "联系人", value: `${arrName}` },
        { label: "挂靠关系", value: `${data.data.affiliation_name}` },
        {
          label: "组织介绍",
          value: `${data.data.introduction}`
        },
        {
          label: "证明材料",
          value: img(`${CDN}/${data.data.org_docs}`),
          // value: `${CDN}/${data.data.org_docs}`,
          info: "材料包括：1.经县民政局登记的法人证书；2.负责人身份证（正反面）；3.乡镇（街道）备案手续；4.村（社区）党组织书记承诺书",
        },
      ])
    }

  }
  const renderItem = (item) => {
    const postFilePreview = (params) => {
      const url = `${CDN}/${params.file_path}`;
      if (params.file_path.includes("img/")) {
        setPreviewImg(url);
      } else {
        const previewUrl = `${url}&fullfilename=${params.file_name}`;
        window.open(
          "https://dangjian.cq.tobacco.gov.cn/html/fileview/onlinePreview?url=" +
          encodeURIComponent(
            Base64.encode(previewUrl.replace("https", "http"))
          )
        );
      }
    };

    return (
      <div className="CheckInAuditDetail-Info-Item">
        <div className="CheckInAuditDetail-Info-Item-label">{item.label}</div>
        <div className="CheckInAuditDetail-Info-Item-value">
          {Array.isArray(item.value) ? (
            <span>
              {item.value.map((item) => {
                return <img src={item} alt="" />;
              })}
            </span>
          ) : (
            item.value
          )}
        </div>
        {item.info && (
          <div className="CheckInAuditDetail-Info-Item-info">{item.info}</div>
        )}
      </div>
    );
  };
  const img = (img) => {
    return (
      <img src={img}></img>
    )
  }
  return (
    <div className="CheckInAuditDetail-Info">
      {/* <Divider orientation="left" orientationMargin="0" dashed>
        <Divider type="vertical" className="title-vertical" />
        申请资料
      </Divider> */}
      <SectionTitle title="申请资料" />
      {detailItem.map((item) => {
        return renderItem(item);
      })}

      <Modal
        className="img-preview_wrap"
        width="50%"
        visible={previewImg}
        closable={false}
        footer={false}
        onCancel={() => setPreviewImg()}
      >
        <img className="preview-img" width="100%" src={previewImg} alt="" />
      </Modal>
    </div>
  );
};

export default index;
