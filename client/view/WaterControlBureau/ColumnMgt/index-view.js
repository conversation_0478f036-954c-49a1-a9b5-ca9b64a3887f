import React from 'react';
import { Table, Popconfirm, Button } from 'antd';
import SearchHeader from 'client/components/search-header';

export default (props) => {
  const {
    title,
    dataSource,
    tableLoading,
    moveHandler,
    addHandler,
    edit<PERSON>and<PERSON>,
    delete<PERSON>andler,
    management<PERSON>and<PERSON>
  } = props;
  const moveNewsColumnHandler = (record, changeRecord) => {

    moveHandler(record, changeRecord);
  }
  const addNewsColumnHandler = (parentColumn) => {
    addHandler(parentColumn);
  }
  const newsManagement = (record) => {
    managementHandler(record);
  }
  const editNewsColumns = (record) => {
    editHandler(record);
  }
  const deleteNewsColumn = (record) => {
    deleteHandler(record);
  }
  const popconfirmProps = {
    title: '确认删除？'
  }
  const columns = [
    {
      title: '栏目',
      dataIndex: 'column_name',
      key: 'column_name',
      render(text, record) {
        return text + (record.is_default ? '（默认栏目）':'')
      }
    },
    {
      title: '资源数',
      align: 'center',
      dataIndex: 'resource_num',
      key: 'resource_num',
      width: 160
    },
    {
      title: '操作',
      align: 'center',
      width: 470,
      render(text, record, index) {
        // console.log(record);
        let deleteBtn;
        if(record.parent_id !== 0 || (record.parent_id === 0 && (!record.count && !record.children))){
            deleteBtn = (
                <Popconfirm {...popconfirmProps} onConfirm={() => {
                  deleteNewsColumn(record);
                }}><Button type="link">删除</Button>
                </Popconfirm>
            );
        }

        return (
          <div className={'handler-wrapper'}>
            <Button type="link" disabled={index === 0} onClick={() => moveNewsColumnHandler(record, record.siblingNodes[index - 1])}>
              上移
            </Button>
            <Button type="link"  disabled={index === (record.siblingNodes.length - 1)} onClick={() => moveNewsColumnHandler(record, record.siblingNodes[index + 1])}>
              下移
            </Button>
            <Button type="link" onClick={() => newsManagement(record)}>
              管理资源
            </Button>
            <Button type="link" onClick={() => editNewsColumns(record)}>
              编辑栏目
            </Button>
            {
              (!record.top_column_id || record.top_column_id == 0) &&
              <Button type="link" onClick={() => addNewsColumnHandler(record)}>
                新增子栏目
              </Button>
            }
            {deleteBtn}
          </div >
        )
      }
    }
  ]
  const tableProps = {
    columns,
    dataSource,
    bordered: true,
    rowKey: 'column_id',
    loading: tableLoading,
    //取消分页
    pagination: false,
    title() {
      return (
        <a href="javascript:void (0)" onClick={() => addNewsColumnHandler()}>新增栏目</a>
      )
    }
  }
  return (
    <div className="index-view-container">
      <SearchHeader title={title} />
      <section className="main-content-container">
        <Table {...tableProps} />
      </section>
    </div>
  )
}
