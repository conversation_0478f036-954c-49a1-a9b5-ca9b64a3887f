.add-document {
  &-form {
    padding: 34px 0 25px;

    .preview-img {
      .imageClip {
        width: 150px;
      }
    }

    .phoneInput {
      margin-bottom: 0;
      display: inline-block;
    }

    .image {
      .view {
        width: 82px;
        height: 82px;
        cursor: pointer;
      }

      .text {
        cursor: pointer;
        padding-top: 15px;
        width: 82px;
        height: 82px;
        background: #EEEEEE;
        text-align: center;
        color: #999999;
        font-size: 12px;

        .anticon {
          margin-bottom: 4px;
          font-size: 25px;
        }

        &>div {
          height: 12px;
          line-height: 12px;
        }
      }
    }

    .annotation {
      font-size: 12px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #BFBFBF;
      line-height: 24px;
    }

    .clipping {
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      z-index: 99999;

      iframe {
        width: 100%;
        height: 100%;
      }
    }

    .select-org {
      .ant-form-item-control-wrapper {
        width: 46.83333333%;

        .ant-form-item-children {
          display: flex;
        }
      }
    }

    .ant-form-item {
      margin-bottom: 27px;
    }

    .sort-line {
      margin-bottom: 10px;
    }

    .seq-line {
      margin: 10px 0 10px;
    }

    textarea.ant-input {
      resize: none;
    }

    .ant-radio-wrapper:hover .ant-radio,
    .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus+.ant-radio-inner {
      border-color: #F46E65;
    }

    .ant-radio-checked {
      .ant-radio-inner {
        border-color: #F46E65;
      }
    }

    .ant-radio-inner {
      &::after {
        background-color: #F46E65;
      }
    }

    .ant-checkbox-checked>.ant-checkbox-inner {
      background-color: #F46E65;
      border-color: #F46E65;
    }

    .btn {
      .ant-btn {
        width: 90px;
        height: 36px;
        border-radius: 4px;
        font-size: 16px;
      }

      .ant-btn-primary {
        background: #F46E65;
      }

      .ant-btn-default {
        color: #999999;
        margin-left: 24px;
      }
    }
  }
}

.document-preview-modal {
  .ant-modal-body {
    padding: 24px 140px 48px;
  }

  .preview-modal-tab {
    margin-bottom: 36px;
    text-align: center;

    &>:first-child {
      margin-right: 60px;
    }

    &-item {
      display: inline-block;
      position: relative;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #999999;
      cursor: pointer;
    }

    .active {
      font-weight: bold;
      color: #FF4D4F;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -6px;
        width: 100%;
        height: 3px;
        background: #FF4D4F;
        border-radius: 2px;
      }
    }
  }

  .preview-modal-content {
    &-pc {
      .ant-carousel {
        width: 50%;
        display: inline-block;
      }

      &-Carousel {
        width: 100%;
        height: 100%;
        float: left;
        margin-bottom: 40px;
      }

      &-titleBox {
        width: 49%;
        float: right;
        display: inline-block;
        padding-left: 36px;

        &-title {
          font-size: 36px;
          font-family: PingFang SC-Heavy, PingFang SC;
          font-weight: 800;
          color: #333333;
          line-height: 36px;

          .ant-descriptions-item-content {
            font-size: 36px;
            font-family: PingFang SC-Heavy, PingFang SC;
            font-weight: 800;
            color: #333333;
            line-height: 36px;
          }
        }

        &-tags {
          display: inline-block;
          height: 24px;
          border-radius: 5px;
          background: #e9e9e9;
          width: 95px;
          text-align: center;
          line-height: 24px;
          margin-right: 10px;
        }

        &-titleText {
          display: flex;
          font-size: 18px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;

          .ant-descriptions-item-content {
            // width: 70%;
          }
        }

        .displaynone {
          display: none;
        }
      }


      &-info {
        padding-bottom: 30px;
        border-bottom: 1px solid #EEEEEE;
        text-align: center;
        font-size: 16px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;

        &-v1,
        &-v2 {
          margin-right: 24px;
        }
      }

      &-abs {
        margin-top: 24px;
        padding: 24px 29px;
        background: #F1F1F1;
        border-radius: 10px;
        font-size: 18px;
        font-family: PingFang SC-Bold, PingFang SC;
        color: #666666;

        &>span:first-child {
          font-weight: bold;
        }
      }

      &-text {
        font-size: 18px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        Text-indent: 2em;
      }
    }

    &-mobile {
      position: relative;
      width: 100%;
      height: 700px;
      background: url(../../image/rectangle.png) no-repeat center/contain;

      .preview-modal-content-phone {
        position: absolute;
        width: 100%;
        height: 600px;
        top: 50%;
        transform: translateY(-50%);
        padding: 0px 465px;
        font-family: 'PingFang SC';
        font-style: normal;
        -ms-overflow-style: none;
        overflow-x: hidden;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        &-info {
          // padding-bottom: 12px;
          // margin-bottom: 16px;

          &-v1 {
            margin-top: 5px;
            margin-bottom: 12px;
            font-weight: 900;
            font-size: 16px;
          }

          &-v2 {
            color: #666666;
            font-weight: 400;
            font-size: 14px;
          }

          &-v3 {
            margin-top: 12px;

            span {
              padding: 2px 4px;
              margin-right: 16px;
              background: #EEEEEE;
              border: 1px solid #EEEEEE;
              color: #999999;
              border-radius: 3px;
              font-weight: 400;
              font-size: 14px;
            }
          }
        }

        &-titleBox {
          &-text {
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            Text-indent: 2em;
          }

          .phone-icon {
            position: relative;
            top: 4px;
            height: 20px;
            width: 20px;
            display: inline-block;
            margin-right: 10px;
            margin-top: -3px;
          }


          &-titleText {
            padding-bottom: 8px;
            position: relative;

            .phone-icon-right {
              position: absolute;
              top: 20px;
              right: 0;
              height: 20px;
              width: 20px;
              display: inline-block;
            }
          }
        }

        &-abs {
          margin-bottom: 18px;
          padding: 10px 8px;
          background: #F8F8F8;
          border-radius: 5px;
          font-size: 15px;
          color: #666666;

          &>span:first-child {
            font-weight: bold;
          }
        }

        &-wrap,
        &-src,
        &-date {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }

        &-src {
          margin: 18px 0 14px;
        }

        &-wrap {
          img {
            width: 100% !important;
            height: auto !important;
          }
        }
      }
    }

  }
}

.add-document {
  &-form {
    padding: 34px 0 25px;

    &-imgTips {
      margin-top: 10px;
      font-size: 12px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #BFBFBF;
      line-height: 12px;
    }

    .up-file {
      height: 150px;
      width: 150px;
      text-align: center;
      background-color: #e9e9e9;
      padding-top: 45px;
      display: inline-block;

      .anticon {
        display: block;
      }
    }

    .up-file:hover {
      cursor: pointer;
    }

    .phoneInput {
      margin-bottom: 0;
      display: inline-block;
    }

    .phone-or {
      margin: 0 10px;
      display: inline-block;
    }

    .image {
      .imgBox {
        padding-top: 5px;
        box-sizing: border-box;
        position: relative;
        display: inline-block;

        .view {
          display: inline-block;
          width: 82px;
          height: 82px;
          cursor: pointer;
          margin-left: 10px;
          vertical-align: baseline;
          padding-top: 5px;
        }

        .icon {
          position: absolute;
          top: 5px;
          right: -5px;
        }
      }


      .text {
        display: inline-block;
        cursor: pointer;
        padding-top: 15px;
        width: 82px;
        height: 82px;
        background: #EEEEEE;
        text-align: center;
        color: #999999;
        font-size: 12px;

        .anticon {
          margin-bottom: 4px;
          font-size: 25px;
        }

        &>div {
          height: 12px;
          line-height: 12px;
        }
      }
    }

    // .annotation {
    //   font-size: 12px;
    //   font-family: PingFang SC-Medium, PingFang SC;
    //   font-weight: 500;
    //   color: #BFBFBF;
    //   line-height: 24px;
    // }

    // .clipping {
    //   position: fixed;
    //   top: 0px;
    //   left: 0px;
    //   width: 100%;
    //   height: 100%;
    //   z-index: 99999;

    //   iframe {
    //     width: 100%;
    //     height: 100%;
    //   }
    // }

    // .select-org {
    //   .ant-form-item-control-wrapper {
    //     width: 46.83333333%;

    //     .ant-form-item-children {
    //       display: flex;
    //     }
    //   }
    // }

    .ant-form-item {
      margin-bottom: 27px;
    }

    // .sort-line {
    //   margin-bottom: 10px;
    // }

    .seq-line {
      margin: 10px 0 10px;
    }

    textarea.ant-input {
      resize: none;
    }

    // .ant-radio-wrapper:hover .ant-radio,
    // .ant-radio:hover .ant-radio-inner,
    // .ant-radio-input:focus+.ant-radio-inner {
    //   border-color: #F46E65;
    // }

    .ant-radio-checked {
      .ant-radio-inner {
        border-color: #F46E65;
      }
    }

    .ant-radio-inner {
      &::after {
        background-color: #F46E65;
      }
    }

    // .ant-checkbox-checked>.ant-checkbox-inner {
    //   background-color: #F46E65;
    //   border-color: #F46E65;
    // }

    .btn {
      .ant-btn {
        width: 90px;
        height: 36px;
        border-radius: 4px;
        font-size: 16px;
      }

      .ant-btn-primary {
        background: #F46E65;
      }

      .ant-btn-default {
        color: #999999;
        margin-left: 24px;
      }
    }
  }

  .ant-form-item-children {
    // display: flex;
    // flex-wrap: wrap;
    // align-items: center;
    width: 100%;

    .preview-img {
      display: inline-block;
      // display: flex;
      // flex-wrap: wrap;
      // align-items: center;
      margin: 10px;

      // display: block;
      // width: 600px;
      .img-wrap {
        position: relative;
        // flex: 0 0 29%;
        // width: 180px;
        text-align: center;
        // margin-bottom: 20px;
        // margin-right: 20px;
        min-width: 268px;

        .operation-btn {
          position: absolute;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 156px;

          // left: 28%;
          .anticon {
            display: none;
            padding: 20px 10px;
            font-size: 30px;
            color: #eee;
            cursor: pointer;

            &:hover {
              color: #fff;
            }
          }
        }

        .img {
          height: 156px;
          width: 100%;
          background-size: contain;
          color: #CCC;
        }

        &:hover {
          .operation-btn {
            background-color: rgba(0, 0, 0, 0.3);

            .anticon {
              display: inline-block;
            }
          }
        }
      }
    }
  }
}

.document-preview-modal {
  .ant-modal-body {
    padding: 24px 140px 48px;
  }

  .preview-modal-tab {
    margin-bottom: 36px;
    text-align: center;

    &>:first-child {
      margin-right: 60px;
    }

    &-item {
      display: inline-block;
      position: relative;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #999999;
      cursor: pointer;
    }

    .active {
      font-weight: bold;
      color: #FF4D4F;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -6px;
        width: 100%;
        height: 3px;
        background: #FF4D4F;
        border-radius: 2px;
      }
    }
  }

  .ant-descriptions-item-content {
    display: flex;
  }

  .position-text {
    width: 95%;
  }

  .lastTap {
    display: flex;
  }
}

.lastMsg {
  margin-top: 10px;
  letter-spacing: 2px;
  font-size: 18px;
  color: #999;
  font-weight: 400;
}