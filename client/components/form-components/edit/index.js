import React from "react";
import emuns from "config/emuns";
import formFields from "config/formFields";
import { Form, message } from "antd";
import {
  ActivityEdit
} from "components/activity-form"; //活动表单
import {
  download_model,
  upUserSccope,
  limitUser
} from "apis/activity";
import propTypes from "prop-types";
// 参加人员侧面弹窗
import Personnel from "./personnel";
// 参加组织选择
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import "./style.less";

class Edit extends React.Component {
  constructor(props) {
    super(props);
    this.state = {}
  }

  // 活动编辑
  ActivityEditHandle() {
    const { state, updateState } = this.props;
    // 获取下载模板
    const getTemplate = async () => {
      const { data } = await download_model();
      updateState({
        downloadModelName: data.data ? data.data.model_file_id : ""
      });
    };
    // 上传人员名单成功
    const upUserScopeSuccess = res => {
      if (res.code === 0) {
        updateState({
          task_id: res.data.task_id,
          limit_user: 0,
          _user_count: res.data.num,
          upload: false
        });
      } else {
        message.error(res.message);
      }
      updateState({
        upload: false
      });
    };
    const onStart = res => {
      updateState({
        upload: true
      });
    };
    // 选择组织
    const chooseOrg = () => {
      updateState({
        orgModal: true
      });
    };
    // 保存所选组织
    const saveOrg = async (organization_ids) => {
      const { task_id } = state;
      updateState({
        orgModal: false,
        user_count: organization_ids.length,
        organization_ids,
        _org_count: organization_ids.length
      });
      // updateUserScope
      const { data } = await upUserSccope(task_id, (organization_ids || []).map(org => org.org_id))
      if (data.code === 0) {
        updateState({
          task_id: data.data.task_id,
          user_count: data.data.num
        });
      }
    };
    // 关闭选择组织
    const closeOrg = () => {
      updateState({
        orgModal: false
      });
    };
    // 选择人员限制
    const choosePerson = () => {
      updateState({
        personnelModal: true
      });
    };
    // 关闭人员限制
    const closePersonnel = () => {
      updateState({
        personnelModal: false
      });
    };
    // 选择人员限制保存
    const savePersonnel = async (rule) => {
      const { data } = await limitUser({
        activity_limit_user_id: state.activity_limit_user_id,
        editRule: rule
      });
      if (data.code === 0) {
        updateState({
          activity_limit_user_id: data.data.activity_limit_user_id,
          editRule: rule
        });
      }
      closePersonnel();
    };
    // 组件内radio事件
    // 参加人员范围radio
    const personnelRangeRadio = e => {
      updateState({
        limit_user_scope: e.target.value,
        limit_user:
          e.target.value === 2
            ? 0
            : e.target.value === 1
              ? 0
              : state.limit_user,
        limit_public: e.target.value === 1 ? 0 : state.limit_public
      });
    };
    // 参加人员限制
    const personnelRadio = e => {
      updateState({
        limit_user: e.target.value
      });
    };
    // 可见度
    const visibleRadio = e => {
      updateState({
        limit_public: e.target.value
      });
    };
    return {
      onStart,
      getTemplate,
      chooseOrg,
      closeOrg,
      saveOrg,
      upUserScopeSuccess,
      choosePerson,
      closePersonnel,
      savePersonnel,
      personnelRangeRadio,
      visibleRadio,
      personnelRadio
    };
  }

  render() {
    const { state, isPreview, data, updateState, form } = this.props;
    return (
      <div className="form-components-edit-container">
        <ActivityEdit
          handleEvents={this.ActivityEditHandle.bind(this)()}
          content={state}
          form={form}
          preview={isPreview}
        />
        <Personnel
          visible={state.personnelModal}
          updateState={updateState}
          state={state}
          handleCancel={this.ActivityEditHandle.bind(this)().closePersonnel}
          handleSave={this.ActivityEditHandle.bind(this)().savePersonnel}
        />
        {
          state.orgModal ? (
            <OrganizeModal {...{
              visible: state.orgModal,
              //: [{org_id:3407, name:"asdasd"}],
              dataSource: state.organization_ids,
              hideModal: this.ActivityEditHandle.bind(this)().closeOrg,
              loadOrganizeData: (data) => {
                this.ActivityEditHandle.bind(this)().saveOrg(data);
                // const org_ids = data.map(org => org.org_id);
              }
            }} />
          ) : null
        }
      </div>
    )
  }
}

Edit.propTypes = {
  state: propTypes.object,
  isPreview: propTypes.bool,
  data: propTypes.object,
  updateState: propTypes.func
}

Edit.defaultProps = {
  state: {},
  isPreview: false,
  data: {},
  updateState: () => { }
}

export default Form.create()(Edit);