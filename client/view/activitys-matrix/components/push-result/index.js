
// import { pushResults } from "apis/activity";

import React, { Component } from "react";

import { Modal, message } from "antd";

import PushResultView from './view';

import PropTypes from "prop-types";


const propTypes = {
  currentData: PropTypes.object,
  visible: PropTypes.bool,
  onChange: PropTypes.func,
  loading: PropTypes.bool,
};

const defaultProps = {

};

export default Object.assign( class PushResult extends Component {

  constructor(props) {
    super(props);
    this.state = {
      data:{
        data:{} //结构 http://10.10.100.12:8090/pages/viewpage.action?pageId=13009465
      },
      loading: false
    }

    this.handleCancel = this.handleCancel.bind(this);

  }

  componentDidUpdate(prevProps, prevState){

    if(this.props.currentData
      && prevProps.currentData != this.props.currentData){

        // const { activity_id } = this.props.currentData;
        this.pushResults();

    }
  }
  pushResults(page=1, pagesize=10){
    const self = this;
    const data = this.props.currentData;

    if(this.props.onChange){
      self.setState({
        loading : true
      })
      const pms = this.props.onChange(data, page, pagesize)

      if(pms instanceof Promise){
        pms.then((data) => {
          //model需处理数据结构为
          //http://10.10.100.12:8090/pages/viewpage.action?pageId=13009465
          self.setState({
            data : data
          })
        }).catch(e=>{
          console.error(e)
        })
        self.setState({
          loading : false
        })
      }else{
        throw new Error('onChange must return instanceof Promise')
      }

    }
  }


  changePage(page, pagesize){
    this.pushResults(
      page,
      pagesize
    )
  }

  handleCancel(){
    if(this.props.onCancel){
      this.props.onCancel();
    }
  }

  render(){


    return (<Modal
      title="推送结果"
      width={800}
      visible={this.props.visible}
      // onOk={this.handleOk}
      onCancel={this.handleCancel}
      footer={null}
    >
      <PushResultView
        title={this.props.title}
        data={this.state.data}
        onChange={(page, pageSize)=>{this.changePage(page, pageSize)}}
        onShowSizeChange={(page, pageSize)=>{this.changePage(page, pageSize)}}
        ></PushResultView>
    </Modal>
    )
  }
}, {propTypes, defaultProps} )
