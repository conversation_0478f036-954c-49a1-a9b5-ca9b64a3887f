const { http } = require('client/tool/axios');
import {host, ucHost} from './config';


// //104002003901-创建委员会届次
export const createCommitteePeriodUrl = (data = {}) => {
    return http.post(`${host}/period/add`, data);
}

//1061140602-修改委员会届次
export const changeCommitteePeriodUrl = (data = {}) => {
    return http.post(`${host}/period/update`, data);
}

//104002003903-删除委员会届次
export const deleteCommitteePeriodUrl = ({period_id}) => {
    return http.get(`${host}/period/delete/${period_id}`);
}

//104002003904-查询委员会届次
export const searchCommitteePeriodUrl = (data = {}) => {
    return http.get(`${host}/period/find`, data);
}

// 30002020301201-查询书记和副书记
export const getSecretaryList = (period_id) => {
    return http.get(`${host}/org-period-member/boss`, { period_id });
}

//1061140605-查询委员会届次-组织信息查询
export const getOrgParams = ({org_id}) => {
    return http.get(`${host}/period/find-org/${org_id}`);
}

//104002003906-修改委员会成员
export const changeCommitteeMemberUrl = (data = {}) => {
    return http.post(`${host}/org-period-member/update`, data);
}

//104002003906-新增委员会成员
export const addCommitteeMemberUrl = (data = {}) => {
    return http.post(`${host}/org-period-member/add`, data);
}

//104002003907-删除委员会成员
export const deleteCommitteeMemberUrl = (data = {}) => {
    return http.post(`${host}/org-period-member/delete/${data}`);
}

//104002003908-查询委员会成员
export const searchCommitteeMemberUrl = (data = {}) => {
    return http.get(`${host}/org-period-member/list/${data.period_id}`);
}

//104002003801-通用获取职务/职级数据字典
export const commonJobUrl = (data = {}, filter = 3) => {
    // filter 1:获取书记 2:获取副书记 3:获取除了书记和副书记外的职务
    return http.get(`${host}/leader/option/${data.type}`, { filter });
}

//10611400702-党组织委员会参数 配置-查询
export const getPartyOrgCommittee = () => {
	return http.get(`${host}/party-org-committee/list`)
}

//10611400701-党组织委员会参数 配置-更新
export const updateParyOrgCommittee = (data = {}) => {
	return http.post(`${host}/party-org-committee/update`, data)
}

//1061140610-查询委员会届次-详细-头部
export const getPeriodDetail = (data = {}) => {
	return http.get(`${host}/period/detail/${data}`)
}
//1061140608-查询委员会成员
export const getPeriodMember = (data = {}) => {
	return http.get(`${host}/org-period-member/list`, data)
}
//10611400704-查询纪律检查委员会、党务办公室这两个字段是否可见
export const getOrgCommittee = (data = {}) => {
	return http.get(`${host}/party-org-committee/is-visible/${data}`)
}
// 1061140609-查询用户是否专职党务干部，是否专职纪检干部
export const getExtendInfo = (data = {}) => {
	return http.get(`${host}/user-extend-info/is-full-time/${data}`)
}
