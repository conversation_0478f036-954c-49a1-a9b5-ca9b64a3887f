/**
 * 物品管理界面
 * <AUTHOR>
 */
import React, { Component } from "react";
import { Form, message } from "antd";
import { connect } from "dva";
import IndexView from "./index-view";
import SideView from "./side-view";
import "./index.less";

// 暂不明确为何设置该标志位
let isSport = false;
// 切换边栏显示或者消失
const toggleSide = (dispatch, isShow = false) => {
  if (isShow) {
    // 切换外壳显示动画
    dispatch({ type: "articleManage/toggleHideWrap" });
    setTimeout(() => {
      dispatch({ type: "articleManage/toggleCollapsed" });
    }, 0);
  } else {
    // 关闭的时候 等待动画完毕的时候执行隐藏外壳
    dispatch({ type: "articleManage/toggleCollapsed" });
    setTimeout(() => {
      isSport = false;
      dispatch({ type: "articleManage/toggleHideWrap" });
    }, 300);
  }
};
// 变更侧边栏标题
const toggleModalTitle = (dispatch, title = "") => {
  dispatch({ type: "articleManage/toggleModalTitle", title });
};

class ArticleManage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      prize_id: null,
    };
  }

  componentDidMount() {
    //富文本编辑器
    const { dispatch } = this.props;
    dispatch({ type: "articleManage/fetchArticleList", payload: {} });
  }

  render() {
    const { descriptionContent, descriptionAppContent } = this.state;
    const { articleManage, dispatch, form, history } = this.props,
      _this = this,
      {
        //列表数据
        dataList,
        //侧边栏编辑框控制及表单数据
        collapsed,
        isHideWrap,
        modalTitle,
        uploadFileUrl,
        formData,
        fileList,
        //列表分页
        pageNum,
        pageSize,
        total,
      } = articleManage,
      { name, description, description_app } = formData,
      indexViewProps = {
        onBack() {
          history.goBack();
        },
        dataList,
        props: this.props,
        //列表分页
        pageNum,
        pageSize,
        total,
        editHandler(row) {
          toggleSide(dispatch, true);
          toggleModalTitle(dispatch, "编辑奖品");
          let { prize_id } = row;
          _this.setState({
            prize_id: prize_id,
          });
          //根据prize_id获取物品信息
          dispatch({
            type: "articleManage/fetchArticleById",
            payload: { id: prize_id },
          }).then((data) => {
            // console.log('获取到数据', data);
            _this.description_editor.setEditorContent(
              decodeURIComponent(data.description)
            );
            _this.description_app_editor.setEditorContent(
              decodeURIComponent(data.description_app)
            );
          });
        },
        deleteHandler(row) {
          // console.log('删除栏目', row);
          let { prize_id } = row;
          dispatch({
            type: "articleManage/deleteArticle",
            payload: { id: prize_id },
          })
            .then(() => {
              dispatch({
                type: "articleManage/fetchArticleList",
                payload: {
                  pageNum: 1,
                  pageSize,
                },
              })
                .then(() => {
                  dispatch({
                    type: "articleManage/save",
                    payload: {
                      pageNum: 1,
                    },
                  });
                })
                .catch((error) => {
                  console.error(error);
                });
            })
            .catch((error) => {
              console.error(error);
            });
        },
        pageChangeHandler(pageNum, pageSize) {
          dispatch({
            type: "articleManage/fetchArticleList",
            payload: { pageNum, pageSize },
          })
            .then(() => {
              dispatch({ type: "articleManage/save", payload: { pageNum } });
            })
            .catch((error) => {
              console.error(error);
            });
        },
      },
      sideViewProps = {
        onEditorInput(e, editor) {
          editor.setContent(e.target.innerHTML);
        },
        collapsed,
        isHideWrap,
        modalTitle,
        _this,
        form,
        fileList,
        props: this.props,
        // BraftEditor: this.BraftEditor,
        // BraftEditorLoading: this.state.BraftEditorLoading,
        uploadFileUrl,
        //表单的初始值
        name,
        onClose() {
          // 关闭的时候 加一个开关  解决 当点击背景的时候 多次点击 会造成事件重复执行
          if (isSport) return;
          isSport = true;
          toggleSide(dispatch, false);

          //重置表单状态
          form.resetFields();
          //上传列表清空
          dispatch({ type: "articleManage/save", payload: { fileList: [] } });
        },
        submitHandler(e) {
          e.preventDefault();
          form.validateFields((errors, values) => {
            //表单校验通过
            if (!errors) {
              let postObject = {
                name: "", //'string|必填|奖品名称',
                description: "", //'string|必填|奖品描述',
                description_app: "", //'string|可选|奖品描述(APP)',
                first_img: "", //'string|可选|第一张图片的url',
                img_two: "", //'string|可选|第二张图片的url',
                img_three: "", //'string|可选|第三张图片的url',
                img_four: "", //'string|可选|第四张图片的url',
                img_five: "", //'string|可选|第五张图片的url'
                create_user: "",
                create_time: "",
                update_time: "",
                last_change_user: "",
                status: "",
              };
              if (values.name) {
                postObject.name = values.name;
              }
              //网站使用奖品描述
              postObject.description = encodeURIComponent(
                _this.description_editor.getEditorContent()
              );

              if (values.manageScope === 1) {
                //APP与网站使用不同奖品描述
                postObject.description_app = encodeURIComponent(
                  _this.description_editor.getEditorContent()
                );
              } else if (values.manageScope === 0) {
                //APP与网站使用相同奖品描述
                postObject.description_app = encodeURIComponent(
                  _this.description_app_editor.getEditorContent()
                );
              }
              if (Array.isArray(fileList) && fileList.length !== 0) {
                fileList.forEach((image, index) => {
                  if (index === 0) {
                    postObject.first_img = image.url;
                  } else if (index === 1) {
                    postObject.img_two = image.url;
                  } else if (index === 2) {
                    postObject.img_three = image.url;
                  } else if (index === 3) {
                    postObject.img_four = image.url;
                  } else if (index === 4) {
                    postObject.img_five = image.url;
                  }
                });
              }
              //将prize_id装入请求体数据
              postObject.prize_id = _this.state.prize_id;
              //
              if (formData.create_user) {
                postObject.create_user = formData.create_user;
              }
              if (formData.create_time) {
                postObject.create_time = formData.create_time;
              }
              if (formData.update_time) {
                postObject.update_time = formData.update_time;
              }
              if (formData.last_change_user) {
                postObject.last_change_user = formData.last_change_user;
              }
              if (formData.status) {
                postObject.status = formData.status;
              }
              if (formData.organization_id) {
                postObject.organization_id = formData.organization_id;
              }
              //发送异步请求，编辑物品
              dispatch({
                type: "articleManage/editArticle",
                payload: { postObject },
              })
                .then((res) => {
                  if (!res) return;
                  console.log("🚀 ~ file: index.js ~ line 253 ~ res", res);
                  message.success("编辑成功");
                  //编辑之后重载当前页
                  dispatch({
                    type: "articleManage/fetchArticleList",
                    payload: {
                      pageNum,
                      pageSize,
                    },
                  });
                  toggleSide(dispatch, false);
                  //重置表单状态
                  form.resetFields();
                  //上传列表清空
                  dispatch({
                    type: "articleManage/save",
                    payload: { fileList: [] },
                  });
                })
                .catch((error) => {
                  console.error(error);
                });
            }
          });
        },
        //删除图片处理器
        removeHandler(index) {
          console.log("删除图片", index, fileList);
          fileList.splice(index, 1);
          dispatch({ type: "articleManage/save", payload: { fileList } });
        },
        // customRequest(info) {
        //     console.log('手动上传', info);
        // },
        changeHandler(e) {
          console.log("上传文件变化", e);
        },
        beforeUpload(e) {
          console.log("文件上传之前", e);
        },
        onStart(e) {
          console.log("开始上传", e);
        },
        onSuccess(response) {
          // console.log('上传成功', response);
          let { code, data, status } = response;
          if (status === 200 && code === 0) {
            if (data && data.length === 1) {
              const successFile = data && data[0];
              let uid = successFile.path;
              let uploadFile = {
                uid: -parseInt(uid),
                name: `${uid}`,
                status: "done",
                url: `${uid}`,
              };
              // console.log('上传成功', `${fileHost}/file/download/${uid}.png`);
              if (fileList.length < 5) {
                fileList.push(uploadFile);
              }
              dispatch({ type: "articleManage/save", payload: { fileList } });
            }
          } else {
            message.error(response.message);
          }
        },
        onError(e) {
          // console.error('上传失败', e);
          message.error("上传失败");
        },
        onChangeDescription: (val, attr) => {
          this.setState({ [attr]: val });
        },
      };
    return (
      <div className="article-manage">
        <IndexView {...indexViewProps} />
        <SideView {...sideViewProps} />
      </div>
    );
  }
}

const mapStateToProps = ({ articleManage }) => ({ articleManage });

// 由dva提供的connect连接方法  去连接 当前组件需要用到的状态
export default connect(mapStateToProps)(Form.create()(ArticleManage));
