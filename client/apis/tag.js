import { ucHost } from './config';
const { http } = require( 'client/tool/axios' );

// 查询所有标签/标签组列表
export const getTagList = ( { type = 2, tagId, page = 1 } ) => {
    return http.get( `${ucHost}/uc/tag/getTag`, {
        type, tagId
    } );
};

export const addTagList = ( { parent_id = 0, name } ) => {
    return http.post( `${ucHost}/uc/tag/add`, {
        parent_id, name
    } );
}

export const editTagList = ( { tag_id, name } ) => {
    return http.post( `${ucHost}/uc/tag/update`, {
        tag_id, name
    } );
}

export const deleteTagList = ( {id} ) => {
    return http.delete( `${ucHost}/uc/tag/del/${id}` );
}

export const editBatch = ( { tag_id, tag_list, type = 2 } ) => {
    return http.post( `${ucHost}/uc/tag/batch-edit`, {
        tag_id, tag_list, type
    } );
}

//查询标签列表 lvhe
export const getTabList_v2 = (data) => {
    return http.get(`${ucHost}/uc/tag/getTagList`, data);
}
export const addTagList_v2 = (data) => {
    return http.post( `${ucHost}/uc/tag/add`, data);
}
export const editTagList_v2 = (data) => {
    return http.post( `${ucHost}/uc/tag/update`, data);
}
