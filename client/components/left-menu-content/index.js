import React, { Component } from 'react';
import { Icon } from 'antd';
import SelfIcon from "components/self-icon";
import './index.less';

/**
 * 主体内容区左侧主要按钮
 * <AUTHOR>
 * @param { Array }  menuList      按钮列表
 * @description  [{
 * 		name : '按钮名字',
 * 		icon : '按钮图标',
 * 		handleClick : 点击按钮后的回调
 * }]
 * @return {*}
 */
export default function ( { menuList = [], listType = false } ) {
	if ( menuList.length === 0 ) return null;

	if(listType){
		return (
			<div className={"main-left-menu "}>

                {menuList.map( val => {
                	if(val.component){
                		return val.component;
					}

					return (
						<div className="li" onClick={val.handleClick} key={val.key}>
                            {val.icon && <SelfIcon type={val.icon}/>}
							<span>{val.name}</span>
						</div>
					)
				})}

			</div>
		)

	}

	return (
		<div className="main-left-menu">
			<ul>
				{menuList.map( val => (
					<li onClick={val.handleClick} key={val.key}>
						{val.icon && <SelfIcon type={val.icon}/>}
						<span>{val.name}</span>
					</li>
				) )}
			</ul>
		</div>
	)
}
