import { memo, useState, useEffect, useMemo } from 'react';
import QRCode from "qrcode.react";
import SelfIcon from "components/self-icon";
import { Button, Modal } from "antd";

const QrCodeEL = ({ codeInfo = [], downLoading, downloadAll, DownQr, qrVisible, openQrVisible }) => {
  console.log('🚀 ~ file: q------ ~ codeInfo:', codeInfo);
  return <Modal
    title={"下载测评码" + Math.random()}
    visible={qrVisible}
    onCancel={() => openQrVisible()}
    width="60%"
    className="org-modal-qr"
    footer={[
      <Button
        type="primary"
        disabled={downLoading}
        loading={downLoading}
        onClick={downloadAll}
      >
        批量下载
      </Button>,
    ]}
  >
    <div className="Qr-box">
      <QrCodeEL />
      {codeInfo.map(({ url, title }, index) => {
        return (
          <div className="box" key={index}>
            <div id={"qrCode" + index} key={index} className="qr-warp">
              <QRCode
                value={url}
                size={240} // 二维码的大小
                fgColor="#000000" // 二维码的颜色
                style={{ margin: "auto" }}
              />
              <div className="qr-text">{title}请扫此码测评</div>
            </div>
            <div
              className="down-text"
              onClick={DownQr(index, title)}
            >
              <SelfIcon className="down-icon" type={"gsg-xiayi"} />
              <div>下载二维码</div>
            </div>
          </div>
        );
      })}
    </div>
  </Modal>
};
export default memo(QrCodeEL)