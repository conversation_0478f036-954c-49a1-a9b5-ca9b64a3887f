import React, { useState } from "react";
import "./index.less";

import CadreInfo from "./components/cadre-info";
import Search from "./components/search";
import StatisticAnalysis from "./components/statistic-analysis";
const menu = [
  {
    label: "干部信息",
    key: 1,
    icon: require("./image/user.png"),
    icon_active: require("./image/user-active.png"),
  },
  {
    label: "干部查询",
    key: 2,
    icon: require("./image/chaxun.png"),
    icon_active: require("./image/chaxun-active.png"),
  },
  {
    label: "统计分析",
    key: 3,
    icon: require("./image/tongji.png"),
    icon_active: require("./image/tongji-active.png"),
  },
];

function index() {
  const [active, setActive] = useState(2);
  return (
    <div className="cadre-home">
      <div className="header">县管干部</div>
      <div className="menu">
        {menu.map((item) => {
          return (
            <div
              key={item.key}
              className={`menu-item ${
                active === item.key ? "active-menu" : ""
              }`}
            >
              <img src={active === item.key ? item.icon_active : item.icon} />
              <span
                className="label"
                onClick={() => {
                  setActive(item.key);
                }}
              >
                {item.label}
              </span>
            </div>
          );
        })}
      </div>
      <div className="content">
        {active === 1 && <CadreInfo />}
        {active === 2 && <Search />}
        {active === 3 && <StatisticAnalysis />}
      </div>
    </div>
  );
}

export default index;
