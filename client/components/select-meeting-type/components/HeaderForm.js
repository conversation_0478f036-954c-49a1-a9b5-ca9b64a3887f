import React from "react";
import PropTypes from "prop-types";
import { Form, Input, Select, Button } from "antd";


const Option = Select.Option;
const FormItem = Form.Item;

const HeaderForm = (props) => {
  const { form, data, search } = props;
  const { getFieldDecorator, validateFields } = form;

  const commonStyle = {
    width: "150px",
    height: "32px"
  }

  const ButtonStyle = {
    width: "90px",
    height: "36px",
    backgroundColor: "#f46e65",
    borderRadius: "5px",
    border: "none",
    marginLeft: "12px"
  }
  const fieldArr = [];
  const renderView = (record, index) => {
    fieldArr.push(record.getField);
    if (record.type === "Select") {
      return (
        <FormItem
          key={index}
          label={record.label}
        >
          {getFieldDecorator(record.getField, {
            initialValue: record.init
          })(
            <Select
              style={commonStyle}
              onChange={record.onChange && record.onChange}
            >
              <Option value={null}>全部</Option>
              {record.mayBeSelect.map((_item, index) => {
                return (
                  <Option key={index} value={_item[record.valueKey]}>{_item[record.nameKey]}</Option>
                )
              })}
            </Select>
          )}
        </FormItem>
      )
    }
    return (
      <FormItem
        key={index}
        label={record.label}
      >
        {getFieldDecorator(record.getField, {
          initialValue: ""
        })(
          <Input style={commonStyle} />
        )}
      </FormItem>
    )
  }
  const handleSubmit = (e) => {
    e.preventDefault();
    validateFields(fieldArr, (err, values) => {
      if (!err) {
        search && search(values);
      }
    })
  }
  return (
    <div>
      <Form
        layout="inline"
        style={{ marginLeft: "12px" }}
        onSubmit={handleSubmit}
      >
        {data.map((item, index) => {
          return renderView(item, index);
        })}
        <FormItem>
          <Button type="primary" htmlType="submit" style={ButtonStyle}>查询</Button>
        </FormItem>
      </Form>
    </div>
  )
}


HeaderForm.propTypes = {
  form: PropTypes.object,
  data: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    getField: PropTypes.string,
    type: PropTypes.string,
    valueKey: PropTypes.string,
    nameKey: PropTypes.string,
    mayBeSelect: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.number,
      name: PropTypes.string
    }))
  })),
  search: PropTypes.func
}
export default HeaderForm;