import { useState, useEffect, useMemo } from "react";
import { Card, Select, Icon, Radio, DatePicker,Empty  } from "antd";
import { YearsData } from "client/tool/util";
import moment from "moment";
import { getOrganizationalVitalityRanking } from "apis/Vitality-index";
import gold from "../image/gold.png"
import silver from "../image/silver.png"
import copper from "../image/copper.png"

const { MonthPicker } = DatePicker;
const { Option } = Select;
const RadioGroup = Radio.Group;
const CardList = ({ type, cardType, org_id }) => {
  const { data: yearList } = YearsData(2019, false);
  const [dateVal, setDateVal] = useState(moment().subtract(1,'months'));
  const [searchType, setSearchType] = useState("month");
  const [Res, setRes] = useState([]);
  useEffect(() => {
    console.log("🚀 ~ file: CardList.js:5 ~ props:", cardType);
    getBranchUserLineChartToplist()
  }, [org_id, dateVal]);

  //村/社党组织活力指数榜
  const getBranchUserLineChartToplist = async () => {
    const params = {
      time: searchType !== "year" ? moment(dateVal).format("YYYYMM") : '',
      org_id: org_id,
      year: searchType === "year" ? moment(dateVal).format("YYYY") : ''
    }
    const { data: res } = await getOrganizationalVitalityRanking(params);
    if (res.code !== 0) return message.error(res.message);
    setRes(res.data)
  };
  return (
    <Card
      title={
        <span className="title-wrap">
          <div className="title">{type === '1' && "村/社党"}组织活力指数榜</div>
          <div>
            <RadioGroup
              value={searchType}
              buttonStyle="solid"
              onChange={({ target: { value } }) => {
                setDateVal(moment().subtract(1,'months'));
                setSearchType(value);
              }}
            >
              <Radio.Button value="month">月度</Radio.Button>
              <Radio.Button value="year">年度</Radio.Button>
            </RadioGroup>
            {searchType === "year" ? (
              <Select
                value={Number(dateVal.format("YYYY"))}
                onChange={(val) => setDateVal(moment(String(val)))}
              >
                {yearList.map((year) => (
                  <Option key={year} value={year}>
                    {year}
                  </Option>
                ))}
              </Select>
            ) : (
              <MonthPicker
                allowClear={false}
                value={dateVal}
                disabledDate={(current) => current > moment().endOf("day").subtract(1,'months')}
                onChange={(val) => setDateVal(val)}
              />
            )}
          </div>
        </span>
      }
      bordered={false}
      className="cardList-wrap"
    >
      {Res.map((item, index) => {
          return (
            <div className="card-item">
              <div className="item">
                {index == 0 ?
                  <div className="rank-icon rank-1"><img src={gold}></img></div>
                  : index == 1 ?
                    <div className="rank-icon rank-1"><img src={silver}></img></div>
                    : index == 2 ?
                      <div className="rank-icon rank-1"><img src={copper}></img></div>
                      :
                      <div className="rank-4">{index + 1}</div>}
                <div className="item-info">
                  <div className="org">
                    <div className="ord-name">
                      {item.name}
                    </div>
                    <div className="org-score">{`${item.score}`}</div>
                  </div>
                  <div className="org-type">组织类型：{item.org_range == 1 ? "党委" : item.org_range == 2 ? "党总支" : "党支部"}</div>
                  <div>组织类别：{item.org_category == 104801 ? "城市社区" : item.org_category == 104802 ? "涉农社区" : "村"}</div>
                </div>

              </div>
            </div>
          )
        })
      }
      {Res.length==0&&<Empty />}
      {/* <div className="card-item">
        <div className="item">
          <div className="rank-icon rank-1"></div>
          <div className="item-info">
            <div className="ord-name">
              丰都县名山街道花园社区委员名山街道花园社区委员会
            </div>
            <div className="org-type">组织类型：组织</div>
            <div>组织类别：组织类别</div>
          </div>
          <div className="item-score">99.10</div>
        </div>
      </div>
      <div className="card-item">
        <div className="item">
          <div className="rank-icon rank-2"></div>
          <div className="item-info">
            <div className="ord-name">
              丰都县名山街道花园社区委员名山街道花园社区委员会
            </div>
            <div className="org-type">组织类型：组织</div>
            <div>组织类别：组织类别</div>
          </div>
          <div className="item-score">99.10</div>
        </div>
      </div>
      <div className="card-item">
        <div className="item">
          <div className="rank-icon rank-3"></div>
          <div className="item-info">
            <div className="ord-name">
              丰都县名山街道花园社区委员名山街道花园社区委员会
            </div>
            <div className="org-type">组织类型：组织</div>
            <div>组织类别：组织类别</div>
          </div>
          <div className="item-score">99.10</div>
        </div>
      </div>
      <div className="card-item">
        <div className="item">
          <div className="rank-4">4</div>
          <div className="item-info">
            <div className="ord-name">
              丰都县名山街道花园社区委员名山街道花园社区委员会
            </div>
            <div className="org-type">组织类型：组织</div>
            <div>组织类别：组织类别</div>
          </div>
          <div className="item-score">99.10</div>
        </div>
      </div> */}
    </Card>
  );
};
export default CardList;
