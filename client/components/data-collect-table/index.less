@red: #FF4D4F;
@grey: #999;

.data-collect-table-container {
  .tab-content-container {
    .deduct-wrapper {
      text-align: right;
      color: @red;
      padding-top: 15px;
      font-weight: 700;
      .btn-last{
        color: #fff;
        background: #F46E65;
        text-align: center;
        margin-left:20px;
        span {
          text-align:center;
          margin-left: 0px !important
        }
      }
      span {
        margin-left: 20px;

        // &:last-child {
        //   line-height: 32px;
        //   position: relative;
        //   display: inline-block;
        //   font-weight: 400;
        //   white-space: nowrap;
        //   text-align: center;
        //   background-image: none;
        //   border: 1px solid transparent;
        //   -webkit-box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
        //   box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
        //   cursor: pointer;
        //   -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        //   transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        //   -webkit-user-select: none;
        //   -moz-user-select: none;
        //   -ms-user-select: none;
        //   user-select: none;
        //   -ms-touch-action: manipulation;
        //   touch-action: manipulation;
        //   height: 32px;
        //   padding: 0 15px;
        //   font-size: 14px;
        //   border-radius: 4px;
        //   color: #fff;
        //   background-color: #FF4D4F;
        //   border-color: #FF4D4F;
        // }
      }

    }

    .intro-wrapper {
      padding-top: 15px;
      color: @grey;
      // line-height: 18px;

    }
  }

  .tab-search {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;

    // width: 100%;
    .tab-search_el {
      margin-right: 30px;
      width: 250px;
    }
  }
}