import { http, filterParamsValue } from 'client/tool/axios'

import { fileHost } from './config';

//新闻栏目数据
export const fetchNewsColumn = (data) => {
  return http.get(`${fileHost}/news-column/find-all`,data);
}

//移动新闻栏目
export const moveNewsColumn = (data) => {
  return http.get(`${fileHost}/news-column/move`, data);
}

//新增新闻栏目
export const addNewsColumn = (data) => {
  return http.post(`${fileHost}/news-column/add`, data);
}

//删除新闻栏目
export const deleteNewsColumn = (id) => {
  return http.delete(`${fileHost}/news-column/delete/${id}`);
}

//编辑新闻栏目
export const editNewsColumn = (data) => {
  return http.post(`${fileHost}/news-column/update`, data);
}
//加载新闻列表
export const loadnewsList = ({ page }) => {
  return http.get(`${fileHost}/news/find-list`, { page });
};
//加载新闻列表
export const searchnewsList = (params) => {
  return http.get(`${fileHost}/news/find-list`, filterParamsValue(params));
};
// 删除新闻
export const deleteNews = ({ id }) => {
  return http.delete(`${fileHost}/news/delete/${id}`);
}
// 取消发布新闻
export const Unpublish = ({ id }) => {
  return http.get(`${fileHost}/news/cancel-send-news/${id}`);
}
//设置新闻序号
export const setNewIndex = ({ nid, oid }) => {
  return http.get(`${fileHost}/news/move-order`, { nid, oid });
};
// 批量移动新闻
export const batchMoveNews = ({ ids, id }) => {
  return http.post(`${fileHost}/news/move-column`, { ids, id });
};
//编辑照片
export const setPhotos = ({ id, url }) => {
  return http.get(`${fileHost}/news/upload-focus`, { id, url });
};
//设置置顶与聚焦
export const setRecommend = ({ id, type, focus_img }) => {
  return http.get(`${fileHost}/news/set-recommend`, { id, type, focus_img: focus_img });
};



//10300080108-根据栏目id查询栏目名称
export const columnName = (id) => {
  return http.get(`${fileHost}/news-column/column-name/${id}`);
};

//10300080308-新闻列表(推送、接收、矩阵)
export const matrix = (param) => {
  return http.get(`${fileHost}/news_matrix`, filterParamsValue(param));
};


//http://************:8090/pages/viewpage.action?pageId=13009435
//10300080309-新闻列表/推送列表-推送发起、修改
export const pushto = (param) => {
  return http.post(`${fileHost}/news_matrix/push`, param);
};


//http://************:8090/pages/viewpage.action?pageId=16253136
//10300080316-推送列表-撤回推送
export const pushRollback = (param) => {
  return http.post(`${fileHost}/news_matrix/push/rollback?news_id=${param.news_id}`);
};


//http://************:8090/pages/viewpage.action?pageId=13009492
//10300080310-推送列表-查询推送结果
export const pushResults = (param) => {
  return http.get(`${fileHost}/news_matrix/push/result`, param);
};


// http://************:8090/pages/viewpage.action?pageId=14648116
// 10300080315-新闻管理-已发布-推送/转发类型-显示/隐藏

export const setVisibility = (param) => {
  const { news_id=0, visibility } = param;
  return http.get(`${fileHost}/news/visibility/${news_id}/${visibility}`);
};

//http://************:8090/pages/viewpage.action?pageId=13729894
//10300080313-新闻矩阵-接受列表-推送设置
export const newsPushsetup = (param) => {
  //{super_org_ids:param.map(org=>org.org_id)}
  let data = [];
  param.forEach(item => {
    data.push(`super_org_ids=${item.org_id}`)
  });
  let urlstr = `${fileHost}/news_matrix/receive/blacklist`;
  if (data.length) {
    urlstr += `?${data.join('&')}`;
  }

  return http.post(urlstr);
}

// http://************:8090/pages/viewpage.action?pageId=13729898
//10300080314-新闻矩阵-接受列表-获取当前推送设置
export const newsGetpushsetup = (param) => {
  return http.get(`${fileHost}/news_matrix/receive/blacklist`, param);
}

// http://************:8090/pages/viewpage.action?pageId=13009492
//10300080310-推送列表-查询推送结果
export const newsMatrixResult = (param) => {
  return http.get(`${fileHost}/news_matrix/push/result`, param);
}

// http://************:8090/pages/viewpage.action?pageId=22610924
//10300080319-获取已推送新闻的配置
export const pushtoInfo = (id) => {
  return http.post(`${fileHost}/news_matrix/push/config?news_id=${id}`);
}


//转发新闻
//http://************:8090/pages/viewpage.action?pageId=13009497
//10300080312-转发新闻
export const rePostNews = (payload) => {
  const {
    news_id,
    main_column_id,
    sub_column_ids,
    is_top,
    is_focus,
    img_url
  } = payload;
  let sub_column_ids_str = "";
  if (Array.isArray(sub_column_ids)) {
    sub_column_ids.map((el, k) => {
      sub_column_ids_str += `&sub_column_ids=${el}`;
    });
  }

  let url = `${fileHost}/news_matrix/repost?news_id=${news_id}
  &main_column_id=${main_column_id}
    ${is_top ? `&is_top=${is_top}` : ""}
    ${is_focus ? `&is_focus=${is_focus}` : ""}
    ${img_url ? `&img_url=${img_url}` : ""}
    ${sub_column_ids_str ? `${sub_column_ids_str}` : ""}
    `.replace(/\s+/g, "");
  return http.post(url);
};
