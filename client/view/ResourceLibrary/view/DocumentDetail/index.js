import React, { useEffect, useState } from "react";
import <PERSON>Header from "components/search-header";
import { Input, Icon, message, Carousel } from "antd";
import { findById, collectionEvent } from "apis/resource-library";
import { CDN } from "apis/config";
import ShowUploadFileType from "../../components/showUploadFileType";
import "./index.less";

/**
 * 文件库、案例库资源详情
 * path: /resource-library/case/detail
 *       /resource-library/document/detail
 */

const {
  user_id,
  user_name,
  oid: org_id,
  name: org_name,
  regionId: region_id,
} = JSON.parse(sessionStorage.getItem("UserInfo") || "{}");
const DocumentDetail = (props) => {
  const {
    history,
    location: {
      state: { resource_library_id, keyWord },
    },
  } = props;

  const [dataSource, setDataSource] = useState({});
  const [imgList, setImgList] = useState([]);
  const [keyword, setkeyword] = useState("");

  useEffect(() => {
    // 埋点
    collectionEvent({
      user_id,
      user_name,
      org_id,
      org_name,
      region_id,
      type: "L",
      object: resource_library_id,
      channal: "ResourceManager",
      transfer_time: new Date().getTime(),
      amount: 1,
    });
    initDataSource();
    if (keyWord) {
      setkeyword(keyWord);
    }
  }, []);

  const initDataSource = () => {
    findById({ resourceLibraryId: resource_library_id }).then(
      ({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        setDataSource(res.data);
        let imgs = [];
        res.data.resource_files &&
          res.data.resource_files.map((item) => {
            if (/\.(jpg|jpeg|png)$/i.test(item.path)) {
              imgs.push(item);
            }
          });
        setImgList(imgs);
      }
    );
  };

  const searchHighlight = (e) => {
    e.preventDefault();
    setkeyword(e.target.value);
  };

  const Reg = new RegExp(keyword, "g");
  return (
    <div className="document-detail">
      <SearchHeader
        onBack={() => history.goBack()}
        title={"资源详情"}
        renderRight={() => {
          return (
            <Input
              allowClear
              defaultValue={keyWord}
              placeholder="请输入关键词"
              maxLength={150}
              onChange={(e) => searchHighlight(e)}
              suffix={
                <Icon
                  type="search"
                  style={{ color: "rgba(213, 213, 213, 1)" }}
                />
              }
            />
          );
        }}
      />
      <div className="document-detail-content">
        <div
          className="document-detail-content-title"
          dangerouslySetInnerHTML={{
            __html: dataSource.name
              ? dataSource.name.replace(
                Reg,
                `<span style="color: red;">${keyword}</span>`
              )
              : "",
          }}
        />
        <div className="document-detail-content-info">
          <span className="document-detail-content-info-v1">
            来自：{dataSource.sources || "无"}
          </span>
          <span className="document-detail-content-info-v2">
            标签：{dataSource.tags ? dataSource.tags.replace(/,/g, "/") : "无"}
          </span>
          <span className="document-detail-content-info-v3">
            上传时间：{dataSource.create_time || "无"}
          </span>
        </div>
        {dataSource.summary && (
          <div className="document-detail-content-abs">
            <span>摘要：</span>
            <span
              dangerouslySetInnerHTML={{
                __html: dataSource.summary.replace(
                  Reg,
                  `<span style="color: red;">${keyword}</span>`
                ),
              }}
            />
          </div>
        )}
        {imgList && imgList.length !== 0 && (
          <div className="document-detail-content-carousel">
            <Carousel autoplay>
              {imgList.map((item) => {
                if (item) {
                  return (
                    <img
                      className="document-detail-content-carousel-item"
                      alt=""
                      src={`${CDN}/${item.path}`}
                    />
                  );
                }
              })}
            </Carousel>
          </div>
        )}
        <div
          className="document-detail-content-wrap"
          dangerouslySetInnerHTML={{
            __html:
              dataSource.content && keyword
                ? dataSource.content.replace(
                  Reg,
                  `<span style="color: red;">${keyword}</span>`
                )
                : dataSource.content,
          }}
        />
        <div className="document-detail-content-src">
          信息提供：{dataSource.provider_org_name}
        </div>
        {dataSource.resource_files && dataSource.resource_files.length !== 0 && (
          <div className="document-detail-content-file">
            <div className="document-detail-content-file-label">案例附件：</div>
            <ShowUploadFileType
              canPreview={true}
              data={dataSource.resource_files}
              isDelete={false}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentDetail;
