import React, { Component } from "react";
import { Icon, message } from "antd";
import Clip from "components/clip/clipping";
import { CDN } from "apis/config";

class Image extends Component {
  constructor() {
    super();
    this.state = {
      showClip: false,
      imgSrc: []
    };
    this.onClick = this._onClick.bind(this);
    this.cancel = this._cancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
  }
  _onClick() {
    this.setState({
      showClip: true,
    });
  }
  // 提交图片
  _onSubmit(base64) {
    const self = this;
    return new Promise((r, j) => {
      this.props.events
        .upload(base64)
        .then(() => {
          self.setState({
            showClip: false,
          });
          r();
        })
        .catch((e) => {
          j(e);
        });
    });
  }
  // 关闭裁剪框
  _cancel() {
    this.setState({
      showClip: false,
    });
  }

  componentWillReceiveProps(props) {
    const { src = '' } = props
    const { imgSrc } = this.state;
    if (src) {
      let itemSrc = ''
      if (src.url && !src.url.includes("http")) {
        itemSrc = `${CDN}/${src.url}`
      } else {
        itemSrc = `${src.url}`;
      }
      this.setState({
        imgSrc: [itemSrc, ...imgSrc]
      })
    }
    console.log("=======", imgSrc, src)
  }

  render() {
    const { size, src } = this.props;
    const { imgSrc } = this.state;
    const reg = /http?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/; //判断图片地址是否是http
    // var imgSrc = "";
    // var imgSrc = [];
    // src.url.includes("http")
    // if (src) {
    //   if (src.url && !src.url.includes("http")) {
    //     imgSrc = `${CDN}/${src.url}`;
    //   } else {
    //     imgSrc = `${src.url}`;
    //   }
    // }
    return (
      <div className="image">
        {JSON.stringify(imgSrc)}
        {src && src.url ? (
          <div style={{ display: 'inline-block' }}>
            <div className="text" onClick={this.onClick}>
              <Icon type="plus" />
              <div>点击上传封面</div>
            </div>
            {imgSrc.map((item, index) => {
              return (
                <span className="imgBox">
                  <img src={item} alt="" className="view" />
                  <Icon type="close-circle" className="icon" style={{ color: "red" }} onClick={() => {
                    this.setState({
                      imgSrc: imgSrc.splice(index+1, 1)
                    })
                  }} />
                  {console.log(imgSrc)}
                </span>
              )
            })}
          </div>
        ) : (
          <div className="text" onClick={this.onClick}>
            <Icon type="plus" />
            <div>点击上传封面</div>
          </div>
        )}
        {/* {this.state.showClip ? (
          <Clip
            title={"资源封面"}
            src={imgSrc}
            size={size}
            onCancel={this.cancel}
            onSubmit={this.onSubmit}
          />
        ) : null} */}
      </div>
    );
  }
}

export default Image;
