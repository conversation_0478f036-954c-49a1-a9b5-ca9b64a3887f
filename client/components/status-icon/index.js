// 状态标志组件
import React from "react";
import "./index.less";
import propTypes from "prop-types";

const StatusIcon = (props) => {
  const { text, direction, type, color, textColor} = props;

  //父span标签样式
  let ps = {
    color: textColor
  };
  //子b标签样式
  let cs = {};


  if(color){
    ps['backgroundColor'] = color;

    if(direction == 'left'){
      cs['borderRightColor'] = color;
    }else{
      cs['borderLeftColor'] = color;
    }
  }

  return (
    <span className={direction && `status-icon ${direction} ${type}`} style={ps}>
      {text}<b style={cs} className="r"></b>
    </span>
  )
}

StatusIcon.propTypes = {
  text: propTypes.string,
  direction: propTypes.oneOf(["left", "right"]),
  type: propTypes.oneOf(["default", "success", "info", "error", "warning", "disable"]),
  color: propTypes.string,
  textColor: propTypes.string
}

StatusIcon.defaultProps = {
  text: "文字",
  direction: "right",
  type: "default",
  color: "",
  textColor: "#fff"
}

export default StatusIcon;
