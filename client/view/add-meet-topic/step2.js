import React from "react";
import OrganizeSelector from "components/organize-selector";
import { Form, Input, Button, DatePicker, message } from "antd";
import moment from "moment";

const FormItem = Form.Item;
const TextArea = Input.TextArea;

class Step2 extends React.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    setTimeout(() => {
      const { state } = this.props;
      this.child && this.child.batchAddOrgs(state.orgs);
      this.loginOrg && this.loginOrg.batchAddOrgs(state.orgs);
    }, 0);
  }

  render() {
    const {
      formItemLayout, // 布局参数
      prev, state, form, // 
      updateState,
      addATopic, // 新增组织
      updateATopic, // 修改
    } = this.props;
    const { getFieldDecorator, validateFields } = form;
    const addTopic = () => {
      validateFields((err, values) => {
        if (!err) {
          if (state.orgs.length <= 0 && state.status !== 2) {
            return message.error("执行组织必选")
          } else {
            // 新增议题得参数
            const queryData = {
              name: state.name, // 议题名称
              description: state.description, // 议题描述
              files: state.files, // 附件
              contents: state.contents, // 内容
              start_time: state.start_time, // 开始时间
              end_time: state.end_time, // 结束时间
              orgs: state.orgs, // 组织 id 数组
              status: state.status, // 上级还是下级提交
            }
            // console.log(queryData);
            if (state.isAdd) {
              // 新增议题
              addATopic(queryData)
            } else {
              // 修改原有议题
              updateATopic(Object.assign({}, queryData, { topic_id: state.topic_id }))
            }
          }
        }
      })
    }
    return (
      <div>
        <Form>
          <FormItem
            label="开始时间"
            {...formItemLayout}
          >
            {getFieldDecorator("start_time", {
              initialValue: state.start_time === "" || state.start_time === undefined ? null : moment(state.start_time, "YYYY-MM-DD"),
              rules: [{ required: true, message: "开始时间必填" }]
            })(
              <DatePicker
                onChange={(date) => updateState({ start_time: moment(date).format("YYYY-MM-DD") })}
              />
            )}
          </FormItem>
          <FormItem
            label="结束时间"
            {...formItemLayout}
          >
            {getFieldDecorator("end_time", {
              initialValue: state.end_time === "" || state.end_time === undefined ? null : moment(state.end_time, "YYYY-MM-DD"),
              rules: [{ required: true, message: "结束时间必填" }]
            })(
              <DatePicker
                onChange={(date) => updateState({ end_time: moment(date).format("YYYY-MM-DD") })}
              />
            )}
          </FormItem>
          <FormItem
            label="执行组织"
            {...formItemLayout}
            className="no-margin"
          >
            {
              (state && state.status === 1) &&
              <OrganizeSelector
                canEdit={state && state.status === 1}
                getSelectedValues={(data) => updateState({ orgs: data })}
                ref={ref => this.child = ref}
              />
            }
            {
              (state && state.status === 2) &&
              <OrganizeSelector
                canEdit={false}
                ref={ref => this.loginOrg = ref}
              />
            }

          </FormItem>
          <FormItem
            wrapperCol={{ span: 20, offset: 2 }}
          >
            <Button
              className="btnWrapper"
              onClick={() => prev()}
            >
              上一步
            </Button>
            <Button
              type="primary"
              className="btnWrapper"
              style={{ marginLeft: 28 }}
              onClick={addTopic}
              loading={state.submitLoading}
            >
              提交
            </Button>
          </FormItem>
        </Form>
      </div>
    )
  }
}

export default Step2;