// 融合商城商品分类组件
import React, { Component } from "react";
import { Form, Row, Col, Checkbox, message } from "antd";
import { fetchMixmallCommodityClassification } from "apis/integral-exchange";
import "./index.less";

class CommodityClassification extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 图书根目录
      classification: [],
      // 二级目录
      level2Options: [],
      level2CheckAll: [],
      // 三级目录
      level3Options: {},
      level3CheckAll: []
    }
    this.fetchMixmallCommodityClassification = this.fetchMixmallCommodityClassification.bind(this);
    this.handleClassification = this.handleClassification.bind(this);
    this.resetCommodityClassification = this.resetCommodityClassification.bind(this);
    this.getCommodityClassification = this.getCommodityClassification.bind(this);
  }

  componentDidMount() {
    // 获取融合商城商品分类
    this.fetchMixmallCommodityClassification();
  }

  // 重置商品类型选择
  resetCommodityClassification() {
    const { form } = this.props;
    // console.log("重置商品分类", form);
    const { resetFields } = form;
    resetFields();
  }

  // 获取商品类型选择ID值数组
  getCommodityClassification() {
    let level2 = [], level3 = [];
    const { form } = this.props;
    const { getFieldsValue } = form;
    const result = getFieldsValue();
    if (result) {
      const { level2Checked, level3Checked } = result;
      if (level2Checked && Array.isArray(level2Checked) && level2Checked.length !== 0) {
        level2 = level2.concat(level2Checked);
        if (level3Checked) {
          Object.keys(level3Checked).forEach((key) => {
            if (level3Checked[key] && Array.isArray(level3Checked[key]) && level3Checked[key].length !== 0) {
              level3 = level3.concat(level3Checked[key]);
            }
          })
        }
      }
    }
    // 输出之前对选择类别进行去重处理\
    level2 = Array.from(new Set(level2));
    level3 = Array.from(new Set(level3));
    return {
      level2,
      level3
    };
  }

  // 处理某个类别的二级类别和三级类别
  handleClassification(catagory) {
    // console.log("获取类别列表为", catagory);
    // return;
    let level2Options = [], level3Options = {},
      level2CheckAll = [], level3CheckAll = {};
    catagory.forEach((item) => {
      level2Options.push({
        label: item.name,
        value: item.id
      });
      level2CheckAll.push(item.id);
      if (!level3Options[item.id]) {
        level3Options[item.id] = [];
      }
      if (!level3CheckAll[item.id]) {
        level3CheckAll[item.id] = [];
      }
      if (item.two) {
        const childNode = item.two;
        if (Array.isArray(childNode) && childNode.length !== 0) {
          childNode.forEach((child) => {
            level3Options[item.id].push({
              label: child.name,
              value: child.id
            });
            level3CheckAll[item.id].push(child.id);
          })
        }
      }
    })
    this.setState({
      level2Options,
      level2CheckAll,
      level3Options,
      level3CheckAll
    })
    // console.log("二级目录", level2Options);
    // console.log("三级目录", level3Options);
  }

  // 获取商品分类列表
  async fetchMixmallCommodityClassification(params) {
    const _this = this;
    const response = await fetchMixmallCommodityClassification(params);
    // 商品分类中仅取出图书类
    // console.log(response);
    const { data: body } = response;
    const { code, data, message: msg } = body;
    if (code !== 0) {
      message.error(msg);
      return;
    }
    if (data && Array.isArray(data) && data.length !== 0) {
      this.setState({
        classification: data || []
      });
      this.handleClassification(data || []);
      // data.forEach((item) => {
      //   // 精准查找到分类中配置的图书类别
      //   if (item.name === "图书") {
      //     _this.setState({
      //       classification: item.childNode || []
      //     });
      //     _this.handleClassification(item.childNode || []);
      //   }
      // });
    }
  }

  render() {
    const { form } = this.props;
    const CheckboxGroup = Checkbox.Group,
      FormItem = Form.Item;
    const { getFieldDecorator, getFieldValue } = form;
    const { classification, level2Options, level3Options, level3CheckAll } = this.state;
    return (
      <div className="commodity-classification-container">
        {
          (level2Options && Array.isArray(level2Options) && level2Options.length !== 0) &&
          <Form>
            <FormItem label="商品分类" labelCol={{ span: 4 }}>
              <Row type="flex">
                <Col span={24} className="sub-item-container second-level">
                  {
                    getFieldDecorator(
                      "level2Checked",
                      {
                        initialValue: []
                      }
                    )(
                      <CheckboxGroup options={level2Options} />
                    )
                  }
                </Col>
                {
                  Object.keys(level3Options).map((key, index) => {
                    if (getFieldValue("level2Checked").indexOf(Number(key)) !== -1) {
                      // if (!level3Options[key] || !Array.isArray(level3Options[key]) || level3Options[key].length === 0) {
                      //   return null;
                      // }
                      return (
                        <Col span={24} className="sub-item-container" key={index}>
                          <h4>
                            {
                              (level2Options && Array.isArray(level2Options))
                              && level2Options.map((item) => {
                                if (item.value == key) {
                                  return item.label;
                                }
                                return null;
                              })
                            }
                          </h4>
                          {
                            getFieldDecorator(
                              `level3Checked.sub${key}`,
                              {
                                initialValue: level3CheckAll[key]
                              }
                            )(
                              <CheckboxGroup options={level3Options[key]} />
                            )
                          }
                        </Col>
                      )
                    }
                    return null;
                  })
                }
              </Row>
            </FormItem>
          </Form>
        }
      </div>
    )
  }
}

export default Form.create()(CommodityClassification);