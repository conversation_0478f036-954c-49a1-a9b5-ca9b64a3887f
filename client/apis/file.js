import { fileHost, uploadHost } from "./config";
import { http, headers } from "client/tool/axios";
import { staticHttp } from "client/tool/axios-now"

// 仅正式环境可用
// export const UPLOAD_URL = "https://owssaas.goodsogood.com/owsz/file"
export const UPLOAD_URL = fileHost

/**
 * 上传文件
 * @param {object} upfile
 */
export const uploadFile = (params, queryHeader) => {
  // return http.post(`https://owss.cqjgdj.gov.cn/owsz/file/file/upload`, params, queryHeader || headers());
  return http.post(`${uploadHost}/file/upload`, params, queryHeader || headers());
};
// base64 上传文件
export const uploadBase64File = (params, queryHeader) => {
  return staticHttp.post(`${uploadHost}/file/upload/base64`, params, queryHeader || headers());
};
export const uploadVideoFile = (params, queryHeader) => {
  return staticHttp.post(`${uploadHost}/file/upload/video`, params, queryHeader || headers());
};

export const uploadVideoResult = (params) => {
  return http.get(`${uploadHost}/file/upload/video/result?task_id=${params}`);
};
// 文件预览
export const postFilePreview = (params = {}) => {
  return http.post(`${fileHost}/file/preview`, params);
}
