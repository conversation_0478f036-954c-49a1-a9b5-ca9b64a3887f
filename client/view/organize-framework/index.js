/**
 * 组织结构
 * 2018/5/21
 */
import {
  <PERSON><PERSON>,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Select
} from "antd";
import { getFormConfigList } from "apis/dynamic-form";
import {
  addOrgNew,
  addOrgTree,
  deleteOrg,
  deleteOrgTree,
  findByName,
  findByUser,
  getAllArea,
  getOrgInfo,
  getOrgTotalCount,
  getOrgTree,
  getTreeDetail,
  getTreeList,
  locateOrgTree,
  sortOrg,
  updateOrgNew,
  updateOrgTree,
} from "apis/organize";
import { getCodeList } from "client/apis/voluntary-group-management";
import DynamicForm from "client/components/dynamic-form";
import OrgSelect from "client/components/organize-selector/index";
import { connect } from "dva";
import React from "react";
import PositionModal from "./components/position-modal";
import IndexView from "./index-view";
import TreeManagerModal from "./tree-manager-modal";

// new
import {
  addOrg,
  deleteOrgStatus,
  getLeaderCadreUsers,
  getNewOrgInfo,
  getOpList,
  getOrgNameList,
  queryOrgGroupList,
  updateOrgRenovate
} from "client/apis/cadre-portrait";

import "./index.less";

const { Option } = Select;
const trim = (str) => {
  if (!str) return "";
  return str.replace(/^(\s*)|(\s*)$/g, "");
};

const getSearchData = async (payload = {}, searchType = 1) => {
  let result;
  if (searchType == 1) {
    result = await findByUser(payload);
  } else {
    result = await findByName(payload);
  }

  return result.data;
};

const tabAdd = [
  {
    tree_name: "+ 新建组织树",
  },
];

class OrganizeFramework extends React.Component {
  constructor() {
    super();

    this.scrollSearchTimeout = null;
    this.state = {
      parentOrgId: null,
      selectOrgId: null,
      autoExpandParent: true,
      organizeModalStatus: 1,
      organizeModalTitle: "新建组织",

      isSiderLoading: false,
      isSiderSubmitLoading: false,

      ownerDeparts: [],
      siderDeparts: [],
      allDeparts: [], //树数组

      initDepartmentValue: [],
      selectedDepartmentValue: ["-1"],
      currentDepartmentValue: "-1",
      ownerDepartmentValue: ["-1"],

      isLoading: false,
      orgTreeTabList: [].concat(tabAdd),
      orgTreeTabIndex: 0,
      orgTreeTabCurData: null,
      orgTreeTabSelectData: null,
      statisData: {
        organizeCount: 0,
        personCount: 0,
      },

      isTreeModalVisible: false,
      isTreeModalMode: false,
      isTreeModalLoading: false,
      isTreeModalSubmitLoading: false,
      isTreeModalDeleteLoading: false,
      isTreePanelExist: false,

      isSearchLoading: false,
      isSearchListLoading: false,
      isSearchModal: false,
      searchType: 2,
      oldSearchType: null,
      searchTableType: 1,
      searchList: [],

      searchListPageNum: 1,
      searchListPageTotal: 1,

      organizeTypeData: [],
      orgStatus: false,
      ownerStatus: false,
      isRootId: false,
      // new
      institutionSort: [], //机构类别
      institutionClassify: [], //机构分类
      institutionLevel: [], //机构层次
      institutionNature: [], //机构性质
      institutionLead: [], //机构领导列表
      newControl: true, // 新建组织控制
      parentOrgList: [], // 上级机构列表
      orgDatum: {
        org_code: "",
        name: "",
        short_name: "",
        parent_id: "",
        parent_name: "",
        org_unique_code: "",
        institution_category: "",
        institution_categoryName: "",
        institution_classification: "",
        institution_classifyName: "",
        institution_level: "",
        institution_levelName: "",
        institution_nature: "",
        institution_natureName: "",
        has_divided: "",
        real_leader_num: null,
        vice_leader_num: null,
        charge_leader_name: "",
        org_type: "",
        regular_team: "",
        group_id: "",
      }, //组织资料/新建

      /** ---------用户中心4.0-动态表单相关------------------ **/
      drawerVisible: false,
      drawerTitle: "新建组织",
      drawerLoading: false,
      extraAttr: {},
      fieldList: [], // 动态表单数据
      edit: false, // 是否编辑信息， false为非编辑（新增）, 编辑则为当前的org_id
      orgDetails: {}, // 编辑时存放的组织信息
      /** ---------用户中心4.0-动态表单相关------------------ **/
      orgGroupdata: [], // 组织分组列表
    };
    this.userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
    this.permissionList = this.userInfo.element.map((item) => item.element_id);
    this.onSubmit = this.onSubmit.bind(this);
    this.onDelOrg = this.onDelOrg.bind(this);
    this.getOrgDetail = this.getOrgDetail.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.onSearch = this.onSearch.bind(this);
    this.rootOrgId =
      typeof window !== "undefined" &&
      window.sessionStorage.getItem("_root_oid");
    //
    this.positionModalRef = React.createRef();
    this.organizeSelector = React.createRef();
  }
  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: "organizeFramework/updateState",
      payload: {
        searchResultVisible: false,
        isSearchResultLoading: false,
      },
    });
  }
  async componentDidMount() {
    const { dispatch, userInfo, organizeFramework } = this.props;
    const orgId = userInfo.oid;
    dispatch({
      type: "organizeFramework/updateState",
      payload: {
        currentOrgId: orgId,
      },
    });
    await this.getTreeList({ orgId, tabIndex: organizeFramework.tabIndex });
    await dispatch({ type: "organizeFramework/loadArea" });
    await dispatch({ type: "organizeFramework/loadOrgType" });
    await dispatch({ type: "organizeFramework/loadIndustry" });
    this.getSort();
    this.getClassify();
    this.getLevel();
    this.getNature();
    this.getLeader();
    this.orgGroupList()
    this.getParentOrgList()
  }
  async orgGroupList() {
    const res = await queryOrgGroupList();

    if (res.data.code === 0) {
      this.setState({
        orgGroupdata: res.data.data
      })
    }
  };
  // 获取领导干部列表
  async getLeader() {
    const params = {
      user_name: undefined,
    };
    const { data } = await getLeaderCadreUsers(params);
    const list =
      data.data &&
      data.data.map((item, index) => {
        return {
          value: item.user_id,
          text: item.user_name,
        };
      });
    if (data.code == 0) {
      this.setState({
        institutionLead: list,
      });
    }
  }
  async getSort() {
    const params = {
      code: 102901,
    };
    const { data } = await getOpList(params);
    if (data.code == 0) {
      this.setState({
        institutionSort: data.data,
      });
    }
  }
  async getClassify() {
    const params = {
      code: 102902,
    };
    const { data } = await getOpList(params);
    if (data.code == 0) {
      this.setState({
        institutionClassify: data.data,
      });
    }
  }
  async getLevel() {
    const params = {
      code: 102903,
    };
    const { data } = await getOpList(params);
    if (data.code == 0) {
      this.setState({
        institutionLevel: data.data,
      });
    }
  }
  async getNature() {
    const params = {
      code: 102904,
    };
    const { data } = await getOpList(params);
    if (data.code == 0) {
      this.setState({
        institutionNature: data.data,
      });
    }
  }
  async ensureDeleteOrg() {
    const params = {
      org_id: this.state.orgDatum.organization_id,
    };
    const { data } = await deleteOrgStatus(params);
    if (data.code == 0) {
      const { userInfo, organizeFramework } = this.props;
      const orgId = userInfo.oid;
      message.success("删除成功");
      this.setState(
        {
          drawerVisible: false,
          orgDatum: {},
        },
        () => {
          this.getTreeList({ orgId, tabIndex: organizeFramework.tabIndex });
        }
      );
    } else {
      message.error(data.message);
    }
  }
  async getParentOrgList() {
    try {
      const params = { org_id: 0, name: '' };
      const { data } = await getOrgNameList(params);
      if (data.code === 0) {
        this.setState({
          parentOrgList: data.data || [],
        });
      } else {
        message.error(data.message || "获取上级机构列表失败");
      }
    } catch (error) {
      message.error("获取上级机构列表失败，请重试");
    }
  }

  async newAdd() {
    const { validateFields, resetFields } = this.props.form;
    validateFields(async (error, value) => {
      const { organization_id } = this.state.orgDatum; // 当前组织的 organization_id
      if (!error) {
        // 如果是编辑状态，使用表单中选择的 parent_name
        // 如果是新增状态，使用原始的 parent_id
        const parent_id = organization_id ? value.parent_name : this.state.orgDatum.parent_id;
        const params = {
          ...value,
          parent_id: parent_id, // 从表单中获取的上级机构的 organization_id
          real_leader_num: this.state.orgDatum.real_leader_num,
          vice_leader_num: this.state.orgDatum.vice_leader_num,
          org_type: this.state.orgDatum.org_type,
          organization_id // 当前组织的 organization_id
        };
        let res;
        if (organization_id) {
          res = await updateOrgRenovate(params);
        } else {
          res = await addOrg(params);
        }
        const { data } = res;
        if (data.code == 0) {
          message.success(organization_id ? "编辑成功" : "新增成功");
          this.setState({
            drawerVisible: false,
            orgDatum: {},
          });
          // 刷新页面数据
          const { userInfo, organizeFramework } = this.props;
          const orgId = userInfo.oid;
          this.getTreeList({ orgId, tabIndex: organizeFramework.tabIndex });
        } else {
          message.error(data.message);
        }
      }
    });
  }
  async onSelect(value) {
    console.log(value, "模糊搜索");
  }
  async onSearch(value) {
    const { data } = await getLeaderCadreUsers({ user_name: value });
    const list =
      data.data &&
      data.data.map((item, index) => {
        return {
          value: item.user_id,
          text: item.user_name,
        };
      });
    if (data.code == 0) {
      this.setState({
        institutionLead: list,
      });
    }
  }
  // 获取表单配置
  getFormConfigList() {
    const { orgTreeTabSelectData } = this.state;
    const { org_type } = orgTreeTabSelectData;
    getFormConfigList({ own: 2, type: 1, org_type }).then((res) => {
      const { status, code, data } = res.data;
      if (status === 200 && code === 0) {
        data.map((item) => {
          const { type, attr } = item;
          if (type === 8) {
            item.attr = { ...attr, org_type };
          }
        });
        this.setState({ fieldList: DynamicForm.formatFieldDefaultValue(data) });
      }
    });
  }

  async getTreeList(payload = {}) {
    console.log("1👙");
    const { dispatch, userInfo } = this.props;
    const { orgId, tabIndex, isOpt, isOptTree } = payload;
    const currentIndex = tabIndex || 0;

    let data;
    let isTreePanelExist = false;
    if (!isOpt) {
      this.setState({
        isLoading: true,
      });

      const result = (await getTreeList(orgId)).data;
      this.setState({
        isLoading: false,
      });

      if (result.code !== 0) {
        return message.error(result.message);
      }
      data = result.data.sort((a, b) => {
        if (!a.tree_type || !b.tree_type) {
          return 0;
        }
        return a.tree_type - b.tree_type;
      });
      if (userInfo.user_id === 1 && userInfo.oid === 1) {
        data = data.concat(tabAdd);
      }
      data.forEach((item) => {
        if (item.tree_type == 1) {
          isTreePanelExist = true;
        }
      });
    } else {
      data = this.state.orgTreeTabList;
      isTreePanelExist = this.state.isTreePanelExist;
    }

    const curData = data[currentIndex];
    if (curData) {
      if (!isOptTree) {
        const params = {};
        if (curData.tree_type == 2) {
          params["orgType"] = curData.org_type;
        }
        this.loadOrgTree(
          Object.assign(
            {
              orgId,
              treeType: curData.tree_type,
            },
            params
          )
        );
      }

      this.setState(
        {
          isTreePanelExist,
          orgTreeTabList: data,
          orgTreeTabCurData: curData,
          orgTreeTabSelectData: curData,
          orgTreeTabIndex: currentIndex,
        },
        () => {
          this.getFormConfigList();
        }
      );

      dispatch({
        type: "organizeFramework/updateState",
        payload: {
          tabIndex: currentIndex,
        },
      });
    }
  }

  async loadOrgTree(payload = {}) {
    console.log("2👙");
    const { orgId, treeType, orgType, target, isCloseExpand } = payload;
    console.log(orgType, "orgType");
    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const { userInfo } = this.props;
    const { oid } = userInfo;
    this.setState({
      isLoading: true,
    });

    let ttype = treeType;
    let torgType = orgType;
    if (!treeType) {
      ttype = orgTreeTabSelectData.tree_type;
    }
    if (!orgType && orgTreeTabSelectData) {
      torgType = orgTreeTabSelectData.org_type;
    }

    let p = {
      ["load_root"]: 1,
    };
    if (ttype == 2) {
      p["org_type"] = torgType;
    }
    await this.loadOrgCount({ orgId: oid, treeType: ttype, orgType: torgType });

    let result;
    if (orgId === oid) {
      console.log(p, "p1");
      result = (
        await getOrgTree(
          Object.assign(
            {
              org_id: orgId,
              tree_type: ttype,
              show_code: 1,
            },
            p
          )
        )
      ).data;
    } else {
      console.log(p, "p2");
      result = (
        await locateOrgTree(
          Object.assign(
            {
              root_org_id: oid,
              org_id: orgId,
              tree_type: ttype,
              show_code: 1,
            },
            p
          )
        )
      ).data;
    }
    console.log(result, "result");
    if (result.code != 0) {
      this.setState({
        allDeparts: [],
        statisData: {
          organizeCount: 0,
          personCount: 0,
        },
        isLoading: false,
      });

      return message.error(result.message);
    }

    let data = result.data;

    let initValue = [];
    if (data && data[0]) {
      initValue.push(data[0].org_id + "");
    }

    const initDepartmentValue = this.state.initDepartmentValue;
    if (orgId !== oid && !isCloseExpand) {
      initValue.push(orgId + "");
    }
    if (initDepartmentValue && initDepartmentValue.length) {
      initValue = Array.from(new Set(initValue.concat(initDepartmentValue)));
    }

    this.setState(
      {
        isLoading: false,
        allDeparts: data,
        initDepartmentValue: initValue || [],
        autoExpandParent: true,
      },
      target
        ? () => {
          if (this.scrollSearchTimeout) {
            clearTimeout(this.scrollSearchTimeout);
            this.scrollSearchTimeout = null;
          }

          const getOrgId = [orgId + ""];
          const currentDepartmentValue = this.state.currentDepartmentValue;
          const filterVal = initDepartmentValue.filter((val) => {
            return currentDepartmentValue != val;
          });
          const initVal = Array.from(new Set(getOrgId.concat(filterVal)));

          this.setState({
            currentDepartmentValue: getOrgId[0],
            initDepartmentValue: initVal,
          });
          this.scrollSearchTimeout = setTimeout(() => {
            document
              .querySelector(`.treeItem-${orgId}`)
              .scrollIntoView({ block: "center", behavior: "smooth" });
          }, 600);
        }
        : null
    );
  }

  async loadOrgCount(payload = {}) {
    const { orgId, treeType, orgType } = payload;
    const isRootId = orgId == this.rootOrgId;
    const p = {};
    if (treeType == 2) {
      p["org_type"] = orgType;
    }

    const result = (
      await getOrgTotalCount(
        Object.assign(
          {
            org_id: orgId,
            tree_type: treeType,
          },
          p
        )
      )
    ).data;

    const data = result.data;
    if (result.code != 0 && !data) {
      this.setState({
        isRootId,
        isInitLoading: false,
        statisData: {
          organizeTotal: 0,
          basicOrgCount: 0,
          manageOrgCount: 0,
          orgUserTotal: 0,
          partyMemberCount: 0,
          manageUserCount: 0,
        },
      });
      return message.error(result.message);
    }

    const isShizhi =
      JSON.parse(sessionStorage.getItem("userInfo") || "{}").regionId === 3;
    const _orgType = sessionStorage.getItem("_org_type") === "102803";
    const isAdd = isShizhi && isRootId && _orgType;

    this.setState({
      isShizhi,
      isRootId,
      statisData: {
        organizeTotal: isAdd ? data.org_total + 152 : data.org_total,
        basicOrgCount: isAdd
          ? data.basic_org_total + 152
          : data.basic_org_total,
        manageOrgCount: isAdd
          ? data.manage_org_total + 152
          : data.manage_org_total,
        orgUserTotal: isAdd ? data.org_user_total + 2196 : data.org_user_total,
        partyMemberCount: isAdd
          ? data.party_member_total + 2196
          : data.party_member_total,
        manageUserCount: isAdd
          ? data.manage_user_total + 2196
          : data.manage_user_total,
      },
    });
  }

  async loadReverseOrgTree(payload = {}) {
    const { userInfo, dispatch } = this.props;
    let orgId = payload.orgId;
    const oid = userInfo.oid;

    if (!orgId) {
      orgId = oid;
    }

    const p = {};
    if (payload.treeType == 2) {
      p["org_type"] = payload.orgType;
    }
    if (payload.isFilter) {
      p["is_filter"] = payload.isFilter;
    }

    let result;
    result = (
      await locateOrgTree(
        Object.assign(
          {},
          {
            root_org_id: oid,
            org_id: orgId,
            tree_type: payload.treeType,
            load_root: 1,
          },
          p
        )
      )
    ).data;
    // }

    if (result.code != 0) {
      this.setState({
        isSiderLoading: false,
      });
      return message.error(result.message);
    }

    const data = result.data;
    let paramName;
    let paramSelectName;

    if (payload.type == 1) {
      paramName = "siderDeparts";
      paramSelectName = "selectedDepartmentValue";
    } else {
      paramName = "ownerDeparts";
      paramSelectName = "ownerDepartmentValue";
    }

    let params = { [paramName]: data };
    this.setState(params);

    if (payload.value) {
      let ps;
      if (payload.type === 1) {
        ps = { organizeParent: payload.value };
      } else {
        ps = { ownerId: payload.value };
      }

      if (ps) {
        dispatch({
          type: "organizeFramework/updateFormFields",
          params: ps,
        });
      }
    }

    if (paramSelectName) {
      let openValue = payload.value
        ? [payload.value + ""]
        : paramName == "ownerDeparts"
          ? undefined
          : [data[0].org_id + ""];
      this.setState({
        [paramSelectName]: openValue,
      });
    }
  }

  async loadOrgInfo(payload = {}) {
    const { orgId, level, isCreateNext } = payload;
    const { dispatch, userInfo } = this.props;
    this.setState({
      isSiderLoading: true,
    });

    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const result = (
      await getOrgInfo({
        org_id: orgId,
      })
    ).data;

    if (result.code != 0) {
      this.setState({
        isSiderLoading: false,
      });
      return message.error(result.message);
    }

    const oid = userInfo.oid;

    if (!isCreateNext) {
      let organizeType;
      if (result.data.org_type) {
        await dispatch({
          type: "organizeFramework/loadSecondOrgType",
          payload: {
            code: result.data.org_type,
          },
        });
        organizeType = [result.data.org_type];
        if (result.data.org_type_child) {
          organizeType.push(result.data.org_type_child);
        }
      }

      let area = null;
      if (result.data.org_area) {
        // 加载列表数据
        const allResult = (await getAllArea({ adcode: result.data.org_area }))
          .data;
        // 如果请求错误
        if (allResult.code !== 0) return message.error(allResult.message);
        area = allResult.data.parents.map((value) => {
          return value.adcode;
        });

        if (area && area[0]) {
          await dispatch({
            type: "organizeFramework/loadArea",
            payload: {
              pid: area[0],
              level: 1,
              nextId: area[1],
            },
          });
        }
      }
      // let id;
      // if (level == 0) {
      //     id = oid;
      // } else {
      //     id = result.data.parent_id;
      // }
      await this.loadReverseOrgTree({
        orgId: result.data.parent_id || oid,
        treeType: orgTreeTabSelectData.tree_type,
        orgType: orgTreeTabSelectData.org_type,
        type: 1,
        value: result.data.parent_id || oid,
        level,
      });

      if (orgTreeTabSelectData.tree_type === 2) {
        await this.loadReverseOrgTree({
          orgId: result.data.owner_id || oid,
          treeType: 1,
          type: 2,
          isFilter: 1,
          value: result.data.owner_id,
        });
      }

      const thirdDataInfo =
        result.data.third_data_info && JSON.parse(result.data.third_data_info);
      dispatch({
        type: "organizeFramework/updateFormFields",
        params: {
          organizeType,
          industry: result.data.industry_type,
          ownerId: result.data.owner_id,
          organizeCode: result.data.org_code,
          organizeName: result.data.name,
          area,
          manager: result.data.org_leader,
          magagerTel: result.data.org_leader_phone,
          organizeLinkman: result.data.org_contacts,
          organizeTel: result.data.org_phone,
          postcode: result.data.postcode,
          address: result.data.org_address,
          isRetire: result.data.is_retire || 2,
          thirdDataInfo: thirdDataInfo,
        },
      });
    } else {
      if (orgTreeTabSelectData.tree_type === 2) {
        dispatch({
          type: "organizeFramework/updateFormFields",
          params: {
            organizeType: [orgTreeTabSelectData.org_type],
          },
        });
        await this.loadReverseOrgTree({
          orgId: result.data.owner_id || oid,
          treeType: 1,
          type: 2,
          isFilter: 1,
        });
      }
      await this.loadReverseOrgTree({
        orgId: orgId,
        treeType: orgTreeTabSelectData.tree_type,
        orgType: orgTreeTabSelectData.org_type,
        type: 1,
        value: orgId,
      });
    }
    this.setState({
      isSiderLoading: false,
    });
  }
  // 动态表单提交
  onSubmit(value) {
    let params = { ...value };
    const { edit, orgTreeTabSelectData, orgDetails } = this.state;
    if (edit) {
      params.organization_id = edit;
      params = { ...orgDetails, ...params };
    }
    params.org_type = orgTreeTabSelectData.org_type;
    params.owner_tree = orgTreeTabSelectData.tree_type;
    params.type = 1;
    const { service_list = [] } = params;
    if (service_list.length > 3) {
      message.warning("服务清单不能超过3个");
      return;
    }
    this.setState({ drawerLoading: true });
    this.submitOrg(params).then((res) => {
      if (res) {
        message.error(res);
      } else {
        message.success("操作成功！");
      }
      this.setState({ drawerLoading: false, drawerVisible: false });
    });
  }
  //新建修改组织
  async submitOrg(payload = {}) {
    console.log("3👙");
    const { organization_id } = payload;
    const { userInfo, organizeFramework } = this.props;

    this.setState({
      isSiderSubmitLoading: true,
    });

    let result;
    if (organization_id !== undefined) {
      result = (await updateOrgNew(payload)).data;
    } else {
      result = (await addOrgNew(payload)).data;
    }
    // 如果请求错误
    if (result.code !== 0) {
      this.setState({
        isSiderSubmitLoading: false,
      });
      return result.message;
    }

    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const parentOrgId = this.state.parentOrgId;
    const oid = userInfo.oid;
    const p = {
      orgId: organization_id || parentOrgId || oid,
      treeType: orgTreeTabSelectData.tree_type,
    };
    if (p.treeType === 2) {
      p["orgType"] = orgTreeTabSelectData.org_type;
    }
    this.loadOrgTree(p);
    this.setState({
      isSiderSubmitLoading: false,
    });

    const isSearchModal = this.state.isSearchModal;
    const formFields = organizeFramework.formFields.form3;

    if (isSearchModal) {
      this.getSearchList({ value: formFields.searchKey[1] });
    }
  }
  onDelOrg() {
    const { orgDetails } = this.state;
    Modal.confirm({
      title: `确定删除当前组织`,
      content: "删除后不可恢复",
      okText: "确定",
      okType: "danger",
      cancelText: "取消",
      onOk: () => {
        this.setState({ drawerLoading: true });
        deleteOrg({ org_id: orgDetails.organization_id })
          .then((res) => {
            const { code, status, message: msg } = res.data;
            if (code === 0 && status === 200) {
              message.success("操作成功！");
              this.setState({ drawerVisible: false });
              const { userInfo, organizeFramework } = this.props;
              const orgId = userInfo.oid;
              this.getTreeList({ orgId, tabIndex: organizeFramework.tabIndex });
            } else {
              message.error(msg);
            }
          })
          .finally(() => {
            this.setState({ drawerLoading: false });
          });
      },
    });
  }

  async deleteOrg(payload = {}) {
    console.log("4👙");
    const { orgId } = payload;
    const { userInfo, organizeFramework } = this.props;
    this.setState({
      isSiderSubmitLoading: true,
    });

    const result = (
      await deleteOrg({
        org_id: orgId,
      })
    ).data;

    if (result.code != 0) {
      this.setState({
        isSiderSubmitLoading: false,
      });
      return result.message;
    }

    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const parentOrgId = this.state.parentOrgId;
    const oid = userInfo.oid;
    const p = {
      orgId: parentOrgId || oid,
      treeType: orgTreeTabSelectData.tree_type,
    };
    if (orgTreeTabSelectData.org_type) {
      p["orgType"] = orgTreeTabSelectData.org_type;
    }

    this.loadOrgTree(p);
    this.setState({
      isSiderSubmitLoading: false,
    });

    const isSearchModal = this.state.isSearchModal;
    const formFields = organizeFramework.formFields.form3;

    if (isSearchModal) {
      this.getSearchList({
        value: formFields.searchKey[1],
      });
    }
  }

  //定位组织
  async locateOrg(payload = {}) {
    console.log("5👙");
    const { orgId, target } = payload;
    this.setState({
      isSearchModal: false,
    });
    this.loadOrgTree({ orgId, target });
  }
  //排序组织
  async sortOrg(payload = {}) {
    console.log("6👙");
    const { orgId, orgSeq, isNext, desOrgId, desOrgSeq } = payload;

    this.setState({
      isLoading: true,
    });

    let params = {};
    if (isNext) {
      params["desc_seq"] = orgSeq;
      params["desc_oid"] = orgId;
      params["asc_seq"] = desOrgSeq;
      params["asc_oid"] = desOrgId;
    } else {
      params["asc_seq"] = orgSeq;
      params["asc_oid"] = orgId;
      params["desc_seq"] = desOrgSeq;
      params["desc_oid"] = desOrgId;
    }

    const result = (await sortOrg(params)).data;

    if (result.code != 0) {
      this.setState({ isLoading: false });
      return message.error(result.message);
    }

    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const p = {
      orgId: orgId,
      treeType: orgTreeTabSelectData.tree_type,
      isCloseExpand: true,
    };
    if (orgTreeTabSelectData.org_type) {
      p["orgType"] = orgTreeTabSelectData.org_type;
    }
    this.loadOrgTree(p);
  }

  async getSearchList(payload = {}) {
    const { value, page } = payload;
    const { userInfo, dispatch } = this.props;
    const searchType = this.state.searchType;
    const oldSearchType = this.state.oldSearchType;
    let orgType = userInfo.org_type;
    const orgTreeTabSelectData = this.state.orgTreeTabSelectData;
    const treeType = orgTreeTabSelectData.tree_type;

    if (treeType === 2) {
      orgType = orgTreeTabSelectData.org_type;
    }

    const params = {
      org_type: orgType,
      tree_type: orgTreeTabSelectData.tree_type,
    };

    if (searchType === 1) {
      params["param"] = value;
    } else {
      params["org_name"] = value;
    }

    if (oldSearchType == searchType) {
      params["page"] = page || this.state.searchListPageNum;
    }

    dispatch({
      type: "organizeFramework/updateFormFields",
      formId: 3,
      params: {
        searchKey: value,
      },
    });
    this.setState({
      isSearchLoading: true,
      isSearchListLoading: true,
      searchList: [],
      searchTableType: searchType,
    });
    console.log(searchType, params, "====searchType");
    const result = await getSearchData(params, searchType);

    this.setState({
      isSearchLoading: false,
      isSearchListLoading: false,
      isSearchModal: false,
    });
    if (result.code !== 0) {
      return message.error(result.message);
    }

    this.setState({
      searchList: result.data,
      searchListPageNum: result.pageNum || 1,
      searchListPageTotal: result.total,
      oldSearchType: searchType,
      isSearchModal: true,
    });
  }

  //获取组织树详情
  async getTreeDetail(payload = {}) {
    const { treeId } = payload;
    const { dispatch } = this.props;
    this.setState({
      isTreeModalLoading: true,
    });

    const result = (await getTreeDetail(treeId)).data;

    this.setState({
      isTreeModalLoading: false,
    });

    if (result.code != 0) {
      return message.error(result.message);
    }

    const data = result.data;
    const params = {
      treeType: data.tree_type,
      treeName: data.tree_name,
    };

    if (data.org_type) {
      params["orgType"] = data.org_type;
    } else {
      params["orgType"] = null;
    }

    dispatch({
      type: "organizeFramework/updateFormFields",
      formId: 2,
      params,
    });
  }
  async updateOrgTree(payload = {}) {
    const { tree_id, tree_name, tree_type, org_type } = payload;
    const { userInfo } = this.props;
    this.setState({
      isTreeModalSubmitLoading: true,
    });

    const params = {};
    params["tree_name"] = tree_name;
    params["tree_type"] = tree_type;
    if (org_type) {
      params["org_type"] = org_type;
    }

    let result;
    let tabIndex = this.state.orgTreeTabIndex;
    if (tree_id) {
      params["tree_id"] = tree_id;
      result = (await updateOrgTree(params)).data;
    } else {
      result = (await addOrgTree(params)).data;
    }

    this.setState({
      isTreeModalSubmitLoading: false,
    });

    if (result.code != 0) {
      return message.error(result.message);
    }

    const orgId = userInfo.oid;
    this.setState({
      isTreeModalVisible: false,
    });

    const p = {
      orgId,
      tabIndex,
    };
    if (!tree_id) {
      p["isOptTree"] = true;
    }
    this.getTreeList(p);
  }

  async deleteOrgTree(payload = {}) {
    const { treeId } = payload;
    const { userInfo } = this.props;

    this.setState({
      isTreeModalDeleteLoading: true,
    });

    const result = (await deleteOrgTree(treeId)).data;

    this.setState({
      isTreeModalDeleteLoading: false,
    });

    if (result.code != 0) {
      return message.error(result.message);
    }

    const orgId = userInfo.oid;
    let tabIndex = 0;

    this.setState({
      isTreeModalVisible: false,
    });

    const p = {
      orgId,
      tabIndex,
    };

    this.getTreeList(p);
  }
  // 获取组织资料详情
  async getOrgDetail(id) {
    let institutionSortNmae = "";
    let institutionClassifyName = "";
    let institutionLevelName = "";
    let institutionNatureName = "";
    const { data } = await getNewOrgInfo(id);
    this.state.institutionSort.forEach((item) => {
      if (item.op_key == data.data.institution_category) {
        return (institutionSortNmae = item.op_value);
      }
    });
    this.state.institutionClassify.forEach((item) => {
      if (item.op_key == data.data.institution_classification) {
        return (institutionClassifyName = item.op_value);
      }
    });
    this.state.institutionLevel.forEach((item) => {
      if (item.op_key == data.data.institution_level) {
        return (institutionLevelName = item.op_value);
      }
    });
    this.state.institutionNature.forEach((item) => {
      if (item.op_key == data.data.institution_nature) {
        return (institutionNatureName = item.op_value);
      }
    });
    if (data.code === 0) {
      this.setState({
        orgDatum: {
          ...data.data,
          institution_categoryName: institutionSortNmae,
          institution_classifyName: institutionClassifyName,
          institution_levelName: institutionLevelName,
          institution_natureName: institutionNatureName,
        },
      });
      const { super_department } = data.data
      super_department && this.organizeSelector.current.batchAddOrgs([super_department])
    }
  }

  render() {
    const { userInfo, organizeFramework, dispatch, history, form } = this.props;
    const { getFieldDecorator } = form;
    const { oid, name: orgName, user_id, org_type } = userInfo;
    const _this = this;
    const {
      orgStatus,
      parentOrgId,
      selectOrgId,
      autoExpandParent,
      organizeModalStatus,
      organizeModalTitle,
      isSiderLoading,
      isSiderSubmitLoading,
      ownerDeparts,
      siderDeparts,
      allDeparts,
      initDepartmentValue,
      selectedDepartmentValue,
      currentDepartmentValue,
      ownerDepartmentValue,
      isLoading,
      orgTreeTabList,
      orgTreeTabIndex,
      orgTreeTabCurData,
      orgTreeTabSelectData,
      statisData,
      isTreeModalVisible,
      isTreeModalMode,
      isTreeModalLoading,
      isTreeModalSubmitLoading,
      isTreeModalDeleteLoading,
      isTreePanelExist,
      isSearchLoading,
      isSearchListLoading,
      isSearchModal,
      searchType,
      searchTableType,
      searchList,
      searchListPageTotal,
      searchListPageNum,
      isRootId,
      institutionSort,
      institutionClassify,
      institutionLevel,
      institutionNature,
      institutionLead,
      orgDatum,
      orgGroupdata
    } = this.state;
    const {
      areaAddressData,
      orgTypeData,
      organizeTypeData,
      collapsed,
      isHideWrap,
      industryList,
      formFields,
      tabIndex,
    } = organizeFramework;

    const appProps = {
      _this,
      userId: user_id,
      autoExpandParent,
      orgId: oid,
      orgType: org_type,
      isRootId,
      isShizhi: this.state.isShizhi,
      dataSource: allDeparts,
      departmentValue: initDepartmentValue,
      currentDepartmentValue,
      statisData,
      isLoading,
      orgTreeTabList,
      orgTreeTabIndex,
      orgTreeTabCurData,
      orgTreeTabSelectData,
      orgName,
      searchList,
      isSearchLoading,
      isSearchListLoading,
      isSearchModal,
      searchType,
      searchTableType,
      formFields: formFields.form3,
      searchListPageTotal,
      searchListPageNum,
      onSearchListPageChange: (pageNum) => {
        _this.getSearchList({
          value: formFields.form3.searchKey[1],
          page: pageNum,
        });
      },
      onInit: (form) => {
        _this.searchForm = form;
      },
      locateOrg: async (e) => {
        await _this.locateOrg({
          orgId: e.org_id,
          target: e.target,
        });
        _this.searchForm.resetFields();
        dispatch({
          type: "organizeFramework/resetFormFields",
          formId: 3,
        });
      },
      gotoOrg: async (record, table) => {
        console.log(record, "组织资料111111");
        // 组织资料
        this.setState({
          drawerLoading: true,
          drawerVisible: true,
          drawerTitle: `${record.name}的资料`,
          edit: record.org_id,
          newControl: true,
        });
        if (table) {
          _this.getOrgDetail(record.org_id);
        }
      },
      gotoNextOrg: async (item) => {
        console.log(orgType, "新建orgType");
        const { resetFields } = this.props.form;
        const { userInfo } = this.props;
        let orgType = userInfo.org_type;
        const { org_id } = item;
        // 新建下级组织
        this.setState(
          {
            drawerLoading: true,
            drawerVisible: true,
            edit: false,
            drawerTitle: "新建下级组织",
            newControl: false,
            orgDatum: {
              org_type: orgType,
              parent_name: item.name,
              parent_id: item.org_id,
            },
          },
          () => {
            resetFields();
          }
        );
      },
      gotoHref(e) {
        if (e.isOrg) {
          window.sessionStorage.setItem("_org_orgId", e.org_id);
          window.sessionStorage.setItem("__org_name", e.name);
          window.sessionStorage.removeItem("_org_userId");

          history.push({
            pathname: "/organize-data",
            query: {
              orgId: e.org_id,
              name: e.name,
            },
          });
        } else {
          window.sessionStorage.setItem("_org_orgId", e.org_id);
          window.sessionStorage.setItem("__org_name", e.org_name);
          window.sessionStorage.setItem("_org_userId", e.user_id);
          window.sessionStorage.setItem("_org_username", e.name);
          // window.sessionStorage.setItem('_org_card', e.card);
          // window.sessionStorage.setItem('_org_phone', e.phone);

          history.push({
            pathname: "/organize-data",
            query: {
              orgId: e.org_id,
              name: e.org_name,
              userName: e.name,
              // phone: e.phone,
              // card: e.card,
              userId: e.user_id,
            },
          });
        }
      },
      // 刷新
      onClearSearch(form) {
        _this.setState(
          {
            isSearchLoading: false,
            isSearchListLoading: false,
            isSearchModal: false,
            oldSearchType: null,
            searchListPageNum: 1,
            searchListPageTotal: 1,
            orgDatum: {},
          },
          () => {
            _this.getTreeList({
              orgId: oid,
              tabIndex: tabIndex,
            });
          }
        );
        form.resetFields();
        dispatch({
          type: "organizeFramework/resetFormFields",
          formId: 3,
        });
      },
      loadTreeData(treeNode, f) {
        return new Promise(async (resolve) => {
          if (
            (treeNode.props.children && treeNode.props.children.length > 0) ||
            !treeNode.props.dataRef.child_org_num
          ) {
            resolve();
            return;
          }
          const p = {};
          if (f != 2 && orgTreeTabSelectData.org_type) {
            p["org_type"] = orgTreeTabSelectData.org_type;
          }
          if (f == 2) {
            p["is_filter"] = 1;
          }
          const result = (
            await getOrgTree(
              Object.assign(
                {},
                {
                  org_id: treeNode.props.dataRef.org_id,
                  tree_type: f == 2 ? 1 : orgTreeTabSelectData.tree_type,
                  show_code: 1,
                  load_root: 0,
                },
                p
              )
            )
          ).data;

          if (result.code !== 0) {
            return message.error(result.message);
          }

          if (!result.data.length) {
            treeNode.props.dataRef.child_org_num = 0;
          } else {
            treeNode.props.dataRef.children = result.data.map((item) => {
              return { ...item, isLeaf: item.child_org_num === 0 };
            });
          }

          const payload = {};
          if (f === 1) {
            payload.siderDeparts = siderDeparts;
          } else if (f === 2) {
            payload.ownerDeparts = ownerDeparts;
          } else {
            payload.allDeparts = allDeparts;
          }
          _this.setState(payload);
          resolve();
        });
      },
      onEditOrgTree() {
        _this.getTreeDetail({ treeId: orgTreeTabCurData.tree_id });
        _this.setState({
          isTreeModalVisible: true,
          isTreeModalMode: false,
        });
      },
      onChangeOrgTreeTab: (key) => {
        if (key == "new") {
          dispatch({
            type: "organizeFramework/updateFormFields",
            formId: 2,
            params: {
              treeType: isTreePanelExist ? 2 : 1,
              treeName: null,
              orgType: null,
            },
          });
          _this.setState({
            isTreeModalVisible: true,
            isTreeModalMode: true,
          });
        } else {
          _this.searchForm.resetFields();
          dispatch({
            type: "organizeFramework/resetFormFields",
            formId: 3,
          });

          _this.getTreeList({
            orgId: oid,
            tabIndex: key,
            isOpt: true,
          });
          _this.setState({
            isSearchLoading: false,
            isSearchListLoading: false,
            isSearchModal: false,
            oldSearchType: null,
          });
        }
      },

      onSearchSubmit: (form) => {
        const key = trim(form.getFieldValue("searchKey"));
        if (!key) {
          return message.error("请输入查询名称");
        }
        this.setState(
          {
            oldSearchType: null,
          },
          () => {
            this.getSearchList({ value: key });
          }
        );
      },
      onSearchTypeChange(e) {
        _this.setState({ searchType: e });
      },
      onSelectDepartment(e, node) {
        _this.setState({ selectedDepartmentValue: e });
      },
      onExpand(e) {
        _this.setState({
          initDepartmentValue: e,
          autoExpandParent: false,
        });
      },

      //点击
      createFramework: async () => {
        // 新增按钮
        this.setState({
          drawerLoading: true,
          drawerVisible: true,
          edit: false,
          drawerTitle: "新建组织",
        });
        const { fieldList, allDeparts } = this.state;
        const initVal = {};
        fieldList.map(({ key, defaultValue, type }) => {
          initVal[key] = DynamicForm.formatDefaultValue(type, defaultValue);
        });
        if (this.dynamicFormRef) {
          this.dynamicFormRef.resetFields();
        }
        // 组织类型
        const orgTypeList =
          (await getCodeList({ code: orgTreeTabSelectData.org_type })).data
            .data || [];
        const orgDetails =
          (await getOrgInfo({ org_id: orgTreeTabSelectData.organization_id }))
            .data.data || [];
        // 所属单位
        const affiliation =
          (
            await locateOrgTree({
              root_org_id: sessionStorage.getItem("_oid"),
              tree_type: 1,
              is_filter: 1,
              load_root: 1,
              org_id: orgDetails.owner_id,
            })
          ).data.data || [];

        this.setState(
          {
            drawerLoading: false,
            extraAttr: {
              parent_id: {
                getRef: (ref) => {
                  this.orgRef = ref;
                },
                disabled: true,
              },
              org_type_child: {
                options: Array.isArray(orgTypeList)
                  ? orgTypeList.map(({ op_key, op_value }) => ({
                    value: String(op_key),
                    label: op_value,
                  }))
                  : [],
              },
              owner_id: {
                options: Array.isArray(affiliation)
                  ? affiliation.map(({ org_id, name }) => ({
                    value: String(org_id),
                    label: name,
                  }))
                  : [],
              },
            },
          },
          () => {
            setTimeout(async () => {
              const topOrgId = allDeparts[0].org_id;
              if (this.orgRef) {
                await this.orgRef.locateOrgTree(
                  topOrgId,
                  orgTreeTabSelectData.org_type
                );
              }
              this.dynamicFormRef.setFieldsValue({
                ...initVal,
                parent_id: String(topOrgId),
              });
            }, 0);
          }
        );
      },
      onSort(e) {
        _this.sortOrg({
          isNext: e.isNext,
          orgId: e.id,
          orgSeq: e.target.seq,
          desOrgId: e.target.org_id,
          desOrgSeq: e.order,
        });
      },
      popupDataSource: [
        {
          title: "组织资料",
          onClick(e) {
            console.log("资料", e);
            _this.setState({
              orgDatum: {},
              drawerLoading: true,
              drawerVisible: true,
              drawerTitle: `${e.name}的资料`,
              edit: e.id,
              newControl: true,
            });
            _this.getOrgDetail(e.id);
            // appProps.gotoOrg({
            //   org_id: e.id,
            //   parent_id: e.parentId || e.parent_id,
            //   name: e.name,
            // });
          },
        },
        {
          title: "职务管理",
          onClick(e) {
            _this.positionModalRef.open(e);
          },
        },
        {
          title: "新建下级组织",
          hide: this.permissionList.indexOf("insert-org") === -1,
          async onClick(e) {
            const { resetFields } = _this.props.form;
            console.log("新建下级", e);
            _this.setState(
              {
                drawerLoading: true,
                drawerVisible: true,
                edit: false,
                newControl: false,
                orgDatum: {
                  parent_id: e.id,
                  parent_name: e.name,
                  org_type: e.orgType,
                  vice_leader_num: 0,
                  real_leader_num: 0,
                },
              },
              () => {
                resetFields();
              }
            );
            // appProps.gotoNextOrg({
            //   org_id: e.id,
            //   parent_id: e.parentId || e.parent_id,
            // });
          },
        },
      ],
    };
    const treeProps = {
      formFields: formFields.form2,
      orgTypeData,
      isVisible: isTreeModalVisible,
      isTreePanelExist,
      isMode: isTreeModalMode,
      isLoading: isTreeModalLoading,
      isSubmitLoading: isTreeModalSubmitLoading,
      isDeleteLoading: isTreeModalDeleteLoading,
      orgTreeTabCurData,
      orgTreeTabList,
      onCancel() {
        _this.setState({ isTreeModalVisible: false });
      },
      onSubmit(form, isMode) {
        form.validateFields((err, values) => {
          if (!err) {
            const params = { ...values };
            if (!isMode) {
              params["tree_id"] = orgTreeTabCurData.tree_id;
            }

            _this.updateOrgTree(params);
          }
        });
      },
      onChaneTreeType(e) {
        dispatch({
          type: "organizeFramework/updateFormFields",
          formId: 2,
          params: {
            treeType: e.target.value,
          },
        });
      },
      onDelete() {
        _this.deleteOrgTree({ treeId: orgTreeTabSelectData.tree_id });
      },
    };
    return (
      <div>
        <IndexView {...appProps} />
        <TreeManagerModal {...treeProps} />
        <PositionModal
          ref={(ref) => {
            this.positionModalRef = ref;
          }}
        />
        {/* 抽屉 */}
        <Drawer
          width={700}
          visible={this.state.drawerVisible}
          // visible={true}
          title={this.state.drawerTitle}
          onClose={() => {
            this.setState({ drawerVisible: false });
            form.resetFields();
          }}
        >
          <div className="new-add-org">
            <Form className={"new-add"} labelCol={{ span: 6 }}>
              <Form.Item label="机构名称">
                {getFieldDecorator("name", {
                  initialValue: orgDatum.name || undefined,
                  rules: [
                    {
                      required: true,
                      message: "请输入机构名称",
                    },
                  ],
                })(<Input placeholder={"请输入机构名称"}></Input>)}
              </Form.Item>
              <Form.Item label="机构简称">
                {getFieldDecorator("short_name", {
                  initialValue: orgDatum.short_name || undefined,
                  rules: [
                    {
                      required: true,
                      message: "请输入机构简称",
                    },
                  ],
                })(<Input placeholder={"请输入机构简称"}></Input>)}
              </Form.Item>
              <Form.Item label="上级机构">
                {getFieldDecorator("parent_name", {
                  initialValue: orgDatum.parent_name || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择上级机构",
                    },
                  ],
                })(
                  <Select
                    placeholder="请选择"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {this.state.parentOrgList
                      .filter(item => item.organization_id !== orgDatum.organization_id)
                      .map((item) => (
                        <Option key={item.organization_id} value={item.organization_id}>
                          {item.name}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="统一社会信用代码">
                {getFieldDecorator("org_unique_code", {
                  initialValue: orgDatum.org_unique_code || "",
                })(<Input placeholder={"请输入"}></Input>)}
              </Form.Item>
              <Form.Item label="机构类别">
                {getFieldDecorator("institution_category", {
                  initialValue: orgDatum.institution_category || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Select>
                    {institutionSort && institutionSort.length > 0
                      ? institutionSort.map((item, index) => {
                        return (
                          <Option value={item.op_key} key={index}>
                            {item.op_value}
                          </Option>
                        );
                      })
                      : null}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="机构分类">
                {getFieldDecorator("institution_classification", {
                  initialValue: orgDatum.institution_classify || undefined,
                })(
                  <Select>
                    {institutionClassify && institutionClassify.length > 0
                      ? institutionClassify.map((item, index) => {
                        return (
                          <Option value={item.op_key} key={index}>
                            {item.op_value}
                          </Option>
                        );
                      })
                      : null}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="机构层次">
                {getFieldDecorator("institution_level", {
                  initialValue: orgDatum.institution_level || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Select>
                    {institutionLevel && institutionLevel.length > 0
                      ? institutionLevel.map((item, index) => {
                        return (
                          <Option value={item.op_key} key={index}>
                            {item.op_value}
                          </Option>
                        );
                      })
                      : null}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="机构性质">
                {getFieldDecorator("institution_nature", {
                  initialValue: orgDatum.institution_nature || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Select>
                    {institutionNature && institutionNature.length > 0
                      ? institutionNature.map((item, index) => {
                        return (
                          <Option value={item.op_key} key={index}>
                            {item.op_value}
                          </Option>
                        );
                      })
                      : null}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="所属序列">
                {getFieldDecorator("group_id", {
                  initialValue: orgDatum.group_id || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Select>
                    {orgGroupdata && orgGroupdata.length > 0
                      ? orgGroupdata.map((item, index) => {
                        return (
                          <Option value={item.group_id} key={index}>
                            {item.group_name}
                          </Option>
                        );
                      })
                      : null}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="是否党政分设">
                {getFieldDecorator("has_divided", {
                  initialValue: orgDatum.has_divided || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Radio.Group>
                    <Radio value={1}>是</Radio>
                    <Radio value={2}>否</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              <Form.Item label="是否正科级班子">
                {getFieldDecorator("regular_team", {
                  initialValue: orgDatum.regular_team || undefined,
                  rules: [
                    {
                      required: false,
                      message: "请选择",
                    },
                  ],
                })(
                  <Radio.Group>
                    <Radio value={1}>是</Radio>
                    <Radio value={2}>否</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              <Form.Item label="合并对比的主管部门">
                {getFieldDecorator("super_department", {
                  initialValue: orgDatum.super_department || undefined,
                })(<OrgSelect selectType="text" ref={(ref) => { this.organizeSelector = ref }} radio />
                )}
              </Form.Item>
            </Form>
            <div
              style={{
                width: "100%",
                padding: "10px 16px",
                background: "#fff",
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                type="primary"
                style={{ marginRight: "50px" }}
                onClick={() => {
                  this.newAdd();
                }}
              >
                保存
              </Button>
              {this.state.newControl && (
                <Popconfirm
                  title="确认删除该组织吗?"
                  onConfirm={() => {
                    this.ensureDeleteOrg();
                    // this.onDelOrg();
                  }}
                >
                  <Button style={{ marginRight: 8 }}>删除组织</Button>
                </Popconfirm>
              )}
            </div>
          </div>
        </Drawer>
      </div>
    );
  }
}

const mapStateToProps = ({ organizeFramework, userInfo }) => ({
  organizeFramework,
  userInfo,
});
export default connect(mapStateToProps)(Form.create()(OrganizeFramework));
