import React, { Component } from "react";
import SearchHeader from "components/search-header";
import {
  Button,
  Table,
  Row,
  Col,
  Input,
  message,
  Modal,
  Form,
  Popconfirm,
} from "antd";
import "./index.less";
import md5 from "js-md5";

import Competence from "./component/competence";
import Competence2 from "./component/competence2";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
import PAutoComplete from "../components/PAutoComplete";
import PAutoInput from "../components/PAutoInput";

import {
  getUserPage,
  addUser,
  getAccount,
  updateUserStatus,
  updateUserPwd,
  editUser,
  getUser,
} from "client/apis/cadre-portrait";
class Index extends Component {
  constructor(props) {
    super(props);
    this.rowSelection = this.rowSelection.bind(this);
    this.copyRef = React.createRef();
    this.state = {
      userInfo: {
        user_name: "admin",
        user_number: "admin",
        user_id: "1",
      },
      currentSelect: undefined,
      initPassword: "",
      addVisiable: false,
      resetVisible: false,
      resetValue: {},
      organizeVisible: false,
      orgs: [],
      sourceData: [],
      total: 1,
      selectRows: [],
      competenceVisible1: false,
      activeCompetence1: {},
      competenceVisible2: false,
      activeCompetence2: {},
      selectkeys: [],
      loading: true,
      search: {
        user_name: "",
        page: 1,
        pageSize: 10,
      },
    };
    this.hideOrganizeModal = this.hideOrganizeModal.bind(this);
    this.showOrganizeModal = this.showOrganizeModal.bind(this);
    this.statusOnchange = this.statusOnchange.bind(this);
    this.getUserList = this.getUserList.bind(this);
    this.keyWordOnChange = this.keyWordOnChange.bind(this);
    this.pageOnChange = this.pageOnChange.bind(this);
    this.showCompetence1 = this.showCompetence1.bind(this);
    this.hideCompetence1 = this.hideCompetence1.bind(this);
    this.showCompetence2 = this.showCompetence2.bind(this);
    this.hideCompetence2 = this.hideCompetence2.bind(this);
    this.org_id =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";
    this.org_name =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_org_name")
        : "";
  }
  componentDidMount() {
    this.getUserList(1);
  }
  onEditor(r) {
    const { setFieldsValue } = this.props.form;
    this.setState({
      addVisiable: true,
      currentSelect: r,
    });

    getUser({ sys_user_id: r.sys_user_id }).then((res) => {
      if (res.data.code === 0) {
        setFieldsValue(res.data.data);
      }
    });
  }
  rowSelection(selectkeys, selectRows) {
    this.setState({
      selectRows,
      selectkeys,
    });
  }
  showCompetence1(r) {
    this.setState({
      competenceVisible1: true,
      activeCompetence1: r || {},
    });
  }
  showAddModal() {
    this.setState({
      initPassword: this.generatePassword(),
      addVisiable: true,
    });
  }
  hideAddModal() {
    this.setState({
      addVisiable: false,
      currentSelect: undefined,
    });

    this.props.form.resetFields();
  }
  disableUser(r) {
    console.log("🚀 ~ Index ~ disableUser ~ r:", r);
    updateUserStatus({
      sys_user_id: r.sys_user_id,
      status: r.status === 1 ? 2 : 1,
    }).then((res) => {
      console.log("🚀 ~ Index ~ disableUser ~ res:", res);
      if (res.data.code === 0) {
        this.getUserList();

        message.success(res.data.data);
      }
    });
  }
  resetPassword(r) {
    this.setState({
      initPassword: this.generatePassword(),
      resetVisible: true,
      resetValue: r,
    });
  }
  onOk() {
    const { getFieldsValue, validateFields } = this.props.form;
    validateFields(async (err, values) => {
      console.log("🚀 ~ Index ~ validateFields ~ err:", values);
      /**
       * 
       *  user_id	number	否	干部id
          user_name	string	是	用户
          account	string	是	账号
          password	string	是	密码（加密后密码）
       */
      const params = values;
      if (typeof params.user_name !== "string") {
        const user_id = params.user_name.user_id;
        const user_name = params.user_name.user_name;

        delete params.user_name;

        params.user_id = user_id;
        params.user_name = user_name;
      }

      params.password && (params.password = md5(params.password));

      const isEdit = this.state.currentSelect;
      const res = await addUser(params);

      if (res.data.code === 0) {
        message.success(isEdit ? "编辑成功" : "添加成功");

        this.hideAddModal();

        this.getUserList();
      } else {
        message.error(res.data.message);
      }
    });
  }
  onCopy() {
    const el = this.copyRef.current;
    console.log("🚀 ~ Index ~ onCopy ~ el:", el);

    el.select();

    document.execCommand("copy");

    message.success("密码复制成功!");
    // if (el && el.innerText) {
    //   navigator.clipboard.writeText(el.innerText);
    // }
  }
  showCompetence2(r) {
    console.log("🚀 ~ Index ~ showCompetence2 ~ r:", r);
    this.setState({
      competenceVisible2: true,
      activeCompetence2: r || {},
    });
  }
  handleResetOk(r) {
    const { initPassword, resetValue } = this.state;

   

    this.setState({
      competenceVisible1: false,
    });

    updateUserPwd({
      sys_user_id: resetValue.sys_user_id,
      password: md5(initPassword),
      flag: resetValue.login ? 1 : 2,
    }).then((res) => {
      console.log("🚀 ~ Index ~ handleResetOk ~ res:", res);
      if (res.data.code === 0) {
        this.onCopy();
        this.setState({
          resetVisible: false,
          resetValue: {},
          initPassword: "",
        });
      } else {
        // message.error("重置密码失败");
        message.error(res.data.message);
      }
    });
  }
  hideCompetence1() {
    this.setState({
      competenceVisible1: false,
    });
    this.getUserList();
  }
  hideCompetence2() {
    this.setState({
      competenceVisible2: false,
    });
  }
  hideOrganizeModal() {
    this.setState({
      organizeVisible: false,
    });
  }
  pageOnChange(page) {
    const search = this.state.search;
    search.page = page;
    this.setState(
      {
        search,
      },
      this.getUserList
    );
  }
  statusOnchange(val) {
    const search = this.state.search;
    search.status = val;
    this.setState({
      search,
    });
  }
  keyWordOnChange(e) {
    const search = this.state.search;
    search.user_name = e.target.value;
    this.setState({
      search,
    });
  }
  showOrganizeModal() {
    this.setState({
      organizeVisible: true,
    });
  }
  getUserList(page) {
    this.setState({
      loading: true,
    });
    const postData = Object.assign(this.state.search);

    postData.page = page ? page : postData.page;
    getUserPage(postData).then((data) => {
      const _data = data.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        console.log(_data.data);
        this.setState(
          {
            sourceData: _data.data.data,
            total: _data.data.total,
            selectkeys: [],
          }
          // () => {
          //   console.log(this.state);
          // }
        );
      }
      this.setState({
        loading: false,
      });
    });
  }
  generatePassword() {
    const length = Math.floor(Math.random() * 5) + 8; // 随机长度在8到12之间
    const lowerCaseChars = "abcdefghijklmnopqrstuvwxyz";
    const upperCaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numberChars = "0123456789";
    const allChars = lowerCaseChars + upperCaseChars + numberChars;

    let password = "";
    password +=
      lowerCaseChars[Math.floor(Math.random() * lowerCaseChars.length)];
    password +=
      upperCaseChars[Math.floor(Math.random() * upperCaseChars.length)];
    password += numberChars[Math.floor(Math.random() * numberChars.length)];

    for (let i = 3; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    const shuffle = (string) => {
      const array = string.split("");
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array.join("");
    };

    return shuffle(password); // 打乱顺序
  }
  onSelect(value) {
    const { setFieldsValue } = this.props.form;

    getAccount({
      user_name: typeof value === "string" ? value : value.user_name,
    }).then((res) => {
      if (res.data.code === 0) {
        setFieldsValue({
          account: res.data.data,
        });
      }
    });
  }
  render() {
    const { history, form } = this.props;
    const { getFieldDecorator, getFieldValue } = form;

    const {
      initPassword,
      addVisiable,
      organizeVisible,
      orgs,
      search,
      loading,
      sourceData,
      selectkeys,
      competenceVisible1,
      competenceVisible2,
      resetVisible,
      activeCompetence1,
      activeCompetence2,
      total,
    } = this.state;
    const columns = [
      // {
      //   title: "序号",
      //   dataIndex: "name",
      //   key: "name",
      //   align: "center",
      //   width: "10%",
      //   render: (t, row, i) => {
      //     return i + 1;
      //   },
      // },
      {
        title: "用户姓名",
        dataIndex: "user_name",
        key: "user_name",
        align: "center",
      },
      {
        title: "账号",
        dataIndex: "account",
        key: "account",
        align: "center",
      },
      {
        title: "角色",
        dataIndex: "role_name",
        key: "role_name",
        align: "center",
        // render() {
        //   const roleMap = {
        //     1: "高级管理员",
        //     2: "平板用户",
        //     3: "普通用户",
        //     3: "单位管理员",
        //   };
        //   const role = [].map((item) => roleMap[item]).join(",");
        //   return roleMap[1];
        // },
      },
      {
        title: "用户状态",
        dataIndex: "status",
        key: "status",
        align: "center",
        render(r) {
          return r === 1 ? "正常" : "禁用";
        },
      },
      // {
      //   title: "权限状态",
      //   dataIndex: "name",
      //   key: "name",
      //   width: 290,
      //   align: "center",
      //   render: (t, row) => {
      //     if (row.status == 1) {
      //       return <span className="user-status-normal">正常</span>;
      //     } else if (row.status == 2) {
      //       return <span className="user-status-disable">停用</span>;
      //     }
      //   }
      // },
      {
        title: "操作",
        dataIndex: "handler",
        key: "handler",
        align: "center",
        width: "17%",
        render: (t, r) => {
          return (
            <React.Fragment>
              <a
                href="javascript:;"
                className="table-option-btn"
                onClick={() => {
                  this.onEditor(r);
                }}
              >
                编辑
              </a>
              <Popconfirm
                title={`是否${r.status === 1 ? "禁" : "启"}用该用户？`}
                trigger="click"
                onConfirm={() => {
                  this.disableUser(r);
                }}
                cancelText="否"
                okText="是"
              >
                <a className="table-option-btn">
                  {r.status === 1 ? "禁" : "启"}用
                </a>
              </Popconfirm>
              <a
                href="javascript:;"
                className="table-option-btn"
                onClick={() => {
                  this.showCompetence1(r);
                }}
              >
                配置权限
              </a>
              <a
                href="javascript:;"
                className="table-option-btn"
                onClick={() => {
                  this.resetPassword({
                    ...r,
                    login: true,
                  });
                }}
              >
                重置登录密码
              </a>
              <a
                href="javascript:;"
                className="table-option-btn"
                onClick={() => {
                  this.resetPassword({
                    ...r,
                    personal: true,
                  });
                }}
              >
                重置个人中心密码
              </a>
              {/* <a href="javascript:;" className="table-option-btn">
                停用
              </a> */}
            </React.Fragment>
          );
        },
      },
    ];
    const organizeModalProps = {
      visible: organizeVisible,
      dataSource: orgs,
      hideModal: () => {
        this.hideOrganizeModal();
      },
      loadOrganizeData: (data) => {
        const search = Object.assign(this.state.search);
        search.org_id_list = data.map((d) => d.org_id);
        this.setState({
          search,
          orgs: data,
        });
        this.hideOrganizeModal();
      },
      ref: (ref) => {
        this.organizeModal = ref;
      },
    };
    return (
      <div className="user-group-competence">
        <SearchHeader onBack={history.goBack} title="用户权限管理" />
        <div className="warp">
          {/* <Row className="search-group">
            <Col span={24}>
              组织名称：
              {orgs.length == 0
                ? unescape(this.org_name)
                : orgs.map(d => d.org_name).join(",")}
              <a
                href="javascript:;"
                onClick={this.showOrganizeModal}
                style={{ marginLeft: 10 }}
              >
                选择组织
              </a>
            </Col>
          </Row> */}
          <Row className="search-group" type="flex" justify="space-between">
            {/* <Col span={4}>
              权限状态：
              <Select
                defaultValue="1"
                style={{ width: 120 }}
                onChange={this.statusOnchange}
              >
                <Option value="1">正常</Option>
                <Option value="2">禁用</Option>
              </Select>
            </Col> */}
            <Col span={8}>
              <label>
                姓名：
                <Input
                  style={{ width: 160 }}
                  onChange={this.keyWordOnChange}
                  value={search.user_name}
                  placeholder="请输入"
                />
              </label>
              <Button
                style={{ marginLeft: 20 }}
                type="primary"
                onClick={() => {
                  this.getUserList(1);
                }}
              >
                查询
              </Button>
            </Col>
            <Col span={2}>
              <Button
                className="btn"
                onClick={() => {
                  this.showAddModal();
                }}
              >
                添加用户
              </Button>
            </Col>
          </Row>
          <div className="option-btn">
            {/* <Button className="btn">批量停用</Button>
            <Button className="btn">批量弃用</Button>
            <Button className="btn">批量移除 </Button> */}
          </div>

          <Table
            style={{ paddingRight: "10px" }}
            rowKey="sys_user_id"
            loading={loading}
            columns={columns}
            bordered
            dataSource={sourceData}
            // rowSelection={{
            //   onChange: this.rowSelection,
            //   selectedRowKeys: selectkeys,
            // }}
            pagination={{
              current: search.page,
              pageSize: search.pageSize,
              onChange: this.pageOnChange,
              total: total,
            }}
          />
        </div>
        <OrganizeModal {...organizeModalProps} />
        {competenceVisible1 ? (
          <Competence
            visible={competenceVisible1}
            hideCompetence1={this.hideCompetence1}
            showCompetence2={this.showCompetence2}
            activeCompetence1={activeCompetence1}
            competenceVisible2={competenceVisible2}
          />
        ) : null}
        {competenceVisible2 ? (
          <Competence2
            visible={competenceVisible2}
            hideCompetence2={this.hideCompetence2}
            activeCompetence2={activeCompetence2}
            activeCompetence1={activeCompetence1}
          />
        ) : null}
        {
          <Modal
            visible={addVisiable}
            title={this.state.currentSelect ? "编辑用户" : "添加用户"}
            onOk={() => {
              this.onOk();
            }}
            destroyOnClose
            onCancel={() => {
              this.hideAddModal();
            }}
          >
            <div className="add-user-modal-competence">
              <Form wrapperCol={{ span: 20 }} labelCol={{ span: 4 }}>
                {getFieldDecorator("sys_user_id")(<Input type="hidden" />)}
                <Form.Item label="用户" colon required>
                  {getFieldDecorator("user_name", {
                    rules: [{ required: true, message: "请输入用户名" }],
                  })(
                    <PAutoInput
                      placeholder="请输入"
                      onSelect={(value) => {
                        if (this.state.currentSelect) {
                          return;
                        }
                        this.onSelect(value);
                      }}
                    />
                  )}
                </Form.Item>
                <Form.Item label="账号" colon required>
                  {getFieldDecorator("account", {
                    rules: [{ required: true, message: "请输入账号" }],
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
                <input
                  style={{ position: "fixed", zIndex: -9999, opacity: 0 }}
                  ref={this.copyRef}
                  value={initPassword}
                />
                {!this.state.currentSelect && (
                  <Form.Item label="初始密码" colon required>
                    <div>
                      <span className="password">{initPassword}</span>
                      <a
                        className="copy"
                        style={{ marginLeft: 10 }}
                        onClick={() => {
                          this.onCopy();
                        }}
                      >
                        复制
                      </a>

                      {getFieldDecorator("password", {
                        initialValue: initPassword,
                        // 初始值
                      })(<Input placeholder="请输入" hidden />)}
                    </div>
                  </Form.Item>
                )}
              </Form>
            </div>
          </Modal>
        }

        <Modal
          visible={resetVisible}
          title={`重置${this.state.resetValue.login ? "登录" : "个人中心"}密码`}
          onOk={() => {
            this.handleResetOk();
          }}
          destroyOnClose
          okText="确定修改"
          onCancel={() => {
            this.setState({
              resetVisible: false,
            });
          }}
        >
          <Form.Item
            label="密码"
            colon
            required
            wrapperCol={{ span: 20 }}
            labelCol={{
              span: 4,
            }}
          >
            <div>
              <span className="password" ref={this.copyRef}>
                {initPassword}
              </span>
              <input
                style={{ position: "fixed", zIndex: -9999, opacity: 0 }}
                ref={this.copyRef}
                value={initPassword}
              />
              <a
                className="copy"
                style={{ marginLeft: 10 }}
                onClick={() => {
                  this.onCopy();
                }}
              >
                复制
              </a>
            </div>
          </Form.Item>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(Index);
