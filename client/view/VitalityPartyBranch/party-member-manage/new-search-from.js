import React, { useState, useEffect } from "react";
import "./index.less";
import { message, Form, DatePicker, Button, Input } from "antd";
import FormItem from "antd/lib/form/FormItem";
import moment from "moment";

const SearchFrom = (props) => {
  const {
    form: { getFieldDecorator, getFieldValue, validateFields },
    onSubmit,
    onReset,
  } = props;

  // 表单提交（查询）
  const onHandleSubmit = (e) => {
    e.preventDefault();
    validateFields((err, values) => {
      const { name, phone, start_time, end_time } = values;
      const params = {
        name,
        phone,
      };
      start_time && (params.start_time = start_time.format("YYYY-MM-DD"));
      end_time && (params.end_time = end_time.format("YYYY-MM-DD"));
      onSubmit && onSubmit(params);
    });
    // 暴露获取到的数据
  };
  // 开始报到时间范围限制
  const disabledStartDate = (startValue) => {
    if (startValue) {
      if (startValue < moment("2019-01-01")) {
        return true;
      }
      if (startValue > moment()) {
        return true;
      }
    }
    const endValue = getFieldValue("endTime");
    if (!startValue || !endValue) {
      return false;
    }
    const et = moment(
      getFieldValue("endTime").format("YYYY-MM-DD") + " 23:59:59"
    );
    return startValue > et;
  };
  // 结束报到时间范围限制
  const disabledEndDate = (endValue) => {
    if (endValue) {
      if (endValue < moment("2019-01-01")) {
        return true;
      }
      if (endValue > moment()) {
        return true;
      }
    }
  };

  return (
    <Form layout="inline" className="SearchFrom" onSubmit={onHandleSubmit}>
      <FormItem label="姓名">
        {getFieldDecorator("name")(
          <Input placeholder="请输入姓名" className="inputStyle" />
        )}
      </FormItem>
      <FormItem label="电话">
        {getFieldDecorator("phone")(
          <Input placeholder="请输入电话" className="inputStyle" />
        )}
      </FormItem>
      <FormItem label="报到时间：">
        {getFieldDecorator("start_time")(
          <DatePicker
            disabledDate={disabledStartDate}
            format="YYYY-MM-DD"
            placeholder="开始时间"
            style={{ width: 130 }}
          />
        )}
        <span style={{ paddingLeft: "5px", paddingRight: "5px" }}> ~ </span>
        {getFieldDecorator("end_time")(
          <DatePicker
            disabledDate={disabledEndDate}
            format="YYYY-MM-DD"
            placeholder="结束时间"
            style={{ width: 130 }}
          />
        )}
      </FormItem>
      <Button type="primary" htmlType="submit" style={{ marginRight: 10 }}>
        查询
      </Button>
      <Button className="btn" onClick={onReset}>
        重置
      </Button>
    </Form>
  );
};

export default Form.create()(SearchFrom);
