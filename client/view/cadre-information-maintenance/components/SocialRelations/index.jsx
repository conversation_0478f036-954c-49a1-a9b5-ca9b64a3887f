import {
  Button,
  Form,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Switch,
  Table,
  message,
} from "antd";
import {
  addOrUpdateRelationshipGraph,
  queryByCode,
  queryJobInfo,
} from "client/apis/cadre-portrait";
import {
  convertMomentToFormattedDate,
  convertTimeValues,
  extractBirthDate,
  validateIDCard,
  validateIDCardDate,
} from "client/tool/util";
import PAutoComplete from "client/view/cadre-system/components/PAutoInput";
import DateSingle from "components/date-single/DateSingle";
import moment from "moment";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import POrgAutoInput from "./components/POrgAutoInput";
import "./index.less";

function index({ form, data, org_id, disabled }, ref) {
  const { getFieldDecorator, getFieldsValue, validateFields, setFieldsValue } =
    form;
  const [visible, setVisible] = useState(false);

  const [dataSource, setDataSource] = useState([]);

  const [positionList, setPositionList] = useState([]);

  const [currentSelect, setCurrentSelect] = useState([]);

  const [codeMap, setCodeMap] = useState({
    appellation_id: [],
    political_type: [],
  });
  useEffect(() => {
    if (data && data.length && dataSource.length === 0) {
      setDataSource(data);
    }
  }, [data]);

  useEffect(() => {
    // initPosition()
    initCode("95120", "appellation_id");
    initCode("1013", "political_type");
  }, []);

  useImperativeHandle(ref, () => {
    return {
      onSubmit: onApiSubmit,
    };
  });

  const onEditor = (record, index) => {
    if (disabled) return;
    setFieldsValue(convertTimeValues({ ...record, index }));
    setCurrentSelect(convertTimeValues({ ...record, index }));
    setVisible(true);
  };

  const deleteRow = ({ appellation_id, pms_relationship_graph_id }) => {
    if (disabled) return;
    // 根据姓名职务userid，以及下标判断是否存在
    const index = dataSource.findIndex(
      (item) =>
        item.pms_relationship_graph_id === pms_relationship_graph_id &&
        item.appellation_id === appellation_id
    );
    if (index >= 0) {
      dataSource.splice(index, 1);

      if (pms_relationship_graph_id) {
        addOrUpdateRelationshipGraph({
          del_pms_relationship_graph_ids: [pms_relationship_graph_id],
        })
          .then(() => {
            message.success("删除成功");
            setDataSource([...dataSource]);
          })
          .catch((err) => {
            message.error("删除失败");
          });
      } else {
        setDataSource([...dataSource]);
      }
    }
  };

  // 列
  const columns = [
    {
      title: "关系",
      dataIndex: "appellation_id",
      key: "appellation_id",
      align: "center",
      render: (_) => {
        return _
          ? getCodeName({
              code: "95120",
              key: "appellation_id",
              op_key: _,
            })
          : _;
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "出生年月",
      dataIndex: "birthday",
      key: "birthday",
      align: "center",
    },
    {
      title: "政治面貌",
      dataIndex: "political_type",
      key: "political_type",
      align: "center",
      render: (_) => {
        return _
          ? getCodeName({
              code: "1013",
              key: "political_type",
              op_key: _,
            })
          : _;
      },
    },
    {
      title: "工作单位",
      dataIndex: "work_unit",
      key: "work_unit",
      align: "center",
    },
    {
      title: "职务",
      dataIndex: "position",
      key: "position",
      align: "center",
    },
    {
      title: "身份证号码",
      dataIndex: "number_secret",
      key: "number_secret",
      align: "center",
    },
    {
      title: "是否纳入干审表信息",
      dataIndex: "to_relationship",
      key: "to_relationship",
      align: "center",
      render: (_) => (_ == 1 ? "是" : "否"),
    },
    {
      title: "操作",
      dataIndex: "7",
      key: "7",
      align: "center",
      width: 140,
      render(_, record, index) {
        return (
          <div className={disabled ? "button-disabled" : ""}>
            <a
              onClick={() => {
                onEditor(record, index);
              }}
            >
              编辑
            </a>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <Popconfirm
              onConfirm={() => {
                deleteRow(record);
              }}
              okText="确认"
              cancelText="取消"
              title="确认删除？"
            >
              <a>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
  const initPosition = () => {
    queryJobInfo({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setPositionList(res.data.data);
      }
    });
  };
  const initCode = (code, key) => {
    queryByCode({ code }).then((res) => {
      setCodeMap((pre) => {
        return {
          ...pre,
          [key]: res.data.data,
        };
      });
    });
  };

  const getCodeName = ({ code, key, op_key }) => {
    if (!codeMap[key].length) {
      initCode(code, key);
    } else {
      const item = codeMap[key].find((item) => item.op_key === op_key);

      if (item) {
        return item.op_value;
      }
    }
  };

  const onUserSelect = (value) => {
    const { current_job, user_id } = value;
    setFieldsValue({
      position: current_job,
      user_id,
    });
  };
  const onApiSubmit = async (user_id) => {
    const data = dataSource.map((item) => {
      item.user_id = user_id;
      return item;
    });
    console.log("🚀 ~ data ~ data:", data);
    const res = await addOrUpdateRelationshipGraph({
      list: data,
    });
    if (res.data.code === 0) {
    } else {
      message.error("社会关系保存失败");
    }

    return res;
  };
  const onSubmit = () => {
    validateFields((err, values) => {
      if (!err) {
        const { name, to_relationship, work_unit } = values;
        values.name = name.user_name || name;

        values.work_unit = work_unit.name || work_unit;
        values.to_relationship = !to_relationship ? 0 : 1;

        if (values.index !== undefined) {
          dataSource[values.index] = convertMomentToFormattedDate(
            { ...values, index: undefined },
            "YYYY.MM"
          );
          setDataSource([...dataSource]);
        } else {
          setDataSource([
            ...dataSource,
            convertMomentToFormattedDate(values, "YYYY.MM"),
          ]);
        }

        setVisible(false);
      }
    });
  };

  const children = positionList.map((item) => {
    return (
      <Option
        onClick={() => {
          const params = getFieldsValue();
          setFieldsValue({
            has_divided: item.has_divided,
            current_job_text: item.job_name,
            job_id: item.job_id,
          });
        }}
        value={String(item.job_id)}
      >
        {item.job_name}
      </Option>
    );
  });

  const SwitchCom = useMemo(
    () =>
      ({ value, onChange }) => {
        return <Switch checked={value} onChange={onChange} />;
      },
    []
  );
  //数据源
  return (
    <div className="social-relations">
      <Button
        type="primary"
        disabled={disabled}
        onClick={() => setVisible(true)}
      >
        添加
      </Button>
      <Table
        style={{ marginTop: "10px" }}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        bordered
      ></Table>
      <Modal
        destroyOnClose
        width={600}
        title="添加主要社会关系"
        visible={visible}
        className="social-relations-modal"
        onCancel={() => {
          setVisible(false);
          setCurrentSelect({});
        }}
        onOk={onSubmit}
      >
        <Form
          layout="horizontal"
          colon
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          {/* <Row
            gutter={24}
            type="flex"
            justify="space-between"
          > */}

          {getFieldDecorator("pms_relationship_graph_id", {
            initialValue: currentSelect.pms_relationship_graph_id,
          })(<Input hidden />)}

          {getFieldDecorator("index", {
            initialValue: currentSelect.index,
          })(<Input hidden />)}

          {getFieldDecorator("user_id", {
            initialValue: currentSelect.user_id,
          })(<Input hidden />)}

          <Form.Item label="姓名" required>
            <Row type="flex">
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator("name", {
                  initialValue: currentSelect.name,
                  rules: [{ required: true, message: "请输入姓名" }],
                })(
                  <PAutoComplete
                    style={{ width: "auto" }}
                    onSelect={(value) => {
                      if (currentSelect.length) {
                        return;
                      }
                      onUserSelect(value);
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item
                label="关系"
                labelCol={{ span: 10 }}
                wrapperCol={{ span: 14 }}
                style={{ marginBottom: 0 }}
              >
                {getFieldDecorator("appellation_id", {
                  initialValue: currentSelect.appellation_id,
                  rules: [{ required: true, message: "请输入关系" }],
                })(
                  <Select style={{ width: 120 }} placeholder="请输入">
                    {codeMap.appellation_id.map((value) => (
                      <Select.Option value={value.op_key}>
                        {value.op_value}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Row>
          </Form.Item>

          {/* </Row> */}
          <Form.Item label="政治面貌">
            {getFieldDecorator("political_type", {
              initialValue: currentSelect.political_type,
              rules: [{ required: false, message: "请输入政治面貌" }],
            })(
              <Select style={{ width: 190 }} placeholder="请输入">
                {codeMap.political_type.map((value) => (
                  <Select.Option value={value.op_key}>
                    {value.op_value}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          {/* <Row type="flex"> */}
          <Form.Item label="身份证号">
            <Row type="flex">
              <Form.Item style={{ marginBottom: 0 }}>
                {getFieldDecorator("number_id", {
                  initialValue: currentSelect.number_id,
                  rules: [
                    {
                      required: true,
                      validator: (rule, value, callback) => {
                        console.log("🚀 ~ index ~ value:", value);
                        if (!value || !value.trim()) {
                          return callback();
                        }

                        //号码规则校验
                        if (!validateIDCard(value)) {
                          return callback("无效身份证号码");
                        }

                        if (!validateIDCardDate(value)) {
                          return callback("身份证日期错误");
                        }
                        const { year, day, month } = extractBirthDate(value);
                        setFieldsValue({
                          birthday: moment(`${year}-${month}-${day}`),
                        });
                        callback();
                      },
                    },
                  ],
                })(<Input placeholder="请输入" />)}
              </Form.Item>
              <Form.Item
                label="出生年月"
                labelCol={{ span: 10 }}
                wrapperCol={{ span: 14 }}
                style={{ marginBottom: 0 }}
              >
                {getFieldDecorator("birthday", {
                  initialValue: currentSelect.birthday,
                })(
                  <DateSingle
                    style={{ width: "180px" }}
                    inputWidth="75px"
                    placeholder="请选择"
                    type="month"
                    disabled={disabled}
                  />
                )}
              </Form.Item>
            </Row>
          </Form.Item>

          {/* </Row> */}
          <Form.Item label="工作单位">
            {getFieldDecorator("work_unit", {
              initialValue: currentSelect.work_unit,
              rules: [{ required: true, message: "请输入工作单位" }],
            })(<POrgAutoInput placeholder="请输入" />)}
          </Form.Item>
          <Form.Item label="职务">
            {getFieldDecorator("position", {
              initialValue: currentSelect.position,
              rules: [{ required: false, message: "请输入职务" }],
            })(<Input placeholder="请输入" />)}
          </Form.Item>
          <Form.Item
            label="是否纳入干审表信息"
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            required
          >
            {getFieldDecorator("to_relationship", {
              initialValue:
                currentSelect.to_relationship !== undefined
                  ? currentSelect.to_relationship
                  : 1,
            })(<SwitchCom />)}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default Form.create()(forwardRef(index));
