
import React, { Component } from "react";
import { But<PERSON>, Table, Checkbox, Alert } from "antd";

// import StatusIcon from "components/status-icon";

import "./view.less";

/**
 *
 */

 // const data = [{
 //   state: 0,
 //   name: '<PERSON>'
 // }, {
 //   state: 0,
 //   name: '<PERSON>'
 // }, {
 //   state: 1,
 //   name: '<PERSON>'
 // }];


export default class PushSettingView extends Component {

  constructor() {
    super();
    this.state = {
      visible:true,
    }

    this.onClose = this.onClose.bind(this);
    this.onSelectChange = this.onSelectChange.bind(this);
  }

  onSelectChange(selectedRowKeys) {
    const { data } = this.props;

    // console.log('selectedRowKeys changed: ', selectedRowKeys);

    if(this.props.onChange){
      //根据id取出 对应信息   !!!因为没有分页，这种方法可取
      const d = selectedRowKeys.map(
        id=> (data||[]).find(org=>(org.org_id==id)) || {org_id:id, name:'未知oid:'+id}
      );
      this.props.onChange(d)

      // this.props.onChange(selectedRowKeys)
    }
  }

  onClose(){

  }


  render() {
    const { title, data, selectedRows, isLoading } = this.props;

    const columns = [

      /*{
      title: '状态',
      dataIndex: 'state',
      render: state => <Checkbox onChange={this.onChange}></Checkbox>,

    //<StatusIcon type={state ? 'success' : 'disable' } text={state ? '展示中' : '未展示' }></StatusIcon>,
      align : 'center',
      width : '10%'
    }, */
    {
      title: '组织名称',
      className: 'org_name',
      dataIndex: 'org_name',
      // width : '80%'
    }];

    const rowSelection = {
      // columnTitle:'状态',
      selectedRowKeys : selectedRows.map(s=>s.org_id),
      onChange: this.onSelectChange,
    };

    return (
      <div className="box com-push-setting">
        <div className="box-hd">
          <Alert
            description={`说明：选中的组织给本组织推送${title}后，该${title}直接显示到本组织${title}列表；未选中组织需本组织管理员设置为 显示，才会显示到${title}列表。`}
            type="warning"
            closable
            onClose={this.onClose}
          />
        </div>
        <div className="box-bd">
          <Table
            rowKey={'org_id'}
            columns={columns}
            dataSource={data}
            rowSelection={rowSelection}
            pagination={false}
            loading={isLoading}
            //pagination={{size:"small", showQuickJumper:true, showSizeChanger:true, showTotal:(total) => `共 ${total} 条记录`}}
            bordered
          />
        </div>
      </div>
    )
  }
}
