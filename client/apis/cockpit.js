import { http } from "client/tool/axios";
import { sas1Host } from "./config";

// 党组织换届情况
const getPriodOrgInfo = (data = {}) => {
  return http.get(`${sas1Host}/screen/get-period-org-info`, data);
};

// 党组织数量
const getAllCount = (data = {}) => {
  return http.get(`${sas1Host}/screen/get-all-count`, data);
};

// 党员概况
const getPartyMemberOverviewInfo = (data = {}) => {
  return http.get(`${sas1Host}/screen/get-party-member-overview-info`, data);
};

// 年龄、党龄、学历分布
const getPartyDistributed = (data = {}) => {
  return http.get(`${sas1Host}/screen/get-party-distributed`, data);
};

// 组织生活开展情况
const getMeetingLife = (data = {}) => {
  return http.get(`${sas1Host}/tbc/report/meeting-life`, data);
};

// 党费缴纳情况
const getPartyMembershipDues = (data = {}) => {
  return http.get(`${sas1Host}/tbc/report/office-dues`, data);
};


export {
  getPriodOrgInfo,
  getAllCount,
  getPartyMemberOverviewInfo,
  getPartyDistributed,
  getMeetingLife,
  getPartyMembershipDues,
};
