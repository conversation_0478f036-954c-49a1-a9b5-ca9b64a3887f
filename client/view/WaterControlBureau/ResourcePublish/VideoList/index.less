.VideoList {
  display: inline-block;

  &-item {

    &-file {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 190px;
      height: 136px;
      font-size: 50px;
      color: #999;
      background: #ddd;
  
      &:hover {
        .VideoList-item-file-option {
          display: flex !important;
        }
      }
  
      &-option {
        position: absolute;
        left: 0;
        top: 0;
        display: none;
        justify-content: center;
        align-items: center;
        width: 190px;
        height: 136px;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 40px;
        z-index: 1;
      }
    }

    &-name {
      width: 190px;
      height: 40px;
      white-space: nowrap;
      word-break: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: center;
      line-height: 40px;
    }
  }
}