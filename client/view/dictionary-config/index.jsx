import { Button, Form, Input, Table } from "antd";
import { getDictionaryList } from 'client/apis/active-organization';
import { useEffect, useState } from "react";
import DictionaryModel from './dictionaryModel'; // 确保路径正确
import "./index.less";

function DictionaryConfig(props) {
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [code_name, setCodeName] = useState("");
  const [loading, setLoading] = useState(false);
  
  const [dictionaryData, setDictionaryData] = useState({
    code: "",
    group_id: undefined,
  });
    const [total, setTotal] = useState(0);
    const [pages, setPages] = useState(0);
 const [query, setQuery] = useState({
    page: 1,
    page_size: 10,
  });
  useEffect(() => {
    loadData(1);
  }, []);

  const onCancel = () => {
    setVisible(false);
  };

  const loadData = async (page) => {
    setLoading(true);
    const res = await getDictionaryList({
      code_name: code_name,
      // code_name: "称谓",
    ...query,
    page  
    })
      setLoading(false);
      if (res.data.code === 0) {
        const { data, total,pages } = res.data.data
        setDataSource(data);
        setTotal(total);
        setPages(pages);
        setQuery({
          ...query,
          page,
        });
      }

  };

  const columns = [
    {
      title: "字段名称",
      dataIndex: "code_name",
      key: "code_name",
      align: "center",
      width:"200px"
    },
    {
      title: "字典表内容",
      dataIndex: "values",
      key: "values",
      align: "center",
     
    },
    {
      title: "操作",
      dataIndex: "name",
      key: "name",
      align: "center",
        width:"100px",
      render: (text, record) => (
        <div className="operation">
          <a
            className="editor"
            onClick={() => {
              setVisible(true);
              setDictionaryData(record);
            }}
          >
            配置
          </a>
        </div>
      ),
    },
  ];

  return (
    <div className="dictionaryData-management">
      <div className="top-box">
        <div className="sm-t-left">
          <Form.Item
            label="字典名称"
            colon
      
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
          >
            <Input
              onChange={(e) => {
                setCodeName(e.target.value.trim());
              }}
            />
          </Form.Item>
        </div>
        <div className="sm-r-right">
          <div className="button">
            <Button
              type="primary"
              onClick={() => {
                loadData(1);
              }}
            >
              查询
            </Button>
          </div>
        </div>
      </div>
      <div className="table-box">
        <Table
          dataSource={dataSource}
          columns={columns}
          bordered
          rowKey="code"
          loading={loading}
          pagination={{
						size: 'small',
						showQuickJumper: true,
						pageSize: query.page_size,
						total,
						current: query.page,
            showTotal: ($total) => `共 ${$total} 条记录，共 ${pages} 页`,
            onChange: (page) => {
              loadData(page);
            },
					}}
        />
      </div>
      <DictionaryModel
        visible={visible}
        onClose={onCancel}
        dictionaryData={dictionaryData}
     
      />
    </div>
  );
}

export default DictionaryConfig;