/**
 * input的右边属性字
 * <AUTHOR>
 * 2018/4/27
 */
import React from 'react'
import './style.less'

const InputNumberAttributes = ({ children, unit, disabled, ...props }) => {
    return <span className={'ant-input-group ant-input-group-compact customize-input-group'} {...props}>
        {children}
        <div className={'ant-input-number customize-input-attributes ' + (disabled ? 'ant-input-number-disabled' : '')}>{unit}</div>
    </span>
}

InputNumberAttributes.defaultProps = {
    unit: '元',
    disabled: false
}

export default InputNumberAttributes