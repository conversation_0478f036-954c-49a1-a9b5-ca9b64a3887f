import { orgHost, Gateway } from "./config";
const { http } = require("client/tool/axios");

// 转入列表
export const getTransferList = (params) => {
  return http.post(`${orgHost}/transfer/list`, params);
};

// 查看详情
export const getTransferDetail = (params) => {
  return http.get(`${orgHost}/transfer/detail`, params);
};

// 转入办理
export const getTransferJoin = (params) => {
  return http.get(`${orgHost}/transfer/p-join`, params);
};

// 提交转接申请
export const getTransferAdd = (params) => {
  return http.post(`${orgHost}/transfer/add`, params);
};

// 录入新党员
export const getTransferAddNew = (params) => {
  return http.post(`${orgHost}/transfer/add-new`, params);
};

// 取消申请
export const getTransferCancel = (params) => {
  return http.get(`${orgHost}/transfer/cancel`, { master_id: params });
};

// 400020305071-组织关系内部调整转出 http://wiki.aidangqun.com/project/4?p=1130
export const orgRelateExport = (params) => {
  return http.post(`${orgHost}/transfer/inner-transfer`, params);
};

// 40302020115-组织关系内部调整批量转出（/batch-transfer/inner-transfer） http://wiki.aidangqun.com/project/9?p=1402&keyword=inner
export const batchRollOut = (params) => {
  return http.post(`${orgHost}/transfer/batch-inner-transfer`, params);
};
// 400020305072-根据党组织获取同级党支部列表 http://wiki.aidangqun.com/project/4?p=1139
export const getOrgList = (params) => {
  return http.get(`${orgHost}/org/find-org-by-owner`, params);
};
