import React, { PureComponent } from 'react';
import { Button, Table, Popconfirm } from "antd";
import ToggleBtn from "./toggle-btn";
import LeaderModal from "./leader-modal";

/**
 * 选择领导 按钮（内置弹窗）
 * disabled 是否禁用
 */
class SelectLeader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visibleTable: false,
      modalVisible: false,
      selectList: [],
      leaderList: [],
    };
    
    this.columns = [
      {
        title: '领导姓名',
        dataIndex: 'user_name',
      },
      {
        title: "所在组织",
        dataIndex: "org_name",
      },
      {
        title: "操作",
        key: "id",
        render: (text, index) => {
          const { disabled }= this.props;
          return (
            <Popconfirm
              title="确认删除当前联系领导?"
              onConfirm={() => this.handleDel(index)}
              okText="确定"
              cancelText="取消"
              disabled={disabled}
            >
              <Button type="link" disabled={disabled} style={{padding: 0}}>删除</Button>
            </Popconfirm>
          );
          
        }
      }
    ];
    this.onTableVisibleChange = this.onTableVisibleChange.bind(this);
    this.getLearList = this.getLearList.bind(this);
    this.handleSure = this.handleSure.bind(this);
  }
  handleSure(selectList) {
    const { onChange } = this.props;
    this.setState({selectList}, () => {
      onChange && onChange(selectList);
    });
  }
  // 初始化选中数据
  initSelected(list) {
    this.setState({
      selectList: list
    }, () => {
      this.leaderModal.initSelected(this.state.selectList);
    });
  }
  getLearList(leaderList) {
    const { getLearList } = this.props;
    this.setState({
      leaderList
    }, () => {
      getLearList && getLearList(leaderList);
    });
  }
  handleDel(index) {
    const { onChange } = this.props;
    this.setState((state) => {
      const { selectList } = state;
      selectList.splice(index, 1);
      const newList = [...selectList];
      return { selectList: newList, visibleTable: newList.length > 0};
    }, () => {
      const { selectList } = this.state;
      this.leaderModal.initSelected(selectList);
      onChange && onChange(selectList);
    });
  } 
  onTableVisibleChange(visible) {
    this.setState({visibleTable: visible});
  }
  render() {
    const { selectList, visibleTable, modalVisible } = this.state;
    const { disabled, visible }= this.props;
    return (
      <span style={{display: visible ? "inline" : "none"}}>
        {
          !disabled ? <Button type="link" onClick={() => this.setState({modalVisible: true})}>选择</Button> : ""
        }
        （{selectList.length}人）
        {
          selectList.length > 0 && (
            <ToggleBtn onChange={this.onTableVisibleChange} collapse={visibleTable}/>
          )
        }
        {
          visibleTable && <Table bordered pagination={false} rowKey="user_id" dataSource={selectList} columns={this.columns} />
        }
        <LeaderModal
          ref={(ref) => this.leaderModal = ref}
          title="选择联系领导"
          visible={modalVisible}
          onCancel={() => this.setState({modalVisible: false})}
          onSure={this.handleSure}
          getLearList={this.getLearList}
        />
      </span>
    );
  }
}

export default SelectLeader;
