// 捐赠方式
import React from "react";
import adaptiveLayout from "components/form-components/gridLayout";
import { Form, Radio, Input, InputNumber, Modal, Button, Checkbox } from "antd";
import SelfIcon from "components/self-icon";
import "./style.less";

import propTypes from "prop-types";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

class ContributeSetting extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,

      quotaItemKey: 0,

      // 捐赠方式，默认是1自由捐赠（2定额捐赠）
      donateType: 1,
      // 是否包含捐赠会员全部积分选项
      contributeAll: false,
      // 快捷额度设置，当捐赠方式为1时取
      quotaList: [],
      // 捐赠额度，当捐赠方式为2时取
      quotaValue: null
    };
  }

  componentWillReceiveProps(props) {
    const { state } = props;
    const data = this.parseQuickStrToQuotaList(state.quick_str);

    // console.log('componentWillReceiveProps:', data.quotaList)

    this.setState({
      quotaList: data.quotaList,
      contributeAll: data.contributeAll
    }, () => {
      // this.forceUpdate();
    })

  }


  componentWillMount() {
    const { state } = this.props;
    const { donate_type, quick_str, donate_num } = state;
    this.setState({
      donateType: donate_type
    });
  }

  // 移除一个额度框
  removeQuotaItem(index) {
    let { quotaList } = this.state;
    quotaList.splice(index, 1);
    this.setState({
      quotaList
    });
  }

  // 改变一个额度框的值
  changeQuotaItem(index, value) {
    const { onChange } = this.props;
    let { quotaList, donateType } = this.state;
    quotaList[index].value = value;
    this.setState({
      quotaList
    }, () => {
      let quick_str = [];
      if (quotaList && Array.isArray(quotaList)) {
        if (donateType === 1) {
          quotaList.forEach((item) => {
            if (item.value) {
              quick_str.push(item.value);
            }
          });
        }
      }
    });
    // console.log(quotaList);
  }

  parseQuotaListToQuickStr(quotaList = [], contributeAll = false) {
    let quick_str = [];
    if (quotaList && Array.isArray(quotaList)) {
      quotaList.forEach((item) => {
        if (item.value) {
          quick_str.push(item.value);
        }
      });
    }
    if (contributeAll) {
      quick_str.unshift(-999);
    }
    return quick_str;
  }

  parseQuickStrToQuotaList(quick_str = []) {
    let quotaList = [];
    let contributeAll = false;
    if (quick_str && Array.isArray(quick_str)) {
      quick_str.forEach((item, index) => {
        if (item !== -999) {
          quotaList.push({ key: index, value: item });
        } else {
          contributeAll = true;
        }
      });
    }
    // console.log(quotaList);

    return { contributeAll, quotaList };
  }

  submitHandler() {
    const { updateState } = this.props;
    const { contributeAll, quotaList } = this.state;
    // console.log(quotaList);
    const quickStr = this.parseQuotaListToQuickStr(quotaList, contributeAll);
    // console.log(quickStr)
    updateState({
      quick_str: quickStr
    });

    this.setState({
      modalVisible: false
    })
  }

  render() {
    const { modalVisible, quotaList, donateType, quotaValue, contributeAll } = this.state;
    const { form, updateState, state } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const ModalProps = {
      title: "快捷额度设置",
      visible: modalVisible,
      footer: false,
      onCancel: () => {
        this.setState({
          quotaList: [],
          modalVisible: false
        });
      },
      width: 600,
      className: "quota-contribute-modal",
      destroyOnClose: true,
      maskClosable: false
    };
    // console.log(donateType);
    // console.log(this.props);
    return (
      <div className="contribute-setting-container">
        <FormItem
          label="捐赠方式"
          {...adaptiveLayout.formWrapper}
        >
          <div className="contribute-setting-wrapper">
            {/* donateType 捐赠方式 1:自由捐赠 2:定额捐赠*/}
            {
              getFieldDecorator("donate_type", {
                // initialValue: state.donate_type,
                // valuePropName: "checked"
              })(
                <RadioGroup className="contribute-setting-wrapper"
                // onChange={(e) => {
                //   const target = e.target;
                //   const value = target.value;
                //   this.setState({
                //     donateType: value
                //   });
                //   updateState({
                //     donate_type: donateType
                //   });
                // }}
                >
                  <div className="free-contribute-wrapper">
                    <Radio value={1}>自由捐赠</Radio>
                    {
                      getFieldValue("donate_type") === 1 &&
                      <a href="javascript:void(0)" onClick={() => {
                        this.setState({
                          donateType: 1,
                          modalVisible: true
                        });
                        updateState({
                          donate_type: 1
                        });
                      }}>额度设置</a>
                    }
                  </div>
                  <div className="quota-contribute-wrapper">
                    <Radio value={2}>定额捐赠</Radio>
                  </div>
                </RadioGroup>
              )
            }

            {/* donate_num 定额捐赠时 设置的捐赠数额*/}
            {
              getFieldValue("donate_type") === 2 &&
              <div className="quota-input-wrapper">
                <span className="quota-input-label">捐赠额度</span>
                {getFieldDecorator("donate_num", {
                  // initialValue: state.donate_type,
                  // valuePropName: "checked"
                })(
                  <InputNumber min={1} step={1} precision={0} placeholder="请输入额度"
                    onFocus={() => {
                      this.setState({
                        donateType: 2
                      });
                      updateState({
                        donate_type: 2
                      });
                    }}
                  />
                )}
              </div>
            }
          </div>
        </FormItem>
        <Modal {...ModalProps}>
          <div className="content-wrapper">
            {/* <div className="content-label">
              快捷额度设置
            </div> */}
            <div style={{ paddingLeft: 10 }}>
              <Checkbox checked={contributeAll} onChange={(e) => {
                const target = e.target;
                const checked = target.checked;
                // console.log(checked);
                this.setState({
                  contributeAll: checked
                });
              }}>捐赠会员全部积分</Checkbox>
            </div>
            <div className="list-wrapper">
              {
                (quotaList && Array.isArray(quotaList)) &&
                quotaList.map((item, index) => {
                  if (item === -999) {
                    return null;
                  }
                  else {
                    return (
                      <div className="item-wrapper" key={item.key}>
                        <InputNumber placeholder="请输入额度" value={item.value} step={1} precision={0} onChange={(value) => {
                          this.changeQuotaItem(index, value);
                        }} />
                        <span className="icon-wrapper" onClick={() => {
                          this.removeQuotaItem(index);
                        }}>
                          <SelfIcon type="gsg-shanchu6" />
                        </span>
                      </div>
                    )
                  }
                })
              }
            </div>
            <div className="add-item-wrapper">
              <a href="javascript:void(0)" onClick={() => {
                let { quotaItemKey } = this.state;
                quotaList.push({
                  key: quotaItemKey,
                  value: null
                });
                quotaItemKey++;
                this.setState({
                  quotaItemKey,
                  quotaList
                });
              }}>
                增加快捷额度
              </a>
            </div>
          </div>
          <div className="buttons-wrapper">
            <Button type="primary" onClick={() => this.submitHandler()}>
              提交
            </Button>
            <Button onClick={() => {
              this.setState({
                contributeAll: false,
                quotaList: []
              });
            }}>
              重置
            </Button>
            <Button onClick={() => {
              this.setState({
                contributeAll: false,
                quotaList: [],
                quotaValue: null,
                modalVisible: false
              });
            }}>
              取消
            </Button>
          </div>
        </Modal>
      </div >
    )
  }
}

ContributeSetting.propTypes = {
  updateState: propTypes.func,
  isPreview: propTypes.bool,
  data: propTypes.object,
  state: propTypes.object,
  form: propTypes.object,
  onChange: propTypes.func
};

ContributeSetting.defaultProps = {
  updateState: () => { },
  isPreview: false,
  data: {},
  state: {},
  form: {},
  onChange: (data) => { console.log("default onChange", data) }
};

export default Form.create({
  onFieldsChange(props, changedFields) {
    const { updateState } = props;
    let result = {};
    for (let key in changedFields) {
      result[key] = changedFields[key].value;
    }
    updateState(result);
  },
  mapPropsToFields: (props) => {
    const { state } = props;
    const result = {};
    result.donate_type = Form.createFormField({
      name: "donate_type",
      value: state.donate_type
    });
    result.donate_num = Form.createFormField({
      name: "donate_num",
      value: state.donate_num
    });
    return result
  }
})(ContributeSetting);
