.organize-selector-wrapper {
  .selector-trigger {
    display: inline-block;
    text-decoration: underline;
  }
  .select-count {
    display: inline-block;
    margin-left: 15px;
  }
  .organize-count {
    color: #333;
    // margin-left: 15px;
  }
  .organize-table {
    // border: 1px solid #E4E4E4;
    display: flex;
    flex: auto; // .organize-count {
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   flex-shrink: 0;
    //   width: 130px;
    //   color: #333;
    //   background-color: #F3F5F8;
    // }
    .organize-table-wrapper {
      flex-grow: 1;
    }

    .ant-table-thead > tr > th {
      background-color: #f3f5f8;
    }
  }
  .organize-text {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    .o-t-box {
      display: flex;
      align-items: center;
      span {
        font-size: 14px;
        line-height: 1;
      }
      svg {
        margin-left: 4px;
        cursor: pointer;
      }
    }
  }
}
