import { findByOrg } from "client/apis/cadre-portrait";
import SelectInput from "./SelectInput";

export default Ant.Form.create()(function ModalForm({ form }) {
  const { getFieldDecorator, setFieldsValue } = form;
  const [option, setOption] = React.useState([]);

  const findByOrgInfo = (orgName) => {
    findByOrg({ org_name: orgName }).then((res) => {
      if (res.data.code === 0) {
        setOption(res.data.data.slice(0, 10));
      } else {
        message.error(res.data.message);
      }
    });
  };

  const onSelect = ({ org_name, org_id }) => {
    setFieldsValue({
      org_name,
      org_id,
    });
  };

  return (
    <Ant.Form>
      <Ant.Form.Item label="班子名称">
        {getFieldDecorator("org_name", {
          rules: [{ required: true, message: "请输入班子名称" }],
        })(
          <SelectInput
            placeholder="请输入"
            onSearch={findByOrgInfo}
            onSelect={onSelect}
            option={option}
          />
        )}
      </Ant.Form.Item>
      <div style={{ display: "none" }}>
        <Ant.Form.Item label="组织id">
          {getFieldDecorator("org_id", {
            rules: [{ required: true, message: "请输入组织id" }],
          })(<Ant.Input />)}
        </Ant.Form.Item>
      </div>
    </Ant.Form>
  );
});
