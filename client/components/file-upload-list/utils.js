import axios from "axios";
import { Gateway } from 'client/apis/config';
import { headers as getHeaders } from 'client/tool/axios';

/**
 * 预览图片，返回url
 * @param {string} fileId ---必传 文件id
 */
 export const previewImage = (fileId) => {
  return new Promise((resolve, reject) => {
    axios.get(`${Gateway}/file/file/download/${fileId}`, {
      headers: { "Content-Type": "application/json" },
      responseType: "blob",
    })
      .then((response) => {
        const file = new FileReader();
        file.readAsDataURL(response);// 读取文件保存在result中
        file.onload = (e) => {
          const getRes = e.target.result;// 读取的结果在result中
          resolve(getRes)
        }
      })
      .catch((error) => {
        console.error("预览图片失败", error);
        reject();
      });
  });
}

// export interface AjaxOption {
//   type?: any;
//   dataType?: any;
//   data?: any;
//   url?: any;
//   success?: any;
//   error?: any;
//   onprogress?: any;
//   xhr?: (xhr: XMLHttpRequest) => void;
// }
export function ajax(options) {
  options = options || {};  // 调用函数时如果options没有指定，就给它赋值{},一个空的Object
  options.type = (options.type || "GET").toUpperCase();/// 请求格式GET、POST，默认为GET
  options.dataType = options.dataType || "json";    // 响应数据格式，默认json

  const params = formatParams(options.data);// options.data请求的数据

  const xhr = new XMLHttpRequest();;
  // 传递实例
  if (options.xhr) options.xhr(xhr);

  xhr.upload.onprogress = (event) => {
    if (event.lengthComputable) {//
      const complete = Number((event.loaded / event.total * 100).toFixed(1));
      if (options.onprogress) options.onprogress(Math.round(complete));
    }
  }
  // 启动并发送一个请求
  if (options.type === "GET") {
    xhr.open("GET", `${options.url}?${params}`, true);
    xhr.send(null);
  } else if (options.type === "POST") {
    xhr.responseType = 'json';
    xhr.open("post", options.url, true);
    // 设置表单提交时的内容类型
    // Content-type数据请求的格式
    const headers = {
      _hs: 1008766642,
      _menu_id: 1302,
      "x-csrf-token": typeof window !== "undefined" ? window.sessionStorage.getItem("csrf") || "" : "",
      ...getHeaders(),
    }
    for (const key in headers) {
      if (headers.hasOwnProperty(key)) {
        const element = headers[key];
        if (key !== "Content-Type") {
          xhr.setRequestHeader(key, encodeURI(element));
        }
      }
    }
    xhr.setRequestHeader("Accept", "application/json");
    xhr.setRequestHeader("Accept", "text/plain");
    xhr.setRequestHeader("Accept", "*/*");
    xhr.send(options.data);
  }
  xhr.onreadystatechange = () => {
    if (xhr.readyState === 4) {
      const { status, response } = xhr;
      if (status >= 200 && status < 300 || status === 304) {
        if (response.status === 200) {
          options.success && options.success(response.data);
        } else {
          console.error(response.message);
        }
      } else {
        options.error && options.error(response);
      }
    }
  }


}

// 格式化请求参数
function formatParams(data) {
  const arr = [];
  for (const name in data) {
    arr.push(`${encodeURIComponent(name)}=${encodeURIComponent(data[name])}`);
  }
  arr.push((`v=${Math.random()}`).replace(".", ""));
  return arr.join("&");
}