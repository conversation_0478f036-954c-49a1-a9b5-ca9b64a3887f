/**
 * 选择物品
 * <AUTHOR>
 * 2018/4/23
 */
import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import {
    Tag,
    Modal,
    Table
} from 'antd'

class SelectCommodityModal extends React.Component {
    constructor() {
        super()

        this.state = {
            selectedData: []
        }
    }

    componentDidMount() {
        this.tableColumns = [{
            title: '物品名称',
            dataIndex: 'name',
            key: 'name'
        }, {
            title: '操作',
            dataIndex: 'operating',
            key: 'operating',
            width: 100,
            render: (text, record, index) => {
                let isSelected = false
                for (const item of this.state.selectedData) {
                    if (+item.prize_id === +record.prize_id) {
                        isSelected = true
                        break
                    }
                }
                return <a href="javascript:;" onClick={this.handleSelect.bind(this, record, isSelected)}>{isSelected ? '取消选择' : '选择'}</a>
            }
        }]
    }

    //选择
    handleSelect(record, selected) {
        let tempSelectedData = {}
        this.state.selectedData.forEach(item => {
            tempSelectedData[item.prize_id] = item
        })
        if (selected) {
            delete tempSelectedData[record.prize_id]
        } else {
            tempSelectedData[record.prize_id] = record
        }

        const selectedData = Object.values(tempSelectedData)
        this.setState({ selectedData })
    }

    handleChangePagination({ current, pageSize }) {
        const { dispatch, selectCommodity } = this.props
        dispatch({
            type: 'selectCommodity/getPrizeList',
            payload: {
                page: current,
                pageSize
            }
        })
    }

    handleConfirm() {
        const { onOk } = this.props
        onOk(this.state.selectedData)
        this.setState({ selectedData: [] })
    }

    handleClose() {
        const { onCancel } = this.props
        onCancel()
        this.setState({ selectedData: [] })
    }

    render() {
        const { visible, selectCommodity } = this.props

        return <Modal
            width={800}
            visible={visible}
            title="选择物品"
            okText="确定"
            cancelText="取消"
            onCancel={this.handleClose.bind(this)}
            onOk={this.handleConfirm.bind(this)}
            destroyOnClose>
            <div style={{ marginBottom: 10 }}>
                {
                    this.state.selectedData.length > 0 && '已选择物品：'
                }
                {this.state.selectedData.map(item => <Tag
                    key={item.prize_id}
                    closable
                    onClose={this.handleSelect.bind(this, item, true)}>
                    {item.name}
                </Tag>)}
            </div>
            <Table
                rowKey={record => record.prize_id}
                loading={selectCommodity.loading}
                pagination={{
                    simple: true,
                    current: selectCommodity.currentPage,
                    pageSize: selectCommodity.pageSize,
                    total: selectCommodity.total
                }}
                onChange={this.handleChangePagination.bind(this)}
                dataSource={selectCommodity.commodityData}
                columns={this.tableColumns} />
        </Modal>
    }
}

SelectCommodityModal.defaultProps = {
    visible: false
}

SelectCommodityModal.propTypes = {
    visible: PropTypes.bool.isRequired,
    onOk: PropTypes.func.isRequired,
    onCancel: PropTypes.func.isRequired
}

export default connect(({ selectCommodity }) => ({ selectCommodity }))(SelectCommodityModal)