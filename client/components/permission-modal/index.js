import React, { Component } from "react";
import PropTypes from "prop-types";
import { Modal, message, Tree, Button, Input, Select } from "antd";
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
const Option = Select.Option;
const { TreeNode } = Tree;
export default class Menumodal extends Component {
  constructor(props) {
    super(props);
    this.unflatten = this.unflatten.bind(this);
    this.renderTreeNodes = this.renderTreeNodes.bind(this);
    this.onCheck = this.onCheck.bind(this);
    this.onchangeInput = this.onchangeInput.bind(this);
    this.onChangeBelong = this.onChangeBelong.bind(this);
    this.onSave = this.onSave.bind(this);
    this.edit = this.edit.bind(this);
    this.save = this.save.bind(this);
    this.getMenuList = this.getMenuList.bind(this)
    this.state = {
      tree: [],
      checkedKeys: [],
      role_name: "",
      belong: 1
    };
  }
  unflatten(arr) {
    let tree = [],
      mappedArr = {},
      arrElem,
      mappedElem;

    // First map the nodes of the array to an object -> create a hash table.
    for (let i = 0, len = arr.length; i < len; i++) {
      arrElem = arr[i];
      mappedArr[arrElem["menu_id"]] = arrElem;
      mappedArr[arrElem["menu_id"]]["title"] = arrElem.name;
      mappedArr[arrElem["menu_id"]]["children"] = [];
    }
    for (let id in mappedArr) {
      if (mappedArr.hasOwnProperty(id)) {
        mappedElem = mappedArr[id];
        // If the element is not at the root level, add it to its parent array of children.
        if (
          mappedElem["parent_id"] &&
          mappedArr.hasOwnProperty(mappedElem["parent_id"])
        ) {
          mappedArr[mappedElem["parent_id"]]["children"].push(mappedElem);
        }
        // If the element is at the root level, add it to first level elements array.
        else {
          tree.push(mappedElem);
        }
      }
    }
    return tree;
  }
  componentDidMount() {

  }
  getMenuList(value) {
    http.get(`${host}/uc/menu/all`, value).then(d => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(d.message);
      }
      this.setState({
        tree: this.unflatten(_data.data)
      });
    });
  }
  componentWillReceiveProps(next) {
    this.getMenuList({belong: next.belong || 1})
    this.setState({
      role_name: next.role_name || "",
      checkedKeys: next.checkedKeys,
      belong: next.belong || 1,
      role_id: next.role_id || undefined
    });
  }
  onCheck(checkedKeys, e) {
    this.setState({
      checkedKeys
    });
  }
  onChangeBelong(val) {
    this.getMenuList({belong: val})
    this.setState({
      belong: val
    });
  }
  renderTreeNodes(data) {
    return data.map(item => {
      if (item.children) {
        return (
          <TreeNode title={item.title} key={item.menu_id} dataRef={item}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode {...item} />;
    });
  }
  onchangeInput(e) {
    this.setState({
      role_name: e.target.value
    });
  }
  edit(value) {
    const { handleCancel, handleBack } = this.props;
    http.post(`${host}/uc/role/update-role`, value).then(data => {
      const _data = data.data;
      if (_data.code != 0) {
        return message.error(_data.message);
      }
      handleCancel();
      handleBack()
    });
  }
  save(value) {
    const { handleCancel, handleBack } = this.props;
    http.post(`${host}/uc/role/add-role`, value).then(data => {
      const _data = data.data;
      if (_data.code != 0) {
        return message.error(_data.message);
      }
      handleCancel();
      handleBack()
    });
  }
  onSave() {
    const header = headers();
    const { _oid } = header;
    const { type, root } = this.props;
    const { role_name, checkedKeys, belong } = this.state;
    // 新增权限
    if (type == 0) {
      if (!role_name) {
        return message.error("请输入权限名称");
      }
      if (checkedKeys.checked.length == 0) {
        return message.error("请选择对应的权限");
      }
      this.save({
        role_name,
        menu_list: checkedKeys.checked,
        belong,
        org_id: root || _oid
      });
    } else {
      this.edit({
        role_name,
        menu_list: checkedKeys.checked,
        belong,
        role_id: this.state.role_id
      });
    }
  }
  render() {
    const { type, visible, handleCancel } = this.props;
    return (
      <div>
        <Modal
          title={type == 1 ? "修改权限" : "新增权限"}
          visible={visible}
          onCancel={handleCancel}
          footer={
            <Button type={"primary"} onClick={this.onSave}>
              保存
            </Button>
          }
        >
          <Input
            addonBefore="权限名称:"
            className="global-role-modal-input"
            style={{ width: 300, marginRight: 15 }}
            value={this.state.role_name}
            onChange={this.onchangeInput}
          />
          <Select
            className="global-role-modal-select"
            value={this.state.belong}
            onChange={this.onChangeBelong}
            disabled={type == 1 ? true : false}
            style={{ width: 120 }}
          >
            <Option value={1}>PC端</Option>
            <Option value={2}>微信端</Option>
          </Select>
          <Tree
            checkable
            onCheck={this.onCheck}
            checkedKeys={this.state.checkedKeys}
            checkStrictly
          >
            {this.renderTreeNodes(this.state.tree)}
          </Tree>
        </Modal>
      </div>
    );
  }
}

Menumodal.propTypes = {
  checkedKeys: PropTypes.array, // 被勾选的列
  visible: PropTypes.bool, // 图层可视属性
  handleCancel: PropTypes.func, // 关闭方法
  handleBack: PropTypes.func, // 回调
  role_name: PropTypes.string, // 权限名称
  type: PropTypes.number, // title显示新增还是修改 1 修改 0 新增
  belong: PropTypes.number, // 菜单所属 1 pc 2 wx
  root: PropTypes.number
};

Menumodal.defaultProps = {
  type: 0,
  checkedKeys: [],
  visible: false,
  role_name: "",
  belong: 1,
  handleCancel: () => {},
  handleBack: () => {}
};
