import { useState, useEffect, useMemo } from "react";
import SearchHeader from "components/search-header";
import { Radio, Button } from "antd";
import Setting from "./components/Setting";
import Record from "./components/Record";
import "./index.less";

const AppointmentManagement = ({ history, location: { state } }) => {
  const [tabKey, setTabKey] = useState(0);
  const [dataSource, setDataSource] = useState();

  useEffect(() => {
    setDataSource({ time: 20 });
  }, [tabKey]);

  const el = useMemo(() => {
    return !tabKey ? (
      <Setting state={state} />
    ) : (
      <Record state={state} />
    );
  }, [dataSource]);

  return (
    <div className="AppointmentManagement">
      <SearchHeader onBack={() => history.goBack()} title="预约管理" />
      <div className="btn-wrap">
        <div className={`btn ${!tabKey && "btn-active"}`} onClick={() => setTabKey(0)}>预约设置</div>
        <div className={`btn ${tabKey && "btn-active"}`} type="link" onClick={() => setTabKey(1)}>预约记录</div>
      </div>
      {/* <Radio.Group
        value={tabKey}
        onChange={({ target: { value } }) => setTabKey(value)}
      >
        <Radio.Button value="0">预约设置</Radio.Button>
        <Radio.Button value="1">预约记录</Radio.Button>
      </Radio.Group> */}
      <main className="AppointmentManagement-main">{el}</main>
    </div>
  );
};
export default AppointmentManagement;
