/*
 * @Description: 
 * @Author: b<PERSON><PERSON>
 * @Date: 2020-11-19 17:45:04
 * @LastEditTime: 2024-11-26 16:51:24
 * @LastEditors: lhl
 */
const { http } = require("client/tool/axios");
import { pushHost, MsgCenter } from "./config";

export const interfacePushRecordList = (payload) => {
  return http.post(`${pushHost}/pushRecord/list`, payload);
};
export const interfaceDeleteRecord = (payload) => {
  return http.post(`${pushHost}/pushRecord/deleteRecord`, payload);
};
export const interfacePushRecordDetail = (payload) => {
  return http.post(`${pushHost}/pushRecord/detail`, payload);
};
export const interfaceUserList = (payload) => {
  return http.post(`${pushHost}/pushRecord/userList`, payload);
};
export const interfaceDeleteRecordUser = (payload) => {
  return http.post(`${pushHost}/pushRecord/deleteRecordUser`, payload);
};
export const interfaceSame = (payload) => {
  return http.post(`${pushHost}/global/push/same`, payload);
};
export const interfacePushRecordUser = (payload) => {
  return http.post(`${pushHost}/retry/pushRecordUser`, payload);
};
export const interfacePushRecord = (payload) => {
  return http.post(`${pushHost}/retry/pushRecord`, payload);
};
//获取微信消息模板列表
export const getWechatTemplateList = ({ oid, app_id, parse_content = 1 }) => {
  return http.get(
    `${MsgCenter}/template/wechat_template_list?oid=${oid}&app_id=${app_id}&parse_content=${parse_content}`
  );
};

// 下载cxcel模板
export const downloadExcelUserList = () => {
  return http.get(`${pushHost}/download-excel/user-list`);
};

// 主题推送
export const pushMsg = (payload) => {
  return http.post(`${pushHost}/global/push/topic`, payload);
};
