import { logout, getParam } from "tool/util";
import axios from "axios";
const querystring = require("querystring"),
  qs = require("qs"),
  // { notification, message } = require("antd"),
  {
    hash,
    Base64,
    getKey,
    padNumber,
    sdbm,
    aes
  } = require("client/tool/encryptParams"),
  Gateway = `${process.env.getway}`,
  getOid = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_oid") != null
        ? window.sessionStorage.getItem("_oid")
        : "1";
    } else {
      return "-1";
    }
  },
  getRegionId = () => {
    const { region_id } = getParam();
    return region_id ? region_id : __TOG__.regionId ? __TOG__.regionId : (process.env.region_id || '-1')
  };
// 常规请求axios实例
const instance = axios.create({
  // baseURL: 'http://127.0.0.1:7001/api/',
  baseURL: Gateway,
  withCredentials: false,
  headers: {
    // 'Access-Control-Allow-Credentials': 'true',
    "Content-Type": "application/json",
    "access_key": accessKey,
    "_oid": getOid(),
    "_region_id": -1,
  },
  // 响应数据格式
  responseType: "json",

  // 超时处理  开发环境忽略超时
  timeout: process.env.IS_DEV ? 3000000 : 1200000
}),
  handleRequest = ({ type = "get", url, option = {}, data = {} }) => {
    try {
      const headers = {
        // 'Access-Control-Allow-Credentials': 'true',
        "Content-Type": "application/json",
        "access_key": accessKey,
        "_oid": getOid(),
        "_region_id": getRegionId(),
      };
      // console.log('header 信息:', headers);
      option.headers = option.headers || headers;
      switch (type) {
        case "post":
          return instance.post(url, data, option);
        case "delete":
          return instance.delete(url, { params: data, headers: option.headers });
        case "get":
        default:
          return instance.get(url, { params: data, headers: option.headers });
      }
    } catch (err) {
      // notification.error({
      //   message: "请求错误",
      //   description: err
      // });
      console.error(err);
    }
  };
//请求拦截器
instance.interceptors.request.use(
  config => {
    // 加密算法不采用DES，采用AES
    const isAES = true;
    // console.log(config, accessKey);
    // console.log('全局变量', isEncrypted);
    // console.log(config);
    //序列化参数时，允许重复参数名存在，例如?id=1&id=2&id=3...
    config.paramsSerializer = params => {
      // console.log('-------------------', params);
      return qs.stringify(params, { arrayFormat: "repeat" });
    };
    let iv = padNumber(Math.abs(sdbm(config.headers['access_key'])), 16);
    //将原url去除请求地址和网关的部分
    let queryTarget = config.url.replace(config.baseURL, "");
    // console.log(queryTarget);
    let hostURL = config.baseURL.endsWith("/")
      ? config.baseURL.substring(0, config.baseURL.length - 1)
      : config.baseURL;
    // console.log('hostURL->', hostURL);

    // 获取queryString并处理
    let action = queryTarget.split("?");
    // console.log(config.params);
    let queryParams = qs.stringify(config.params, { arrayFormat: "repeat" });
    // console.log(queryParams);
    // 获取请求体
    let queryBody = config.data ? JSON.stringify(config.data) : "";
    // console.log(queryParams);
    // console.log(queryBody);
    // 获取请求hash
    let hs = hash(
      `${queryTarget}${queryParams ? "?" + queryParams : ""}`,
      queryBody
    );
    // console.log(`${queryTarget}${queryParams ? "?" + queryParams : ""}`, queryBody);

    // 获取KEY时
    let keyHex = getKey(hs, isAES);
    // console.log(keyHex);

    // console.log('链接拼凑参数', action);
    // console.log('有请求params', queryParams);
    // console.log('有请求body', queryBody);

    let queryString = "";
    //请求参数加密控制
    if (action.length > 1) {
      queryString = Base64.encode(aes(keyHex, action[1], iv));
    } else if (queryParams) {
      queryString = Base64.encode(aes(keyHex, queryParams, iv));
    }
    if (queryString && isEncrypted) {
      console.log('有请求params', action[1] || queryParams);
      config.params = {
        query_string: queryString
      };
      // config.params.query_string = queryString;
    }
    // console.log(queryString);
    // console.log(queryBody);
    // 请求体加密控制
    if (queryBody && isEncrypted) {
      console.log('有请求body data', queryBody);
      let body = Base64.encode(aes(keyHex, queryBody, iv));
      config.data = body;
    }
    // config.params
    // 添加请求头
    config.headers["_hs"] = hs;
    // console.log(config);
    // 返回请求
    return config;
  },
  error => {
    console.error(error);
  }
);
//响应拦截器
instance.interceptors.response.use(
  response => {
    // console.log(response);
    // if (response.data.code !== 0 && response.data.code !== 125) {
    //   throw new Error(`${response.data.message}`);
    // }
    return response;
  },
  (error, headers) => {
    if (
      error.response &&
      error.response &&
      error.response.data &&
      error.response.data.code
    ) {
      if (
        error.response.data.code === 9905 ||
        error.response.data.code === 119 ||
        error.response.data.code === 122 ||
        error.response.data.code === 141 ||
        error.response.data.code === 9900 ||
        error.response.data.code === 9908
      ) {
        logout().then(response => {
          let { data: body } = response,
            { code } = body;
          if (code === 0) {
            if (typeof window !== "undefined") {
              // window.location.reload();
              //`${error.response.data.message}`
              // message.error('登录状态异常，请重新登录', () => {
              // });
              if (location.search) {
                location.href = `/login${location.search}&error=loginStatusError`
              } else {
                location.href = `/login?error=loginStatusError`
              }
            }
          }
        });
        // throw new Error('登录状态异常，请重新登录');
      }
    }
    throw new Error(
      `请求异常: ${error.message === "timeout of 8000ms exceeded"
        ? "请求超时"
        : error.message
      }`
    );
  }
);
module.exports = {
  // 浏览器端的请求
  http: {
    get(url, data = {}, option = {}) {
      return handleRequest({ url, data, option });
    },
    post(url, data = {}, option = {}) {
      return handleRequest({
        url,
        option,
        data,
        type: "post"
      });
    },
    delete(url, data = {}, option = {}) {
      return handleRequest({
        url,
        option,
        data,
        type: "delete"
      });
    }
  }
};
