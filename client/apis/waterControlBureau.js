import { http, filterParamsValue } from 'client/tool/axios'

import { cloud } from './config';
// 40102020801-新增栏目 (支持新增子栏目) http://wiki.aidangqun.com/project/8?p=1626
export const addSourceColumn = (data) => {
  return http.post(`${cloud}/addColumn`, data);
}

// 40102020802-编辑栏目 http://wiki.aidangqun.com/project/8?p=1627
export const  editSourceColumn = (data) => {
  return http.post(`${cloud}/columnEdit`, data);
}

// 40102020803-删除栏目 (以及下级所有栏目) http://wiki.aidangqun.com/project/8?p=1628
export const deleteSourceColumn = (data) => {
  return http.post(`${cloud}/columnDel`, data);
}

// 40102020804-栏目列表 (查询下级) http://wiki.aidangqun.com/project/8?p=1629
export const fetchSourceColumns = (data) => {
  return http.get(`${cloud}/columnList`, data);
}

// 50102020813-移动栏目 http://wiki.aidangqun.com/project/8?p=1637
export const moveSourceColumn = (data) => {
  return http.get(`${cloud}/moveColumn`, data);
}

// 40102020811-资源列表高级筛选 http://wiki.aidangqun.com/project/8?p=1635
export const getSourceList = (data) => {
  return http.post(`${cloud}/resourceList`, data);
}

// 40102020810-删除资源 http://wiki.aidangqun.com/project/8?p=1634
export const deleteSource = (data) => {
  return http.post(`${cloud}/resourceDel?resource_id=${data.resource_id}`);
}

// 40102020814-置顶 http://wiki.aidangqun.com/project/8?p=1638
export const setSourceTop = (params) => {
  return http.get(`${cloud}/resourceTop`, params);
}

//  40102020809-移动排序 http://wiki.aidangqun.com/project/8?p=1633
export const setResourceIndex = (params) => {
  return http.get(`${cloud}/resourceMoveSort`, params);
}

// 黄云 id_list  : 所选id column_id  :  移动的栏目id
export const batchMoveResource = (data) => {
  return http.post(`${cloud}/allMoveResource`, data);
}



