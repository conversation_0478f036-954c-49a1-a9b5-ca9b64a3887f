import React, { PureComponent, memo } from "react";
import {
  Layout,
  Row,
  Col,
  Button,
  Table,
  Dropdown,
  Icon,
  <PERSON>u,
  <PERSON>confirm,
  Modal,
} from "antd";
import SelfIcon from "components/self-icon";
import SearchHeader from "./search-header";
import { fileDownload } from "client/components/file-download";
import { getDataDictionary } from "client/apis/active-organization";
import FilePollingDownload from "client/components/file-download/FilePollingDownload";
import SearchForm from "./new-search-from";

const { Content } = Layout;
const { SubMenu } = Menu;

class mainContent extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      partyVisible: false,
      joinTypeList: [],
      delTypeList: [],
    };
    // 表单前缀
    this.prefixColumns = [
      {
        title: "序号",
        dataIndex: "key",
        key: "key",
        align: "center",
        width: 70,
      },
    ];
    this.userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");

    this.permissionList = this.userInfo.element.map(
      (item) => item && item.element_id
    );

    this.renderMenuItem = this.renderMenuItem.bind(this);
    this.onAddParty = this.onAddParty.bind(this);
  }
  async componentDidMount() {
    // 进入支部方式
    const joinTypeList = await this.getDataDictionary(1044);
    // 党员离开类型
    const delTypeList = await this.getDataDictionary(1045);
    this.setState({
      joinTypeList,
      delTypeList,
    });
  }
  onAddParty(val) {
    const { openAddMember } = this.props.tableProps;
    openAddMember && openAddMember(val);
  }
  async getDataDictionary(code) {
    const { data } = (await getDataDictionary({ code })).data;
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.has_child === 1) {
        item.children = await this.getDataDictionary(item.op_key);
      }
    }
    return data;
  }
  renderMenuItem(codeList) {
    return codeList.map((item) => {
      if (item.has_child === 1) {
        return (
          <SubMenu title={item.op_value} key={item.op_key}>
            {this.renderMenuItem(item.children || [])}
          </SubMenu>
        );
      } else {
        return <Menu.Item key={item.op_key}>{item.op_value}</Menu.Item>;
      }
    });
  }

  render() {
    const {
      statisData,
      searchProps,
      tableProps,
      _this,
      isShizhi,
      isRootId,
      siderMemberProps,
      tableHeadConfig,
      orgTreeTabSelectData,
    } = this.props;
    const { org_type } = orgTreeTabSelectData || {};
    const rowSelection = tableProps.rowSelection;
    const columns = [
      {
        title: "序号",
        align: "center",
        width: 50,
        render: (val, row, index) => 10 * (tableProps.pageNum - 1) + index + 1,
      },
      {
        title: "姓名",
        align: "center",
        dataIndex: "user_name",
      },
      {
        title: "手机号",
        align: "center",
        dataIndex: "phone_secret",
      },
      {
        title: "所在组织",
        align: "center",
        dataIndex: "affiliated_org_name",
      },
      {
        title: "报到时间",
        align: "center",
        dataIndex: "check_time",
      },
      {
        title: "操作",
        align: "center",
        render: (val, row, index) => (
          <Popconfirm
            placement="top"
            title={"确认删除？"}
            onConfirm={() => tableProps.onDeleteMember(row.user_vigor_org_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        ),
      },
    ];
    return (
      <div className="main-container">
        <Row className="cardLayout">
          <Col>
            <div className="card">
              <SelfIcon
                className="card-icon"
                style={{ color: "#EFB95B" }}
                type="gsg-zuzhirenyuanshuliang"
              />
              <div className="item">
                <p>活力型党组织数量</p>
                <b>{statisData.vigor_org_num}</b>
              </div>
              <div className="item">
                <p>报到党员人数</p>
                <b>{statisData.vigor_user_num}</b>
              </div>
            </div>
          </Col>
        </Row>
        {/*😓
         <SearchHeader {...searchProps} _this={_this} /> */}
        <SearchForm
          {...searchProps}
          _this={_this}
          ref={searchProps.getSearchFormRef}
        />
        <Table
          rowKey="key"
          className={"table"}
          // rowSelection={rowSelection}
          scroll={{ x: true }}
          columns={columns}
          dataSource={tableProps.dataSource}
          loading={tableProps.isLoading}
          bordered
          pagination={
            tableProps.dataSource.length
              ? {
                  pageSize: 10,
                  // current: tableProps.pageNum,
                  total: tableProps.pageTotal,
                  onChange: tableProps.onPageChange,
                  showTotal: (total, range) => {
                    return `当前显示${range.join("至")}条`;
                  },
                }
              : false
          }
        />
      </div>
    );
  }
}

export default memo(mainContent);
