import { http } from 'tool/axios';
import { mtHost } from "./config";

// 05-议题详情 /topic/detail?topic_id=123
export const fetchTopicDetail = ({ mt_id, topic_id }) => {
    return http.get(`${mtHost}/result/topic/detail`, { mt_id, topic_id });
}

// /result/topic/answer 02-填写纪实情况表-填写 / 修改 议题答案
export const postResultTopicDetail = (data = {}) => {
    return http.post(`${mtHost}/result/topic/answer`, data);
}