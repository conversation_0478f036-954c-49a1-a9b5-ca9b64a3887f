import { Button, Form, Icon, message, Modal, Upload } from 'antd'
import { apiImport } from 'client/apis/cadre-portrait'
import { Fragment, useState } from 'react'
import './ImportModal.less'
function ImportModal({ form, visible, onOk, onCancel, org_id }) {
  const {
    getFieldsValue,
    getFieldValue,
    getFieldDecorator,
    resetFields,
    validateFields,
    setFieldsValue,
  } = form
  const [fileList, setFileList] = useState([])
  const [errorList, setErrorList] = useState([])
  const [submitLoad, setSubmitLoad] = useState(false)

  // 确定按钮
  const _onOk = async () => {
    await validateFields()
    const { user_type, has_divided } = getFieldsValue()

    const formData = new FormData()
    // 将每个文件添加到 FormData 对象
    Array.from(fileList).forEach((file) => {
      formData.append('files', file)
    })
    formData.append('org_id', org_id)
    has_divided && formData.append('has_divided', has_divided)
    // formData.append('user_type', user_type)
    setSubmitLoad(true)
    const { data: resData, status, statusText } = await apiImport(formData)
    setSubmitLoad(false)
    if (status !== 200) {
      message.error(statusText)
      return
    }
    const { code, data, messag: msg } = resData
    if (code === 0) {
      const { error_list = [] } = data || {}
      setErrorList(error_list)
      if (error_list.length === 0) {
        _onCancel()
        message.success("上传成功")
        onOk && onOk()
      }
    } else {
      message.error(msg)
    }
  }
  // 取消按钮
  const _onCancel = () => {
    onCancel()
    setErrorList([])
    setFileList([])
    resetFields()
    setSubmitLoad(false)
  }

  // 新增上传操作
  const handleFileUpChange = ({ file }) => {
    // 检查文件扩展名是否为 .lrmx 或 .doc 或 .docx
    if (/\.(lrmx|doc|docx)$/.test(file.name.toLowerCase())) {
      setFileList([...fileList, file]);
      setFieldsValue({ files: [...fileList, file] });
    } else {
      message.error(`上传文件只支持 lrmx、doc 或 docx 文件格式，${file.name} 文件已自动删除！`);
    }
    validateFields(['files']);
  };
  // 删除所选文件
  const handleDelFile = (uid) => {
    const fileterFileList = fileList.filter((item) => item.uid !== uid)
    setFileList(fileterFileList)
    setFieldsValue({ files: fileterFileList })
    setErrorList([])
  }

  const Footer = () => {
    return (
      <div className="footer" style={{ textAlign: 'center' }}>
        <Button type="primary" onClick={_onOk} loading={submitLoad}>
          确定
        </Button>
        <Button onClick={_onCancel}>取消</Button>
      </div>
    )
  }
  const categoryList = [
    {
      op_value: '市管领导',
      op_key: '1'
    },
    {
      op_value: '县管领导',
      op_key: '2'
    },
    {
      op_value: '中层干部',
      op_key: '3'
    },
  ]
  const hasDividedList = [
    {
      op_value: '正职',
      op_key: '1'
    },
    {
      op_value: '副职',
      op_key: '2'
    },
  ]
  return (
    <Modal
      destroyOnClose
      className="add-form-modal"
      visible={visible}
      width={780}
      onOk={_onOk}
      onCancel={_onCancel}
      title="导入"
      footer={<Footer />}
    >
      <Form>
        {/*
        <Form.Item label={<span>用户类型</span>} required>
          {getFieldDecorator('user_type', {
            rules: [{ required: true, message: '请选择用户类型' }],
          })(
            <Radio.Group
              className="checkbox-layout"
            >
              {categoryList.map((item) => (
                <Radio value={item.op_key} >
                  {item.op_value}
                </Radio>
              ))}
            </Radio.Group>
          )}
        </Form.Item>
        {
          getFieldValue('user_type') === '2' && <Form.Item label={<span>干部职级</span>} required>
            {getFieldDecorator('has_divided', {
              rules: [{ required: true, message: '请选择干部职级' }],
            })(
              <Radio.Group
                className="checkbox-layout"
              >
                {hasDividedList.map((item) => (
                  <Radio value={item.op_key} >
                    {item.op_value}
                  </Radio>
                ))}
              </Radio.Group>
            )}
          </Form.Item>
        }
       */}
        <Form.Item label="干部测评表导入" required>
          {getFieldDecorator('files', {
            rules: [
              { required: true, message: '请上传干部测评表', type: 'array' },
            ],
          })(
            <Fragment>
              <Upload.Dragger
                multiple
                showUploadList={false}
                customRequest={handleFileUpChange}
              >
                <Icon
                  type="cloud-upload"
                  style={{
                    fontSize: '50px',
                    color: '#359AF7',
                  }}
                />
                <div style={{ marginTop: '10px' }}>
                  <span style={{ color: '#359AF7' }}>点击</span>或将文件
                  <span style={{ color: '#359AF7' }}>拖拽</span>到此处上传
                </div>
              </Upload.Dragger>
              {fileList.map((item) => (
                <div key={item.uid} className="file_item">
                  <span>{item.name}</span>
                  <Icon
                    className="del_icon"
                    type="delete"
                    onClick={() => handleDelFile(item.uid)}
                  />
                </div>
              ))}
              {errorList.length > 0 && (
                <Fragment>
                  <div className="error_title">错误信息</div>
                  {errorList.map((item, index) => (
                    <div key={index} className="error_item">
                      <span>{item}</span>
                    </div>
                  ))}
                </Fragment>
              )}
            </Fragment>
          )}
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default Form.create()(ImportModal) //ImportModal
