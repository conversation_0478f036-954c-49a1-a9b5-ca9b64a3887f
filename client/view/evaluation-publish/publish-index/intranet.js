import { connect } from "dva";
import moment from "moment";
import OrgTree from "client/components/org-tree";
import DelayForm from "./DelayForm";

import {
  getEvalList,
  cancelEvaluation,
  closeEvaluation,
  extensionEvaluation,
  resubmitEvaluation,
  syncToEval,
  syncFromPms,
} from "client/apis/evaluation-publish";
import { headers } from "client/tool/axios";
import { CDN, activityHost } from "client/apis/config";
import { createATagDownload } from "client/tool/util";
import { syncDataPercent } from "client/apis/cadre-portrait";

const { message } = Ant;

function IntranetPublishEvaluation(props) {
  const { history, form } = props;

  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const [currentOrgId, setCurrentOrgId] = React.useState(1);
  const [tableData, setTableData] = React.useState();
  const [loading, setLoading] = React.useState(false);
  const [delayVisible, setDelayVisible] = React.useState(false);

  const reqList = (org_id) => {
    const values = getFieldsValue();
    const params = {
      evaluation_name: values.evaluation_name,
      start_time:
        values.range_time &&
        moment(values.range_time[0]).format("YYYY-MM-DD 00:00:00"),
      end_time:
        values.range_time &&
        moment(values.range_time[1]).format("YYYY-MM-DD 23:59:59"),
    };
    setLoading(true);
    getEvalList({ ...params, org_id }).then((res) => {
      if (res.data.code === 0) {
        setTableData(res.data.data);
      }
      setLoading(false);
    });
  };

  React.useEffect(() => {
    if (currentOrgId) reqList(currentOrgId);
  }, [currentOrgId]);

  const openPublish = (type) => {
    history.push(
      `/evaluation-publish/create-evaluation?type=${type}&orgId=${currentOrgId}`
    );
  };

  const openDetail = (row) => {
    if (![8, 9, 10].includes(row.type)) {
      console.error("非测评类型");
      return;
    }
    history.push(
      `/evaluation-publish/create-evaluation?type=${row.type}&orgId=${currentOrgId}&evaluationId=${row.pms_evaluation_id}`
    );
  };

  const [downLoadingData, setDownLoadingData] = React.useState(false);
  const downloadPacket = async () => {
    if (downLoadingData) return;
    setDownLoadingData(true);
    const { data: resName } = await syncToEval();
    let timer;
    let hasEndPre = true;
    if (resName.code === 0) {
      const url = resName.data;
      timer = setInterval(async () => {
        if (!hasEndPre) return;
        hasEndPre = false;
        const { data: res } = await syncDataPercent({
          file_path: url,
        });
        if (res.code !== 0 || res.data === "-1") {
          clearInterval(timer);
          timer = null;
          message.error(res.message);
          setDownLoadingData(false);
        } else if (res.data === "1") {
          // // 创建a标签下载
          createATagDownload(`${CDN}/${url}`);
          clearInterval(timer);
          timer = null;
          setDownLoadingData(false);
        }
        hasEndPre = true;
      }, 1000);
    } else {
      message.error(res.message);
      setDownLoadingData(false);
    }
  };

  // const handleUploadResult = () => {};

  const handleCancel = (row) => {
    cancelEvaluation({ evaluation_id: row.pms_evaluation_id }).then((res) => {
      if (res.data.code === 0) {
        message.success("撤销成功");
        reqList(currentOrgId);
      } else {
        message.error(res.data.message);
      }
    });
  };

  const handleClose = (row) => {
    closeEvaluation({ evaluation_id: row.pms_evaluation_id }).then((res) => {
      if (res.data.code === 0) {
        message.success("结束成功");
        reqList(currentOrgId);
      } else {
        message.error(res.data.message);
      }
    });
  };

  const handleDownload = (row) => {
    history.push(
      `/evaluation-publish/evaluation-code?type=${row.type}&orgId=${currentOrgId}&evaluationId=${row.pms_evaluation_id}`
    );
  };

  const handleDelay = (values, row) => {
    extensionEvaluation({
      end_time: moment(values.end_time).format("YYYY-MM-DD HH:mm:ss"),
      evaluation_id: row.pms_evaluation_id,
    }).then((res) => {
      if (res.data.code === 0) {
        message.success("延期成功");
        reqList(currentOrgId);
        setDelayVisible(false);
      } else {
        message.error(res.data.message);
      }
    });
  };

  // const handleRepublish = (row) => {
  //   resubmitEvaluation({ evaluation_id: row.pms_evaluation_id }).then((res) => {
  //     if (res.data.code === 0) {
  //       message.success("重新发布成功");
  //       reqList(currentOrgId);
  //     } else {
  //       message.error(res.data.message);
  //     }
  //   });
  // };

  const columns = [
    {
      title: "测评类型",
      dataIndex: "type",
      key: "type",
      render: (t) => {
        if (t == "9") return "一般干部测评";
        if (t == "8") return "班子成员测评";
        if (t == "10") return "市管领导测评";
        return "-";
      },
    },
    { title: "测评名称", dataIndex: "evaluation_name", key: "evaluation_name" },
    {
      title: "发布时间",
      dataIndex: "release_time",
      key: "release_time",
      align: "center",
    },
    {
      title: "截至时间",
      dataIndex: "end_time",
      key: "end_time",
      align: "center",
      render: (t) => moment(t).format("YYYY-MM-DD"),
    },
    {
      title: "状态",
      dataIndex: "eval_status",
      key: "eval_status",
      align: "center",
      render: (t) => {
        const map = {
          1: "进行中",
          2: "已结束",
          3: "已撤销",
        };
        return map[t];
      },
    },
    {
      title: "操作",
      dataIndex: "handle",
      key: "handle",
      align: "center",
      width: 280,
      render: (_, record) => {
        if (record.eval_status === 1) {
          return (
            <div>
              <Ant.Button
                type="link"
                size="small"
                onClick={() => openDetail(record)}
              >
                详情
              </Ant.Button>
              <Ant.Button
                type="link"
                size="small"
                onClick={() => handleCancel(record)}
              >
                撤销
              </Ant.Button>
              <Ant.Popconfirm
                title="是否现在结束这个测评?"
                onConfirm={() => handleClose(record)}
                okText="是"
                cancelText="否"
              >
                <Ant.Button type="link" size="small">
                  结束测评
                </Ant.Button>
              </Ant.Popconfirm>
              <Ant.Button
                type="link"
                size="small"
                onClick={() => handleDownload(record)}
              >
                下载测评码
              </Ant.Button>
            </div>
          );
        }
        if (record.eval_status === 2) {
          return (
            <Ant.Button
              type="link"
              size="small"
              onClick={() => setDelayVisible(record)}
            >
              延期
            </Ant.Button>
          );
        }
        if (record.eval_status === 3) {
          return (
            <Ant.Button
              type="link"
              size="small"
              onClick={() => openDetail(record)}
            >
              重新发布
            </Ant.Button>
          );
        }
      },
    },
  ];

  const onTreeChange = (value) => {
    if (Array.isArray(value) && value.length) {
      setCurrentOrgId(value[0]);
    }
  };

  const uploadProps = {
    name: "file",
    action: syncFromPms,
    headers: headers(),
    showUploadList: false,
    data: {
      // task_id: content.task_id || "",
      // type: 1,
    },
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} 上传失败.`);
      }
    },
  };

  return (
    <div style={{ background: "#f1f5f8" }}>
      <Ant.Row gutter={8} style={{ height: "100%" }}>
        <Ant.Col span={4} style={{ height: "100%" }}>
          <Ant.Card bodyStyle={{ padding: 16 }} style={{ height: "100%" }}>
            <OrgTree onChange={onTreeChange} />
          </Ant.Card>
        </Ant.Col>
        <Ant.Col span={20} style={{ height: "100%" }}>
          <Ant.Card style={{ height: "100%" }}>
            <Ant.Form layout="inline">
              <Ant.Form.Item label="测评时间">
                {getFieldDecorator("range_time")(
                  <Ant.DatePicker.RangePicker
                    style={{ width: "100%" }}
                    placeholder={["开始时间", "结束时间"]}
                  />
                )}
              </Ant.Form.Item>
              <Ant.Form.Item label="测评名称">
                {getFieldDecorator("evaluation_name")(
                  <Ant.Input placeholder="请输入" />
                )}
              </Ant.Form.Item>
              <Ant.Form.Item>
                <Ant.Button
                  type="primary"
                  onClick={() => reqList(currentOrgId)}
                >
                  查询
                </Ant.Button>
              </Ant.Form.Item>
              <Ant.Form.Item>
                <Ant.Button
                  onClick={() => {
                    resetFields();
                    reqList(currentOrgId);
                  }}
                >
                  重置
                </Ant.Button>
              </Ant.Form.Item>
            </Ant.Form>
            <Ant.Row gutter={8} type="flex" style={{ margin: "16px 0" }}>
              <Ant.Col>
                <Ant.Dropdown
                  overlay={
                    <Ant.Menu>
                      <Ant.Menu.Item>
                        <a onClick={() => openPublish("10")}>市管领导</a>
                      </Ant.Menu.Item>
                      <Ant.Menu.Item>
                        <a onClick={() => openPublish("8")}>班子成员</a>
                      </Ant.Menu.Item>
                      <Ant.Menu.Item>
                        <a onClick={() => openPublish("9")}>一般干部</a>
                      </Ant.Menu.Item>
                    </Ant.Menu>
                  }
                >
                  <Ant.Button
                    type="primary"
                    style={{ background: "#F46E65", borderColor: "#F46E65" }}
                  >
                    发布测评
                    <Ant.Icon type="down" />
                  </Ant.Button>
                </Ant.Dropdown>
              </Ant.Col>
              <Ant.Col>
                <Ant.Button
                  type="primary"
                  loading={downLoadingData}
                  onClick={downloadPacket}
                >
                  下载测评数据包
                </Ant.Button>
              </Ant.Col>
              <Ant.Col>
                <Ant.Upload {...uploadProps}>
                  <Ant.Button
                    style={{ borderColor: "#47b9ff", color: "#47b9ff" }}
                    // onClick={handleUploadResult}
                  >
                    上传测评结果
                  </Ant.Button>
                </Ant.Upload>
              </Ant.Col>
            </Ant.Row>
            <Ant.Table
              loading={loading}
              bordered
              columns={columns}
              rowKey="pms_evaluation_id"
              dataSource={tableData || []}
              pagination={false}
            />
          </Ant.Card>
        </Ant.Col>
      </Ant.Row>

      <DelayForm
        visible={!!delayVisible}
        record={delayVisible}
        onCancel={() => setDelayVisible(false)}
        onOk={handleDelay}
      />
    </div>
  );
}

export default connect(({ organizeData, userInfo }) => ({
  organizeData,
  userInfo,
}))(Ant.Form.create()(IntranetPublishEvaluation));
