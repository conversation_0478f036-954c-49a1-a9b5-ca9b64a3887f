import React from "react";
import "./index.less";
import { Modal, message as Message, But<PERSON> } from "antd";
import propTypes from "prop-types";
import Top from "./sub-components/form";
import Bottom from "./sub-components/table";
import { getcodeList } from "apis/users";

class Class extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // 字典表查询基础条件
      baseCode: 1028,
      // 下拉框可选项目
      typeList: [],
      // 表格数据（假分页）
      dataSource: [],
      // 下拉框选中项目
      selectedType: null,
      // 下拉框选中键值
      selectedTypeId: null,
      // 下拉框选中名称
      selectedTypeName: null,
      // 表格loading
      tableLoading: false,
      // 选中项目
      checkedList: [],
      // 选中项目
      checkedListCopy: []
    };
  }

  componentWillMount() {
    const { baseCode } = this.state;
    const { input } = this.props;
    if (input) {
      this.setState({
        checkedList: JSON.parse(JSON.stringify(input)),
        checkedListCopy: JSON.parse(JSON.stringify(input))
      });
    }

    this.fetchCodeList({ code: baseCode }, 1);
  }

  componentWillReceiveProps(props) {
    const { input } = props;
    if (input) {
      this.setState({
        checkedList: JSON.parse(JSON.stringify(input)),
        checkedListCopy: JSON.parse(JSON.stringify(input))
      });
    }
  }

  resetHandler() {
    this.setState({
      // 选中项目
      checkedList: []
    })
  }

  // type为1时，查询结果填充下拉框,type为2时，查询结果填充表格
  async fetchCodeList(queryparams = {}, type = 2) {
    const { defaultSelectorOrgType } = this.props;
    this.setState({
      tableLoading: true
    });
    const response = await getcodeList(queryparams);
    // console.log(response);
    const { data: body } = response;
    const { data, code, message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (data && Array.isArray(data) && data[0] && type === 1) {
      // defaultSelectorOrgType,
      let target = data.find((item, index) => {
        return item.op_key === defaultSelectorOrgType;
      });
      if (!target) {
        target = data[0];
      }
      this.setState({
        selectedType: target ? target : data[0],
        selectedTypeId: target ? target.op_key : data[0].op_key,
        selectedTypeName: target ? target.op_value : data[0].op_value,
        typeList: data
      }, () => {
        this.fetchCodeList({ code: target.op_key });
      });
    } else {
      this.setState({
        tableLoading: false,
        dataSource: data
      });
    }
  }

  render() {
    const {
      selectedType,
      selectedTypeId,
      typeList,
      dataSource,
      tableLoading,
      checkedList,
      checkedListCopy
    } = this.state;
    const {
      width,
      visible,
      updateState,
      onChange,
      onCancel,
      defaultSelectorOrgType,
      lockSelector,
    } = this.props;
    const modalProps = {
      title: "选择组织类型",
      visible,
      footer: null,
      maskClosable: false,
      width,
      bodyStyle: {},
      wrapClassName: "org-type-selector-modal",
      onCancel: () => {
        // console.log("关闭模态框");
        onCancel && onCancel();
      }
    },
      topProps = {
        typeList,
        selectedType,
        selectedTypeId,
        lockSelector,
        onChange: (payload = {}) => {
          // console.log("onChange", payload);
          if (payload) {
            this.setState({
              selectedType: payload,
              selectedTypeId: payload.op_key,
              selectedTypeName: payload.op_value,
            }, () => {
              this.fetchCodeList({ code: payload.op_key }, 2);
            })
          }
        },
      },
      bottomProps = {
        checkedList,
        selectedType,
        dataSource,
        tableLoading,
        onChange: (payload = {}) => {
          const { checked, item } = payload;
          const { checkedList = [] } = this.state;
          if (item && checkedList && Array.isArray(checkedList)) {
            const index = checkedList.findIndex((data) => {
              return data.op_key === item.op_key;
            });
            if (checked && index === -1) {
              checkedList.push(item);
            } else if (!checked && index !== -1) {
              checkedList.splice(index, 1);
            }
            this.setState({
              checkedList
            });
          }
        }
      };
    return (
      <Modal {...modalProps}>
        <Top {...topProps} />
        <Bottom {...bottomProps} />
        <div className="buttons-wrapper">
          <Button type="primary" onClick={() => {
            // console.log("确定", checkedList);
            const result = JSON.parse(JSON.stringify(checkedList));
            onChange && onChange(result, () => {
              onCancel && onCancel();
            });
          }}>确定</Button>
          <Button onClick={() => {
            console.log("取消", checkedList, checkedListCopy);
            this.setState({
              checkedList: checkedListCopy
            }, () => {
              onCancel && onCancel();
            });
          }}>取消</Button>
        </div>
      </Modal>
    )
  }
}

Class.propTypes = {
  width: propTypes.number,
  visible: propTypes.bool,
  updateState: propTypes.func,
  onChange: propTypes.func,
  onCancel: propTypes.func,
  // 传入默认的组织类型
  defaultSelectorOrgType: propTypes.number,
  lockSelector: propTypes.bool
}

Class.defaultProps = {
  width: 800,
  visible: true,
  updateState: () => { },
  onChange: () => { },
  onCancel: () => { },
  defaultSelectorOrgType: null,
  lockSelector: false,
}

export default Class;