.cadre-information-maintenance {
  flex: 1 !important;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  .cim-content-box {
    flex: 1;
    overflow: auto;
  }
  .save-button-box {
    width: 100%;
    display: flex;
    align-items: center;
    padding-left: 120px;
    margin: 50px 0px 100px 0px;
    button {
      width: 150px;
      height: 40px;
    }
  }
}
body:has(.cadre-information-maintenance) {
  min-width: 1637px;
}
