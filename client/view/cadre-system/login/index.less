.new-login {
  width: 100%;
  height: 100%;
  background: url(./image/bg.png) no-repeat center / cover;
  overflow: hidden;
  .inner-box {
    margin: 9vh auto 0;
    width: 1072px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .header {
      width: 100%;
      height: 204px;
      background: url(./image/header.png) no-repeat center / contain;
    }
    .form-box {
      padding: 46px;
      width: 454px;
      height: 380px;
      // background: linear-gradient(
      //   to bottom,
      //   rgba(218, 241, 255, 0.7) 100%,
      //   rgba(218, 241, 255, 0.1) 0%
      // );
      background: url(./image/login-img.png) no-repeat center / cover;
      border-radius: 16px;
      .login-form-panel {
        margin-top: 30px;
        .verification-code-input {
          .ant-form-item-children {
            display: block;
          }
          .verification-code-wrapper {
            position: absolute; // background-color: #898D98;
            color: #fff;
            right: 4px;
            top: 2px;
            z-index: 1;
            width: 100px;
            height: 38px;
            text-align: center;
            line-height: 38px;
            .verification-code {
              display: inline-block;
              box-sizing: border-box;
              -moz-box-sizing: border-box;
              -webkit-box-sizing: border-box;
              width: 100%;
              height: 100%;
              cursor: pointer;
              border: 1px solid #000;
            }
          }
        }
        .ant-input-affix-wrapper {
          input {
            padding-left: 42px;
          }
        }
        .verification-code-input {
          &.ant-input-affix-wrapper {
            input {
              padding-right: 110px;
            }
          }
        }
        .input-icon {
          display: inline-block;
          width: 22px;
          height: 22px;
          background-size: 100% 100%;
        } //border: 1px solid red;
        .ant-input-affix-wrapper {
          input {
            padding-left: 42px;
            min-height: auto;
          }
        }
        .ant-input:focus {
          -webkit-box-shadow: none;
          -moz-box-shadow: none;
          box-shadow: none;
        }
        input {
          background-color: #fff;
          outline: 0;
          &:focus {
            border-color: #1e8cf1 !important;
            border-width: 1px;
          }
          border-color: #eeeeee;
        }

        input:-webkit-autofill {
          background-color: #fff;
          background-image: none;
          -webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;
          -webkit-text-fill-color: #3e3e3e !important;
        }

        .ant-btn.login-form-button.image-button {
          display: block;
          background-size: 100% 100%;
          width: 380px;
          height: 70px;
          border: none;
          outline: none;
          &.ant-btn-clicked:after {
            //取消按钮点击周围光晕效果
            animation: none; //-webkit-box-shadow: none;
            //-moz-box-shadow: none;
            //box-shadow: none;
            //border: none;
            //outline: none;
          }
        }
        .ant-btn.login-form-button {
          display: block;
          width: 100%;
          height: 40px;
          padding: 0;
          margin: 0;
        }
        .submit-wrapper {
          margin-bottom: 25px;
          .login-form-button.image-button {
            display: block;
            background-size: 100% 100%;
            color: #fff;
            font-size: 18px;
            width: 100%;
            height: 50px;
            line-height: 50px;
            border: none;
            outline: none;
            text-align: center;
            letter-spacing: 2px;
            padding: 0;
            margin: 0 auto;
            cursor: pointer;
            background: linear-gradient(86deg, #1e8cf1 0%, #48a8ff 100%);
            box-shadow: 0px 4px 4px 0px rgba(96, 179, 255, 0.35);
          }
        }
      }
      input[type="text"],
      input[type="password"],
      input[type="number"],
      textarea {
        font-size: 14px;
        background-color: #f7f8f9;
        height: 46px;
      }
      .ant-btn-primary {
        height: 46px;
        background-color: #f46e65;
        border-color: #f46e65;
      }
      .getCode {
        position: relative;
        .get-code-button-wrapper {
          position: absolute;
          right: 5px;
          top: 3px;
        }
        .ant-btn.get-code-button {
          font-size: 14px;
          width: 100px;
          height: 38px;
          border: 2px solid #f41d19;
          color: #f46e65;
          border-radius: 19px;
        }
      }
      .unit-list-wrapper {
        flex-grow: 1;
        overflow-y: auto;
        li.unit-item-wrapper {
          position: relative;
          padding: 0 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          font-size: 16px;
          max-width: 380px;
          height: 44px;
          line-height: 44px;
          border-bottom: 1px solid #898d98;
          cursor: pointer;
          .unit-item-name-wrapper {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .anticon-arrow-right {
            color: #ccc;
          }
          /* .icon {
                  &.list-item-icon {
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    right: 14px;
                    top: 14px;
                  }
                } */
          &:hover {
            color: #f41d19;
            .anticon-arrow-right {
              color: #f41d19;
            }
          }
        }
      }
      .inp-tip {
        position: absolute;
        width: 50px;
        left: 20px;
        top: -20px;
        font-size: 14px;
        line-height: 14px;
        color: #999;
        height: 14px;
        background-color: #fff;
        padding: 0 6px;
        text-align: center;
      }
      .hide {
        display: none;
      }
      .forget-link {
        display: block;
        text-align: right;
        color: #359af7;
      }
      .register-button {
        width: 100%;
      }
      .footer-wrapper {
        display: flex;
        align-content: center;
        justify-content: space-between;
      }
      .section-wrap {
        position: absolute;
        bottom: -20px;
        width: 100%;
        text-align: center;
        color: #d4d4d4;
        font-size: 16px;
        .title {
          position: relative;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 100%;
            height: 1px;
            background-color: #f6f6f6;
          }
          &::after {
            content: "其他系统登录";
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 2;
            padding: 0 12px;
            transform: translate(-50%, -50%);
            background-color: #fff;
          }
        }
      }
    }

    .org-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 454px;
      height: 380px;
      border-radius: 16px;
      border: 2px solid #d1edff;
      background-image: linear-gradient(
        to bottom,
        rgba(218, 241, 255, 0.7) 100%,
        rgba(218, 241, 255, 0.1) 0%
      );
      .title {
        margin-top: 37px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 24px;
        color: #11528d;
        line-height: 28px;
      }
      .select-org {
        flex: 1;
        overflow: auto;
        margin-top: 17px;
        display: flex;
        flex-direction: column;
        gap: 10px 0px;
        .org-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 362px;
          padding: 17px 20px;
          background: rgba(255, 255, 255, 0.5);
          border-radius: 4px 4px 4px 4px;
          cursor: pointer;
          .org-icon {
            display: inline-block;
            height: 36px;
            width: 36px;
            background: url(./image/org-icon.png) no-repeat center / cover;
          }
          .to-icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            background: url(./image/to-icon.png) no-repeat center / cover;
          }
          .org-name {
            margin-left: 18px;
            flex: 1;
            font-weight: 400;
            font-size: 18px;
            color: #11528d;
            line-height: 21px;
            text-align: left;
          }
        }
      }
    }
  }
}
