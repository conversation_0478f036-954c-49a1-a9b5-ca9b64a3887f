import React from "react";
import PropTypes from "prop-types";
import { fileHost } from "apis/config";
import { message } from 'antd';
import IndexView from './index-view';
import TaskModal from './modal';
import "client/components/eval-task-viewbox/index.less";
import "../examination-task-view/eval-task-viewbox.less";
import "./style.less";

import {
    taskDetails, taskOrNotList, evalFlow, taskResult, getContNotCommitNumber, getContNotCommit
} from "apis/appraisals";

class AuditTaskDetail extends React.Component {
    constructor() {
        super();
        this.state = {
            id: null,
            notCommitNum: 0,
            isLoading: false,
            searchParam: '',
            selectVal: 1,
            isTabsLoading: false,
						tabsIndex: 1,
            dataSource: {
                eval_entity: null,
                eval_task_entity: null,
                eval_file_entities: null,
                eval_org_entity: null
            },
            modalProps: {
                orgId: null,
                isLoading: false,
                isVisible: false,
                isEdit: false,
                title: null,
                isSubmitLoading: false,
                dataSource: null
            }
        }
    }
  componentDidMount() {
    const { history } = this.props;
    const { query } = history.location;
    let id;

    if (query) {
      id = query.id;
      window.sessionStorage.setItem('__audit_eval_id', id);
    } else {
      id = window.sessionStorage.getItem('__audit_eval_id');
    }

    if (id) {
      this.setState({
        id
      }, async () => {
        await this.taskDetails({ id });
        this.onNotCommit(id)
      });
    }
  }
    //获取未提交数量
    async onNotCommit(eval_id){
        let res = (await getContNotCommitNumber({
            eval_id,
        })).data;
        if(res.code === 0 ){
            this.setState({
                notCommitNum: res.data
            })
        }
    }
    async taskOrNotList(payload = {}) {
        const { id, value='' } = payload;
        const { tabsIndex, dataSource, selectVal, id : task_id } = this.state;
        let taskId = id;
        if (!id) {
            taskId = task_id;
        }
        if (!taskId) {
            return message.error('任务id不存在');
        }
        
        
        this.setState({
            isTabsLoading: true,
            dataSource: Object.assign(dataSource, {
                eval_org_entity: null
            })
        });

        let result = null;
        if(tabsIndex*1 === 3) {
            result  = (await getContNotCommit({
                eval_id: taskId,
            })).data;
            let arr = []
            for (const val of result.data) {
                arr.push({
                    eval_org_entity: {
                        eval_org_id: arr.length+1,
                        org_name: val
                    }
                })
            }
            result.data = arr
        }else{
            result = (await taskOrNotList({
                eval_id: taskId,
                type: tabsIndex
            })).data;
        }
        this.setState({
            isTabsLoading: false
        });
        if (result.code != 0) {
            return message.error(result.message);
        }
        let data = result.data;
        //有值时进行过滤
        data = data.filter(org=>{
            if(selectVal != 1){
                return org.eval_org_entity.org_name.includes(value) && org.eval_org_entity.status == selectVal
            }
            //默认查全部
            return org.eval_org_entity.org_name.includes(value)
        })
        this.setState({
            dataSource: Object.assign(dataSource, {
                eval_org_entity: data
            })
        });
    }
    async taskDetails(payload = {}) {
        const { id } = payload;
        let taskId = id;
        if (!id) {
            taskId = this.state.id;
        }
        if (!taskId) {
            return message.error('任务id不存在');
        }

        this.setState({
            isLoading: true
        });

        const result = (await taskDetails({ eval_id: taskId })).data;
        const { dataSource, tabsIndex } = this.state;

        if (result.code != 0) {
            this.setState({
                isLoading: false
            });
            return message.error(result.message);
        }

        const resultList = (await taskOrNotList({
            eval_id: taskId,
            type: tabsIndex
        })).data;

        this.setState({
            isLoading: false
        });
        if (resultList.code != 0) {
            return message.error(result.message);
        }

        const data = result.data;
        this.setState({
            dataSource: Object.assign(dataSource, {
                eval_entity: data.eval_entity,
                eval_task_entity: Object.assign(data.eval_task_entity, {
                    create_time: data.eval_entity.create_time,
                    eval_year: data.eval_entity.eval_year,
                    eval_cycle: data.eval_entity.eval_cycle,
                    eval_date: data.eval_entity.eval_date,
                    approval_time: data.eval_task_entity.approval_time
                }),
                eval_file_entities: data.eval_file_entities,
                eval_org_entity: resultList.data
            })
        });
    }

    async evalFlow(payload = {}) {
        const { org_id, org_name, isEdit } = payload;
        const { id, modalProps } = this.state;

        if (!org_id) {
            return message.error('考核任务id不存在');
        }

        this.setState({
            modalProps: Object.assign(modalProps, {
                orgId: org_id,
                title: org_name,
                isVisible: true,
                isEdit: isEdit,
                isSubmitLoading: false,
                isLoading: true
            })
        });

        const params = {
            eval_id: id,
            org_id,
            is_commit: isEdit ? 1 : 2
        }

        const result = (await evalFlow(params)).data;

        this.setState({
            modalProps: Object.assign(modalProps, {
                isLoading: false
            })
        });
        if (result.code !== 0) {
            return message.error(result.message);
        }

        const data = result.data;

        let files = [];
        if (isEdit) {
            if (data.files && data.files.length) {
                files = data.files.map((item) => {
                    return {
                        ...item,
                        date_time: data.create_time
                    }
                })
            }
            this.setState({
                modalProps: Object.assign(modalProps, {
                    isLoading: false,
                    dataSource: (Object.assign(data, {
                        files
                    })) || {}
                })
            });
        } else {
            if (data.last_task_commit && data.last_task_commit.files && data.last_task_commit.files.length) {
                files = data.last_task_commit.files.map((item) => {
                    return {
                        ...item,
                        date_time: data.last_task_commit.create_time
                    }
                });
            }

            this.setState({
                modalProps: Object.assign(modalProps, {
                    isLoading: false,
                    dataSource: (Object.assign(data, {
                        files,
                        summary: data.last_task_commit && data.last_task_commit.descriptive_opinions,
                        recordList: data.execution_record_list || []
                    })) || {}
                })
            });
        }
    }

    render() {
        const { id, dataSource,searchParam, tabsIndex, isLoading, isTabsLoading, modalProps, notCommitNum,selectVal } = this.state;
        const { history } = this.props;
        const appProps = {
            isLoading,
            isTabsLoading,
            tabsIndex,
            dataSource,
            notCommitNum,
            searchParam,
            id,
            selectVal,
            headerProps: {
                title: "任务审核详情",
                onBack() {
                    history.goBack();
                }
            },
            onChangeSelect: (val) => {
                this.setState({
                    selectVal: val
                })
            },
            onChangeTab: (index,value) => {
                const {selectVal} = this.state;
                this.setState({
                    tabsIndex: index,
                    searchParam: value,
                    selectVal: index != 2 ? 1 : selectVal
                }, async () => {
                    await this.taskOrNotList({value});
                });
            },
            onOpen: (record) => {
                this.evalFlow({
                    org_id: record.eval_org_entity.org_id,
                    org_name: record.eval_org_entity.org_name,
                    isEdit: record.isEdit
                });
            },
            onChangeSearch:(e)=>{
                this.setState({
                    searchParam: e.target.value
                })
            },
        };

        const modalViewProps = {
            orgId: modalProps.orgId,
            isEdit: modalProps.isEdit,
            isLoading: modalProps.isLoading,
            isSubmitLoading: modalProps.isSubmitLoading,
            title: modalProps.title,
            isVisible: modalProps.isVisible,
            dataSource: modalProps.dataSource,
            onCancel: () => {
                this.setState({
                    modalProps: Object.assign(modalProps, {
                        title: null,
                        isVisible: false,
                        isEdit: false,
                        isSubmitLoading: false,
                        isLoading: false,
                        dataSource: null,
                    })
                });
            },
            onSubmit: (payload = {}) => {
                const form = payload.form;
                const orgId = payload.orgId
                if (!payload.orgId) {
                    return message.error('被审批的组织id不存在');
                }
                form.validateFieldsAndScroll(async (error, values) => {
                    if (!error) {
                        this.setState({
                            modalProps: Object.assign(modalProps, {
                                isSubmitLoading: true
                            })
                        });

                        values.eval_id = id;
                        values.org_id = orgId;

                        const result = (await taskResult(values)).data;
                        this.setState({
                            modalProps: Object.assign(modalProps, {
                                isSubmitLoading: false
                            })
                        });
                        if (result.code != 0) {
                            return message.error(result.message);
                        }

                        this.setState({
                            modalProps: Object.assign(modalProps, {
                                title: null,
                                isVisible: false,
                                isEdit: false,
                                isLoading: false,
                                dataSource: null
                            })
                        });

                        this.taskOrNotList();
                    }
                });
            }
        }

        return (
            <div className="audit-task-detail">
                <IndexView {...appProps} />
                <TaskModal {...modalViewProps} />
            </div>
        );
    }
}

// AuditTaskDetail.propTypes = {
//   desc: PropTypes.string
// };
// AuditTaskDetail.defaultProps = {
//   desc: "接口功能开发升级中，敬请期待…"
// }

export default AuditTaskDetail;
