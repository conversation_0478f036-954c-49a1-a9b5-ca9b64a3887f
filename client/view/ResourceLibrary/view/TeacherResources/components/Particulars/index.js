import React, { useState, useEffect } from "react";
import <PERSON>Header from "components/search-header";
import { Input, Icon, message, Carousel } from "antd";
import { findById, collectionEvent } from "apis/resource-library";
import { CDN } from "apis/config";
import './index.less';

const {
    user_id,
    user_name,
    oid: org_id,
    name: org_name,
    regionId: region_id,
} = JSON.parse(sessionStorage.getItem("UserInfo") || "{}");

const Particulars = (props) => {
    const {
        history,
        // location: {
        //     state: { keyWord },
        // },
    } = props;
    const { previewModalData, table = 0 } = props
    const [dataSource, setDataSource] = useState({})
    const [imgList, setImgList] = useState('')
    const [content, setContent] = useState('')
    const [name, setName] = useState('')
    const [tags, setTags] = useState([])
    const Name = new RegExp(!table && props.location.state.name, "g");
    const KeyWord = new RegExp(!table && props.location.state.keyWord, "g");
    useEffect(() => {
        // console.log(props);
        if (!table) {
            // 埋点
            collectionEvent({
                user_id,
                user_name,
                org_id,
                org_name,
                region_id,
                type: "L",
                object: props.location.state.resource_library_id,
                channal: "ResourceManager",
                transfer_time: new Date().getTime(),
                amount: 1,
            });
            initDataSource();

        }
        // if (keyWord) {
        //     setkeyword(keyWord);
        // }
    }, []);


    const initDataSource = () => {
        findById({ resourceLibraryId: props.location.state.resource_library_id }).then(
            ({ data: res }) => {
                if (res.code !== 0) {
                    message.error(res.message);
                    return;
                }
                setDataSource(res.data);
                setContent(res.data.content)
                setTags(res.data.tags ? res.data.tags.split(',') : [])
                setName(res.data.name)
                console.log(res.data.cover_url);
                const imgs =
                    res.data.resource_files &&
                    res.data.resource_files.map((item) => {
                        if (/\.(jpg|jpeg|png|JPG|PNG|JPEG)$/i.test(item.path)) {
                            return item;
                        }
                    });
                setImgList(imgs);
            }
        );
    };
    console.log(props);
    return (
        <div className="Particulars">
            {!table && <SearchHeader title={'资源详情'} onBack={() => history.goBack()} />}
            <div className="Content" style={{ padding: !table && '0px 224px' }}>
                <div className="Particulars-information">
                    <img className="Particulars-information-img" src={table ? (previewModalData.cover_url ? `${CDN}/${previewModalData.cover_url}` : require('../../../../image/ren.png')) : (dataSource.cover_url ? `${CDN}/${dataSource.cover_url}` : require('../../../../image/ren.png'))} />
                    <div className="Particulars-information-content">
                        <div className="name"
                            dangerouslySetInnerHTML={{
                                __html:
                                    table ? previewModalData.name : name.replace(
                                        Name,
                                        `<span style="color: red;">${props.location.state.name}</span>`
                                    ),
                            }}
                        >
                        </div>
                        <div className="unit">{(table ? previewModalData : dataSource).belong_unit}</div>
                        <div className="label">
                            {(table ? (previewModalData.tags ? previewModalData.tags.split(',') : []) : tags).map((item) => {
                                return (
                                    item && <div className="tag">{item} </div>

                                )
                            })}
                        </div>
                        <div className="technical-post">
                            <div style={{ marginRight: 60, wordWrap: 'normal' }}>职务：{table ? (previewModalData.job ? previewModalData.job : '无') : (dataSource.job ? dataSource.job : '无')}</div>
                            <div>职级：{table ? (previewModalData.profession_level ? previewModalData.profession_level : '无') : (dataSource.profession_level ? dataSource.profession_level : '无')}</div>
                        </div>
                        <div className="technical-post">联系方式：{table ? previewModalData.contact_details : dataSource.contact_details}</div>
                        <div className="technical-post">
                            信息提供：{(table ? previewModalData : dataSource).provider_org_name}
                        </div>
                    </div>
                </div>
                <div
                    className="Particulars-content"
                    style={{ marginLeft: table === 1 && 0 }}
                    dangerouslySetInnerHTML={{
                        __html:
                            table ? previewModalData.content : content.replace(
                                KeyWord,
                                `<span style="color: red;">${props.location.state.keyWord}</span>`
                            ),
                    }}
                // dangerouslySetInnerHTML={{
                //     __html: (table ? previewModalData.content : content),
                // }}
                ></div>
            </div>
        </div >
    )

}
export default Particulars