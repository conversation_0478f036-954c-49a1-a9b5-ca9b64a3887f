import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { <PERSON>r } from 'antd';

/**
 * 字典级联选择器
 */
class DicCascader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      myValue: undefined,
      cache: undefined,
      options: [],
    };
    this.onValueChange = this.onValueChange.bind(this);
    this.initCodeList = this.initCodeList.bind(this);
    this.getCodeList = this.getCodeList.bind(this);
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.code !== this.props.code) {
      this.initCodeList(nextProps.code);
    } 
  }
  componentDidMount() {
    const { code } = this.props;
    this.initCodeList(code);
  }
  // 初始化级联options
  initCodeList(code) {
    if (code === undefined) {
      return;
    }
    this.getCodeList(code).then(data => {
			const list = [];
			data.map( async (item) => {
				const res = {
					label: item.op_value,
					value: item.op_key
				};
				if (item.has_child) {
					res.children = await this.getCodeList(item.op_key);
				}
				list.push(res);
				return ;
      });
      this.setState({ options: list});
		});
  }
  // 获取字典
  getCodeList(code) {
		const { dispatch } = this.props;
		return new Promise((resolve, reject) => {
			dispatch({
				type: 'dicData/initDicDataByCode',
				payload: {
					code,
					callback: (data) => {
						if (data) {
							resolve(data);
						} else {
							reject(data);
						}
					}
				}
			});
		});
  }
  onValueChange(val) {
    const { onChange } = this.props;
    this.setState({myValue: val});
    if (onChange) onChange(val);
  }
  render() {
    const { myValue } = this.state;
    return (
      <Cascader value={myValue} onChange={this.onValueChange} />
    );
  }
}

export default connect(({ dicData }) => ({ dicData }))(DicCascader);