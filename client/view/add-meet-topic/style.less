.addMeetTopic {
  .contents-wrapper {
    margin-bottom: 36px;
  }
  .ant-col-2.ant-form-item-label {
    width: 90px;
  }
  .ant-col-offset-2.ant-form-item-control-wrapper {
    margin-left: 90px;
  }
  .ant-form-item-control-wrapper {
    min-width: 580px;
  }
  .topicName {
    background: rgba(247, 248, 249, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    border-radius: 4px;
  }
  .uploadWrap>div>div.ant-form-item-control {
    margin-top: 0;
  }
  .upload {
    margin-top: 10px;
  }
  .uploadAtarg {
    text-decoration: underline;
  }
  .textAreaWrap {
    position: relative;
    height: 120px;
    background: rgba(247, 248, 249, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    padding-bottom: 28px;
    border-radius: 4px;
    transition: all .3s;
    .textArea {
      resize: none;
      height: 100%;
      border: none;
      padding-bottom: 30px;
      background: rgba(247, 248, 249, 1);
    }
    .textArea:focus {
      border-color: none !important;
      box-shadow: none;
      & .textAreaWrap {
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }
    .topicDescSizehint {
      position: absolute;
      height: 20px;
      line-height: 20px;
      bottom: 8px;
      right: 12px;
      z-index: 99;
    }
  }
  .textAreaWrap:hover {
    border: 1px solid #ff7875;
  }
  .ant-row.ant-form-item.no-margin {
    .ant-form-item-control {
      margin-top: 0;
    }
  }
  .ant-carousel .slick-slider .slick-list .slick-track .slick-slide {
    overflow: auto;
  }
  .btnWrapper {
    min-width: 120px;
    height: 40px;
  }
}