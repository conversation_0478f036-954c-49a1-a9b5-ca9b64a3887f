.person-modal-new {
  .main-content-wrapper {
    display: flex;
    justify-content: space-between;
    .tree-wrapper-title{
      padding: 15px;
      width: 280px;
      background-color: #f7f8f9;
      border: 1px solid #e5e5e5;
      border-bottom: none;
      font-size:18px;
      font-family:FZLTHK--GBK1-0;
      font-weight:bold;
      color:rgba(51,51,51,1);
      .organize-tree-name{
        display: inline-block;
        padding-bottom: 17px;
        width:218px;
      }
      .ant-select{
        width: 100%;
        font-weight: 400;
      }
      .ant-btn:not(.ant-btn-circle):not(.ant-btn-circle-outline).tree-wrapper-title_btn{
        padding: 0;
        width: 30px;
        height: 30px;
        color: #F46E65;
        font-size: 20px;
        border:none;
        background-color: #f7f8f9;
      }
    }
    .organize-tree-wrapper {
      height: 494px;
      width: 280px;
      // flex-shrink: 0;
      border: 1px solid red;
      overflow: auto;
      background-color: #f7f8f9;
      border: 1px solid #e5e5e5;
      .ant-tree {
        .ant-tree-switcher {
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }
        .current-item{
          min-width: 300px;
          background-color: #F46E65;
          span{
            color: #fff;
          }
        }
      }
    } // 人员选择表格
    .person-table-wrapper {
      display: flex;
      flex-direction: column;
      flex:1;
      height: 610px;
      margin-left: 24px;
      .person-table{
        width: 500px;
        .ant-table-pagination{
          float: left;
        }
        /* .table-org_name{
          display: inline-block;
          width: 200px;
          overflow:hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        } */
      }
      .select-content-wrapper{
        flex: 1;
        display: flex;
      }
      .person-selected-list{
        flex:1;
        margin-bottom: 15px;
        margin-left: 10px;
        border: 1px solid #8FC31F;
        overflow-y: auto;
        .selected-list{
          padding: 5px;
          max-height: 457px;
          overflow-y: auto;
          .ant-btn-group{
            margin:5px 5px 0 0;
          }
          .selected{
            color: #ff9c91
          }
          &_tag{
            height: 38px;
            border-color: #E5E5E5;
            border-radius: 0;
            box-shadow: none;
            &:hover,&:focus{
              border-color:rgb(217, 217, 217);
              color: #F46E65;
            }
            .anticon-close{
              color: #F46E65;
            }
          }
          /* &_tag{
            padding-right: 2px;
            padding-left: 8px;
            border-right: 0;
            font-size:14px;
            font-family:FZLTHJW--GB1-0;
            font-weight:400;
            color:rgba(51,51,51,1);
          }
          &_close{
            margin-right: 8px;
            padding-right: 6px;
            padding-left: 5px;
            border-left: 0;
            color: #F46E65;
            // cursor: pointer;
          } */
        }
        .selected-list-title{
          background-color: #8FC31F;
          line-height: 46px;
          .selected-num{
            display: inline-block;
            width: 160px;
            color: #fff;
            padding-left: 15px;;
          }
        }
        
      }
    }
  }
  .buttons-wrapper {
    margin-top: 32px;
    display: flex;
    justify-content: center;
    .ant-btn {
      width: 115px;
      height: 36px;
      margin: 0 22.5px;
    }
  }
}
.tree-wrapper-propver{
  width: 260px;
  .items{
    margin: -12px;
  }
  .item{
    padding-left: 12px;
    line-height: 40px;
    border-bottom: 1px solid #E4E4E4;
    cursor: pointer;
  }
  .propver-active{
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #F46E65;
    border-color: #F46E65;
  }
  .ant-popover-inner{
    border-radius: 2px;
  }
}