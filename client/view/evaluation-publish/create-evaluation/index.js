import CreateXianguan from "./xianguan";
import CreateShiguan from "./shiguan";

export default function CreateEvaluation(props) {
  const { location } = props;

  const query = new URLSearchParams(location.search);
  const orgId = query.get("orgId");
  const type = query.get("type");
  const evaluationId = query.get("evaluationId");

  const isXianguan = type !== "10";

  return isXianguan ? (
    <CreateXianguan
      type={type}
      orgId={orgId}
      evaluationId={evaluationId}
      {...props}
    />
  ) : (
    <CreateShiguan orgId={orgId} evaluationId={evaluationId} {...props} />
  );
}
