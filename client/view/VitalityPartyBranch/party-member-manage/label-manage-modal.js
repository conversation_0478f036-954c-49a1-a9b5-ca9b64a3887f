import React, { Component } from 'react';
import { Modal, Table, Form, Button, Input, Popconfirm, message } from 'antd';

const FormItem = Form.Item;

const getTitle = (data) => {
    const ret = [];
    data.forEach((val) => {
        ret.push(val.name);
    });
    return `是否删除" ${ret.join('、')} "标签`;
}

export default Form.create()(( {
    form, isDelete, isDeleteVisible, isVisible, isLoading, isSearchLoading, isSubmitLoading, title,
    dataSource, rowSelection,
    onClose, onSubmit, onSearch, onVisibleChange
} ) => {

    const { getFieldDecorator } = form;
    const columns = [{
        title: '标签名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
    }];
    const selectedRowKeys = rowSelection.selectedRowKeys;
    const selectedData = rowSelection.selectedData;

    const submitHandler = () => {
        if(!selectedRowKeys.length){
            return message.error('请选择标签名称');
        }
        form.resetFields();
        onSubmit({
            selectedRowKeys, selectedData, isDelete
        });
    }

    return (
        <Modal
            className="member-manage-label-modal"
            title={title}
            width={700}
            visible={isVisible}
            destroyOnClose={true}
            onCancel= {() => {
               onClose();
            }}
            footer={null}
        >
            <Form className="form" layout='inline'>
                <FormItem label='标签名称' >
                    {getFieldDecorator('label', {
                        initialValue: null
                    })(
                        <Input placeholder='请输入标签名称' style={{ width: 200 }} maxLength={50} />
                    )}
                </FormItem>
                <Button type='primary' loading={isSearchLoading} onClick={() => {
                    onSearch(form);
                }}>查询</Button>
                <Button loading={isSearchLoading} onClick={() => {
                    form.resetFields();
                    onSearch(form, true);
                }}>重置</Button>
            </Form>
            <Table
                rowKey='tag_id'
                loading={isLoading}
                rowSelection={rowSelection}
                dataSource={dataSource}
                columns={columns}
                pagination={false}
                scroll={{ y: 240 }}
                bordered
            />
            <div className="buttons">
                {isDelete ? (
                    <Popconfirm
                        visible={isDeleteVisible}
                        onVisibleChange={(e) => {
                            if(e && !selectedRowKeys.length){
                                onVisibleChange(false);
                                return message.error('请选择标签名称');
                            }
                            onVisibleChange(e);
                        }}
                        overlayStyle={{width:400}}
                        placement="top"
                        title={getTitle(selectedData)}
                        onConfirm={submitHandler}
                    >
                        <Button type='primary' loading={isSubmitLoading}>提交</Button>
                    </Popconfirm>
                ) : (
                    <Button type='primary' loading={isSubmitLoading} onClick={submitHandler}>提交</Button>
                )}
                <Button  onClick={() => {
                    form.resetFields();
                    onClose();
                }}>取消</Button>
            </div>
        </Modal>
    );
})