//TODO:这个类是js生成的
//client/index.js
@import (reference) '../../asset/styles/theme.less';
#draftModal {
    .fixed-bg {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, .65);
        height: 100%;
        z-index: 1000;
        .confirm-modal-wrapper {
            border-radius: 4px;
            width: 420px;
            margin-left: -260px;
            height: 220px;
            margin-top: -110px;
            position: absolute;
            text-align: center;
            top: 50%;
            left: 50%;
            background-color: #fff;
            .tips {
                height: 55px;
                line-height: 55px;
                margin-bottom: 0;
                font-size: 20px;
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                align-items: center;
                .title{
                    font-size:16px; vertical-align: middle;
                }
                .close {
                  
                }
            }
            .message {
                border-top: 1px solid #e8e8e8;
                height: 100px;
                line-height: 100px;
                font-size: 14px;
            }
            .btn-wrapper {
                padding: 0 20px;
                text-align:center;
                .btn {
                    display:inline-block;
                    height: 33px;
                    line-height: 31px;
                    border: 0;
                    border-radius: 3px;
                    margin: 0 10px;
                    padding: 0 15px;
                    &.confirm-save {
                        color: #fff;
                        background-color: @primary-color;
                    }
                    &.confirm-not-save {
                        color: #666666;
                        background: rgba(247, 248, 249, 1);
                        border: 1px solid rgba(229, 229, 229, 1)
                    }
                    &.cancel {
                        color: #666666;
                        background: rgba(247, 248, 249, 1);
                        border: 1px solid rgba(229, 229, 229, 1)
                    }
                    &:last-child {
                        margin-right: 0;
                    }
                }
            }
        }
    }
}