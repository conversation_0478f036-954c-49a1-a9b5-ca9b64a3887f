import { useState, useEffect, useMemo, lazy, Suspense } from "react";
import { But<PERSON>, message, Card, Spin } from "antd";
// import Category from "./components/Category";
// import DoubleCard from "./components/DoubleCard";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import "./index.less";
import { getOrganization, getLineChartTop, getOrganizationRankingTop, getUserRankingTop, getUserTop, getUserRanking, getUser, getBranchTarget } from "apis/Vitality-index";
import moment from "moment";

const CardList = lazy(() => import("./components/CardList"));
const DoubleCard = lazy(() => import("./components/DoubleCard"));
const Category = lazy(() => import("./components/Category"));
const TableIndex = lazy(() => import("./components/TableIndex"));

/**
 * 根据 type 及 cardType 加载不同数据
 * @param {*} type 0县委  1村社  2街道  3支部
 * @returns
 */
const VitalityIndex = (props) => {
  const {
    match: {
      params: { type },
    },
    history
  } = props;
  const [selectorVisible, setSelectorVisible] = useState(false);
  const [orgInfo, setOrgInfo] = useState(() => {
    const { name: org_name, oid: org_id } = JSON.parse(
      sessionStorage.getItem("userInfo")
    );
    return { org_name, org_id };
  });
  const [types, setTypes] = useState(10);
  const [select, setSelect] = useState('');//顶级组织切换村/社党组织概况
  const [searchType, setSearchType] = useState("year");
  const [dateVal, setDateVal] = useState(moment());
  const [res, setRes] = useState([]);
  const [datas, setDatas] = useState([]);
  //党员概况
  const [userRes, setUserRes] = useState([]);
  const [userdatas, setUserDatas] = useState([]);
  const [userdateVal, setUserDateVal] = useState(moment().subtract(1, 'months').startOf('month'));
  //活力党组织指数
  const [Target, setTarget] = useState([]);
  const [TargetTime, setTargetTime] = useState(moment().subtract(1, 'months').startOf('month'));
  const [TargetsearchType, setTargetSearchType] = useState("month");
  useEffect(() => { getTreeListData() }, [orgInfo]);
  useEffect(() => {
    if (types == 0) {
      getLineChartToplist()
      getOrganizationRankingToplist()
      getUserLineChartToplist()
    }
    if (types == 4) {
      getBranchUserLineChartToplist()
      BranchTarget()
    }
    if (types == 3) {
      getBranchUserLineChartToplist()
      getBranchUserlist()
      BranchTarget()
    }
  }, [orgInfo, types]);
  useEffect(() => {
    getBranchUserlist()
  }, [select, searchType, userdateVal, types]);
  useEffect(() => {
    getOrganizationRankingToplist()
  }, [select, searchType, dateVal, types]);
  useEffect(() => {
    if (types == 3) {
      BranchTarget()
    }
    if (types == 4) {
      BranchTarget()
    }
  }, [TargetTime, orgInfo, types]);

  useEffect(() => {
    if (types == 0) {
      getUserToplist()
      getLineChartToplist()
    }
    if (types == 3) {
      getBranchUserlist()
    }

  }, [select, userdateVal, types]);
  //判断是什么组织
  const getTreeListData = async () => {
    const params = {
      is_query_sub: 1,
      org_id: orgInfo.org_id
    }
    const { data: res } = await getOrganization(params);
    if (res.code !== 0) return message.error(res.message);
    console.log('第一部', res);
    let num = --(res.data.orgType)
    console.log('0县委  1村社  2街道  3支部', num);
    setTypes(num)
  };
  //村/社党组织概况折线图
  const getLineChartToplist = async () => {
    const params = {
      org_category: select,
      org_id: orgInfo.org_id
    }
    const { data: res } = await getLineChartTop(params);
    if (res.code !== 0) return message.error(res.message);
    setRes(res)
  };

  //村/社党组织概况排行榜
  const getOrganizationRankingToplist = async () => {
    const date = moment(dateVal).format(
      searchType === "year" ? "YYYY" : "YYYYMM"
    );
    const params = {
      org_category: select,
      org_id: orgInfo.org_id,
      sta_time: date,
    }
    const { data: res } = await getOrganizationRankingTop(params);
    if (res.code !== 0) return message.error(res.message);
    setDatas(res.data)
  };

  //党员概况折线图
  const getUserLineChartToplist = async () => {
    const params = {
      // org_category: select,
      org_id: orgInfo.org_id
    }
    const { data: res } = await getUserRankingTop(params);
    if (res.code !== 0) return message.error(res.message);
    setUserRes(res)
    console.log('党员概况折线图', res);
  };

  //党员概况排行榜
  const getUserToplist = async () => {
    const date = moment(userdateVal).format("YYYY");
    // console.log("🚀 ~ file: index.js:59 ~ date:", date);
    const params = {
      sta_year: date,
      org_id: orgInfo.org_id
    }
    const { data: res } = await getUserTop(params);
    if (res.code !== 0) return message.error(res.message);
    setUserDatas(res.data)
    // console.log('排行榜',res.data);
  };
  //支部党员概况折线图
  const getBranchUserLineChartToplist = async () => {
    const params = {
      // org_category: select,
      org_id: orgInfo.org_id
      // org_id: 41
    }
    const { data: res } = await getUserRanking(params);
    if (res.code !== 0) return message.error(res.message);
    setUserRes(res)
  };
  //支部党员概况排行榜
  const getBranchUserlist = async () => {
    const date = moment(userdateVal).format("YYYY");
    const params = {
      year: date,
      org_id: orgInfo.org_id
      // org_id: 41
    }
    const { data: res } = await getUser(params);
    if (res.code !== 0) return message.error(res.message);
    setUserDatas(res.data)
  };
  //支部活力党组织指标
  const BranchTarget = async () => {
    const date = moment(TargetTime).format(TargetsearchType == "month" ? 'YYYYMM' : "YYYY");
    const params = {
      year: TargetsearchType !== "month" ? date : '',
      org_id: orgInfo.org_id,
      time: TargetsearchType == "month" ? date : ''
    }

    const { data: res } = await getBranchTarget(params);
    if (res.code !== 0) return message.error(res.message);
    setTarget(res.data)
  };

  const onChangeVisible = () => {
    setSelectorVisible(!selectorVisible);
  };

  const organizeModalProps = {
    visible: selectorVisible,
    radio: true,
    dataSource: [],
    hideModal: () => {
      onChangeVisible();
    },
    loadOrganizeData: (data) => {
      // console.log(data);
      data = data.map((d) => {
        if (!d.type) {
          d.type = 2;
        }
        return d;
      });
      const org_info = data[0] || {};
      if (org_info.org_id) {
        setOrgInfo(org_info);
        onChangeVisible();
      } else {
        message.warning("请选择组织");
      }
    },
  };

  const el = useMemo(() => {
    if (types !== 10) {
      const defaultProps = {
        type: types,
        org_id: orgInfo.org_id,
        setSelect: setSelect,
        setSearchType: setSearchType,
        setDateVal: setDateVal,
        searchType: searchType,
        select: select,
        dateVal: dateVal,
        res: res,
        datas: datas,
        history
      };

      //党员概况
      const defaultProps2 = {
        type: types,
        org_id: orgInfo.org_id,
        setSelect: setSelect,
        setSearchType: setSearchType,
        setDateVal: setUserDateVal,
        searchType: 'year',
        // select: select,
        dateVal: userdateVal,
        res: userRes,
        datas: userdatas,
        history
      };

      //活力党组织指数
      const TargetData = {
        type: types,
        org_id: orgInfo.org_id,
        Target: Target,
        dateVal: TargetTime,
        setDateVal: setTargetTime,
        searchType: TargetsearchType,
        setSearchType: setTargetSearchType,
        history
      }
      return (
        <Suspense fallback={<i />}>
          <div>
            {/* card1 */}
            <Card title={Number(types) === 0 && "活力党组织"} bordered={false}>
                <Category {...defaultProps} cardType={types + "-1"} />
            </Card>
            {/* card2 */}
            {[4, 5, 6].includes(Number(types)) ? (
              <CardList {...defaultProps} cardType={types + "-2"} />
            ) : [3].includes(Number(types)) ? (
              <DoubleCard {...defaultProps2} cardType={types + "-2"} />
            ) : (
              <DoubleCard {...defaultProps} cardType={types + "-2"} />
            )}
            {/* card3 */}
            {[0, 4, 5, 6].includes(Number(types)) ? (
              <DoubleCard {...defaultProps2} cardType={types + "-3"} />
            ) : (
              <TableIndex {...TargetData} cardType={types + "-3"} />
            )}
            {/* card4 */}
            {types == "4" && (
              <TableIndex {...TargetData} cardType={types + "-4"} />
            )}
          </div>
        </Suspense>
      );
    }
  }, [types, searchType, select, dateVal, res, datas, userdateVal, userRes, userdatas, Target, TargetTime, TargetsearchType]);

  return (
    <div className="vitalityIndex">
      <header className="vitalityIndex-header">
        <div className="org-wrap">
          <span className="org-name" title={orgInfo.org_name}>
            组织：{orgInfo.org_name}
          </span>
          <Button type="link" onClick={onChangeVisible}>
            选择组织
          </Button>
        </div>
        <div className="update-time">更新时间：{moment().format("YYYY年MM月DD日")}</div>
      </header>
      <main className="vitalityIndex-main">{el}</main>
      <OrganizeModal {...organizeModalProps} />
    </div>
  );
};
export default VitalityIndex;
