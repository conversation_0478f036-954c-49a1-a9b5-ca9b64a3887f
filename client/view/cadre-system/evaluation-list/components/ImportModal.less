.add-form-modal {
  .ant-form label {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 1);
  }

  .ant-modal-title {
    font-size: 18px;
    color: rgba(0, 0, 0, 0.95);
  }

  .checkbox-layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 19px;

    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0px;
    }

    .ant-checkbox-wrapper {
      color: rgba(51, 51, 51, 0.9);
    }
  }

  .ant-radio-wrapper {
    color: rgba(51, 51, 51, 0.9);
  }

  .file_item {
    padding: 0px 4px;
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    color: #40a9ff;
    cursor: pointer;
    border-radius: 4px;

    .del_icon {
      display: none;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .file_item:hover {
    background-color: #e6f7ff;

    .del_icon {
      display: block;
    }
  }

  .error_title {
    font-size: 18px;
  }

  .error_item {
    color: #f5222d;
    height: 30px;
  }

  .footer {
    padding: 20px;

    button {
      width: 120px;
      height: 36px;
      border-radius: 4px 4px 4px 4px;
    }
  }

  .ant-form-item-control {
    line-height: normal;
  }
}
