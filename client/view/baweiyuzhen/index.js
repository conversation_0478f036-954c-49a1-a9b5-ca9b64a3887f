import React, { Component } from 'react';
import { connect } from 'dva';
import MobilePreview from 'components/mobile-preview';
import { Anchor, message, Form, Button, Input, Radio, Table, Popconfirm, Icon } from 'antd';
import Image from './component/upload-image';
import ModalTop from './component/modal-top';
import ModalBulletin from './component/modal-bulletin';
import ModalCharitys from './component/modal-charitys';
import ModalCommodity from './component/modal-commodity';
import { CDN } from 'apis/config';
import './index.less';

const { Link } = Anchor;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { xs: { span: 2 }, sm: { span: 2 } },
  wrapperCol: { xs: { span: 22 }, sm: { span: 22 } },
};
const fomatePreview = (data) => {
  const top_banner = { ...data.top_banner };
  const top_commodity = data.top_commodity.filter((item) => item.is_show === 1);
  top_banner.is_show = 1;

  data.top_banner = top_banner;
  data.top_commodity = [top_commodity[Math.floor(Math.random() * top_commodity.length)]];
  data.charitys = data.charitys.filter((item) => item.is_show === 1);
  data.commodity_list = data.commodity_list.filter((item) => item.is_show === 1).map((item)=>{
    return {
      ...item,
      price:Number(item.price)
    }
  });
  return data;
};

const wrapperColumns = (columns) => {
  return columns.map((item) => {
    if (item.key === undefined) {
      item.key = item.dataIndex;
    }
    if (item.align === undefined) {
      item.align = 'center';
    }
    return item;
  });
};

class Baweiyuzhen extends Component {
  constructor() {
    super();
    this.topCommodityColumns = wrapperColumns([
      {
        title: '序号',
        dataIndex: 'x',
        width: 60,
        render: (text, record, index) => {
          return index + 1
        }
      },
      { title: '视频标题', dataIndex: 'name' },
      { 
        title: '状态', 
        dataIndex: 'is_show',
        render: (text) => {
          return text == 1 ? '显示' : '隐藏'
        }
      },
      {
        title: '操作',
        width: 150,
        dataIndex: '_op',
        render: (text, record, index) => (
          <span>
            <Button type="link" onClick={() => this.onShowForm('top_commodity', index)}>
              编辑
            </Button>
            <Popconfirm
              onConfirm={() => this.onChangeStatus('top_commodity', index)}
              title={`确定${record.is_show === 1 ? '隐藏' : '显示'}吗？`}>
              <Button type="link">{record.is_show === 1 ? '隐藏' : '显示'}</Button>
            </Popconfirm>
            <Popconfirm onConfirm={() => this.onDelete('top_commodity', index)} title="确定删除吗？">
              <Button type="link">删除</Button>
            </Popconfirm>
          </span>
        ),
      },
    ]);
    this.bulletinBoardColumns = wrapperColumns([
      {
        title: '序号',
        dataIndex: 'x',
        width: 60,
        render: (text, record, index) => {
          return index + 1
        }
      },
      { title: '公告名称', dataIndex: 'name' },
      { 
        title: '链接', 
        dataIndex: 'target_url',
        render: (text) => {
          const { baweiyuzhen } = this.props
          const { topic_list } = baweiyuzhen.formData;
          let str = "其他"
          topic_list.forEach(element => {
            if (element.target_url == text){
              str = element.topic_name
            }
          });
          return str
        } 
      },
      { 
        title: '状态', 
        dataIndex: 'is_show',
        render: (text) => {
          return text == 1 ? '显示' : '隐藏'
        }
      },
      {
        title: '操作',
        width: 150,
        dataIndex: '_op',
        render: (text, record, index) => (
          <span>
            <Button type="link" onClick={() => this.onShowForm('bulletin_board', index)}>
              编辑
            </Button>
            <Popconfirm
              onConfirm={() => this.onChangeStatus('bulletin_board', index)}
              title={`确定${record.is_show === 1 ? '隐藏' : '显示'}吗？`}>
              <Button type="link">{record.is_show === 1 ? '隐藏' : '显示'}</Button>
            </Popconfirm>
            <Popconfirm onConfirm={() => this.onDelete('bulletin_board', index)} title="确定删除吗？">
              <Button type="link">删除</Button>
            </Popconfirm>
          </span>
        ),
      },
    ]);
    this.charitysColumns = wrapperColumns([
      { title: '商品名称', width: 140, dataIndex: 'name' },
      {
        title: '图片',
        width: 150,
        dataIndex: 'image_url',
        render: (text) => <img src={`${CDN}/${text}`} width={100} height={90} alt="" />,
      },
      { title: '类别ID', width: 120, dataIndex: 'recommend_type' },
      { title: '所属期次', width: 120, dataIndex: 'cycle' },
      {
        title: '操作',
        width: 150,
        dataIndex: '_op',
        render: (text, record, index) => (
          <span>
            <Button type="link" onClick={() => this.onShowForm('charitys', index)}>
              编辑
            </Button>
            <Popconfirm
              onConfirm={() => this.onChangeStatus('charitys', index)}
              title={`确定${record.is_show === 1 ? '隐藏' : '显示'}吗？`}>
              <Button type="link">{record.is_show === 1 ? '隐藏' : '显示'}</Button>
            </Popconfirm>
            <Popconfirm onConfirm={() => this.onDelete('charitys', index)} title="确定删除吗？">
              <Button type="link">删除</Button>
            </Popconfirm>
          </span>
        ),
      },
    ]);
    this.commodityListColumns = wrapperColumns([
      { title: '商品名称', dataIndex: 'name' },
      {
        title: '图片',
        dataIndex: 'image_url',
        render: (text) => <img src={`${CDN}/${text}`} width={100} height={66} alt="" />,
      },
      {
        title: '价格（元）',
        dataIndex: 'price',
        width: 100,
      },
      {
        title: '描述',
        dataIndex: 'description',
        width: 150,
        render: (text, record, index) => <span dangerouslySetInnerHTML={{ __html: text }}></span>,
      },
      { title: '标签', dataIndex: 'bottom_label' },
      { title: '商品ID', width: 80, dataIndex: 'commodity_id' },
      {
        title: '操作',
        width: 150,
        dataIndex: '_op',
        render: (text, record, index) => (
          <span>
            <Button type="link" onClick={() => this.onShowForm('commodity_list', index)}>
              编辑
            </Button>
            <Popconfirm
              onConfirm={() => this.onChangeStatus('commodity_list', index)}
              title={`确定${record.is_show === 1 ? '隐藏' : '显示'}吗？`}>
              <Button type="link">{record.is_show === 1 ? '隐藏' : '显示'}</Button>
            </Popconfirm>
            <Popconfirm onConfirm={() => this.onDelete('commodity_list', index)} title="确定删除吗？">
              <Button type="link">删除</Button>
            </Popconfirm>
          </span>
        ),
      },
    ]);
  }

  componentDidMount() {
    const { dispatch, form, baweiyuzhen } = this.props;
    const hide = message.loading('加载中', 5);
    dispatch({
      type: 'baweiyuzhen/getInitData',
      payload: { data_type: 2 },
    }).then((res) => {
      form.setFieldsValue({ ...res });
      hide();
    });
  }

  /**重置 */
  onReset() {
    const { dispatch, form } = this.props;
    const hide = message.loading('加载中', 5);
    dispatch({
      type: 'baweiyuzhen/getInitData',
      payload: { data_type: 1 },
    }).then((res) => {
      form.setFieldsValue({ ...res });
      hide();
    });
  }

  closePreview() {
    this.props.dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: { mobilePreview: false },
    });
  }
  /**预览 */
  onPreview() {
    this._validate().then((value) => {
      const { dispatch, form, baweiyuzhen } = this.props;
      const { top_commodity, charitys, bulletin_board, commodity_list } = baweiyuzhen.formData;
      // 先更新formdata
      dispatch({
        type: 'baweiyuzhen/updateForm',
        payload: { ...value, top_commodity, charitys, bulletin_board, commodity_list },
      });
      dispatch({
        type: 'baweiyuzhen/updateStatus',
        payload: { mobilePreview: true },
      });
    });
  }
  /**保存草稿 */
  onSaveDraft() {
    this.onSubmit(2);
  }
  _validate() {
    return new Promise((resolve, reject) => {
      const { dispatch, form, baweiyuzhen } = this.props;
      const { top_commodity, charitys, bulletin_board, commodity_list } = baweiyuzhen.formData;
      form.validateFieldsAndScroll(
        {
          scroll: {
            offsetTop: 100,
            offsetBottom: 100,
          },
        },
        (err, value) => {
          if (err) {
            return reject(err);
          }
          let isOtherErr = false;
          const top_commodity_show = top_commodity.filter((item) => item.is_show === 1);
          const charitys_show = charitys.filter((item) => item.is_show === 1);
          const bulletin_board_show = bulletin_board.filter((item) => item.is_show === 1);
          // const commodity_list_show = commodity_list.filter((item) => item.is_show === 1);
          if (top_commodity_show.length === 0) {
            isOtherErr = true;
            return message.error('至少显示一个视频');
          }
          if (charitys_show.length === 0) {
            isOtherErr = true;
            return message.error('至少显示一个公益宝贝');
          }
          // if (commodity_list_show.length === 0) {
          //   isOtherErr = true;
          //   return message.error('至少显示一个推荐商品');
          // }
          if (bulletin_board_show.length === 0) {
            isOtherErr = true;
            return message.error('至少显示一个公告');
          }
          if (charitys_show.length % 2 === 1) {
            isOtherErr = true;
            return message.error('只允许显示偶数个公益宝贝商品');
          }
          // if (commodity_list_show.length % 2 === 1) {
          //   isOtherErr = true;
          //   return message.error('只允许显示偶数个推荐商品');
          // }
          if (isOtherErr) {
            reject(value);
          } else {
            resolve(value);
          }
        }
      );
    });
  }
  /**发布 */
  onSubmit(submit_type) {
    this._validate().then((value) => {
      const hide = message.loading('加载中', 5);
      const { dispatch, form, baweiyuzhen } = this.props;
      const { top_commodity, charitys, bulletin_board, charitys_bottom, charitys_head, top_banner } = baweiyuzhen.formData;
      const param = {
        charitys_bottom: { ...charitys_bottom, ...value.charitys_bottom},
        charitys_head: { ...charitys_head, ...value.charitys_head },
        top_banner: { ...top_banner, ...value.top_banner },
        top_commodity,
        charitys,
        bulletin_board,
        submit_type
      }
      dispatch({
        type: 'baweiyuzhen/submit',
        payload: param,
      })
        .then((res) => {
          hide();
          message.success(submit_type === 1 ? '发布成功' : '已保存草稿');
        })
        .catch((err) => {
          hide();
        });
    });
  }
  /**表格删除 */
  onDelete(type, index) {
    const { dispatch, baweiyuzhen } = this.props;
    const list = [...baweiyuzhen.formData[type]];
    list.splice(index, 1);
    dispatch({
      type: 'baweiyuzhen/updateForm',
      payload: {
        [type]: list,
      },
    });
  }
  /**表格隐藏 */
  onChangeStatus(type, index) {
    const { dispatch, baweiyuzhen } = this.props;
    const list = [...baweiyuzhen.formData[type]];
    const data = { ...list[index] };
    if (data.is_show === 1) {
      data.is_show = 0;
    } else {
      data.is_show = 1;
    }
    list.splice(index, 1, data);
    delete data.id //没有ID代表修改过的  后端才会保存
    dispatch({
      type: 'baweiyuzhen/updateForm',
      payload: {
        [type]: list,
      },
    });
  }
  /**打开modal窗口编辑数据 */
  onShowForm(type, index) {
    const { dispatch } = this.props;
    dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: {
        [type]: {
          show: true,
          index,
        },
      },
    });
  }
  render() {
    const { form, baweiyuzhen } = this.props;
    if (!baweiyuzhen) {
      return null;
    }
    const { status, formData } = baweiyuzhen;
    const { top_commodity, bulletin_board, charitys, commodity_list } = formData;
    const { getFieldValue, getFieldDecorator } = form;
    return (
      <div className="page-baweiyuzhen">
        <div className="header">
          <div className="header-left">
            <span>消费扶贫首页配置</span>
          </div>
          <div className="header-right">
            <Button loading={false} onClick={this.onSaveDraft.bind(this)}>
              保存草稿
            </Button>
            <Button loading={false} onClick={this.onReset.bind(this)}>
              重置
            </Button>
            <Button loading={false} onClick={this.onPreview.bind(this)}>
              预览
            </Button>
            <Button loading={false} type="primary" onClick={this.onSubmit.bind(this, 1)}>
              发布
            </Button>
          </div>
        </div>
        <div className="main-box">
          <div className="side">
            <Anchor>
              <Link href="#baweiyuzhen_banner" title="顶部banner" />
              <Link href="#baweiyuzhen_bulletin_board" title="公告栏" />
              <Link href="#baweiyuzhen_top_commodity" title="视频列表" />
              <Link href="#baweiyuzhen_charitys" title="公益宝贝" />
              {/* <Link href="#baweiyuzhen_commodity_list" title="本期推荐" /> */}
            </Anchor>
          </div>
          <div className="main">
            <div id="baweiyuzhen_banner" className="item">
              <div className="title">
                顶部banner<span className="tips">（页面顶部图片）</span>
              </div>
              <div className="card img-card">
                <Form>
                  <FormItem {...formItemLayout} label="图片">
                    {getFieldDecorator('top_banner.image_url', {
                      rules: [{ required: true, message: '请上传图片' }],
                    })(<Image size={{ w: 750, h: 520 }} />)}
                  </FormItem>
                  <FormItem {...formItemLayout} label="链接">
                    {getFieldDecorator('top_banner.target_url', {
                      rules: [
                        { pattern: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/, message: "请输入正确的链接" }
                      ]
                    })(<Input disabled={false} style={{ width: 450 }} placeholder="请输入链接地址" />)}
                  </FormItem>
                </Form>
              </div>
            </div>
            <div id="baweiyuzhen_bulletin_board" className="item">
              <div className="title">
                公告栏<span className="tips">（顶部banner下方，售后模块上方）</span>
              </div>
              <div className="card table-card">
                <Table
                  rowClassName={(record) => {
                    if (record.is_show !== 1) {
                      return 'is-hide';
                    }
                    return '';
                  }}
                  bordered
                  // scroll={{ y: 400 }}
                  pagination={false}
                  dataSource={bulletin_board}
                  columns={this.bulletinBoardColumns}
                />
               {bulletin_board.length < 3 ? (
                <Button onClick={() => this.onShowForm('bulletin_board')} type="primary" ghost className="btn-add">
                  <Icon type="plus" />
                  点击添加公告
                </Button>
               ) : null
              }
              </div>
            </div>
            <div id="baweiyuzhen_top_commodity" className="item">
              <div className="title">
               视频列表 {/* <span className="tips">（售后模块下方,最多10个商品）</span> */}
              </div>
              <div className="card table-card">
                <Table
                  rowClassName={(record) => {
                    if (record.is_show !== 1) {
                      return 'is-hide';
                    }
                    return '';
                  }}
                  bordered
                  // scroll={{ y: 400 }}
                  pagination={false}
                  dataSource={top_commodity}
                  columns={this.topCommodityColumns}
                />
                {/* {top_commodity.length < 10 ? ( */}
                  <Button onClick={() => this.onShowForm('top_commodity')} type="primary" ghost className="btn-add">
                    <Icon type="plus" />
                    点击添加置顶商品
                  </Button>
                {/* ) : null} */}
              </div>
            </div>
            <div id="baweiyuzhen_charitys" className="item">
              <div className="title">
                公益宝贝<span className="tips">（置顶商品版块下方，只显示偶数个商品）</span>
              </div>
              <div className="card img-card">
                <div className="card-tittle">版头</div>
                <Form>
                  <FormItem {...formItemLayout} label="图片">
                    {getFieldDecorator('charitys_head.image_url', {
                      rules: [{ required: true, message: '请上传图片' }],
                    })(<Image size={{ w: 750, h: 220 }} />)}
                  </FormItem>
                  <FormItem {...formItemLayout} label="状态">
                    {getFieldDecorator('charitys_head.is_show', {
                      // initialValue: '',
                      rules: [{ required: true, message: '请选择显示状态' }],
                    })(
                      <Radio.Group>
                        <Radio value={1}>显示</Radio>
                        <Radio value={0}>隐藏</Radio>
                      </Radio.Group>
                    )}
                  </FormItem>
                  <FormItem {...formItemLayout} label="链接">
                    {getFieldDecorator('charitys_head.target_url', {
                      rules: [
                        { pattern: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/, message: "请输入正确的链接" }
                      ]
                    }
                    )(<Input disabled={false} style={{ width: 450 }} placeholder="请输入链接地址" />)}
                  </FormItem>
                </Form>
              </div>
              <div className="card table-card">
                <Table
                  // scroll={{ y: 400 }}
                  rowClassName={(record) => {
                    if (record.is_show !== 1) {
                      return 'is-hide';
                    }
                    return '';
                  }}
                  bordered
                  pagination={false}
                  dataSource={charitys}
                  columns={this.charitysColumns}
                />
                <Button type="primary" onClick={() => this.onShowForm('charitys')} ghost className="btn-add">
                  <Icon type="plus" />
                  点击添加公益宝贝商品
                </Button>
              </div>
              <div className="card img-card">
                <div className="card-tittle">版末</div>
                <Form>
                  <FormItem {...formItemLayout} label="图片">
                    {getFieldDecorator('charitys_bottom.image_url', {
                      rules: [{ required: true, message: '请上传图片' }],
                    })(<Image size={{ w: 750}} type={'scale'} />)}
                  </FormItem>
                  <FormItem {...formItemLayout} label="状态">
                    {getFieldDecorator('charitys_bottom.is_show', {
                      // initialValue: '',
                      rules: [{ required: true, message: '请选择显示状态' }],
                    })(
                      <Radio.Group>
                        <Radio value={1}>显示</Radio>
                        <Radio value={0}>隐藏</Radio>
                      </Radio.Group>
                    )}
                  </FormItem>
                  <FormItem {...formItemLayout} label="链接">
                    {getFieldDecorator('charitys_bottom.target_url', {
                      rules: [
                        { pattern: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/, message: "请输入正确的链接" }
                      ]
                    }
                    )(<Input disabled={false} style={{ width: 450 }} placeholder="请输入链接地址" />)}
                  </FormItem>
                </Form>
              </div>
            </div>
            {/* <div id="baweiyuzhen_commodity_list" className="item">
              <div className="title">
                本期推荐<span className="tips">（公益宝贝版块下方，只显示偶数个商品）</span>
              </div>
              <div className="card img-card">
                <div className="card-tittle">版头</div>
                <Form>
                  <FormItem {...formItemLayout} label="图片">
                    {getFieldDecorator('commodity_list_head.image_url', {
                      rules: [{ required: true, message: '请上传图片' }],
                    })(<Image size={{ w: 750, h: 220 }} />)}
                  </FormItem>
                  <FormItem {...formItemLayout} label="状态">
                    {getFieldDecorator('commodity_list_head.is_show', {
                      // initialValue: '',
                      rules: [{ required: true, message: '请选择显示状态' }],
                    })(
                      <Radio.Group>
                        <Radio value={1}>显示</Radio>
                        <Radio value={0}>隐藏</Radio>
                      </Radio.Group>
                    )}
                  </FormItem>
                  <FormItem {...formItemLayout} label="链接">
                    {getFieldDecorator(
                      'commodity_list_head.target_url',
                      {}
                    )(<Input disabled={false} style={{ width: 450 }} placeholder="请输入链接地址" />)}
                  </FormItem>
                </Form>
              </div>
              <div className="card table-card">
                <Table
                  rowClassName={(record) => {
                    if (record.is_show !== 1) {
                      return 'is-hide';
                    }
                    return '';
                  }}
                  // scroll={{y:400 }}
                  bordered
                  pagination={false}
                  dataSource={commodity_list}
                  columns={this.commodityListColumns}
                />
                <Button type="primary" onClick={() => this.onShowForm('commodity_list')} ghost className="btn-add">
                  <Icon type="plus" />
                  点击添加本期推荐
                </Button>
              </div>
              <div className="card img-card">
                <div className="card-tittle">版末</div>
                <Form>
                  <FormItem {...formItemLayout} label="图片">
                    {getFieldDecorator('commodity_list_bottom.image_url', {
                      rules: [{ required: true, message: '请上传图片' }],
                    })(<Image size={{ w: 750, h: 220 }} type={'scale'} />)}
                  </FormItem>
                  <FormItem {...formItemLayout} label="状态">
                    {getFieldDecorator('commodity_list_bottom.is_show', {
                      // initialValue: '',
                      rules: [{ required: true, message: '请选择显示状态' }],
                    })(
                      <Radio.Group>
                        <Radio value={1}>显示</Radio>
                        <Radio value={0}>隐藏</Radio>
                      </Radio.Group>
                    )}
                  </FormItem>
                  <FormItem {...formItemLayout} label="链接">
                    {getFieldDecorator(
                      'commodity_list_bottom.target_url',
                      {}
                    )(<Input disabled={false} style={{ width: 450 }} placeholder="请输入链接地址" />)}
                  </FormItem>
                </Form>
              </div>
            </div> */}
          </div>
        </div>
        <ModalBulletin />
        <ModalTop />
        <ModalCharitys />
        {/* <ModalCommodity /> */}
        {status.mobilePreview && (
          <MobilePreview
            src={'/ssr/baweiyuzhen'}
            data={fomatePreview({ ...formData })}
            closePreview={this.closePreview.bind(this)}></MobilePreview>
        )}
      </div>
    );
  }
}

export default connect(({ baweiyuzhen }) => ({ baweiyuzhen }))(Form.create()(Baweiyuzhen));
