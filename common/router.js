import React from "react";
import { Route, Switch } from "dva/router";
import dynamic from "dva/dynamic";
import LoginLayout from "client/view/login/LoginLayout";
import MainContent from "client/view/content";
import ActiveOrganization from "client/view/active-organization";
import Expect from "client/view/expect";
import SelectUnit from "client/view/login/SelectUnitPage";

//外层路由
const outRouter = [
  // {
  //   path: "/login",
  //   component: () => import("client/view/login/login"),
  //   exact: true,
  //   oneself: true,
  //   key: "login",
  //   nolayout: true, // 是否需要layout布局
  //   namespace: ["userInfo"],
  // },
  {
    path: "/login-dd",
    component: () => import("client/view/login/login-dd"),
    exact: true,
    oneself: true,
    key: "login-dd",
    nolayout: true, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/selectUnit",
    component: () => import("client/view/login/SelectUnitPage"),
    exact: true,
    oneself: true,
    key: "selectUnit",
    nolayout: true, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/forgetPassword",
    component: () => import("client/view/login/ForgetPassword"),
    exact: true,
    oneself: true,
    key: "ForgetPassword",
    namespace: ["userInfo"],
  },
  {
    path: "/setupPassword",
    component: () => import("client/view/login/SetupPassword"),
    exact: true,
    oneself: true,
    key: "SetupPassword",
    namespace: ["userInfo"],
  },

  {
    // 激活组织
    path: "/active-organization",
    component: () => import("client/view/active-organization"),
    exact: true,
    key: "ActiveOrganization",
    layoutType: "activeOrganization",
  },
  {
    path: "/",
    component: () => import("client/view/content"),
    exact: true,
    key: "mainContent",
    namespace: ["userInfo"],
  },
];

//内层路由
const inRouter = [
  {
    path: "/404",
    component: () => import("components/404"),
    exact: true,
    key: "404",
  },
  {
    path: "/order-statistics",
    component: () => import("client/view/order-statistics"),
    exact: true,
    key: "order-statistics",
  },
  {
    // 七一书院的免登陆判断
    path: "/library-islogin",
    component: () => import("client/view/library-islogin/library-islogin"),
    exact: true,
    key: "library-islogin",
  },
  {
    path: "/payment-count",
    component: () => import("client/view/payment-count"),
    exact: true,
    key: "payment-count",
  },
  {
    path: "/evaluation-report",
    component: () => import("client/view/evaluation-report"),
    exact: true,
    key: "evaluation-report",
  },
  {
    path: "/report-setting",
    component: () => import("client/view/report-setting"),
    exact: true,
    key: "report-setting",
  },
  {
    path: "/party-normal/remove-and-repay",
    component: () => import("client/view/party-normal/remove-and-repay"),
    exact: true,
    key: "remove-and-repay",
  },
  {
    path: "/evaluation-statistics",
    component: () => import("client/view/evaluation-statistics"),
    exact: true,
    key: "evaluation-statistics",
  },
  // 积分捐赠
  // {
  //   path: "/contribute",
  //   component: Contribute,
  //   key: "contribute",
  //   exact: true
  // },
  // doc文件预览
  {
    path: "/doc-preview",
    component: () => import("client/view/demo/doc-preview"),
    exact: true,
    key: "contribute",
  },
  {
    path: "/user-group-competence",
    component: () => import("client/view/user-group-competence"),
    exact: true,
    key: "userGroupCompetence",
  },
  {
    path: "/role-competence",
    component: () => import("client/view/role-competence"),
    exact: true,
    key: "roleCompetence",
  },
  {
    path: "/user-competence",
    component: () => import("client/view/user-competence"),
    exact: true,
    key: "userCompetence",
  },
  {
    path: "/contribute",
    component: () => import("client/view/contribute"),
    exact: true,
    key: "contribute",
    namespace: ["contribute", "centerContent", "selectCommodity"],
  },
  // 发起积分捐赠
  {
    path: "/new-contribute",
    component: () => import("client/view/contribute"),
    key: "new-contribute",
    exact: true,
  },
  // 编辑积分捐赠
  {
    path: "/edit-contribute",
    component: () => import("client/view/contribute"),
    key: "edit-contribute",
    exact: true,
  },
  // 查看积分捐赠
  {
    path: "/view-contribute",
    component: () => import("client/view/contribute"),
    key: "view-contribute",
    exact: true,
  },
  // 权限管理
  {
    path: "/permission",
    component: () => import("client/view/permission"),
    key: "Permission",
    exact: true,
  },
  {
    path: "/permission-org",
    component: () => import("client/view/permission-org"),
    key: "PermissionOrg",
    exact: true,
  },
  // 党费返还-工委
  {
    path: "/dues-return",
    // component: DuesReturn,
    component: () => import("client/view/expect"),
    type: "custumProps",
    exact: true,
    desc: "接口开发升级中，敬请期待…",
    key: "duesReturn",
    namespace: ["userInfo"],
  },
  // 党费新增修改交纳基数
  {
    path: "/party-normal-edit",
    component: () => import("client/view/party-normal-edit"),
    key: "PartyNormalEdit",
  },
  // 党费标准
  {
    path: "/party-normal",
    component: () => import("client/view/party-normal"),
    key: "PartyNormal",
  },
  // 党委返还 - 党委
  {
    path: "/party-return",
    component: () => import("client/view/party-return"),
    // desc: "接口开发升级中，敬请期待…",
    // type: "custumProps",
    // component: PartyReturn,
    key: "PartyReturn",
  },
  // 党费返还记录
  {
    path: "/party-history",
    component: () => import("client/view/party-history"),
    // desc: "接口开发升级中，敬请期待…",
    // type: "custumProps",
    // component: PartyHistory,
    key: "PartyHistory",
  },
  // 党费缴纳流水
  {
    path: "/party-pay-flow",
    component: () => import("client/view/party-pay-flow"),
    key: "PartyPayFlow",
  },
  // 收交统计
  {
    path: "/party-pay-statistics",
    component: () => import("client/view/party-pay-statistics"),
    key: "PartyPayStatistics",
  },
  // 党费编辑
  {
    path: "/party-edit",
    component: () => import("client/view/party-setting/edit-index"),
    key: "partyEdit",
  },
  // 党费设置
  {
    path: "/party-setting",
    component: () => import("client/view/party-setting"),
    key: "PartySetting",
  },
  {
    path: "/check-plan",
    component: () => import("client/view/check-plan"),
    key: "CheckPlan",
  },
  {
    path: "/check-plan/fill-meeting-info",
    component: () =>
      import("client/view/check-plan/sub-route/fill-meeting-info"),
    key: "FillMeetingInfo",
  },
  {
    path: "/check-plan/check-plan-result",
    component: () =>
      import("client/view/check-plan/sub-route/check-plan-result"),
    key: "CheckPlanResult",
  },
  {
    path: "/component-demo",
    component: () => import("client/view/component-demo"),
    key: "ComponentDemo",
  },
  {
    path: "/news-column/:type",
    component: () => import("client/view/news-column"),
    exact: true,
    key: "NewsColumn",
    namespace: ["userInfo", "newsColumn"],
  },
  // 新闻管理
  {
    path: "/news-list",
    component: () => import("client/view/news-list"),
    exact: true,
    key: "NewsList",
    namespace: ["newsList", "newsMatrix"],
  },
  // 专题栏目管理
  {
    path: "/informations-list",
    component: () => import("client/view/news-list"),
    exact: true,
    key: "informationsList",
  },
  {
    path: "/news-list/:tab",
    component: () => import("client/view/news-list"),
    exact: true,
    key: "NewsList",
    namespace: ["newsList", "newsMatrix"],
  },
  // 专题栏目管理
  {
    path: "/informations-list/:tab",
    component: () => import("client/view/news-list"),
    exact: true,
    key: "informationsList",
    namespace: ["newsList", "newsMatrix"],
  },
  {
    path: "/news-matrix",
    component: () => import("client/view/news-matrix"),
    exact: true,
    key: "NewsMatrix",
    namespace: ["newsMatrix"],
  },
  {
    path: "/activitys-matrix",
    component: () => import("client/view/activitys-matrix"),
    exact: true,
    key: "ActivitysMatrix",
    namespace: ["activitysMatrix"],
  },
  {
    path: "/weixin-bind",
    component: () => import("client/view/weixin-bind"),
    exact: true,
    key: "WeixinBind",
    namespace: ["weixinBind"],
  },
  {
    path: "/weixin-accounts",
    component: () => import("client/view/weixin-accounts"),
    exact: true,
    key: "WeixinAccounts",
    namespace: ["weixinAccounts"],
  },
  {
    path: "/demo",
    component: () => import("client/view/demo"),
    exact: true,
    key: "demo",
    // namespace: ["demo"]
  },
  // 发布新闻
  {
    path: "/create-news/1",
    component: () => import("client/view/create-news/edit"),
    exact: true,
    key: "createNews",
    namespace: ["userInfo"],
  },
  // 发布资讯
  {
    path: "/create-news/2",
    component: () => import("client/view/create-news/edit"),
    exact: true,
    key: "createNews",
    namespace: ["userInfo"],
  },
  {
    path: "/edit-news",
    component: () => import("client/view/create-news/edit"),
    exact: true,
    key: "editNews",
    namespace: ["userInfo"],
  },
  {
    path: "/",
    component: () => import("client/view/home"),
    exact: true,
    key: "home",
    namespace: ["userInfo"],
  },
  {
    path: "/questionnaire",
    component: () => import("client/view/questionnaire"),
    exact: true,
    key: "questionnaire",
    namespace: ["questionnaire", "centerContent", "selectCommodity"],
  },
  {
    path: "/questionnaire-new",
    component: () => import("client/view/questionnaire-new"),
    exact: true,
    key: "questionnaireNew",
    namespace: ["questionnaireNew"],
  },
  {
    path: "/activity-template",
    component: () => import("client/view/activity-template"),
    exact: true,
    key: "ActivityTemplate",
  },
  {
    path: "/activity-situation/:activity_id",
    component: () => import("client/view/activity-situation"),
    exact: true,
    key: "ActivitySituation",
    namespace: [],
  },
  {
    path: "/contest",
    component: () => import("client/view/contest"),
    exact: true,
    key: "contest",
    namespace: ["contest", "centerContent", "selectCommodity"],
  },
  {
    path: "/physical",
    component: () => import("client/view/physical"),
    exact: true,
    key: "physical",
    namespace: ["physical", "centerContent", "selectCommodity"],
  },
  {
    path: "/questionnaire-survey/:activity_id",
    component: () => import("client/view/questionnaire-survey"),
    exact: true,
    key: "QuestionnaireSurvey",
    namespace: ["questionnaireSurvey"],
  },
  {
    path: "/competition/:activity_id",
    component: () => import("client/view/competition"),
    exact: true,
    key: "Competition",
    namespace: ["competition"],
  },
  {
    path: "/vote-detail/:vote_detail_id",
    component: () => import("client/view/vote-detail"),
    exact: true,
    key: "VoteDetail",
    namespace: ["voteDetail"],
  },
  {
    path: "/contribute-detail/:contribute_detail_id",
    component: () => import("client/view/contribute-detail"),
    exact: true,
    key: "ContributeDetail",
    namespace: ["contributeDetail"],
  },
  {
    path: "/activity-details/:activity_id",
    component: () => import("client/view/activity-details"),
    exact: true,
    key: "ActivityDetails",
    namespace: ["activityDetails", "selectCommodity"],
  },
  {
    path: "/activity-list",
    component: () => import("client/view/activity-list"),
    exact: true,
    key: "ActivityList",
    namespace: ["activityList"],
  },
  {
    path: "/personnel-manage",
    component: () => import("client/view/personnel-manage"),
    exact: true,
    key: "PersonMange",
    namespace: ["personMange", "personMangeAdd"],
  },
  {
    path: "/personnel-manage/edit-and-create",
    component: () => import("client/view/personnel-manage-info"),
    exact: true,
    key: "PersonMangeInfo",
    namespace: ["personMangeAdd"],
  },
  {
    path: "/root-manage",
    component: () => import("client/view/root-manage"),
    exact: true,
    key: "RootManage",
    namespace: ["rootManage"],
  },
  {
    path: "/department-manage",
    component: () => import("client/view/department-manage"),
    exact: true,
    key: "DepartmentManage",
    namespace: ["departmentManage", "systeManage", "userInfo"],
  },
  {
    path: "/user-info-password",
    component: () => import("client/view/user-info-password"),
    exact: true,
    key: "UserWord",
    namespace: ["updatePassword", "userInfo"],
  },
  {
    path: "/user-info-phone",
    component: () => import("client/view/user-info-phone"),
    exact: true,
    key: "UserTel",
    namespace: ["updatePhoneInfo", "userInfo"],
  },
  {
    path: "/user-info-phone/set-phone",
    component: () => import("client/view/user-info-newphone"),
    exact: true,
    key: "UserTel1",
    namespace: ["updateNewPhoneInfo", "userInfo"],
  },
  {
    path: "/tag-manage",
    component: () => import("client/view/tag-manage"),
    exact: true,
    key: "TagManage",
    namespace: ["tagManage"],
  },
  {
    path: "/all-activity",
    component: () => import("client/view/all-activity"),
    exact: true,
    key: "AllActivity",
    namespace: ["allActivity", "activitysMatrix"],
  },
  {
    path: "/system-manage",
    component: () => import("client/view/system-manage"),
    exact: true,
    key: "SystemManage",
    namespace: ["systeManage"],
  },
  {
    path: "/company-info",
    component: () => import("client/view/company-info"),
    exact: true,
    key: "CompanyInfo",
  },
  {
    path: "/my-process",
    component: () => import("client/view/my-process"),
    exact: true,
    key: "MyProcess",
    namespace: ["myProcess"],
  },
  {
    path: "/new-process/:workflow_id",
    component: () => import("client/view/new-process"),
    exact: true,
    key: "newProcess",
    namespace: ["newProcess"],
  },
  {
    path: "/new-process",
    component: () => import("client/view/new-process"),
    exact: true,
    key: "newProcess",
    namespace: ["newProcess"],
  },
  {
    path: "/type-process",
    component: () => import("client/view/type-process"),
    exact: true,
    key: "TypeProcess",
    namespace: ["typeProcess"],
  },
  {
    path: "/new-vote",
    component: () => import("client/view/new-vote"),
    exact: true,
    key: "newVote",
    namespace: ["newVote", "selectCommodity"],
  },
  {
    path: "/article-manage",
    component: () => import("client/view/article-manage"),
    exact: true,
    key: "ArticleManage",
    namespace: ["articleManage"],
  },
  {
    path: "/create-article",
    component: () => import("client/view/create-article"),
    exact: true,
    key: "CreateArticle",
    namespace: ["createArticle"],
  },
  {
    path: "/winning-history",
    component: () => import("client/view/winning-history"),
    exact: true,
    key: "WinningHistory",
    namespace: ["winningHistory"],
  },
  {
    path: "/prize-delivery",
    component: () => import("client/view/prize-delivery"),
    exact: true,
    key: "PrizeDelivery",
    namespace: ["prizeDelivery", "logistics"],
  },
  {
    path: "/logistics",
    component: () => import("client/view/logistics"),
    exact: true,
    key: "Logistics",
    namespace: ["logistics"],
  },
  {
    path: "/secondaryDirectory/:id",
    component: () => import("client/view/secondary-directory"),
    exact: true,
    key: "secondaryDirectory",
    namespace: ["secondaryDirectory"],
  },
  {
    path: "/organize-data",
    component: () => import("client/view/organize-data"),
    exact: true,
    key: "OrganizeData",
    namespace: ["organizeData", "organizeFramework"],
  },
  {
    path: "/organize-framework",
    component: () => import("client/view/organize-framework"),
    exact: true,
    key: "OrganizeFramework",
    namespace: ["organizeFramework", "departmentManage"],
  },
  {
    path: "/organize-framework/batch-addition",
    component: () => import("client/view/batch-addition"),
    exact: true,
    key: "BatchAddition",
    namespace: ["batchAddition"],
  },
  {
    path: "/integral-manage",
    component: () => import("client/view/integral-manage"),
    exact: true,
    key: "IntegralManage",
    namespace: ["integralManage"],
  },
  //积分兑换设置
  {
    path: "/integral-rule",
    component: () => import("client/view/integral-rule"),
    exact: true,
    key: "IntegralRule",
    // namespace: ["integralRule"]
  },

  {
    path: "/unit-information",
    component: () => import("client/view/unit-information"),
    exact: true,
    key: "UnitInformation",
    namespace: ["unitInformation"],
  },
  {
    path: "/organization-information",
    component: () => import("client/view/organization-information"),
    exact: true,
    key: "OrganizationInformation",
    namespace: ["organizationInformation"],
  },
  {
    path: "/upload-resource",
    component: () => import("client/view/upload-resource"),
    exact: true,
    key: "UploadResource",
    namespace: ["uploadResource"],
  },
  {
    // 考核系统-查看具体组织
    path: "/assessment-org-detail",
    component: () => import("client/view/assessment-org-detail"),
    exact: true,
    key: "assessment-org-detail",
    namespace: ["assessmentOrgDetail"],
    desc: "考核系统-查看具体组织",
  },
  // assessment-org-detail
  {
    // 议题池管理
    path: "/meeting-topic-management",
    component: () => import("client/view/meeting-topic-management"),
    exact: true,
    key: "MeetingTopicManagement",
    // namespace: ["MeetingTopicManagement"]
  },
  {
    // 基础设置
    path: "/docu-manage-base-setting",
    component: () => import("client/view/docu-manage-base-setting"),
    exact: true,
    key: "DocuManageBaseSetting",
  },
  {
    // 会议类型管理
    path: "/topic-type-management",
    component: () =>
      import("client/view/docu-manage-base-setting/topic-type-management"),
    exact: true,
    key: "TopicTypeManagement",
  },
  {
    // 会议类型组合
    path: "/topic-type-group",
    component: () =>
      import("client/view/docu-manage-base-setting/topic-type-group"),
    exact: true,
    key: "TopicTypeGroup",
  },
  {
    // 配置报表
    path: "/config-statement",
    component: () =>
      import("client/view/docu-manage-base-setting/config-statement"),
    exact: true,
    key: "ConfigStatement",
  },
  {
    // 新增会议议题
    path: "/addMeetTopic",
    component: () => import("client/view/add-meet-topic"),
    exact: true,
    key: "AddMeetTopic",
  },
  {
    // 编辑会议议题
    path: "/editMeetTopic",
    component: () => import("client/view/add-meet-topic"),
    exact: true,
    key: "editMeetTopic",
  },
  {
    // 考核计划管理
    path: "/assess-plan-manage",
    component: () => import("client/view/assess-plan-management"),
    exact: true,
    key: "AssessPlanManagement",
  },
  {
    // 新增考核计划
    path: "/add-assess-plan",
    component: () =>
      import("client/view/assess-plan-management/components/add-assess-plan"),
    exact: true,
    key: "AddAssessPlan",
  },
  {
    // 考核任务管理
    path: "/examination-tasks-list",
    component: () => import("client/view/examination-tasks-list"),
    exact: true,
    key: "ExaminationTasksList",
  },
  {
    // 考核任务详情
    path: "/examination-task-view/:id",
    component: () => import("client/view/examination-task-view"),
    exact: true,
    key: "ExaminationTaskView",
  },
  {
    // 新增考核任务
    path: "/examination-task-add",
    component: () => import("client/view/examination-task-add"),
    exact: true,
    key: "ExaminationTaskAdd",
  },
  {
    // 修改考核任务
    path: "/examination-task-edit/:id",
    component: () => import("client/view/examination-task-add"),
    exact: true,
    key: "ExaminationTaskEdit",
  },
  // 民主评议
  {
    // 民主评议党员
    path: "/democracy-member",
    component: () => import("client/view/democracy-member"),
    exact: true,
    key: "DemocracyMember",
  },
  {
    // 民主评议统计
    path: "/democracy-statistical",
    component: () => import("client/view/democracy-statistical"),
    exact: true,
    key: "DemocracyStatistical",
  },
  {
    // 民主评议统计-下级
    path: "/democracy-statistical-child",
    component: () =>
      import("client/view/democracy-statistical/democracy-statistical-child"),
    exact: true,
    key: "DemocracyStatisticalChild",
  },
  // 述职评议
  {
    // 基层组织述职评议
    path: "/project-review",
    component: () => import("client/view/project-review"),
    exact: true,
    key: "ProjectReview",
  },
  {
    // 述职评议统计
    path: "/project-statistical",
    component: () => import("client/view/project-statistical"),
    exact: true,
    key: "ProjectStatistical",
  },

  {
    // 任务执行
    path: "/execute-task",
    component: () => import("client/view/execute-task"),
    exact: true,
    key: "ExecuteTask",
    namespace: ["executeTask"],
  },
  {
    path: "/execute-task-detail",
    component: () => import("client/view/execute-task-detail"),
    exact: true,
    key: "ExecuteTaskDetail",
    namespace: ["executeTaskDetail"],
  },
  // 数据采集
  {
    path: "/collect-data-detail",
    component: () => import("client/view/collect-data-detail"),
    exact: true,
    key: "CollectDataDetail",
  },
  {
    // 任务审核
    path: "/audit-task",
    component: () => import("client/view/audit-task"),
    exact: true,
    key: "AuditTask",
    namespace: ["auditTask"],
  },
  {
    path: "/audit-task-detail",
    component: () => import("client/view/audit-task-detail"),
    exact: true,
    key: "AuditTaskDetail",
    namespace: ["auditTaskDetail"],
  },
  // 权限管理 authorityManagement
  {
    // 下机组织, 基础设置
    path: "/authority-management",
    component: () => import("client/view/authority-management"),
    exact: true,
    key: "authorityManagement",
  },
  {
    // 下机组织, 基础设置
    path: "/task-manage-base-setting",
    component: () => import("client/view/task-manage-base-setting"),
    exact: true,
    key: "TaskManageBaseSetting",
  },
  //字典表配置
  {
    path: "/dictionary-config",
    component: () => import("client/view/dictionary-config"),
    exact: true,
    oneself: true,
    key: 'DictionaryConfig"',
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  //干部查询
  {
    path: "/search-personnel",
    component: () => import("client/view/search-personnel"),
    exact: true,
    oneself: true,
    key: "PersonnelSearch",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    // 会议完成情况
    path: "/meeting-performance",
    component: () =>
      import("client/view/task-manage-base-setting/meeting-performance"),
    exact: true,
    key: "MeetingPerformance",
  },
  {
    // 议题完成情况
    path: "/topic-performance",
    component: () =>
      import("client/view/task-manage-base-setting/topic-performance"),
    exact: true,
    key: "TopicPerformance",
  },
  {
    // 会议管理
    path: "/meeting-manage",
    component: () => import("client/view/meeting-manage"),
    exact: true,
    key: "MeetingManage",
  },
  {
    // 会议管理
    path: "/meeting-create",
    component: () => import("client/view/meeting-create"),
    exact: true,
    key: "MeetingCreate",
    namespace: ["meetingCreate", "userInfo"],
  },
  {
    // 查看会议报表
    path: "/cat-meeting-statement",
    component: () =>
      import(
        "client/view/task-manage-base-setting/meeting-performance/component/cat-meeting-statement"
      ),
    exact: true,
    key: "CatMeetingStatement",
  },
  {
    // 填报会议纪实情况表
    path: "/fill-meeting-report-condition",
    component: () => import("client/view/fill-meeting-report-condition"),
    exact: true,
    key: "FillMeetingReportCondition",
  },
  {
    // 填报会议纪实情况表
    path: "/view-meeting-report-condition",
    component: () => import("client/view/fill-meeting-report-condition"),
    exact: true,
    key: "FillMeetingReportCondition",
  },
  {
    // 直接填报会议纪实情况表
    path: "/fill-meeting-report-directly",
    component: () => import("client/view/fill-meeting-report-directly"),
    exact: true,
    key: "FillMeetingReportDirectly",
  },
  {
    // 办公OA
    type: "custumProps", // 自定义props
    path: "/office-oa",
    component: () => import("client/view/expect"),
    desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "office-oa",
    // namespace: ["officeOa"]
  },
  // {
  //   // 新闻管理
  //   type: "custumProps",
  //   path: "/news-management",
  //   component: () => import("client/view/expect") Expect,
  //   desc: "数据接口开发接入中，敬请期待…",
  //   exact: true,
  //   key: "news-management",
  //   namespace: ["newsManagement"]
  // },
  {
    // 通知消息
    type: "custumProps",
    path: "/notification-message",
    component: () => import("client/view/expect"),
    desc: "接口开发升级中，敬请期待…",
    exact: true,
    key: "notification-message",
    // namespace: ["notificationMessage"]
  },
  {
    // 消息管理
    type: "custumProps",
    path: "/message-management",
    component: () => import("client/view/expect"),
    desc: "接口开发升级中，敬请期待…",
    exact: true,
    key: "message-management",
    // namespace: ["messageManagement"]
  },

  {
    // 模板设置
    type: "custumProps",
    path: "/template-setting",
    component: () => import("client/view/expect"),
    desc: "接口开发升级中，敬请期待…",
    exact: true,
    key: "template-setting",
    // namespace: ["templateSetting"]
  },
  {
    // 发布资源
    type: "custumProps",
    path: "/release-resources",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "release-resources",
    // namespace: ["releaseResources"]
  },
  {
    // 组织关系转接
    type: "custumProps",
    path: "/org-change",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "org-change",
    // namespace: ["releaseResources"]
  },
  {
    // 发展党员
    type: "custumProps",
    path: "/develop-member",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "develop-member",
    // namespace: ["releaseResources"]
  },
  {
    // 管理资源
    type: "custumProps",
    path: "/resources-management",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "resources-management",
    // namespace: ["resourcesManagement"]
  },
  {
    // 积分兑换统计
    // type: "custumProps",
    path: "/integral-exchange-statisitics",
    component: () => import("client/view/integral-exchange-statisitics"),
    // desc: "功能尚未配置",
    exact: true,
    key: "integral-exchange-statisitics",
    // namespace: ["statisticalBoard"]
  },
  {
    // 积分兑换明细
    // type: "custumProps",
    path: "/integral-exchange",
    component: () => import("client/view/integral-exchange"),
    // desc: "功能尚未配置",
    exact: true,
    key: "integral-exchange",
    // namespace: ["integralExchange"]
  },
  {
    // 统计设置
    type: "custumProps",
    path: "/statistical-setting",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "statistical-setting",
    // namespace: ["statisticalSetting"]
  },
  // 敬请期待页面路由
  {
    type: "custumProps",
    path: "/expect",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "expect",
  },
  {
    // type: "messageCenter",
    path: "/accounts-stylem",
    component: () => import("client/view/expect"),
    desc: "功能尚未配置",
    exact: true,
    key: "expect",
  },
  {
    // 李科------问题查处
    type: "question", // 自定义props
    path: "/question-search",
    component: () => import("client/view/question-search"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "question-search",
    // namespace: ["officeOa"]
  },
  {
    // 李科------问题查处-增加
    type: "question", // 自定义props
    path: "/question-searchAdd",
    component: () => import("client/view/question-search/questionSearchAdd"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "question-searchAdd",
    // namespace: ["officeOa"]
  },
  {
    // 李科------考核分值管理
    type: "score", // 自定义props
    path: "/score-management",
    component: () => import("client/view/score-management"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "score-management",
    // namespace: ["officeOa"]
  },
  // 吕诃-----升级日志管理
  {
    path: "/version-management",
    component: () => import("client/view/version-management"),
    exact: true,
    key: "version-management",
  },
  {
    path: "/label-management",
    component: () => import("client/view/label-management"),
    exact: true,
    key: "label-management",
  },
  {
    path: "/member-manage-base-setting",
    component: () => import("client/view/member-manage-base-setting"),
    exact: true,
    key: "member-manage-base-setting",
  },
  {
    path: "/member-management",
    component: () => import("client/view/member-management"),
    exact: true,
    key: "member-management",
    namespace: ["organizeData"],
  },
  // 报到党员管理
  {
    path: "/party-member-manage",
    component: () =>
      import("client/view/VitalityPartyBranch/party-member-manage"),
    exact: true,
    key: "party-member-manage",
  },
  // 党员报到审核
  {
    path: "/party-member-audit",
    component: () =>
      import("client/view/VitalityPartyBranch/party-member-audit"),
    exact: true,
    key: "party-member-audit",
  },
  //活力指数概况主页  0县委  1村社  2街道  3支部
  {
    path: "/vitality-index",
    component: () => import("client/view/VitalityPartyBranch/VitalityIndex"),
    exact: true,
    key: "vitality-index",
  },
  {
    // 李科------领导班子首页
    type: "leader", // 自定义props
    path: "/leader-group",
    component: () => import("client/view/leader-group"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "leader-group",
    // namespace: ["officeOa"]
  },
  {
    // 李科------添加班子成员
    type: "leader", // 自定义props
    path: "/add-groupMember",
    component: () => import("client/view/leader-group/addGroupMember"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "add-groupMember",
    // namespace: ["officeOa"]
  },
  {
    // 李科------编辑班子成员
    type: "leader", // 自定义props
    path: "/edit-groupMember",
    component: () => import("client/view/leader-group/editGroupMember"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "edit-groupMember",
    // namespace: ["officeOa"]
  },
  {
    // 李科------编辑班子成员
    type: "leader", // 自定义props
    path: "/operate-groupMember",
    component: () => import("client/view/leader-group/leaderTeam"),
    // desc: "数据接口开发接入中，敬请期待…",
    exact: true,
    key: "operate-groupMember",
    // namespace: ["officeOa"]
  },
  {
    // 李科------添加届次
    type: "leader", // 自定义props
    path: "/organization-committee/:id",
    component: () =>
      import("client/view/party-organization/components/organizationCommittee"),
    exact: true,
    key: "OrganizationCommittee",
    // namespace: ["officeOa"]
  },
  {
    // 李科------党小组
    type: "leader", // 自定义props
    path: "/party-group",
    component: () => import("client/view/party-group"),
    exact: true,
    key: "party-group",
    // namespace: ["officeOa"]
  },
  {
    // 李科------添加党小组
    type: "leader", // 自定义props
    path: "/add-partyGroup",
    component: () => import("client/view/party-group/addPartyGroup"),
    exact: true,
    key: "add-partyGroup",
    // namespace: ["officeOa"]
  },
  {
    // 李科------编辑党小组
    type: "leader", // 自定义props
    path: "/edit-partyGroup",
    component: () => import("client/view/party-group/editPartyGroup"),
    exact: true,
    key: "edit-partyGroup",
    // namespace: ["officeOa"]
  },
  {
    // 李科------党小组成员列表
    type: "leader", // 自定义props
    path: "/party-groupMember",
    component: () => import("client/view/party-group/partyGroupMember"),
    exact: true,
    key: "party-groupMember",
    // namespace: ["officeOa"]
  },
  {
    path: "/party-return-accounts",
    component: () => import("client/view/party-return-accounts"),
    exact: true,
    key: "PartyReturnAccounts",
  },
  {
    path: "/party-branch-report",
    component: () => import("client/view/party-branch-report"),
    exact: true,
    key: "party-branch-report",
  },
  {
    path: "/leading-cadres-report",
    component: () => import("client/view/leading-cadres-report"),
    exact: true,
    key: "leading-cadres-report",
  },
  {
    path: "/send-message",
    component: () => import("client/view/send-message"),
    exact: true,
    key: "sendMessage",
  },
  {
    path: "/send-message-record",
    component: () => import("client/view/send-message-record"),
    exact: true,
    key: "sendMessageRecord",
  },
  {
    path: "/assessment-list",
    component: () => import("client/view/assessment-list"),
    exact: true,
    key: "AssessmentList",
  },
  {
    path: "/send-person-list/:type/:push_id",
    component: () => import("client/view/send-message-record/send-person-list"),
    exact: true,
    key: "sendPersonList",
  },
  {
    path: "/message-detail/:type/:push_id",
    component: () => import("client/view/send-message-record/message-detail"),
    exact: true,
    key: "messageDetail",
  },
  {
    path: "/channel-registration",
    component: () => import("client/view/channel-registration"),
    exact: true,
    key: "channelRegistration",
  },
  {
    path: "/channel-generator-url",
    component: () => import("client/view/channel-generator-mall-url"),
    exact: true,
    key: "ChannelGeneratorMallUrl",
  },
  {
    path: "/party-member-org-life",
    component: () => import("client/view/party-member-org-life"),
    exact: true,
    key: "partyMemberOrgLife",
  },
  {
    path: "/org-life-view",
    component: () => import("client/view//org-life-view"),
    exact: true,
    key: "orgLifeView",
  },
  {
    path: "/member-photo",
    component: () => import("client/view/member-photo"),
    exact: true,
    key: "memberPhoto",
  },
  {
    path: "/org-photo",
    component: () => import("client/view/org-photo"),
    exact: true,
    key: "orgPhoto",
  },
  {
    path: "/member-oneMoney",
    component: () => import("client/view/member-oneMoney"),
    exact: true,
    key: "memberOneMoney",
  },
  {
    path: "/party-fee-reconciliation",
    component: () => import("client/view/party-fee-reconciliation"),
    exact: true,
    key: "PartyFeeReconciliation",
  },
  {
    path: "/org-management",
    component: () => import("client/view/org-management"),
    exact: true,
    key: "OrgManagement",
  },
  {
    path: "/party-organization",
    component: () => import("client/view/party-organization"),
    exact: true,
    key: "PartyOrganization",
  },
  {
    path: "/party-org-params",
    component: () => import("client/view/party-org-params"),
    exact: true,
    key: "PartyOrgParams",
  },
  {
    path: "/org-report-comment",
    component: () => import("client/view/org-report-comment"),
    exact: true,
    key: "OrgReportComment",
  },
  {
    path: "/party-report-comment",
    component: () => import("client/view/party-report-comment"),
    exact: true,
    key: "PartyReportComment",
  },
  {
    path: "/push-strategy",
    component: () => import("client/view/push-strategy"),
    exact: true,
    key: "PushStrategy",
  },
  {
    path: "/strategy-form",
    component: () => import("client/view/push-strategy/strategy-form"),
    exact: true,
    key: "StrategyForm",
  },
  {
    path: "/comprehensive-report-library",
    component: () => import("client/view/comprehensive-report-library"),
    exact: true,
    key: "ComprehensiveReportLibrary",
  },
  {
    path: "/comprehensive-report",
    component: () =>
      import("client/view/comprehensive-report-library/comprehensive-report"),
    exact: true,
    key: "ComprehensiveReport",
  },
  {
    path: "/report-iframe/:id",
    component: () =>
      import("client/view/comprehensive-report-library/comprehensive-report"),
    exact: true,
    key: "ComprehensiveReport",
  },
  {
    path: "/baweiyuzhen",
    component: () => import("client/view/baweiyuzhen"),
    exact: true,
    key: "Baweiyuzhen",
  },
  {
    path: "/schedule-management",
    component: () => import("client/view/schedule-management"),
    exact: true,
    key: "ScheduleManagement",
  },
  {
    path: "/create-schedule",
    component: () => import("client/view/create-schedule"),
    exact: true,
    key: "CreateSchedule",
  },
  {
    path: "/schedule-details",
    component: () => import("client/view/schedule-details"),
    exact: true,
    key: "ScheduleDetails",
  },
  {
    path: "/schedule-signin",
    component: () => import("client/view/schedule-signin"),
    exact: true,
    key: "ScheduleSignin",
  },
  {
    path: "/poverty-alleviation-theme",
    component: () => import("client/view/poverty-alleviation-theme"),
    exact: true,
    key: "PovertyAlleviationTheme",
  },
  {
    path: "/theme-page-edit",
    component: () =>
      import("client/view/poverty-alleviation-theme/theme-page_edit"),
    exact: true,
    key: "ThemePageEdit",
  },
  {
    path: "/voluntary-group-management",
    component: () => import("client/view/voluntary-group-management"),
    exact: true,
    key: "VoluntaryGroupManagement",
  },
  {
    path: "/deliver-service",
    component: () => import("client/view/deliver-service"),
    exact: true,
    key: "DeliverService",
  },
  {
    path: "/deliver-service-info",
    component: () => import("client/view/deliver-service/info"),
    exact: true,
    key: "DeliverServiceInfo",
  },
  {
    path: "/deliver-service-info/:id",
    component: () => import("client/view/deliver-service/info"),
    exact: true,
    key: "DeliverServiceInfo",
  },
  {
    path: "/deliver-service-apply",
    component: () => import("client/view/deliver-service-apply"),
    exact: true,
    key: "DeliverServiceApply",
  },
  {
    path: "/volunteer",
    component: () => import("client/view/volunteer"),
    exact: true,
    key: "Volunteer",
  },
  {
    path: "/volunteer-recruitment",
    component: () => import("client/view/volunteer-recruitment"),
    exact: true,
    key: "VolunteerRecruitment",
  },
  {
    path: "/volunteer-integral-settlement",
    component: () => import("client/view/volunteer-integral-settlement"),
    exact: true,
    key: "VolunteerIntegralSettlement",
  },
  {
    path: "/volunteer-timekeeping",
    component: () => import("client/view/volunteer-timekeeping"),
    exact: true,
    key: "VolunteerTimekeeping",
  },
  {
    path: "/volunteer-evaluation",
    component: () => import("client/view/volunteer-evaluation"),
    exact: true,
    key: "VolunteerEvaluation",
  },
  {
    path: "/volunteer-project",
    component: () => import("client/view/volunteer-project"),
    exact: true,
    key: "VolunteerProject",
  },
  {
    path: "/volunteer-project-edit",
    component: () => import("client/view/volunteer-project/edit-page"),
    exact: true,
    key: "VolunteerProjectEdit",
  },
  {
    path: "/subordinate-volunteer-project",
    component: () => import("client/view/volunteer-project"),
    exact: true,
    key: "VolunteerProject",
  },
  {
    path: "/volunteer-seek-help",
    component: () => import("client/view/volunteer-seek-help"),
    exact: true,
    key: "VolunteerSeekHelp",
  },
  {
    path: "/unlock-user",
    component: () => import("client/view/unlock-user"),
    exact: true,
    key: "UnlockUser",
  },
  {
    path: "/dynamic-form-config",
    component: () => import("client/view/dynamic-form-config"),
    exact: true,
    key: "DynamicFormConfig",
  },
  {
    path: "/menu-config",
    component: () => import("client/view/menu-config"),
    exact: true,
    key: "MenuConfig",
  },
  {
    path: "/data-initialization",
    component: () => import("client/view/cadre-system/data-initialization"),
    exact: true,
    key: "dataInitialization",
  },
  /* 云区 */
  {
    path: "/cloud-topic-management",
    component: () => import("client/view/cloud-topic-management"),
    exact: true,
    key: "MenuConfig",
  },
  /* 南岸区网信办新增路由: 2021/7/30 */
  {
    path: "/wangxin-task-create",
    component: () => import("client/view/wangxin-task-create"),
    exact: true,
    key: "wangxinTaskCreate",
  },
  {
    path: "/wangxin-task-fill",
    component: () => import("client/view/wangxin-task-fill"),
    exact: true,
    key: "wangxinTaskCreate",
  },
  {
    path: "/wangxin-examine-fill",
    component: () => import("client/view/wangxin-task-fill"),
    exact: true,
    key: "wangxinTaskCreate",
  },
  // 审核列表
  {
    path: "/wangxin-examine-list",
    component: () => import("client/view/wangxin-examine-list"),
    exact: true,
    key: "wangxinTaskWord",
  },
  // 转办单word
  {
    path: "/wangxin-task-word",
    component: () => import("client/view/wangxin-task-word"),
    exact: true,
    key: "wangxinTaskWord",
  },
  // 派发任务
  {
    path: "/wangxin-distributed-task",
    component: () => import("client/view/wangxin-distributed-task"),
    exact: true,
    key: "wangxinDistributedTak",
  },
  // 我的任务
  {
    path: "/wangxin-my-task",
    component: () => import("client/view/wangxin-my-task/topic-performance"),
    exact: true,
    key: "wangxinMyTask",
  },
  {
    path: "/wangxin-framework",
    component: () => import("client/view/wangxin-framework"),
    exact: true,
    key: "wangxinFranmework",
  },
  {
    path: "/wangxin-member-management",
    component: () => import("client/view/wangxin-member-management"),
    exact: true,
    key: "wangxinMemberManagement",
  },
  // 待办任务
  {
    path: "/wangxin-todo-list",
    component: () => import("client/view/wangxin-todo-list"),
    exact: true,
    key: "wangxinToDoList",
  },
  {
    path: "/smart-reports/:id",
    component: () =>
      import("client/view/comprehensive-report-library/smart-reports"),
    exact: true,
    key: "SmartReports",
  },
  {
    path: "/cockpit",
    component: () => import("client/view/cockpit"),
    exact: true,
    key: "Cockpit",
  },
  {
    path: "/data-physical-examination",
    component: () => import("client/view/DataPhysicalExamination"),
    exact: true,
    key: "DataPhysicalExamination",
  },
  // 我要出题-审核列表 1 初审 2 终审
  {
    path: "/audit-question-list/:id",
    component: () => import("client/view/AuditQuestion"),
    exact: true,
    key: "AuditQuestion",
  },
  // 题目审核/详情/编辑
  {
    path: "/audit-question/detail",
    component: () => import("client/view/AuditQuestion/QuestionDetail"),
    exact: true,
    key: "QuestionDetail",
  },
  // 六点打卡-内容审核列表 1 初审 2 终审
  {
    path: "/audit-content-list/:id",
    component: () => import("client/view/ContentSolicitation"),
    exact: true,
    key: "ContentSolicitation",
  },
  // 内容审核/详情/编辑
  {
    path: "/audit-content/detail",
    component: () => import("client/view/ContentSolicitation/ContentDetail"),
    exact: true,
    key: "ContentDetail",
  },
  // 烟草党费数据同步
  {
    path: "/party-sync-abnormal",
    component: () => import("client/view/partySyncAbnormal"),
    exact: true,
    key: "PartySyncAbnormal",
  },
  // 党建阵地管理
  {
    path: "/party-building-positions",
    component: () =>
      import("client/view/promotionManagement/PartyBuildingPositions"),
  },
  // 党建品牌管理
  {
    path: "/party-brand",
    component: () => import("client/view/promotionManagement/PartyBrand"),
  },
  // VR党建内容管理
  {
    path: "/vr-Administration",
    component: () =>
      import("client/view/promotionManagement/VR-Administration"),
  },
  // 组织风采
  {
    path: "/organization-style",
    component: () =>
      import("client/view/promotionManagement/organization-style"),
  },
  // 资源库
  {
    path: "/resource-library",
    component: () => import("client/view/ResourceLibrary"),
    exact: true,
    key: "ResourceLibrary",
  },
  // 文件库
  {
    path: "/resource-library/document",
    component: () => import("client/view/ResourceLibrary/view/DocumentLibrary"),
    exact: true,
    key: "DocumentLibrary",
  },
  // 文件库详情
  {
    path: "/resource-library/document/detail",
    component: () => import("client/view/ResourceLibrary/view/DocumentDetail"),
    exact: true,
    key: "DocumentDetail",
  },
  // 文件库管理
  {
    path: "/document-manage",
    component: () => import("client/view/ResourceLibrary/view/DocumentManage"),
    exact: true,
    key: "DocumentManage",
  },
  // 新增文件库资源
  {
    path: "/document-manage/add",
    component: () => import("client/view/ResourceLibrary/view/AddDocument"),
    exact: true,
    key: "AddDocument",
  },
  // 分类管理
  {
    path: "/sort-manage",
    component: () => import("client/view/ResourceLibrary/view/SortManage"),
    exact: true,
    key: "SortManage",
  },
  //机关大屏
  {
    path: "/office-large-size",
    component: () => import("client/view/officeLargeSize"),
    exact: true,
    key: "officeLargeSize",
  },
  //万紫山大屏
  {
    path: "/wzs-large-size",
    component: () => import("client/view/wzsLargeSize"),
    exact: true,
    key: "wzsLargeSize",
  },
  // 师资库
  {
    path: "/teacher-library",
    component: () => import("client/view/ResourceLibrary/view/TeacherLibrary"),
    exact: true,
    key: "TeacherLibrary",
  },
  //师资库管理
  {
    path: "/teacher-resources",
    component: () =>
      import("client/view/ResourceLibrary/view/TeacherResources"),
    exact: true,
    key: "TeacherResources",
  },
  //新增师资库资源
  {
    path: "/teacher-resources/add",
    component: () =>
      import(
        "client/view/ResourceLibrary/view/TeacherResources/components/NewlyIncreased"
      ),
    exact: true,
    key: "NewlyIncreased",
  },
  //师资库详情
  {
    path: "/teacher-resources/details",
    component: () =>
      import(
        "client/view/ResourceLibrary/view/TeacherResources/components/Particulars"
      ),
    exact: true,
    key: "Particulars",
  },
  // 案例库管理
  {
    path: "/case-manage",
    component: () => import("client/view/ResourceLibrary/view/CaseManage"),
    exact: true,
    key: "CaseManage",
  },
  // 新增案例库资源
  {
    path: "/case-manage/add",
    component: () => import("client/view/ResourceLibrary/view/AddDocument"),
    exact: true,
    key: "AddCase",
  },
  // 案例库
  {
    path: "/resource-library/case",
    component: () => import("client/view/ResourceLibrary/view/CaseLibrary"),
    exact: true,
    key: "CaseLibrary",
  },
  // 案例库详情
  {
    path: "/resource-library/case/detail",
    component: () => import("client/view/ResourceLibrary/view/DocumentDetail"),
    exact: true,
    key: "CaseDetail",
  },
  // 场地库展示
  {
    path: "/field-library/document",
    component: () =>
      import("client/view/ResourceLibrary/view/fieldLibraryDocument"),
    exact: true,
    key: "fieldLibraryDocument",
  },
  // 场地库管理
  {
    path: "/field-manage",
    component: () => import("client/view/ResourceLibrary/view/fieldLibrary"),
    exact: true,
    key: "fieldLibrary",
  },
  // 新增场地库资源
  {
    path: "/field-manage/Field-add",
    component: () =>
      import("client/view/ResourceLibrary/view/FieldAddDocument"),
    exact: true,
    key: "FieldAddDocument",
  },
  // 场地库详情
  {
    path: "/field-library/document/detail",
    component: () =>
      import("client/view/ResourceLibrary/view/fieldLibraryDetail"),
    exact: true,
    key: "fieldLibraryDocumentDetail",
  },
  // 场地库 预约管理
  {
    path: "/field-library/appointment-management",
    component: () =>
      import("client/view/ResourceLibrary/view/AppointmentManagement"),
    exact: true,
    key: "AppointmentManagement",
  },

  // 水利局-资源发布
  {
    path: "/WaterControlBureau/ResourcePublish",
    component: () =>
      import("client/view/WaterControlBureau/ResourcePublish/edit"),
    exact: true,
    key: "WaterControlBureauResourcePublish",
    namespace: ["WaterControlBureauResourcePublish", "newsMatrix"],
  },
  // 水利局-资源管理 tab
  {
    path: "/WaterControlBureau/ResourceMgt/:tab",
    component: () => import("client/view/WaterControlBureau/ResourceMgt"),
    exact: true,
    key: "WaterControlBureauResourceMgt",
    namespace: ["WaterControlBureauResourceMgt", "newsMatrix"],
  },
  // 水利局-资源管理
  {
    path: "/WaterControlBureau/ResourceMgt",
    component: () => import("client/view/WaterControlBureau/ResourceMgt"),
    exact: true,
    key: "WaterControlBureauResourceMgt",
    namespace: ["WaterControlBureauResourceMgt", "newsMatrix"],
  },
  // 水利局-栏目管理
  {
    path: "/WaterControlBureau/ColumnMgt",
    component: () => import("client/view/WaterControlBureau/ColumnMgt"),
    exact: true,
    key: "WaterControlBureauColumnMgt",
    namespace: ["WaterControlBureauColumnMgt", "newsMatrix"],
  },
  // 组织历史管理
  {
    path: "/History-Management",
    component: () =>
      import("client/view/promotionManagement/History-Management"),
  },
  //邢家桥大屏
  {
    path: "/xjq-large-size",
    component: () => import("client/view/xjqLargeSize"),
    exact: true,
    key: "xjqLargeSize",
  },
  // 组织活力指数榜
  {
    path: "/organizational-vitality-ranking",
    component: () => import("client/view/organizational-vitality-ranking"),
    exact: true,
    key: "organizational-vitality-ranking",
  },
  // 党员活力指数榜
  {
    path: "/member-vitality-ranking",
    component: () => import("client/view/member-vitality-ranking"),
    exact: true,
    key: "member-vitality-ranking",
  },
  // 个人活力指数榜
  {
    path: "/individual-vitality-ranking",
    component: () => import("client/view/individual-vitality-ranking"),
    exact: true,
    key: "individual-vitality-ranking",
  },
  // 入驻审核
  {
    path: "/ResidentAudit",
    component: () => import("client/view/ResidentAudit"),
    exact: true,
    key: "ResidentAudit",
  },
  // 入驻审核--审核与详情页面
  {
    path: "/ResidentAuditDetails",
    component: () => import("client/view/ResidentAudit/detail"),
    exact: true,
    key: "ResidentAuditDetails",
  },
  {
    path: "/PartyServiceCenter",
    component: () => import("client/view/PartyServiceCenter"),
    exact: true,
    key: "PartyServiceCenter",
  },
  {
    path: "/cadre-system/release-evaluation",
    component: () => import("client/view/cadre-system/release-evaluation"),
    exact: true,
    oneself: true,
    key: "ReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/test-code",
    component: () => import("client/view/cadre-system/test-code"),
    exact: true,
    oneself: true,
    key: "TestCode",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/evaluation-list",
    component: () => import("client/view/cadre-system/evaluation-list"),
    exact: true,
    oneself: true,
    key: "EvaluationList",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/middle-release-evaluation",
    component: () =>
      import("client/view/cadre-system/middle-release-evaluation"),
    exact: true,
    oneself: true,
    key: "ReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/mechanism-test-code",
    component: () => import("client/view/cadre-system/mechanism-test-code"),
    exact: true,
    oneself: true,
    key: "ReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/middle-test-code",
    component: () => import("client/view/cadre-system/middle-test-code"),
    exact: true,
    oneself: true,
    key: "TestCode",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  // {
  //   path: "/cadre-system/middle-list",
  //   component: () => import("client/view/cadre-system/middle-list"),
  //   exact: true,
  //   oneself: true,
  //   key: "EvaluationList",
  //   nolayout: false, // 是否需要layout布局
  //   namespace: ["userInfo"],
  // },
  {
    path: "/cadre-system/xuncha-release-evaluation",
    component: () =>
      import("client/view/cadre-system/xuncha-release-evaluation"),
    exact: true,
    oneself: true,
    key: "ReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/xuncha-test-code",
    component: () => import("client/view/cadre-system/xuncha-test-code"),
    exact: true,
    oneself: true,
    key: "XunChaTestCode",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/xuncha-list",
    component: () => import("client/view/cadre-system/xuncha-evaluation-list"),
    exact: true,
    oneself: true,
    key: "EvaluationList",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/zhongceng-test-code",
    component: () => import("client/view/cadre-system/zhongceng-test-code"),
    exact: true,
    oneself: true,
    key: "ZhongCengTestCode",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/zhongceng-release-evaluation",
    component: () =>
      import("client/view/cadre-system/zhongceng-release-evaluation"),
    exact: true,
    oneself: true,
    key: "ZhongcengReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/zhongceng-list",
    component: () => import("client/view/cadre-system/zhongceng-list"),
    exact: true,
    oneself: true,
    key: "ZhongCengList",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/mechanism-list",
    component: () => import("client/view/cadre-system/mechanism-list"),
    exact: true,
    oneself: true,
    key: "MechanismList",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/mechanism-release-evaluation",
    component: () =>
      import("client/view/cadre-system/mechanism-release-evaluation"),
    exact: true,
    oneself: true,
    key: "MechanismReleaseEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-information-maintenance",
    component: () => import("client/view/cadre-information-maintenance"),
    exact: true,
    oneself: true,
    key: "CadreInformationMaintenance",
  },
  // 干部信息管理
  {
    path: "/cadre-management",
    component: () => import("client/view/cadre-management"),
    exact: true,
    oneself: true,
    key: "cadreManagement",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/authority-management/role-competence",
    component: () => import("client/view/cadre-system/role-competence"),
    exact: true,
    oneself: true,
    key: "cadreSystemRoleCompetence",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/authority-management/user-competence",
    component: () => import("client/view/cadre-system/user-competence"),
    exact: true,
    oneself: true,
    key: "cadreSystemUserCompetence",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  // 表扬表彰
  {
    path: "/praise-commendation",
    component: () => import("client/view/praise-commendation"),
    exact: true,
    oneself: true,
    key: "praiseAndCommendation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  // 负面清单
  {
    path: "/negative-list",
    component: () => import("client/view/negative-list"),
    exact: true,
    oneself: true,
    key: "negativeList",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  //
  {
    path: "/sequence-management",
    component: () => import("client/view/sequence-management"),
    exact: true,
    oneself: true,
    key: "sequenceManagement",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },

  {
    path: "/sequence-detail",
    component: () => import("client/view/sequence-detail"),
    exact: true,
    oneself: true,
    key: "sequenceDetail",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/organizational-sequence-management",
    component: () => import("client/view/organizational-sequence-management"),
    exact: true,
    oneself: true,
    key: "organizationalSequenceManagement",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/organizational-sequence-detail",
    component: () => import("client/view/organizational-sequence-detail"),
    exact: true,
    oneself: true,
    key: "organizationalSequenceDetail",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/annual-assessment",
    component: () => import("client/view/cadre-system/annual-assessment"),
    exact: true,
    oneself: true,
    key: "annualAssessment",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/significant-performance",
    component: () => import("client/view/cadre-system/significant-performance"),
    exact: true,
    oneself: true,
    key: "significantPerformance",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/appointment-records",
    component: () => import("client/view/cadre-system/appointment-records"),
    exact: true,
    oneself: true,
    key: "appointmentRecords",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/import-page",
    component: () => import("client/view/cadre-system/import-page/index"),
    exact: true,
    oneself: true,
    key: "importPage",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/petition-reporting",
    component: () => import("client/view/cadre-system/petition-reporting"),
    exact: true,
    oneself: true,
    key: "petitionReporting",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/patrol",
    component: () => import("client/view/cadre-system/patrol"),
    exact: true,
    oneself: true,
    key: "patrol",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/personal-matters",
    component: () => import("client/view/cadre-system/personal-matters"),
    exact: true,
    oneself: true,
    key: "personalMatters",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/marital-status",
    component: () => import("client/view/cadre-system/marital-status"),
    exact: true,
    oneself: true,
    key: "maritalStatus",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/overseas-record",
    component: () => import("client/view/cadre-system/overseas-record"),
    exact: true,
    oneself: true,
    key: "overseasRecord",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/team-visit",
    component: () => import("client/view/cadre-system/team-visit"),
    exact: true,
    oneself: true,
    key: "TeamVisit",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/annual-report",
    component: () => import("client/view/cadre-system/annual-report"),
    exact: true,
    oneself: true,
    key: "AnnualReport",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/take-advice",
    component: () => import("client/view/cadre-system/take-advice"),
    exact: true,
    oneself: true,
    key: "TakeAdvice",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/training",
    component: () => import("client/view/cadre-system/training"),
    exact: true,
    oneself: true,
    key: "training",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  //职务管理
  {
    path: "/position-management",
    component: () => import("client/view/position-management"),
    exact: true,
    oneself: true,
    key: "positionManagement",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
    //班子成员
  {
    path: "/cadre-party-member",
    component: () => import("client/view/cadre-system/cadre-party-member"),
    exact: true,
    oneself: true,
    key: "cadrePartyMember",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  //动议方案导出
  {
    path: "/motion-plan",
    component: () => import("client/view/cadre-system/motion-plan"),
    exact: true,
    oneself: true,
    key: "MotionPlan",
  },
  {
    path: "/evaluation-management/evaluation-config",
    component: () =>
      import("client/view/evaluation-management/evaluation-config"),
    exact: true,
    oneself: true,
    key: "EvaluationConfig",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-stats/team-eval",
    component: () => import("client/view/evaluation-stats/team-eval"),
    exact: true,
    oneself: true,
    key: "TeamEval",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-stats/primary-eval",
    component: () => import("client/view/evaluation-stats/primary-eval"),
    exact: true,
    oneself: true,
    key: "PrimaryEval",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-stats/deputy-eval",
    component: () => import("client/view/evaluation-stats/deputy-eval"),
    exact: true,
    oneself: true,
    key: "DeputyEval",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-stats/general-eval",
    component: () => import("client/view/evaluation-stats/general-eval"),
    exact: true,
    oneself: true,
    key: "GeneralEval",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-publish",
    component: () => import("client/view/evaluation-publish"),
    exact: true,
    oneself: true,
    key: "EvaluationPublish",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-publish/create-evaluation",
    component: () => import("client/view/evaluation-publish/create-evaluation"),
    exact: true,
    oneself: true,
    key: "CreateEvaluation",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-publish/evaluation-code",
    component: () => import("client/view/evaluation-publish/evaluation-code"),
    exact: true,
    oneself: true,
    key: "EvaluationCode",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/evaluation-publish/evaluation-detail",
    component: () => import("client/view/evaluation-publish/evaluation-detail"),
    exact: true,
    oneself: true,
    key: "Evaluationdetail",
    nolayout: false, // 是否需要layout布局
    namespace: ["userInfo"],
  },
];
// 干部全息系统
const cadreRouter = [
  {
    path: "/login",
    component: () => import("client/view/cadre-system/login"),
    exact: true,
    oneself: true,
    key: "login",
    nolayout: true, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/menu",
    component: () => import("client/view/cadre-system/menu"),
    exact: true,
    oneself: true,
    key: "CadreSystemMenu",
    nolayout: true, // 是否需要layout布局
    namespace: ["userInfo"],
  },
  {
    path: "/cadre-system/home",
    component: () => import("client/view/cadre-system/home"),
    exact: true,
    oneself: true,
    key: "CadreSystemHome",
    nolayout: true, // 是否需要layout布局
    namespace: ["userInfo"],
  },
];
const routes = outRouter.concat(inRouter);
const Router = (props) => {
  const { history, match, app } = props;
  return (
    <Switch>
      {cadreRouter.map((r) => {
        const PageComponent = dynamic({
          app,
          component: r.component,
        });
        return (
          <Route
            path={r.path}
            exact={r.exact}
            key={r.key}
            component={PageComponent}
          />
        );
      })}
      {outRouter &&
        outRouter.map((r) => {
          const PageComponent = dynamic({
            app,
            component: r.component,
          });

          if (r.nolayout) {
            return (
              <Route
                path={r.path}
                exact={r.exact}
                key={r.key}
                component={PageComponent}
              />
            );
          }
          // 需要login layout 的页面
          if (r.oneself) {
            return (
              <Route
                path={r.path}
                exact
                key={r.key}
                history={history}
                children={(props) => (
                  <LoginLayout component={PageComponent} {...props} />
                )}
              />
            );
          } else if (r.layoutType === "activeOrganization") {
            // 激活页面
            return (
              <Route
                path={r.path}
                exact
                key={r.key}
                history={history}
                component={ActiveOrganization}
              />
            );
          } else {
            // 需要全局layout 的页面
            const ExpectPage = (props, desc) => {
              return <Expect desc={desc} {...props} />;
            };

            return (
              <Route
                path={r.path}
                key={r.key}
                history={history}
                match={match}
                children={(props) => (
                  <MainContent history={history} {...props} inRouter={inRouter}>
                    {inRouter &&
                      inRouter.map((inr, index) => {
                        const PageComponent2 = dynamic({
                          app,
                          component: inr.component,
                        });
                        if (inr.type === "custumProps") {
                          return (
                            <Route
                              path={inr.path}
                              exact
                              key={`${inr.key}${index}`}
                              render={(props) => ExpectPage(props, inr.desc)}
                            />
                          );
                        }

                        return (
                          <Route
                            path={inr.path}
                            exact
                            key={`${inr.key}${index}`}
                            component={PageComponent2}
                          />
                        );
                      })}
                    {/* 以下方法避免用户手动输入router访问无权限页面 */}
                    {/* <Redirect to="/" /> */}
                  </MainContent>
                )}
              />
            );
          }
        })}
      {/* <Redirect to="/login"/> */}
    </Switch>
  );
};

export { routes, Router };
