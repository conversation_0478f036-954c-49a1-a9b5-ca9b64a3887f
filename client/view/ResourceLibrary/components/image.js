import React, { Component } from "react";
import { Icon, message } from "antd";
import Clip from "components/clip/clipping";
import { CDN } from "apis/config";
import SelfIcon from "components/self-icon";

class Image extends Component {
  constructor() {
    super();
    this.state = {
      showClip: false,
    };
    this.onClick = this._onClick.bind(this);
    this.cancel = this._cancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
    this.delete = this._Delete.bind(this);
  }
  _onClick() {
    this.setState({
      showClip: true,
    });
  }
  // 提交图片
  _onSubmit(base64) {
    const self = this;
    return new Promise((r, j) => {
      this.props.events
        .upload(base64)
        .then(() => {
          self.setState({
            showClip: false,
          });
          r();
        })
        .catch((e) => {
          j(e);
        });
    });
  }
  // 关闭裁剪框
  _cancel() {
    this.setState({
      showClip: false,
    });
  }

  _Delete() {
    this.props.delete();
  }

  render() {
    const { size, src, wrapClass } = this.props;
    const reg = /http?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/; //判断图片地址是否是http
    var imgSrc = "";
    // src.url.includes("http")
    if (src) {
      if (src.url && !src.url.includes("http")) {
        imgSrc = `${CDN}/${src.url}`;
      } else {
        imgSrc = `${src.url}`;
      }
    }
    return (
      <div className="image">
        {src && src.url ? (
          <div className="image-view" style={wrapClass}>
            <img
              src={imgSrc}
              alt=""
              className={`view`}
              style={wrapClass}
              onClick={this.onClick}
            />
            <div className="upload-done-action-shadow" style={wrapClass}>
              <span title="重新上传" onClick={this.onClick}>
                <SelfIcon type="gsg-shangchuantupian" />
              </span>
              <span title="删除图片" onClick={this.delete}>
                <SelfIcon type="gsg-shanchu4" />
              </span>
            </div>
          </div>
        ) : (
          <div className="text" onClick={this.onClick}>
            <Icon type="plus" />
            <div>点击上传封面</div>
          </div>
        )}
        {this.state.showClip ? (
          <Clip
            title={"资源封面"}
            src={imgSrc}
            size={size}
            onCancel={this.cancel}
            onSubmit={this.onSubmit}
          />
        ) : null}
      </div>
    );
  }
}

export default Image;
