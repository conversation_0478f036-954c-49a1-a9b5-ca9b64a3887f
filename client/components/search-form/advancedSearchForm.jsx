// components/AdvancedSearchForm.tsx
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
} from "antd";
import { queryByCode } from "client/apis/cadre-portrait";
import moment from "moment";
import { useEffect, useState } from "react";
import "./asset/formLess/advance.less";
import PocFormItem from "./asset/js/pocFormItem";
import {
  Attention,
  FullTimeEducation,
  Gender,
  Nation,
  Origin,
  Political,
  Society,
  Training,
} from "./type.js";
const { RangePicker } = DatePicker;
const { Option } = Select;

const AdvancedSearchForm = ({ form }) => {
  const { getFieldDecorator, getFieldsValue } = form;
  const [codeMap, setCodeMap] = useState({
    cadreCategoryOption: [],
    identityOption: [],
    cadreLevelOption: [],
    currentRankOption: [], //干部职务层次
    assignTypeOption: [], //类别
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initCode(2001, "cadreCategoryOption");
    initCode(2002, "identityOption");
    initCode(96300, "cadreLevelOption");
    initCode(2003, "currentRankOption");
    initCode(10013, "assignTypeOption");
  }, []);
  const initCode = async (code, key) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      setCodeMap((codeMap) => {
        codeMap[key] = res.data.data;

        return { ...codeMap };
      });
    }
  };
  const formatDate = (dateString, format = "YYYY-MM") => {
    if (!dateString) return null;
    return moment(dateString).format(format);
  };

  const renderDateRangePicker = (
    label,
    startFieldName,
    endFieldName,
    format = "YYYY-MM"
  ) => (
    <div className="date-flex">
      <Form.Item label={label} className="form_title">
        {getFieldDecorator(startFieldName)(
          <DatePicker placeholder="开始日期" format={format} />
        )}
      </Form.Item>
    </div>
  );

  const renderNumberRangeInput = (label, gteFieldName, lteFieldName) => (
    <div className="date-flex">
      <Form.Item label={label} className="form_title">
        {getFieldDecorator(gteFieldName)(
          <div style={{ display: "flex", alignItems: "center" }}>
            <span className="text-style">大于</span>
            <Input placeholder="请输入" className="custom-input" />
            <span className="text-year">年</span>
            {getFieldDecorator(lteFieldName)(
              <div style={{ display: "flex", alignItems: "center" }}>
                <span className="text-style">小于</span>
                <Input placeholder="请输入" className="custom-input" />
                <span className="text-year">年</span>
              </div>
            )}
          </div>
        )}
      </Form.Item>
    </div>
  );
  const onReset = () => {
    setFormState({
      // 重置表单状态
      name: "",
      gender: "",
      ethic: "",
      birthday_start: null,
      birthday_end: null,
      join_time_start: null,
      join_time_end: null,
      political: "",
      profession_specialty: "",
      technical_position: "",
      train: "",
      talents_type: "",
      cadre_category: "",
      identity: "",
      assign_start: null,
      assign_end: null,
      assign_type: "",
      current_job: "",
      current_job_time_gte: null,
      current_job_time_lte: null,
      current_rank: "",
      current_rank_time_gte: null,
      current_rank_time_lte: null,
      work_resume_start: null,
      cadre_rank: "",
      cadre_rank_time_gte: null,
      cadre_rank_time_lte: null,
      full_time_education: "",
      on_job_education: "",
      degree: "",
      full_time_school: "",
      major: "",
      on_job_school: "",
      on_job_specialty: "",
      year_examine_start: null,
      year_examine_end: null,
      examine_count: null,
      examine_level: "",
      base_year_start: null,
      base_year_end: null,
      rank_start: null,
      rank_end: null,
      department_type: "",
      source: "",
      relationship: "",
      attention: "",
      information: "",
      responsibilities: "",
      speciality: "",
      label: "",
    });
  };

  const onSearch = () => {
    // 查询逻辑
    const values = getFieldsValue();
    const formattedBirthdayStart = formatDate(values.birthday_start);
    const formattedBirthdayEnd = formatDate(values.birthday_end);
    console.log("表单数据:", values);
  };
  return (
    <div className="advanced-search-form">
      <Form layout="vertical">
        {/* 基本信息 */}
        <Col span={24}>
          <section className="basic-info">
            <h3 className="title-with-indicator">基本信息</h3>
            <div className="basic-info-row">
              {" "}
              <Form.Item label="姓名" className="form_title">
                {getFieldDecorator("name")(<Input placeholder="请输入" />)}
              </Form.Item>
              <Form.Item label="性别" className="form_title">
                {getFieldDecorator("gender", {
                  initialValue: [], // 默认选中值
                })(
                  <Checkbox.Group
                    options={[
                      { label: "男性", value: Gender.Male },
                      { label: "女性", value: Gender.Female },
                    ]}
                  />
                )}
              </Form.Item>
              <Form.Item label="民族" className="form_title">
                {getFieldDecorator("ethnic", {
                  initialValue: [], // 默认选中值
                })(
                  <Checkbox.Group
                    options={[
                      { label: "汉族", value: Nation.Han },
                      { label: "蒙古族", value: Nation.Mongolian },
                      { label: "其他", value: Nation.Other },
                    ]}
                  />
                )}
              </Form.Item>
              {renderDateRangePicker("出生年月", "birthday_start", "YYYY-MM")}
              {renderDateRangePicker("入党时间", "join_time_start", "YYYY-MM")}
              <Form.Item label="政治面貌" className="form_title">
                {getFieldDecorator("political", {
                  initialValue: [], // 默认选中值
                })(
                  <Checkbox.Group
                    options={[
                      { label: "中共党员", value: Political.CPCMember },
                      { label: "非党员", value: Political.NonMember },
                    ]}
                  />
                )}
              </Form.Item>
            </div>
            <div className="line_wave"></div>
            <div className="basic-info-row">
              {" "}
              <Form.Item label="熟悉专业和特长" className="form_title">
                {getFieldDecorator("profession_specialty")(
                  <Input placeholder="请输入" />
                )}
              </Form.Item>
              <Form.Item label="专业技术职务" className="form_title">
                {getFieldDecorator("profession_specialty")(
                  <Input placeholder="请输入" />
                )}
              </Form.Item>
              <Form.Item label="培训情况" className="form_title">
                {getFieldDecorator("train", {
                  initialValue: [], // 默认选中值
                })(
                  <Checkbox.Group
                    options={[
                      { label: "参加过培训", value: Training.Participated },
                      { label: "优秀学员", value: Training.Outstanding },
                    ]}
                  />
                )}
              </Form.Item>
            </div>
          </section>
        </Col>

        {/* 职务信息 */}
        <Col span={24}>
          <section className="job-info">
            <h3 className="title-with-indicator">职务信息</h3>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="干部类别" className="form_title">
                  {getFieldDecorator("cadre_type")(
                    <Checkbox.Group
                      options={codeMap.cadreCategoryOption.map(
                        (item) => item.op_value
                      )}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="现任职务" className="form_title">
                  {getFieldDecorator("current_job")(
                    <Input placeholder="请输入" />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                {renderNumberRangeInput(
                  "任现职务时间",
                  "current_job_time_gte",
                  "current_job_time_lte"
                )}
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="干部职务层次" className="form_title">
                  {getFieldDecorator("current_rank")(
                    <Checkbox.Group
                      options={codeMap.currentRankOption.map(
                        (item) => item.op_value
                      )}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={12}>
                {renderNumberRangeInput(
                  "现职级任职时间",
                  "current_rank_time_gte",
                  "current_rank_time_lte"
                )}
              </Col>
              {/* <Col span={12}>
                <Form.Item label="干部身份" className="form_title">
                  {getFieldDecorator("identity")(
                    <Checkbox.Group
                      options={codeMap.identityOption.map(
                        (item) => item.op_value
                      )}
                    />
                  )}
                </Form.Item>
              </Col> */}
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="干部职级" className="form_title">
                  {getFieldDecorator("cadre_rank")(
                    <Checkbox.Group
                      options={codeMap.cadreLevelOption.map(
                        (item) => item.op_value
                      )}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={12}>
                {renderDateRangePicker(
                  "干部任用情况(研究时间)",
                  "assign_start",
                  "YYYY-MM"
                )}
              </Col>
              <Col span={12}>
                <Form.Item label="类别" className="form_title">
                  {getFieldDecorator("assign_type")(
                    <Checkbox.Group
                      options={codeMap.assignTypeOption.map(
                        (item) => item.op_value
                      )}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
          </section>
        </Col>

        {/* 教育信息 */}
        <Col span={24}>
          <section className="education-info">
            <h3 className="title-with-indicator">教育信息</h3>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="学历(全日制)" className="form_title">
                  {getFieldDecorator("full_time_education", {
                    initialValue: [], // 默认值
                  })(
                    <Checkbox.Group
                      options={[
                        { label: "研究生", value: FullTimeEducation.Graduate },
                        {
                          label: "大学本科",
                          value: FullTimeEducation.Bachelor,
                        },
                        { label: "大学专科", value: FullTimeEducation.College },
                      ]}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="学历(全职)" className="form_title">
                  {getFieldDecorator("on_job_education", {
                    initialValue: [], // 默认值
                  })(
                    <Checkbox.Group
                      options={[
                        { label: "研究生", value: FullTimeEducation.Graduate },
                        {
                          label: "大学本科",
                          value: FullTimeEducation.Bachelor,
                        },
                        { label: "大学专科", value: FullTimeEducation.College },
                      ]}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="学位" className="form_title">
                  {getFieldDecorator("degree", {
                    initialValue: [], // 默认值
                  })(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="全日制院校" className="form_title">
                  {getFieldDecorator("full_time_school")(
                    <Input placeholder="请输入" />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="专业" className="form_title">
                  {getFieldDecorator("major")(<Input placeholder="请输入" />)}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="在职院校" className="form_title">
                  {getFieldDecorator("on_job_school")(
                    <Input placeholder="请输入" />
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="专业" className="form_title">
                  {getFieldDecorator("on_job_specialty")(
                    <Input placeholder="请输入" />
                  )}
                </Form.Item>
              </Col>
            </Row>
          </section>
        </Col>

        {/* 其他信息 */}
        <Col span={24}>
          <section className="other-info">
            <h3 className="title-with-indicator">其他信息</h3>
            <Row gutter={16}>
              <Col span={8}>
                <PocFormItem name="full_time_education" label="年度考核">
                  <div className="year-aprove form-common-style">
                    <Select placeholder="请选择" className="custom-select">
                      {Array.from({ length: 2025 - 2019 + 1 }, (_, i) => (
                        <Option key={2019 + i} value={(2019 + i).toString()}>
                          {2019 + i}
                        </Option>
                      ))}
                    </Select>

                    <span className="line"></span>
                    <Form.Item name="year_examine_end">
                      {getFieldDecorator("year_examine_end")(
                        <Select placeholder="请选择" className="custom-select">
                          {Array.from({ length: 2025 - 2019 + 1 }, (_, i) => (
                            <Option
                              key={2019 + i}
                              value={(2019 + i).toString()}
                            >
                              {2019 + i}
                            </Option>
                          ))}
                        </Select>
                      )}
                    </Form.Item>
                    <span className="text-style">年有</span>
                    <Form.Item name="examine_count">
                      {getFieldDecorator("examine_count")(
                        <Select placeholder="请选择" className="custom-select">
                          {Array.from({ length: 10 }, (_, i) => (
                            <Option key={i + 1} value={(i + 1).toString()}>
                              {i + 1}
                            </Option>
                          ))}
                        </Select>
                      )}
                    </Form.Item>
                    <span className="text-style">次</span>
                    <Form.Item name="examine_level">
                      {getFieldDecorator("examine_level")(
                        <Select placeholder="请选择" className="custom-select">
                          <Option value="1">优秀</Option>
                          <Option value="2">称职</Option>
                          <Option value="4">基本称职</Option>
                          <Option value="6">不称职</Option>
                        </Select>
                      )}
                    </Form.Item>
                  </div>
                </PocFormItem>
              </Col>
              <Col span={8}>
                <Form.Item label="基层年限" className="form_title">
                  <Row gutter={8}>
                    <Col span={11}>
                      {getFieldDecorator("base_year_start")(
                        <Input placeholder="开始年限" />
                      )}
                    </Col>
                    <Col span={2}>-</Col>
                    <Col span={11}>
                      {getFieldDecorator("base_year_end")(
                        <Input placeholder="结束年限" />
                      )}
                    </Col>
                  </Row>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="干部指数排名" className="form_title">
                  <Row gutter={8}>
                    <Col span={11}>
                      {getFieldDecorator("cadre_rank_time_gte")(
                        <Input placeholder="请输入" />
                      )}
                    </Col>
                    <Col span={2} className="text-rank">
                      排名
                    </Col>
                    <Col span={11}>
                      {getFieldDecorator("cadre_rank_time_lte")(
                        <Input placeholder="请输入" />
                      )}
                    </Col>
                  </Row>
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="干部来源" className="form_title">
                  {getFieldDecorator("source", {
                    initialValue: [], // 默认选中值
                  })(
                    <Checkbox.Group
                      options={[
                        { label: "选调生", value: Origin.Selective },
                        { label: "村官", value: Origin.VillageOfficial },
                        { label: "五方面人才", value: Origin.FiveAspects },
                      ]}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="社会关系" className="form_title">
                  {getFieldDecorator("society", {
                    initialValue: [], // 默认选中值
                  })(
                    <Checkbox.Group
                      options={[
                        {
                          label: "有社会关系在体制内",
                          value: Society.RelationInSystem,
                        },
                        {
                          label: "夫妻双方都在体制内",
                          value: Society.BothInSystem,
                        },
                      ]}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="班子回访评价" className="form_title">
                  {getFieldDecorator("attention", {
                    initialValue: [], // 默认选中值
                  })(
                    <Checkbox.Group
                      options={[
                        { label: "重点关注", value: Attention.KeyFocus },
                      ]}
                    />
                  )}
                </Form.Item>
              </Col>
            </Row>
            <div className="line_wave"></div>
            <Row gutter={16}>
              <Col span={8}>
                {getFieldDecorator("cadre_rank_time_gte")(
                  <Input placeholder="请输入" />
                )}
              </Col>
              <Col span={8}>
                {getFieldDecorator("cadre_rank_time_gte")(
                  <Input placeholder="请输入" />
                )}
              </Col>
              <Col span={8}>
                {getFieldDecorator("cadre_rank_time_gte")(
                  <Input placeholder="请输入" />
                )}
              </Col>
            </Row>
          </section>
        </Col>
      </Form>
      <div className="button-box">
        <Button onClick={onReset}>重置</Button>
        <Button type="primary" onClick={onSearch}>
          查询
        </Button>
      </div>
    </div>
  );
};

export default Form.create()(AdvancedSearchForm);
