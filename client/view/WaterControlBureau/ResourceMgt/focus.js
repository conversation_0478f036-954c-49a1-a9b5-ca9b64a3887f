import React, { Component } from "react";

import Clip from "components/clip/clipping";

import { uploadBase64File } from "apis/file";
import {message, Spin} from "antd"

class Focus extends Component {
  constructor() {
    super();
    this.upload = this._upload.bind(this);
    this.onCancel = this._onCancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
  }
  _upload(base64) {
    return new Promise((r, j) => {
      uploadBase64File({
        upfile: base64,
        upType: "no-compress-image",
        is_water: 0,
        is_val: 0
      })
        .then(data => {
          const _data = data.data;
          if (_data.code != 0) {
            message.error(_data.message);
            j(_data.message);
          } else {
            r(_data);
          }
        })
        .catch(e => j(e));
    });
  }
  _onSubmit(base64) {
    const self = this
    const {setFocusInfoSubmit, dispatch} = this.props
    return new Promise((r, j) => {

    this.upload(base64).then((data) => {
      // 这里的加载画面要等到那边上传成功重新加载列表前才取消
      setFocusInfoSubmit(data.data);
      r();
    }).catch(e => {
      message.error(e);
      j(e)
     })
    })
  } //listLoading
  _onCancel() {
    const { dispatch } = this.props;
    dispatch({
      type: "newsList/save",
      payload: {
        focusIsShow: false,
        editPhotosData: {},
        focusIsEdit: undefined,
        focusDisplayImageUrl: ''
      }
    });
  }
  render() {
    const size = {
      w: 1080,
      h: 558
    }
    return (
      <div className="mmk">
        <Clip size={size} onCancel={this.onCancel} onSubmit={this.onSubmit} src={this.props.focusDisplayImageUrl}/>
        <div style={{ display: (this.props.showImageSubmitOpacity === true) ? 'block' : 'none'  }} class="new-list-image-submit-opacity"> <Spin delay={0} tip="上传中······" size="large" style={{ position: 'relative',top: '50%' }} /></div>
      </div>
    );
  }
}

export default Focus;
