import React, {useState, useEffect, useRef} from 'react'
import {
    Spin,
    Button,
    Form,
    Input,
    Tree,
    AutoComplete,
    Table,
    Modal,
    Popconfirm,
    Row,
    Col,
    Checkbox,
    message
} from 'antd'
import _ from "lodash"
import {getOrgTree, locateOrgTree, getTreeList, findOrgByName} from "client/apis/organize"
import {
    loadLeader<PERSON>ist,
    addLeader,
    deleteLeader,
    addLeaderOrg,
    addLeaderCadre
} from "client/apis/evaluation-management/evaluation-config"
import {queryCadreList} from "client/apis/cadre-portrait"

const {TreeNode} = Tree
let scrollTreeNodeId
const LeaderConfig = ({userInfo, form}) => {
    const [pageLoading, setPageLoading] = useState(false)
    const [modalLoading, setModalLoading] = useState(false)
    const [cadreLoading, setCadreLoading] = useState(false)
    const [tableData, setTableData] = useState([])
    const [page, setPage] = useState(1)
    const [total, setTotal] = useState(0)
    const [cadreData, setCadreData] = useState([])
    const [checkedCadre, setCheckedCadre] = useState([])
    const [orgTreeTabSelectData, setOrgTreeTabSelectData] = useState({})
    const [treeProps, setTreeProps] = useState({})
    const [departmentAutoCompleteProps, setDepartmentAutoCompleteProps] = useState({dataSource: []})
    const [currentLeader, setCurrentLeader] = useState({})
    const [showAddModal, setShowAddModal] = useState(false)
    const [showDepartmentModal, setShowDepartmentModal] = useState(false)
    const [showLeaderModal, setShowLeaderModal] = useState(false)
    const [checkedDepartment, setCheckedDepartment] = useState([])
    const treePropsRef = useRef(treeProps)
    const currentLeaderRef = useRef(currentLeader)
    const throttledSearchRef = useRef()
    const columns = [
        {
            title: '领导姓名',
            dataIndex: 'leader_name',
            align: 'center',
            width: 120
        },
        {
            title: '职务',
            dataIndex: 'current_job',
            ellipsis: true,
            align: 'center'
        },
        {
            title: '分管部门（联系乡镇）',
            dataIndex: 'org_list',
            ellipsis: true,
            align: 'center',
            render: (org_list, row) => {
                if (org_list.length) {
                    return (
                        <span>共{org_list.length}个：{org_list.map(item => item.org_name).join('、')}</span>
                    )
                }
            }
        },
        {
            title: '分管干部',
            dataIndex: 'user_info_list',
            ellipsis: true,
            align: 'center',
            render: (user_info_list) => {
                if (user_info_list.length) {
                    return (
                        <span>共{user_info_list.length}人：{user_info_list.map(item => item.username).join('、')}</span>
                    )
                }
            }
        },
        {
            title: '操作',
            key: 'action',
            align: 'center',
            render: (row) => {
                return (
                    <span>
                        <a onClick={() => openDepartmentModal(row)}>分管部门</a>
                        <a style={{margin: "0 20px"}} onClick={() => {
                            openLeaderModal(row)
                        }}>分管干部</a>
                        <Popconfirm
                            title="确定删除吗?"
                            onConfirm={() => deleteTableData(row)}
                            okText="确定"
                            cancelText="取消"
                        >
                            <a>删除</a>
                        </Popconfirm>
                    </span>
                )
            }
        }
    ]

    const getTableData = async () => {
        setPageLoading(true)
        const {data: {data, total = 0}} = await loadLeaderList({page})
        setTableData(data)
        setTotal(total)
        setPageLoading(false)
    }
    const handlePageChange = (pagination) => {
        setPage(pagination.current)
    }
    const getTreeTabList = async () => {
        const {data: {data}} = await getTreeList()
        setOrgTreeTabSelectData(data[0])
    }
    const getTreeData = async (params) => {
        if (!params.org_id) {
            const {data: {data}} = await getOrgTree(
                {
                    org_id: userInfo.oid,
                    tree_type: params.tree_type,
                    load_root: 1,
                    org_type: params.org_type
                }
            )
            if (!data.length) return
            setTreeProps({
                checkable: true,
                selectedKeys: [String(data[0].org_id)],
                checkedKeys: {},
                expandedKeys: [String(data[0].org_id)],
                autoExpandParent: true,
                dataSource: [...data],
                hasLoadedKeys: [String(data[0].org_id), ...data[0].children.map(item => String(item.org_id))],
                checkStrictly: true,
                onExpand: (e) => {
                    setTreeProps((prev) => ({
                        ...prev,
                        expandedKeys: e,
                        autoExpandParent: false
                    }))
                },
                onCheck: (e) => {
                    setTreeProps((prev) => ({
                        ...prev,
                        checkedKeys: e
                    }))
                    const getUniqueArr = (ids, orgList) => {
                        const idSet = new Set(ids.map(id => parseInt(id, 10)));
                        const seen = new Set();
                        const result = [];
                        for (const org of orgList) {
                            const orgId = org.org_id;
                            if (idSet.has(orgId) && !seen.has(orgId)) {
                                seen.add(orgId);
                                result.push(org);
                            }
                        }
                        return result;
                    }
                    const {org_list} = currentLeaderRef.current
                    // 合并远程数据和本地数据 并去重
                    const list = [...org_list, ...getCheckedDepartment(e.checked)]
                    setCheckedDepartment(getUniqueArr(e.checked, list))
                },
                onSelect: (e) => {
                    const {selectedKeys} = treePropsRef.current
                    if (!e.length) {
                        setTreeProps((prev) => ({
                            ...prev,
                            expandedKeys: [...new Set([...prev.expandedKeys, selectedKeys[0]])]
                        }))
                        return
                    }
                    setTreeProps((prev) => ({
                        ...prev,
                        expandedKeys: [...new Set([...prev.expandedKeys, e[0]])],
                        selectedKeys: e
                    }))
                    handleQueryCadreList(e[0])
                },
                loadData: (treeNode) => {
                    return new Promise(async (resolve) => {
                        const {children, dataRef} = treeNode.props
                        const parentKey = String(dataRef.org_id)
                        if (children && children.length || !dataRef.child_org_num) {
                            resolve()
                            return
                        }
                        const result = (
                            await getOrgTree({
                                    org_id: treeNode.props.dataRef.org_id,
                                    tree_type: params.tree_type,
                                    load_root: 0,
                                    org_type: params.org_type
                                }
                            )
                        ).data
                        if (result.code !== 0) {
                            return message.error(result.message)
                        }
                        const newDataSource = _.cloneDeep(treePropsRef.current.dataSource)
                        const hasLoadedKeys = _.cloneDeep(treePropsRef.current.hasLoadedKeys)
                        const updateNode = (nodes) => {
                            return nodes.map((node) => {
                                if (node.org_id === dataRef.org_id) {
                                    return {
                                        ...node,
                                        child_org_num: result.data.length ? node.child_org_num : 0,
                                        children: result.data.map((item) => {
                                            hasLoadedKeys.push(String(item.org_id))
                                            return ({...item})
                                        })
                                    }
                                }
                                if (node.children) {
                                    return {
                                        ...node,
                                        children: updateNode(node.children)
                                    }
                                }
                                return node
                            })
                        }
                        const updatedData = updateNode(newDataSource)
                        setTreeProps((prev) => ({
                            ...prev,
                            expandedKeys: [...new Set([...prev.expandedKeys, parentKey])],
                            hasLoadedKeys: [...new Set([...hasLoadedKeys])],
                            dataSource: updatedData
                        }))
                        resolve()
                    })
                }
            })
            handleDepartmentAutoCompleteProps()
        } else {
            const {data: {data}} = await locateOrgTree(
                {
                    root_org_id: userInfo.oid,
                    org_id: params.org_id,
                    tree_type: params.tree_type,
                    load_root: 1,
                    org_type: params.org_type
                }
            )
            if (!data.length) return
            const findParentChildren = (nodes, targetId) => {
                for (const node of nodes) {
                    for (const child of node.children) {
                        if (child.org_id === targetId) {
                            return node.children
                        }
                    }
                    const result = findParentChildren(node.children, targetId)
                    if (result) {
                        return result
                    }
                }
                return null
            }
            const hasLoadedKeys = findParentChildren(data, params.org_id).map(item => String(item.org_id))
            setTreeProps((prev) => ({
                ...prev,
                expandedKeys: [...new Set([...prev.expandedKeys, String(params.org_id)])],
                hasLoadedKeys: [...new Set([...prev.hasLoadedKeys, ...hasLoadedKeys])],
                dataSource: [...data]
            }))
        }
    }
    const handleTreeScroll = (orgId) => {
        const el = document.getElementById(orgId)
        el && el.scrollIntoView({behavior: "smooth"})
    }
    const handleQueryCadreList = async (org_id, name, callback) => {
        setCadreLoading(true)
        const {data: {data, code}} = await queryCadreList({page_size: 50, org_id, name})
        if (code === 0) {
            setCadreData(data.content)
            if (callback) {
                callback(data.content)
            }
        }
        setCadreLoading(false)
    }
    const handleFilterCadreList = (e) => {
        const value = e.target.value.trim()
        throttledSearchRef.current(value)
    }
    const handleDepartmentAutoCompleteProps = () => {
        let timer = null
        const getAutoComplete = (value) => {
            if (timer) {
                clearTimeout(timer)
                timer = null
            }
            value = value.trim()
            if (!value) {
                setDepartmentAutoCompleteProps((prev) => (
                    {
                        ...prev,
                        dataSource: []
                    }
                ))
            } else {
                timer = setTimeout(async () => {
                    const result = (
                        await findOrgByName({
                            org_id: sessionStorage.getItem("_oid"),
                            org_name: value,
                            tree_type: orgTreeTabSelectData.tree_type
                        })
                    ).data
                    if (result.code !== 0) {
                        return message.error(result.message)
                    }
                    const data = result.data
                    setDepartmentAutoCompleteProps((prev) => (
                        {
                            ...prev,
                            dataSource: data
                                .slice(0, data.length > 10 ? 10 : data.length)
                                .map((val) => ({
                                    value: val.org_id,
                                    name: val.org_name
                                }))
                        }
                    ))
                }, 300)
            }
        }
        setDepartmentAutoCompleteProps({
            dataSource: [],
            onSearch: (e) => getAutoComplete(e),
            onSelect: (e) => {
                const {hasLoadedKeys} = treePropsRef.current
                scrollTreeNodeId = e.value
                if (!hasLoadedKeys.includes(String(e.value))) {
                    getTreeData({
                        ...orgTreeTabSelectData,
                        org_id: e.value
                    })
                } else {
                    setTreeProps((prev) => ({
                        ...prev,
                        expandedKeys: [...new Set([...prev.expandedKeys, String(e.value)])],
                        autoExpandParent: true
                    }))
                }
            }
        })
    }
    const renderDepartmentAutoCompleteList = (data) => {
        return data.map((item) => (
            <AutoComplete.Option
                className="member-manage-autoComplete"
                key={item.value}
                text={item.name}
                value={item.name}
                onClick={() => departmentAutoCompleteProps.onSelect(item)}
            >
                {item.name}
            </AutoComplete.Option>
        ))
    }
    const renderTreeNodes = (data, level) => {
        if (!data) return
        level = level || 0
        const length = data.length
        let cssName = ""
        if (level < 1) {
            cssName = "first-item"
        }
        const resultData = length === 0 ? [] : data
        return resultData.map((item) => {
            if (!item) return
            let shortName
            let name = (shortName = (item.short_name || item.name).trim())
            if (name && name.length > 20) {
                name = name.substring(0, 20) + "..."
            }
            let currentCssName = ""
            if (item.children && item.children.length) {
                return (
                    <TreeNode
                        isLeaf={item.child_org_num === 0}
                        title={
                            <div
                                id={item.org_id}
                                className={cssName + " " + currentCssName}
                            >
                                <span title={item.name || shortName}>{name}</span>
                            </div>
                        }
                        parentId={item.parent_id}
                        key={item.org_id}
                        dataRef={item}
                    >
                        {renderTreeNodes(
                            item.children,
                            ++level
                        )}
                    </TreeNode>
                )
            }
            return (
                <TreeNode
                    isLeaf={item.child_org_num === 0}
                    title={
                        <div
                            id={item.org_id}
                            className={cssName + " " + currentCssName}
                        >
                            <span title={item.name || shortName}>{name}</span>
                        </div>
                    }
                    parentId={item.parent_id}
                    key={item.org_id}
                    dataRef={item}
                />
            )
        })
    }
    const openAddModal = () => {
        setShowAddModal(true)
    }
    const confirmAddModal = () => {
        form.validateFields(async (err, values) => {
            setModalLoading(true)
            const {data} = await addLeader(values)
            setModalLoading(false)
            if (data.code === 0) {
                message.success('添加成功')
            } else {
                message.error(data.message)
            }
            cancelAddModal()
            if (page === 1) {
                await getTableData()
            } else {
                setPage(1)
            }
        })
    }
    const cancelAddModal = () => {
        form.resetFields()
        setShowAddModal(false)
    }
    const deleteTableData = async (row) => {
        setPageLoading(true)
        const {data} = await deleteLeader({leaderId: row.city_leader_id})
        if (data.code === 0) {
            message.success('删除成功')
            await getTableData()
        }
        setPageLoading(false)
    }
    const openDepartmentModal = (row) => {
        setCurrentLeader(row)
        // 回显已选择的部门
        setCheckedDepartment(row.org_list)
        setTreeProps({
            ...treePropsRef.current,
            checkedKeys: {
                checked: row.org_list.map(item => item.org_id.toString()),
                halfChecked: []
            }
        })
        setShowDepartmentModal(true)
    }
    const getCheckedDepartment = (checkedKeys = []) => {
        const {dataSource = []} = treePropsRef.current
        const filterSelectedNodes = (nodes) => {
            const result = []
            for (const node of nodes) {
                if (checkedKeys.includes(node.org_id.toString())) {
                    result.push({
                        org_id: node.org_id,
                        org_name: node.name
                    })
                }
                if (node.children && node.children.length > 0) {
                    result.push(...filterSelectedNodes(node.children))
                }
            }
            return result
        }
        return filterSelectedNodes(dataSource) || []
    }
    const removeDepartment = (index) => {
        const list = _.cloneDeep(checkedDepartment)
        list.splice(index, 1)
        setCheckedDepartment(list)
        setTreeProps((prev) => ({
            ...prev,
            checkedKeys: {
                checked: list.map(item => item.org_id.toString()),
                halfChecked: []
            }
        }))
    }
    const confirmDepartmentModal = async () => {
        const {checkedKeys: {checked = []}} = treePropsRef.current
        const params = {
            leader_id: currentLeader.city_leader_id,
            org_ids: checked
        }
        setModalLoading(true)
        const {data} = await addLeaderOrg(params)
        setModalLoading(false)
        if (data.code === 0) {
            message.success('设置成功')
            // 首次设置部门时，默认将分管部门正职加入已选列表
            const {org_list} = currentLeader
            if (!org_list.length && checked.length) {
                const arr = []
                setModalLoading(true)
                const promises = checked.map(item =>
                    new Promise(resolve => {
                        handleQueryCadreList(item, '', res => {
                            arr.push(...res.filter(v => v.category && v.category.includes('正职')))
                            resolve()
                        })
                    })
                )
                await Promise.all(promises)
                if (arr.length) {
                    await confirmLeaderModal(arr)
                } else {
                    setModalLoading(false)
                    await getTableData()
                }
            } else {
                await getTableData()
            }
        } else {
            message.error(data.message)
        }
        setShowDepartmentModal(false)
    }
    const openLeaderModal = (row) => {
        if (!row.org_list.length) {
            Modal.warning({
                title: '提示',
                content: '请先设置分管部门',
                okText: '确定',
                onOk() {
                    openDepartmentModal(row)
                }
            })
            return
        }
        handleQueryCadreList(treeProps.dataSource[0].org_id)
        setCurrentLeader(row)
        const {user_info_list} = row
        user_info_list.length && setCheckedCadre(user_info_list)
        setShowLeaderModal(true)
    }
    const handleCadreCheckChange = (e, item) => {
        let cadre = _.cloneDeep(checkedCadre)
        if (e.target.checked) {
            cadre.push({
                user_id: item.user_id,
                username: item.user_name,
                job: item.current_job
            })
            setCheckedCadre(cadre)
        } else {
            cadre = cadre.filter(val => val.user_id !== item.user_id)
            setCheckedCadre(cadre)
        }
    }
    const removeCadre = (index) => {
        const cadre = _.cloneDeep(checkedCadre)
        cadre.splice(index, 1)
        setCheckedCadre(cadre)
    }
    const confirmLeaderModal = async (defaultData) => {
        setModalLoading(true)
        const cadre = defaultData || _.cloneDeep(checkedCadre)
        const {data} = await addLeaderCadre({
            leader_id: currentLeader.city_leader_id,
            user_ids: [...new Set(cadre.map(item => item.user_id))]
        })
        if (data.code === 0) {
            await getTableData()
            message.success('设置成功')
        } else {
            message.error(data.message)
        }
        setModalLoading(false)
        setShowLeaderModal(false)
    }

    useEffect(() => {
        getTableData()
    }, [page])
    useEffect(() => {
        getTreeTabList()
    }, [])
    useEffect(() => {
        if (!_.isEmpty(orgTreeTabSelectData)) getTreeData(orgTreeTabSelectData)
    }, [orgTreeTabSelectData])
    useEffect(() => {
        treePropsRef.current = treeProps
        if (scrollTreeNodeId) {
            handleTreeScroll(scrollTreeNodeId)
            scrollTreeNodeId = null
        }
    }, [treeProps])
    useEffect(() => {
        currentLeaderRef.current = currentLeader
    }, [currentLeader])
    useEffect(() => {
        throttledSearchRef.current = _.throttle((value) => {
            const {selectedKeys} = treePropsRef.current
            handleQueryCadreList(selectedKeys[0], value)
        }, 500)
        return () => {
            throttledSearchRef.current.cancel()
        }
    }, [])
    return (
        <div className="LeaderConfig-container">
            <Spin spinning={pageLoading}>
                <Button type="primary" icon="plus" onClick={openAddModal}>添加</Button>
                <div className="tip">
                    注：人大/政协领导需要对人大（含乡镇人大主席）/政协及班子成员进行测评，分管部门及干部需要加入对应领导的分管信息中。
                </div>
                <Table
                    columns={columns}
                    dataSource={tableData}
                    pagination={
                        {
                            current: page,
                            total: total
                        }
                    }
                    bordered
                    rowKey="city_leader_id"
                    onChange={handlePageChange}
                />
                <Modal
                    title="添加"
                    centered
                    visible={showAddModal}
                    width={700}
                    onCancel={cancelAddModal}
                    onOk={confirmAddModal}
                >
                    <Spin spinning={modalLoading}>
                        <Form
                            layout="inline"
                            labelAlign="left"
                            labelCol={{span: 3}}
                            wrapperCol={{span: 16}}
                            style={{height: "300px"}}
                        >
                            <Form.Item label="姓名" style={{marginBottom: "20px"}}>
                                {form.getFieldDecorator('leader_name', {
                                    rules: [{required: true, message: '请输入姓名'}]
                                })(<Input style={{width: "500px"}}/>)}
                            </Form.Item>
                            <Form.Item label="职务">
                                {form.getFieldDecorator('current_job', {
                                    rules: [{required: true, message: '请输入职位'}]
                                })(<Input style={{width: "500px"}}/>)}
                            </Form.Item>
                        </Form>
                    </Spin>
                </Modal>
                <Modal
                    className="LeaderConfig-modal department"
                    title="选择分管部门"
                    centered
                    visible={showDepartmentModal}
                    width={858}
                    onOk={confirmDepartmentModal}
                    onCancel={() => {
                        setShowDepartmentModal(false)
                        setModalLoading(false)
                    }}
                >
                    <Spin spinning={modalLoading}>
                        <Row gutter={[16, 0]}>
                            <Col span={10}>
                                <div className="LeaderConfig-modal__left">
                                    <div className="AutoComplete-container">
                                        <AutoComplete
                                            style={{width: '100%'}}
                                            allowClear
                                            onSearch={departmentAutoCompleteProps.onSearch}
                                            placeholder="输入机构名称"
                                            optionLabelProp="value"
                                        >
                                            {renderDepartmentAutoCompleteList(departmentAutoCompleteProps.dataSource)}
                                        </AutoComplete>
                                    </div>
                                    <div className="tree-container">
                                        <Tree className="tree"  {...treeProps} selectable={false}>
                                            {renderTreeNodes(treeProps.dataSource)}
                                        </Tree>
                                    </div>
                                </div>
                            </Col>
                            <Col span={14}>
                                <div className="LeaderConfig-modal__right">
                                    <div className="LeaderConfig-modal__right__top">
                                        <div>
                                            已选择<span style={{color: "#1FA1FD"}}>（{checkedDepartment.length}）</span>
                                        </div>
                                    </div>
                                    <div className="checked-list">
                                        {
                                            checkedDepartment.map((item, index) => {
                                                return (
                                                    <div key={item.org_id} className="checked-item">
                                                        <div>{item.org_name}</div>
                                                        <div className="remove-icon"
                                                             onClick={() => removeDepartment(index)}>×
                                                        </div>
                                                    </div>
                                                )
                                            })
                                        }
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Spin>
                </Modal>
                <Modal
                    className="LeaderConfig-modal leader"
                    title="选择分管干部"
                    centered
                    visible={showLeaderModal}
                    width={1200}
                    onOk={() => confirmLeaderModal(null)}
                    onCancel={() => {
                        setShowLeaderModal(false)
                        setModalLoading(false)
                    }}
                >
                    <Spin spinning={modalLoading}>
                        <Row gutter={[16, 0]}>
                            <Col span={6}>
                                <div className="LeaderConfig-modal__left">
                                    <div className="tree-container">
                                        <Tree className="tree" {...treeProps} checkable={false}>
                                            {renderTreeNodes(treeProps.dataSource)}
                                        </Tree>
                                    </div>
                                </div>
                            </Col>
                            <Col span={9}>
                                <div className="LeaderConfig-modal__center">
                                    <Input allowClear placeholder="输入姓名" style={{marginBottom: '16px'}}
                                           onChange={handleFilterCadreList}/>
                                    <div className="leader-list">
                                        {
                                            !cadreData.length && !cadreLoading && (
                                                <div className="no-data">暂无数据</div>
                                            )
                                        }
                                        <Spin spinning={cadreLoading} style={{height: '300px'}}>
                                            {cadreData.map(item => {
                                                return (
                                                    <div key={item.user_id} className="leader-list-item">
                                                        <Checkbox
                                                            checked={checkedCadre.map(item => item.user_id).includes(item.user_id)}
                                                            className="checkbox-container"
                                                            onChange={(e) => handleCadreCheckChange(e, item)}></Checkbox>
                                                        <div className="leader-list-item__text">
                                                            <div className="name">{item.user_name}</div>
                                                            <div className="position">{item.current_job}</div>
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </Spin>
                                    </div>
                                </div>
                            </Col>
                            <Col span={9}>
                                <div className="LeaderConfig-modal__right">
                                    <div className="LeaderConfig-modal__right__top">
                                        <div>已选择<span style={{color: "#1FA1FD"}}>（{checkedCadre.length}）</span></div>
                                    </div>
                                    <div className="checked-list">
                                        {
                                            checkedCadre.map((item, index) => {
                                                return (
                                                    <div>
                                                        <div key={item.user_id} className="checked-item">
                                                            <div className="checked-item__text">
                                                                <div className="name">{item.username}</div>
                                                                <div className="job">{item.job}</div>
                                                            </div>
                                                            <div
                                                                className="remove-icon"
                                                                onClick={() => removeCadre(index)}
                                                            >×
                                                            </div>
                                                        </div>
                                                    </div>
                                                )
                                            })
                                        }
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </Spin>
                </Modal>
            </Spin>
        </div>
    )
}

export default Form.create()(LeaderConfig)
