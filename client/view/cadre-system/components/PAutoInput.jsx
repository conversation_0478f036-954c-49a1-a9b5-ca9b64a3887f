import React, { useCallback, useState } from "react";
import { queryCadreList } from "client/apis/cadre-portrait";
import { Select, AutoComplete } from "antd";

class PAutoComplete extends Component {
  constructor(props) {
    super(props);
    this.state = {
      searchValue: "",
      dataSource: [],
    };
    this.timerId = null;

    this.onSearch = this.onSearch.bind(this);
    this._onChange = this._onChange.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.debounceQuery = this.debounceQuery.bind(this);
    this.onBlur = this.onBlur.bind(this);
  }

  debounce(func, delay) {
    var context = this;
    return function () {
      var args = arguments;
      clearTimeout(context.timerId);
      context.timerId = setTimeout(function () {
        func.apply(context, args);
      }, delay);
    };
  }

  onSearch(value) {
    this.setState({ searchValue: value });
    this.debounceQuery(value);
  }

  debounceQuery(value) {
    var context = this;
    queryCadreList({
      name: value,
      org_id: this.props.org_id,
      page_size: 200,
    }).then(function (res) {
      if (res.data.code === 0) {
        context.setState({ dataSource: res.data.data.content });
      }
    });
  }

  onSelect(value) {
    this.props.onChange(value);

    if (this.props.onSelect) this.props.onSelect(value);
  }

  _onChange(value) {
    const user = this.state.dataSource.find(
      (item) => String(item.user_id) === String(value)
    );

    this.props.onChange(user || value);
  }
  onBlur(e) {
    const value = this.props.value;

    // if (e && value && e === value) return;

    if (this.props.onSelect) this.props.onSelect(value);
  }
  render() {
    const { value, style = {} } = this.props;
    const { dataSource } = this.state;

    return (
      <AutoComplete
        value={(value && value.user_name) || value}
        style={{ width: 300, ...style }}
        onSearch={this.onSearch}
        onChange={(value) => {
          this._onChange(value);
        }}
        onBlur={(e) => {
          this.onBlur(e);
        }}
        showSearch
        showArrow={false}
        filterOption={false}
        defaultActiveFirstOption={false}
        notFoundContent={null}
        placeholder="输入后根据关键字匹配进行选择"
      >
        {this.state.dataSource.map((item) => {
          return (
            <AutoComplete.Option
              value={String(item.user_id)}
              key={item.user_id}
              onClick={() => {
                this.onSelect(item);
              }}
            >
              {item.user_name}
              <div
                title={item.current_job}
                style={{ whiteSpace: "normal", width: "100%" }}
              >
                {item.current_job}
              </div>
            </AutoComplete.Option>
          );
        })}
      </AutoComplete>
    );
  }
}

export default PAutoComplete;
