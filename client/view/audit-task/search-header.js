import React from 'react';
import { Select, Form, Button } from 'antd';

import { YearsData } from 'tool/util';

const dateScope = YearsData().data;
const dateYear = YearsData().current;

const quarterData = [{
    key: -1,
    value: '全部'
}, {
    key: 1,
    value: '第一季度'
}, {
    key: 2,
    value: '第二季度'
}, {
    key: 3,
    value: '第三季度'
}, {
    key: 4,
    value: '第四季度'
}]

const renderSelect = (dataSource, key = 'key', value = 'value') => {
    return dataSource.map((item, index) => {
        return (
            <Select.Option
                key={item[key] || index}
                title={item[value] || item}
                value={item[key] || item}
            >{item[value] || item}</Select.Option>
        );
    })
}

export default Form.create()(({
    form, onInit, onSubmit
}) => {

    const { getFieldDecorator } = form;
    onInit && onInit(form);

    return (
        <Form className="execute-audit-search-header-panel" layout='inline'>
            <Form.Item label='考核时间' style={{ marginRight: 3 }}>

                {getFieldDecorator('taskYear', {
                    initialValue: dateYear
                })(
                    <Select
                        placeholder="请选择"
                        style={{ width: 90, marginRight: 5 }}
                    >
                        {renderSelect(dateScope)}
                    </Select>
                )}

            </Form.Item>
            <Form.Item>
                {getFieldDecorator('quarter', {
                    initialValue: -1
                })(
                    <Select
                        placeholder="请选择"
                        style={{ width: 150 }}
                    >
                        {renderSelect(quarterData)}
                    </Select>
                )}

            </Form.Item>

            <Form.Item>
                <Button type='primary' className='query-btn' loading={false} onClick={() => {
                    onSubmit(form);
                }} >查询</Button>
            </Form.Item>

        </Form>
    )
});