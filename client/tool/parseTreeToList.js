const countLeaf = (node, result) => {
    // console.log(node);
    let children = node.children || node.opts;
    if (node) {
        if (Array.isArray(children)) {
            // console.log(node.children);
            if (children.length !== 0) {
                children.forEach((child, index) => {
                    result = countLeaf(child, result);
                });
            } else {
                result++;
            }
        }
    }
    return result;
};

//将树形列表转换为表格所需的list数组（适用于有奖竞答，投票）
const tryFetchLeaf = (node, target, level, result, index, span = 0) => {
    // console.log(index);
    let resultTarget = JSON.parse(JSON.stringify(target));
    if (!resultTarget.subject) {
        resultTarget.subject = node.title;
    }
    if (!resultTarget.typeName) {
        resultTarget.typeName = node.typeName;
    }
    if (!resultTarget.totalNum) {
        resultTarget.totalNum = node.num;
    }

    if (node.download) {
        resultTarget.download = node.download;
        resultTarget.download_aid = node.download_aid;
        resultTarget.download_opts_id = node.download_opts_id;
    }

    //设置跨行跨列属性
    if (index === 0 && span > 0) {
        //优先采取行合并，其次采取列合并
        if (!resultTarget.rowSpan) {
            resultTarget.rowSpan = span;
        } else if (!resultTarget.colSpan) {
            resultTarget.colSpan = span;
        } else {
            resultTarget['colSpan' + level] = span;
        }
    } else if (index > 0 && span > 0) {
        //优先采取行合并，其次采取列合并
        if (!resultTarget.rowSpan) {
            resultTarget.rowSpan = 0;
        } else if (!resultTarget.colSpan) {
            resultTarget.colSpan = 0;
        } else {
            resultTarget['colSpan' + level] = 0;
        }
    }

    if (node && node.children && node.children.length !== 0) {
        let children = node.children;
        /*
        * 表示当前节点尚未到达叶子节点
        * 将当前层级存入target中
        * 层级自增
        * 并递归调用tryFetchLeaf
        * */
        if (level === 1) {
            resultTarget['title'] = node.name;
        } else {
            resultTarget['name' + level] = node.name;
        }

        level++;
        children.forEach((childrenNode, index) => {
            tryFetchLeaf(childrenNode, resultTarget, level, result, index, children.length);
        });
    } else {
        resultTarget['num_type'] = node.name;
        resultTarget['num'] = node.num;
        resultTarget['percent'] = node.percent;
        result.push(resultTarget);
    }
};
//将树形列表转换为表格所需的list数组（适用于问卷调查）
const transToQuestionList = (node, target, level, result, index, span = 0) => {
    // console.log(node);
    //前提是需要传入节点
    if (node) {
        let resultTarget = JSON.parse(JSON.stringify(target));
        resultTarget['path' + level] = node.name || '';
        let count = 0;
        //抓取节点下方所有叶子节点的数量
        resultTarget['level' + level + 'RowSpan'] = countLeaf(node, count);

        if (node.download) {
            resultTarget.download = node.download;
            resultTarget.download_aid = node.download_aid;
            resultTarget.download_opts_id = node.download_opts_id;
        }

        //如果当前节点为题目节点，则存入一条题目表格记录
        if (node.title) {
            resultTarget.subject = '题目';
            resultTarget.title = node.title;
            resultTarget.path2 = node.title;
            resultTarget.num = node.num;
            resultTarget.num_type = '总数';
            resultTarget.percent = '-';
            resultTarget.type = node.type;
            resultTarget.typeName = node.typeName;
            resultTarget.rowType = 'question';
            result.push(resultTarget);
        } else {
            resultTarget.title = node.name || '';
            if (level === 2) {
                resultTarget.subject = '选项' + (index + 1);
            }
        }

        if ((node.children && node.children.length !== 0) || (node.opts && node.opts.length !== 0)) {
            let children = node.children || node.opts;
            //当需要向下遍历子级分支时，level字段增加1
            level++;
            children.forEach((childrenNode, index) => {
                transToQuestionList(childrenNode, resultTarget, level, result, index, children.length);
            });
        } else {
            if (!node.title) {
                resultTarget.num = node.num;
                resultTarget.num_type = '';
                resultTarget.percent = node.percent;
                resultTarget.rowType = 'option';
                result.push(resultTarget);
            }
        }
    }
};

const parseTreeToList = (tree, type) => {
    let result = [];
    if (tree && tree.length !== 0) {
        tree.forEach((treeNode, nodeIndex) => {
            let target = {
                path1: '',
                path2: '',
                path3: '',
                path4: ''
            }, level = 1;
            if (!type) {
                tryFetchLeaf(treeNode, target, level, result);
            } else if (type === 'question') {
                transToQuestionList(treeNode, target, level, result);
            }
        })
    }
    // console.log(result);
    //添加为每个项目添加index，避免table组件报错
    result.map((item, index) => {
        item.index = index;
        return item;
    });
    return result;
};

export default parseTreeToList;

