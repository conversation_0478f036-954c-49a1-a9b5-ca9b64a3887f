import React, { Component } from "react";
import { But<PERSON>, Table, Modal, Popconfirm } from "antd";
import "./index.less";

import { addKeyToTableDataSource } from "tool/util";

import PersonModal from "./sub-components/person-modal";
import AddAttendUsers from "../person-selector/sub-components/add-attend-users";
import AttendanceStatus from "../person-selector/sub-components/attendance-status";
import SelfIcon from "components/self-icon";

import propTypes from "prop-types";

class PersonSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      inputData: [],
      labelText: "",
      visible: false,
      // 列席人员计数
      attendUserCount: 1,
      // 考勤管理模态框可见
      attendanceModalVisible: false,
      currentIndex: -1,
      currentData: {},
      // 查看请假原因模态框
      reasonVisible: false,
      // 默认人员列表收起
      isCollapse: true,
    };
    this.showModal = this.showModal.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.hideAttendanceModal = this.hideAttendanceModal.bind(this);
    this.exportData = this.exportData.bind(this);
  }

  componentWillMount() {
    let { inputData = [], label, selectorType } = this.props;
    if (!label) {
      if (selectorType === 2) {
        // label = "参会人员";
        label = "参加人员";
      } else if (selectorType === 3) {
        label = "列席人员";
      }
    }
    if (inputData && Array.isArray(inputData) && inputData.length !== 0) {
      let attendUserCount = 1;
      inputData.forEach((item) => {
        if (item.user_id < 0) {
          attendUserCount++;
        }
      });
      this.setState({
        attendUserCount,
        dataSource: addKeyToTableDataSource(inputData),
      });
    }
    this.setState({
      labelText: label,
    });
  }

  componentWillReceiveProps(props) {
    let { inputData = [], label, selectorType } = props;
    if (!label) {
      if (selectorType === 2) {
        // label = "参会人员";
        label = "参加人员";
      } else if (selectorType === 3) {
        label = "列席人员";
      }
    }
    if (inputData && Array.isArray(inputData)) {
      let attendUserCount = 1;
      inputData.length &&
        inputData.forEach((item) => {
          if (item.user_id < 0) {
            attendUserCount++;
          }
        });
      this.setState({
        attendUserCount,
        dataSource: addKeyToTableDataSource(inputData),
      });
    }
    this.setState({
      labelText: label,
    });
  }

  // 导出数据
  exportData() {
    const { dataSource } = this.state;
    const output = JSON.parse(JSON.stringify(dataSource));
    if (output && Array.isArray(output) && output.length !== 0) {
      output.forEach((item) => {
        delete item.key;
      });
    }
    // console.log("导出数据", output);
    return output;
  }

  removeItem(index) {
    const { dataSource } = this.state;
    const { onChange } = this.props;
    dataSource.splice(index, 1);
    onChange(dataSource);
    this.setState({
      dataSource: addKeyToTableDataSource(dataSource),
    });
  }

  showAttendanceModal(index, record) {
    this.setState(
      {
        currentIndex: index,
        currentData: record,
      },
      () => {
        this.setState({
          attendanceModalVisible: true,
        });
      }
    );
  }

  hideAttendanceModal() {
    this.setState({
      attendanceModalVisible: false,
    });
  }

  showModal() {
    // console.log(this.props);
    const { dataSource } = this.state;
    // console.log(dataSource, this.personModal);
    this.setState({
      visible: true,
    });
    this.personModal.init(dataSource);
  }

  hideModal() {
    this.setState({
      visible: false,
    });
  }

  calcPersonCount(data) {
    // console.log(data);
    let checkUserId = [];
    if (data && Array.isArray(data) && data.length !== 0) {
      data.forEach((person) => {
        if (checkUserId.indexOf(person.user_id) === -1) {
          checkUserId.push(person.user_id);
        }
      });
    }
    return checkUserId.length;
  }

  showAbsenceModal(record) {
    this.setState(
      {
        currentData: record,
      },
      () => {
        this.setState({
          reasonVisible: true,
        });
      }
    );
    // console.log("展示请假原因", record);
  }

  render() {
    const This = this;
    const {
      visible,
      reasonVisible,
      dataSource,
      labelText,
      attendUserCount,
      attendanceModalVisible,
      currentIndex,
      currentData,
      isCollapse,
    } = this.state;

    const {
      selectorType,
      withStatus,
      withHandler,
      canEdit,
      is_sign_in,
      notOrzPerson,
      onChange,
      isUpdateMeeting,
      notNull,
      labelStyle,
      radio,
      isPartyGroup,
      isLeaderGroup,
    } = this.props;

    // 表格状态列
    const statusCol = [
      {
        title: "考勤情况",
        dataIndex: "sign_status",
        align: "center",
        width: 245,
        render(text = 1, record, index) {
          let label = "";
          if (text === 1) {
            label = "已签到";
          } else if (text === 2) {
            label = "未签到";
          } else if (text === 3) {
            label = <a onClick={() => This.showAbsenceModal(record)}>因公请假</a>;
          } else if (text === 4) {
            label = <a onClick={() => This.showAbsenceModal(record)}>因私请假</a>;
          }
          // 取消缺席状态
          // else if (text === 5) {
          //   label = "缺席";
          // }
          return <div className={`sign-status sign-status-${text}`}>{label}</div>;
        },
      },
    ];
    // 表格操作列
    const handlerCol = [
      {
        title: "操作",
        align: "center",
        width: 200,
        render(text, record, index) {
          return (
            <div className="handler-wrapper">
              {/* 当人员不是从组件中添加，从接口读取数据，当会议不需要签到，则允许更改考勤操作  */}
              {record.is_add === 0 && is_sign_in === 0 && withStatus && (
                <a onClick={() => This.showAttendanceModal(index, record)}>考勤管理</a>
              )}
              {/* 当人员是从组件中添加，不是从接口读取数据，则允许更改考勤操作  */}
              {record.is_add !== 0 && withStatus && (
                <a onClick={() => This.showAttendanceModal(index, record)}>考勤管理</a>
              )}
              {/* 当人员是从组件中添加，不是从接口读取数据，则允许删除操作 */}
              {(record.is_add !== 0 || isUpdateMeeting) && (
                <Popconfirm
                  okText="确认"
                  cancelText="取消"
                  title={`是否将【${record.user_name}】删除？`}
                  onConfirm={() => This.removeItem(index)}
                >
                  <a>删除</a>
                </Popconfirm>
              )}
            </div>
          );
        },
      },
    ];
    // 常规表格列
    let columns = [
      {
        title: "序号",
        dataIndex: "key",
        align: "center",
        width: 100,
      },
      {
        title: "姓名",
        dataIndex: "user_name",
        align: "center",
        width: 150,
      },
      {
        title: "电话",
        dataIndex: "phone",
        align: "center",
        width: 240,
      },
      {
        title: "所属部门",
        dataIndex: "org_name",
        align: "center",
        width: 350,
      },
    ];

    if (withStatus) {
      columns = columns.concat(statusCol);
    }

    // 如果可以编辑，并且有操作列
    if (canEdit && withHandler) {
      columns = columns.concat(handlerCol);
    }

    // console.log("状态列", withStatus);
    // console.log("操作列", withHandler);

    const tableProps = {
      rowKey: "user_id",
      bordered: true,
      columns,
      dataSource,
      pagination: false,
      scroll: { y: 560 },
    };
    const personModalProps = {
      radio,
      isPartyGroup,
      isLeaderGroup,
      isUpdateMeeting,
      title: labelText,
      visible,
      dataSource,
      hideModal: () => {
        this.hideModal();
      },
      ref: (ref) => {
        this.personModal = ref;
      },
      // 从子组件获取人员选择数据
      onChange: (data) => {
        const { dataSource } = this.state;
        const { onChange } = this.props;
        let output = [];
        // {
        //   "name": "张老三",
        //     "user_id": 52676,
        //       "phone": "150****6655",
        //         "cert_number": "500225********4717",
        //           "org_id": 3,
        //             "org_name": "重庆市级机关"
        // }
        // 遍历数据并进行重新初始化
        if (data && Array.isArray(data) && data.length !== 0) {
          let item,
            arr = [];
          data.forEach((person) => {
            if (arr.indexOf(person.user_id) === -1) {
              arr.push(person.user_id);
              item = {
                user_name: person.name || person.user_name,
                user_id: person.user_id,
                phone: isPartyGroup ? person.phone_secret : person.phone,
                phone_secret: person.phone_secret,
                cert_number: person.cert_number,
                org_id: person.org_id,
                org_name: person.org_name,
                // 是从组件中添加
                reason: "",
                is_add: 1,
                // 默认添加的人员考勤状态为1，已签到
                sign_status: 1,
                // meeting_user_id
                // meeting_id
              };
              // 从之前选中的数据中，查找本次选中人员，是否存在重复部分，如果存在，则将状态保留
              dataSource.some((i) => {
                if (i.user_id === person.user_id && i.org_id === person.org_id) {
                  // console.log(i);
                  item.is_add = i.is_add;
                  item.sign_status = i.sign_status;
                  // 如果是修改，存在之前读取数据包含下列会议相关字段，予以保留
                  item.meeting_user_id = i.meeting_user_id;
                  item.meeting_id = i.meeting_id;
                  item.reason = i.reason;
                  // 跳出循环
                  return true;
                }
              });
              output.push(item);
            }
          });
        }
        // console.log(output);
        onChange(output);
        this.setState({
          dataSource: addKeyToTableDataSource(output),
        });
      },
    };
    // 手动添加列席人员
    const addAttendUsersProps = {
      attendUserCount: attendUserCount,
      notOrzPerson,
      addToList: (data) => {
        let { attendUserCount, dataSource } = this.state;
        data.key = dataSource.length + 1;
        // console.log("添加", data);
        dataSource.push(data);
        onChange(dataSource);
        this.setState({
          dataSource,
          attendUserCount: ++attendUserCount,
        });
      },
    };
    // 考勤状态
    const attendanceStatusProps = {
      visible: attendanceModalVisible,
      hideModal: this.hideAttendanceModal,
      currentIndex,
      currentData,
      refreshList: (data, index) => {
        const { dataSource } = this.state;
        dataSource[index] = data;
        onChange(dataSource);
        this.setState({
          dataSource,
        });
        this.hideAttendanceModal();
      },
    };
    // 缺席原因模态框
    const modalProps = {
      title: "请假原因",
      visible: reasonVisible,
      footer: null,
      wrapClassName: "absence-modal",
      width: 600,
      onCancel: () => {
        this.setState(
          {
            reasonVisible: false,
          },
          () => {
            this.setState({
              currentData: {},
            });
          }
        );
      },
    };
    return (
      <div className="person-selector-wrapper">
        {/* 人员选择器组件 */}
        <div className="person-count">
          {labelText && labelText !== "" && (
            <span
              className={notNull ? "ant-form-item-required selector-label" : "selector-label"}
              style={labelStyle || {}}
            >
              {labelText}
            </span>
          )}
          {canEdit && (
            <a href="javascript:void(0)" className="selector-trigger" onClick={() => this.showModal()}>
              添加
              {selectorType === 2 ? "参加" : selectorType === 3 ? "列席" : ""}人员
            </a>
          )}
          {canEdit && selectorType === 3 && <AddAttendUsers {...addAttendUsersProps} />}
          <span className="selected-count">（{this.calcPersonCount(dataSource)}人）</span>
          {this.calcPersonCount(dataSource) !== 0 && (
            <a
              href="javascript:void(0)"
              onClick={() => {
                const { isCollapse } = this.state;
                this.setState({
                  isCollapse: !isCollapse,
                });
              }}
            >
              <SelfIcon type={isCollapse ? "plus-square" : "minus-square"} />
              {isCollapse ? "展开" : "收起"}
            </a>
          )}
        </div>
        {dataSource && Array.isArray(dataSource) && dataSource.length !== 0 && !isCollapse && (
          <div className={dataSource.length > 12 ? "ie_scroll_table person-table" : "person-table"}>
            <div className="person-table-wrapper">
              <Table {...tableProps} />
            </div>
          </div>
        )}
        {canEdit && <PersonModal {...personModalProps} />}
        {/* 出息情况模态框，需要存在操作列 */}
        {canEdit && withHandler && <AttendanceStatus {...attendanceStatusProps} />}
        {/* 请假情况模态框 */}
        {withStatus && (
          <Modal {...modalProps}>
            <div className="absence-container">
              <div className="absence-wrapper">
                <div className="label">请假类型</div>
                <div className="content">
                  {currentData.sign_status === 3
                    ? "因公请假"
                    : currentData.sign_status === 4
                    ? "因私请假"
                    : "未知请假类型"}
                </div>
              </div>
              <div className="absence-wrapper">
                <div className="label">请假原因</div>
                <div className="content">{currentData.reason}</div>
              </div>
              <div className="buttons-wrapper">
                <Button
                  type="primary"
                  onClick={() => {
                    this.setState(
                      {
                        reasonVisible: false,
                      },
                      () => {
                        this.setState({
                          currentData: {},
                        });
                      }
                    );
                  }}
                >
                  确定
                </Button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    );
  }
}
PersonSelector.propTypes = {
  labelStyle: propTypes.object,
  // 标签是否必填字段
  notNull: propTypes.bool,
  // 输出数据
  exportData: propTypes.func,
  // 组件是否接受修改
  canEdit: propTypes.bool,
  // 结果表格是否带有考勤情况状态列
  withStatus: propTypes.bool,
  // 结果表格是否带有操作列
  withHandler: propTypes.bool,
  label: propTypes.string,
  inputData: propTypes.array,
  // 1.普通选择人员
  // 2.选择参会人员，
  // 3.选择列席人员，可以手动添加人员
  selectorType: propTypes.oneOf([1, 2, 3]),
  // 外层传入，若本组件用于添加会议人员，如果本会议需要签到，则已经存在，并传入组件的人员，不允许修改签到状态
  // 0：不需要；1：需要（默认）
  is_sign_in: propTypes.oneOf([0, 1]),
  notOrzPerson: propTypes.bool, // 是否显示, 录入非组织内人员
  onChange: propTypes.func,
  default_sign_status: propTypes.number, //默认的签到状态，默认为1.已签到
  isUpdateMeeting: propTypes.bool,
  // 是否人员单选，默认为false
  radio: propTypes.bool,
  // 是否读取党小组人员接口，默认为false
  isPartyGroup: propTypes.bool,
  // 是否为领导班子
  isLeaderGroup: propTypes.bool,
};

PersonSelector.defaultProps = {
  labelStyle: {},
  notNull: false,
  isUpdateMeeting: false,
  exportData: (data) => {
    // console.log("默认方法导出数据", data);
  },
  canEdit: true,
  withStatus: true,
  withHandler: true,
  label: "",
  inputData: [],
  selectorType: 1,
  is_sign_in: 1,
  notOrzPerson: true,
  onChange: (output) => {
    // console.log("默认onChange方法", output);
  },
  default_sign_status: 1,
  radio: false,
  isPartyGroup: false,
  isLeaderGroup: false,
};

export default PersonSelector;
