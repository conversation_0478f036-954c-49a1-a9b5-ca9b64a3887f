import { sasHost } from "./config";
import { http } from 'client/tool/axios';

/**
 * @description 查询设置统计信息
 * @returns {*}
 */
export const getConfigDetail = () => http.post(`${sasHost}/sas-config/detail`);

/**
 * @description 修改设置统计信息
 * @param params
 * @returns {*}
 */
export const updateConfigDetail = (params = {}) => http.post(`${sasHost}/sas-config/edit`, params);

/**
 * @description 新增设置统计信息
 * @param params
 * @returns {*}
 */
export const addConfigDetail = (params = {}) => http.post(`${sasHost}/sas-config/add`, params);
