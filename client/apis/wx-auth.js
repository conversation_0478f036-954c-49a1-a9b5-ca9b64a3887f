/**
 * 微信认证
 */

import { ucHost, wechatHost } from "./config";
import { http } from "client/tool/axios";
import {staticHttp} from "client/tool/axios-now"


// const ucHost = 'http://************:8080/user';


//http://************:8090/pages/viewpage.action?pageId=********

export const getPreAuthCode = params => {

  return http.get(`${ucHost}/official_account/pre_auth_code`, params);
};


export const getAuthorizerInfo = params => {
  return http.get(`${ucHost}/official_account/authorizer_info`, params);
};

//http://************:8090/pages/viewpage.action?pageId=********

export const getAccesstoken = params => {
  return http.get(`${ucHost}/official_account/oauth2_access_token`, params);
};


//http://************:8090/pages/viewpage.action?pageId=********
export const getAuthorizerTree = params => {
  return http.get(`${ucHost}/official_account/authorizer_tree`, params);
};

//http://************:8090/pages/viewpage.action?pageId=********
export const setAuthorizerTree = params => {
  return http.post(`${ucHost}/official_account/authorizer_tree`, params);
};

//http://************:8090/pages/viewpage.action?pageId=********
export const WxUnBind = params => {
  return http.post(`${ucHost}/official_account/unbind`, params);
};

//**********-获取组织和公众号的绑定统计信息
//http://************:8090/pages/viewpage.action?pageId=********
export const getWeixinAccounts = params => {
  return http.get(`${ucHost}/official_account/binding_statistical`, params);
};


// **********-跳转到微信公众号授权第三方平台页面
// http://************:8090/pages/viewpage.action?pageId=********
// project_name pre_auth_code
export const authPreUrl = `${wechatHost}/redirect/auth_pre`;
