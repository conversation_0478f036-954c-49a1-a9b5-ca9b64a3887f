import { Col, DatePicker, Form, Input, message, Popconfirm, Row } from "antd";
import {
    addOrUpdateChargeRange
} from "client/apis/cadre-portrait";
import { convertMomentToFormattedDate, convertTimeValues } from "client/tool/util";
import { uuid } from 'client/tool/uuid';
import DateSingle from "components/date-single/DateSingle";
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
const MonthPicker = DatePicker.MonthPicker

function index({ form, data, disabled }, ref) {
    const { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue, validateFields } = form

    getFieldDecorator("keys", {
        initialValue: []
    })
    const [initFormData, setInitFormData] = useState({
        keys: [],
        start_time: {},
        end_time: {},
        charge_range: {},
        charge_range_id: {},
        type: {}
    })

    useEffect(() => {

        if (data && data.length) {
            setFieldsValue({ keys: data.map(item => item.charge_range_id) })
            const valueMap = {
                keys: [],
                start_time: {},
                end_time: {},
                charge_range: {},
                type: {},
                charge_range_id: {}
            }
            data.map(item => {
                const { start_time, end_time, charge_range, charge_range_id } = convertTimeValues(item)
                valueMap.start_time[item.charge_range_id] = start_time
                valueMap.end_time[item.charge_range_id] = end_time
                valueMap.charge_range[item.charge_range_id] = charge_range
                valueMap.charge_range_id[item.charge_range_id] = charge_range_id
                valueMap.type[item.charge_range_id] = 1 // 1为接口返回 2为新增
                valueMap.keys.push(charge_range_id)
            })

            setInitFormData(valueMap)

        } else {

            setFieldsValue({ keys: [uuid()] })
        }
    }, [data])

    const onSubmit = (user_id) => new Promise((resolve, reject) => {
        const data = getFormData(user_id)

        addOrUpdateChargeRange({
            list: data
        }).then(res => {
            if (res.data.code === 0) {
                resolve(res.data)
            } else {
                reject(res.data.message)
            }
        })

    })

    useImperativeHandle(ref, () => {
        return {
            onSubmit, validate: async () => {
                return await new Promise((resolve, reject) => {
                    const fields = getFieldsValue()
                    const { keys, start_time, charge_range } = fields
                    console.log(fields, keys, keys.length, 'fields');

                    let needValidate = true
                    if (keys.length == 1) {
                        keys.map(key => {
                            if (!start_time[key] && !charge_range[key]) {
                                needValidate = false
                                resolve()
                            }
                        })
                    }
                    needValidate && validateFields((err, values) => {
                        let error = false;
                        if (err) {
                            keys.forEach(key => {
                                if (err.start_time && err.start_time[key] || err.charge_range && err.charge_range[key]) {
                                    error = true;
                                }
                            });
                        }

                        if (!error) {
                            resolve(err)
                        } else {
                            message.error("分管领域信息不完整！！")
                            reject(err)
                        }
                    })
                })
            }
        }
    }, [])

    const formWapper = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    }

    const onAdd = () => {
        const newKeys = keys.concat(uuid())
        setFieldsValue({ keys: newKeys })
    }

    const onDelete = async (key, type) => {
        const _keys = [].concat(keys)

        const keyIndex = _keys.findIndex((item) => {
            return item === key
        })
        const delIds = []

        if (type === 1) {
            delIds.push(key)
        }

        if (delIds.length) {
            const res = await addOrUpdateChargeRange({
                del_charge_range_ids: delIds
            })

            if (res.data.code === 0) {
                message.success("删除成功！")
            } else {
                message.error(res.data.message)
            }
        }

        _keys.splice(keyIndex, 1)

        setFieldsValue({ keys: _keys.length ? [..._keys] : [uuid()] })
    }

    const getFormData = (user_id) => {

        const _keys = getFieldValue("keys")
        const fileds = getFieldsValue()
        return _keys.map(key => {

            const fields = {
                start_time: fileds.start_time[key],
                end_time: fileds.end_time[key],
                charge_range: fileds.charge_range[key],
                charge_range_id: fileds.charge_range_id[key],
                user_id
            }

            return convertMomentToFormattedDate(fields, "YYYY.MM")

        }).filter(item => item.start_time && item.charge_range)

    }


    const keys = getFieldValue("keys")

    const formItems = keys.map(key => <Row type="flex" align="middle">
        {
            getFieldDecorator(`charge_range_id[${key}]`, {
                initialValue: initFormData.charge_range_id[key]
            })(<Input hidden />)
        }
        <Col span={4}>
            <Form.Item label="开始时间" required>
                {getFieldDecorator(`start_time[${key}]`, {
                    initialValue: initFormData.start_time[key],
                    rules: [{
                        required: false, message: '请选择开始时间', validator: (rule, value, callback) => {
                            if (keys.length === 1) {
                                if (!getFieldValue(`charge_range[${key}]`) && !!value) {
                                    return callback()
                                }
                            }
                            if (!value) {
                                callback('请选择开始时间')
                            } else {
                                callback()
                            }
                        }
                    }]
                })(
                    <DateSingle placeholder="请选择" type="month" isWrap={false} disabled={disabled} />
                )}
            </Form.Item>
        </Col>
        <Col span={4}>
            <Form.Item label="结束时间">
                {getFieldDecorator(`end_time[${key}]`, {
                    initialValue: initFormData.end_time[key]
                })(
                    <DateSingle placeholder="请选择" type="month" isWrap={false} disabled={disabled} />
                )}
            </Form.Item>
        </Col>
        <Col span={13}>
            <Form.Item label="分管领域（工作）" required>
                {getFieldDecorator(`charge_range[${key}]`, {
                    initialValue: initFormData.charge_range[key],
                    rules: [{
                        required: false, message: '请输入分管领域',
                        validator: (rule, value, callback) => {
                            if (keys.length === 1) {
                                if (!getFieldValue(`start_time[${key}]`) && !!value) {
                                    return callback()
                                }
                            }
                            if (!value) {
                                callback('请输入分管领域')
                            } else {
                                callback()
                            }
                        }
                    }]
                })(
                    <Input placeholder='请输入' disabled={disabled} />
                )}
            </Form.Item>
        </Col>

        {!disabled && (
            <Col span={3} className="margin-right-10">
                <Form.Item label=" ">
                    <div className="button-box">
                        <svg
                            onClick={onAdd}
                            t="1715392676445"
                            class="icon"
                            viewBox="0 0 1024 1024"
                            version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                            p-id="4273"
                            width="16"
                            height="16"
                        >
                            <path
                                d="M928.78 339.61A449.35 449.35 0 1 0 964 514a446.24 446.24 0 0 0-35.22-174.39zM713 544H546v167a30 30 0 0 1-60 0V544H319a30 30 0 0 1 0-60h167V317a30 30 0 0 1 60 0v167h167a30 30 0 0 1 0 60z"
                                fill="#0368CD"
                                p-id="4274"
                            ></path>
                        </svg>
                        {
                            <Popconfirm
                                title="确认删除此项?"
                                onConfirm={() => {
                                    onDelete(key, initFormData.type[key]);
                                }}
                                okText="确认"
                                cancelText="取消"
                            >
                                <svg
                                    t="1715392815192"
                                    class="icon"
                                    viewBox="0 0 1024 1024"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    p-id="9085"
                                    width="16"
                                    height="16"
                                >
                                    <path
                                        d="M782.246469 240.527099c36.608779 36.608779 64.905242 79.291904 84.184331 126.794801 18.651802 45.9004 28.084639 94.451162 28.010961 144.263661 0.069585 49.808406-9.359158 98.363262-28.015054 144.260591-19.279089 47.50392-47.569412 90.189092-84.175121 126.793778-36.606733 36.606733-79.294974 64.900126-126.797871 84.179214-45.896306 18.654872-94.451162 28.084639-144.259568 28.015054-49.808406 0.069585-98.365308-9.358135-144.259568-28.015054-47.50392-19.277042-90.192162-47.569412-126.800941-84.179214-36.604686-36.603663-64.895009-79.289857-84.173075-126.793778-18.656919-45.895283-28.084639-94.452185-28.015054-144.260591-0.072655-49.812499 9.359158-98.363262 28.010961-144.263661 19.280112-47.501874 47.575552-90.186022 84.183308-126.793778 36.604686-36.604686 79.286788-64.899102 126.789685-84.178191 45.9004-18.651802 94.451162-28.084639 144.263661-28.011984 49.813522-0.072655 98.363262 9.360182 144.264685 28.011984C702.959681 175.626973 745.642806 203.922413 782.246469 240.527099M827.505255 195.268312C652.829957 20.593014 369.558335 20.593014 194.883037 195.269335 20.202623 369.94975 20.201599 653.220349 194.876897 827.895647c174.681438 174.681438 457.952037 174.679391 632.632451 0C1002.18567 653.220349 1002.18567 369.94975 827.505255 195.268312L827.505255 195.268312 827.505255 195.268312zM557.129338 513.486864 716.737476 353.87975l-45.222971-45.223994L511.906367 468.263893 352.762811 309.12136l-45.344744 45.344744 159.142533 159.143557L309.697993 670.472268l45.201481 45.201481 156.86363-156.86363 157.358911 157.356864 45.333488-45.333488L557.129338 513.486864zM714.465736 670.824285"
                                        fill="#d81e06"
                                        p-id="9086"
                                    ></path>
                                </svg>
                            </Popconfirm>
                        }
                    </div>
                </Form.Item>
            </Col>
        )}
    </Row>)
    return (
        <div class='responsibility'>
            <Form layout='vertical'>
                {
                    formItems
                }
            </Form>
        </div>
    )
}

export default Form.create()(forwardRef(index))
