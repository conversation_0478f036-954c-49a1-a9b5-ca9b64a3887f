/**
 * 表单字段
 */
export default {
    longTitle: 'title',                                                 //活动长标题
    shortTitle: 'sub_title',                                            //活动短标题
    customizeTag: 'tags',                                               //自定义标签
    appListThumbnail: 'appListThumbnail',                               //APP列表缩略图
    webListThumbnail: 'webListThumbnail',                               //网站列表缩略图
    appListFocusedThumbnail: 'appListFocusedThumbnail',                 //APP列表缩聚焦图
    webListFocusedThumbnail: 'webListFocusedThumbnail',                 //网站列表聚焦图
    activityColumn: 'column_id',                                        //活动栏目
    eventOrganizationDepartment: 'department',                     //活动组织部门
    activityTimeStartTime: 'start_time',                                //活动开始时间
    activityTimeEndTime: 'end_time',                                    //活动结束时间
    activityTimeStartDisabled: 'activityTimeStartDisabled',             //不设置开始时间
    activityTimeEndDisabled: 'activityTimeEndDisabled',                 //不设置结束时间

    //活动介绍
    activityContentWebAndApp: 'activityContentWebAndApp',               //使用通用介绍还是web/app分开介绍
    commonIntroduction: 'description',                                  //通用详细介绍
    webIntroduction: 'description',                                     //web详细介绍
    appIntroduction: 'description_app',                                 //app详细介绍

    //投票选项
    votingOptions: 'votingOptions',                                     //投票选项

    //活动奖励
    rewardTypeIntegral: 'rewardTypeIntegral',                           //是否奖励积分
    rewardTypeIntegralSource: 'rewardTypeIntegralSource',               //奖励积分数据集
    rewardTypeIntegralTotal: 'rewardTypeIntegralTotal',                 //奖励积分总额
    rewardTypeCommodity: 'rewardTypeCommodity',                         //是否奖励物品
    rewardTypeCommoditySource: 'rewardTypeCommoditySource',             //奖励物品数据集
    rewardTypeMoney: 'rewardTypeMoney',                                 //是否奖励现金
    rewardTypeMoneyMin: 'rewardTypeMoneyMin',                           //奖金最小值
    rewardTypeMoneyMax: 'rewardTypeMoneyMax',                           //奖金最大值
    rewardTypeMoneySum: 'rewardTypeMoneySum',                           //奖金总额
    rewardModelType: 'rewardModelType',                                 //奖励模式
    rewardModelProbabilityOfWinning: 'hit_rate',                        //随机抽取奖励

    //投票结果
    voteResultPublic: 'result_visible',                                 //是否公布投票结果
    voteSortingSetting: 'sort_type',                                    //排序设置
    voteSortingSettingOnlyShow: 'sort_top_num',                         //排序设置只显示票数前
    voteStatisticsShowSetting: 'display_type',                          //统计显示设置
    voteSumPeople: 'total_visible',                                     //总投票人次设置

    //参加活动人员统计
    statisticalDimension: 'statisticalDimension',                       //维度统计
    dimensionAgeBegin: 'dimensionAgeBegin',                             //年龄维度以下
    dimensionAgeEnd: 'dimensionAgeEnd',                                 //年龄维度以上

    //活动规则
    joinModel: 'need_login',                                            //参与模式
    accountRestrictions: 'rouser',                                      //账户限制
    ipRestrictions: 'roip',                                             //IP限制
    deviceRestrictions: 'rophone',                                      //设备限制

    //审批流程
    approvalType: 'workflow_id',                                        //审批类型
    isApproval: 'must_approve',                                         //不要审批
    approvalStatus: 'approvalStatus',                                   //审批状态
    //活动编辑
    joinStaffRange: 'joinStaffRange',                                   //参加人员范围
    joinStaffRestriction: 'joinStaffRestriction',                       //参加人员限制
    activityVisible: 'activityVisible',                                 //活动可见
    everyTimeJoin: 'everyTimeJoin',                                     //每次参加消耗积分
    designatedListOfParticipants: 'designatedListOfParticipants',       //指定参加人员名单
    designatedToParticipateInTheOrganization: 'designatedToParticipateInTheOrganization',        //指定参加组织人员
    participantRestrictions: 'participantRestrictions'                  //参加人员限制
}