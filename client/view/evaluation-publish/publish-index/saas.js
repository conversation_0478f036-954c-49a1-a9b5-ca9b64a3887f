import { connect } from "dva";
import moment from "moment";

import {
  getSaasEvalList,
  syncToEvalSaas,
  syncFromPmsSaas,
} from "client/apis/evaluation-publish";
import { headers } from "client/tool/axios";
import { CDN, activityHost } from "client/apis/config";
import { createATagDownload } from "client/tool/util";
import { syncDataPercent } from "client/apis/cadre-portrait";

const { message } = Ant;

function SaasPublishEvaluation(props) {
  const { userInfo, history, form } = props;

  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const currentOrgId = userInfo.oid;

  const [tableData, setTableData] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  const reqList = () => {
    const values = getFieldsValue();
    const params = {
      evaluation_name: values.evaluation_name,
      start_time:
        values.range_time &&
        moment(values.range_time[0]).format("YYYY-MM-DD 00:00:00"),
      end_time:
        values.range_time &&
        moment(values.range_time[1]).format("YYYY-MM-DD 23:59:59"),
    };
    setLoading(true);
    getSaasEvalList({ ...params, org_id: currentOrgId }).then((res) => {
      if (res.data.code === 0) {
        setTableData(res.data.data);
      }
      setLoading(false);
    });
  };

  React.useEffect(() => {
    reqList();
  }, []);

  const [downLoadingData, setDownLoadingData] = React.useState(false);
  const downloadPacket = async () => {
    if (downLoadingData) return;
    setDownLoadingData(true);
    const { data: res } = await syncToEvalSaas();
    let timer;
    let hasEndPre = true;
    if (resName.code === 0) {
      const url = resName.data;
      timer = setInterval(async () => {
        if (!hasEndPre) return;
        hasEndPre = false;
        const { data: res } = await syncDataPercent({
          file_path: url,
        });
        if (res.code !== 0 || res.data === "-1") {
          clearInterval(timer);
          timer = null;
          message.error(res.message);
          setDownLoadingData(false);
        } else if (res.data === "1") {
          // // 创建a标签下载
          createATagDownload(`${CDN}/${url}`);
          clearInterval(timer);
          timer = null;
          setDownLoadingData(false);
        }
        hasEndPre = true;
      }, 1000);
    } else {
      message.error(res.message);
      setDownLoadingData(false);
    }
  };

  // const handleUploadResult = () => {};

  const handleDownload = (row) => {
    history.push(
      `/evaluation-publish/evaluation-code?type=${row.type}&orgId=${currentOrgId}&evaluationId=${row.pms_evaluation_id}&evaluation_name=${row.evaluation_name}`
    );
  };

  const columns = [
    {
      title: "测评类型",
      dataIndex: "type",
      key: "type",
      render: (t) => {
        if (t == "8") return "一般干部测评";
        if (t == "9") return "班子成员测评";
        if (t == "10") return "市管领导测评";
        return "-";
      },
    },
    { title: "测评名称", dataIndex: "evaluation_name", key: "evaluation_name" },
    {
      title: "发布时间",
      dataIndex: "release_time",
      key: "release_time",
      align: "center",
    },
    {
      title: "截至时间",
      dataIndex: "end_time",
      key: "end_time",
      align: "center",
      render: (t) => moment(t).format("YYYY-MM-DD"),
    },
    {
      title: "操作",
      dataIndex: "handle",
      key: "handle",
      align: "center",
      width: 280,
      render: (_, record) => {
        return (
          <div>
            <Ant.Button
              type="link"
              size="small"
              onClick={() => handleDownload(record)}
            >
              下载测评码
            </Ant.Button>
          </div>
        );
      },
    },
  ];

  const uploadProps = {
    name: "file",
    action: syncFromPmsSaas,
    headers: headers(),
    showUploadList: false,
    onChange(info) {
      if (info.file.status !== "uploading") {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === "done") {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === "error") {
        message.error(`${info.file.name} 上传失败.`);
      }
    },
  };

  return (
    <div style={{ padding: 24 }}>
      <Ant.Form layout="inline">
        <Ant.Form.Item label="测评时间">
          {getFieldDecorator("range_time")(
            <Ant.DatePicker.RangePicker
              style={{ width: "100%" }}
              placeholder={["开始时间", "结束时间"]}
            />
          )}
        </Ant.Form.Item>
        <Ant.Form.Item label="测评名称">
          {getFieldDecorator("evaluation_name")(
            <Ant.Input placeholder="请输入" />
          )}
        </Ant.Form.Item>
        <Ant.Form.Item>
          <Ant.Button type="primary" onClick={() => reqList(currentOrgId)}>
            查询
          </Ant.Button>
        </Ant.Form.Item>
        <Ant.Form.Item>
          <Ant.Button
            onClick={() => {
              resetFields();
              reqList(currentOrgId);
            }}
          >
            重置
          </Ant.Button>
        </Ant.Form.Item>
      </Ant.Form>
      <Ant.Row gutter={8} type="flex" style={{ margin: "16px 0" }}>
        <Ant.Col>
          <Ant.Button
            type="primary"
            loading={downLoadingData}
            onClick={downloadPacket}
          >
            下载测评结果数据包
          </Ant.Button>
        </Ant.Col>
        <Ant.Col>
          <Ant.Upload {...uploadProps}>
            <Ant.Button
              style={{ borderColor: "#47b9ff", color: "#47b9ff" }}
              // onClick={handleUploadResult}
            >
              上传测评数据包
            </Ant.Button>
          </Ant.Upload>
        </Ant.Col>
      </Ant.Row>
      <Ant.Table
        loading={loading}
        bordered
        columns={columns}
        dataSource={tableData || []}
        pagination={false}
      />
    </div>
  );
}

export default connect(({ organizeData, userInfo }) => ({
  organizeData,
  userInfo,
}))(Ant.Form.create()(SaasPublishEvaluation));
