import { http } from "client/tool/axios";
import { ucPms1 } from "./config";

// const base = "http://nmwork.s7.tunnelfrp.com";

/** 根据组织获取中层用户 */
export const findAppraisalMiddleUser = (org_id) => {
  return http.get(`${ucPms1}/p_eval/find_appraisal_middle_user`, { org_id });
};

/** 根据组织ID获取测评列表 */
export const getEvalList = (params) => {
  return http.get(`${ucPms1}/eval/manage/get_eval_list`, params);
};

/** 撤销测评 */
export const cancelEvaluation = (data) => {
  return http.post(`${ucPms1}/eval/manage/cancel_evaluation`, data, {
    params: data,
  });
};

/** 结束测评 */
export const closeEvaluation = (data) => {
  return http.post(`${ucPms1}/eval/manage/close_evaluation`, data, {
    params: data,
  });
};

/** 延期测评 */
export const extensionEvaluation = (data) => {
  return http.post(`${ucPms1}/eval/manage/extension_evaluation`, data, {
    params: data,
  });
};

/** 重新提交测评 */
export const resubmitEvaluation = (params) => {
  return http.post(`${ucPms1}/eval/manage/resubmit_evaluation`, params, {
    params,
  });
};

// 下载测评数据包
export const syncToEval = (data = {}) => {
  return http.get(`${ucPms1}/sync/sync_to_eval`, data);
};

// 上传测评结果
export const syncFromPms = (data = {}) => {
  return http.get(`${ucPms1}/sync/sync_from_pms`, data);
};

/** =========== start 县管、中层测评相关接口 =========== */

/** 提交发布测评(县管、中层) */
export const initiateEvaluation = (data) => {
  return http.post(`${ucPms1}/eval/middle/initiate_evaluation`, data);
};

/** 根据测评ID获取测评详情 */
export const getEvaluation = (evaluation_id) => {
  return http.get(`${ucPms1}/eval/middle/get_evaluation`, { evaluation_id });
};

/** 根据测评id，获取测评二维码的详情 */
export const getEvaluationQrcode = (evaluation_id) => {
  return http.get(`${ucPms1}/eval/middle/get_evaluation_qrcode`, {
    evaluation_id,
  });
};
/** 市管内网 获取评测想去 */
export const getInternalQrDetail = (param) => {
  return http.get(`${ucPms1}/eval/city/get/qr/code`, param);
};
/** 市管内网 获取评测详情 */
export const getInternalQrCodesDownload = (param) => {
  return http.get(`${ucPms1}/eval/city/list/qr/codes`, param);
};
/** 市管内网 设置状态 */
export const setInternalQrcode = (param) => {
  return http.post(`${ucPms1}/eval/city/set/qr/code`, param);
};

/** 设置测评二维码 */
export const setEvaluationQrcode = (data) => {
  return http.post(`${ucPms1}/eval/middle/set_evaluation_qrcode`, data);
};

/** 添加测评码 */
export const addEvaluationQrcode = (data) => {
  return http.post(`${ucPms1}/eval/middle/add_evaluation_qrcode`, data);
};

/** 修改测评码名称 */
export const modifyEvaluationQrcodeName = (data) => {
  return http.post(`${ucPms1}/eval/middle/modify_evaluation_qrcode_name`, data);
};
//打票类型 字典接口
export const getRelationshipsDict = (param) => {
  return http.get(`${ucPms1}/eval/manage/get/relationships`, param);
};

/** =========== end 县管、中层测评相关接口 =========== */

/** =========== start 市管领导评测 =========== */

/** 初始化 */
export const getInitEvalData = () => {
  return http.get(`${ucPms1}/eval/city/get/init/data`);
};

/** 详情 */
export const getEvalDetail = (evaluation_id) => {
  return http.get(`${ucPms1}/eval/city/get/detail`, { evaluation_id });
};

/** 发布 */
export const publishEval = (data) => {
  return http.post(`${ucPms1}/eval/city/publish`, data);
};

/** =========== end 市管领导评测 =========== */

/** =================================================== start SAAS =========== */

// const base = "https://pms-rucheng.aidangqun.com/owsz/pms_eval";

/** saas 首页列表 */
export const getSaasEvalList = (data) => {
  return http.get(`${ucPms1}/eval/manage/get_eval_list`, data);
};

// 上传测评数据包 SAAS
export const syncFromPmsSaas = (data = {}) => {
  return http.get(`${ucPms1}/sync/sync_from_pms`, data);
};

// 下载测评结果数据包 SAAS
export const syncToEvalSaas = (data = {}) => {
  return http.get(`${ucPms1}/sync/sync_to_pms`, data);
};

/** 市管saas 获取评测详情 */
export const getSaasQrCodes = (param) => {
  return http.get(`${ucPms1}/eval/city/list/saas/qr/codes`, param);
};

/** 县管/中层saas 获取评测详情 */
export const getSaasEvaluationQrcode = (evaluation_id) => {
  return http.get(`${ucPms1}/eval/middle/get_evaluation_qrcode`, {
    evaluation_id,
  });
};

/** =================================================== end SAAS =========== */
