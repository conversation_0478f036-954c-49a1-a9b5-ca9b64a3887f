/**
 *  弹出选择人员标签
 * <AUTHOR>
 * @param { Boolean }       visible              控制是否显示 true 为显示
 * @param { Function }      handleOk             提交确认，返回值
 * @param { Function }      handleCancel         关闭显示，取消操作
 * @param { String   }      [title]              侧边栏主标题，默认为'选择人员标签'，可自己设置
 * @param {number }         [width]              可视区的宽度，默认为600px
 *
 * */
import React, {Component} from 'react';
import {Modal, Button, Form, Row, Col, Checkbox ,Spin} from 'antd';
import PropTypes from 'prop-types';
/**
* 标签人员选择对话框
 * <AUTHOR>
 * @param {bool}        visible          控制是否收起显示，true收起
 * @param {function}    tagSubmit        提交确认的函数，返回值
 * @param {function}    handleCancel     关闭显示，取消操作
 * @param {string}      [title]          弹框的题目
 * @param {number}      [width]          弹框的宽度
 * @param {bool}        tagLoading       加载的判断
 * @param {array}       selsctTagId      选中的数组标签下标
 * @param {array}       tagList          标签数组对象
 * */
class tagSider extends React.Component{
    constructor(props){
        super(props)
        this.state = {
            preprops:props,
            checkedList:props.selsctTagId
        }
    }
    componentWillReceiveProps(props){
        this.setState({
            preprops:props,
            checkedList:props['selsctTagId']
        });
    }
    submit(){
        const checkedList=this.state.checkedList,
            {tagList ,tagSubmit} = this.state.preprops;
        let newtaglist = [];
        for(let i in tagList){
            for(let j in checkedList)
                if(tagList[i].tag_id==checkedList[j]){
                newtaglist.push(tagList[i]);
                break;
                }
        }
        tagSubmit(newtaglist,checkedList);
    };

    changeList(value){
        this.setState({checkedList:value});

    }
    render(){
        const {title ,visible,handleCancel,width,tagLoading,tagList}=this.state.preprops;
        return (
            <div className='select-personTag'>
                <Modal
                    className='select-personTag-modal'
                    title={title}
                    visible={visible}
                    onOk={this.submit}
                    onCancel={handleCancel}
                    width={width}
                    footer={null}
                    style={{top:'20%'}}
                >
                    <Spin spinning={tagLoading}>
                            <Checkbox.Group style={{ width: '100%' }} value={this.state.checkedList} onChange={this.changeList.bind(this)}>
                                <Row>
                                    {
                                        tagList.map(val=>(
                                            <Col span={8} style={{padding:'5px 0'}} key={val.tag_id}><Checkbox value={val.tag_id} key={val.tag_id}>{val.name}</Checkbox></Col>
                                        ))
                                    }
                                </Row>
                            </Checkbox.Group>


                    <Button type='primary' style={{marginLeft:'45%',marginTop:'50px'}} onClick={this.submit.bind(this)}>确定</Button>

                    </Spin>
                </Modal>
            </div>
        )
    }
}

tagSider.propTypes = {
        // 控制是否收起 true 为收起
        visible:PropTypes.bool.isRequired,
        //提交确认，返回值
        tagSubmit:PropTypes.func.isRequired,
        //关闭显示，取消操作
        handleCancel:PropTypes.func.isRequired,
        //当前弹框的题目
        title:PropTypes.string,
        //当前弹框的宽度  600
        width:PropTypes.number,
        //加载
        tagLoading:PropTypes.bool.isRequired,
        //选中的选项数组
        selsctTagId:PropTypes.array,
        //标签数组对象
        tagList:PropTypes.array.isRequired
};

tagSider.defaultProps={
    visible:false,
    title:'选择人员标签',
    handleCancel:null,
    width:600,
    tagList:[],
    tagSubmit:null,
    tagLoading:false,
    selsctTagId:[]
};


export default tagSider;

