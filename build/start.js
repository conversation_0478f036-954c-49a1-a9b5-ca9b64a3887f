/*
 * @Description:
 * @Author: baic<PERSON>
 * @Date: 2021-02-01 11:35:31
 * @LastEditTime: 2021-02-20 15:16:27
 * @LastEditors: baichao
 */
const execa = require("execa");
const inquirer = require("inquirer");
const RegionConfig = require("./config/region.config");
const prompt = inquirer.createPromptModule();
const autocomplete = require("inquirer-autocomplete-prompt");
const archiver = require("archiver");
const fs = require("fs");
const path = require("path"); // 引入 path 模块用于处理路径

inquirer.registerPrompt("autocomplete", autocomplete);

const CUSTOM_VERSION_LIST = []; // 定制化版本（区县版本） 根据config中的regionConfig配置文件生成打包时选择
for (const [key, value] of Object.entries(RegionConfig)) {
  // key 作为选择值， 在webpack中设置为全局region
  CUSTOM_VERSION_LIST.push({ name: value.version, value: key });
}

/**
 * @description: 运行打包命令
 * @param {*} cmd 打包命令
 * @return {*}
 */
const execSpawnSync = async (cmd, option) => {
  try {
    const res = await execa.command(cmd, {
      stdio: "inherit",
      shell: true,
      ...option,
    });
    return res;
  } catch (error) {
    console.log("🚀 ~ file: start.js ~ execSpawnSync ~ error", error);
    throw error;
  }
};

inquirer
  .prompt([
    {
      type: "autocomplete",
      name: "version",
      message: "请选择要运行的[系统版本]：",
      emptyText: "未配置该版本！",
      pageSize: 8,
      source: (answers, input) =>
        new Promise(function (resolve) {
          const results = [];
          CUSTOM_VERSION_LIST.forEach(function (el) {
            // 根据输入显示系统版本
            el.name.includes(input || "") && results.push(el.name);
          });
          resolve(results);
        }),
    },
    {
      type: "list",
      name: "env",
      message: "请选择要运行的[运行环境]：",
      choices: () => [
        { name: "本地", value: "local" },
        { name: "测试", value: "test" },
        { name: "预发布", value: "release" },
        { name: "线上", value: "master" },
      ],
    },
    {
      type: "list",
      name: "onlineAddress",
      message: "请选择线上地址：",
      choices: () => [
        { name: "基线", value: "base" },
        { name: "汇川", value: "huichuan" },
        { name: "沙坡头", value: "shapotou" },
        { name: "寻甸", value: "xundian" },
        { name: "电白", value: "dianbai" },
        { name: "丰镇", value: "fengzhen" },
        { name: "甘州", value: "ganzhou" },
        { name: "巫山", value: "wushan" },
        { name: "汝城", value: "rucheng" },
        { name: "伊旗", value: "yiqi" },
        { name: "华龙", value: "hualong" },
        { name: "仙桃", value: "xiantao" },
        { name: "芒康", value: "mangkang" },
        { name: "攀枝花", value: "panzhihua" },

        // 可以根据实际情况添加更多地址选项
      ],
      when: (answers) => answers.env === "master", // 只有当选择线上环境时才会询问
    },
  ])
  .then(function (answers) {
    // 获取所选系统版本
    const CUSTOM_ENV = CUSTOM_VERSION_LIST.filter((item) => {
      return item.name === answers["version"];
    });
    const ONLINE_ENV = answers.env;
    let online_env = ONLINE_ENV === "local" ? "test" : ONLINE_ENV; // 系统运行环境
    let node_env = ONLINE_ENV === "local" ? "development" : "production"; // node编译环境
    let command = ONLINE_ENV === "local" ? "webpack-dev-server" : "webpack"; // 编译命令
    // 生成运行指令
    const build = `cross-env CUSTOM_ENV=${CUSTOM_ENV[0].value} ONLINE_ENV=${online_env} NODE_ENV=${node_env} NODE_OPTIONS=--max-old-space-size=4096 ${command}`;
    console.log("\n编译指令：", build, answers);
    return execSpawnSync(`${build}`).then(() => {
      // 生成压缩文件名
      const config = {
        test: "tog_admin_test",
        release: "tog_admin_release",
        master: `tog_admin_${answers.onlineAddress}`, // 线上环境
      };
      const zipFileName = `${config[answers.env]}.zip`;
      const output = fs.createWriteStream(
        path.join(__dirname, "..", zipFileName)
      ); // 使用相对路径
      const archive = archiver("zip", {
        zlib: { level: 9 }, // 设置压缩级别
      });
      // 监听事件...
      archive.pipe(output);
      const filesToCompress = [
        "public/plugin",
        "public/static",
        "public/browserSetup.html",
        "public/index.html",
        "public/manifest.json",
      ];

      filesToCompress.forEach((fileOrFolder) => {
        const fullPath = path.join(__dirname, "..", fileOrFolder);
        // 获取 public 目录下的子路径
        const relativePath = path.relative(
          path.join(__dirname, "..", "public"),
          fullPath
        );
        if (fs.lstatSync(fullPath).isDirectory()) {
          // 打包目录时，设置目标路径为子路径
          archive.directory(fullPath, relativePath);
        } else {
          // 打包文件时，设置目标路径为子路径
          archive.file(fullPath, {
            name: relativePath,
          });
        }
      });

      return archive.finalize();
      // output.on("close", function () {
      //   console.log(archive.pointer() + " total bytes");
      //   console.log(
      //     "Archiver has been finalized and the output file descriptor has closed."
      //   );
      // });

      // archive.on("error", function (err) {
      //   throw err;
      // });

      // archive.pipe(output);

      // // 只打包 dist 目录下的文件
      // archive.directory("D:/GS/cadre-admin/dist", false);

      // return archive.finalize();
    });
  })
  .catch((error) => {
    console.error("打包过程中出现错误:", error);
  });
