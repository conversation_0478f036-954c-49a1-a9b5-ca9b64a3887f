import React, {Component} from 'react';
import {
	Form,
	Input,
	Button
} from 'antd';

class Class extends Component {
	handleSubmit (e) {
		e.preventDefault();
		const {
			form,
			onChange
		} = this.props;
		const {validateFields} = form;
		validateFields((err, values) => {
			if (!err && onChange) {
				onChange(values)
			}
		})
	}

	render() {
		const {form} = this.props;
		const {getFieldDecorator} = form;
		return (
			<div className="activity-filter">
				<div>
					<Form
						layout="inline"
						onSubmit={e => this.handleSubmit(e)}
					>
						<Form.Item label="活动标题">
							{getFieldDecorator('activityTitle')(
								<Input
									allowClear
									type="text"
									maxLength={120}
									placeholder="活动标题"
									style={{width: 206}}
								/>
							)}
						</Form.Item>
						<Form.Item>
							<Button
								type="primary"
								shape="round"
								htmlType="submit"
							>查询</Button>
						</Form.Item>
					</Form>
				</div>
			</div>
		)
	}
}

export default Form.create()(Class);
