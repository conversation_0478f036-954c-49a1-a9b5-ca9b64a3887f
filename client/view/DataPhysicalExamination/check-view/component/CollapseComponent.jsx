import React, {
  createRef,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import "./collapaceComponent.less";
import {
  Drawer,
  Collapse,
  Button,
  Modal,
  Select,
  Form,
  Input,
  Spin,
  message,
} from "antd";
import {
  getExamineDetail,
  getOrgExamineDetail,
  setExamineRemark,
} from "apis/data-physical-examination";

const { Option } = Select;
const { Panel } = Collapse;

/**
 *
 *
 * @export
 * @param {*} { type: group/item 按组织党委支部/异常项统计 }
 * @return {*}
 */
const CollapseComponent = ({
  history,
  drawerParams,
  visible,
  setDrawerVisible,
}) => {
  //abnormalType 1组织/党员统计 2 异常统计
  //flag 0:组织范围 1:党员范围
  const {
    org_id,
    selected_oid,
    selected_orgName,
    abnormalType,
    flag,
    org_name,
    org_secretary_name,
    examine_id: parentExamineId,
    examine_name,
    user_id,
    user_name,
    phone,
    mark,
    text,
  } = drawerParams;

  // 表单ref
  const formRef = useRef(null);
  const [modalData, setModalData] = useState();
  const [dealVisible, setDealVisible] = useState();
  const [listData, setListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [processingData, setProcessingData] = useState({});

  useEffect(() => {
    if (visible) {
      initData();
    } else {
      setListData([]);
    }
  }, [visible]);

  const initData = () => {
    setLoading(true);
    if (Number(abnormalType) === 2) {
      getExamineDetailData();
    } else {
      getOrgExamineDetailData();
    }
  };

  // 异常项详情
  const getExamineDetailData = () => {
    getExamineDetail({
      flag,
      examine_id: parentExamineId,
      org_id: selected_oid,
      page_size: 100000,
    }).then(({ data: res }) => {
      setLoading(false);
      const { code, data } = res;
      if (code !== 0) {
        message.error(res.message);
        return false;
      }
      const listData = [
        {
          examine_title: examine_name,
          examine_types: data.map((item) => ({
            examine_id: item.org_id || item.user_id,
            examine_name: item.org_name || item.user_name,
            examine_type_name: item.org_secretary_name || item.phone,
            type: item.type,
            text: item.text,
          })),
        },
      ];
      setListData(listData);
      console.log("```````", listData, parentExamineId);
    });
  };

  // 组织或党员详情
  const getOrgExamineDetailData = () => {
    getOrgExamineDetail({
      flag,
      list_org_id: org_id,
      list_user_id: user_id,
    }).then(({ data: res }) => {
      setLoading(false);
      const { code, data } = res;
      if (code !== 0) {
        message.error(res.message);
        return false;
      }
      setListData(data);
    });
  };

  const collapseProps = {
    accordion: true,
    defaultActiveKey: ["0"],
    onChange: () => {},
    expandIconPosition: "right",
  };

  const onOk = () => {
    const { status, content } = formRef.current.getFieldsValue();
    if (status === "1" && !content) {
      message.error("请填写情况说明");
      return;
    }
    const param = {
      list_org_id: modalData.examine_id,
      examine_id: parentExamineId,
      status: Number(status),
      content,
    };
    setExamineRemark(param).then(({ data: res }) => {
      const { code, data } = res;
      if (code !== 0) {
        message.error(res.message);
        return false;
      }
      message.success("备注成功！");
      setModalData();
      initData();
    });
  };

  const onClickProcessing = (params) => {
    // const { org_id, org_name, selected_oid, selected_orgName } = drawerParams
    const { type, examine_id, examine_name } = params;
    setProcessingData({
      id: examine_id,
      name: examine_name,
      isEdit: true,
      org_id: org_id || selected_oid,
    });
    if ([1, 5, 8, 11].includes(type)) {
      setDealVisible(true);
    } else {
      const query = {};
      // abnormalType:2 异常项 mark 非党员时
      if (abnormalType === 2 && !mark) {
        query.org_id = examine_id;
        query.org_name = examine_name;
      } else {
        query.org_id = org_id || selected_oid;
        query.org_name = org_name || selected_orgName;
      }
      let pathname = "";
      switch (String(type)) {
        case "2":
        case "6":
        case "9":
          pathname = "/party-organization";
          break;
        case "3":
          pathname = "/leader-group";
          break;
        case "4":
        case "7":
        case "10":
          pathname = `/project-review?org_id=${query.org_id}`;
          history.push(pathname);
          return;
        case "12":
        case "13":
          // 按党员统计
          if (abnormalType === 1) {
            query.user_id = user_id;
            query.user_name = user_name;
          } else {
            // 按异常项统计
            query.user_id = examine_id;
            query.user_name = examine_name;
          }
          pathname = "/party-normal";
          break;
        /* case '13':
          pathname = "/party-pay-statistics"
          break; */
        case "14":
          pathname = "/democracy-member";
          break;
        default:
          break;
      }
      history.push({
        pathname,
        query,
        state: query,
      });
    }
  };

  //  不做缓存， 重置值
  const FormModal = Form.create()(
    forwardRef(({ form }, ref) => {
      const { getFieldValue, getFieldDecorator } = form;
      useImperativeHandle(ref, () => ({
        ...form,
      }));
      return (
        <Form
          labelCol={{ span: 4, offset: 3 }}
          wrapperCol={{ span: 10, offset: 1 }}
        >
          <Form.Item label="规则检查">
            {getFieldDecorator("status", { initialValue: "0" })(
              <Select style={{ width: "200px" }}>
                <Option value="0">每次检查</Option>
                <Option value="1">本届次内不做检查</Option>
              </Select>
            )}
          </Form.Item>
          {getFieldValue("status") === "1" && (
            <Form.Item
              label="情况说明"
              required
              wrapperCol={{ span: 12, offset: 1 }}
            >
              {getFieldDecorator("content")(
                <Input.TextArea
                  placeholder="请输入情况说明"
                  rows={4}
                ></Input.TextArea>
              )}
            </Form.Item>
          )}
        </Form>
      );
    })
  );

  const modalProps = {
    visible: !!modalData,
    title: "备注信息",
    onCancel: () => {
      setModalData();
    },
    onOk,
  };

  const dealModaProps = {
    title: "处理",
    footer: null,
    visible: dealVisible,
    onCancel: () => {
      setDealVisible(false);
    },
  };

  return (
    <Drawer
      className="panel-box"
      width="80%"
      getContainer={false}
      closable={false}
      visible={visible}
      onClose={() => setDrawerVisible(false)}
      style={{ position: "absolute" }}
    >
      {Number(abnormalType) !== 2 && (
        <div className="collapse-title">
          <div className="collapse-title-item">
            <span>{!flag ? "组织名称" : "姓名"}：</span>
            <span>{org_name || user_name}</span>
          </div>
          <div className="collapse-title-item">
            <span>{!flag ? "组织书记" : "手机号"}：</span>
            <span>{org_secretary_name || phone}</span>
          </div>
          {user_id && (
            <div className="collapse-title-item">
              <span>组织：</span>
              <span>{selected_orgName}</span>
            </div>
          )}
        </div>
      )}
      <Collapse {...collapseProps}>
        {listData.length &&
          listData.map((item, i) => {
            const { examine_title, examine_number, examine_types, text } = item;
            return (
              <Panel
                key={i}
                header={
                  <div className="panel-header">
                    <div className="abnormal-name">
                      <div className="abnormal-name-examine">
                        {" "}
                        {examine_title}
                      </div>
                      {parentExamineId === 1 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党委组织信息中，未设置党委组织类型）
                        </span>
                      )}
                      {parentExamineId === 2 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党委组织信息中，未设置党委行业类别）
                        </span>
                      )}
                      {parentExamineId === 3 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党委组织信息中，未设置党委所属单位）
                        </span>
                      )}
                      {parentExamineId === 4 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，以当前时间点计算党委届次信息未录入）
                        </span>
                      )}
                      {parentExamineId === 5 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中最新届次结束日期小于当前日期）
                        </span>
                      )}
                      {parentExamineId === 6 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党委届次信息起止时间不等于5年）
                        </span>
                      )}
                      {parentExamineId === 7 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党委书记信息未设置）
                        </span>
                      )}
                      {parentExamineId === 8 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党委书记未在本党委）
                        </span>
                      )}
                      {/* {parentExamineId === 9 && (
                        <span style={{marginLeft:20 ,color:"#F46E65",fontSize:14}}>（党组织委员会中，已设置的党委副书记未在本党委）</span>
                      )} */}
                      {parentExamineId === 10 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党委副书记未在本党委）
                        </span>
                      )}
                      {parentExamineId === 11 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，未设置党委委员会）
                        </span>
                      )}
                      {parentExamineId === 12 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：最新届次详情信息中，已设置党委委员会的成员人数与书记、副书记之和少于5名或为偶数）
                        </span>
                      )}
                      {parentExamineId === 13 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置党委委员会的成员未在本党委）
                        </span>
                      )}
                      {parentExamineId === 14 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，如设立纪律检查委员会，则需要设置，如没有设立纪律检查委员会，则填写备注在本届次内不再检查此数据项）
                        </span>
                      )}
                      {parentExamineId === 15 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的纪律检查委员会成员未在本党委）
                        </span>
                      )}
                      {parentExamineId === 16 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，如设立党委办公室，则需要设置，如没有设立党委办公室，则填写备注在本届次内不再检查此数据项）
                        </span>
                      )}
                      {parentExamineId === 17 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党委办公室成员未在本党委）
                        </span>
                      )}
                      {parentExamineId === 18 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，未设置单位领导班子）
                        </span>
                      )}
                      {parentExamineId === 19 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，未设置单位主要负责人）
                        </span>
                      )}
                      {parentExamineId === 20 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，领导班子成员未设置未设置联系支部）
                        </span>
                      )}
                      {parentExamineId === 21 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，已设置的领导班子成员所在支部和联系支部相同）
                        </span>
                      )}
                      {parentExamineId === 22 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，已设置的单位领导班子成员未在本单位）
                        </span>
                      )}
                      {/* {parentExamineId === 9 && (
                        <span style={{marginLeft:20 ,color:"#F46E65",fontSize:14}}>（党组织委员会中，已设置的党委副书记未在本党委）</span>
                      )} */}
                      {parentExamineId === 23 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：领导班子中，每年12月检查已设置的单位领导班子成员本年度未讲党课）
                        </span>
                      )}
                      {parentExamineId === 24 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：检查前一年度的述职评议是否录入）
                        </span>
                      )}
                      {parentExamineId === 25 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党总支组织信息中，未设置党总支组织类型）
                        </span>
                      )}
                      {parentExamineId === 26 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党总支组织信息中，未设置党总支行业类别）
                        </span>
                      )}
                      {parentExamineId === 27 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党总支组织信息中，未设置党总支所属单位）
                        </span>
                      )}
                      {parentExamineId === 28 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党总支组织信息中，未设置党总支是否离退休）
                        </span>
                      )}
                      {parentExamineId === 29 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，以当前时间点计算党总支届次信息未录入）
                        </span>
                      )}
                      {parentExamineId === 30 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，以当前时间点计算党总支届次将在6个月内即将到期或已到期未换届）
                        </span>
                      )}
                      {parentExamineId === 31 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党总支届次信息起止时间不等于3年）
                        </span>
                      )}
                      {parentExamineId === 32 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党总支书记信息未设置）
                        </span>
                      )}
                      {parentExamineId === 33 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党总支书记未在本党总支）
                        </span>
                      )}
                      {parentExamineId === 35 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党总支副书记未在本总支）
                        </span>
                      )}
                      {parentExamineId === 36 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，未设置党总支委员会）
                        </span>
                      )}
                      {parentExamineId === 37 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：最新届次详情信息中，已设置党总支委员会的成员人数与书记、副书记之和少于5名或为偶数）
                        </span>
                      )}
                      {parentExamineId === 38 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置党总支委员会的成员未在本总支）
                        </span>
                      )}
                      {parentExamineId === 39 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：检查前一年度的述职评议是否录入）
                        </span>
                      )}
                      {parentExamineId === 40 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党支部组织信息中，未设置党支部组织类型）
                        </span>
                      )}
                      {parentExamineId === 41 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党支部组织信息中，未设置党支部行业类别）
                        </span>
                      )}
                      {parentExamineId === 42 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党支部组织信息中，未设置党支部所属单位）
                        </span>
                      )}
                      {parentExamineId === 43 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党支部组织信息中，未设置党支部是否离退休）
                        </span>
                      )}
                      {parentExamineId === 44 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党支部组织信息中，党支部成员人数超过50名）
                        </span>
                      )}
                      {parentExamineId === 45 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，以当前时间点计算党支部届次信息未录入）
                        </span>
                      )}
                      {parentExamineId === 46 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，以当前时间点计算党支部届次将在6个月内即将到期或已到期未换届）
                        </span>
                      )}
                      {parentExamineId === 47 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党支部届次信息起止时间不等于3年）
                        </span>
                      )}
                      {parentExamineId === 50 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，党支部书记信息未设置）
                        </span>
                      )}
                      {parentExamineId === 51 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党支部书记未在本支部）
                        </span>
                      )}
                      {parentExamineId === 53 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置的党支部副书记未在本支部）
                        </span>
                      )}
                      {parentExamineId === 54 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，未设置党支部委员会）
                        </span>
                      )}
                      {parentExamineId === 55 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置党支部委员会的成员人数为偶数）
                        </span>
                      )}
                      {parentExamineId === 56 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置党支部委员会的成员人数少于3名）
                        </span>
                      )}
                      {parentExamineId === 57 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党组织委员会中，已设置党支部委员会的成员未在本支部）
                        </span>
                      )}
                      {parentExamineId === 58 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：检查前一年度的述职评议是否录入）
                        </span>
                      )}
                      {parentExamineId === 59 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置手机信息）
                        </span>
                      )}
                      {parentExamineId === 60 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，已设置的手机号码位数不等于11位数字）
                        </span>
                      )}
                      {parentExamineId === 61 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置身份证信息）
                        </span>
                      )}
                      {parentExamineId === 62 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，已设置的身份证号码位数不符合标准）
                        </span>
                      )}
                      {parentExamineId === 63 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置入党时间）
                        </span>
                      )}
                      {parentExamineId === 64 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置转正时间）
                        </span>
                      )}
                      {parentExamineId === 65 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，已设置的转正时间未满一年）
                        </span>
                      )}
                      {parentExamineId === 66 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，已设置的转正时间超过一年半以上）
                        </span>
                      )}
                      {parentExamineId === 67 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，已设置的政治面貌为“中共党员、预备党员”）
                        </span>
                      )}
                      {parentExamineId === 68 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置籍贯信息）
                        </span>
                      )}
                      {parentExamineId === 69 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置性别信息）
                        </span>
                      )}
                      {parentExamineId === 70 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置民族信息）
                        </span>
                      )}
                      {parentExamineId === 71 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置学历信息）
                        </span>
                      )}
                      {parentExamineId === 72 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党员信息中，未设置工作岗位）
                        </span>
                      )}
                      {parentExamineId === 73 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党费管理中，未设置党员党费标准）
                        </span>
                      )}
                      {parentExamineId === 74 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党费管理中，党员欠缴党费1月以上（含6个月以上））
                        </span>
                      )}
                      {parentExamineId === 75 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：党费管理中，党员欠缴党费6个月以上）
                        </span>
                      )}
                      {parentExamineId === 76 && (
                        <span
                          style={{
                            marginLeft: 20,
                            color: "#F46E65",
                            fontSize: 14,
                          }}
                        >
                          （判定条件：检查前一年度的民主评议是否录入）
                        </span>
                      )}
                    </div>
                    {examine_number && (
                      <div className="abnormal-count">
                        检测到<i>{examine_number}</i>项异常
                      </div>
                    )}
                  </div>
                }
              >
                {examine_types.map((item, index) => {
                  return (
                    <div className="abnormal-item" key={index}>
                      <div className="abnormal-name">
                        {item.text ? (
                          <span className="name">
                            {item.examine_name}&nbsp;&nbsp;&nbsp;({item.text})
                          </span>
                        ) : (
                          <span className="name">{item.examine_name}</span>
                        )}
                        <span>{item.examine_type_name}</span>
                      </div>
                      <div className="operation-box">
                        {[6, 12, 15, 17, 31, 37, 47, 55].includes(
                          parentExamineId
                        ) && (
                          <Button
                            className="abnormal-item-btn"
                            onClick={() => {
                              setModalData(item);
                            }}
                          >
                            备注
                          </Button>
                        )}
                        <Button
                          className="abnormal-item-btn"
                          onClick={() => onClickProcessing(item)}
                        >
                          处理
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </Panel>
            );
          })}
      </Collapse>
      <Modal {...modalProps} className="collapse-modal">
        <div className="content-box">
          <FormModal wrappedComponentRef={formRef} />
        </div>
      </Modal>
      <Modal {...dealModaProps}>
        <div className="deal-modal">
          <div className="tip-text">
            {flag
              ? "请前往“成员管理”模块完善党员信息"
              : "请前往“组织大数据”模块完善组织资料"}
          </div>
          <Button
            type="primary"
            onClick={() =>
              history.replace({
                pathname: flag ? "/member-management" : "/organize-framework",
                state: processingData,
              })
            }
          >
            前往
          </Button>
        </div>
      </Modal>
      <Spin className="spin-wrap" size="large" delay={50} spinning={loading} />
    </Drawer>
  );
};

export default CollapseComponent;
