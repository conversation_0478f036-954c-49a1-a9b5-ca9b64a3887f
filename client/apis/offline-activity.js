import { activityHost } from './config';
import { http, headers } from 'client/tool/axios';
import qs from 'querystring';

//获取多维度统计信息
export const fetchMultipleDimensions = (aid) => {
    return http.get(`${activityHost}/activity/count/offline/${aid}`);
}

//请求线下活动基本信息
export const fetchActivityBase = (aid) => {
    return http.get(`${activityHost}/activity/offline/detail/base`, { aid });
}

//请求线下活动审批信息
export const fetchActivityCheck = (aid) => {
    return http.get(`${activityHost}/activity/offline/detail/check`, { aid });
}

//请求线下活动日程信息
export const fetchActivityPlan = (aid) => {
    return http.get(`${activityHost}/activity/offline/detail/plan`, { aid });
}

//请求颁奖明细信息
export const fetchActivityReward = (aid) => {
    return http.get(`${activityHost}/activity/offline/detail/reward`, { aid });
}

//请求人员名单
export const fetchOfflineSiderUsers = ({ postData }) => {
    // console.log(postData)
    return http.get(`${activityHost}/offline/find_users`, postData);
}

//对人员或队伍进行审批
export const audit = (activity_id, offline_user_id, status) => {
    return http.post(`${activityHost}/offline/audit`, { activity_id, offline_user_id, status });
}

//对人员或队伍进行晋级
export const promote = (postData) => {
    return http.post(`${activityHost}/offline/promote`, postData);
}

//对人员或队伍进行颁奖
export const award = (postData) => {
    return http.post(`${activityHost}/offline/prize`, postData);
}

//线下活动人员名单下载
export const offlineUsersDownLoad = (postData) => {
  const params = {
      ...postData
  };
  return `/ac/offline/find_users_download?${qs.stringify(params)}`;
};
