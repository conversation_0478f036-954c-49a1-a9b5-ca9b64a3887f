import React from "react";
import { Modal, Spin, Timeline, message } from 'antd';
import SelfIcon from "components/self-icon";
import './index.less';

import {
    getList
} from "apis/version-log";

const renderTimeline = (recordList) => {
    if (!recordList || recordList && !recordList.length) return null;
    return recordList.map((item, index) => {
        let clsName;
        if(index === 0) {
            clsName = 'first-icon';
        }

        let content;
        try {
            content = decodeURIComponent(item.content || '');
        } catch (e){
            content = unescape(item.content || '');
        }
        return (
            <Timeline.Item
                className="version-log-record-list"
                key={`version_log_record_${index}`}
                dot={<div className={`icon ${clsName}`}></div>}
            >
                <div className="top">
                    <span className="date">{item.upgrade_time}</span>|<span className="v">{item.ver}</span>
                </div>
                <div className="content" dangerouslySetInnerHTML={{ __html: content }}></div>
            </Timeline.Item>
        );
    });
};

class VersionLogModal extends React.Component {
    constructor(props) {
        super(props);
        this.isInit = true;
        this.state ={
            title: '版本升级日志',
            isLoading: false,
            isInitLoading: false,
            dataSource: [],
            pages: {
                pageNum: 1,
                pageTotal: 0,
                pageSize: 5
            },
            isVisible: false,
            isEnd: false
        };
    }

    async getList(){
        const { pages, dataSource } = this.state;
        const { pageNum, pageSize } = pages;

        if(this.isInit){
            this.setState({
                isInitLoading: true
            });
        } else {
            this.setState({
                isLoading: true
            });
        }

        const params = {
            page_no: this.isInit ? pageNum : pageNum + 1,
            page_size: pageSize
        }
        const result = (await getList(params)).data;

        this.setState({
            isInitLoading: false,
            isLoading: false
        });
        if(result.code !== 0){
            return message.error(result.message);
        }

        const total = result.total;
        const data = dataSource.concat(result.data);

        this.isInit = false;
        this.setState({
            dataSource: data,
            pages: {
                pageNum: result.pageNum,
                pageTotal: result.total,
                pageSize: result.pageSize,
            },
            isEnd: data.length >= total
        });
    }

    componentWillReceiveProps(props) {
        const { isVisible, isUpdate } = props;
        const { isEnd, dataSource, pages } = this.state;
        if ( this.state.isVisible != isVisible ) {
            this.setState({
                isVisible,
                dataSource: isUpdate ? [] : dataSource,
                isEnd: isUpdate ? false : isEnd,
                pages: Object.assign(pages, {
                    pageNum: 1,
                    pageTotal: 0
                })
            }, () => {
                if(isUpdate){
                    this.isInit = true;
                }
                if(isVisible && this.isInit){
                    this.getList();
                }
            });
        }
    }

    render() {
        const { isVisible, isInitLoading, isLoading, isEnd, dataSource } = this.state;
        const { onClose } = this.props;

        let footerHTML;
        if(!isInitLoading){
            if(isEnd) {
                footerHTML = <div className="end" data-title="End" />;
            } else {
                footerHTML = (
                    <div className="more">
                        <a onClick={() => {
                            if(!isLoading) {
                                this.getList();
                            } else {
                                message.warning('请不要频繁点击!');
                            }
                        }}>点击查看更多<SelfIcon type='gsg_shuangjiantou_xia' /></a>
                    </div>
                );
            }
        }

        return (
            <Modal
                title='版本升级日志'
                visible={isVisible}
                width={800}
                height={600}
                footer={null}
                onCancel={() => {
                    this.setState({
                        isVisible: false,
                        isInitLoading: false
                    }, () => {
                        onClose();
                    });
                }}
                className="version-log-modal"
            >
                <Spin spinning={isInitLoading}>
                    <div className="version-log-modal-main">
                        {!!dataSource && !!dataSource.length ? (<Timeline pending={isLoading ? '加载中...' : false} className="version-log-record-panel">{renderTimeline(dataSource)}</Timeline>) : (<div>暂无日志记录</div>)}
                        {footerHTML}
                    </div>
                </Spin>
            </Modal>
        );
    }
}

export default VersionLogModal;