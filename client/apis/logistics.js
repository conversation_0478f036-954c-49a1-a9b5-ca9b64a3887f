import { logisticsHost } from './config';
import {http} from 'client/tool/axios-now';
import {dataIsNull} from 'client/tool/util';


// 系统订单状态其中可以包括多个子订单比如我们系统下单后京东拆分成多个,但是本状态指示针对该订单在本系统的状态
const logisticsCompany = [{ wy_id: 'ems' , wy_name: 'E<PERSON>', select_id: 'ems', select_name: '<PERSON><PERSON>' },
    { wy_id: 'shunfeng' , wy_name: '顺丰', select_id: 'shunfeng', select_name: '顺丰速运' },
    { wy_id: 'yunda' , wy_name: '韵达', select_id: 'yunda', select_name: '韵达快运' },
    { wy_id: 'zhongtong' , wy_name: '中通', select_id: 'zhongtong', select_name: '中通快递' },
    { wy_id: 'huitong' , wy_name: '百世汇通', select_id: 'huitong', select_name: '百世快递' },
    { wy_id: 'yuantong' , wy_name: '圆通', select_id: 'yuantong', select_name: '圆通速递' },
    { wy_id: 'shentong' , wy_name: '申通', select_id: 'shentong', select_name: '申通快递' },
    { wy_id: 'tiantian' , wy_name: '天天', select_id: 'tiantian', select_name: '天天快递' },
    { wy_id: 'quanfengkuaidi' , wy_name: '全峰', select_id: 'quanfeng', select_name: '全峰快递' },
    { wy_id: 'zhaijisong' , wy_name: '宅急送', select_id: 'zhaijisong', select_name: '宅急送' },
    { wy_id: 'guotongkuaidi' , wy_name: '国通', select_id: 'guotong', select_name: '国通快递' },
    { wy_id: 'debangwuliu' , wy_name: '德邦', select_id: 'debang', select_name: '德邦物流' },
    { wy_id: 'suer' , wy_name: '速尔', select_id: 'sure', select_name: '速尔物流' },
    { wy_id: 'ganzhongnengda' , wy_name: '能达', select_id: '', select_name: '' }, //
    { wy_id: 'youshuwuliu' , wy_name: '优速', select_id: 'yousu', select_name: '优速快递' },
    { wy_id: 'kuaijiesudi' , wy_name: '快捷', select_id: 'kuaijie', select_name: '快捷速递' },
    { wy_id: 'xinbangwuliu' , wy_name: '新邦物流', select_id: 'xinbang', select_name: '新邦物流' },
    { wy_id: '' , wy_name: '晟邦物流', select_id: 'shengbang', select_name: '晟邦物流' },
    { wy_id: 'tiandihuayu' , wy_name: '天地华宇', select_id: 'huayu', select_name: '天地华宇物流' },
    { wy_id: '' , wy_name: '京东物流', select_id: '', select_name: '' }, //
    { wy_id: 'jd' , wy_name: '京东快递', select_id: 'jingdong', select_name: '京东快递' },
    { wy_id: 'zhongtongguoji' , wy_name: '中通国际', select_id: '', select_name: ' ' } //
];

export const logisticsData = {
    ems: 'EMS',
    shunfeng: '顺丰',
    yunda: '韵达',
    zhongtong: '中通',
    huitong: '百世汇通',
    yuantong: '圆通',
    shentong: '申通',
    tiantian: '天天',
    quanfengkuaidi: '全峰',
    zhaijisong: '宅急送',
    guotongkuaidi: '国通',
    debangwuliu: '德邦',
    suer: '速尔',
    ganzhongnengda: '能达',
    youshuwuliu: '优速',
    kuaijiesudi: '快捷',
    xinbangwuliu: '新邦物流',
    shengbang: '晟邦物流',
    tiandihuayu: '天地华宇',
    jd: '京东快递',
    zhongtongguoji: '中通国际'
}

export const getloacalCompanyCode = (wy_code, companyArr = logisticsCompany, code = 'wy_id', name = 'wy_name', returnFiled = 'select_id') => {
    if (dataIsNull(wy_code) || dataIsNull(companyArr) || dataIsNull(companyArr.length)) {
        return;
    }
    let ret = undefined;
    for (let i = 0; i < companyArr.length; i++) {
        if (companyArr[i][code] === wy_code || companyArr[i][name] === wy_code ) {
            ret = companyArr[i][returnFiled];
            break;
        }
    }
    return ret;
}

export const findExpressCompanyList = () => {
    return http.post(`${logisticsHost}/express/findExpressCompanyList`, null, null, true);
};

export const findLogisticsTracking = (data) => {
    return http.post(`${logisticsHost}/express/findLogisticsTracking`, data, null, true);
}