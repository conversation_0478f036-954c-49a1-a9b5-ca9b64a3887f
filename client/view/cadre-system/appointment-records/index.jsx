import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import {
  Form,
  Input,
  Select,
  Button,
  Table,
  Modal,
  Popconfirm,
  message,
  DatePicker,
} from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryAnnualEvalList,
  queryGroupList,
  queryByCode,
  addOrUpdateAssign,
  deleteAssign,
  exportAssignList,
  queryAnnualEvalDetail,
  queryAssignList,
} from "client/apis/cadre-portrait";
import moment from "moment";

function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);

  const orgRef = useRef(null);

  const [org_ids, setOrgIds] = useState(["1"]);

  const [modalType, setModalType] = useState("add");

  const [org_name, setOrgName] = useState("");

  const [treeData, setTreeData] = useState([]);

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [typeOption, setTypeOption] = useState([]);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [pagination, setPagination] = useState({
    total: 0,
  });

  const [seqOption, setSeqOption] = useState([]);

  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  useEffect(() => {
    loadSeq();

    loadData();

    initCode();
  }, []);

  const loadData = async (_page, org_id) => {
    setLoading(true);
    const _params = getFieldsValue();
    _params.start_time = _params.start_time
      ? moment(_params.start_time).format("YYYY-MM-DD")
      : undefined;
    _params.end_time = _params.end_time
      ? moment(_params.end_time).format("YYYY-MM-DD")
      : undefined;

    const params = {
      ..._params,
      page: _page || page,
      org_id: org_id || org_ids[0],
    };

    const res = await queryAssignList(params);

    if (res.data.code === 0) {
      const data = res.data.data.content;

      setDataSource(data);

      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    } else {
      message.error(res.data.message);
    }

    setLoading(false);
  };

  const loadSeq = async () => {
    const res = await queryGroupList();

    if (res.data.code === 0) {
      setSeqOption(res.data.data);
    } else {
      message.error(res.data.message);
    }
  };

  const initCode = async () => {
    const res = await queryByCode({ code: 10013 });
    if (res.data.code === 0) {
      setTypeOption(res.data.data);
    } else {
      message.error(res.data.message);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData(1, orgs[0]);
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportAssignList({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onDel = async (record) => {
    const res = await deleteAssign({
      pms_user_assign_id: record.pms_user_assign_id,
    });

    if (res.data.code === 0) {
      loadData();

      message.success(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queueMicrotask(() => {
      const _ = { ...record };

      _.assign_time = _.assign_time ? moment(_.assign_time) : undefined;

      modalFormRef.current.setFieldsValue(_);
    });

    // queryAnnualEvalDetail({
    //   annual_eval_id: record.annual_eval_id,
    // }).then((res) => {
    //   if (res.data.code === 0) {
    //     const data = res.data.data;
    //     data.level = data.level ? String(data.level) : undefined;

    //     modalFormRef.current.setFieldsValue(data);
    //     // queueMicrotask(() => {
    //     // });
    //   }
    // });
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=3&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const columns = [
    {
      title: "研究时间",
      dataIndex: "assign_time",
      key: "assign_time",
      width: "8%",
      align: "center",
    },
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: "5%",
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "性别",
      dataIndex: "gender",
      key: "gender",
      width: "5%",
      align: "center",
    },
    {
      title: "现任职务",
      dataIndex: "current_job",
      key: "current_job",
      // width: 100,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "拟任职务",
      dataIndex: "assign_job",
      key: "assign_job",
      // width: 100,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "拟免职务",
      dataIndex: "exempt_job",
      key: "exempt_job",
      // width: 100,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "类别",
      dataIndex: "type",
      key: "type",
      width: "10%",
      align: "center",
      render(_, record) {
        const data = typeOption.filter((item) => item.op_key == record.type);

        if (data.length) {
          return data[0].op_value;
        }

        return "";
      },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: "7%",
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData(page);
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData(1);
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }

        values.assign_time = values.assign_time
          ? values.assign_time.format("YYYY-MM-DD")
          : undefined;

        const res = await addOrUpdateAssign({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, setFieldsValue } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout}>
        {getFieldDecorator("pms_user_assign_id")(<Input type="hidden" />)}
        {getFieldDecorator("user_id")(<Input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 20 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="研究时间">
          {getFieldDecorator("assign_time", {
            rules: [{ required: true, message: "请输入年度考核" }],
          })(<DatePicker />)}
        </Form.Item>
        <Form.Item label="现任职务">
          {getFieldDecorator("current_job", {
            rules: [{ required: false, message: "请输入综合得分" }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="拟任职务">
          {getFieldDecorator("assign_job", {
            rules: [{ required: false, message: "请输入拟任职务" }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="拟免职务">
          {getFieldDecorator("exempt_job", {
            rules: [{ required: false, message: "请输入拟免职务" }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="类别">
          {getFieldDecorator("type", {
            rules: [{ required: true, message: "选择类别" }],
          })(
            <Select placeholder="请选择">
              {typeOption.map((item) => {
                return (
                  <Select.Option key={item.op_key}>
                    {item.op_value}
                  </Select.Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="appointment-records">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} ref={(ref) => (orgRef.current = ref)} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="研究时间">
              {getFieldDecorator("start_time")(<DatePicker />)}
              <span style={{ padding: "0 10px" }}>至</span>
              {getFieldDecorator("end_time")(<DatePicker />)}
            </Form.Item>
            <Form.Item label="类别">
              {getFieldDecorator("type")(
                <Select style={{ minWidth: "150px" }} placeholder="请选择">
                  {typeOption.map((item) => {
                    return (
                      <Select.Option value={item.op_key}>
                        <div style={{ whiteSpace: "normal" }}>
                          {item.op_value}
                        </div>
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "干部任用记录"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
