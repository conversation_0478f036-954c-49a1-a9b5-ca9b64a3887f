const { http, filterParamsValue } = require("client/tool/axios");
import { ucHost } from "../config";

/**
 * 400020306001-新建党建阵地
 * @param {Object} data
 * @returns
 */
const addPartyPositions = (data) => {
  return http.post(`${ucHost}/partyPositions/add`, filterParamsValue(data));
};
const getPartyPositionsList = (org_id) => {
  return http.get(`${ucHost}/partyPositions/list?org_id=${org_id}`);
};
/**
 * 400020306004-删除党建阵地
 * @param {string} org_id 阵地所属组织编号
 * @param {string} party_position_id 阵地编号
 * @returns
 */
const delPartyPosition = (org_id, party_position_id) => {
  return http.get(
    `${ucHost}/partyPositions/del?org_id=${org_id}&party_position_id=${party_position_id}`
  );
};
const editPartyPositions = (data) => {
  return http.post(`${ucHost}/partyPositions/edit`, filterParamsValue(data));
};

export {
  addPartyPositions,
  editPartyPositions,
  getPartyPositionsList,
  delPartyPosition,
};
