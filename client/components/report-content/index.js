// 纪实系统，会议议题，报表管理组件
import React, { Component } from "react";
import { Button, Table, Divider } from "antd";
import AddModal from "./sub-components/add-modal";
import { addKeyToTableDataSource } from "tool/util";
import "./index.less";

import propTypes from "prop-types";

class ReportContent extends Component {
  constructor() {
    super();
    this.state = {
      contents: [],
      modalVisible: false,
      // 标志位：当前是否编辑状态，默认为否（新增状态）
      isEdit: false,
      // 暂存当前编辑的内容对象
      currentEdit: null,
      // 暂存当前编辑的内容Index
      currentIndex: null
    };
    this.showAddModal = this.showAddModal.bind(this);
    this.hideAddModal = this.hideAddModal.bind(this);
    this.addContent = this.addContent.bind(this);
    this.exportData = this.exportData.bind(this);
    this.batchAddContent = this.batchAddContent.bind(this);
  }

  initContents(contents) {
    if (contents && Array.isArray(contents)) {
      this.setState({
        contents
      });
    }
  }

  componentDidMount() {
    const { contents } = this.props;
    this.initContents(contents);
  }

  componentWillReceiveProps(props) {
    const { contents } = props;
    this.initContents(contents);
  }

  // 显示添加弹窗
  showAddModal() {
    const { afterChange } = this.props;
    this.setState({
      modalVisible: true
    });
    afterChange && afterChange();
  }
  // 隐藏添加弹窗
  hideAddModal() {
    this.setState({
      modalVisible: false,
      isEdit: false,
      currentEdit: null,
      currentIndex: null
    })
  }
  // 批量添加内容，用于编辑议题时内容回填
  batchAddContent(arr = []) {
    let contents = [];
    if (arr && Array.isArray(arr) && arr.length !== 0) {
      contents = arr;
    }
    addKeyToTableDataSource(contents);
    this.setState({
      contents
    });
  }
  // 添加内容
  addContent(item, edit) {
    let { contents, isEdit, currentIndex } = this.state;
    const { onChange } = this.props;
    if (edit && isEdit) {
      contents[currentIndex] = item;
    } else {
      contents.push(item);
    }
    addKeyToTableDataSource(contents);
    // console.log(contents);
    this.setState({
      contents
    });
    onChange && onChange(contents);
  }
  // 删除内容
  removeContent(index) {
    let { contents } = this.state;
    contents.splice(index, 1);
    addKeyToTableDataSource(contents);
    // console.log(contents);
    this.setState({
      contents
    });
  }
  // 编辑内容
  editContent(index, record) {
    // console.log(index, record);
    this.setState({
      isEdit: true,
      currentEdit: record,
      currentIndex: index
    }, () => {
      this.showAddModal();
    })
  }
  // 数据输出接口
  exportData() {
    const { contents } = this.state;
    return JSON.parse(JSON.stringify(contents));
  }

  render() {
    const { modalVisible, contents, isEdit, currentEdit } = this.state;
    const _this = this;
    const columns = [
      {
        title: "序号",
        dataIndex: "key",
        align: "center"
      }, {
        title: "内容",
        dataIndex: "name",
        align: "center"
      }, {
        title: "完成方式",
        dataIndex: "type",
        align: "center",
        render(text, record, index) {
          // console.log(text);
          return (
            <div>{text === 1 ? "填写内容" : text === 2 ? "单项选择" : text === 3 ? "多项选择" : "未知类型"}</div>
          )
        }
      }, {
        title: "操作",
        align: "center",
        render(text, record, index) {
          return (
            <div>
              <a onClick={() => _this.editContent(index, record)}>编辑</a>
              <Divider type="vertical" />
              <a onClick={() => _this.removeContent(index)}>删除</a>
            </div>
          )
        }
      }
    ];
    const tableProps = {
      bordered: true,
      columns,
      dataSource: contents,
      pagination: false
    }
    const addModalProps = {
      visible: modalVisible,
      showContinueModal: this.showContinueModal,
      hideAddModal: this.hideAddModal,
      addContent: this.addContent,
      isEdit,
      currentEdit
    }

    return (
      <div className="report-content">
        {/* <h4>报表内容</h4> */}
        <div className="report-content-wrapper">
          <a onClick={this.showAddModal} style={
            (contents && Array.isArray(contents) && contents.length !== 0) ? { display: "inline-block", marginBottom: "28px", textDecoration: "underline" } : { textDecoration: "underline" }
          }>新增内容</a>
          {
            (contents && Array.isArray(contents) && contents.length !== 0) &&
            <Table {...tableProps} />
          }
        </div>
        <AddModal {...addModalProps} />
      </div>
    )
  }
}

ReportContent.propTypes = {
  afterChange: propTypes.func,
  onChange: propTypes.func
}

ReportContent.defaultProps = {
  afterChange: () => { },
  onChange: () => { }
}

export default ReportContent;