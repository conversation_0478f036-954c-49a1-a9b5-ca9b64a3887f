import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import { Form, Input, Popconfirm, Button, Table, Modal, message, Radio, DatePicker, Select } from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryMattersCheck,
  addOrUpdateMattersCheck,
  deleteMattersCheck,
  exportMattersCheck,
  queryMajorEventDetail,
} from "client/apis/cadre-portrait";
import moment from "moment";
const { Option } = Select;
function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;
  const { RangePicker } = DatePicker;
  const modalFormRef = useRef(null);

  const [treeData, setTreeData] = useState([]);

  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });

  useEffect(() => {
    loadData();
  }, []);
  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();
    const params = {
      // ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.user_name,
    };
    const res = await queryMattersCheck(params);
    if (res.data.code === 0) {
      const data = res.data.data.data;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    }

    setLoading(false);
  }

  const loadDataDesc = async (id) => {
    const res = await queryMajorEventDetail({
      performance_id: id,
    });

    if (res.data.code === 0) {
      modalFormRef.current.setFieldsValue(res.data.data);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportMattersCheck({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=6&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteMattersCheck({
      id: record.matters_check_id,
    });
    if (res.data.code === 0) {
      loadData();
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queueMicrotask(() => {
      // loadDataDesc(record.performance_id);
      // modalFormRef.current.setFieldsValue(record);
      const _ = { ...record };
      _.user_name = _.name ? _.name : undefined;
      _.check_date = _.check_date ? moment(_.check_date) : undefined;
      // console.log("record: ", _)
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      width: '6%',
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 100,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "查核时间",
      dataIndex: "check_date",
      key: "check_date",
      width: '8%',
      align: "center",
    },
    {
      title: "查核类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      align: "center",
    },
    {
      title: "查核结果",
      dataIndex: "result",
      key: "result",
      width: 100,
      align: "center",
    },
    {
      title: "本人说明",
      dataIndex: "explains",
      key: "explains",
      width: 100,
      align: "center",
    },
    {
      title: (
        <div>
          认定<br />意见
        </div>
      ),
      dataIndex: "opinion",
      key: "opinion",
      width: '8%',
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: '8%',
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name, start_time: _start_time } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        // if (Array.isArray(_start_time) && _start_time.length === 2) {
        //   const [start_time, end_time] = _start_time;
        //   values.start_time = start_time.format("YYYY-MM-DD");
        //   values.end_time = end_time.format("YYYY-MM-DD");
        // }
        values.check_date = values.check_date ? values.check_date.format("YYYY-MM-DD") : undefined;
        const res = await addOrUpdateMattersCheck({
          ...values,
        });
        // console.log("values: ", values);

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };
    const options = [
      "不一致",
      "不一致（基本一致）",
      "基本一致",
      "漏报情节较轻",
      "漏报情节较重",
      "瞒报",
      "如实报告",
      "如实报告（基本一致）",
      "涉嫌违纪",
      "隐瞒不报",
      "隐瞒不报情节较轻"
    ];

    const options1 = [
      "查核验证对象",
      "调任",
      "进一步使用",
      "列入优秀后备干部",
      "拟任国企领导班子成员",
      "拟双向交流",
      "拟提拔",
      "拟提拔（县属国企领导班子成员）",
      "拟提拔（县属医院正职）",
      "市委组织部提出后备人选",
      "随机抽查",
      "增补县政协委员",
      "重点核查"
    ];
    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("matters_check_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="时任职务">
          {getFieldDecorator("job", {
            rules: [{ required: true, message: "请输入时任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="查核时间">
          {getFieldDecorator("check_date", {
            rules: [{ required: true, message: "请输入查核时间" }],
          })(<DatePicker />)}
        </Form.Item>
        <Form.Item label="查核类型">
          {getFieldDecorator("type", {
            rules: [{ required: true, message: "请选择查核类型" }],
          })(
            <Select placeholder="请选择">
              {
                options1.map(item => <Option key={item} >{item}</Option>)
              }
            </Select>
          )}
        </Form.Item>
        <Form.Item label="核查结果">
          {getFieldDecorator("result", {
            rules: [{ required: true, message: "请输入核查结果" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="本人说明">
          {getFieldDecorator("explains", {
            rules: [{ required: false, message: "请输入本人说明" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="认定意见">
          {getFieldDecorator("opinion", {
            rules: [{ required: true, message: "请输入政治生态和评级" }],
          })(
            <Select placeholder="请选择">
              {
                options.map(item => <Option key={item} >{item}</Option>)
              }
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="petition-reporting">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            {/* <Form.Item label="事件">
              {getFieldDecorator("event")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item> */}
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "个人事项核查"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
