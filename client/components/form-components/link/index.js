import React from "react";
import "./style.less";
import { Form, Radio, Input } from "antd";

import propTypes from "prop-types";
import adaptiveLayout from "../gridLayout";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

class Link extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {

    const { form } = this.props;
    const { getFieldDecorator, getFieldValue, validateFields, setFields } = form;
    return (
      <div className="link-container">
        <FormItem
          label="外链开关"
          {...adaptiveLayout.formWrapper}
        >
          {
            getFieldDecorator("is_open", {})(
              <RadioGroup>
                <Radio value={1}>开启</Radio>
                <Radio value={0}>关闭</Radio>
              </RadioGroup>
            )
          }
        </FormItem>
        {
          getFieldValue("is_open") === 1 &&
          <FormItem
            label="外链类型"
            {...adaptiveLayout.formWrapper}
          >
            {
              getFieldDecorator("link_type", {})(
                <RadioGroup>
                  <Radio value={1}>二维码</Radio>
                  <Radio value={2}>链接</Radio>
                </RadioGroup>
              )
            }
          </FormItem>
        }
        <FormItem
          label="外链名称"
          validateStatus={(getFieldValue("link_name") && getFieldValue("link_name").length > 6) ? "error" : ""}
          help={(getFieldValue("link_name") && getFieldValue("link_name").length > 6) ? "限定不超过6个字" : null}
          {...adaptiveLayout.formWrapper}
        >
          {
            getFieldDecorator("link_name", {
              // validateTrigger: "onBlur",
              rules: [
                { max: 6, message: "限定不超过6个字" }
              ]
            })(
              <Input placeholder="请输入" style={{ width: 160 }} />
            )
          }
        </FormItem>
        <FormItem
          label="链接"
          {...adaptiveLayout.formWrapper}
        >
          {
            getFieldDecorator("link_str", {})(
              <Input placeholder="请输入" style={{ width: 360 }} />
            )
          }
        </FormItem>
      </div>
    )
  }
}

Link.propTypes = {
  updateState: propTypes.func,
  isPreview: propTypes.bool,
  data: propTypes.object,
  state: propTypes.object,
  form: propTypes.object,
  onChange: propTypes.func
}

Link.defaultProps = {
  updateState: () => { },
  isPreview: false,
  data: {},
  state: {},
  form: {},
  onChange: (data) => { console.log("default onChange", data) }
}

export default Form.create({
  onFieldsChange(props, changedFields) {
    const { updateState } = props;
    let result = {};
    for (let key in changedFields) {
      result[key] = changedFields[key].value;
    }
    updateState(result);
  },
  mapPropsToFields: (props) => {
    const { state } = props;
    const result = {};
    result.is_open = Form.createFormField({
      name: "is_open",
      value: state.is_open
    });
    result.link_name = Form.createFormField({
      name: "link_name",
      value: state.link_name
    });
    result.link_type = Form.createFormField({
      name: "link_type",
      value: state.link_type
    });
    result.link_str = Form.createFormField({
      name: "link_str",
      value: state.link_str
    });
    return result
  }
})(Link);