/**
 * 送服务相关接口
 * http://************:8090/pages/viewpage.action?pageId=39289708
 */

import { http } from "client/tool/axios";
import { vlHost } from './config';

const MOCK_URL = vlHost;

/**
 * 10630202020402-服务列表（PC端）
 */
export const getServiceList = (params) => {
  return http.post(`${MOCK_URL}/volunteer/service/list`, params)
}

/**
 * 10630202020404-服务详情
 */
export const getServiceInfo = (service_info_id) => {
  return http.get(`${MOCK_URL}/volunteer/service/detail`, { service_info_id })
}

/**
 * 编辑/新增 服务
 * 10630202020401-发布服务
 * 10630202020405-服务编辑
 */
export const editServiceInfo = (id, params) => {
  if (id) {
    params.publish_service_info_id = id
    return http.post(`${MOCK_URL}/volunteer/service/edit`, params)
  } else {
    return http.post(`${MOCK_URL}/volunteer/service/add`, params)
  }
}

/**
 * 10630202020403-服务状态修改
 */
export const updateServiceStatus = (service_info_id, status, end_date = null) => {
  return http.get(`${MOCK_URL}/volunteer/service/change/status`, {
    service_info_id, status, end_date
  })
}