import React, { Component } from 'react';
import { Modal, Form, Input, Button, Radio, message } from 'antd';
import { connect } from 'dva';
import Image from './upload-image';
// import UploadVedio from './upload-vedio';
import VideoUpload from 'components/video-upload';
import ShowUploadFileType from 'components/show-upload-file-type';
import './index.less';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 4 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 20 },
    sm: { span: 20 },
  },
};

class ModalTop extends Component {
  constructor() {
    super();
    this.state = {
      files: [],
    };
  }
  componentDidMount() {}
  componentDidUpdate(prevProps) {
    const { baweiyuzhen, form } = this.props;
    const { show, index } = baweiyuzhen.status.top_commodity;
    if (show && show !== prevProps.baweiyuzhen.status.top_commodity.show) {
      if (index !== undefined) {
        const data = baweiyuzhen.formData.top_commodity[index];
        form.setFieldsValue({ link_type: data.link_type });
        this.setState({
          files: [
            {
              id: data.video_id,
              file_name: data.video_name,
              path: data.video_url,
            },
          ],
        });
        setTimeout(() => {
          form.setFieldsValue(data);
        }, 0);
      }
    }
  }
  onSubmit() {
    const { form, baweiyuzhen, dispatch } = this.props;
    form.validateFields((err, value) => {
      if (err) return;
      const { files } = this.state
      dispatch({
        type: 'baweiyuzhen/submitModal',
        payload: { data: { ...value, video_id: files[0].id, video_name: files[0].file_name }, type: 'top_commodity' },
      });
      this.onCancel();
    });
  }
  onCancel() {
    const { dispatch, form } = this.props;
    form.resetFields();
    this.setState({
      files: [],
    });
    dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: { top_commodity: { show: false } },
    });
  }

  render() {
    const { baweiyuzhen, form } = this.props;
    if (!baweiyuzhen) {
      return null;
    }
    const { top_commodity } = baweiyuzhen.status;
    const { index, show } = top_commodity;
    const { getFieldValue, getFieldDecorator } = form;
    return (
      <div>
        <Modal
          title={`${index === undefined ? '新增' : '编辑'}视频`}
          visible={show}
          width={660}
          wrapClassName="modal-baweiyuzhen"
          bodyStyle={{ minHeight: 400, width: 660, margin: 'auto' }}
          destroyOnClose
          onCancel={this.onCancel.bind(this)}
          footer={null}>
          <Form>
            <FormItem {...formItemLayout} label="标题">
              {getFieldDecorator('name', {
                rules: [
                  {
                    required: true,
                    message: '请填写标题',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="介绍">
              {getFieldDecorator('description', {
                rules: [
                  {
                    required: true,
                    message: '请填写介绍',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="视频">
              {getFieldDecorator('video_url', {
                // valuePropName:'fileList',
                rules: [
                  {
                    required: true,
                    message: '请上传视频',
                  },
                ],
              })(
                <VideoUpload
                  onChange={(path, file) => {
                    this.setState({ files: [{ ...file }] });
                    // form.setFieldsValue({ video_url: file.path });
                  }}
                  max={1}
                  upType="no-compress-video"
                  multiple={false}>
                  <Button type="link">上传文件</Button>
                </VideoUpload>
                // <UploadVedio>
                //   <Button type="link">上传文件</Button>
                // </UploadVedio>
              )}
              <ShowUploadFileType
                data={this.state.files}
                updateState={(files) => {
                  if (files.length === 0) {
                    form.resetFields('video_url');
                  }
                  this.setState({ files });
                }}
                isDelete
                hiddenFileSize
              />
            </FormItem>
            <FormItem {...formItemLayout} label="封面图片">
              {getFieldDecorator('image_url', {
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<Image size={{ w: 700, h: 400 }} ratio={3.5} />)}
            </FormItem>
            {/* <FormItem {...formItemLayout} label="链接方式">
              {getFieldDecorator('link_type', {
                rules: [
                  {
                    required: true,
                    message: '请选择链接方式',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={1}>商品类别</Radio>
                  <Radio value={2}>商品详情</Radio>
                </Radio.Group>
              )}
            </FormItem>
            {getFieldValue('link_type') === 1 && (
              <FormItem {...formItemLayout} label="商品类别ID">
                {getFieldDecorator('recommend_type', {
                  rules: [
                    {
                      required: true,
                      message: '请填写ID',
                    },
                  ],
                })(<Input style={{ width: 450 }} placeholder="" />)}
              </FormItem>
            )}
            {getFieldValue('link_type') === 2 && (
              <FormItem {...formItemLayout} label="商品ID">
                {getFieldDecorator('commodity_id', {
                  rules: [
                    {
                      required: true,
                      message: '请填写ID',
                    },
                  ],
                })(<Input style={{ width: 450 }} placeholder="" />)}
              </FormItem>
            )}
            <FormItem {...formItemLayout} label={'链接地址'}>
              {getFieldDecorator('target_url', {
                rules: [
                  {
                    required: true,
                    message: '请填写链接地址',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem> */}
            <FormItem {...formItemLayout} label="状态">
              {getFieldDecorator('is_show', {
                rules: [
                  {
                    required: true,
                    message: '请选择显示状态',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={1}>显示</Radio>
                  <Radio value={0}>隐藏</Radio>
                </Radio.Group>
              )}
            </FormItem>
          </Form>
          <div className="buttons-wrapper">
            <Button
              onClick={() => {
                this.onCancel();
              }}>
              取消
            </Button>
            <Button type="primary" onClick={() => this.onSubmit()}>
              确定
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}
export default connect(({ baweiyuzhen }) => ({ baweiyuzhen }))(Form.create()(ModalTop));
