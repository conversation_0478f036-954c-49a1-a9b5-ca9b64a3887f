import {mobileHost} from 'client/apis/config';

export default (
	{
		type,
		type_name,
		activity_id
	}) => {
	const {
		_tk = '',
		_un = '',
		_uid = '',
		_type = '',
		_oid = '',
		userInfo = ''
	} = window.sessionStorage;
	const {phone = ''} = JSON.parse(userInfo);
	return mobileHost
		.concat('/')
		.concat(activity_id)
		.concat('/')
		.concat(type)
		.concat(`?isview=1&_tk=${_tk}&_uid=${_uid}&_un=${_un}&_type=${_type}&_oid=${_oid}&phone=${phone}`)
}
