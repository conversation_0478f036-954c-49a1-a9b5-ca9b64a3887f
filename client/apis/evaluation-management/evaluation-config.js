import { http, headers, filterParamsValue } from "client/tool/axios";
import { ucPms1 } from "../config";

// const host = 'http://nmwork.s7.tunnelfrp.com/eval/config'

export const loadLeaderList = (params) => {
  return http.get(`${ucPms1}/eval/config/list/leaders`, params);
};

export const addLeader = (data) => {
  return http.post(`${ucPms1}/eval/config/add/leader`, data);
};

export const deleteLeader = (data) => {
  return http.delete(`${ucPms1}/eval/config/delete/leader`, data);
};

export const addLeaderOrg = (data) => {
  return http.post(`${ucPms1}/eval/config/add/leader/org`, data);
};

export const addLeaderCadre = (data) => {
  return http.post(`${ucPms1}/eval/config/add/leader/cadre`, data);
};

export const loadGoodCadre = (data) => {
  return http.get(`${ucPms1}/eval/config/good/grade`, data);
};

export const editGoodCadre = (data) => {
  return http.post(`${ucPms1}/eval/config/good/grade/edit`, data);
};
