.arrow-label {
  height: 28px;
  padding: 0 15px;
  position: relative;
  text-align: center;
  display: inline-block;
  line-height: 28px;
  font-size: 14px;
  color: #fff;
  &.arrow-label-dir-lr {
    border-radius: 2px 0 0 2px;
    &:before {
      content: '';
      position: absolute;
      border-top: 14px solid transparent;
      border-left: 14px solid red;
      border-bottom: 14px solid transparent;
      top: 0;
      right: -14px;
    }
  }
  &.arrow-label-dir-lt {
    border-radius: 0 2px 2px 0;
    &:after {
      content: '';
      position: absolute;
      border-top: 14px solid transparent;
      border-right: 14px solid red;
      border-bottom: 14px solid transparent;
      top: 0;
      left: -14px;
    }
  }
  &.arrow-label-color-blue {
    background: #1790C9;
    &:before {
      border-left: 14px solid #1790C9;
    }
    &:after {
      border-right: 14px solid #1790C9;
    }
  }
  &.arrow-label-color-green {
    background: #4FC136;
    &:before {
      border-left: 14px solid #4FC136;
    }
    &:after {
      border-right: 14px solid #4FC136;
    }
  }
  &.arrow-label-color-gray {
    background: #999999;
    &:before {
      border-left: 14px solid #999999;
    }
    &:after {
      border-right: 14px solid #999999;
    }
  }
  &.arrow-label-color-red {
    background: #f44336;
    &:before {
      border-left: 14px solid #f44336;
    }
    &:after {
      border-right: 14px solid #f44336;
    }
  }
  &.arrow-label-color-pink {
    background: #e91e63;
    &:before {
      border-left: 14px solid #e91e63;
    }
    &:after {
      border-right: 14px solid #e91e63;
    }
  }
  &.arrow-label-color-purple {
    background: #9C27B0;
    &:before {
      border-left: 14px solid #9C27B0;
    }
    &:after {
      border-right: 14px solid #9C27B0;
    }
  }
  &.arrow-label-color-purple-blue {
    background: #3f51b5;
    &:before {
      border-left: 14px solid #3f51b5;
    }
    &:after {
      border-right: 14px solid #3f51b5;
    }
  }
  &.arrow-label-color-light-blue {
    background: #2196f3;
    &:before {
      border-left: 14px solid #2196f3;
    }
    &:after {
      border-right: 14px solid #2196f3;
    }
  }
  &.arrow-label-color-sky-blue {
    background: #00BCD4;
    &:before {
      border-left: 14px solid #00BCD4;
    }
    &:after {
      border-right: 14px solid #00BCD4;
    }
  }
  &.arrow-label-color-jade {
    background: #009688;
    &:before {
      border-left: 14px solid #009688;
    }
    &:after {
      border-right: 14px solid #009688;
    }
  }
  &.arrow-label-color-deep-green {
    background: #4caf50;
    &:before {
      border-left: 14px solid #4caf50;
    }
    &:after {
      border-right: 14px solid #4caf50;
    }
  }
  &.arrow-label-color-yellow-green {
    background: #cddc39;
    &:before {
      border-left: 14px solid #cddc39;
    }
    &:after {
      border-right: 14px solid #cddc39;
    }
  }
  &.arrow-label-color-orange {
    background: #ffc107;
    &:before {
      border-left: 14px solid #ffc107;
    }
    &:after {
      border-right: 14px solid #ffc107;
    }
  }
  &.arrow-label-color-deep-orange {
    background: #ff9800;
    &:before {
      border-left: 14px solid #ff9800;
    }
    &:after {
      border-right: 14px solid #ff9800;
    }
  }
  &.arrow-label-color-crimson {
    background: #ff5722;
    &:before {
      border-left: 14px solid #ff5722;
    }
    &:after {
      border-right: 14px solid #ff5722;
    }
  }
  &.arrow-label-color-soli {
    background: #795548;
    &:before {
      border-left: 14px solid #795548;
    }
    &:after {
      border-right: 14px solid #795548;
    }
  }
}
