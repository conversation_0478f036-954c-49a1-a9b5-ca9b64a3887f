import React, { useEffect, useState, useMemo } from "react";
import { Steps } from "antd";
import moment from "moment";
import "./index.less";

const { Step } = Steps;
const Status = {
  2: "已提交",
  3: "初审通过",
  4: "终审通过",
  5: "初审打回",
  6: "终审打回",
};

const approveRecord = (props) => {
  const { dataSource } = props;

  return (
    <div className="approve-record">
      <Steps progressDot direction="vertical">
        {dataSource &&
          dataSource.length !== 0 &&
          dataSource.map((item) => {
            return (
              <Step
                title={Status[item.status]}
                subTitle={moment(item.create_time).format("YYYY-MM-DD")}
                description={
                  <React.Fragment>
                    <div className="user-name">{item.user_name}</div>
                    {item.status !== 2 && (
                      <div className="comments">
                        审核意见：<span>{item.comments || "无"}</span>
                      </div>
                    )}
                  </React.Fragment>
                }
              />
            );
          })}
        {dataSource && dataSource[dataSource.length - 1].status === 3 && (
          <Step className="wait" title="待审核" />
        )}
      </Steps>
    </div>
  );
};
export default approveRecord;
