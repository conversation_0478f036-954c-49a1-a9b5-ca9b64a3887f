import { http } from "client/tool/axios";
import { mtHost, sasHost } from "./config";

export const getAllCategoryList = (params = {}) => {
  return http.get(`${mtHost}/type/list-all`, params);
};
//查询领导干部生活统计数据
export const getLeaderOrgLifeList = (params = {}) => {
  return http.post(`${sasHost}/leader-org-life/list`, params);
};
//查询党支部生活统计数据
export const getOrgLifeList = (params = {}) => {
  return http.post(`${sasHost}/org-life/list`, params);
};
//查询统计报表设置相关信息
export const getConfigDetail = (params = {}) => {
  return http.post(`${sasHost}/sas-config/detail`, params);
};
//10500802-党员组织生活统计
export const getPartyMemberOrgLife = (params = {}) => {
  return http.post(`${sasHost}/party-member-org-life/list`, params);
};
//10500804-组织生活一览表统计
export const getOrgLifeView = (params = {}) => {
  return http.get(`${sasHost}/org-life-view/list`, params);
};
// 1040090005-导出党支部组织生活统计
export const getExportLifeInfo = (params = {}) => {
  return http.get(`${sasHost}/org-life/export/life`, params);
};

