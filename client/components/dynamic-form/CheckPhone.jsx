import React, { PureComponent } from 'react';
import { Popover, Input, Button, Icon, Spin } from "antd";
import { addCheckPhone } from "client/apis/organize";
import { formatInitValue } from "./utils";
import "./checkphone.less";


/**
 * 
 */
export default class CheckPhone extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      loading: false,
      message: "",
    };
    this.renderPopoverContent = this.renderPopoverContent.bind(this);
    this.handleVisibleChange = this.handleVisibleChange.bind(this);
    this.renderBefore = this.renderBefore.bind(this);
    this.onCheck = this.onCheck.bind(this);
  }
  renderPopoverContent() {
    const { loading, message } = this.state;
    if (loading) {
      const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;
      return <Spin indicator={antIcon} />;
    }
    return message;
  }
  onCheck() {
    const { attr , form, methods } = this.props;
    const { validateFields, getFieldsValue } = form;
    const { currentorg, fieldlist, onCheckPhone } = attr;
    validateFields([attr.id]).then(values  => {
      this.setState({
        loading: true,
        visible: true,
      });
      const formValue = getFieldsValue();
      addCheckPhone({
        phone: values[attr.id],
        oid: currentorg
      }).then((res) => {
        const { code, status, data, message } = res.data;
        if (code === 0 && status === 200) {
          this.setState({loading: false, visible: false}, () => {
            // 回调事件
            const callBack = methods[onCheckPhone];
            if (callBack) callBack(data);
            // 删除空字段
            const formatData = {};
            for (const key in data) {
              if (Object.hasOwnProperty.call(data, key)) {
                const item = data[key];
                if (item !== undefined && item !== null) {
                  formatData[key] = item;
                }
              }
            }
            // 表单重新赋值回填
            form.setFieldsValue(formatInitValue(fieldlist, {...formValue, ...formatData}));
          });
        } else {
          this.setState({loading: false, message});
        }
      });
    });

  }
  handleVisibleChange(visible) {
    this.setState({ visible });
  }
  renderBefore() {
    const { loading } = this.state;
    const { disabled } = this.props.attr || {};
    if (disabled) {
      return null;
    }
    return (
      <Popover
        disabled={disabled}
        trigger='click'
        visible={this.state.visible}
        content={this.renderPopoverContent()}
        onVisibleChange={this.handleVisibleChange}
      >
        <Button disabled={disabled}  loading={loading} type="primary" style={{width: "100%", height: "30px", border: 0, borderRadius: "0 3px 3px 0"}} onClick={this.onCheck}>检测重复</Button>
      </Popover >
    );
  }
  render() {
    const { attr, placeholder } = this.props;
    const { onCheckPhone, ...attrProps } = attr;
    return (
      <Input placeholder={placeholder} allowClear {...attrProps}   className="check-phone-btn" addonAfter={this.renderBefore()}/>
    );
  }
}
