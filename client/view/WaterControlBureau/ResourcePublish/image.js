import React, { Component } from "react";
import { Icon, message } from "antd";
import Clip from "components/clip/clipping";
import { CDN } from "apis/config";

class Image extends Component {
  constructor() {
    super();
    this.state = {
      showClip: false,
    };
    this.onClick = this._onClick.bind(this);
    this.cancel = this._cancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
  }
  _onClick() {
    this.setState({
      showClip: true,
    });
  }
  // 提交图片
  _onSubmit(base64) {
    const self = this;
    return new Promise((r, j) => {
      this.props.events
        .upload(base64)
        .then(() => {
          self.setState({
            showClip: false,
          });
          r();
        })
        .catch((e) => {
          j(e);
        });
    });
  }
  // 关闭裁剪框
  _cancel() {
    this.setState({
      showClip: false,
    });
  }
  render() {
    const { size, src } = this.props;
    const reg = /http?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/; //判断图片地址是否是http
    var imgSrc = "";
    // src.url.includes("http")
    if (src) {
      if (src.url && !src.url.includes("http")) { 
        imgSrc = `${CDN}/${src.url}`;
      } else {
        imgSrc = `${src.url}`;
      }
    }
    return (
      <div className="image" onClick={this.onClick}>
        {src && src.url ? (
          <img src={imgSrc} alt="" className="view" />
        ) : (
          <div className="text">
            <p>
              <Icon type="gsg-shangchuantupian" />
              点击上传
            </p>
            <p>
              {size.w}x{size.h}
              、jpg或png
            </p>
          </div>
        )}
        {this.state.showClip ? (
          <Clip
            src={imgSrc}
            size={size}
            onCancel={this.cancel}
            onSubmit={this.onSubmit}
          />
        ) : null}
      </div>
    );
  }
}

export default Image;
