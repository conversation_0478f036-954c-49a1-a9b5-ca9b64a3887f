
import { evalHost } from './config';
import { http, filterParamsValue } from 'client/tool/axios';
import qs from 'querystring';



// 104001000101-考核任务管理查询
export const getEvalList = (params = {}) => {
  return http.get(`${evalHost}/eval/list`, filterParamsValue(params, [undefined, null, '']));
}

// 104001000103-查询考核任务详情
export const getEvalInfo = (params = {}) => {
  return http.get(`${evalHost}/eval/info`, params);
}


// 104001000104-查询考核任务执行组织执行情况
export const getEvalOrgsInfo = (params = {}) => {
  return http.get(`${evalHost}/eval/condition`, params);
}

// 104001000107-删除草稿
export const deleteEvalTask = (eval_id) => {
  return http.get(`${evalHost}/draft/delete/`, { eval_id });
}

// 104001000108-停用考核任务
export const stopEvalTask = (eval_id) => {
  return http.get(`${evalHost}/eval/stop/${eval_id}`);
}

// 104001000105-查询执行组织执行流水
export const getEvalFlow = (eval_id, org_id, is_commit = 2) => {
  return http.get(`${evalHost}/eval/flow/${eval_id}/${org_id}?is_commit=${is_commit}`);
}


// 104001000102-提交考核任务
export const addEval = (params) => {
  return http.post(`${evalHost}/eval/add`, params);
}

// 104001000109-修改考核任务
export const updateEval = (params) => {
  return http.post(`${evalHost}/eval/update`, params);
}

// 104001000106-保存草稿
export const saveEvalDraft = (params) => {
  return http.post(`${evalHost}/draft/save/`, params);
}

// 104001000107-查看草稿(二期调整)
export const getDraftSelect = (queryparams = {}) => {
  return http.get(`${evalHost}/draft/select`, queryparams);
}

// 104001000110-(二期)获取新建考核数据采集类型字典
export const getEvalCollectionOption = (queryparams = {}) => {
  return http.get(`${evalHost}/eval/collection-option`, queryparams);
}

//10400100110006-数据采集（采集完成）
export const postEvalStatisList = (queryparams = {}) => {
  return http.post(`${evalHost}/eval-statis/list`, queryparams);
}

// 104001001100502-数据采集扣分信息（采集中）
export const getViewDetail = (queryparams = {}) => {
  return http.get(`${evalHost}/data-collect-deduction/view-detail`, queryparams);
}

// 104001001100501-考核信息
export const getView = (queryparams = {}) => {
  return http.get(`${evalHost}/data-collect-deduction/view`, queryparams);
}
// 1050020201-修改分值
export const setUpdateScore = (queryparams = {}) => {
  return http.post(`${evalHost}/collect-result-modify/update`, queryparams);
}
// 1050021401-修改分值
export const setUpdateScore2 = (queryparams = {}) => {
  return http.post(`${evalHost}/collect-result-detail-modify/update`, queryparams);
}
// 1050020202-修正分值-查询
export const getUpdateScoreList = (queryparams = {}) => {
  return http.get(`${evalHost}/collect-result-modify/list`, queryparams);
}
// 1050021504-刷新本项扣分
export const reloadScore = (queryparams = {}) => {
  return http.get(`${evalHost}/collect-result-refresh/one-type`, queryparams);
}
//  1050021505-刷新任务最终扣分
export const collectResultRefresh = (queryparams = {}) => {
  return http.get(`${evalHost}/collect-result-refresh`, queryparams);
}