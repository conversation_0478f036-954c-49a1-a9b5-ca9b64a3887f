import React, { Component } from "react";
import { connect } from "dva";
import { Tree } from "antd";
import PropTypes from "prop-types";

import "./index.less";

import { getCsrf } from "client/apis/cadre-portrait";

import defaultAvatar from "../../image/default-avatar.png";
const { TreeNode } = Tree;

class LeaderGroup extends Component {
  constructor(props) {
    super(props);

    const currentOid =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";
    this.state = {
      selectKey: currentOid,
      expandKeys: [],
      autoExpandParent: true,
      activeTabKey: 0,
      isTabSwitch: false,
      // 新增
      org_id: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      userList: [],
    };
  }
  componentDidMount() {
    this.getOrgTypeList();
  }

  //树
  renderTreeNodes(data) {
    return data.map((item) => {
      const { child_org_num, org_id, name, short_name } = item;
      const { selectKey } = this.state;
      if (item.children) {
        return (
          <TreeNode
            isLeaf={child_org_num === 0}
            title={
              <div
                ref={org_id}
                title={name || short_name}
                className={
                  parseInt(selectKey) === parseInt(org_id) ? "active" : ""
                }
                onClick={this.selectTreeOrg.bind(this, org_id, name)}
              >
                {short_name || name}
              </div>
            }
            key={org_id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf={child_org_num === 0}
          title={
            <div
              ref={org_id}
              onClick={this.selectTreeOrg.bind(this, org_id, name)}
            >
              {short_name || name}
            </div>
          }
          key={org_id}
          dataRef={item}
        />
      );
    });
  }

  // 组织类型获取
  getOrgTypeList(orgId) {
    const { dispatch } = this.props;
    const { selectKey } = this.state;
    dispatch({
      type: "leaderGroup/getTreeTypeList",
      payload: {
        org_id: orgId || selectKey,
      },
    }).then(() => {
      this.loadTreeList(orgId || selectKey);
    });
  }
  // 左边树 滚动到指定位置
  expandTree(keys, params) {
    const { expanded, node } = params;
    const {
      dataRef: { org_id },
    } = node.props;
    this.setState(
      {
        expandKeys: keys,
        autoExpandParent: false,
        isTabSwitch: false,
      },
      () => {
        expanded && this.loadTreeList(org_id);
      }
    );
  }
  //组织树
  loadTreeList(org_id) {
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey, isTabSwitch } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      tree_type,
      org_type,
      org_id,
      isTabSwitch,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    dispatch({
      type: "leaderGroup/getOrgTree",
      payload,
    });
  }

  //切换树
  loadOrgUnit() {
    const { selectKey } = this.state;
    this.setState({
      org_id: selectKey,
    });
  }

  // 点击组织树
  selectTreeOrg(value, name) {
    this.setState(
      {
        selectKey: value,
        org_id: value,
      },
      () => {
        this.getCsrf(selectKey);

        this.loadOrgUnit();
      }
    );
  }

  // 滚动到指定位置
  _scrollPos(key) {
    const el = this.refs[key];
    const pE = this.refs.orgContainer;
    if (el && pE) {
      setTimeout(() => {
        pE.scrollTop = el.offsetTop - 10 - pE.offsetTop;
      }, 300);
    }
  }
  async getCsrf(currentOid) {
    const res = await getCsrf(currentOid);

    if (res.data.code === 0) {
      this.setState({
        userList: res.data.data,
      });
    }
  }
  render() {
    const { leaderGroup } = this.props;
    const { expandKeys, autoExpandParent, userList } = this.state;
    const _expandKeys =
      leaderGroup &&
      leaderGroup.treeList &&
      leaderGroup.treeList.length &&
      leaderGroup.treeList[0].children.length
        ? leaderGroup.treeList[0].org_id
        : "";

    return (
      <div className="cadre-info">
        <div className="left">
          <div className="orgContainer" ref="orgContainer">
            <Tree
              blockNode={true}
              autoExpandParent={autoExpandParent}
              onExpand={this.expandTree.bind(this)}
              expandedKeys={
                expandKeys.length ? expandKeys : [_expandKeys.toString()]
              }
            >
              {leaderGroup.treeList
                ? this.renderTreeNodes(leaderGroup.treeList)
                : null}
            </Tree>
          </div>
        </div>
        <div className="right">
          <div className="user-box">
            {userList.map((item) => (
              <div className="card">
                <div className="avatar">
                  <img src={defaultAvatar} alt="" />
                </div>
                <div className="position-box">
                  <div className="name">石海洪</div>
                  <div className="position-name">镇党委书记</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(LeaderGroup);
