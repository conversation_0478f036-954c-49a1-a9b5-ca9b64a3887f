import React, { useState, useEffect } from "react";
import SearchHeader from "components/search-header";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import {
  Button,
  Form,
  Select,
  Input,
  Table,
  Switch,
  Tooltip,
  Modal,
  TreeSelect,
  message,
} from "antd";
import {
  findByWhereResource,
  findCategoryList,
  removeResource,
  publishResource,
  findAllSources,
} from "apis/resource-library";
import { CDN } from "apis/config";
import "./index.less";

const { Item } = Form;
const { Option } = Select;
const tagsList = ["市内", "市外"];
const { root_oid, oid } = JSON.parse(sessionStorage.getItem("userInfo") || {});
const TeacherResources = (props) => {
  const {
    history,
    form: { getFieldDecorator, getFieldsValue },
  } = props;

  const [delList, setDelList] = useState([]);
  const [selOrg, setSelOrg] = useState([]);
  const [total, setTotal] = useState(0);
  const [name, setName] = useState("");
  const [keyWord, setKeyWord] = useState("");
  const [catagoryId, setCategoryId] = useState(null);
  const [categoryList, setCategoryList] = useState([]);
  const [sourcesList, setSourcesList] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [orgModalVisible, setOrgModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState({});
  const [query, setQuery] = useState({
    page: 1,
    page_size: 10,
  });

  useEffect(() => {
    _findCategoryList();
    // _findAllSources();
  }, []);

  useEffect(() => {
    initDataSource();
  }, [selOrg, query, searchQuery]);

  // 格式化分类数据，选择框只展示一二三级
  const formatData = (value) => {
    value.title = value.resource_category_name;
    value.value = value.resource_category_id;
    value.key = value.resource_category_id;
    if (value.children) {
      value.children.forEach(formatData);
    }
  };

  const initDataSource = () => {
    findByWhereResource({
      ...query,
      ...searchQuery,
      provider_org_id: selOrg[0] ? selOrg[0].org_id : null,
      resource_type: 2,
    }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      if (searchQuery.name) {
        setName(searchQuery.name);
      }
      if (searchQuery.content) {
        setKeyWord(searchQuery.content);
      }
      setTotal(res.total);
      setDataSource(res.data);
    });
  };

  // 获取来源
  // const _findAllSources = () => {
  //     findAllSources({
  //         resource_type: 2,
  //         view_type: 2,
  //         provider_org_id: selOrg.length ? selOrg[0].org_id : null,
  //     }).then(({ data: res }) => {
  //         if (res.code !== 0) {
  //             message.error(res.message);
  //             return;
  //         }
  //         setSourcesList(res.data);
  //     });
  // };

  // 获取分类
  const _findCategoryList = () => {
    findCategoryList({ category_type: 2 }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      res.data.forEach(formatData);
      setCategoryList(res.data);
    });
  };

  const onHandleDelect = (isSingle = false, id) => {
    Modal.confirm({
      title: "提示",
      content: isSingle ? "确认删除这条数据吗？" : "确认批量删除选中数据吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        removeResource(isSingle ? [id] : delList).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error("删除失败");
            return;
          }
          message.success("删除成功");
          initDataSource();
        });
      },
    });
  };

  const onHandleSubmit = () => {
    setSearchQuery({ ...searchQuery, ...getFieldsValue() });
  };

  const onHandlePublish = (row) => {
    Modal.confirm({
      title: "提示",
      content:
        row.status === 1
          ? "取消公开后信息提供单位党员可查看， 确认取消公开这条数据吗？"
          : "公开后全系统党员可查看，确认公开这条数据吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        publishResource({
          resource_library_id: row.resource_library_id,
          status: row.status === 1 ? 2 : 1,
        }).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error(`${row.status === 1 ? "取消" : ""}公开失败`);
            return;
          }
          message.success(`${row.status === 1 ? "取消" : ""}公开成功`);
          initDataSource();
        });
      },
    });
  };

  const onHandleChangeStatus = (checked, row) => {
    publishShowStatus({
      resource_library_id: row.resource_library_id,
      show_status: !checked ? 0 : 1, //1显示 0隐藏
    }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(`${!checked ? "隐藏" : "显示"}失败`);
        return;
      }
      message.success(`${!checked ? "隐藏" : "显示"}成功`);
    });
  };

  const orgModalProps = {
    visible: orgModalVisible,
    dataSource: selOrg,
    hideModal: () => setOrgModalVisible(false),
    radio: true,
    hasRoot: false,
    onlyDirectSub: true,
    rootOrgId: oid,
    loadOrganizeData: (data) => {
      setOrgModalVisible(false);
      setSelOrg(data);
    },
  };

  const columns = [
    {
      title: "照片",
      dataIndex: "cover_url",
      key: "cover_url",
      align: "center",
      width: 91,
      render: (text) => {
        return (
          <img
            className="teacher-resources-table-title"
            src={text ? `${CDN}/${text}` : require("../../image/ren.png")}
          />
        );
      },
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (text) => {
        return <div style={{ float: "left", textAlign: "left" }}>{text}</div>;
      },
    },
    {
      title: "师资分类",
      dataIndex: "resource_category_name",
      key: "resource_category_name",
      align: "center",
      render: (text) => {
        return <div style={{ float: "left", textAlign: "left" }}>{text}</div>;
      },
    },
    {
      title: "所在单位",
      dataIndex: "belong_unit",
      key: "belong_unit",
      align: "center",
      render: (text) => {
        return <div style={{ float: "left", textAlign: "left" }}>{text}</div>;
      },
    },
    {
      title: "联系方式",
      dataIndex: "contact_details",
      key: "contact_details",
      align: "center",
      render: (text) => {
        return <div style={{ float: "left", textAlign: "left" }}>{text}</div>;
      },
    },
    {
      title: "擅长领域",
      dataIndex: "tags",
      key: "tags",
      width: 200,
      align: "center",
      render: (text) => {
        // console.log(text.split(','));
        return (
          <div className="suit">
            {text &&
              text.split(",").map((item) => {
                return <span>{item}</span>;
              })}
          </div>
        );
      },
    },
    {
      title: "排序",
      dataIndex: "seq",
      key: "seq",
      align: "center",
      render: (text) => text || "-",
    },
    {
      title: "上传时间",
      dataIndex: "create_time",
      key: "create_time",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operate",
      width: "20%",
      key: "operate",
      align: "center",
      render: (text, row) => {
        console.log(row);
        return (
          <div>
            <Button type="link" onClick={() => onHandlePublish(row)}>
              {row.status === 1 ? "取消公开" : "公开"}
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push("/teacher-resources/details", {
                  // ...row,
                  resource_library_id: row.resource_library_id,
                  resource_type: 1,
                  name,
                  keyWord,
                })
              }
            >
              详情
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push("/teacher-resources/add", {
                  categoryList,
                  type: 2,
                  resource_library_id: row.resource_library_id,
                })
              }
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => onHandleDelect(true, row.resource_library_id)}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];

  const pagination = {
    total,
    current: query.page,
    pageSize: query.page_size,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100", "200"],
    showTotal: ($total, range) =>
      `总计${$total}条，当前展示第${range[0]}至${range[range.length - 1]}条`,
    onChange: (page) => setQuery({ ...query, page }),
    onShowSizeChange: (_, page_size) => setQuery({ ...query, page_size }),
  };

  const tableProps = {
    columns,
    dataSource,
    pagination,
    rowKey: "resource_library_id",
    bordered: true,
    // rowSelection: {
    //     onChange: (selectedRowKeys) => {
    //         setDelList(selectedRowKeys);
    //     },
    // },
  };

  return (
    <div className="teacher-resources">
      <SearchHeader
        onBack={false}
        title={"师资库管理"}
        renderRight={() => {
          return (
            <div className="teacher-resources-header-button">
              <Button
                type="default"
                className="upload-btn"
                icon="plus"
                onClick={() =>
                  history.push("/teacher-resources/add", {
                    categoryList,
                    type: 1,
                  })
                }
              >
                新增资源
              </Button>
            </div>
          );
        }}
      />
      <div className="teacher-resources-content">
        <div className="teacher-resources-content-search">
          {root_oid === oid && (
            <div className="teacher-resources-content-search-org">
              信息提供：{selOrg[0] ? selOrg[0].org_name : ""}
              <Button type="link" onClick={() => setOrgModalVisible(true)}>
                选择组织
              </Button>
              <OrganizeModal {...orgModalProps} />
            </div>
          )}
          <Form
            layout="inline"
            className="teacher-resources-content-search-form"
          >
            <Item label="姓名">
              {getFieldDecorator("name", {
                initialValue: null,
              })(
                <Input
                  allowClear
                  placeholder="请输入姓名关键词"
                  maxLength={150}
                />
              )}
            </Item>
            <Item label="简介">
              {getFieldDecorator("content", {
                initialValue: null,
              })(
                <Input
                  allowClear
                  placeholder="请输入简介关键词"
                  maxLength={150}
                />
              )}
            </Item>
            <Item label="师资分类">
              {getFieldDecorator("resource_category_id", {
                initialValue: catagoryId,
              })(
                <TreeSelect
                  allowClear
                  style={{ width: "100%" }}
                  dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                  treeData={categoryList}
                  placeholder="请选择师资分类"
                  treeDefaultExpandAll
                  onChange={(val) => setCategoryId(val)}
                />
              )}
            </Item>
            <Item label="擅长领域">
              {getFieldDecorator("tags", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择擅长领域" mode="multiple" allowClear>
                  {tagsList.map((item, index) => {
                    return (
                      <Option value={item} key={index}>
                        {item}
                      </Option>
                    );
                  })}
                </Select>
              )}
            </Item>
            <Item label="排序规则">
              {getFieldDecorator("order_type", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择排序规则" allowClear>
                  <Option value={1}>上传时间正序</Option>
                  <Option value={2}>上传时间倒序</Option>
                  <Option value={3}>排序值</Option>
                </Select>
              )}
            </Item>
            <Item label="公开状态">
              {getFieldDecorator("status", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择状态" allowClear>
                  <Option value={1}>已公开</Option>
                  <Option value={2}>未公开</Option>
                </Select>
              )}
            </Item>
            <Button type="primary" onClick={() => onHandleSubmit()}>
              查询
            </Button>
          </Form>
        </div>
        <div className="teacher-resources-content-table">
          <Table {...tableProps} />
        </div>
      </div>
    </div>
  );
};

export default Form.create()(TeacherResources);
