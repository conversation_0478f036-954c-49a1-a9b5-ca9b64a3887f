import React, { Component } from "react";
import { Layout, Menu, Icon, message } from "antd";
import SelfIcon from "components/self-icon";
import { headers } from "client/tool/axios";
import { Base64 } from "client/tool/util";
import { connect } from "dva";
// import mapMenus from 'config/map-menu';
import { menus } from "config/map-menu";
// 处理菜单从list转为tree的方法
import { parseMenuListToMenuTree, cookie } from "tool/util";
import "./index.less";

const mapMenu = menus,
  SubMenu = Menu.SubMenu,
  // MenuItemGroup = Menu.ItemGroup,
  { Sider } = Layout;

/**
 * 渲染菜单栏
 * <AUTHOR>
 * @param { Array }    menus            按钮数组
 * @param { Boolean }    collapsed        是否收起
 * @param { Function }  onCollapse        点击切换是否收起的按钮
 * @return {XML}
 */
class Menus extends Component {
  constructor(props) {
    super(props);
    this.state = {
      collapsed: true,
      menus: [],
      OpenKeys: [],
    };
  }
  componentDidMount() {
    // console.dir(this.props.userInfo.menu);
    this.initMenus();
  }
  /* componentDidUpdate (prevProps) {
    if (this.props.userInfo.menu && (prevProps.userInfo.menu.length !== this.props.userInfo.menu.length) ) {
      this.initMenus()
    }
  } */
  initMenus() {
    const { history } = this.props;
    let tmp_menu = [];
    try {
      tmp_menu = JSON.parse(JSON.stringify(this.props.userInfo.menu));
    } catch (err) {
      tmp_menu = [];
    }
    const menus = parseMenuListToMenuTree(tmp_menu);
    this.setState(
      {
        menus,
        OpenKeys: [],
      },
      () => {
        // 侧边栏展开
        this.state.menus &&
          this.state.menus.forEach((x) => {
            if (x && x.path === history.location.pathname) {
              this.setState({
                OpenKeys: [x.id],
              });
            }
            x.children &&
              Array.isArray(x.children) &&
              x.children.forEach((v) => {
                if (v && v.path === history.location.pathname) {
                  this.setState({
                    OpenKeys: [x.id],
                  });
                }
              });
          });
      }
    );
  }
  onOpenChange(openkeys) {
    this.setState({
      OpenKeys: openkeys,
    });
  }
  render() {
    const { menus } = this.state;
    const { history, onCollapse, dispatch, collapsed, userInfo } = this.props;
    const { systemName } = userInfo || {};

    // let a = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
    // delete a.menu
    // delete a.element
    // const userInfoStr = encodeURIComponent(Base64.encode(JSON.stringify(JSON.stringify(a))))
    // console.log(userInfoStr)

    const itemMenu = function (val) {
      const but = mapMenu[val.id];
      return (
        <Menu.Item key={val.path} menuId={val.id}>
          {but["icon"] && (
            <SelfIcon
              type={but["icon"]}
              style={{ fontSize: 22, position: "relative", top: 3 }}
            />
          )}
          <span>{val.name}</span>
        </Menu.Item>
      );
    },
      goHref = ({ item, keyPath }) => {
        const { menuId: key } = item.props
        // 路由白名单
        const routeArr = [];
        if (routeArr.indexOf(key) !== -1) {
          history.push(key);
        } else {
          let path = "",
            link = "";
          Object.keys(mapMenu).some((itemId) => {
            // 如果是跳外部链接
            if (itemId === key && mapMenu[itemId].link) {
              link = mapMenu[itemId].link;
              const headerStr = encodeURIComponent(
                Base64.encode(JSON.stringify(headers()))
              );

              let a = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
              delete a.menu;
              delete a.element;
              const userInfoStr = encodeURIComponent(
                Base64.encode(JSON.stringify(JSON.stringify(a)))
              );
              const url = mapMenu[itemId].link;

              if (itemId == 68) {
                typeof window !== "undefined" &&
                  window.open(
                    url +
                    `${url.indexOf("?") != -1 ? "&" : "?"}h=${userInfoStr}`
                  );
              } else {
                typeof window !== "undefined" &&
                  window.open(
                    url + `${url.indexOf("?") != -1 ? "&" : "?"}_h=${headerStr}`
                  );
              }
              return true;
            }
            if (mapMenu[itemId] && mapMenu[itemId].id == key) {
              path = mapMenu[itemId].path;
              if (typeof window !== "undefined") {
                window.sessionStorage.setItem("_menu_id", itemId);
                cookie.set("_menu_id", mapMenu[itemId].id);
              }
              return true;
            }
          });
          if (path) {
            const menuList = this.props.userInfo.menu;
            dispatch({
              type: "secondaryDirectory/load",
              payload: { menuList, path },
            });
            history.push(path);
          }
          // 当path 和 link 都不存在时,跳转到敬请期待
          else if (!link) {
            history.push("/expect");
            // message.error("功能开发中，敬请期待...");
            // message.error("菜单未配置");
          }
        }
      };

    // 传入子菜单和是否有图标选项，默认没有图标
    const renderSubmenu = (submenu, haveIcon = false, level = 1) => {
      const path = mapMenu[submenu.id]
        ? mapMenu[submenu.id].path || submenu.id
        : submenu.id;
      if (
        submenu.children &&
        Array.isArray(submenu.children) &&
        submenu.children.length !== 0
      ) {
        if (mapMenu[submenu.id]) {
          return (
            <SubMenu
              key={path}
              menuId={submenu.id}
              className={`menuLevel${level}`}
              title={
                <div className={"menu-title-wrapper"} dataref={submenu}>
                  {/* {haveIcon && (
                    <SelfIcon
                      type={mapMenu[submenu.id]["icon"] || "file"}
                      style={{ fontSize: 22 }}
                    />
                  )} */}
                  <span>{submenu.name}</span>
                </div>
              }
            >
              {submenu.children &&
                Array.isArray(submenu.children) &&
                submenu.children.length !== 0
                ? submenu.children.map((child) => {
                  if (
                    child.children &&
                    Array.isArray(child.children) &&
                    child.children.length !== 0
                  ) {
                    return renderSubmenu(child, false, level + 1);
                  } else {
                    return itemMenu(child);
                  }
                })
                : itemMenu(submenu)}
            </SubMenu>
          );
        } else {
          return null;
        }
      } else {
        return itemMenu(submenu);
      }
    };
    // console.log(menus);
    return (
      <Sider
        className="sider-main"
        trigger={null}
        collapsible
        style={{
          overflow: "auto",
          height: "100vh",
          position: "fixed",
          left: 0,
        }}
        collapsed={collapsed}
      >
        <div className="logo">
          {!collapsed && <span className="logo-name">{systemName}</span>}
          {/* <span
            className={collapsed ? "icon" : "unicon"}
            onClick={() => onCollapse()}
          >
            <Icon
              type={collapsed ? "menu-unfold" : "menu-fold"}
              style={{ color: "#778296", fontSize: 32 }}
            />
          </span> */}
          <div className="hr" />
        </div>
        <Menu
          theme="dark"
          defaultSelectedKeys={[history.location.pathname]}
          openKeys={this.state.OpenKeys}
          // defaultOpenKeys={this.state.OpenKeys}
          selectedKeys={[
            history.location.pathname == "/edit-news"
              ? "/news-list"
              : history.location.pathname,
            history.location.pathname == "/party-edit"
              ? "/party-setting"
              : history.location.pathname,
          ]}
          onClick={goHref}
          onOpenChange={this.onOpenChange.bind(this)}
          mode="inline"
          // inlineCollapsed={collapsed}
          inlineIndent={22}
        >
          {menus &&
            Array.isArray(menus) &&
            menus.length !== 0 &&
            menus.map((menu) => {
              return renderSubmenu(menu, true);
            })}
        </Menu>
      </Sider>
    );
  }
}

const mapStateToProps = ({ userInfo }) => ({ userInfo });
export default connect(mapStateToProps)(Menus);
