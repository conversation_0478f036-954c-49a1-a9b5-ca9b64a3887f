import { useState, useEffect, useMemo } from "react";
import { Form, TimePicker, message, Row, Icon, Col, InputNumber, Checkbox, Button, Input } from "antd";
import DateMultiPicker from "components/DateMultiPicker";
import {
  getResourceAppointment,
  setResourceAppointment,
} from "apis/resource-library";
import moment from "moment";

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 15 },
};
const layout2 = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};

const options = [
  { label: "周一", value: 1 },
  { label: "周二", value: 2 },
  { label: "周三", value: 3 },
  { label: "周四", value: 4 },
  { label: "周五", value: 5 },
  { label: "周六", value: 6 },
  { label: "周日", value: 7 },
];
let initKey = 1;

const Setting = (props) => {
  const { form, state = {} } = props
  const { getFieldDecorator, getFieldValue, validateFields, setFieldsValue } = form
  const { resource_library_id } = state;
  const [loading, setLoading] = useState(false);
  const [settingId, setSettingId] = useState();
  const [appointmentDate, setAppointmentDate] = useState([])
  const [keys, setKeys] = useState([0])

  useEffect(() => {
    getFieldDecorator('keys', { initialValue: [0] });
    resource_library_id &&
      getResourceAppointment(resource_library_id).then(({ data: res }) => {
        if (res.code === 0) {
          const { data = {} } = res;
          const { not_appointment_date, open_days, appointment_date = [] } = data
          let appointmentDate = []
          if (appointment_date.length) {
            appointmentDate = appointment_date.map((item) => {
              item.start_time = moment(item.start_time, "HH:mm")
              item.end_time = moment(item.end_time, "HH:mm")
              return item;
            })
          } else {
            appointmentDate = [{ week: [1, 2, 3, 4, 5, 6, 7] }];
          }
          form.setFieldsValue({
            not_appointment_date,
            open_days
          });
          setSettingId(data.resource_library_setting_id);
          setAppointmentDate(appointmentDate)
          setKeys(Array.from(appointment_date.keys()))
        }
      });
  }, []);

  const onSubmit = (e) => {
    e.preventDefault();
    validateFields((error, values) => {
      if (error) { return }
      const { start_time, end_time, week, ...other } = values
      const appointment_date = []
      week.map((item, key) => {
        appointment_date.push({
          week: item,
          start_time: start_time[key].format('HH:mm'),
          end_time: end_time[key].format('HH:mm')
        })

      })
      const params = {
        ...other,
        appointment_date,
        resource_library_id,
        resource_library_setting_id: settingId,
      };
      setLoading(true);
      setResourceAppointment(params).then(({ data: res }) => {
        setLoading(false);
        if (res.code === 0) {
          message.success("设置成功")
        } else {
          message.error("设置失败，请重试")
        }
      });
    })
  };


  const add = () => {
    const nextKeys = keys.concat(initKey++);
    setKeys(nextKeys)
  };
  const remove = (k) => {
    setKeys(keys.filter(key => key !== k))
  }

  const formItems = keys.map((k, index) => {
    const appointment_date = appointmentDate[k] || {}
    return <Row key={k}>
      <Form.Item className="week-wrap">
        {getFieldDecorator(`week[${k}]`, {
          initialValue: appointment_date.week || null,
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        })(
          <Checkbox.Group options={options} />
        )}
        {index !== 0 && <Form.Item>
          <Icon className="close-btn" type="close-circle" onClick={() => remove(k)} />
        </Form.Item>}
      </Form.Item>
      <Row>
        <Col span={3} style={{ minWidth: 140 }}>
          <Form.Item>
            {getFieldDecorator(`start_time[${k}]`, {
              initialValue: appointment_date.start_time || null,
              rules: [
                {
                  required: true,
                  message: '请选择开始时间',
                }
              ],
            })(<TimePicker style={{ width: 120 }} format="HH:mm" />)}
          </Form.Item>
        </Col>
        <Col span={1}>至</Col>
        <Col span={6}>
          <Form.Item>
            {getFieldDecorator(`end_time[${k}]`, {
              initialValue: appointment_date.end_time || null,
              rules: [
                {
                  required: true,
                  message: '请选择结束时间',
                }
              ],
            })(<TimePicker style={{ width: 120 }} format="HH:mm" />)}
          </Form.Item>
        </Col>
      </Row>
    </Row>
  })

  return (
    <Form
      className="AppointmentManagement-form"
      {...layout}
      onSubmit={onSubmit}
    >
      <Form.Item label="开放预约时间"  >
        {getFieldDecorator('open_days', {
          rules: [
            {
              required: true,
              message: '请填写开放预约时间',
            },
          ],
        })(
          <InputNumber size="large" precision={0} min={1} placeholder="请填写" />
        )}
      </Form.Item>
      <Form.Item
        label="不可预约时间"
      >
        {getFieldDecorator('not_appointment_date', {
          rules: [
            {
              required: true,
              message: '请选择时间',
            },
          ],
        })(
          <DateMultiPicker />
        )}
      </Form.Item>
      <Form.Item label="可预约时间" required>
        {formItems}
        <Form.Item wrapperCol={{ offset: 0 }}>
          <Button
            type="dashed"
            onClick={() => add()}
            block
            icon="plus"
            style={{ width: "150px" }}
          >
            添加时间段
          </Button>
        </Form.Item>
      </Form.Item>
      <Form.Item wrapperCol={{ offset: 3 }}>
        <Button
          loading={loading}
          disabled={loading}
          type="primary"
          htmlType="submit"
          style={{ width: "300px" }}
        >
          保存
        </Button>
      </Form.Item>
    </Form>
  );
};
export default Form.create()(Setting);
