import { Col, Form, Input, Row, Select, DataPicker, DatePicker } from "antd";
import DateSingle from "components/date-single/DateSingle";
import moment from "moment";
import { forwardRef } from "react";

const FormRow = ({ eduOption = [], degreeOption = [], form, data, disabled, onBlur }) => {
  const { getFieldDecorator, setFieldsValue } = form;

  const _onBlur = () => {
    onBlur && onBlur();
  };

  return (
    <Form>
      <Row span={24} type="flex" align="middle" justify="space-around">
        {getFieldDecorator("pms_edu_resume_id", {
          initialValue: data.pms_edu_resume_id,
        })(<input type="hidden" disabled={disabled} />)}

        <Col span={3}>
          <div className="form-col">
            <div className="form-label">开始时间</div>
            <div className="form-content">
              {getFieldDecorator("start_time", {
                initialValue: data.start_time ? moment(data.start_time) : undefined,
                onChange: (val) => {
                  setFieldsValue({ start_time: val });
                  _onBlur();
                },
              })(
                // <DatePicker placeholder="请选择" disabled={disabled} />
                <DateSingle placeholder="请选择月份" type="month" disabled={disabled} isWrap={true} />
              )}
            </div>
          </div>
        </Col>
        <Col>
          <div className="driver">-</div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">结束时间</div>
            <div className="form-content">
              {getFieldDecorator("end_time", {
                initialValue: data.end_time ? moment(data.end_time) : undefined,
                onChange: (val) => {
                  setFieldsValue({ end_time: val });
                  _onBlur();
                },
              })(
                // <DatePicker placeholder="请选择" disabled={disabled} />
                <DateSingle placeholder="请选择月份" type="month" disabled={disabled} isWrap={true} />
              )}
            </div>
          </div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">学校</div>
            <div className="form-content">
              {getFieldDecorator("school", {
                initialValue: data.school,
              })(
                <Input
                  onBlur={_onBlur}
                  placeholder="请输入"
                  className="form-input"
                  disabled={disabled}
                />
              )}
            </div>
          </div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">院系</div>
            <div className="form-content">
              {getFieldDecorator("department", {
                initialValue: data.department,
              })(
                <Input
                  onBlur={_onBlur}
                  placeholder="请输入"
                  className="form-input"
                  disabled={disabled}
                />
              )}
            </div>
          </div>
        </Col>
        <Col span={2}>
          <div className="form-col">
            <div className="form-label">专业</div>
            <div className="form-content">
              {getFieldDecorator("specialty", {
                initialValue: data.specialty,
              })(
                <Input
                  onBlur={_onBlur}
                  placeholder="请输入"
                  className="form-input"
                  disabled={disabled}
                />
              )}
            </div>
          </div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">学历</div>
            <div className="form-content">
              {getFieldDecorator("diploma", {
                initialValue: data.diploma,
              })(
                <Select
                  placeholder="请选择"
                  disabled={disabled}
                  onBlur={_onBlur}
                  allowClear
                >
                  {eduOption.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              )}
            </div>
          </div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">学位</div>
            <div className="form-content">
              {getFieldDecorator("degree", {
                initialValue: data.degree,
              })(
                <Select
                  placeholder="请选择"
                  disabled={disabled}
                  onBlur={_onBlur}
                  allowClear
                >
                  {degreeOption.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              )}
            </div>
          </div>
        </Col>
        <Col span={3}>
          <div className="form-col">
            <div className="form-label">备注说明</div>
            <div className="form-content">
              {getFieldDecorator("remark", {
                initialValue: data.remark,
              })(<Input disabled={disabled} onBlur={_onBlur} />)}
            </div>
          </div>
        </Col>
      </Row>
    </Form>
  );
};

export default Form.create()(forwardRef(FormRow));
