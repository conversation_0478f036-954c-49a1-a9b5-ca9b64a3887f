<html>
  <head>
    <meta charset="UTF-8" />
    <!-- 建议禁用外框浏览器自带的缩放 -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,user-scalable=no"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title></title>
    <style>
      * {
        box-sizing: border-box;
      }
      html,
      body {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
        /* 防止双击缩放 */
        touch-action: manipulation;
      }
      .main {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }
      #aliyunPreview {
        flex: 1;
      }
    </style>
    <script type="text/javascript" charset="utf-8">
      var url =
        "https://ows-test.obs.cn-north-4.myhuaweicloud.com/" +
        window.parent.target_url;
      function json2str(obj) {
        return JSON.stringify(obj, function(key, val) {
          if (typeof val === "function") {
            val = val.toString();
          }
          return val;
        });
      }
      window.sendMessage = function(action, data) {
        var iframe = document.getElementById("aliyunPreview");
        iframe.contentWindow.postMessage(
          json2str({ action: action, data: data }),
          "*"
        );
      };
      window.addEventListener(
        "message",
        function(e) {
          try {
            var res = JSON.parse(e.data);
          } catch (err) {
            return;
          }
          switch (res.action) {
            case "preview.ready":
              window.sendMessage("preview.init", {
                url: url
                // region: 'oss-cn-beijing',
                // bucket: 'xuliduo',
                // accessKeyId: 'STS.NHgnREtHnQoFhLeWqpZ6yjdvL',
                // accessKeySecret: 'CzGqsAryR65GAUS5aBoynRfdwySTN5f2pAB36CWqDb5N',
                // stsToken: 'CAIShAJ1q6Ft5B2yfSjIr4vSJejxmZdP5q2tamrUs3ElVrlVhaHdrjz2IHlFdXltB+kfvv8znGBU7PcYlqNyTpoAQkLKbMB9tmGVFoVdJtivgde8yJBZoq/HcDHhNnyW9cvWZPqDO7G5U/yxalfCuzZuyL/hD1uLVECkNpv75vwKac8MDEvCLlggPtpNIRZ4o8I3LGbYMe3XUiTnmW3NFkFlyG0e7Gp08va42dbOqEic3h/YsrZF/9ygesX6MZUwYc0vDYiPsbYoJvab4kl58ANX8ap6tqtA9Arcs8uVa1sruEzXYrqJrIQ2fFQlPvhmQf8etpv7juYkqvHXlojqzAam5xSOm/EuLxqAAYcwiEvAPCWFevosxFWX2acgN1Bsdfi0RQ/A0D9w0ta9B6GJdFWirCWTJMXJ8clghD+jAkBy765j8nO0/TkUcNm14W6NxBLusZK07tEvr+4YaxgNUL+g9h56JyrGf3WElE10P9eYt06F0QwZcIqu3/eHOyT/jDDn+qUyiP5HPFoi'
              });
              break;
          }
        },
        false
      );
      //禁止双指缩放手势
      document.addEventListener("gesturestart", function(e) {
        e.preventDefault();
      });
    </script>
  </head>
  <body>
    <iframe
      class="main"
      allowfullscreen
      id="aliyunPreview"
      frameborder="0"
      src="https://preview.imm.aliyun.com/index.html"
    ></iframe>
  </body>
</html>
