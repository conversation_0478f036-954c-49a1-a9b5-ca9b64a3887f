import React from "react";
import emuns from "config/emuns";
import formFields from "config/formFields";

import { Form, message } from "antd";
import {
  ActivityIntroduce
} from "components/activity-form"; //活动表单

import propTypes from "prop-types";

class Introduce extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  //编辑器表单处理
  editorHandle() {
    const { form, updateState } = this.props;
    const { setFieldsValue } = form;
    const receiver = (target, content) => {
      // console.log(target, content);
      setFieldsValue({ [target]: content });
      updateState({
        [target]: content
      });
    };

    //通用介绍详情
    const commonIntroductionReceiver = content => {
      receiver(formFields.commonIntroduction, content);
    };

    //web介绍详情
    const webIntroductionReceiver = content => {
      receiver(formFields.webIntroduction, content);
    };

    //app介绍详情
    const appIntroductionReceiver = content => {
      receiver(formFields.appIntroduction, content);
    };

    return {
      commonIntroductionReceiver,
      webIntroductionReceiver,
      appIntroductionReceiver
    };
  }

  render() {
    // console.log(this.props);
    const { form, isPreview, state } = this.props;
    return (
      <ActivityIntroduce
        form={form}
        preview={isPreview}
        formFields={formFields}
        activityFormState={state}
        handleEvents={{
          ...this.editorHandle.bind(this)()
        }}
      />
    )
  }
}

Introduce.propTypes = {
  isPreview: propTypes.bool,
  data: propTypes.object.isRequired,
  state: propTypes.object,
  updateState: propTypes.func,
};

Introduce.defaultProps = {
  isPreview: false,
  data: {},
  state: {},
  updateState: () => { },
};

export default Form.create()(Introduce);