.data-initialization {
  padding: 20px;
  .grid-container {
    display: grid;
    gap: 24px 30px;
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-item {
    height: 198px;
    padding: 0px 54px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f9f9f9;
    border-radius: 8px;
    text-align: center;
    cursor: not-allowed;
    background: url("./images/bg.png") no-repeat center center / 100% 100%;
  }
  .pointer {
    cursor: pointer;
  }

  .icon {
    width: 110px;
    height: 110px;
    flex-shrink: 0;
  }
  .title {
    font-size: 32px;
    font-weight: bold;
    font-family: Source <PERSON>, Source Han Sans CN;
    font-weight: bold;
    color: #222222;
    line-height: 32px;
    text-align: left;
    font-style: normal;
    user-select: none;
  }
  .instructions {
    padding: 33px 43px;
    margin-top: 20px;

    .instructions-title {
      font-size: 20px;
      font-weight: bold;
      font-family: Source <PERSON>, Source <PERSON>;
      font-weight: bold;
      color: #222222;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
    .desc-list {
      margin-top: 16px;
      font-family: Source <PERSON>, Source <PERSON>s CN;
      font-weight: 400;
      font-size: 18px;
      color: #9a9a9a;
      line-height: 50px;
    }
  }
}
.icon-organization {
  background: url("./images/organization.png") no-repeat center center / 100%
    100%;
}
.icon-approval {
  background: url("./images/approval.png") no-repeat center center / 100% 100%;
}
.icon-roster {
  background: url("./images/roster.png") no-repeat center center / 100% 100%;
}
.icon-evaluation {
  background: url("./images/evaluation.png") no-repeat center center / 100% 100%;
}
.icon-info {
  background: url("./images/info.png") no-repeat center center / 100% 100%;
}
.icon-relationship {
  background: url("./images/relationship.png") no-repeat center center / 100%
    100%;
}
.icon-area {
  background: url("./images/area.png") no-repeat center center / 100% 100%;
}
.icon-bz {
  background: url("./images/banzi.png") no-repeat center center / 100% 100%;
}

.icon-nd {
  background: url("./images/niandu.png") no-repeat center center / 100% 100%;
}

.icon-zq {
  background: url("./images/zhengqiu.png") no-repeat center center / 100% 100%;
}
