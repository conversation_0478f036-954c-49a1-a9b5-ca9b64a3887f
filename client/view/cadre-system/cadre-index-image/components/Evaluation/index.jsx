import React, { useState, useEffect, useMemo } from "react";
import Card from "../Card";
import ReactECharts from "echarts-for-react";
import DataTable from "../Table";
import { Table } from "antd";
import "./index.less";

function decreaseOpacity(color, amount) {
  // 解析颜色值
  const isHex = color.indexOf("#") === 0;
  const isRgb = color.indexOf("rgb") === 0;

  let r, g, b, a;

  if (isHex) {
    color = color.slice(1); // 移除 #
    r = parseInt(color.substr(0, 2), 16);
    g = parseInt(color.substr(2, 2), 16);
    b = parseInt(color.substr(4, 2), 16);
    a = 1; // 默认不透明
  } else if (isRgb) {
    const rgba = color
      .substring(color.indexOf("(") + 1, color.lastIndexOf(")"))
      .split(",");
    r = parseInt(rgba[0].trim());
    g = parseInt(rgba[1].trim());
    b = parseInt(rgba[2].trim());
    a = parseFloat(rgba[3].trim());
  } else {
    r = g = b = a = 0;
  }

  // 降低透明度
  a = Math.max(0, Math.min(1, a - amount));

  return "rgba(" + r + ", " + g + ", " + b + ", " + a + ")";
}
const EvaluationComparison = ({ data, user_id }) => {
  const [coorMenuSelected, setMenuSelected] = useState(2);

  const [apiData, setApiData] = useState({
    line: [],
    table: [],
    data: [],
    xlabel: [],
    source: [],
  });

  const [option, setOption] = useState({});
  const [dataSource, setDataSource] = useState([]);
  const [columns, setColumns] = useState([]);
  const [dataMapSource, setDataMapSource] = useState([]);

  const colorList = [
    "#FF6C47",
    "#6AAEFB",
    "#6ADDE4",
    "#CBADFF",
    "#F06292",
    "#AED581",
    "#66BB6A",
    "#E6EE9C",
    "#BF360C",
    "#9FA8DA",
    "#80CBC4",
    "#9CCC65",
    "#03A9F4",
    "#00BCD4",
    "#A1887F",
    "#CDAD00",
    "#32CD32",
    "#CD5B45",
    "#A020F0",
    "#BDB76B",
  ];

  const createData = (origin_data, title) => {
    const data = { name: title };
    for (let i = 0; i < origin_data.length; i++) {
      data[`name${i + 1}`] =
        typeof origin_data[i] === "number"
          ? origin_data[i].toFixed(2)
          : origin_data[i];
    }
    return data;
  };

  const initOption = ({ XName, dataMap }) => {
    const datas = [];
    const lengendSelect = {};
    let isAvgFirst = false;

    dataMap.forEach((item, index) => {
      const { list, name, user_id: _user_id } = item;
      if (!list) return;

      const symbolSize = 8;
      const lineWidth = 2;
      const color = colorList[index] || "#fff";
      const [_, ..._list] = list;

      datas.push({
        symbolSize: symbolSize,
        symbol: "circle",
        name: name,
        type: "line",
        data: _list,
        color: color,
        smooth: false,
        itemStyle: {
          borderWidth: 4,
          borderColor: color,
          color: "#fff",
          shadowColor: decreaseOpacity(color, 0.5),
          shadowBlur: 13,
        },
        lineStyle: {
          color: color,
          type: "solid",
          width: lineWidth,
        },
      });
    });

    setOption({
      grid: {
        left: "1%",
        top: datas.length > 11 ? "23%" : "13%",
        bottom: "0%",
        right: "3%",
        containLabel: true,
      },
      legend: {
        show: true,
        top: 0,
        left: "center",
        icon: "circle",
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: "#333333",
          fontSize: 18,
        },
        itemStyle: {
          borderWidth: 4,
        },
        itemGap: 28,
      },
      yAxis: [
        {
          type: "value",
          position: "left",
          min: (value) => value.min - 1,
          max: (value) => value.max + 1,
          nameTextStyle: {
            color: "#00FFFF",
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#EEEEEE",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#333333",
            },
            symbol: ["none", "arrow"],
            symbolOffset: 7,
            symbolSize: [7, 10],
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#666666",
            fontSize: 16,
            formatter: (value) => {
              value = Number(value);
              return value > 100 ? "" : value.toFixed(2);
            },
          },
        },
      ],
      xAxis: {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#333333",
          },
          symbol: ["none", "arrow"],
          symbolOffset: 7,
          symbolSize: [7, 10],
          onZero: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#666666",
            fontWeight: "normal",
            fontSize: 16,
            lineHeight: 22,
          },
          interval: 0,
        },
        data: XName,
      },
      series: datas,
    });
  };

  const dynamicTableHeader = (list, title = "") => {
    const columns = [];

    list.forEach((item, index) => {
      const key = "name";
      if (index === 0) {
        columns[index] = {
          key,
          dataIndex: key,
          title: title,
          width: "10%",
          align: "center",
        };
      }
      columns[index + 1] = {
        key: `${key}${index + 1}`,
        dataIndex: `${key}${index + 1}`,
        title: item,
        width: `${100 / (list.length + 1)}%`,
        align: "center",
        showMax: true,
        showMin: true,
      };

      if (item === "政治三力") {
        columns[index + 1].customization = "CustomBlock";
      }
    });
    return columns;
  };

  const formmaterData = (data) => {
    const { my_result = {}, avg_sequence_list = {}, xlabel } = data;

    const { list: my_result_list = [], name: resultName } = my_result;
    const { list: avg_sequence_list_list = [], name: avgName } =
      avg_sequence_list;

    setApiData((prev) => ({
      ...prev,
      xlabel,
    }));

    const format = (list, name) => {
      const dataMap = {};
      return list.map((item, index) => {
        if (index === 0) {
          dataMap[index] = name;
        }
        dataMap[index + 1] = item;
      });
    };

    setColumns(dynamicTableHeader(xlabel, "干部姓名"));

    setDataSource([
      createData(my_result_list, resultName),
      createData(avg_sequence_list_list, avgName),
    ]);

    const [, ..._xlabel] = xlabel;
    const _data = [avg_sequence_list, my_result];

    initOption({
      XName: _xlabel,
      dataMap: _data,
    });
  };

  // 菜单配置
  const menuComputed = useMemo(() => {
    const menu = [
      // { label: '本单位', key: 1 },
      { label: "同序列", key: 2 },
      // { label: '乡镇/部门', key: 3 },
      // { label: '全县', key: 0 },
    ];

    return menu;
  }, []);

  useEffect(() => {
    if (data) {
      formmaterData(data);
    }
  }, [data]);

  return (

    <div className={"box-content h-300"}>
      <ReactECharts option={option} style={{ height: "100%" }} />
    </div>

  );
};

export default EvaluationComparison;
