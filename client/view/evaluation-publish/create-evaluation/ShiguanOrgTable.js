import { message } from "antd";
import { EditableFormRow, EditableCell } from "./components/EditableRow";
import NewOrg from "./components/NewOrg";

export default function ShiguanOrgTable(props) {
  const { record, index, onUpdateData } = props;

  const formRef = React.useRef(null);
  const [visible, setVisible] = React.useState(false);

  const handleDelete = (org) => {
    const { org_id } = org;
    if (!org_id) return;
    const newData = record.data.filter((item) => item.org_id != org_id);
    onUpdateData({ ...record, data: newData }, index);
  };

  const handleNumChange = (value) => {
    const newData = { ...record, num: value };
    onUpdateData(newData, index);
  };

  const handleAdd = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        const maxSeq = record.data.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 1);
        }, 1);
        const dataSource = [...record.data];
        dataSource.push({
          ...values,
          seq: maxSeq + 1,
        });
        onUpdateData({ ...record, data: dataSource }, index);
        setVisible(false);
      } else {
        message.error("请先选择机构树中的班子");
      }
    });
  };

  const handleSave = (row) => {
    const newData = [...record.data];
    // if (newData.find((item) => item.seq.toString() === row.seq.toString())) {
    //   message.warning("该测评序号已存在");
    //   return;
    // }
    const idx = newData.findIndex((item) => row.org_id === item.org_id);
    if (idx > -1) {
      newData[idx] = row;
    }
    onUpdateData({ ...record, data: newData }, index);
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "org_name",
      key: "org_name",
      align: "center",
      width: "20%",
      title: "班子名称",
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (_, record) => (
        <Ant.Popconfirm
          title="确认删除?"
          onConfirm={() => handleDelete(record)}
          okText="是"
          cancelText="否"
        >
          <Ant.Button type="link">删除</Ant.Button>
        </Ant.Popconfirm>
      ),
    },
  ];

  const newColumns = columns.map((col) => {
    if (!col.editable) return col;
    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave: handleSave,
      }),
    };
  });

  return (
    <div style={{ marginBottom: 20 }}>
      <Ant.Row type="flex" align="middle" style={{ marginBottom: 10 }}>
        <div style={{ fontSize: 16, fontWeight: 800 }}>
          {record.sequence_name}
        </div>
        <div style={{ marginLeft: 52, marginRight: 20 }}>
          好等次个数：
          <Ant.InputNumber
            value={record.num}
            precision={0}
            size="small"
            onChange={handleNumChange}
          />
        </div>
        <Ant.Button
          type="primary"
          size="small"
          onClick={() => setVisible(true)}
        >
          <Ant.Icon type="plus" />
          添加
        </Ant.Button>
      </Ant.Row>
      <Ant.Table
        bordered
        components={{
          body: {
            row: EditableFormRow,
            cell: EditableCell,
          },
        }}
        columns={newColumns}
        dataSource={record.data}
        scroll={{ y: 300 }}
        pagination={false}
      />

      <Ant.Modal
        title="添加班子"
        visible={visible}
        onOk={handleAdd}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        <NewOrg ref={formRef} />
      </Ant.Modal>
    </div>
  );
}
