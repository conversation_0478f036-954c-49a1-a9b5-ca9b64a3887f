import React, { useState, useEffect, useMemo } from 'react';
import Card from '../Card';
import './index.less';

// 导入图片资源
import ImgPyramid1 from '../../images/pyramid-1.png';
import ImgPyramid2 from '../../images/pyramid-2.png';
import ImgPyramid3 from '../../images/pyramid-3.png';
import ImgPyramid4 from '../../images/pyramid-4.png';


const CadreIndex = ({ data }) => {
    const [activeRow, setActiveRow] = useState(1);
    const [activeInfo, setActiveInfo] = useState({});
    const [coorMenuSelected, setCoorMenuSelected] = useState(2);


    // 金字塔列表数据
    const pyramidList = useMemo(() => [
        { img: ImgPyramid1, label: '10%', key: 1 },
        { img: ImgPyramid2, label: '20%', key: 2 },
        { img: ImgPyramid3, label: '30%', key: 3 },
        { img: ImgPyramid4, label: '40%', key: 4 },
    ], []);


    // 菜单配置
    const menuComputed = useMemo(() => {
        const menu = [
            // { label: '本单位', key: 1 },
            { label: '同序列', key: 2 },
            // { label: '乡镇/部门', key: 3 },
            // { label: '全县', key: 0 },
        ];

        return menu;
    }, []);


    useEffect(() => {
        console.log(data)
        if (data) {
            setActiveInfo(data);
        }
    }, [data]);

    const handleMenuClick = (key) => {
        setCoorMenuSelected(key);
    };

    const handlePyramidClick = (key) => {
        setActiveRow(key);
    };

    return (
        <Card
            title="干部指数"
            header
            sizeType="2"
            rightSlot={
                <div className="data-menu">
                    {menuComputed.map(item => (
                        <div
                            key={item.key}
                            className={`menu-item ${coorMenuSelected === item.key ? 'menu-active' : ''}`}
                            onClick={() => handleMenuClick(item.key)}
                        >
                            {item.label}
                        </div>
                    ))}
                </div>
            }
        >

            <div className="pyramid scale-0-9">
                {pyramidList.map(item => (
                    <div
                        key={item.key}
                        className={`pyramid-item pyramid-${item.key}`}
                        onClick={() => handlePyramidClick(item.key)}
                    >
                        <div className="pyramid-img">
                            <div className="pyramid-label">{item.label}</div>
                            <img src={item.img} alt="" />
                            {activeInfo.pyramid_index === item.key - 1 && (
                                <div className="select">
                                    <div className="select-line"></div>
                                    <div className="text">
                                        {activeInfo.user_name} {activeInfo.rindex}
                                    </div>
                                </div>
                            )}
                            <div className="split-line">
                                <div className="line-box">
                                    <div className="left-line"></div>
                                    <div className="right-line"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </Card>
    );
};

export default CadreIndex;