.activity-template {
  background-color: #F7F8F9;
  .search-header-wrap{
    .header{
      background-color: #fff;
    }
  }
  .template-wrap{
    margin-top: -20px;
    padding-bottom: 20px;
    background-color: #fff;
  }
  .ant-input {
    border-radius: 4px;
  }
  .active-header {
    height: 60px;
    padding: 0 30px;
    line-height: 60px;
    font-size: 19px;
    box-shadow: 0 4px 2px #eee;
  }
  .activity-filter {
    padding: 28px 28px 0 28px;
    > div {
      padding: 0 10px 18px 10px;
      border-bottom: 1px solid #E6E6E6;
    }
    .ant-btn {
      width: 88px;
      height: 36px;
      background-color: #FF4D4F;
    }
  }
  .activity-list {
    display: flex;
    padding: 20px 44px 0 0;
    flex-wrap: wrap;
  }
  .activity-item {
    width: 280px;
    height: 222px;
    border: 1px solid #E6E6E6;
    background: #fff;
    position: relative;
    user-select: none;
    margin-left: 45px;
    margin-bottom: 20px;
    &:hover {
      border-color: #FF4D4F
    }
    div {
      &:nth-child(1) {
        span {
          position: absolute;
          top: -1px;
          left: -1px;
          width: 70px;
          height: 28px;
          border: 1px solid #99C9F5;
          background: #EAF4FE;
          color: #47A0F6;
          text-align: center;
          line-height: 28px;
          &:before {
            content: '';
            position: absolute;
            border-top: 14px solid transparent;
            border-right: 12px solid #99C9F5;
            border-bottom: 14px solid transparent;
            top: -1px;
            right: -1px;
          }
          &:after {
            content: '';
            position: absolute;
            border-top: 13px solid transparent;
            border-right: 11px solid #fff;
            border-bottom: 13px solid transparent;
            top: 0px;
            right: -1px;
          }
        }
      }
      &:nth-child(2) {
        padding: 66px 42px;
        font-size: 15px;
        font-weight: bold;
        text-align: center;
      }
      &:last-child {
        text-align: center;
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 37px;
        .selfIcon {
          // display: inline-block;
          padding: 10px;
          font-size: 18px;
          background: #fff;
          border-radius: 50%;
          margin: 0 10px;
          border: 1px solid #E6E6E6;
          cursor: pointer;
        }
        .selfIcon:hover{
          background-color: #FFF5F5;
          color: #FF4D4F;
          border-color: transparent
        }
      }
    }
  }
  .activity-add {
    width: 280px;
    height: 222px;
    border: 1px solid #E6E6E6;
    background: #fff;
    position: relative;
    cursor: pointer;
    line-height: 222px;
    text-align: center;
    user-select: none;
    margin-left: 44px;
    margin-bottom: 20px;
    &:hover {
      border-color: #ddd;
    }
    span {
      span {
        vertical-align: middle;
        &:first-child {
          position: relative;
          width: 20px;
          height: 20px;
          display: inline-block;
          &:before {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background: #7990B5;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
          }
          &:after {
            content: '';
            position: absolute;
            width: 2px;
            height: 20px;
            background: #7990B5;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
          }
        }
        &:last-child {
          font-size: 19px;
          color: #7990B5;
          padding-left: 10px;
        }
      }
    }
  }
  .activity-spin {
    min-height: 504px;
    text-align: center;
    line-height: 504px;
  }
  .activity-pages {
    padding-left: 44px;
    .ant-pagination {
      display: inline-block;
      vertical-align: middle;
    }
    span {
      vertical-align: middle;
      padding-left: 10px;
      color: #999;
    }
  }
}
