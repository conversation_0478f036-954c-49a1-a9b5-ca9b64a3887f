import {
  Button,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Tree,
} from "antd";
import {
  batchAddGroupOrg,
  checkOrgGroup,
  orgDataTree,
} from "client/apis/cadre-portrait";
import { connect } from "dva";
import PropTypes from "prop-types";
import { Component } from "react";

import "./preson.less";
const { confirm } = Modal;
const { Search } = Input;
const { TreeNode } = Tree;

class LeaderGroup extends Component {
  constructor(props) {
    super(props);
    this.state = {
      orgData: [],
      treeData: [],
      expandedKeys: [],
      autoExpandParent: true,
      checkedKeys: [],
      selectedKeys: [],
      searchValue: '',
    };
    // 绑定方法
    this.loop = this.loop.bind(this);
    this.onExpand = this.onExpand.bind(this);
    this.onCheck = this.onCheck.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.getTreeData = this.getTreeData.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.submitData = this.submitData.bind(this);
    this.onChange = this.onChange.bind(this);
  }
  componentDidMount() {
    this.getTreeData()
  }
  buildTreeData(items) {
    return items.map((item) => ({
      title: item.name,
      key: item.organization_id,
      children: item.children ? this.buildTreeData(item.children) : [],
    }));
  };

  // 递归获取所有节点的key，用于默认展开
  getAllTreeKeys(treeData) {
    let keys = [];
    const traverse = (nodes) => {
      nodes.forEach(node => {
        keys.push(node.key);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return keys;
  }
  async getTreeData() {
    try {
      let res = await orgDataTree();
      if (res.data && res.data.data) {
        let newtreeData = this.buildTreeData(res.data.data);
        // 获取所有节点的key用于默认展开
        let allKeys = this.getAllTreeKeys(newtreeData);
        this.setState({
          treeData: newtreeData,
          expandedKeys: allKeys,
          autoExpandParent: true
        });
      } else {
        console.error('API 返回的数据格式不正确:', res);
      }
    } catch (error) {
      console.error('获取树数据时出错:', error);
      message.error('获取树数据失败，请稍后重试');
    }
  }
  removeItem(index, id) {
    const { orgData, checkedKeys } = this.state;
    const neworgData = [...orgData];
    // 获取要删除的项
    const removedItem = neworgData.splice(index, 1)[0];
    // 过滤 checkedKeys，移除与被删除项对应的 key
    // 检查 checkedKeys 是否为数组，若不是则初始化为空数组
    const validCheckedKeys = Array.isArray(checkedKeys) ? checkedKeys : [];
    let newCheckedKeys = validCheckedKeys;
    if (removedItem) {
      // 过滤 checkedKeys，移除与被删除项对应的 key
      newCheckedKeys = validCheckedKeys.filter(key => key != removedItem.org_id);
    }
    this.setState({
      orgData: neworgData,
      checkedKeys: newCheckedKeys,
      autoExpandParent: true,
    });
  }
  submitData() {
    const { orgData } = this.state;
    let cadres = []
    orgData.map((item, index) => {
      cadres.push({
        org_id: item.org_id,
        group_id: this.props.groupId,
        name: item.name,
      })
    })
    checkOrgGroup({ orgs: cadres }).then((res) => {
      let _this = this;
      if (res.data.code === 0) {
        if (res.data.data.existsOrgIds.length > 0) {

          confirm({
            content: (
              <div>
                <p>
                  {
                    res.data.data.existsOrgNames.map((item, index) => {
                      // 除了最后一项，其余项后面添加逗号
                      return index === res.data.data.existsOrgNames.length - 1 ?
                        <span key={index}>{item}</span> :
                        <span key={index}>{item},</span>;
                    })
                  }
                </p>
                <p>更新：将序列修改为当前序列</p>
                <p>跳过：继续在原序列</p>
              </div>
            ),
            cancelText: '更新',
            okText: '跳过',
            // okType: 'danger',
            icon: <Icon type="exclamation-circle" style={{ color: 'red' }} />,
            onOk() {
              let obj = {
                need_exclude_org_ids: res.data.data.existsOrgIds,
                operate_mark: 2,
                orgs: cadres
              }
              batchAddGroupOrg(obj).then((re) => {
                if (re.data.code === 0) {
                  message.success('添加成功');
                  _this.setState({
                    orgData: [],
                    checkedKeys: [],
                    selectedKeys: []
                  })
                  _this.props.hideModal()
                }
              })
            },
            onCancel() {
              let obj = {
                need_exclude_org_ids: res.data.data.existsOrgIds,
                operate_mark: 1,
                orgs: cadres
              }
              batchAddGroupOrg(obj).then((r) => {
                if (r.data.code === 0) {
                  message.success('添加成功');
                  _this.setState({
                    orgData: [],
                    checkedKeys: [],
                    selectedKeys: []
                  })
                  _this.props.hideModal()
                }
              })
            },

          });
        } else {
          batchAddGroupOrg({ orgs: cadres }).then((res) => {
            if (res.data.code === 0) {
              message.success('添加成功');
              this.setState({
                orgData: [],
                checkedKeys: [],
                selectedKeys: []
              })
              this.props.hideModal()
            }
          })
        }
      } else {
        message.error(res.data.message);
      }
    });
  }
  onExpand(expandedKeys) {
    this.setState({
      expandedKeys,
      autoExpandParent: true,
    });
  };
  looptree(data, keys) {
    // 检查 keys 数组是否为空
    if (keys.length === 0) {
      return [];
    }
    let arr = [];
    data.forEach(item => {
      // 使用 some 方法检查 item.key 是否存在于 keys 数组中
      if (keys.some(key => item.key == key)) {
        arr.push({
          org_id: item.key,
          name: item.title,
        });
      }
      if (item.children) {
        // 处理递归返回的结果并合并到 arr 中
        arr = arr.concat(this.looptree(item.children, keys));
      }
    });
    return arr;
  }
  onCheck(checkedKeys, info) {
    const { treeData } = this.state;
    let arr = this.looptree(treeData, checkedKeys)
    this.setState({
      checkedKeys,
      orgData: arr
    });
  };

  onSelect(selectedKeys, info) {
    this.setState({ selectedKeys });
  };
  loop(data) {
    const { searchValue } = this.state;
    return data.map(item => {
      const index = item.title.indexOf(searchValue);
      const beforeStr = item.title.substr(0, index);
      const afterStr = item.title.substr(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: '#f50' }}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (item.children) {
        return (
          <TreeNode key={item.key} title={title}>
            {this.loop(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode key={item.key} title={title} />;
    });
  }
  getParentKey(key, tree) {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key;
        } else if (this.getParentKey(key, node.children)) {
          parentKey = this.getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };
  onChange(e) {
    const { treeData } = this.state;
    const { value } = e.target;
    const expandedKeysSet = new Set();

    // 定义一个递归函数来查找匹配节点及其父节点
    const findExpandedKeys = (nodes) => {
      nodes.forEach((node) => {
        if (node.title.indexOf(value) > -1) {
          // 添加匹配节点的父节点键
          let parentKey = this.getParentKey(node.key, treeData);
          while (parentKey) {
            expandedKeysSet.add(parentKey);
            parentKey = this.getParentKey(parentKey, treeData);
          }
          // 添加匹配节点自身的键
          expandedKeysSet.add(node.key);
        }
        if (node.children) {
          findExpandedKeys(node.children);
        }
      });
    };

    findExpandedKeys(treeData);
    const expandedKeys = Array.from(expandedKeysSet);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };
  render() {
    const { orgData, treeData } = this.state;
    return (
      <Modal
        footer={null}
        title={this.props.title}
        width='800px'
        destroyOnClose={true}
        visible={this.props.visible}
        onCancel={
          () => {
            this.props.hideModal()
            this.setState({
              orgData: [],
              checkedKeys: [],
              selectedKeys: []
            })
          }
        }
      >
        <div className="cadre-management">
          <div className="left">
            <Search style={{ marginBottom: 8 }} placeholder="请输入机构名称" onChange={this.onChange} />
            <div style={{ background: '#f7f8f9', marginTop: '10px' }}>
              {
                treeData.length > 0 &&
                <Tree
                  checkable
                  onExpand={this.onExpand}
                  expandedKeys={this.state.expandedKeys}
                  autoExpandParent={this.state.autoExpandParent}
                  onCheck={this.onCheck}
                  checkedKeys={this.state.checkedKeys}
                  onSelect={this.onSelect}
                  selectedKeys={this.state.selectedKeys}
                >
                  {this.loop(treeData)}
                </Tree>
              }
            </div>

          </div>
          <div className="right-box">
            <div className="right-box-header">
              <span>已选择</span><span>({orgData.length})</span>
            </div>
            <div className="right-box-content">
              {
                orgData.length > 0 && orgData.map((item, index) => {
                  return (
                    <div className="right-box-item" key={item.org_id}>
                      <div className="right-box-item-left">
                        <div>
                          {item.name}
                        </div>
                      </div>
                      <div className="icon" onClick={this.removeItem.bind(this, index, item.org_id)}>
                        <Icon type="close-circle" style={{ color: 'red' }} />
                      </div>
                    </div>
                  )
                })
              }

            </div>
          </div>

        </div>
        <div className="footer-btn">
          <Button type="primary" onClick={this.submitData.bind(this)}>确认</Button>
        </div>
      </Modal>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(Form.create()(LeaderGroup));
