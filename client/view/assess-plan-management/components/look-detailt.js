import React from "react";
import StatusIcon from "components/status-icon";
import { Modal, Form, Table, Input, Button } from "antd";


const FormItem = Form.Item;

const LookDetail = (props) => {

  const {
    visible,
    detailData,
    cancel,
    filterValue,
    updateState,
    execute_orgs_filtered = []
  } = props;
  const formItemLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 }
  }

  const {
    name,
    execute_type,
    start_time,
    end_time,
    start_month,
    start_year,
    start_quarter,
    meeting_types = [],
    is_execute,
    execute_orgs = [],
    send_type,
    limit = {},
    limit_type_list = []
  } = detailData;

  const renderOrgTypes = (list = []) => {
    // console.log(list);
    if (list && Array.isArray(list) && list.length !== 0) {
      return list.map((item, index) => {
        return (
          <span className="limit-org-type-wrapper" key={item.meeting_plan_limit_type_id || index}>
            {item.org_type_child_name}
          </span>
        )
      });
    }
    return (
      <span>
        -
      </span>
    )
  }


  const executeTypeName = (num) => {
    let execute_type_name = {};
    if (num === 1) {
      execute_type_name = {
        name: "自定义时间段",
        start_time,
        end_time
      };
    }
    if (num === 2) {
      execute_type_name = {
        name: "自然月周期",
        start_time: `${start_year}-${start_month}`
      };
    }
    if (num === 3) {
      execute_type_name = {
        name: "季度周期",
        start_time: `${start_year} 年, 第${start_quarter === 1 ? "一" : start_quarter === 2 ? "二" : start_quarter === 3 ? "三" : "四"}季度`
      };
    }
    if (num === 4) {
      execute_type_name = {
        name: "年度周期",
        start_time: start_year
      };
    }
    return execute_type_name;
  }
  const executeOrzColumns = [
    {
      title: "序号",
      align: "center",
      render: (val, record, index) => (index + 1)
    },
    {
      title: "组织名称",
      dataIndex: "org_name",
      align: "center"
    }
  ];
  const executeOrzTableProps = {
    columns: executeOrzColumns,
    dataSource: execute_orgs_filtered,
    bordered: true,
    pagination: {
      size: "small",
      pageSize: 10,
      pageSizeOptions: ["10", "20", "30"],
      showQuickJumper: true,
      showSizeChanger: true,
    },
    rowKey: "org_id"
  }
  const meetingTypeColumns = [
    {
      title: "序号",
      align: "center",
      render: (val, record, index) => (index + 1)
    },
    {
      // title: "会议类别",
      title: "活动类别",
      dataIndex: "category",
      align: "center"
    },
    {
      // title: "会议类型",
      title: "活动类型",
      dataIndex: "type",
      align: "center"
    },
    {
      // title: "召开次数",
      // title: "举办次数",
      title: "要求开展频次",
      dataIndex: "meeting_num",
      align: "center"
    },
    {
      title: "未执行扣分",
      dataIndex: "deduct",
      align: "center"
    },
    {
      title: "是否需要签到",
      dataIndex: "is_sign_in",
      align: "center",
      render: val => val === 0 ? "不需要" : "需要"
    },
    // {
    //   title: "是否需要填写决议",
    //   dataIndex: "is_w_resolution",
    //   align: "center",
    //   render: val => val === 0 ? "不需要" : "需要"
    // },
  ]
  const meetingTypeTableProps = {
    columns: meetingTypeColumns,
    dataSource: meeting_types,
    bordered: true,
    pagination: false,
    rowKey: "category_id"
  }

  const filterByName = () => {
    let result = execute_orgs;
    if (filterValue) {
      result = execute_orgs.filter((org, index) => {
        // console.log(org);
        return org.org_name.indexOf(filterValue) !== -1;
      });
    }
    updateState({
      execute_orgs_filtered: result
    });
  }

  return (
    <div>
      <Modal
        // title="考核计划详情"
        // title="组织生活详情"
        title="活动类型详情"
        footer={null}
        visible={visible}
        width={1100}
        onCancel={cancel}
        wrapClassName="assess-plan-manage-detail-modal-wrapper"
      >
        <div
          style={{
            width: "100%",
            height: "600px",
            overflowY: "auto"
          }}
        >
          <Form>
            <FormItem
              wrapperCol={{
                span: 15,
                offset: 1
              }}
              style={{
                margin: "0"
              }}
            >
              <p
                style={{
                  display: "flex",
                  alignItems: "center",
                  fontWeight: "bold",
                  fontSize: "20px",
                  color: "#000",
                  fontFamily: "MicrosoftYaHei"
                }}
              >{name}{is_execute === 0 ? <StatusIcon direction="left" text="已停用" type={"disable"} /> : <StatusIcon direction="left" text="已启用" type={"success"} />}</p>
            </FormItem>
            <FormItem
              // label="执行方式"
              label="任务周期"
              {...formItemLayout}
            >
              <p style={{
                // margin: "-3.5px"
              }}>{executeTypeName(execute_type).name}</p>
            </FormItem>
            <FormItem
              label="开始时间"
              {...formItemLayout}
            >
              <p style={{
                // margin: "-3.5px"
              }}>{executeTypeName(execute_type).start_time}</p>
            </FormItem>
            <FormItem
              label="结束时间"
              {...formItemLayout}
              style={{
                display: `${execute_type === 1 ? "block" : "none"}`
              }}
            >
              <p style={{
                // margin: "-3.5px"
              }}>{execute_type === 1 && executeTypeName(execute_type).end_time}</p>
            </FormItem>
            <FormItem
              // label="会议类型"
              label="活动类型"
              {...formItemLayout}
            >
              <p><Table {...meetingTypeTableProps} /></p>
            </FormItem>
            <FormItem label="发放方式" {...formItemLayout}>
              <p>
                {
                  send_type === 1 ? "自动发放" : "手动发放"
                }
                {
                  send_type === 1 &&
                  <div style={{ backgroundColor: "#F1F5F8", padding: 15 }}>
                    <div>
                      <label className="limit-label">组织类型</label>
                      <span>
                        {
                          renderOrgTypes(limit_type_list)
                        }
                      </span>
                    </div>
                    <div>
                      <label className="limit-label">离退休组织</label>
                      <span>
                        {
                          limit.is_retire === 1 ? "包含" :
                            limit.is_retire === 2 ? "不包含" :
                              limit.is_retire === 3 ? "仅包含" : "-"
                        }
                      </span>
                    </div>
                    <div>
                      <label className="limit-label">已成立党小组</label>
                      <span>
                        {
                          limit.party_group === 1 ? "包含" :
                            limit.party_group === 2 ? "不包含" :
                              limit.party_group === 3 ? "仅包含" : "-"
                        }
                      </span>
                    </div>
                    <div>
                      <label className="limit-label">已成立支委会</label>
                      <span>
                        {
                          limit.period === 1 ? "包含" :
                            limit.period === 2 ? "不包含" :
                              limit.period === 3 ? "仅包含" : "-"
                        }
                      </span>
                    </div>
                  </div>
                }
              </p>
            </FormItem>
            <FormItem
              label="执行组织"
              {...formItemLayout}
            >
              <div className="search-wrapper">
                <label>组织名称</label>
                <div className="input-wrapper">
                  <Input
                    placeholder="请输入组织名称"
                    value={filterValue}
                    onChange={(e) => {
                      const target = e.target;
                      const value = target.value;
                      updateState({
                        filterValue: value,
                      })
                    }}
                    onPressEnter={() => {
                      filterByName();
                    }}
                  />
                </div>
                <div className="buttons-wrapper">
                  <Button type="primary" onClick={() => {
                    // console.log("查询", filterValue);
                    filterByName();
                  }}>查询</Button>
                  <Button onClick={() => {
                    updateState({
                      filterValue: "",
                      execute_orgs_filtered: execute_orgs
                    });
                  }}>重置</Button>
                </div>
              </div>
              <Table {...executeOrzTableProps} />
            </FormItem>
          </Form>
        </div>

      </Modal>
    </div>
  )
}

export default LookDetail;