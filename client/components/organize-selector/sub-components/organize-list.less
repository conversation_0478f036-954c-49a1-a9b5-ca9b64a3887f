.organize-modal .organize-list {
  display: flex;
  justify-content: space-between;
  position: relative;

  .selector-wrapper,
  .selected-list {
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
    }

    .breadcrumb-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;

      .breadcrumb-item {
        max-width: 100%;
        font-size: 14px;
        align-items: center;
        color: #999;
        flex-shrink: 0;
        white-space: pre-wrap;
        word-break: break-all;

        .anticon.anticon-right {
          margin: 0 2px;
        }

        a {
          color: #007AFF;
        }
      }
    }

    width: 420px;
    border: 1px solid #E5E5E5;
    height: 550px;
    overflow: auto;
    background-color: #F7F8F9;
    padding: 20px;

    .org-item-wrapper {
      min-height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #E5E5E5;

      .org-item-name {
        &.indent-right {
          margin-right: 50px;
        }

        display: flex;
        flex-grow: 1;
        align-items: center;

        .checkbox-wrapper {
          margin-right: 8px;

          .ant-checkbox-wrapper {
            display: inline-flex;
            align-items: center;
          }
        }

        .name-wrapper {
          cursor: pointer;
          padding: 0 5px;
        }
      }

      .org-item-next {
        width: 50px;
        flex-shrink: 0; // border: 1px solid red;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #999;
        cursor: pointer;
      }
    }

    .count {
      color: #333;
      padding-left: 10px;
      margin-bottom: 5px;
    }

    .org-tag {
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      min-height: 44px;
      padding: 12px;
      border: 1px solid #E5E5E5;
      font-size: 14px;
      line-height: 1;
      box-sizing: border-box;
      background-color: #fff;
      margin: 5px;

      .tag-text {
        line-height: 1.4;
      }

      .tag-close {
        margin-left: 10px;
        cursor: pointer;
        color: #f46e65;
      }
    }
  }

  .ant-tree .ant-tree-switcher {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}