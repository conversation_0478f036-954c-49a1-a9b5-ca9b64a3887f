.NewlyIncreased {
  &-form {
    padding: 34px 0 25px;

    .image {
      .view {
        // width: 82px;
        // height: 82px;
        cursor: pointer;
      }

      .text {
        cursor: pointer;
        padding-top: 15px;
        width: 82px;
        height: 82px;
        background: #eeeeee;
        text-align: center;
        color: #999999;
        font-size: 12px;

        .anticon {
          margin-bottom: 4px;
          font-size: 25px;
        }

        & > div {
          height: 12px;
          line-height: 12px;
        }
      }
      .image-view {
        .upload-done-action-shadow {
          transition: 0.2s;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          left: 0;
          top: 0;
          background-color: rgba(0, 0, 0, 0.3);

          .ant-upload {
            width: 30px;
          }

          & > span {
            cursor: pointer;
            margin: 0 17px;
          }

          .anticon {
            font-size: 30px;
            color: #fff;
          }
        }
      }
    }

    .annotation {
      font-size: 12px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #bfbfbf;
      line-height: 24px;
    }

    .clipping {
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      z-index: 99999;

      iframe {
        width: 100%;
        height: 100%;
      }
    }

    .select-org {
      .ant-form-item-control-wrapper {
        width: 46.83333333%;

        .ant-form-item-children {
          display: flex;
        }
      }
    }
    .text-Area {
      position: relative;
      display: flex;
      flex-direction: column;
      .input-text {
        padding-bottom: 20px;
        padding-right: 50px;
      }
      .echo {
        position: absolute;
        top: 65%;
        left: 92%;
      }
    }

    .ant-form-item {
      margin-bottom: 27px;
    }

    .sort-line {
      margin-bottom: 10px;
    }

    .seq-line {
      margin: 10px 0 10px;
    }

    textarea.ant-input {
      resize: none;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    input[type="number"] {
      -moz-appearance: textfield;
    }
    .ant-input-number {
      min-width: 100%;
    }

    .ant-radio-wrapper:hover .ant-radio,
    .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus + .ant-radio-inner {
      border-color: #f46e65;
    }

    .ant-radio-checked {
      .ant-radio-inner {
        border-color: #f46e65;
      }
    }

    .ant-radio-inner {
      &::after {
        background-color: #f46e65;
      }
    }

    .ant-checkbox-checked > .ant-checkbox-inner {
      background-color: #f46e65;
      border-color: #f46e65;
    }

    .btn {
      .ant-btn {
        width: 90px;
        height: 36px;
        border-radius: 4px;
        font-size: 16px;
      }

      .ant-btn-primary {
        background: #f46e65;
      }

      .ant-btn-default {
        color: #999999;
        margin-left: 24px;
      }
    }
  }
}
