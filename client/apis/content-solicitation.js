import { http } from "client/tool/axios";
import { activityHost } from "./config";

// 400020316-六点打卡-内容审核列表 http://wiki.aidangqun.com/project/7?p=907
const listApproveMy = (params = {}) => {
  return http.get(`${activityHost}/content/list-approve-my`, params);
};

// 400020317-六点打卡-内容审批 http://wiki.aidangqun.com/project/7?p=908
const approveContent = (params = {}) => {
  return http.post(`${activityHost}/content/approve`, params);
};

// 400020314-六点打卡-保存、提交我的内容 http://wiki.aidangqun.com/project/7?p=905
const saveMyContent = (params = {}) => {
  return http.post(`${activityHost}/content/save-my`, params);
};

// 400020319-六点打卡-获取内容详情 http://wiki.aidangqun.com/project/7?p=912
const getContent = (params = {}) => {
  return http.get(`${activityHost}/content/get-content`, params);
};

// 400020318-六点打卡-删除内容 http://wiki.aidangqun.com/project/7?p=909
const delMy = (params = {}) => {
  return http.get(`${activityHost}/content/del-my`, params);
};

export {
  listApproveMy,
  approveContent,
  saveMyContent,
  getContent,
  delMy,
}