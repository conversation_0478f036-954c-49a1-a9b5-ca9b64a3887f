import React, { Component } from "react";
import {
  Form,
  Card,
  Input,
  Button,
  Row,
  Col,
  Select,
  Cascader,
  message
} from "antd";
import VerifyCodeButton from "client/components/verifycode-button";
import FormBlock from "client/components/form-block";
import PropTypes from "prop-types";
import {
  getPhoneCaptcha,
  getDataDictionary,
  dealRegistInfo,
  getArea
} from "client/apis/active-organization";
import phoneReg from "client/tool/phoneReg";

const FormItem = Form.Item;
const SelectOption = Select.Option;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 }
};

class RegisterUserInfo extends Component {
  constructor(props) {
    super(props);

    this.state = {
      areaData: [], // 籍贯
      genderDatas: [], // 性别
      politicalTypeDatas: [], // 政治面貌
      jobGradeDatas: [], // 技术等级
      comunistDatas: [], // 党组织
      youthLeagueDatas: [], // 团组织
      unionMemberDatas: [], // 工会组织
      womenLeagueDatas: [], // 妇女组织
      certTypeDatas: [], // 证件类型
      ethnicDatas: [], // 民族
      educationDatas: [], //学历
      censusTypeDatas: [] // 户籍类型
    };
  }

  componentDidMount() {
    const dictionary = [
      {
        code: "1002",
        name: "性别",
        statePropName: "genderDatas"
      },
      {
        code: "1013",
        name: "政治面貌",
        statePropName: "politicalTypeDatas"
      },
      {
        code: "1009",
        name: "技术等级",
        statePropName: "jobGradeDatas"
      },
      {
        code: "1022",
        name: "中共党员",
        statePropName: "comunistDatas"
      },
      {
        code: "1023",
        name: "团组织",
        statePropName: "youthLeagueDatas"
      },
      {
        code: "1024",
        name: "工会组织",
        statePropName: "unionMemberDatas"
      },
      {
        code: "1025",
        name: "妇女组织",
        statePropName: "womenLeagueDatas"
      },
      {
        code: "1010",
        name: "证件类型",
        statePropName: "certTypeDatas"
      },
      {
        code: "1004",
        name: "民族",
        statePropName: "ethnicDatas"
      },
      {
        code: "1011",
        name: "户籍类型",
        statePropName: "censusTypeDatas"
      },
      {
        code: "1003",
        name: "学历",
        statePropName: "educationDatas"
      }
    ];

    Promise.all(dictionary.map(item => getDataDictionary({ code: item.code })))
      .then(res => {
        dictionary.forEach((item, index) => {
          const { data } = res[index];
          this.setState({ [item.statePropName]: data.data });
        });
      })
      .catch(err => {
        message.error("获取数据字典出错");
      });

    // 初始化籍贯数据
    getArea({ pid: null })
      .then(res => {
        const { data } = res;
        if (data.code === 0) {
          const area = data.data.map(item => {
            return {
              label: item.area_name,
              value: item.adcode,
              isLeaf: false
            };
          });

          this.setState({ areaData: area });
        } else {
          return Promise.reject(data);
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  }

  // 提交验证
  handleSubmit(e) {
    e.preventDefault();
    const props = this.props;

    // props.changeState({ step: 2 });

    props.form.validateFieldsAndScroll((errs, values) => {
      if (!errs) {
        // data.oid = props.state.organizationInfo.org_id;

        let registerUserInfo = { ...values };
        registerUserInfo.native_province = values.nativeCascader[0];
        registerUserInfo.native_city = values.nativeCascader[1];
        delete registerUserInfo.nativeCascader;

        props.changeState({ registerUserInfo });

        const data = {
          name: values.name,
          phone: values.phone,
          cert_number: values.cert_number,
          captcha: values.captcha,
          cert_type: values.cert_type
        };

        // 验证手机号和身份证号码
        dealRegistInfo(data)
          .then(res => {
            const { data } = res;
            if (data.code === 0) {
              props.changeState({
                userId: data.data || "",
                step: 2 // 跳转下一步
              });
            } else {
              return Promise.reject(data);
            }
          })
          .catch(err => {
            message.error(err.message);
          });
      }
    });
  }

  // 获取手机验证码
  verifyCode(callback) {
    const props = this.props;
    const phone = props.form.getFieldValue("phone");

    if (!phone) {
      return props.form.setFields({
        phone: { errors: [new Error("请输入手机号码")] }
      });
    }

    getPhoneCaptcha({ phone })
      .then(res => {
        const { data } = res;
        if (data.code === 0) {
          // 获取短信验证码成功，开始倒计时
          callback(true);
        } else {
          return Promise.reject(data);
        }
      })
      .catch(err => {
        message.error(err.message);
      });
  }

  // render select option
  renderOptions(data) {
    if (!data) return;

    let options = [
      <SelectOption key="" value="">
        请选择
      </SelectOption>
    ];
    data.forEach(item => {
      options.push(
        <SelectOption key={item.op_key} value={item.op_key}>
          {item.op_value}
        </SelectOption>
      );
    });

    return options;
  }

  // 加载籍贯数据
  loadAreaData(selectedOptions) {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    getArea({ pid: targetOption.value })
      .then(res => {
        targetOption.loading = false;

        const { data } = res;
        if (data.code === 0) {
          const area = data.data.map(item => {
            return {
              label: item.area_name,
              value: item.adcode,
              isLeaf: selectedOptions.length > 0
            };
          });
          targetOption.children = area;

          this.setState({ areaData: [...this.state.areaData] });
        }
      })
      .catch(err => {
        targetOption.loading = false;
      });
  }

  render() {
    const { getFieldDecorator } = this.props.form;

    const { registerUserInfo } = this.props.state;

    const ereaDataInit = () => {
      const initialValue = [];
      if (registerUserInfo.native_province) {
        initialValue.push(registerUserInfo.native_province);
      }
      if (registerUserInfo.native_city) {
        initialValue.push(registerUserInfo.native_city);
      }
      return initialValue;
    };

    return (
      <Card className="register-user-info">
        <Form onSubmit={this.handleSubmit.bind(this)}>
          <FormBlock title="基本资料">
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="姓名：" {...formItemLayout}>
                  {getFieldDecorator("name", {
                    initialValue: registerUserInfo.name,
                    rules: [{ required: true, message: "请输入您的姓名" }]
                  })(<Input placeholder="请输入您的姓名" />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="性别：" {...formItemLayout}>
                  {getFieldDecorator("gender", {
                    initialValue: registerUserInfo.gender,
                    rules: [{ required: true, message: "请选择性别" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.genderDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="手机号：" {...formItemLayout}>
                  {getFieldDecorator("phone", {
                    initialValue: registerUserInfo.phone,
                    rules: [
                      {
                        required: true,
                        message: "请输入您的手机号"
                      },
                      {
                        pattern: phoneReg,
                        message: '电话格式错误'
                      }
                    ]
                  })(<Input placeholder="请输入您的手机号" />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="短信验证码" {...formItemLayout}>
                  {getFieldDecorator("captcha", {
                    initialValue: registerUserInfo.captcha,
                    rules: [{ required: true, message: "请输入验证码" }]
                  })(
                    <Input
                      placeholder="请输入验证码"
                      addonAfter={
                        <VerifyCodeButton
                          verifyCode={callback => this.verifyCode(callback)}
                        />
                      }
                    />
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="证件类型：" {...formItemLayout}>
                  {getFieldDecorator("cert_type", {
                    initialValue: registerUserInfo.cert_type,
                    rules: [{ required: true, message: "请输入证件类型" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.certTypeDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="证件号：" {...formItemLayout}>
                  {getFieldDecorator("cert_number", {
                    initialValue: registerUserInfo.cert_number,
                    rules: [{ required: true, message: "请输入证件号" }]
                  })(<Input maxLength={18} placeholder="请输入证件号" />)}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="籍贯：" {...formItemLayout}>
                  {getFieldDecorator("nativeCascader", {
                    initialValue: ereaDataInit(),
                    rules: [{ required: true, message: "请选择所属行政区划" }]
                  })(
                    <Cascader
                      placeholder="请选择所属行政区划"
                      options={this.state.areaData}
                      loadData={this.loadAreaData.bind(this)}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="政治面貌：" {...formItemLayout}>
                  {getFieldDecorator("political_type", {
                    initialValue: registerUserInfo.political_type,
                    rules: [{ required: true, message: "请选择政治面貌" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.politicalTypeDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="民族：" {...formItemLayout}>
                  {getFieldDecorator("ethnic", {
                    initialValue: registerUserInfo.ethnic,
                    rules: [{ required: true, message: "请选择民族" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.ethnicDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="户籍类型：" {...formItemLayout}>
                  {getFieldDecorator("census_type", {
                    initialValue: registerUserInfo.census_type,
                    rules: [{ required: true, message: "请选择户籍类型" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.censusTypeDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="学历：" {...formItemLayout}>
                  {getFieldDecorator("education", {
                    initialValue: registerUserInfo.education,
                    rules: [{ required: true, message: "请选择学历" }]
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.educationDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="技术等级：" {...formItemLayout}>
                  {getFieldDecorator("job_grade", {
                    initialValue: registerUserInfo.job_grade
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.jobGradeDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="职务：" {...formItemLayout}>
                  {getFieldDecorator("position", {
                    initialValue: registerUserInfo.position
                  })(<Input placeholder="请输入职务" />)}
                </FormItem>
              </Col>
            </Row>
          </FormBlock>

          <FormBlock title="群团属性">
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="党组织：" {...formItemLayout}>
                  {getFieldDecorator("comunist", {
                    initialValue: registerUserInfo.comunist
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.comunistDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="团组织：" {...formItemLayout}>
                  {getFieldDecorator("youth_league", {
                    initialValue: registerUserInfo.youth_league
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.youthLeagueDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <FormItem label="工会组织：" {...formItemLayout}>
                  {getFieldDecorator("union_member", {
                    initialValue: registerUserInfo.union_member
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.unionMemberDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="妇女组织：" {...formItemLayout}>
                  {getFieldDecorator("women_league", {
                    initialValue: registerUserInfo.women_league
                  })(
                    <Select placeholder="请选择">
                      {this.renderOptions(this.state.womenLeagueDatas)}
                    </Select>
                  )}
                </FormItem>
              </Col>
            </Row>
          </FormBlock>
          <FormItem style={{ marginTop: 10 }}>
            <Button
              style={{ width: 140, marginLeft: 120 }}
              onClick={() => this.props.changeState({ step: 0 })}
            >
              上一步
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: 140, marginLeft: 20 }}
            >
              下一步
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}

const RegisterUserInfoWrapper = Form.create()(RegisterUserInfo);

RegisterUserInfoWrapper.propTypes = {
  changeState: PropTypes.func, // 改变父级state
  state: PropTypes.object
};

export default RegisterUserInfoWrapper;
