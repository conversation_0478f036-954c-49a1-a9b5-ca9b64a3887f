import { ucHost } from "./config";
import { http } from "client/tool/axios";

// const ucHost = "http://localhost:3001/liguoyong";

// 2000010308-用户查询框结构配置-New
export const getQueryConfigList = (data) => {
  return http.get(`${ucHost}/form/query-config-list`, data);
};

// 2000010310-用户表格表头结构查询-New
export const getTableConfigList = (data) => {
  return http.get(`${ucHost}/form/table-config-list`, data);
};

// 2000010401-表单结构查询-New（查看用户详情页面）
export const getFormConfigList = (data) => {
  return http.get(`${ucHost}/form/field-list`, data);
};

/**
 * 查询框更新接口
 * @param {*} data http://*************:38080/doc-view-50.html
 */
export const updateQueryConfigList = (params, data) => {
  return http.post(`${ucHost}/form/query-config-update`, data, {
    params
  });
};

/**
 * 查询框更新接口
 * @param {*} data http://*************:38080/doc-view-49.html
 */
export const updateTableConfigList = (params, data) => {
  return http.post(`${ucHost}/form/table-config-update`, data, {
    params
  });
};

/**
 * 用户组织表单更新接口
 * @param {*} data http://*************:38080/doc-view-48.html
 */
export const updateFormConfigList = (params, data) => {
  return http.post(`${ucHost}/form/field-list-update`, data, {
    params
  });
};

/**
 * 获取省市数据
 */
export const getProvincesList = () => {
  return http.get(`${ucHost}/component/getCache/4`)
}