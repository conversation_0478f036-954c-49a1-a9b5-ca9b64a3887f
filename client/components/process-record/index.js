import React, { Component } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "antd";

import TimeLine from "client/view/meeting-manage/components/time-line";

import "./index.less";

import propTypes from "prop-types";

const ProcessRecord = (props) => {
  const { processRecordData, visible, onCancel } = props;
  const modalProps = {
    // title: "流程记录",
    title: "流程跟踪",
    footer: null,
    width: 600,
    visible,
    wrapClassName: "process-record-modal",
    onCancel: () => {
      onCancel();
    }
  }
  return (
    <Modal {...modalProps}>
      <TimeLine data={processRecordData} />
      <div className="buttons-wrapper">
        <Button type="primary" onClick={() => onCancel()}>确定</Button>
      </div>
    </Modal>
  )
}

ProcessRecord.propTypes = {
  processRecordData: propTypes.array,
  visible: propTypes.bool,
  onCancel: propTypes.func
}

ProcessRecord.defaultProps = {
  processRecordData: [],
  visible: false,
  onCancel: () => { }
}

export default ProcessRecord;

