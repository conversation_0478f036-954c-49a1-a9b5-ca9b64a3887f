import React, { useState } from "react";
import { Button, Modal, Form, TreeSelect, Input, message } from "antd";
import { createCategory, updateCategory } from "apis/resource-library";

const { Item } = Form;
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 16 },
};
const methods = {
  1: createCategory,
  2: updateCategory,
};

const AddModal = (props) => {
  const {
    type, // 1新增 2编辑
    visible,
    onCancel,
    treeData,
    parent_id,
    isRowOpera,
    category_type,
    initDataSource,
    resource_category_id,
    resource_category_name,
    form: { getFieldDecorator, validateFields },
  } = props;

  const [categoryType, setCategoryType] = useState(null);
  const [parentId, setParentId] = useState(null);

  const onSubmit = () => {
    validateFields((err, res) => {
      if (err) {
        return;
      }
      const params = {
        ...res,
        category_type: category_type || categoryType,
        resource_category_id,
      };
      if (type === 1) {
        delete params.resource_category_id;
      }

      methods[type](params).then(({ data: res }) => {
        if (res.code !== 0) {
          message.error(`${type === 1 ? "新建" : "编辑"}失败`);
          return;
        }
        message.success(`${type === 1 ? "新建" : "编辑"}成功`);
        onClose();
        initDataSource();
      });
    });
  };

  const onClose = () => {
    onCancel();
  };

  const onChangeSelect = (val, extra) => {
    const { category_type } = extra.triggerNode.props;
    setParentId(val);
    setCategoryType(category_type);
  };

  const modalProps = {
    visible,
    centered: true,
    destroyOnClose: true,
    title: `${type === 1 ? "新建" : "编辑"}分类`,
    onCancel: () => onClose(),
    wrapClassName: "resource-modal",
    maskClosable: false,
    footer: (
      <span>
        <Button type="primary" onClick={() => onSubmit()}>
          提交
        </Button>
        <Button type="default" onClick={() => onClose()}>
          取消
        </Button>
      </span>
    ),
  };

  return (
    <Modal {...modalProps}>
      <Form>
        <Item label="上级分类" required {...formItemLayout}>
          {getFieldDecorator("parent_id", {
            initialValue: parent_id ? parent_id : parentId,
            rules: [
              {
                required: true,
                message: "请选择上级分类",
              },
            ],
          })(
            <TreeSelect
              disabled={isRowOpera}
              style={{ width: "100%" }}
              dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
              treeData={treeData}
              placeholder="请选择"
              treeDefaultExpandAll
              onChange={(val, label, extra) => onChangeSelect(val, extra)}
            />
          )}
        </Item>
        <Item label="分类名称" required {...formItemLayout}>
          {getFieldDecorator("resource_category_name", {
            initialValue: resource_category_name
              ? resource_category_name
              : null,
            rules: [
              {
                required: true,
                message: "请输入分类名称",
              },
            ],
          })(<Input allowClear placeholder="请输入" maxLength={50} />)}
        </Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(AddModal);