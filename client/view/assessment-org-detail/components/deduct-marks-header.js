import React, { Component } from "react";

export default class DeductMarksHeader extends Component {
  state = { visible: false };

  showModal = () => {
    this.setState({
      visible: true
    });
  };

  handleOk = e => {
    console.log(e);
    this.setState({
      visible: false
    });
  };

  handleCancel = e => {
    console.log(e);
    this.setState({
      visible: false
    });
  };
  render() {
    return (
      <div>
        <Button type="primary" onClick={this.showModal}>
          Open Modal
        </Button>
        <Modal
          title="Basic Modal"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <p><span>考核时间：</span><span>111</span></p>
          <p><span>考核时间：</span><span>111</span></p>
          <p><span>考核时间：</span><span>111</span></p>
          <p><span>考核时间：</span><span>111</span></p>
          <p><span>考核时间：</span><span>111</span></p>
        </Modal>
      </div>
    );
  }
}
