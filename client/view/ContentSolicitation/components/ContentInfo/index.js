import React, { useEffect, useState } from "react";
import moment from "moment";
import { Row, Col, Form, Input, Button, message } from "antd";
import InputTips from "components/activity-form/sub-components/InputTips";
import { saveMyContent } from "apis/content-solicitation";
import ImageUploader from "client/view/questionnaire-new/component/imageUploader";
import "./index.less";

const { Item } = Form;
const { TextArea } = Input;
const contentInfo = (props) => {
  const {
    history,
    dataSource,
    isEdit,
    form: { getFieldValue, getFieldDecorator, validateFields },
  } = props;

  const [imgUrl, setImgUrl] = useState("");

  useEffect(() => {
    setImgUrl(dataSource.url);
  }, [dataSource.url]);

  const onHandleSubmit = () => {
    validateFields((err, values) => {
      if (err) {
        return;
      }
      const params = {
        ...values,
        action: 2,
        url: imgUrl,
        content_personal_id: dataSource.content_personal_id,
      };
      saveMyContent(params).then(({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        message.success("编辑成功！");
        history.goBack();
      });
    });
  };

  return (
    <Form className="content-info">
      <Row>
        <Col span={8}>
          <Item label="提交人">
            <span className="value">{dataSource.name}</span>
          </Item>
        </Col>
        <Col span={8}>
          <Item label="提交时间">
            <span className="value">
              {moment(dataSource.create_time).format("YYYY-MM-DD")}
            </span>
          </Item>
        </Col>
      </Row>
      <Row>
        <Item label="知识标题" required={isEdit}>
          {getFieldDecorator("content_title", {
            initialValue: dataSource.content_title
              ? dataSource.content_title
              : "",
            rules: [
              {
                required: true,
                message: "知识标题不能为空",
              },
            ],
          })(
            <Input
              placeholder={isEdit ? "请输入" : "-"}
              allowClear
              disabled={!isEdit}
            />
          )}
        </Item>
      </Row>
      <Row>
        <Item label="上传图片" required={isEdit}>
          <ImageUploader
            tip="建议图片尺寸为568mm x 378mm"
            value={imgUrl}
            disabled={!isEdit}
            onChange={(img) => setImgUrl(img)}
          />
        </Item>
      </Row>
      <Row>
        <Item label="知识简述" required={isEdit}>
          {isEdit ? (
            <React.Fragment>
              {/* <InputTips max={500} text={getFieldValue("content")}> */}
              {getFieldDecorator("content", {
                initialValue: dataSource.content ? dataSource.content : "",
                rules: [
                  { required: true, message: "知识简述不能为空" },
                  // {
                  //   max: 500,
                  //   message: "输入不能超过500个字",
                  // },
                ],
              })(
                <TextArea
                  autoSize={{ minRows: 8 }}
                  placeholder={"请输入"}
                  allowClear
                />
              )}
              {/* </InputTips> */}
            </React.Fragment>
          ) : (
            <div className="value">{dataSource.content}</div>
          )}
        </Item>
      </Row>
      {isEdit && (
        <Row className="btn">
          <Button type="primary" onClick={() => onHandleSubmit()}>
            确定
          </Button>
          <Button type="default" onClick={() => history.goBack()}>
            取消
          </Button>
        </Row>
      )}
    </Form>
  );
};
export default Form.create()(contentInfo);
