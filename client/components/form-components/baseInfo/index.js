// 简单封装之前的活动基础信息组件
import React from "react";
import emuns from "config/emuns";
import formFields from "config/formFields";
import { Form, message } from "antd";
import {
  ActivityBase
} from "components/activity-form"; //活动表单
import { fetchWorkflowItemInfo } from "apis/activity";
import moment from "moment";
import propTypes from "prop-types";

class BaseInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  //基础表单
  //基础表单
  activityBaseHandle() {
    const { updateState, state } = this.props;
    //图片上传时
    const onUploading = (typeName, info) => { };
    //图片上传完成
    const onUploadSuccess = (typeName, result) => {
      if (result.code !== 0) {
        message.error(result.message);
        return
      }
      const uri = result.data[0].path;
      updateState({
        thumbnail: {
          ...state.thumbnail,
          [typeName]: uri
        }
      });
    };
    //图片商城失败
    const onUploadError = (typeName, result) => { };

    //删除上传图片
    const onUploadDelete = typeName => {
      updateState({
        thumbnail: {
          ...state.thumbnail,
          [typeName]: ""
        }
      });
    };

    //选择活动组织部门
    const changeDepartment = (value, option = {}) => {
      const depName = (option.props || {}).children || "";
      let eventOrganizationDepartment = {};

      //key和name相同，则是输入，反之则是选择
      if (value === depName) {
        eventOrganizationDepartment = {
          id: 0,
          name: value || ""
        };
      } else {
        eventOrganizationDepartment = {
          id: +value,
          name: depName
        };
      }

      updateState({
        eventOrganizationDepartment
      });
    };

    // 选择栏目
    const selectedWorkFlow = async (value, selectedOptions) => {
      //Mark:修改workflow_id 为undefined 的bug
      const workflow_id = selectedOptions[0] && selectedOptions[0][workflow_id];
      if (workflow_id && workflow_id !== -999) {
        const { data } = await fetchWorkflowItemInfo(workflow_id);
        const result = data.data || {};
        const approvalStaff = [];
        (result.users || []).forEach(item => {
          approvalStaff.push({
            name: item.user_name[0],
            user_id: item.user_id[0]
          });
        });
        const CcStaff = [];
        (result.cc || []).forEach(item => {
          CcStaff.push({
            name: item.user_name,
            user_id: item.user_id
          });
        });
        updateState({
          approvalStaff,
          CcStaff,
          approvalType: {
            ...approvalType,
            name: result.name || ""
          }
        });
      }
    };

    return {
      onUploading,
      onUploadSuccess,
      onUploadError,
      onUploadDelete,
      changeDepartment,
      selectedWorkFlow
    };
  }

  //活动时间
  activityTimeEventsHandle() {
    const { form, updateState } = this.props;
    const { getFieldValue } = form;

    const disabledStartDate = startValue => {
      const endValue = getFieldValue(formFields.activityTimeEndTime);
      if (!startValue || !endValue) {
        return false;
      }
      return startValue.valueOf() > endValue.valueOf();
    };

    const disabledEndDate = endValue => {
      const startValue = getFieldValue(formFields.activityTimeStartTime);
      if (!endValue || !startValue) {
        return false;
      }
      return endValue.valueOf() <= startValue.valueOf();
    };

    const handleStartOpenChange = open => {
      if (!open) {
        updateState({
          activityTimeEndOpen: true
        });
      }
    };

    const handleEndOpenChange = open => {
      updateState({
        activityTimeEndOpen: open
      });
    };

    return {
      disabledStartDate,
      disabledEndDate,
      handleStartOpenChange,
      handleEndOpenChange
    };
  }

  render() {
    // console.log(this.props);
    const { form, container, isPreview, data, state } = this.props;
    // console.log(state);
    return (
      <ActivityBase
        form={form}
        emuns={emuns}
        preview={isPreview}
        container={container}
        formFields={formFields}
        activityFormState={state}
        handleEvents={{
          ...this.activityBaseHandle.bind(this)(),
          ...this.activityTimeEventsHandle.bind(this)(),
        }}
      />
    )
  }
}

BaseInfo.propTypes = {
  updateState: propTypes.func,
  container: propTypes.any.isRequired,
  isPreview: propTypes.bool.isRequired,
  data: propTypes.object.isRequired,
  state: propTypes.object
}

BaseInfo.defaultProps = {
  updateState: () => { },
  container: null,
  isPreview: false,
  data: {},
  state: {}
}


export default Form.create(
  // {
  //   onFieldsChange(props, changedFields) {
  //     const { updateState } = props;
  //     let result = {};
  //     for (let key in changedFields) {
  //       if (key === "tags") {
  //         const tagsValue = changedFields[key].value;
  //         // 如果改变的是自定义标记字段，将数组处理为字符串拼接
  //         if (tagsValue) {
  //           result[key] = Array.isArray(tagsValue) ?
  //             tagsValue.join() : tagsValue
  //         }
  //       }
  //       else if (key === "column_id") {
  //         const columnValue = changedFields[key].value;
  //         if (columnValue) {
  //           if (Array.isArray(columnValue)) {
  //             result["activityColumn"] = columnValue;
  //             // 栏目ID取最后一项
  //             result[key] = columnValue[columnValue.length - 1];
  //           }
  //         }
  //       }
  //       // else if (key === "start_time" || key === "end_time") {
  //       //   const timeValue = changedFields[key].value;
  //       //   if (timeValue) {
  //       //     // 2019-01-09 00:00:00
  //       //     result[key] = timeValue.format("YYYY-MM-DD HH:mm:ss");
  //       //   }
  //       // }
  //       else {
  //         result[key] = changedFields[key].value;
  //       }
  //     }
  //     // console.log(result);
  //     updateState(result);
  //   },
  //   mapPropsToFields: (props) => {
  //     // console.log(props, formFields);
  //     const { state } = props;
  //     const result = {};
  //     for (let key in formFields) {
  //       if (formFields[key] === "column_id") {
  //         result[formFields[key]] = Form.createFormField({
  //           name: formFields[key],
  //           value: state["activityColumn"]
  //         });
  //       }
  //       else {
  //         result[formFields[key]] = Form.createFormField({
  //           name: formFields[key],
  //           value: state[formFields[key]]
  //         });
  //       }
  //     }
  //     return result;
  //   }
  // }
)(BaseInfo);