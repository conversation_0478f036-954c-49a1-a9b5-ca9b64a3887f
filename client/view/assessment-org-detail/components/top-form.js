import React from "react";
import "./style.less";
import { Form, Select, Button } from "antd";
import PropTypes from "prop-types";
import moment from "moment";

const FormItem = Form.Item,
  Option = Select.Option;

const TopForm = (props = {}) => {
  const { form, dispatch, org } = props,
  { getFieldDecorator, validateFields, resetFields } = form;
  const renderTimeOption = (type = "year") => {
    let result = [];
    if (type === "year") {
      // 可选起始时间确定为2018年
      const startYear = 2018, nowYear = moment().year(), endYear = nowYear;
      // console.log(nowYear, startYear);
      if (nowYear > startYear) {
        for (let i = startYear; i <= endYear; i++) {
          result.push(i);
        }
      }
      else {
        result.push(startYear);
      }
    }
    else if (type === "quarter") {
      result = [
        { label: "第1季度", value: 13 },
        { label: "第2季度", value: 14 },
        { label: "第3季度", value: 15 },
        { label: "第4季度", value: 16 },
      ]
    }
    // console.log(result);
    return result.map((item, index) => {
      return (
        <Option key={index} value={item.value || item}>
          {item.label || item}
        </Option>
      )
    });
  }

  // 扣分类型. 1 逾期扣分2 审核扣分 3 查处扣分 4 数据采集扣分
  const deductTypes = [
    { label: "逾期扣分", value: 1 },
    { label: "审核扣分", value: 2 },
    { label: "查处扣分", value: 3 },
    { label: "数据采集扣分", value: 4 },
  ];
  return (
    <div className="top-form-wrapper">
      <Form layout="inline" onSubmit={(e) => {
        e.preventDefault();
        validateFields((error, values) => {
          if (!error) {
            // console.log(values);
            dispatch({
              type: "assessmentOrgDetail/save",
              payload: {
                queryParams: values
              }
            });
            if (values.time === -1) {
              delete values.time;
            }
            if (values.deduct_type === -1) {
              delete values.deduct_type;
            }
            dispatch({
              type: "assessmentOrgDetail/getCountDeductLog",
              payload: {
                org_id: org.org_id,
                ...values
              }
            })
          }
        });
      }}>
        <FormItem label="选择时间">
          {
            getFieldDecorator("year", {
              initialValue: org ? moment(`${org.year}`).year() : moment().year()
            })(
              <Select style={{ width: 90 }}>
                {
                  renderTimeOption("year")
                }
              </Select>
            )
          }
        </FormItem>
        <FormItem>
          {
            getFieldDecorator("time", {
              initialValue: -1
            })(
              <Select style={{ width: 150 }}>
                <Option value={-1}>全部</Option>
                {
                  renderTimeOption("quarter")
                }
              </Select>
            )
          }
        </FormItem>
        <FormItem label="扣分类型" style={{ marginLeft: 30 }}>
          {
            getFieldDecorator("deduct_type", {
              initialValue: -1
            })(
              <Select style={{ width: 150 }}>
                <Option value={-1}>全部</Option>
                {
                  deductTypes.map((type, index) => {
                    return (
                      <Option key={index} value={type.value}>{type.label}</Option>
                    )
                  })
                }
              </Select>
            )
          }
        </FormItem>
        <FormItem>
          <Button className="query-btn" type="primary" htmlType="submit">
            查询
          </Button>
          <Button className="query-btn" onClick={() => {
            resetFields();
            const queryParams = {
              year: moment().year(),
              time: -1,
              deduct_type: -1
            }
            dispatch({
              type: "assessmentOrgDetail/save",
              payload: {
                queryParams
              }
            });
            dispatch({
              type: "assessmentOrgDetail/getCountDeductLog",
              payload: {
                org_id: org.org_id,
                year: org ? moment(`${org.year}`).year() : moment().year(),
              }
            });
          }}>
            重置
          </Button>
        </FormItem>
      </Form>
    </div>
  )
}

TopForm.propTypes = {
  dispatch: PropTypes.func,
  org: PropTypes.object,
};
TopForm.defaultProps = {
  dispatch: () => { },
  org: {}
};

export default Form.create()(TopForm);