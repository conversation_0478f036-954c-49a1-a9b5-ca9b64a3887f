import { Button, DatePicker, Input, message } from "antd";
import moment from "moment";
import "moment/locale/zh-cn";
import { useEffect, useState } from "react";
import "./CustomDatePicker.less";

moment.locale("zh-cn");

const { MonthPicker } = DatePicker;

const CustomDatePicker = ({
  placeholder = "请选择日期",
  type = "date",
  value,
  onChange,
  allowClear = true,
  disabled = false,
  className,
  style = {},
}) => {
  // 定义支持的日期格式
  const dateFormats = ["YYYY.MM.DD", "YYYY.M.D", "YYYY-MM-DD HH:mm:ss"];
  const monthFormats = ["YYYY.MM", "YYYY.M", "YYYY-MM"];

  const defaultFormat = type === "date" ? "2024.12.04" : "2024.12";

  const [dateValue, setDateValue] = useState(null);
  const [inputValue, setInputValue] = useState("");

  const parseInputDate = (value) => {
    const formats = type === "date" ? dateFormats : monthFormats;
    for (const format of formats) {
      const date = moment(value, format, true);
      if (date.isValid()) return date;
    }
    return null;
  };

  const syncInputValue = (date) => {
    let formattedValue = "";
    if (date) {
      const month = date.month() + 1; // 月份从0开始
      formattedValue = date.format(month >= 10 ? "YYYY.MM" : "YYYY.M");
    }
    setInputValue(formattedValue);
    return formattedValue;
  };

  const handlePickerChange = (date) => {
    setDateValue(date);
    const formattedValue = syncInputValue(date);
    onChange(formattedValue);
  };

  const handleInputConfirm = () => {
    // 如果输入值为空，直接返回
    if (!inputValue.trim()) {
      onChange('');
      return;
    }
    
    const date = parseInputDate(inputValue);
    if (date) {
      handlePickerChange(date);
    } else {
      // 只有在用户手动输入且格式错误时才提示错误
      if (dateValue === null) {
        message.error(`格式错误，示例：${defaultFormat}`, 3);
      }
    }
  };

  useEffect(() => {
    if (value != null) {
      const date = parseInputDate(value);
      if (date) {
        setDateValue(date);
        syncInputValue(date);
}
    } else {
  setDateValue(null);
  setInputValue("");
}
  }, [value]);

return (
  <div className={className} style={{ display: "inline-block", ...style }}>
    {type === "date" ? (
      <DatePicker
        format={dateFormats}
        placeholder={placeholder}
        value={dateValue}
        allowClear={allowClear}
        disabled={disabled}
        onChange={handlePickerChange}
      />
    ) : (
      <MonthPicker
        format={monthFormats}
        placeholder={placeholder}
        value={dateValue}
        allowClear={allowClear}
        disabled={disabled}
        onChange={handlePickerChange}
        onOpenChange={(open) => {
          if (!open) handleInputConfirm();
        }}
        renderExtraFooter={() => (
          <div className="custom-footer">
            <div className="input-container">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onPressEnter={handleInputConfirm}
                onBlur={handleInputConfirm}
                placeholder="输入年月"
                style={{ width: 120 }}
              />
              <Button type="link" size="small" onClick={handleInputConfirm}>
                确认
              </Button>
            </div>
            <div className="format-hint">示例格式：{defaultFormat}</div>
          </div>
        )}
      />
    )}
  </div>
);
};

export default CustomDatePicker;