// 审批流程组件
import React, { Component } from "react";
import {
  Form,
  AutoComplete,
  Row,
  Col,
  Spin,
  Checkbox,
  message as Message
} from "antd";
// 选择人员组建
import CenterContent from "components/center-content";
import PeopleCover from "components/people-cover";
import adaptiveLayout from "components/activity-form/gridLayout";

import "./index.less";

import { fetchWorkflowList, fetchWorkflowItemInfo, workflowAdd } from "apis/activity";

import propTypes from "prop-types";

const Option = AutoComplete.Option;
// const FormItem = Form.Item;

// 查询流程类型
// /type/list/?name=测试&page=1&pagesize=10
//   [
//     'name': 'string|必填|类型名称|15字以内',
//   'workflow_id': 'number|必填|流程类型id|大于0的11位之内的整数'
//  ]

// 新增审批流程
// 'name': 'string|必填|类型名称|15字以内',
// 'type': 'number|可选|流程类型1.新建，2基于已有流程类型新建的流程类型|不填默认为1，在其他模块调用该接口的时候请传入2',
// 'users': [{ // users array代表每个节点
//   'user_name': ['string array|必填-每个节点审批人至少有一个|审批人员名称|30字以内'],
//   'user_id': ['number array|必填-至少有一个|审批人员id|大于0的11位之内的整数']
// }],
// 'cc': [{
//   'user_name': 'string|必填|抄送人名称|30字以内',
//   'user_id': 'number|必填|抄送人员id|大于0的11位之内的整数'
// }]
// 'type':'number|可选|流程类型1.新建，2基于已有流程类型新建的流程类型|不填默认为1，在其他模块调用该接口的时候请传入2',

class ApprovalProcess extends Component {
  constructor(props) {
    super(props);
    this.state = {
      centerContentVisible: false,
      workflow_name: "",
      workflow_id: "",
      // "must_approve":"int|必填|是否需要审批，0：不需要，1：需要",
      must_approve: 1,
      // 下拉框中工作流列表
      workflow_list: [],
      // 审批人员列表
      users: [],
      // 抄送人员列表
      cc: [],
      // 存储是否变更审批模板，如果变更，则在基础上新增，传入type：2
      isWorkflowTempChange: false,
      // 添加人员操作列表,默认为添加审批人员
      addType: "users",
      // 删除人员操作列表,默认为移除审批人员
      // removeType: "users",
      init: false
    };
    this.autoComplete = null;
    this.getApproveData = this.getApproveData.bind(this);
    this.setApproveData = this.setApproveData.bind(this);
  }


  componentWillMount() {
    this.fetchTypeList();
  }

  componentWillReceiveProps(props) {
    
    if(JSON.stringify(this.props) != JSON.stringify(props)){

      const { workflow_id, workflow_name, must_approve } = props;
      if (workflow_id !== -999) {
        if (must_approve === 0) {
          this.setState({
            must_approve
          });
        } else {
          this.fetchTypeItemInfo(workflow_id);
          this.setState({
            must_approve,
            workflow_id
          });
        }
      } else {
        // form.setFieldsValue({ "workflow_id": workflow_name });
        this.fetchTypeItemInfo(workflow_id);
        this.setState({
          must_approve,
          workflow_id
        });
      }
      if (this.props.workflow_name !== workflow_name && workflow_name !== this.state.workflow_name) {
        this.setState({workflow_name});
      }
    }
  }

  componentDidMount() {
    // 外部有传入值,当不需要审批时，只设置复选框状态
    const { workflow_id, workflow_name, must_approve } = this.props;
    if (workflow_id !== -999) {
      if (must_approve === 0) {
        this.setState({
          must_approve
        });
      } else {
        // form.setFieldsValue({ "workflow_id": workflow_name });
        this.fetchTypeItemInfo(workflow_id);
        this.setState({
          must_approve,
          workflow_name,
          workflow_id
        });
      }
    } else {
      // form.setFieldsValue({ "workflow_id": workflow_name });
      this.fetchTypeItemInfo(workflow_id);
      this.setState({
        must_approve,
        workflow_name,
        workflow_id
      });
    }
  }

  onChange(result) {
    const { fetchWorkflowData } = this.props;
    fetchWorkflowData && fetchWorkflowData(result);
  }

  // 获取审批数据，用于草稿功能
  getApproveData() {
    const { must_approve,  workflow_name, users, cc, workflow_id  } = this.state;
    return { must_approve, workflow_name, users, cc, workflow_id };
  }

  // 提取数据后用于 回显数据
  setApproveData(data) {
    const { workflow_id } = data;
    if (workflow_id === -999 || workflow_id == "") {
      this.fetchTypeItemInfo(workflow_id);
    }
    this.setState(data);
  }

  async exportData(disabledErrorMessage = false, ) {//不提示错误消息
    const { fetchWorkflowData } = this.props;
    const { workflow_id, workflow_name, must_approve, isWorkflowTempChange, users, cc } = this.state;
    let result = {};
    if (must_approve === 0) {
      // console.log("不需要审批");
      result = { must_approve };
    } else {
      // 如果模板改动过
      if (!workflow_name) {
        if(!disabledErrorMessage){
          Message.error("请输入审批类型名字");
        }
        return;
      }
      if (!users || users.length === 0) {
        if(!disabledErrorMessage){
          Message.error("请选择审批人员");
        }
        return;
      }

      if (!workflow_id) {
        // console.log("新增全新审批流程");
        const response = await workflowAdd({ name: workflow_name, type: 2, users, cc });
        const { data: body } = response;
        const { code, data, message } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        result = { must_approve, workflow_id: data.workflow_id, workflow_name: data.name }
      } else {
        // console.log(isWorkflowTempChange);
        if (isWorkflowTempChange) {
          // console.log("在已有基础上新增", workflow_id);
          const response = await workflowAdd({ name: workflow_name, type: 2, users, cc });
          const { data: body } = response;
          const { code, data, message } = body;
          if (code !== 0) {
            Message.error(message);
            return;
          }
          result = { must_approve, workflow_id: data.workflow_id, workflow_name: data.name }
        } else {
          // console.log("选择已有流程", workflow_id);
          result = { must_approve, workflow_id, workflow_name };
        }
      }
    }
    fetchWorkflowData && fetchWorkflowData(result);
    return result;
  }

  showCenterContent() {
    this.setState({
      centerContentVisible: true
    });
  }
  hideCenterContent() {
    this.setState({
      centerContentVisible: false
    });
  }

  // 清空的时候处理方法
  clearHandler() {
    // const { workflow_name } = this.state;
    // console.log("清空", workflow_name);
    this.setState({
      workflow_name: ""
    });
    this.onChange({
      workflow_name: ""
    });
  }

  // 选中列表中的某个工作流
  selectItem(value, option) {
    const { props } = option;
    const { data } = props;
    // console.log("选中", value, data);
    this.setState({
      isWorkflowTempChange: false,
      workflow_id: data.workflowId || -999,
      workflow_name: data.name || ""
    });
    this.onChange({
      workflow_id: data.workflowId || -999,
      workflow_name: data.name || ""
    });
    this.fetchTypeItemInfo(data.workflowId);
  }

  // 按关键字查询工作流程类型
  searchList(value) {
    this.onChange({
      workflow_name: value
    });
    this.fetchTypeList(value);
    // 当前有记录workflow_id，并在其基础上修改
    const { isWorkflowTempChange, workflow_id } = this.state;
    if (!isWorkflowTempChange && workflow_id) {
      this.setState({
        isWorkflowTempChange: true
      });
    }
  }

  // 变更是否需要审批状态
  mustApprovalStatusChange(checked) {
    // console.log(checked);
    if (checked) {
      // console.log("不要审批");
      // const { form } = this.props;
      // const { setFieldsValue } = form;
      // setFieldsValue({ "workflow_id": "" });
      this.setState({
        isWorkflowTempChange: false,
        must_approve: 0,
        workflow_id: "",
        workflow_name: "",
        users: [],
        cc: []
      });
      this.onChange({
        must_approve: 0,
        workflow_id: "",
        workflow_name: "",
      });
    } else {
      // 重新加载下拉列表数据
      this.setState({
        must_approve: 1
      }, () => {
        this.fetchTypeList();
      });
      this.onChange({
        must_approve: 1
      });
    }
  }

  // 变更之后调用的方法
  changeHandler() {
    const { form } = this.props;
    const { validateFields, getFieldValue } = form;
    validateFields((error, values) => {
      if (!error) {
        const workflow_id = getFieldValue("workflow_id");
        const { workflow_name, users, cc, isWorkflowTempChange } = this.state;
        // console.log(workflow_name);
        // console.log(values, workflow_name, workflow_id, users, cc, isWorkflowTempChange);
      }
    });
  }

  removePerson(listType, index, person) {
    // console.log("移除", listType, index, person);
    if (listType === "users") {
      const { users } = this.state;
      users.splice(index, 1);
      this.setState({
        users
      });
    } else if (listType === "cc") {
      const { cc } = this.state;
      cc.splice(index, 1);
      this.setState({
        cc
      });
    }
    // 当前有记录workflow_id，并在其基础上修改
    const { isWorkflowTempChange, workflow_id } = this.state;
    if (!isWorkflowTempChange && workflow_id) {
      this.setState({
        isWorkflowTempChange: true
      });
    }
  }

  // 获取工作流程类型
  async fetchTypeList(name) {
    const response = await fetchWorkflowList(name);
    if(!response){return}
    const { data: body } = response || {};
    const { code=-1, data=[], message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    this.setState({
      workflow_list: data
    })
  }
  // 获取工作流程详细信息
  async fetchTypeItemInfo(workflowId = -999) {
    if (workflowId === -999 || workflowId === "") {
      this.setState({
        users: [],
        cc: []
      });
      return;
    }
    const response = await fetchWorkflowItemInfo(workflowId);
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message);
      this.setState({
        users: [],
        cc: []
      });
      return;
    }
    this.setState({
      users: data.users || [],
      cc: data.cc || []
    }, () => {
      // this.changeHandler();
    });
  }
  render() {
    const { centerContentVisible, users, cc, workflow_list, workflow_id, workflow_name, must_approve } = this.state;
    const { preview, labelSpan, wrapperSpan, isLabel } = this.props;
    // form
    // const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const centerContentProps = {
      // 控制是否收起 true 为收起
      visible: centerContentVisible,
      //提交确认，返回值
      handleOk: (outlist, selectChange) => {
        // console.log("人员选择组件centerContent导出数据", outlist, selectChange);
        if ((outlist && Array.isArray(outlist) && outlist.length !== 0) && selectChange) {
          const { users, cc, addType } = this.state;
          outlist.forEach((person, index) => {
            if (addType === "users") {
              // console.log(users);
              users.push({
                user_name: [person.name],
                user_id: [person.user_id]
              });

            } else if (addType === "cc") {
              // console.log(cc);
              cc.push({
                user_name: person.name,
                user_id: person.user_id
              });
            }
          });
        }
        // 当前有记录workflow_id，并在其基础上修改
        const { isWorkflowTempChange, workflow_id } = this.state;
        if (!isWorkflowTempChange && workflow_id) {
          this.setState({
            isWorkflowTempChange: true
          });
        }
        this.setState({
          centerContentVisible: false
        });
      },
      //关闭显示，取消操作
      handleCancel: () => {
        this.setState({
          centerContentVisible: false
        });
      },
      //当前弹框的题目 选择人员
      // title: PropTypes.string,
      //当前弹框的宽度  600
      // width: PropTypes.number,
      //控制选择人员是单选还是多选  true是多选
      singleSlec: true
    }

    const componentResult = (
      <div className="approval-process-header">
        <AutoComplete
          ref={(ref) => this.autoComplete = ref}
          allowClear={!preview && must_approve !== 0}
          value={workflow_name}
          // defaultValue={workflow_name}
          defaultActiveFirstOption={false}
          disabled={preview || must_approve === 0}
          placeholder="搜索审批类型名字"
          style={{ width: 200, marginRight: 30 }}
          filterOption={false}
          onChange={(value) => {
            if (!value) {
              // 表示点击清空按钮
              this.clearHandler();
            }
            this.setState({workflow_name: value});
          }}
          onSearch={(value) => { this.searchList(value); }}
          onSelect={(value, option) => { this.selectItem(value, option); }}
        >
          {
            workflow_list.map((item) => {
              return (
                <Option key={item.workflowId} data={item}>{item.name}</Option>
              )
            })
          }
        </AutoComplete>
        <Checkbox disabled={preview} checked={must_approve === 0} onChange={(e) => {
          const target = e.target;
          const checked = target.checked;
          this.mustApprovalStatusChange(checked);
        }}>不要审批</Checkbox>
      </div>
    );

    return (
      <div className={`approval-process-container ${!isLabel ? 'approval-process-no-container' : ''}`}>
        {isLabel ? (
          <Row className="ant-form-item">
            <Col span={labelSpan} className="ant-form-item-label">
              <label title="审批类型">审批类型</label>
            </Col>
            <Col span={wrapperSpan} style={{ paddingTop: 5 }}>{componentResult}</Col>
          </Row>
        ) : componentResult}
        {
          // !getFieldValue("must_approval") &&
          !!must_approve &&
          <div className="approval-person-info">
            <Row className="ant-form-item">
              <Col span={labelSpan} className="ant-form-item-label">
                <label title="审批人员">审批人员</label>
              </Col>
              <Col span={wrapperSpan} className="approval-process">
                {
                  users.map((turn, index) => {
                    if (turn.user_id && Array.isArray(turn.user_id) && turn.user_id.length !== 0) {
                      return turn.user_id.map((user_id, i) => {
                        return (
                          <PeopleCover
                            key={user_id}
                            name={turn.user_name[i]}
                            disabled={preview}
                            onClose={() => {
                              this.removePerson("users", index, turn);
                            }}
                          />
                        )
                      });
                    }
                    return null;
                  })
                }
                <PeopleCover
                  isAdd={true}
                  disabled={preview}
                  onAdd={() => {
                    this.setState({
                      addType: "users"
                    });
                    this.showCenterContent();
                  }} />
              </Col>
            </Row>
            <Row className="ant-form-item">
              <Col span={labelSpan} className="ant-form-item-label">
                <label title="抄送人员">抄送人员</label>
              </Col>
              <Col span={wrapperSpan}>
                {
                  cc.map((item, index) => {
                    return (
                      <PeopleCover
                        key={index}
                        name={item.user_name}
                        disabled={preview}
                        onClose={() => {
                          this.removePerson("cc", index, item);
                        }}
                      />
                    )
                  })
                }
                <PeopleCover
                  isAdd={true}
                  disabled={preview}
                  onAdd={() => {
                    this.setState({
                      addType: "cc"
                    });
                    this.showCenterContent();
                  }} />
              </Col>
            </Row>
          </div>
        }
        {/* 选取人员插件 */}
        <CenterContent {...centerContentProps} />
      </div >
    )
  }
}

ApprovalProcess.propTypes = {
  isLabel: propTypes.bool,
  labelSpan: propTypes.number,
  wrapperSpan: propTypes.number,
  workflow_id: propTypes.number,
  workflow_name: propTypes.string,
  must_approve: propTypes.number,
  preview: propTypes.bool,
  fetchWorkflowData: propTypes.func
};

ApprovalProcess.defaultProps = {
  isLabel: true,
  labelSpan: 3,
  wrapperSpan: 15,
  workflow_id: -999,
  workflow_name: "",
  must_approve: 0,
  preview: false,

  fetchWorkflowData: (data) => {
    // console.log("审批数据为", data);
  }
};

export default ApprovalProcess;
// export default Form.create()(ApprovalProcess);
