import React, { Component } from "react";
import { message, Form, Input, Select, TreeSelect, Button, Modal } from "antd";
import { http } from "client/tool/axios";
import { fileHost } from "apis/config";
import "./index.less";
import View from "./index-view";
import merge from "merge";
import { uploadBase64File } from "apis/file";
import ApprovalProcess from "components/approval-process";



class CreateNews extends Component {
  constructor() {
    super();
    this.state = {
      param: {
        news_id: "",
        title: "",
        sub_title: "",
        show_type: 1,
        news_column_id: "",
        sub_column: [],
        source: "",
        keywords: "",
        summary: "",
        content: ""
      },
      files_type2: [],
      files_type3: [],
      files_type4: [],
      newsColumn: [],
      status: 0, // 0为新建, 1为修改
      preview_list: false,
      preview_article: false,
      loading: false,
      preview_list_data: "",

      workflow_id: -999,
      workflow_name: '',
      must_approve: 0,

      isPreview: false,
      record: null,
      tabsKey: 1,
      status: null
    };
    this.handleSubmit = this._handleSubmit.bind(this);
    this.cloumnFormat = this._cloumnFormat.bind(this);
    this.columnOnChange = this._columnOnChange.bind(this);
    this.editOnChange = this._editOnChange.bind(this);
    this.sub_columnOnChange = this._sub_columnOnChange.bind(this);
    this.clipEvent = this._clipEvent.bind(this);
    this.preview_list = this._preview_list.bind(this);
    this.preview_article = this._preview_article.bind(this);
    this.closePreview = this._closePreview.bind(this);

    this.fetchWorkflowData = this.fetchWorkflowData.bind(this);
  }
  async componentDidMount() {
    await this.getColumn();
    const { history } = this.props;
    const { id, action, record, tabsKey, status } = history.location.query || {};

    this.setState({
      action,
      isPreview: action === 'view'
    })

    if (id) {
      await this.findNews(id);
      this.setState({
        record, tabsKey, status
      });
    }
  }


  hanlderSaveDraft(e) {
    const self = this;
    e.preventDefault();
    this.setState({
      loading: true
    });
    this.props.form.validateFields((err, values) => {
      if (!this.state.param.news_column_id) {
        message.error("请选择文章主栏目");
        return;
      }
      if (!err) {
        self.createNews(values, true);//true代表草稿
      }
    });
  }

  _handleSubmit(e) {
    e.preventDefault();
    this.setState({
      loading: true
    });
    this.props.form.validateFields((err, values) => {
      if (!this.state.param.news_column_id) {
        message.error("请选择文章主栏目");
        return;
      }
      if (!err) {
        if(this.state.status === 2){
          let m = Modal.confirm({
              centered: true,
              title: "此次编辑将会撤回之前的审批，并重新发起新的审批，是否提交？",
              onOk: () => {
                  this.createNews(values);
              },
              onCancel: () => {
                  m.destroy();
              }
          });
        } else {
          this.createNews(values);
        }
      }
    });
  }
  _clipEvent(type, index) {
    const self = this;
    const upload = base64 => {
      return new Promise((r, j) => {
        uploadBase64File({
          upfile: base64,
          upType: "image"
        })
          .then(data => {
            const _data = data.data;
            if (_data.code != 0) {
              message.error(_data.message);
              j(_data.message);
              return;
            } else {
              const i = index ? index : 0;
              const images = self.state[`files_type${type}`];
              if (images[i] && images[i].url) {
                images[i].url = _data.data.path;
              } else {
                images[i] = { url: _data.data.path };
              }
              self.setState({
                [`files_type${type}`]: images
              }, () => {
                // console.log(this.state)
              });
              r("成功");
            }
          })
          .catch(e => j(e));
      });
    };
    return {
      upload
    };
  }
  _columnOnChange(e) {
    const param = Object.assign({}, this.state.param);
    param.news_column_id = e;
    this.setState({ param: param });
  }
  _sub_columnOnChange(e) {
    const param = Object.assign({}, this.state.param);
    param.sub_column = e;
    this.setState({ param: param });
  }
  async getColumn() {
    const self = this;
    const data = await http.get(`${fileHost}/news-column/find-all`);
    var _data = data.data;
    if (_data.code !== 0) {
      message.error(_data.message);
    }
    var list = self._cloumnFormat(_data.data);
    self.setState({
      newsColumn: list
    });
  }
  _cloumnFormat(data) {
    const self = this;
    if (!data) {
      return null;
    }
    const node = data.map((v, i) => {
      if (v.children) {
        return {
          label: v.name,
          value: v.news_column_id + "",
          key: v.news_column_id + "",
          children: self._cloumnFormat(v.children)
        };
      }
      return {
        value: v.news_column_id + "",
        label: v.name,
        key: v.news_column_id
      };
    });
    return node;
  }


  fetchWorkflowData(data) {
    // console.log("fetchWorkflowData::", data);

    this.setState(data)
  }

  async createNews(param, draft = false) {

    const self = this;

    let approvalData = self.state;

    if (this.approvalProcess) {
      approvalData = await this.approvalProcess.exportData();
    }
    // console.log(approvalData);

    // .then(()=>{
    //   const {workflow_id, workflow_name, must_approve} = self.state;
    //   console.log("B:", workflow_id, workflow_name, must_approve)
    // }).catch().finally()

    const { workflow_id, workflow_name, must_approve } = approvalData;

    // console.log("A:", workflow_id, workflow_name, must_approve);
    // return false;
    //
    //
    const { history } = this.props;
    Object.assign(param, {
      organization_id: window.sessionStorage.getItem("_oid"),
      organization_name: window.sessionStorage.getItem("_on")
    });
    param.news_column_id = +this.state.param.news_column_id;
    if (this.state.param.sub_column) {
      var sub_column = "";
      param.sub_column = this.state.param.sub_column.forEach(v => {
        sub_column += v.value + ",";
      });
      param.sub_column = sub_column.slice(0, sub_column.length - 1);
    }
    if (param.show_type === 2) {
      param.imgs = this.state.files_type2;
      if (this.state.files_type2.length === 0) {
        message.error("请上传列表缩略图");
        return;
      }
    } else if (param.show_type === 3) {
      param.imgs = this.state.files_type3;
      if (this.state.files_type3.length !== 3) {
        message.error("请上传3张略表缩略图");
        return;
      }
    } else if (param.show_type === 4) {
      param.imgs = this.state.files_type4;
      if (this.state.files_type4 === 0) {
        message.error("请上传列表缩略图");
        return;
      }
    }
    if (this.state.param.news_id) {
      param.news_id = this.state.param.news_id;
    }
    if (!this._view.getContent()) {
      message.error("请编辑内容");
      return;
    }
    param.source = encodeURIComponent(param.source);
    param.content = encodeURIComponent(this._view.getContent());
    param.title = encodeURIComponent(param.title);
    param.sub_title = encodeURIComponent(param.sub_title);
    param.summary = encodeURIComponent(param.summary);
    param.keywords = encodeURIComponent(param.keywords);

    // 必须审批
    if (this.state.must_approve === 1) {
      param.workflow_id = workflow_id;
    } else {
      param.workflow_id = -999
    }
    param.workflow_name = workflow_name;

    let fetch = this.state.param.news_id ? "update" : "add";
    if (draft) {
      fetch = "draft";
    }

    http
      .post(`${fileHost}/news/${fetch}`, param)
      .then(data => {
        var _data = data.data;
        if (_data.code !== 0) {
          message.error(_data.message);
          return;
        }
        history.push("/news-list", {
          record: this.state.record,
          tabsKey: this.state.tabsKey
        });
        // 发布成功后需要引导history.back(), 跳转到新闻列表页面
      })
      .catch(e => {
        message.error("发布文章失败");
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }
  findNews(id) {
    const self = this;
    http
      .get(`${fileHost}/news/find-id/${id}`)
      .then(data => {
        const _data = data.data;
        const sub_column = [];
        // console.log(data);
        if (_data.code !== 0) {
          message.error(_data.message);
        }
        if (_data.data.sub_column) {
          _data.data.sub_column = _data.data.sub_column.split(",");
          _data.data.sub_column.forEach(v => {
            loop_sub_column(self.state.newsColumn, v, sub_column);
          });
          _data.data.sub_column = sub_column;
        }
        const show_type = _data.data.show_type;
        _data.data.source = decodeURIComponent(_data.data.source);
        _data.data.content = decodeURIComponent(_data.data.content);
        _data.data.title = decodeURIComponent(_data.data.title);
        _data.data.sub_title = decodeURIComponent(_data.data.sub_title);
        _data.data.summary = decodeURIComponent(_data.data.summary);
        _data.data.keywords = decodeURIComponent(_data.data.keywords);
        self.setState({
          param: merge(self.state.param, _data.data),
          [`files_type${show_type}`]: _data.data.imgs
        });
      })
      .catch(e => {
        // console.log(e);
        message.error("查找新闻内容失败");
      });
    function loop_sub_column(list, id, sub_column) {
      list.forEach(v => {
        if (v.value === id) {
          sub_column.push({
            value: v.value,
            label: v.label
          });
        }
        if (v.children) {
          loop_sub_column(v.children, id, sub_column);
        }
      });
    }
  }
  _editOnChange(e) {
    const param = this.state.param;
    param.content = e;
    // this.props.form.setFieldsValue({ content: e });
    this.setState({
      param
    });
  }
  _preview_list() {
    const { getFieldValue } = this.props.form;
    var data = "";
    var show_type = getFieldValue("show_type");
    var imgs = this.state[`files_type${show_type}`]
      ? this.state[`files_type${show_type}`]
      : [];
    imgs.forEach(i => {
      i.type = show_type;
    });
    data += "title=" + getFieldValue("title");
    data += "&sub_title=" + getFieldValue("sub_title");
    data += "&source=" + getFieldValue("source");
    data += "&showtype=" + show_type;
    data += "&imgs=" + JSON.stringify(imgs);
    this.setState({
      preview_list: true,
      preview_list_data: encodeURIComponent(data)
    });
  }
  _preview_article() {
    const { getFieldValue } = this.props.form;
    var data = "";
    var content = this._view.getContent();
    data += "title=" + getFieldValue("title");
    data += "&sub_title=" + getFieldValue("sub_title");
    data += "&source=" + getFieldValue("source");
    data += "&content=" + content;
    this.setState({
      preview_article: true,
      preview_article_data: encodeURIComponent(data)
    });
  }
  _closePreview() {
    this.setState({
      preview_article: false,
      preview_list: false
    });
  }
  render() {
    const { history } = this.props;
    const { id } = history.location.query || {};
    const formItemLayout = {
      labelCol: {
        xs: { span: 3 },
        sm: { span: 3 }
      },
      wrapperCol: {
        xs: { span: 21 },
        sm: { span: 21 }
      }
    };
    const container = () => document.getElementById("create-news");


    const approvalProcessProps = {
      preview: true,
      isLabel: false,
      ref: (ref) => {
        this.approvalProcess = ref;
      },
      workflow_id: this.state.param.workflow_id,
      workflow_name: this.state.param.workflow_name,
      must_approve: this.state.param.must_approve,
      fetchWorkflowData: this.fetchWorkflowData.bind(this)
    }

    const props = {
      ApprovalProcess,
      approvalProcessProps,
      // approvalProcessHandler: this.approvalProcessHandler.bind(this),
      container: container,
      form: this.props.form,
      history: history,
      newsColumn: this.state.newsColumn,
      formItemLayout: formItemLayout,
      news_column_id: this.state.param.news_column_id,
      sub_column: this.state.param.sub_column,
      state: Object.assign({}, this.state),
      setState: this.setState.bind(this),
      _edit: this._edit,
      isEdit: !!id,
      action: this.state.action,
      isPreview: this.state.isPreview,
      files_type2: this.state.files_type2,
      files_type3: this.state.files_type3,
      files_type4: this.state.files_type4,
      preview_list: this.state.preview_list,
      preview_list_data: this.state.preview_list_data,
      preview_article: this.state.preview_article,
      preview_article_data: this.state.preview_article_data,
      hanlderSaveDraft: this.hanlderSaveDraft.bind(this),
      events: {
        handleSubmit: this.handleSubmit,
        columnOnChange: this.columnOnChange,
        editOnChange: this.editOnChange,
        sub_columnOnChange: this.sub_columnOnChange,
        clipEvent: this.clipEvent,
        show_preview_list: this.preview_list,
        show_preview_article: this.preview_article,
        closePreview: this.closePreview
      },
    };
    return <View {...props} ref={e => (this._view = e)} />;
  }
}

export default Form.create()(CreateNews);
