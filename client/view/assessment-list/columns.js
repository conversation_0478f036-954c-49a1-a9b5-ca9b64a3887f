import React from 'react';
import {Popconfirm} from 'antd';
import ArrowLabel from 'client/components/arrow-label';
import previewPath from './preview-path';

export const columnsA = ({history, onPreview, onDelete}) => [
	{
		title: '互动标题',
		align: 'center',
		dataIndex: 'title'
	},
	{
		title: '状态',
		align: 'center',
		render: ({status}) => {
			const map = {
				1: {text: '活动中', theme: 'green'},
				2: {text: '审批中', theme: 'orange'},
				3: {text: '未通过', theme: 'gray'},
				4: {text: '已结束', theme: 'gray'},
				5: {text: '未开始', theme: 'blue'},
				6: {text: '草稿', theme: 'gray'},
				7: {text: '未启动', theme: 'blue'}
			};
			const {text, theme} = map[status];
			return <ArrowLabel theme={theme}>{text}</ArrowLabel>
		},
		width: 150
	},
	{
		title: '互动时间',
		align: 'center',
		width: 200,
		render: ({start_time, end_time}) => (
			<span>
				{start_time}至{end_time}
			</span>
		)
	},
	{
		title: '需求量与组织量',
		align: 'center',
		dataIndex: 'required_join_org_count',
		width: 150
	},
	{
		title: '实际参与组织数量',
		align: 'center',
		dataIndex: 'actually_join_org_count',
		width: 150
	},
	{
		title: '操作',
		align: 'center',
		width: 250,
		render: ({status, type, create_user, activity_id, type_id}) => {
			return (
				<span>
					{status !== 6 && (
						<a onClick={() => onPreview && onPreview(previewPath({
							type,
							type_id,
							activity_id
						}))}>手机预览</a>
					)}
					{status === 6 && (
						<a
							style={{paddingLeft: 10}}
							onClick={()=>history.push({pathname:'/questionnaire-new', query: {activity_id, isPreview: false}})}
						>编辑</a>
					)}
					{status !== 6 && (
						<a
							style={{paddingLeft: 10}}
							onClick={()=>history.push({pathname:'/questionnaire-new', query: {activity_id, isPreview: true}})}
						>查看</a>
					)}
					<Popconfirm
						title="是否确认删除？"
						onConfirm={() => onDelete && onDelete({status, create_user, type, activity_id})}
					>
						<a style={{paddingLeft: 10}}>删除</a>
					</Popconfirm>
					{(status !== 6 && status !== 2) && (
						<a
							style={{paddingLeft: 10}}
							onClick={() => history.push('/activity-situation/'.concat(activity_id))}
						>互动情况</a>
					)}
			</span>
			)
		}
	}
];

export const columnsB = ({history, onPreview, onStart}) => [
	{
		title: '互动标题',
		align: 'center',
		dataIndex: 'title',
		width: 360
	},
	{
		title: '状态',
		align: 'center',
		width: 150,
		render: ({status}) => {
			const map = {
				1: {text: '活动中', theme: 'green'},
				2: {text: '审批中', theme: 'orange'},
				3: {text: '未通过', theme: 'gray'},
				4: {text: '已结束', theme: 'gray'},
				5: {text: '未开始', theme: 'blue'},
				6: {text: '草稿', theme: 'gray'},
				7: {text: '未启动', theme: 'blue'}
			};
			const {text, theme} = map[status];
			return <ArrowLabel theme={theme}>{text}</ArrowLabel>
		}
	},
	{
		title: '互动时间',
		align: 'center',
		width: 380,
		render: ({start_time, end_time}) => (
			<span>
				{start_time}至{end_time}
			</span>
		)
	},
	{
		title: '操作',
		align: 'center',
		width: 380,
		render: ({start_time, end_time, status, type_id, type, activity_id, org_id}) => {
			const {_oid} = window.sessionStorage;
			return (
				<span>
					<a onClick={() => onPreview && onPreview(previewPath({
						type,
						type_id,
						activity_id
					}))}>手机预览</a>
					<a
						style={{paddingLeft: 10, display: 'inline-block', width: 50}}
						onClick={() => onStart && onStart({start_time, end_time, status, type_id, type, activity_id})}
					>{status === 7 ? '启动' : ''}</a>
					<a
						style={{paddingLeft: 10}}
						onClick={() => history.push({
							pathname: `/questionnaire-survey/${activity_id}?is_three=${_oid !== '3'}&activity_type=appraise&org_id=${org_id}`,
						})}
					>互动情况</a>
				</span>
			)
		}
	}
];

