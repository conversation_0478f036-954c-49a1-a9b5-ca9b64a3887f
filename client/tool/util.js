import { menus } from "config/map-menu";
import { Component } from "react";
import { createBrowserHistory } from "history";
import axios from "./axios";
import moment from "moment";
export const fetchStorage = (from = 1, key) => {
  if (from === 1 || from === 2) {
    let type = "localStorage";
    if (from === 2) {
      type = "sessionStorage";
    }
    return typeof window !== "undefined" ? window[type][key] : "";
  }
};

export const randomString = (len) => {
  len = len || 32;
  let $chars =
    "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  let maxPos = $chars.length;
  let pwd = "";
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
};
export const dataIsNull = (value) => {
  return (
    value === undefined ||
    value === null ||
    String(value).toLocaleLowerCase() === "null" ||
    value === ""
  );
};

const getIconMap = () => {
  const map = {
    ppt: ["gsg-ppt", "#f1583e"],
    pptx: ["gsg-ppt", "#f1583e"],
    txt: ["gsg-txt", "#005e9c"],
    png: ["gsg-png", "#f1a02e"],
    jpg: ["gsg-jpg", "#78c5ee"],
    jpeg: ["gsg-jpg", "#78c5ee"],
    mp4: ["gsg-mp1", "#f16690"],
    mp3: ["gsg-mp", "#8252b1"],
    mov: ["gsg-mov", "#f16690"],
    mkv: ["gsg-mkv", "#f16690"],
    doc: ["gsg-doc", "#00aeef"],
    docx: ["gsg-doc", "#00aeef"],
    xls: ["gsg-ex", "#00b48d"],
    xlsx: ["gsg-ex", "#00b48d"],
    flv: ["gsg-flv", "#f16690"],
    avi: ["gsg-avi", "#f16690"],
  };

  return (extname) => {
    return map[extname] || ["gsg-wenjian", "#00b48d"];
  };
};
export const getIcon = getIconMap();

// update dingxing 2018-05-10 16:04
/**
 * 脱敏电话号码
 * @param {*} phone 电话号码
 */
export const desensitizationPhone = (phone) => {
  if (!phone || typeof phone !== "string") {
    return "";
  }
  const reg = /^(\d{3})(\d{4})(\d{4})$/;
  if (reg.test(phone)) {
    return phone.replace(/^(\d{3})(\d{4})(\d{4})$/, (matched, p1, p2, p3) => {
      return p1 + "****" + p3;
    });
  }
  return phone;
};

// uuid Generate
export const guid = () => {
  const s4 = () => {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  };
  return (
    s4() +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    s4() +
    s4()
  );
};

// 给table数据添加key属性
export const addKeyToTableDataSource = (data) => {
  // console.log(data);
  // console.log(Object.prototype.toString.call(data));
  if (
    (Array.isArray(data) && data.length !== 0) ||
    Object.prototype.toString.call(data) === "[object Array]"
  ) {
    data.map((item, index) => {
      return (item.key = ++index);
    });
    return data;
  } else if (Object.prototype.toString.call(data) === "[object Object]") {
    data.key = 1;
    return [data];
  }
  return data;
};

// 格式化数字，适用于计算后数字接口，精度出现舍入误差的情况（金钱等）
/*
  input:传入的数字或字符串
  output:输出的精确到两位小数字符串
*/
export const formatNumber = (input) => {
  // isNaN处理空串时,采取Number,将空串转换为0；
  // 匹配输入首位非空字符必须为数字（之前可以包含任意数量空格）;
  const reg = /^[\s]*\d[\s\S]*/;
  let output = input;
  if (reg.test(input)) {
    output = parseFloat(input);
    return output.toFixed(2);
  }
  return NaN;
};

// 数组去重
export const unique = (array) => {
  var res = array.filter(function (item, index, array) {
    return array.indexOf(item) === index;
  });
  return res;
};

//文件大小转化B, KB， MB， GB
export const converFileSize = (limit) => {
  limit = parseFloat(limit);
  let size = "";
  if (limit < 0.1 * 1024) {
    //如果小于0.1KB转化成B
    size = limit.toFixed(2) + "B";
  } else if (limit < 0.1 * 1024 * 1024) {
    //如果小于0.1MB转化成KB
    size = (limit / 1024).toFixed(2) + "KB";
  } else if (limit < 0.1 * 1024 * 1024 * 1024) {
    //如果小于0.1GB转化成MB
    size = (limit / (1024 * 1024)).toFixed(2) + "MB";
  } else {
    //其他转化成GB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }

  var sizestr = size + "";
  var len = sizestr.indexOf(".");
  var dec = sizestr.substr(len + 1, 2);
  if (dec == "00") {
    //当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
  }
  return sizestr;
};

export const numberToChinese = (number) => {
  const chnNumChar = [
    "零",
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
  ];
  const chnUnitChar = ["", "十", "百", "千"];
  let strIns = "",
    chnStr = "";
  let unitPos = 0;
  let zero = true;
  while (number > 0) {
    let v = number % 10;
    if (v === 0) {
      if (!zero) {
        zero = true;
        chnStr = chnNumChar[v] + chnStr;
      }
    } else {
      zero = false;
      strIns = chnNumChar[v];
      strIns += chnUnitChar[unitPos];
      chnStr = strIns + chnStr;
    }
    unitPos++;
    number = Math.floor(number / 10);
  }
  return chnStr;
};

//  获取url params
export const getParam = () => {
  var qs = (function (a) {
    if (a == "") return {};
    var b = {};
    for (var i = 0; i < a.length; ++i) {
      var p = a[i].split("=");
      if (p.length != 2) continue;
      b[p[0]] = decodeURIComponent(p[1].replace(/\+/g, " "));
    }
    return b;
  })(window.location.search.substr(1).split("&"));
  return qs;
};

/**
 * 获取url的参数对象
 */
export const getQuery = (href) => {
  const url = href || location.href;
  const query = {};
  if (url.indexOf("?") !== -1) {
    const queryStr = url.split("?")[1];
    queryStr.split("&").forEach((item) => {
      const itemStrs = item.split("=");
      query[itemStrs[0]] = itemStrs[1];
    });
  }
  return query;
};
// 获取url参数
export const getUrlQuery = (url) => {
  const _query = new URLSearchParams((url || location.href).split("?")[1]);

  const query = {};

  for (var pair of _query.entries()) {
    query[pair[0]] = pair[1];
  }

  return query;
};
/**
 * 根据传入的key删除url的某些参数
 * @key Array
 */
export const delQuery = (keys, href) => {
  const url = href || location.href;
  if (url.indexOf("?") === -1) {
    return url;
  }
  const queryStrs = [];
  const query = getQuery(url);
  keys.forEach((key) => {
    delete query[key];
  });
  Object.keys(query).forEach((k) => {
    queryStrs.push(`${k}=${query[k]}`);
  });
  return `${url.split("?")[0]}?${queryStrs.join("&")}`;
};
/**
 * 数据对象映射 Object map（键改名）
 * 用于 后端接口返回数据 和 前端数据结构或字段名称不一致，互相转换问题
 * 类似于解构赋值
 *
 * <AUTHOR>
 * @param {object} obj     原始数据
 * @param {object} mapRule 映射规则
 *              只返回定义了规则的数据，没有定义的数据会丢失，即使有些数据的键值都没有变化，你也要定义规则
 *              规则结构 {key:'new_key', value:new_value, [mapArray:true]}
 *              {string}       key       新的键名
 *              {any|function} value     当any时直接当新值使用   当function时以函数返回值作为新值使用参数（当前值，obj）
 *              {[boolean]}    mapArray  可选。作为数组遍历时使用此字段，此时value必为function并且参数有（当前数组元素, 当前索引, 当前数组, obj）
 * @return {object} 映射后的数据
 *
 * eg.
 *
 * 基本需求： {a:'hello', b:'world', c:111}  =>  {new_a:'hello', new_b:'world', new_cccc:111}
 *
 * 用法： map({a:'hello', b:'world', c:111},  {a:'new_a', b:'new_b', c:'new_cccc'})
 *
 *
 *
 * 计算和数组遍历 需求：  {count:0, list:[1, 2, 3]}   =>(count统计，数据计算v+1)   {new_count:3, dataList:[2, 3, 4]}
 *
 * 用法：map({foo:1, list:[1, 2, 3]},  {
 *    count:{key:'new_count', value:function(v, obj){return obj.list.length}},
 *    list:{
 *        key: 'dataList',
 *        value:function(v){
 *          return v+1
 *        },
 *        mapArray:true
 *      }
 *    })
 *
 * 嵌套遍历 需求： {foo:1, bar: {aaa: {xxx: 'fengdi'}}}  => {foo:1, a: {b: {c: 'fengdi'}}}
 * 用法：（借助value的function 嵌套调用map，虽然麻烦点但此设计更加灵活一点）
 *
 *  map({foo:1, bar: {aaa: {xxx: 'fengdi'}}}, {
 *    foo: 'foo',
 *    bar: {key:'a', value:function(barValue){
 *        return map(barValue, {aaa: {key:'b', value: function(aaaValue){
 *         return map(aaaValue, {xxx: {key:'c'}})
 *        }}
 *      })
 *    }}
 *  })
 */
export const map = function (obj, mapRules) {
  var newObj = {};

  //抛出 异常还是undefined
  if (typeof obj != "object") return undefined;
  if (!mapRules && typeof mapRules != "object") return Object.assign({}, obj); //统一返回全新的object

  for (var k in mapRules) {
    if (mapRules.hasOwnProperty(k)) {
      var newKeyRule = mapRules[k];
      var keyName = undefined;
      var valueFn = function (v) {
        return v;
      };
      var mapArray = false;

      if (typeof newKeyRule === "string") {
        keyName = newKeyRule;
      } else if (typeof newKeyRule === "object") {
        if (typeof newKeyRule.key === "string") {
          keyName = newKeyRule.key;
        }
        if (typeof newKeyRule.value === "function") {
          valueFn = newKeyRule.value;
        } else {
          valueFn = function (v) {
            return newKeyRule.hasOwnProperty("value") ? newKeyRule.value : v;
          };
        }
        if (newKeyRule.mapArray) {
          var old_valuefn = valueFn;
          valueFn = function (obj, parent) {
            return obj.map(function (currentValue, index) {
              return old_valuefn.bind(obj)(currentValue, index, obj, parent);
            });
          };
        }
      } else {
        continue;
      }

      newObj[keyName] = valueFn.bind(obj)(obj[k], obj);
    }
  }

  return newObj;
};

export const updateUserOperateState = (props) => {
  const { dispatch, userInfo } = props;
  dispatch({
    type: "userInfo/setUserOperate",
  }).then((res) => {
    setTimeout(() => {
      dispatch({
        type: "userInfo/resetOperate",
      });
    }, 500);
  });
};

export const confirmClose = (confirm = true, tip = "你确定要离开吗") => {
  if (confirm) {
    window.onbeforeunload = function () {
      return tip ? tip : "你确定要离开吗";
    };
  } else {
    window.onbeforeunload = null;
  }
};

// 默认转换为三级菜单
export const parseMenuListToMenuTree = (menuList = [], level = 3) => {
  function fetchChildren(pid) {
    let result = [];
    // 根节点是：menuItem.menu_id
    menuList.forEach((menuItem) => {
      if (menuItem.parent_id === pid && menus[menuItem.menu_id]) {
        result.push({
          ...menus[menuItem.menu_id],
          name: menuItem.name,
          seq: menuItem.seq,
        });
      }
    });
    return result;
  }
  function fetchTree(node = {}, menuChildrenMap = {}, currentLevel = 0) {
    // console.log(currentLevel);
    currentLevel++;
    if (currentLevel <= level) {
      const nodeChildren = menuChildrenMap[node.id];
      if (
        nodeChildren &&
        Array.isArray(nodeChildren) &&
        nodeChildren.length !== 0
      ) {
        const _children = nodeChildren.map((child) => {
          return fetchTree(child, menuChildrenMap, currentLevel);
        });
        node.children = _children.sort((a, b) => {
          return a.seq - b.seq;
        });
      }
    } else {
      node.children = [];
    }
    return node;
  }

  let menuTree = [],
    menuChildrenMap = {};
  if (menuList && Array.isArray(menuList) && menuList.length !== 0) {
    menuList.forEach((menuItem, index) => {
      if (menuItem.parent_id === "0") {
        if (menus[menuItem.menu_id]) {
          menuTree.push({
            ...menus[menuItem.menu_id],
            name: menuItem.name,
            seq: menuItem.seq,
          });
        }
      }
      menuChildrenMap[menuItem.menu_id] = fetchChildren(menuItem.menu_id);
    });
  }
  // 记录当前菜单等级
  let currentLevel = 1;
  menuTree.map((node) => {
    return fetchTree(node, menuChildrenMap, currentLevel);
  });
  return menuTree.sort((a, b) => {
    return a.seq - b.seq;
  });
};

//考核统计年份数据 2018开始到  当前年份的下一年
export const YearsData = (start = 2019, hasNext = true) => {
  const date = new Date();
  const currentYear = date.getFullYear() + (hasNext ? 0 : -1);

  let data = [];
  if (currentYear <= start) {
    data = [start];
  } else {
    for (let index = start; index <= currentYear + 1; index++) {
      data.push(index);
    }
  }

  return {
    data: data,
    current: currentYear,
  };
};

//参数占位组件，getFieldDecorator 方法占位用
export class Param extends Component {
  constructor(props) {
    super(props);
  }
  render() {
    return <div>{/*JSON.stringify(this.props.value)*/}</div>;
  }
}

//简易类型判断
// from:https://github.com/fengdi/imjs/blob/master/im-dev.js#L244
export const type = function (obj, type) {
  var ts = {}.toString,
    ud = void 0,
    t =
      obj === null
        ? "null"
        : (obj === ud && "undefined") ||
        ts.call(obj).slice(8, -1).toLowerCase();
  return type ? t === type : t;
};

//两个变量是否相等，引用类型根据json判断，所以仅支持能转为JSON的数据判断
//bug：is_equal({b:"1",a:"1"}, {a:"1",b:"1"}); //键顺序不一致 为false
export const is_equal = (a, b) => {
  const simpleTypes = ["undefined", "number", "boolean", "null", "string"]; //基本类型

  //console.log(a, b, a === b, JSON.stringify(a) === JSON.stringify(b));

  if (
    (simpleTypes.indexOf(type(a)) != -1 ||
      simpleTypes.indexOf(type(b)) != -1) &&
    (!isNaN(a) || !isNaN(b))
  ) {
    return a === b;
  } else {
    return JSON.stringify(a) === JSON.stringify(b);
  }
};

/**
 * 表单双向绑定state
 * @param {Component} Comp 组件
 * 用法和.Form.create完全一样，不用关心内部，表单字段变化会自动更新同名的state

 例如：
 <FormItem label="foo字段">
    {getFieldDecorator('foo', {
        initialValue: state.foo,
        rules: [{ required: true, message: 'foo必填' }],
    })(
        <Ant.Input placeholder="" />
    )}
</FormItem>

  此字段和state.foo就自动绑定了，
  {state.foo} - {getFieldValue('foo')} //数据自动同步且等效


 * 此方法依赖：is_equal, React.Component,  Ant.Form.create
 */
let _uid = 1;
export const CreateFormBindState = (Comp) => {
  // return Ant.Form.create()(Comp);

  let fns = []; //存储所有监听的函数

  //简易事件处理方法
  //onFieldsChange(function(){}, key)  //添加监听函数
  //onFieldsChange({data:...}, key)    //触发执行所有监听函数并传入参数
  //onFieldsChange(null, key)          //由于方式2不存在null情况，因此传null清除所有监听函数
  const onFieldsChange = function (changes, key) {
    if (typeof changes === "function") {
      changes._key = key;
      fns.push(changes);
    } else if (changes === null) {
      if (arguments.length == 2) {
        fns = fns.filter((fn) => fn._key != key);
      } else {
        fns = [];
      }
    } else {
      for (let index = 0; index < fns.length; index++) {
        if (arguments.length == 2) {
          if (fns[index]._key === key) {
            fns[index].call(null, changes);
          }
        } else {
          fns[index].call(null, changes);
        }
      }
    }
  };

  //切片编程，替换 componentDidMount componentWillUnmount setState componentWillUpdate

  //hook componentDidMount
  const _componentDidMount = Comp.prototype.componentDidMount || function () { };

  Comp.prototype.componentDidMount = function () {
    const self = this;
    const { formKey: key } = self.props;
    const { state } = self;
    // const { onFieldsChange } = self.props;
    //监听表单字段变更事件，更新state 完成双向绑定
    //（更新state本身会触发视图更新）
    onFieldsChange((changes) => {
      let c = {};
      for (const name in changes) {
        if (changes.hasOwnProperty(name)) {
          if (!is_equal(changes[name].value, state[name])) {
            c[name] = changes[name].value;
          }
        }
      }
      self.setState(c);
    }, key);
    _componentDidMount.apply(self, arguments);
  };

  //hook componentWillUnmount
  // const _componentWillUnmount = Comp.prototype.componentWillUnmount || function(){};
  // Comp.prototype.componentWillUnmount = function(){
  //   _componentWillUnmount.apply(this, arguments);
  //   this.setState = ()=>{};
  // }

  //hook componentWillUpdate
  // const _componentWillUpdate = Comp.prototype.componentWillUpdate || function(){};
  // Comp.prototype.componentWillUpdate = function(nextProps, nextState){
  //   const { form } = this.props;
  //   const { setFieldsValue, getFieldsValue } = form;
  //   let fields = getFieldsValue();

  //   let setData = {}
  //   for (const key in nextState) {
  //     if (nextState.hasOwnProperty(key) && fields.hasOwnProperty(key)) {
  //       if(fields[key] != nextState[key]){
  //         setData[key] = nextState[key]
  //       }
  //     }
  //   }
  //   if(Object.keys(setData).length){
  //     setFieldsValue(setData);
  //   }
  //   _componentWillUpdate.apply(this, arguments);
  // }

  //hook setState
  const _setState = Comp.prototype.setState || function () { };
  Comp.prototype.setState = function (nextState) {
    const { form } = this.props;
    const { setFieldsValue, getFieldsValue } = form;
    let fields = getFieldsValue();

    let setData = {};
    for (const key in nextState) {
      if (nextState.hasOwnProperty(key) && fields.hasOwnProperty(key)) {
        if (!is_equal(nextState[key], fields[key])) {
          setData[key] = nextState[key];
        }
      }
    }
    if (Object.keys(setData).length) {
      setFieldsValue(setData);
    }
    _setState.apply(this, arguments);
  };

  const ExtComp = Ant.Form.create({
    onFieldsChange(props, changedValues) {
      // console.log(props);
      //事件双向绑定
      onFieldsChange(changedValues, props.formKey);
    },
  })(Comp);

  //本来可以直接返回ExtComp组件，
  //但为了每个实例formKey唯一，所以包装了一次ExtForm，构造函数里面创建唯一id
  return class ExtForm extends Component {
    constructor(props) {
      super(props);
      this._uid = _uid++;
    }
    render() {
      const { props } = this;
      return <ExtComp {...props} formKey={this._uid} />;
    }
  };
};

// 进行下载
export const downloadByUrl = (url) => {
  window.open(url, "_parent");
};
/**cookie操作 */
export const cookie = {
  get(name) {
    let value = null;
    if (document.cookie) {
      document.cookie.split(";").some(function (ck) {
        const parts = ck.split("=");
        if (parts[0].trim() === name) {
          value = decodeURIComponent((parts[1] || "").trim());
          return true;
        }
      });
    }
    return value;
  },
  set(name, value) {
    const cookies = {};
    if (document.cookie) {
      document.cookie.split(";").forEach(function (ck) {
        const parts = ck.split("=");
        cookies[parts[0].trim()] = (parts[1] || "").trim();
      });
    }
    cookies[name] = value;
    document.cookie = `${name}=${encodeURIComponent(value)}`;
  },
};

/**
 * 将对象转换成search参数
 * @param {Object} keyValue 对象中的第一个参数必须有值
 * @param {Boolean} fix
 */
export const paramsToString = (keyValue = {}, fix = false) => {
  const keys = Object.keys(keyValue);
  let keyString = "";
  keys.forEach((key, index) => {
    const cur = keyValue[key];
    if (fix) {
      if (cur) {
        keyString = keyString.concat(`&${key}=${keyValue[key]}`);
      }
    } else {
      if (index) {
        if (cur) {
          keyString = keyString.concat(`&${key}=${keyValue[key]}`);
        }
      } else {
        if (cur) {
          keyString = keyString.concat(`?${key}=${keyValue[key]}`);
        }
      }
    }
  });
  return keyString;
};

export const exportExcelFile = (options = {}) =>
  new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    const params = paramsToString(options.params || {}).replace(/^\?/, "");
    xhr.open(
      options.method,
      options.url.concat(options.method === "get" ? "?".concat(params) : "")
    );
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    Object.keys(axios.headers()).forEach((key) =>
      xhr.setRequestHeader(key, axios.headers()[key])
    );
    xhr.responseType = options.responseType || "blob";
    xhr.onreadystatechange = (e) => {
      if (xhr.readyState === 4 && xhr.status === 200) {
        const a = document.createElement("a");
        const href = window.URL.createObjectURL(
          new Blob([xhr.response], {
            type: "application/ynd.ms-excel;charset=UTF-8",
          })
        );
        a.href = href;
        a.style.display = "none";
        a.download = `${options.name}.xls`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(href);
        resolve();
      } else {
        reject();
      }
    };
    xhr.send(options.method === "post" ? params : undefined);
  });

/**
 * 登出 兼容之前node的代码
 */
export const logout = () => {
  return new Promise((resolve) => {
    window.sessionStorage.removeItem("_tk");
    window.sessionStorage.removeItem("_uid");
    window.sessionStorage.removeItem("_un");
    window.sessionStorage.removeItem("_type");
    window.sessionStorage.removeItem("_oid");
    window.sessionStorage.removeItem("_on");
    window.sessionStorage.removeItem("org-list");
    window.sessionStorage.removeItem("current-org");
    window.sessionStorage.removeItem("_mu");
    window.sessionStorage.removeItem("userInfo");
    window.sessionStorage.removeItem("csrf");
    window.sessionStorage.removeItem("_phone");
    window.sessionStorage.removeItem("_rid");
    resolve({ data: { code: 0 } });
  });
};

const createHistory = (getUserConfirmation) => {
  const history = createBrowserHistory({ getUserConfirmation });
  function handleHistory(history) {
    const historyPush = history.push.bind(history);
    const historyReplace = history.replace.bind(history);
    function doPath(path) {
      if (!window.__TOG__) {
        const { region_id } = getParam();
        // 全局没有 regionId，只能去登录页面获取
        return region_id ? `/login?region_id=${region_id}` : "/login";
      }
      let _path = path;
      if (__TOG__.regionId) {
        if (_path.pathname) {
          if (_path.pathname.indexOf("?") > -1) {
            // path.pathname = `${path.pathname}&region_id=${__TOG__.regionId}`
            path.pathname = path.pathname + `&region_id=${__TOG__.regionId}`;
          } else {
            _path.search = `region_id=${__TOG__.regionId}`;
          }
        } else {
          if (_path.indexOf("?") > -1) {
            _path = _path + "&region_id=" + __TOG__.regionId;
          } else {
            _path = path + "?region_id=" + __TOG__.regionId;
          }
        }
      }
      return _path;
    }
    history.push = (...args) => {
      let p = args.shift();
      historyPush(doPath(p), ...args);
    };
    history.replace = (...args) => {
      const p = args.shift();
      historyReplace(doPath(p), ...args);
    };
  }
  handleHistory(history);
  return history;
};

/**
 * 根据选中code 返回 层连选择器的value, 配合getAllArea接口使用
 * @param {*} arr 后端返回的[区数据，市数据，省数据]
 * @param {*} targetCode 选中的区域code
 * @param {*} hasFormat 此参数不传，递归需要作为判断
 */
export function assembleAreaData(arr, targetCode, noFormat) {
  const currentArea = !noFormat
    ? arr[0].map((item) => ({ label: item.area_name, value: item.adcode }))
    : arr[0];
  let lastCode = targetCode;
  if (arr[1]) {
    const lastArea = arr[1].map((item, index) => {
      const res = {
        label: item.area_name || item.label,
        value: item.adcode || item.value,
        isLeaf: false,
      };
      if (targetCode === res.value) {
        res.children = currentArea;
        delete res.isLeaf;
        lastCode = arr[1][index].parentid;
      }
      return res;
    });
    const resArr = arr.slice(1);
    resArr[0] = lastArea;
    return assembleAreaData(resArr, lastCode, true);
  } else {
    return currentArea;
  }
}

/**
 * 根据code数据找到对应的地区数组
 * @param {*} codearr 城市code数组（value值）
 * @param {*} areaArr 后端的区域数据
 * @param {*} res 初始数组，此参数不传（用于递归使用）
 */
export function findCityName(codearr, areaArr, res = []) {
  const code = codearr[0];
  const length = areaArr.length;
  for (let i = 0; i < length; i++) {
    const item = areaArr[i];
    if (code === item.value) {
      res.push(item.label);
      if (codearr.length > 1) {
        findCityName(codearr.slice(1), item.children || [], res);
      }
    }
  }
  return res;
}

/**
 * @description: 表单提交时清楚空值
 * @param {*} formData 表单数据
 * @return {*} formData
 */
export function formatParams(formData) {
  Object.keys(formData).forEach((item) => {
    const obj = formData[item];
    (typeof obj === "undefined" || obj === null || obj === "") &&
      delete formData[item];
  });
  return formData;
}

export const Base64 = {
  _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
  encode(e) {
    let t = "";
    let n;
    let r;
    let i;
    let s;
    let o;
    let u;
    let a;
    let f = 0;
    e = Base64._utf8_encode(e);
    while (f < e.length) {
      n = e.charCodeAt(f++);
      r = e.charCodeAt(f++);
      i = e.charCodeAt(f++);
      s = n >> 2;
      o = ((n & 3) << 4) | (r >> 4);
      u = ((r & 15) << 2) | (i >> 6);
      a = i & 63;
      if (isNaN(r)) {
        u = a = 64;
      } else if (isNaN(i)) {
        a = 64;
      }
      t =
        t +
        this._keyStr.charAt(s) +
        this._keyStr.charAt(o) +
        this._keyStr.charAt(u) +
        this._keyStr.charAt(a);
    }
    return t;
  },
  decode(e) {
    let t = "";
    let n;
    let r;
    let i;
    let s;
    let o;
    let u;
    let a;
    let f = 0;
    e = e.replace(/[^A-Za-z0-9\+\/\=]/g, "");
    while (f < e.length) {
      s = this._keyStr.indexOf(e.charAt(f++));
      o = this._keyStr.indexOf(e.charAt(f++));
      u = this._keyStr.indexOf(e.charAt(f++));
      a = this._keyStr.indexOf(e.charAt(f++));
      n = (s << 2) | (o >> 4);
      r = ((o & 15) << 4) | (u >> 2);
      i = ((u & 3) << 6) | a;
      t += String.fromCharCode(n);
      if (u != 64) {
        t += String.fromCharCode(r);
      }
      if (a != 64) {
        t += String.fromCharCode(i);
      }
    }
    t = Base64._utf8_decode(t);
    return t;
  },
  _utf8_encode(e) {
    e = e.replace(/\r\n/g, "\n");
    let t = "";
    for (let n = 0; n < e.length; n++) {
      const r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
      } else if (r > 127 && r < 2048) {
        t += String.fromCharCode((r >> 6) | 192);
        t += String.fromCharCode((r & 63) | 128);
      } else {
        t += String.fromCharCode((r >> 12) | 224);
        t += String.fromCharCode(((r >> 6) & 63) | 128);
        t += String.fromCharCode((r & 63) | 128);
      }
    }
    return t;
  },
  _utf8_decode(e) {
    let t = "";
    let n = 0;
    let r = 0;
    let c3 = 0;
    let c2 = 0;
    while (n < e.length) {
      r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
        n++;
      } else if (r > 191 && r < 224) {
        c2 = e.charCodeAt(n + 1);
        t += String.fromCharCode(((r & 31) << 6) | (c2 & 63));
        n += 2;
      } else {
        c2 = e.charCodeAt(n + 1);
        c3 = e.charCodeAt(n + 2);
        t += String.fromCharCode(
          ((r & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        n += 3;
      }
    }
    return t;
  },
};

/**
 * 给数组字符串补零（不支持负数）
 * @param {any} num 需要补的值
 * @param {number} fill 多少位
 */
export const padNumber = (num: any, fill: any) => {
  // 改自：http://blog.csdn.net/aimingoo/article/details/4492592
  const len = `${num}`.length;
  return Array(fill > len ? fill - len + 1 || 0 : 0).join(0) + num;
};

export const getKey = (key: any, des: any) => {
  const len = des ? 8 : 32;
  key = `${key}`;
  console.log(key, des);

  if (key.length < len) {
    key = padNumber(key, len);
  } else if (key.length > len) {
    key = key.substring(0, len);
  }
  return key;
};

/**  * javascript sdbm hash算法  */
export const sdbm = (str: any) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const cr = str.charCodeAt(i);
    hash = cr + (hash << 6) + (hash << 16) - hash;
    hash &= 0x7fffffff;
    hash &= hash; // Convert to 32bit integer
  }
  console.log(hash, "hash");
  return hash;
};

/**
 *
 * @param {string} url 请求的完整url(不包含域名/ip部分)，如 http://a.b.com/api/user/get/1/?c=1&c=2&pagesize=999&oid&xx=ABC&aaa=中文 中的 /api/user/get/1/?c=1&c=2&pagesize=999&oid&xx=ABC&aaa=中文
 * @param {string} body json body，如果有
 */
export const hash = (url: any, body: any) => {
  // TODO 空对象
  if (!url.startsWith("/")) {
    url = `/${url}`;
  }
  const str = url + (body !== undefined ? body : "");
  console.log("hash-beforesdbm------------------", url);
  console.log(body, "body");
  console.log(111, str);
  return sdbm(str);
};

/**
 * 调用CryptoJS进行des
 * @param {string} key
 * @param {string} message
 */
const des = (key: any, message: any) => {
  // console.log(key);
  const keyHex = CryptoJS.enc.Utf8.parse(key);
  const encrypted = CryptoJS.DES.encrypt(message, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};
export { createHistory };

/**
 * 从身份证id中获取生日
 * @param {String} idCard 身份证id
 * @returns { String } birthday "2020-10-01"
 */
export const getBirthdayFromIdCard = function (idCard) {
  var birthday = "";
  if (idCard != null && idCard != "") {
    if (idCard.length == 15) {
      birthday = "19" + idCard.substr(6, 6);
    } else if (idCard.length == 18) {
      birthday = idCard.substr(6, 8);
    }

    birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
  }

  return birthday;
};
// 是否日期字符串
export const isDateString = (str) => {
  const datePatterns = [
    /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
    /^\d{4}\.(1[0-2]|0?[1-9])$/, // YYYY-MM-DD
    /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
    /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
    /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
    /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
    /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|([+-]\d{2}:\d{2}))?$/, // ISO 8601
    /^\d{13}$/, // Unix timestamp in milliseconds
    /^\d{10}$/, // Unix timestamp in seconds
  ];

  return datePatterns.some((pattern) => pattern.test(str));
};

// 将对象中的时间
export const convertTimeValues = (obj) => {
  const result = {};

  for (const [key, value] of Object.entries(obj)) {
    let isTimeFormat = false;

    if (typeof value === "string" || typeof value === "number") {
      if (isDateString(value)) {
        result[key] = moment(value);
        isTimeFormat = true;
        continue;
      }
      if (value === "") {
        console.log(key, ":", result[key]);
        result[key] = undefined;
        continue;
      }
    }

    if (!isTimeFormat) {
      result[key] = value;
    }
  }

  return result;
};

export const convertMomentToFormattedDate = (obj, format = "YYYY-MM-DD") => {
  const result = {};

  for (const key of Object.keys(obj)) {
    const value = obj[key];

    if (moment.isMoment(value)) {
      result[key] = value.format(format);
    } else {
      result[key] = value;
    }
  }

  return result;
};
// 身份证校验
export function validateIDCard(idCard) {
  // 校验身份证号码的长度是否为18位
  if (!/^\d{17}(\d|X|x)$/.test(idCard)) {
    return false;
  }

  // 身份证号码的前17位对应的权重
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];

  // 校验码对应表
  const checkCodes = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];

  // 计算前17位的加权和
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += idCard[i] * weights[i];
  }

  // 取模得到校验码
  const checkCode = checkCodes[sum % 11];

  // 校验最后一位是否正确
  return checkCode === idCard[17].toUpperCase();
}

export function validateIDCardDate(idCard) {
  // 校验身份证号码的长度是否为18位
  if (!validateIDCard(idCard)) {
    return false;
  }

  // 验证日期部分
  const birthDateStr = idCard.substring(6, 14); // 提取出生日期
  const year = parseInt(birthDateStr.substring(0, 4), 10);
  const month = parseInt(birthDateStr.substring(4, 6), 10);
  const day = parseInt(birthDateStr.substring(6, 8), 10);

  // 构造日期对象
  const birthDate = new Date(year, month - 1, day); // 注意：月份从0开始，所以要减1
  const currentDate = new Date();

  // 检查日期是否合法：年、月、日是否正确，以及日期是否不超前于当前日期
  if (
    birthDate.getFullYear() !== year ||
    birthDate.getMonth() + 1 !== month ||
    birthDate.getDate() !== day ||
    birthDate > currentDate
  ) {
    return false; // 日期不合法
  } else {
    return true;
  }
}
// 提取身份证日期
export function extractBirthDate(idCard) {
  // 校验身份证号码的长度是否为18位
  if (!validateIDCardDate(idCard)) {
    return null;
  }

  // 提取出生日期
  const birthDateStr = idCard.substring(6, 14); // 第7到14位为出生日期
  const year = birthDateStr.substring(0, 4); // 前4位是年份
  const month = birthDateStr.substring(4, 6); // 接着是月份
  const day = birthDateStr.substring(6, 8); // 最后是日期

  return { year, month, day };
}
export const createATagDownload = (filePath) => {
  // 创建a标签下载
  const a = document.createElement("a");

  a.href = filePath;

  a.download = filePath;

  a.target = "_self";

  a.style.display = "none";

  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export function updateQueryParams(params) {
  console.log(params)
  // 获取当前的URL
  const url = new URL(window.location);

  // 遍历对象中的每个键值对，设置查询参数
  Object.keys(params).forEach(key => {
    console.log(params[key])
    url.searchParams.set(key, typeof params[key] === 'number' ? params[key] : Base64.encode(String(params[key])));
  });

  // 更新浏览器地址栏，但不刷新页面
  window.history.replaceState({}, '', url);
}

// undefined替换为空值
export function replaceVaule(params) {
  const baseData = Object.keys(params).reduce((acc, key) => {
    acc[key] = params[key] === undefined ? '' : params[key];
    return acc;
  }, {});

  return baseData;
}