import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, message } from 'antd';
import SearchHeader from 'client/components/search-header';
import IndexView from './index-view';
import SideView from './side-view';
import ModalView from './modal-view';
import SelectCommodityModal from 'client/components/select-commodity-modal';
import {offlineUsersDownLoad} from 'client/apis/offline-activity';

import './index.less';

let isSport = false;

// 切换边栏显示或者消失
const toggleSide = (dispatch, isShow = false) => {
    if (isShow) {
        // 切换外壳显示动画
        dispatch({ type: 'activityDetails/toggleHideWrap' });
        setTimeout(() => {
            dispatch({ type: 'activityDetails/toggleCollapsed' });
        }, 0);
    } else {
        // 关闭的时候 等待动画完毕的时候执行隐藏外壳
        dispatch({ type: 'activityDetails/toggleCollapsed' });
        setTimeout(() => {
            isSport = false;
            dispatch({ type: 'activityDetails/toggleHideWrap' });
        }, 300);
    }
};

// 侧边栏表单状态重置
// const resetFormData = (dispatch, form) => {
//     dispatch({ type: 'activityDetails/resetFormData', form });
// };

class ActivityDetails extends Component {
    constructor(props) {
        super(props);
    }

    componentWillMount() {
        // console.log(this.props);
        const { dispatch, history, location } = this.props;
        const { pathname } = location || '';
        // console.log(history, location, pathname);
        //获取路由信息中的活动id
        let aid = pathname.replace(/\/activity-details\//, '');
        dispatch({
            type: 'activityDetails/save',
            payload: {
                aid: Number(aid)
            }
        });
        // console.log(aid);
        //请求活动基础信息
        dispatch({
            type: 'activityDetails/fetchActivityBase',
            payload: {
                aid
            }
        });
        //请求活动审批信息
        dispatch({
            type: 'activityDetails/fetchActivityCheck',
            payload: {
                aid
            }
        });
        //请求活动日程信息
        dispatch({
            type: 'activityDetails/fetchActivityPlan',
            payload: {
                aid
            }
        });
        //请求颁奖明细信息
        dispatch({
            type: 'activityDetails/fetchActivityReward',
            payload: {
                aid
            }
        });
        //请求多维度统计表格信息
        dispatch({
            type: 'activityDetails/fetchMultipleDimensions',
            payload: {
                aid
            }
        });
        dispatch({
            type: 'activityDetails/download',
            payload: {
                activity_type: 'offline',
                activity_id: aid,
                dimension: 5,
            }
        });
    }

    render() {
        const { history, match, activityDetails, dispatch, form, userInfo } = this.props,
            { params } = match,
            { activity_id } = params,
            {
                downloadUrl,
                activityNameListType,
                activityType,
                modalVisible,
                collapsed,
                isHideWrap,
                modalTitle,
                formData,
                summaryTableDataSource,
                programmeTableDataSource,
                prizeTableDataSource,
                peopleTableDataSource,
                peopleOverviewDataSource,
                commodityModalVisible,
                commodityList,
                awardFormData,
                baseInfo,
                aid,
                pid,
                awardTarget,
                awardTargetIndex,
                //所有维度
                allCountTableDataSource,
                //年龄维度
                ageTableDataSource,
                //性别维度
                genderTableDataSource,
                //学历维度
                educationDegreeTableDataSource,
                //民族维度
                nationTableDataSource,
                //其他维度
                otherTableDataSource,
                loadingVisible,
                currentPlan
            } = activityDetails,
            tabChangeHandler = (activeKey) => {
                const map = {
                    'all': 5,
                    'age': 1,
                    'gender': 2,
                };
                dispatch({
                    type: 'activityDetails/download',
                    payload: {
                        activity_type: 'offline',
                        activity_id: aid,
                        dimension: map[activeKey],
                    }
                })
            },
            downloadHandler = () => {
                // console.log('下载结果');
                dispatch({
                    type: 'activityDetails/download',
                    payload: {
                        activity_type: 'offline',
                        activity_id: aid
                    }
                }).then(
                    (response) => {
                        // console.log('下载', response);
                        typeof window !== 'undefined' && window.open(response);
                    }
                ).catch(
                    (error) => {
                        console.error(error);
                    }
                )
            },
            onPageChange = (page, pageSize) => {
                console.log(`当前第${page}页，每页${pageSize}条数据`);
            },
            toggleModalVisible = () => {
                dispatch({ type: 'activityDetails/toggleModalVisible' });
            },
            searchHeaderProps = {
                title: '活动详情',
                onBack: () => {
                    history.goBack();
                }
            },
            indexViewProps = {
                userInfo,
                downloadUrl,
                activityType,
                //线下活动基础信息
                baseInfo,
                //前往审核
                goAuditing(record) {
                    if (activityNameListType !== 1) {
                        dispatch({
                            type: 'activityDetails/save',
                            payload: {
                                activityNameListType: 1
                            }
                        });
                    }
                    // console.log(record);
                    const { type } = record;
                    //请求人员列表
                    dispatch({
                        type: 'activityDetails/fetchOfflineSiderUsers',
                        payload: {
                            postData: {
                                aid: aid,
                                pid: '',
                                type: 1,
                                sign_type: activityType
                            }
                        }
                    })
                    toggleSide(dispatch, true);
                },
                //活动概要
                summaryTableDataSource,
                //活动日程
                programmeTableDataSource,
                //颁奖明细
                prizeTableDataSource,
                //所有维度
                allCountTableDataSource,
                //年龄维度
                ageTableDataSource,
                //性别维度
                genderTableDataSource,
                //学历维度
                educationDegreeTableDataSource,
                //民族维度
                nationTableDataSource,
                //其他维度
                otherTableDataSource,
                loadingVisible,
                tabChangeHandler,
                downloadHandler,
                //点击查看人员列表
                peopleListOverview(type, record) {
                    // console.log('弹开模态框,查看名单');
                    // console.log(record);
                    // return;
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            activityNameListType: type,
                            pid: record.offline_plan_id || '',
                            currentPlan: record
                        }
                    });
                    //请求人员列表
                    dispatch({
                        type: 'activityDetails/fetchOfflineSiderUsers',
                        payload: {
                            postData: {
                                aid: aid,
                                pid: record.offline_plan_id || '',
                                type,
                                sign_type: activityType
                            }
                        }
                    }).then(
                        () => {
                            toggleSide(dispatch, true);
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    )
                }
            },
            sideViewProps = {
                baseInfo,
                userInfo,
                currentPlan,
                activityNameListType,
                activityType,
                form,
                collapsed,
                isHideWrap,
                modalTitle,
                // formData,
                peopleOverviewDataSource,
                onPageChange,
                onClose() {
                    if (isSport) return;
                    isSport = true;
                    toggleSide(dispatch, false);
                    // resetFormData(dispatch, form);
                    form.resetFields();
                    //清空侧边栏人员列表
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            peopleOverviewDataSource: []
                        }
                    })
                },
                conditionQuery(queryParams) {
                    console.log('条件查询', queryParams);
                    // console.log({
                    //     aid: aid,
                    //     pid: pid,
                    //     type: activityNameListType,
                    //     sign_type: activityType,
                    //     ...queryParams
                    // })
                    //请求人员列表
                    dispatch({
                        type: 'activityDetails/fetchOfflineSiderUsers',
                        payload: {
                            postData: {
                                aid: aid,
                                pid: pid,
                                type: activityNameListType,
                                sign_type: activityType,
                                ...queryParams
                            }
                        }
                    }).then(
                        () => {
                            // toggleSide(dispatch, true);
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    )
                },
                downloadUsers(queryParams){
                    const url = offlineUsersDownLoad({
                        aid: aid,
                        pid: pid,
                        type: activityNameListType,
                        sign_type: activityType,
                        ...queryParams
                    });
                    return url;
                },
                riseRankHandler(row, index) {
                    if (row) {
                        // console.log('晋级', row, index);
                        // const { offline_sign_in_log_id } = row;
                        let postData = {
                            activity_id: aid,
                            offline_plan_id: pid,
                            sign_type: activityType
                        };
                        if (activityType === 1) {
                            postData.offline_user_id = row.offline_user_id;
                        } else if (activityType === 2) {
                            postData.team_task_id = row.team_task_id;
                        }
                        dispatch({
                            type: 'activityDetails/promote',
                            payload: {
                                postData
                            }
                        }).then(
                            (response) => {
                                if (response) {
                                    const { code } = response;
                                    if (code === 0) {
                                        message.success('操作成功');
                                        // console.log('晋级', row, index);
                                        // console.log(response);
                                        //晋级操作成功，刷新当前侧边栏表格，活动日程表格
                                        if (peopleOverviewDataSource[index]) {
                                            peopleOverviewDataSource[index].status = 2;
                                        }
                                        dispatch({
                                            type: 'activityDetails/save',
                                            payload: {
                                                peopleOverviewDataSource
                                            }
                                        })
                                        //请求活动日程信息
                                        dispatch({
                                            type: 'activityDetails/fetchActivityPlan',
                                            payload: {
                                                aid
                                            }
                                        });
                                    }
                                }
                            }
                        ).catch(
                            (error) => {
                                console.error(error);
                            }
                        );
                    }
                },
                awardingHandler(row, index) {
                    // console.log('颁发奖品给：', row);
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            awardTarget: row,
                            awardTargetIndex: index
                        }
                    });
                    toggleModalVisible();
                },
                approvalHandler(row, index, status) {
                    if (row) {
                        const { offline_user_id } = row;
                        dispatch({
                            type: 'activityDetails/audit',
                            payload: {
                                activity_id: aid,
                                offline_user_id,
                                //"status": "int|必填|2.已审批通过 4,审核不通过"
                                status
                            }
                        }).then(
                            (response) => {
                                if (response) {
                                    const { code } = response;
                                    if (code === 0) {
                                        message.success('操作成功');
                                        // console.log('审核通过', row, index);
                                        // console.log(response);
                                        //审核操作成功，刷新当前侧边栏表格，审批表格和活动日程表格
                                        peopleOverviewDataSource.splice(index, 1);
                                        dispatch({
                                            type: 'activityDetails/save',
                                            payload: {
                                                peopleOverviewDataSource
                                            }
                                        });
                                        //请求活动审批信息
                                        dispatch({
                                            type: 'activityDetails/fetchActivityCheck',
                                            payload: {
                                                aid
                                            }
                                        });
                                        //请求活动日程信息
                                        dispatch({
                                            type: 'activityDetails/fetchActivityPlan',
                                            payload: {
                                                aid
                                            }
                                        });
                                    }
                                }
                            }
                        ).catch(
                            (error) => {
                                console.error(error);
                            }
                        );
                    }
                }
            },
            modalViewProps = {
                commodityList,
                modalVisible,
                awardFormData,
                awardTo(result, index) {
                    // console.log('颁奖给', awardTarget, result);
                    let target = Object.assign(result, {
                        activity_id: aid,
                        offline_plan_id: pid
                    });
                    if (activityType === 1) {
                        target.offline_user_id = awardTarget.offline_user_id;
                    } else if (activityType === 2) {
                        target.offline_user_id = awardTarget.offline_user_id;
                        target.team_task_id = awardTarget.team_task_id;
                    }
                    // console.log(target);
                    dispatch({
                        type: 'activityDetails/award',
                        payload: {
                            postData: target
                        }
                    }).then(
                        (response) => {
                            // console.log(response);
                            const { code } = response;
                            //颁奖操作成功后，刷新当前侧边栏表格，活动日程表格和颁奖信息表格
                            if (code === 0) {
                                message.success('操作成功');
                                if (peopleOverviewDataSource[awardTargetIndex]) {
                                    peopleOverviewDataSource[awardTargetIndex].r_status = 2;
                                }
                                dispatch({
                                    type: 'activityDetails/save',
                                    payload: {
                                        peopleOverviewDataSource
                                    }
                                });
                                //请求活动日程信息
                                dispatch({
                                    type: 'activityDetails/fetchActivityPlan',
                                    payload: {
                                        aid
                                    }
                                });
                                //请求颁奖信息
                                dispatch({
                                    type: 'activityDetails/fetchActivityReward',
                                    payload: {
                                        aid
                                    }
                                });
                            }
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    );
                },
                chooseCommodity() {
                    // console.log('选择物品');
                    dispatch({
                        type: 'selectCommodity/getPrizeList',
                        payload: {
                            page: 1,
                            pageSize: 6
                        }
                    }).then(
                        () => {
                            dispatch({
                                type: 'activityDetails/save',
                                payload: {
                                    commodityModalVisible: true
                                    // commodityList: []
                                }
                            });
                        }
                    ).catch(
                        (error) => {
                            // message.error('获取奖品列表失败');
                            console.error(error);
                        }
                    );
                },
                closeModalHandler() {
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            commodityList: []
                        }
                    });
                    toggleModalVisible();
                },
                removeCommodity(index) {
                    // console.log('移除物品', index);
                    commodityList.splice(index, 1);
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            commodityList
                        }
                    });
                }
            },
            commodityModalProps = {
                visible: commodityModalVisible,
                onOk(list) {
                    // console.log('选中的物品', list);
                    // console.log('之前选中的物品 ', commodityList);
                    let isRepeat;
                    for (let i = 0, listLength = list.length; i < listLength; i++) {
                        isRepeat = false;
                        //listCopy[i],本次选中的每一个物品
                        for (let j = 0, commodityListLength = commodityList.length; j < commodityListLength; j++) {
                            //commodityList[j]之前选中的每一个物品
                            if (list[i].prize_id === commodityList[j].prize_id) {
                                isRepeat = true;
                            }
                        }
                        if (!isRepeat) {
                            let target = JSON.parse(JSON.stringify(list[i]));
                            target.prize_name = target.name;
                            delete target.name;
                            commodityList.push(target);
                        }
                    }
                    if (Array.isArray(commodityList) && commodityList.length !== 0) {
                        commodityList.map((commodity, index) => {
                            if (!commodity.num) {
                                return commodity.num = 1;
                            } else {
                                return commodity;
                            }
                        });
                    }
                    // console.log('合并处理之后的物品序列', commodityList);
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            commodityList,
                            commodityModalVisible: false
                        }
                    });
                },
                onCancel() {
                    dispatch({
                        type: 'activityDetails/save',
                        payload: {
                            commodityModalVisible: false
                        }
                    });
                }
            };

        return (
            <div className="activity-details">
                <SearchHeader {...searchHeaderProps} />
                {/*这是活动详情页面*/}
                <IndexView {...indexViewProps} />
                {/* 侧边栏 */}
                <SideView {...sideViewProps} />
                {/*  */}
                <ModalView {...modalViewProps} />
                <SelectCommodityModal {...commodityModalProps} />
            </div>
        )
    }
}

const mapStateToProps = ({ activityDetails, selectCommodity, userInfo }) => {
    return { activityDetails, selectCommodity, userInfo };
};

export default connect(mapStateToProps)(Form.create()(ActivityDetails));