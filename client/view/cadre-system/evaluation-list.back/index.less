.spin-wrap,
.spin-wrap .ant-spin-container {
  height: 100%;
}
.p-main-content {
  flex: 1;
  .p-main-head {
    background: #f7f8f9;
    padding: 20px 28px;
    p {
      font-size: 17px;
      font-weight: bold;
    }
    ul {
      li {
        display: inline-block;
        &:not(:last-child) {
          margin-right: 45px;
        }
      }
    }
  }
  .p-main-table {
    height: 99%;
    background: #fff;
    padding: 0 28px 20px 28px;
    .party-btn {
      display: flex;
      justify-content: space-evenly;
    }
  }
}
.period-group {
  .period-wrapper {
    .upload-trigger {
      margin: 0;
    }
    .add-member {
      margin-bottom: 20px;
      font-size: 16px;
      height: auto;
      padding: 5px 30px;
    }
    .period-wrapper_btn {
      button {
        font-size: 16px;
        height: auto;
        padding: 5px 40px;
      }
    }
    .ant-form-item {
      label {
        margin-right: 20px;
        color: #5f5e5e;
      }
    }
  }
  .files-wrapper {
    .file-name-wrapper,
    .file-size {
      line-height: 20px;
    }
  }
}
.modal-orgCom {
  .modal-item {
    margin: 20px 0;
    .ant-checkbox-wrapper {
      margin-right: 30px;
    }
  }
  .modal-label {
    // margin-right: 10px;
    text-align: right;
  }
  .vertical span {
    vertical-align: sub;
  }
  .label_required {
    &::before {
      content: "*";
      padding: 3px;
      color: red;
    }
  }
  .modal-a {
    margin-left: 10px;
  }
  .modal_btn {
    margin-top: 40px;
    .ant-btn {
      margin-right: 30px;
      font-size: 16px;
      height: auto;
      padding: 5px 40px;
    }
  }
}
