import React from "react";
import SelfIcon from "components/self-icon";
import { Spin } from "antd";
import { fileHost, CDN } from 'apis/config';
import { postFilePreview } from "apis/file";
import "./style.less";
import PropTypes from "prop-types";
import FileDownload from 'client/components/file-download';
/**
 * 
 * @param data 
 * @type [{size: 1000byte, name: 文件名, file_name: 文件原始名, path: 文件路径}] 导出的时候会新加 一个 is_del: 0
 * 
 * @param updateState
 * @type func 更新父级组件的 state
 * 
 * @param loading
 * @type boolean 上传中
 * 
 * @param isDelete
 * @type boolean true 删除, false 下载
 * 
 */

class ShowUploadFileType extends React.Component {
  constructor(props) {
    super(props);
  }

  async postFilePreview(params = {}) {
    const { changeLoadingStatus } = this.props;
    const response = await postFilePreview(params);
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      // changeLoadingStatus && changeLoadingStatus(false);
      Message.error(message);
      return;
    }
    // 生成成功
    if (data.status === 1 && data.target_url) {
      typeof window !== "undefined" && window.open(`https://preview.imm.aliyun.com/index.html?url=${CDN}/${data.target_url}`);
    }
    else if (data.status === 2 && data.request_id) {
      this.postFilePreview({ ...params, request_id: data.request_id });
    } else {
      Message.error("预览出现错误");
    }
    // changeLoadingStatus && changeLoadingStatus(false);
  }

  render() {
    const {
      data = [],
      updateState,
      // changeLoadingStatus,
      loading = false,
      isDelete = false,
      canPreview = false,
      hiddenFileSize = false,
    } = this.props;
    const fileData = data || [];

    // 删除文件
    const deleteFile = (index) => {
      if (data && Array.isArray(data) && data.length !== 0) {
        data.splice(index, 1);
        updateState([...data]);
      }
    }

    const backStr = (fileName) => {
      console.log(fileName);
      const fileInfo = fileName.split(".");
      const fileType = fileInfo[fileInfo.length - 1];
      switch (fileType) {
        case "txt":
          return "txt";
        case "jpg":
        case "jpeg":
          return "jpg";
        case "png":
          return "png"
        case "xlsx":
        case "xls":
          return "ex";
        case "mp3":
          return "mp3";
        case "mp4":
          return "mp1";
        case "flv":
          return "flv";
        case "mov":
          return "mov";
        case "mkv":
          return "mkv";
        case "avi":
          return "avi";
        case "doc":
        case "docx":
          return "doc";
        case "ppt":
          return "ppt";
        default:
          return "wenjian"
      }
    }

    return (
      <div className="showUploadFileType">
        {fileData.map((item, index) =>
          <div className={`iconWrap ${backStr(item.name || item.file_name || item.filename || item)}`}
            key={item.topic_file_id || index}
            style={{
              display: `${item.is_del === 1 ? "none" : "flex"}`
            }}
          >
            <div className="iconInfo">
              <SelfIcon type={`gsg-${backStr(item.name || item.file_name || item.filename || item)}`} className="icon" />
            </div>
            <div className="fileInfo">
              <div className="fileName">
                <span
                  className="fileNameWrapper"
                  title={item.file_name || item.filename}>
                  {item.file_name || item.filename}
                </span>
                {
                  (canPreview && (item.id || item.path || item.file_url)) &&
                  <a style={{ margin: "0 10px", cursor: "pointer" }} href="javascript:void(0)" onClick={() => {
                    this.postFilePreview({
                      file_id: item.id || null,
                      file_path: item.path || item.file_url || null
                    });
                  }}>预览</a>
                }
                {isDelete
                  ? <SelfIcon type="gsg-shanchu6" className="deleteIcon" onClick={() => deleteFile(index)} />
                  // : <a href={`${fileHost}/file/download/${item.name}`} className="deleteIcon" download>下载附件</a>
                  : <FileDownload
                    filePath={`/file/file/download/${item.id || item.file_id || item.name}`}
                    fileName={item.file_name || item.name || item.filename}
                    type="link"
                    btnName="下载附件"
                  />
                  // : <a className="deleteIcon" 
                  //     onClick={()=>{
                  //       fileDownload(`/file/file/download/${item.id}`)
                  //     }}
                  //     href="javascript:void(0);"
                  //   >下载附件</a>
                }
              </div>
              {!hiddenFileSize ?
                <div className="fileSize">{`${Math.round(item.size / 1000)}KB` || "未知大小"}</div>
                : ''
              }
            </div>
          </div>
        )}
        {/* <Spin spinning={loading} style={{ marginTop: "10px" }} /> */}
      </div>
    )
  }
}

ShowUploadFileType.propTypes = {
  data: PropTypes.array,
  updateState: PropTypes.func,
  changeLoadingStatus: PropTypes.func,
  loading: PropTypes.bool,
  isDelete: PropTypes.bool,
  canPreview: PropTypes.bool,
  hiddenFileSize: PropTypes.bool,
}

ShowUploadFileType.defaultProps = {
  data: [],
  updateState: () => { },
  changeLoadingStatus: () => { },
  loading: false,
  isDelete: false,
  canPreview: false,
  hiddenFileSize: false,
}

export default ShowUploadFileType;