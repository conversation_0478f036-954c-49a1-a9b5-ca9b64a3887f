.AppointmentManagement {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .btn-wrap {
    display: flex;
    margin: 20px;
    .btn {
      position: relative;
      padding: 10px 20px;
      font-size: 18px;
      font-weight: 700;
      color: #000;
      cursor: pointer;
      &:hover {
        color: #f5222d;
      }
    }
    .btn-active {
      color: #f5222d;
    }
    .btn-active::before,
    .btn:hover::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 3px;
      background-color: #f5222d;
    }
  }
  &-main {
    flex: 1;
    margin: 20px;
    overflow-y: auto;
  }
  &-form {
    margin-top: 20px;
    .close-btn {
      padding: 2px 20px;
      font-size: 15px;
      transform: translateY(10px);
      &:hover {
        color: #f5222d;
      }
    }
    .week-wrap {
      .ant-form-item-children {
        display: flex;
        align-items: center;
      }
    }
  }
}
