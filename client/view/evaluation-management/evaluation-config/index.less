.EvaluationConfig-container {
  .tip {
    margin: 12px 0;
    color: #D80C0C;
  }

  .ant-spin-container {
    height: calc(100% - 45px);
  }

  .ant-tabs-content {
    height: 100%;
  }

  .ant-spin-nested-loading {
    height: 100% !important;
  }

  .save-button {
    width: 120px;
    height: 36px;
    position: absolute;
    bottom: 46px;
    left: 50%;
    transform: translateX(-50%)
  }
}

.LeaderConfig-modal {
  .ant-modal-title {
    font-weight: bold;
  }

  &__left, &__center, &__right {
    height: calc(100vh - 220px);
    background: #F7F8F9;
    border-radius: 5px;
    overflow: hidden;
  }

  &__center {
    padding: 20px;

    .leader-list {
      height: calc(100% - 46px);
      overflow-y: auto;

      .no-data {
        height: 300px;
        line-height: 300px;
        text-align: center;
        color: #aaa;
      }

      &-item {
        margin-bottom: 16px;
        padding: 14px 20px;
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
        display: flex;
        align-items: center;

        .checkbox-container {

          .checkbox, .ant-checkbox-inner {
            width: 24px;
            height: 24px;
          }

          .ant-checkbox-inner::after {
            left: 30%;
          }
        }

        &__text {
          margin-left: 16px;

          .name {
            font-weight: bold;
            margin-bottom: 4px;
          }

          .position {
            color: #666666;
          }
        }
      }
    }
  }

  &__right {
    .checked-list {
      padding: 20px;
      height: calc(100% - 55px);
      overflow-y: auto;
    }

    .checked-item {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      background: #fff;
      border-radius: 6px;
      line-height: 1;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);

      &__text {
        width: 80%;

        .name {
          margin-bottom: 12px;
          font-weight: bold;
        }

        .job {
          color: #666666;
          line-height: 1.5;
        }
      }
    }

    .checked-item {
      margin-bottom: 16px;
    }

    .remove-icon {
      cursor: pointer;
      width: 16px;
      height: 16px;
      border: 2px solid #FD1F1F;
      border-radius: 50%;
      text-align: center;
      color: #FD1F1F;
      font-weight: bold;
      line-height: 10px;
    }

    &__top {
      font-weight: bold;
      padding: 20px;
      border-bottom: 1px solid #DBDBDB;
      line-height: 1;
    }
  }

  .tree-container {
    overflow-y: auto;

    .ant-tree {
      .first-item {
        font-weight: bold;
      }

      .anticon {
        color: #999;
        font-size: 16px !important;
      }

      li .ant-tree-node-content-wrapper {
        vertical-align: middle;
      }

      .ant-tree-switcher,
      .ant-tree-switcher svg,
      .ant-tree-node-content-wrapper,
      .ant-tree-switcher-loading-icon {
        line-height: 30px !important;
        height: 30px !important;
      }

      .ant-tree-node-content-wrapper {
        .current-item {
          background-color: #fff3f0;
        }
      }
    }
  }
}

.LeaderConfig-modal.department {
  .AutoComplete-container {
    padding: 20px;
    border-bottom: 2px solid #fff
  }

  .tree-container {
    height: calc(100% - 74px);
    padding: 4px 10px;
  }
}

.LeaderConfig-modal.leader {
  .tree-container {
    height: calc(100vh - 220px);
    padding: 4px 10px;
  }
}
