import { evalHost } from './config';
import { http } from 'client/tool/axios';

// 104001000903-查看组织扣分记录 get /count/deduct-log
export const getCountDeductLog = (params = {}) => {
  return http.get(`${evalHost}/count/deduct-log`, params);
};

// 104001000905-扣分详情 get /count/deduct-log/detail/{deduct_log_id}
export const getCountDeductLogDetail = (params = {}) => {
  const { deduct_log_id = "" } = params || {};
  return http.get(`${evalHost}/count/deduct-log/detail/${deduct_log_id}`);
};
