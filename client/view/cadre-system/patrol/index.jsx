import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import { Form, Input, Popconfirm, Button, Table, Modal, message, Radio, DatePicker, Select } from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryPatrol,
  checkPatrol,
  addOrUpdatePatrol,
  deletePatrol,
  exportPatrol,
  queryMajorEventDetail,
} from "client/apis/cadre-portrait";
import moment from "moment";
const { Option } = Select;
function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;
  const { RangePicker } = DatePicker;
  const modalFormRef = useRef(null);

  const [treeData, setTreeData] = useState([]);

  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });

  useEffect(() => {
    loadData();
  }, []);
  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();
    const params = {
      // ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.user_name,
      round: fields.round,
    };
    const res = await queryPatrol(params);
    if (res.data.code === 0) {
      const data = res.data.data.data;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    } else {
      message.error(res.data.message);
    }

    setLoading(false);
  }

  const loadDataDesc = async (id) => {
    const res = await queryMajorEventDetail({
      performance_id: id,
    });

    if (res.data.code === 0) {
      modalFormRef.current.setFieldsValue(res.data.data);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportPatrol({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=5&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deletePatrol({
      id: record.pms_patrol_id,
    });
    if (res.data.code === 0) {
      loadData();
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queueMicrotask(() => {
      // loadDataDesc(record.performance_id);
      // modalFormRef.current.setFieldsValue(record);
      const _ = { ...record };
      _.user_name = _.name ? _.name : undefined;
      _.start_time = _.start_time ? moment(_.start_time) : undefined;
      _.end_time = _.end_time ? moment(_.end_time) : undefined;
      // 转换为moment后的start_time , end_time 组合为一个 [moment(start_time), moment(end_time)] 使得RangePicker能接收，RangePicker中绑定的值为start_time
      _.start_time = (_.start_time && _.end_time) ? [_.start_time, _.end_time] : undefined;
      console.log("record: ", _);
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "标题",
      dataIndex: "round",
      key: "round",
      width: 55,
      align: "center",
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      width: '6%',
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 100,
      align: "center",
    },
    {
      title: "被巡视巡察时间",
      dataIndex: "time",
      key: "time",
      width: 100,
      align: "center",
    },
    {
      title: "巡察组",
      dataIndex: "patrol_group",
      key: "patrol_group",
      width: 100,
      align: "center",
    },
    {
      title: "正面评价",
      dataIndex: "virtue",
      key: "virtue",
      width: 200,
      align: "center",
    },
    {
      title: "主要问题和不足",
      dataIndex: "drawback",
      key: "drawback",
      width: 200,
      align: "center",
    },
    {
      title: "政治生态评级",
      dataIndex: "grade",
      key: "grade",
      width: 50,
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: '8%',
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name, start_time: _start_time } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        if (Array.isArray(_start_time) && _start_time.length === 2) {
          const [start_time, end_time] = _start_time;
          values.start_time = start_time.format("YYYY-MM-DD");
          values.end_time = end_time.format("YYYY-MM-DD");
        }
        const res = await addOrUpdatePatrol({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };
    const handleRoundChange = async (e) => {
      const roundValue = e.target.value;
      const targetUserName = getFieldValue('user_name').user_name;
      const params = {
        name: targetUserName,
        round: roundValue,
      }
      const res = await checkPatrol(params)
      if (res.data.code === 0) {
        if (res.data.data) {
          setFields({
            round: {
              value: roundValue,
              errors: [new Error("巡视巡察记录已存在")],
            },
          });
        } else {
          setFields({
            round: {
              value: roundValue,
              errors: null,
            }
          })
        }
      }
    }

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("pms_patrol_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月" labelCol={{span: 6}}>
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="时任职务" labelCol={{span: 6}}>
          {getFieldDecorator("job", {
            rules: [{ required: false, message: "请输入时任职务" }],
          })(<Input placeholder="请输入"/>)}
        </Form.Item>
        <Form.Item label="标题" labelCol={{span: 6}}>
          {getFieldDecorator("round", {
            rules: [{ required: true, message: "请输入标题" }],
          })(<Input onChange={handleRoundChange} placeholder="请输入"/>)}
        </Form.Item>
        <Form.Item label="巡察组" labelCol={{span: 6}}>
          {getFieldDecorator("patrol_group",{
            rules: [{ required: false, message: "请输入巡察组" }],
          })(<Input placeholder="请输入"/>)}
        </Form.Item>
        <Form.Item label="被巡视巡察时间" labelCol={{span: 6}}>
          {getFieldDecorator("start_time",{
            rules: [{ required: false, message: "请选择起止时间" }],
          })(<RangePicker />)}
        </Form.Item>
        <Form.Item label="正面评价" labelCol={{ span: 6 }}>
          {getFieldDecorator("virtue", {
            rules: [{ required: true, message: "请输入正面评价" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="主要问题和不足" labelCol={{ span: 6 }}>
          {getFieldDecorator("drawback", {
            rules: [{ required: true, message: "请输入主要问题和不足" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="政治生态和评级" labelCol={{span: 6}}>
          {getFieldDecorator("grade",{
            rules: [{ required: false, message: "请输入政治生态和评级" }],
          })(
            <Select placeholder="请选择">
              <Option value="A等次">A等次</Option>
              <Option value="B等次">B等次</Option>
              <Option value="C等次">C等次</Option>
              <Option value="D等次">D等次</Option>
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="petition-reporting">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="标题">
              {getFieldDecorator("round")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            {/* <Form.Item label="事件">
              {getFieldDecorator("event")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item> */}
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "巡视巡察评价"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
