

import React, { Component } from "react";

import StatusIcon from "components/status-icon";

import moment from "moment";

import {statusTypes, activitysTypes} from "./dict";


const format = 'YYYY-MM-DD HH:mm:ss';


export default {

  testData:[{
      "activity_id": "1",
      "title": "互动标题1",
      "column_id": "1001",
      "type": 1,
      "type_name": "投票",
      "start_time": "2018-09-09 19:19:19",
      "end_time": "2018-10-08 19:19:19",
      "status": 1,
      "status_name": "已结束",
      "org_id": 134,
      "org_name": "市委统战部",
      "push_time": "2018-09-01 19:19:19",
      "create_time": "2018-09-01 19:19:19",
      "visibility":1
  },{
      "activity_id": "2",
      "title": "互动标题2",
      "column_id": "1001",
      "type": 4,
      "type_name": "线下互动",
      "sign_start_time": "2018-09-09 19:19:19",
      "sign_end_time": "2018-10-08 19:19:19",
      "status": 4,
      "status_name": "已结束",
      "push_time": "2018-09-01 19:19:19",
      "create_time": "2018-09-01 19:19:19",
      "visibility":0
  }],

  columns : {
    type : text => {
      return activitysTypes[text] || '未知类型'
      // switch (text) {
      //     case 1:
      //         return ('投票');
      //     case 2:
      //         return ('问卷调查');
      //     case 3:
      //         return ('有奖竞答');
      //     case 4:
      //         return ('线下活动');
      // }
    },
    status : text => {
      switch (text) {
          case 1:
              return (<StatusIcon color="#4FC136" text={statusTypes[text]}></StatusIcon>);
          case 2:
              return (<StatusIcon color="#F39800" text={statusTypes[text]}></StatusIcon>);
          case 3:
              return (<StatusIcon color="#F46E65" text={statusTypes[text]}></StatusIcon>);
          case 4:
              return (<StatusIcon color="#999999" text={statusTypes[text]}></StatusIcon>);
          case 5:
              return (<StatusIcon color="#2FC9D7" text={statusTypes[text]}></StatusIcon>);
          case 6:
              return (<StatusIcon color="#1790C9" text={statusTypes[text]}></StatusIcon>);
      }
    },
    statusName : text => {
        switch (text) {
            case "活动中":
                return (<StatusIcon color="#4FC136" text={text}></StatusIcon>);
            case "审批中":
                return (<StatusIcon color="#F39800" text={text}></StatusIcon>);
            case "未通过":
                return (<StatusIcon color="#F46E65" text={text}></StatusIcon>);
            case "已结束":
                return (<StatusIcon color="#999999" text={text}></StatusIcon>);
            case "未开始":
                return (<StatusIcon color="#2FC9D7" text={text}></StatusIcon>);
            case "草稿":
                return (<StatusIcon color="#1790C9" text={text}></StatusIcon>);
        }
      },
    time : (text, record) =>{
      let timeResult = '';
      //时间显示策略调整为:
      if (!record.start_time && !record.end_time) {
          //如果开始时间结束时间均未设置:
          timeResult = <div>永久</div>;
      } else {
          if (record.start_time && !record.end_time) {
              //如果开始时间设置，结束时间未设置:
              timeResult = <div>{moment(record.start_time).format(format)}<br /> 至<br /> 永久</div>;
          }
          if (!record.start_time && record.end_time) {
              //如果开始时间未设置,结束时间设置:
              //显示创建时间为开始时间
              timeResult = <div>{
                  record.create_time ? moment(record.create_time).format(format) :
                      '-'}<br />至<br /> {moment(record.end_time).format(format)}</div>;
          }
          if (record.start_time && record.end_time) {
              timeResult = <div>{moment(record.start_time).format(format)}<br /> 至<br /> {moment(record.end_time).format(format)}</div>;
          }
      }
      return timeResult;
    },

    visibility : text=>{
      switch (text) {
          case 1:
              return (<StatusIcon type={'success'} text='展示中'></StatusIcon>);
          case 0:
              return (<StatusIcon color="#999999" text='未展示'></StatusIcon>);
      }
    }

  }



}
