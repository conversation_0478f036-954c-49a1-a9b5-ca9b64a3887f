import React, { useState, useEffect, useRef } from 'react';
import { Select } from 'antd';
import ReactECharts from 'echarts-for-react';
import { getChartOption } from './chartConfig';
import { mockPointData, MENU_OPTIONS, SELECT_OPTIONS } from './mockData';
import Card from '../Card';
import './index.less';
import { getLeaderScreenPerformancePoint } from 'client/apis/cadre-portrait';
const CoordinateChart = ({ data, user_id, updateStatus }) => {
    // 状态管理
    const [menuSelected, setMenuSelected] = useState(2);
    const [selectValue, setSelectValue] = useState('1');

    const chartRef = useRef(null);
    const alreadyRender = useRef(false);

    // useEffect(() => {
    //     if (data && data !== null && data !== undefined) {
    //         setTimeout(() => {
    //             updateStatus('point', true)
    //         }, 1000)
    //     }
    // }, [data])

    // 图表点击事件处理
    const handleChartClick = (params) => {
        setSelectedUserId(params.data[3]);
    };

    // 菜单选择处理
    const handleMenuSelect = (key) => {
        setMenuSelected(key);
    };

    // 获取图表配置
    const option = getChartOption(data, selectValue, user_id);

    const handleChartRender = () => {
        if (data && data !== null && data !== undefined && !alreadyRender.current) {
            alreadyRender.current = true

            setTimeout(() => {
                updateStatus('point', true)
            }, 500)
        }
    }

    return (
        <Card
            className="coordinate-chart"
            title="坐标分布"
            header
            rightSlot={
                <div className="data-menu">
                    {MENU_OPTIONS.map(item => (
                        <div
                            key={item.key}
                            className={`menu-item ${menuSelected === item.key ? 'menu-active' : ''}`}
                            onClick={() => handleMenuSelect(item.key)}
                        >
                            {item.label}
                        </div>
                    ))}
                </div>
            }
        >
            <div className="echarts-box">
                <ReactECharts
                    ref={chartRef}
                    option={option}
                    style={{ height: '385px' }}
                    onEvents={{
                        click: handleChartClick,
                        finished: handleChartRender
                    }}
                />
                <div className="type-selection">
                    <Select
                        value={selectValue}
                        onChange={setSelectValue}
                        bordered={false}
                        suffixIcon={<span className="select-icon" />}
                    >
                        {SELECT_OPTIONS.map(item => (
                            <Select.Option key={item.value} value={item.value}>
                                {item.label}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
            </div>
        </Card>
    );
};

export default CoordinateChart;