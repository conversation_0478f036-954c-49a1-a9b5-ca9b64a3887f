import React, { Component } from "react";
import { connect } from "dva";
import { message } from "antd";
import IndexView from "./view";
import "./index.less";

import {
  getOrgTree,
  locateOrgTree,
  getTreeList,
  findOrgByName,
  getOrgInfo,
} from "client/apis/organize";

import {
  getOrgParams,
  searchCommitteePeriodUrl,
  deleteCommitteePeriodUrl,
} from "client/apis/party-organization";
// 展开的key
let TREE_PROPS = null;

class Class extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 侧边栏组织树state(不动)
      isInitLoading: false,
      currentOrgId: 1,
      selectedOrgId: 1,
      pattern_sub: null,
      orgTreeTabIndex: 0,
      orgTreeTabList: [],
      orgTreeTabSelectData: null,
      statisData: {
        organizeCount: 0,
        personCount: 0,
      },
      // 侧边栏组织树state(不动)
      treeProps: {
        dataSource: [],
        keys: [],
        loadedKeys: [],
        currentKey: 1,
        autoExpandParent: true,
      },
      // 侧边栏组织树state(不动)
      autoCompleteProps: {
        dataSource: [],
      },
      table: {
        loading: false,
        page_num: 1,
        page_size: 10,
        total: 0,
        dataSource: [],
      },
    };
  }
  componentWillMount() {
    const {
      dispatch,
      userInfo,
      location: { state },
    } = this.props;
    let orgId = null;

    if (TREE_PROPS) {
      orgId = TREE_PROPS.currentKey;
    } else {
      orgId = state ? state.org_id : userInfo.oid;
    }
    const { orgTreeTabIndex, table } = this.state;
    this.setState(
      {
        currentOrgId: orgId,
        selectedOrgId: orgId,
        treeProps: TREE_PROPS ? TREE_PROPS : this.state.treeProps,
        table: {
          ...table,
          org_id: orgId,
        },
      },
      () => {
        TREE_PROPS = null;
        this.getTreeList({ orgId: userInfo.oid, orgTreeTabIndex });
        dispatch({ type: "organizeData/loadculture" });
        dispatch({ type: "organizeData/loadSex" });
        dispatch({ type: "organizeData/loadDepment" });
        dispatch({ type: "organizeData/loadCensus" });
        dispatch({ type: "organizeData/loadpolitical" });
        dispatch({ type: "organizeData/loadType" });
        dispatch({ type: "organizeData/loadRecord" });
        dispatch({ type: "organizeData/loadEthnic" });
        dispatch({ type: "organizeData/loadLevel" });
        dispatch({ type: "organizeData/loadArea" });
        dispatch({ type: "organizeData/loadPartyMembers" });
        dispatch({ type: "organizeData/loadCommunistYouth" });
        dispatch({ type: "organizeData/loadUnion" });
        dispatch({ type: "organizeData/loadFede" });
        dispatch({ type: "organizeData/loadPositionCode" });
        dispatch({ type: "organizeData/loadculture" });
      }
    );
  }

  /**
   * @description 查询组织列表
   * @return {Promise<void>}
   */
  getOrgUserList() {
    const { table = {} } = this.state;
    this.setState(
      {
        table: {
          ...table,
          loading: true,
        },
      },
      async () => {
        const { page_size, page_num, org_id } = table;
        const { data: wrapData = {} } = await searchCommitteePeriodUrl({
          page_size,
          page_num,
          org_id,
        });
        const { data: dataSource = [], code, total } = wrapData;
        if (code === 0) {
          this.setState({
            table: {
              ...table,
              total,
              loading: false,
              dataSource: dataSource.map((item, index) => ({
                ...item,
                index: index + 1,
              })),
            },
          });
        }
      }
    );
  }

  async getOrgInfo() {
    const { table = {} } = this.state;
    const { org_id } = table;
    const { data: params = {} } = await getOrgParams({ org_id });
    const { data: config = {} } = params;
    const { data: infoData = {} } = await getOrgInfo({ org_id });
    const { data: info = {} } = infoData;
    this.setState({
      table: {
        ...table,
        info,
        config,
      },
    });
  }

  /**
   * @description 组织树操作函数(不可动)
   * @param value
   */
  getAutoComplete(value) {
    const { currentOrgId, autoCompleteProps, orgTreeTabSelectData } =
      this.state;
    if (this.autoCompleteTimeout) {
      clearTimeout(this.autoCompleteTimeout);
      this.autoCompleteTimeout = null;
    }
    value = value.trim();
    if (!value) {
      this.setState({
        autoCompleteProps: Object.assign(autoCompleteProps, { dataSource: [] }),
      });
    } else {
      this.autoCompleteTimeout = setTimeout(async () => {
        const result = (
          await findOrgByName({
            org_id: sessionStorage.getItem("_oid"),
            org_name: value,
            tree_type: orgTreeTabSelectData.tree_type,
          })
        ).data;
        if (result.code !== 0) {
          return message.error(result.message);
        }
        const data = result.data;
        this.setState({
          autoCompleteProps: Object.assign(autoCompleteProps, {
            dataSource: data
              .slice(0, data.length > 10 ? 10 : data.length)
              .map((val) => ({
                value: val.org_id,
                name: val.org_name,
              })),
          }),
        });
      }, 300);
    }
  }

  /**
   * @description 获取组织树(不可动)
   * @param payload
   */
  async getTreeList(payload = {}) {
    const { orgId, orgTreeTabIndex, isOpt, isOptTree } = payload;
    const currentIndex = orgTreeTabIndex || 0;
    let data;
    if (!isOpt) {
      this.setState({ isInitLoading: true });
      const result = (await getTreeList(orgId)).data;
      if (result.code !== 0) {
        this.setState({ isInitLoading: true });
        return message.error(result.message);
      }
      data = result.data.sort((a, b) => {
        if (!a.tree_type || !b.tree_type) {
          return 0;
        }
        return a.tree_type - b.tree_type;
      });
    } else {
      data = this.state.orgTreeTabList;
    }
    const { treeProps } = this.state;
    const { keys } = treeProps;
    const curData = data[currentIndex];
    if (curData) {
      if (!isOptTree) {
        const params = {};
        if (curData.tree_type === 2) {
          params["orgType"] = curData.org_type;
        }
        this.loadOrgTree(
          Object.assign({ treeType: curData.tree_type }, params)
        );
      }
      this.treeItemPos = {};
      if (keys && keys.length) {
        this.setState({
          treeProps: Object.assign(treeProps, {
            keys: [],
            loadedKeys: [],
          }),
        });
      }
      this.setState({
        orgTreeTabIndex: currentIndex,
        orgTreeTabList: data,
        orgTreeTabSelectData: curData,
      });
    }
  }

  /**
   * @description 获取组织树(不可动)
   * @param payload
   */
  async loadOrgTree(payload = {}) {
    const { treeType, orgType, isTreeDisabled, target } = payload;
    const { orgTreeTabSelectData, treeProps, isInitLoading, selectedOrgId } =
      this.state;
    const { userInfo } = this.props;
    const { oid } = userInfo;
    let ttype = treeType;
    let torgType = orgType;
    if (!treeType) {
      ttype = orgTreeTabSelectData.tree_type;
    }
    if (!orgType && orgTreeTabSelectData) {
      torgType = orgTreeTabSelectData.org_type;
    }
    let p = { ["load_root"]: 1 };
    if (ttype === 2) {
      p["org_type"] = torgType;
    }
    await this.getOrgInfo();
    await this.getOrgUserList();
    if (isTreeDisabled) {
      this.setState({ isInitLoading: false });
    }
    let result;
    if (selectedOrgId === oid) {
      result = (
        await getOrgTree(
          Object.assign(
            {
              org_id: selectedOrgId,
              tree_type: ttype,
            },
            p
          )
        )
      ).data;
    } else {
      result = (
        await locateOrgTree(
          Object.assign(
            {
              root_org_id: oid,
              org_id: selectedOrgId,
              tree_type: ttype,
            },
            p
          )
        )
      ).data;
    }
    if (isInitLoading) {
      this.setState({ isInitLoading: false });
    }
    if (result.code !== 0) {
      this.setState({
        statisData: {
          organizeCount: 0,
          personCount: 0,
        },
        treeProps: Object.assign(treeProps, { keys: [] }),
      });
      return message.error(result.message);
    }
    let data = result.data;
    let initValue = [];
    if (data && data[0]) {
      initValue.push(data[0].org_id + "");
    }
    const initDepartmentValue = treeProps.keys;
    if (selectedOrgId !== oid) {
      initValue.push(selectedOrgId + "");
    }
    if (initDepartmentValue && initDepartmentValue.length) {
      initValue = Array.from(new Set(initValue.concat(initDepartmentValue)));
    }
    let loadedValue = treeProps.loadedKeys;
    const renderData = (val) => {
      if (val && val.children) {
        loadedValue.push(val.org_id + "");
        val.children.forEach(renderData);
        return;
      }
      loadedValue.push(val.org_id + "");
    };
    data.forEach(renderData);
    const getOrgId = [selectedOrgId + ""];
    loadedValue = Array.from(new Set(loadedValue.concat(getOrgId)));
    this.setState(
      {
        treeProps: Object.assign(treeProps, {
          keys: initValue || [],
          loadedKeys: loadedValue || [],
          dataSource: data,
          autoExpandParent: true,
        }),
      },
      target
        ? () => {
            const currentKey = this.state.currentKey;
            const filterVal = initDepartmentValue.filter(
              (val) => currentKey !== val
            );
            const initVal = Array.from(new Set(getOrgId.concat(filterVal)));

            this.setState({
              treeProps: Object.assign(treeProps, {
                currentKey: getOrgId[0],
                keys: initVal,
              }),
            });
            target();
          }
        : null
    );
  }

  /**
   * @description 滚动效果(不可动)
   * @param orgId
   */
  scrollPos(orgId) {
    const el = document.getElementById(orgId);
    const pE = document.getElementById("member-manager-tree-container");
    if (!el || !pE) {
      return;
    }
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      delete this.scrollTimeout;
    }
    this.scrollTimeout = setTimeout(() => {
      pE.scrollTop = el.offsetTop - pE.offsetTop - 10;
      delete this.scrollToOrgId;
    }, 300);
  }

  render() {
    const { userInfo, history } = this.props;
    const {
      isInitLoading,
      selectedOrgId,
      pattern_sub,
      selectedOrgName,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      autoCompleteProps,
      treeProps,
      table,
    } = this.state;
    // 保留当前展开状态
    const onSaveTreeProps = () => {
      TREE_PROPS = this.state.treeProps;
    };

    const leftSiderProps = {
      _this: this,
      isInitLoading,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      onTabChange: (key) =>
        this.getTreeList({
          orgId: userInfo.oid,
          orgTreeTabIndex: key,
          isOpt: true,
        }),
      autoCompleteProps: {
        dataSource: autoCompleteProps.dataSource,
        onSearch: (e) => this.getAutoComplete(e),
        onSelect: (e) => {
          // this.searchForm.resetForm();
          if (e.orgId === selectedOrgId) {
            return;
          }
          e.target = () => {
            this.scrollToOrgId = e.orgId;
            this.scrollPos(e.orgId);
          };
          if (treeProps.loadedKeys.includes(e.orgId + "")) {
            this.setState(
              {
                selectedOrgId: e.orgId,
                isInitLoading: true,
                table: {
                  ...table,
                  page_num: 1,
                  org_id: e.orgId,
                },
                treeProps: Object.assign(treeProps, {
                  keys: Array.from(
                    new Set(treeProps.keys.concat([e.orgId + ""]))
                  ),
                  currentKey: e.orgId,
                  autoExpandParent: true,
                }),
              },
              async () => {
                e.target();
                await this.getOrgInfo();
                await this.getOrgUserList();
                this.setState({ isInitLoading: false });
              }
            );
          } else {
            this.setState(
              {
                isInitLoading: true,
                selectedOrgId: e.orgId,
                table: {
                  ...table,
                  page_num: 1,
                  org_id: e.orgId,
                },
              },
              () => this.loadOrgTree({ target: e.target })
            );
          }
        },
      },
      treeProps: {
        dataSource: treeProps.dataSource,
        keys: treeProps.keys,
        loadedKeys: treeProps.loadedKeys,
        currentKey: Number(treeProps.currentKey),
        autoExpandParent: treeProps.autoExpandParent,
        defaultSelectedKeys: [String(this.state.currentOrgId)],
        onExpand: (e) => {
          const { treeProps } = this.state;
          this.setState({
            treeProps: Object.assign(treeProps, {
              keys: e,
              autoExpandParent: false,
            }),
          });
        },
        onSelect: (e) => {
          if (selectedOrgId === e.id) return;
          this.setState(
            {
              isInitLoading: true,
              selectedOrgId: e.id,
              pattern_sub: e.pattern_sub,
              table: {
                ...table,
                page_num: 1,
                org_id: e.id,
              },
              treeProps: Object.assign(treeProps, { currentKey: e.id }),
            },
            () => {
              // this.searchForm.resetForm();
              this.loadOrgTree({ isTreeDisabled: true });
            }
          );
        },
        loadTreeData: (treeNode) => {
          return new Promise(async (resolve) => {
            if (
              (treeNode.props.children && treeNode.props.children.length > 0) ||
              !treeNode.props.dataRef.child_org_num
            ) {
              resolve();
              return;
            }
            const p = {};
            const { orgTreeTabSelectData, treeProps } = this.state;
            p["org_type"] = orgTreeTabSelectData.org_type;
            const result = (
              await getOrgTree(
                Object.assign(
                  {},
                  {
                    org_id: treeNode.props.dataRef.org_id,
                    tree_type: orgTreeTabSelectData.tree_type,
                    load_root: 0,
                  },
                  p
                )
              )
            ).data;
            if (result.code !== 0) {
              return message.error(result.message);
            }
            if (!result.data.length) {
              treeNode.props.dataRef.child_org_num = 0;
            } else {
              treeNode.props.dataRef.children = result.data.map((item) => {
                treeProps.loadedKeys.push(item.org_id + "");
                return { ...item, isLeaf: item.child_org_num === 0 };
              });
            }
            const payload = {
              treeProps: Object.assign(treeProps, {
                loadedKeys: Array.from(
                  new Set(
                    treeProps.loadedKeys.concat([
                      treeNode.props.dataRef.org_id + "",
                    ])
                  )
                ),
                dataSource: treeProps.dataSource,
              }),
            };
            this.setState(payload);
            resolve();
          });
        },
      },
    };
    console.log(
      "🚀 ~ Class ~ render ~ leftSiderProps.treeProps.treeProps:",
      treeProps
    );
    const { org_id } = table;
    const mainProps = {
      selectedOrgId,
      pattern_sub,
      selectedOrgName,
      history,
      onSaveTreeProps,
      table: {
        ...table,
        onChange: (page_num) =>
          this.setState(
            {
              table: {
                ...table,
                page_num,
              },
            },
            () => this.getOrgUserList()
          ),
        onDelete: async (period_id) => {
          const { data: wrapData = {} } = await deleteCommitteePeriodUrl({
            period_id,
          });
          const { data: params = {} } = await getOrgParams({ org_id });
          const { data: config = {} } = params;
          const { code, message: m } = wrapData;
          if (code === 0) {
            message.success("删除成功");
            this.setState(
              {
                table: {
                  ...table,
                  config,
                },
              },
              () => this.getOrgUserList()
            );
          } else {
            message.error(m);
          }
        },
      },
    };

    return (
      <div>
        <IndexView mainProps={mainProps} leftSiderProps={leftSiderProps} />
      </div>
    );
  }
}

export default connect(({ organizeData, userInfo }) => ({
  organizeData,
  userInfo,
}))(Class);
