// 变量定义
@primary-color: #278236;
@text-color: #666666;
@active-color: #333333;
@line-color: #ff2121;
.scale-0-9 {
  transform: scale(0.8);
}
.data-menu {
  display: flex;
  align-items: center;

  .menu-item {
    margin-left: 12px;
    font-size: 18px;
    font-weight: 500;
    color: @text-color;
    cursor: pointer;

    &.menu-active {
      color: @active-color;
      font-weight: bold;
      position: relative;

      &::after {
        content: "";
        display: inline-block;
        width: 70%;
        height: 2px;
        background: @line-color;
        border-radius: 2px;
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translate(-50%, 0);
      }
    }
  }
}

.pyramid {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
  padding: 0px 29px 27px 10px;

  .pyramid-item {
    position: relative;
    transform: translate(-60px);

    .pyramid-img {
      position: relative;
      margin: 0 auto;

      .pyramid-label {
        position: absolute;
        top: 40%;
        left: -30px;
        font-size: 22px;
        font-weight: 400;
        color: @text-color;
        line-height: 26px;
      }

      img {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }

      .select {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 100%;
        display: flex;
        align-items: center;
        width: 341px;
        height: 75.5px;
        clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
        background: linear-gradient(
          263deg,
          rgba(217, 217, 217, 0) 0%,
          rgba(39, 130, 54, 0.11) 122%
        );

        .select-line {
          height: 1px;
          background: @primary-color;
          width: 191px;
        }

        .text {
          margin-left: 10px;
          font-size: 22px;
          color: @primary-color;
          line-height: 26px;
        }
      }

      .split-line {
        position: absolute;
        inset: 0;

        .line-box {
          .left-line,
          .right-line {
            // 线条样式
          }
        }
      }
    }
  }

  // 金字塔各层级样式
  .pyramid-1 {
    .pyramid-img {
      width: 74px;
      height: 86px;
    }
    .select {
      transform: translate(-40px);
    }
  }

  .pyramid-2 {
    margin-top: -10px;
    .pyramid-img {
      width: 162.23px;
      height: 93.9px;
      .pyramid-label {
        left: -30px !important;
      }
    }
    .select {
      width: 271px;
      transform: translate(-40px);
      .select-line {
        width: 146px;
      }
    }
  }

  .pyramid-3 {
    margin-top: -20px;
    .pyramid-img {
      width: 265.23px;
      height: 112px;
    }
    .select {
      width: 271px;
      transform: translate(-45px, 10px);
      .select-line {
        width: 126px;
      }
    }
  }

  .pyramid-4 {
    margin-top: -20px;
    .pyramid-img {
      width: 384.87px;
      height: 140.51px;
    }
    .select {
      width: 271px;
      transform: translate(-50px, 20px);
      .select-line {
        width: 66px !important;
      }
    }
  }
}
