import React, { useState } from "react";
import {
  <PERSON>u,
  Dropdown,
  I<PERSON>,
  <PERSON><PERSON>,
  Popconfirm,
  message,
  Divider,
} from "antd";
import SelfIcon from "components/self-icon";
import axios from "axios";
import { headers } from "client/tool/axios";
import { ucHost } from "client/apis/config";
import { desensitizationPhone, logout } from "tool/util";
import { getParam } from "tool/util";
import PasswordResetModal from "../ResetPassword";
import "./header.less";

const menu = (orgList = [], currentOrg = {}, dispatch, history, token) => {
  const changeOrgHandler = function ({ item, key, keyPath }, token) {
    const { props } = item || {},
      { params } = props || [],
      { name, oid, phone, type, user_id } = params;

    if (name !== currentOrg.name || oid !== currentOrg.oid) {
      // console.log(params);
      // console.log('原请求头', headers());
      // console.log('组织列表', orgList);
      // console.log('当前组织', currentOrg);
      // console.log('切换组织', name, oid);
      // console.log(ucHost);
      currentOrg.name = name;
      currentOrg.oid = oid;
      currentOrg.token = "";
      currentOrg.menu = [];

      typeof window !== undefined && window.sessionStorage.setItem("_oid", oid);
      typeof window !== undefined && window.sessionStorage.setItem("_on", name);
      typeof window !== undefined &&
        window.sessionStorage.setItem("_type", type);
      typeof window !== undefined && window.sessionStorage.removeItem("_tk");
      // console.log('切换后组织', currentOrg);
      dispatch({
        type: "userInfo/chooseOid",
        payload: {
          ...params,
          token,
          callback(loginUsername) {
            message.success(
              "登录成功，欢迎" + loginUsername || "" + "!",
              1,
              () => {
                typeof window !== "undefined" &&
                  (window.location.href = `/?region_id=${getParam().region_id || process.env.region_id
                    }`);
                // history.push('/')
                // typeof window !== 'undefined' && (window.location.href = '/');
              }
            );
            // 清除党小组
            dispatch({
              type: "partyGroup/set",
              payload: {
                org_id: null,
                org_name: null,
              },
            });
          },
          errorHandler(code) {
            if (code) {
              message.error("登录超时，请重新登录");
            } else {
              message.error("获取菜单为空，登录失败");
            }
            window.location.href = `/login?region_id=${__TOG__.regionId || process.env.region_id}`;
          },
        },
      });

      // console.log('新请求头', headers());

      // axios.post('/api/user/chooseOrg', {
      //     user_id,
      //     name: encodeURIComponent(name),
      //     oid,
      //     type
      // }).then(
      //     (response) => {
      //         console.log(response);
      //     },
      //     (error) => {
      //         console.error(error);
      //     }
      // )
    }
    //在这里应该调用组织信息接口/api/user/choose
    //这里应该设置两个本地存储中的值，一个是currentOrg当前组织，一个是_oid，请求头中使用的组织id
    // message.info(`Click on item ${org}`);
  };
  return (
    <Menu
      onClick={(item) => changeOrgHandler(item, token)}
      selectedKeys={currentOrg.oid ? [currentOrg.oid.toString()] : null}
    >
      {orgList &&
        orgList.length !== 0 &&
        orgList.map((org, index) => {
          return (
            <Menu.Item key={org.oid} params={org}>
              {org.name || "-"}
              {/*是当前选中组织，显示选中状态*/}
              {currentOrg &&
                currentOrg.oid === org.oid &&
                currentOrg.name === org.name && <Icon type="check" />}
            </Menu.Item>
          );
        })}
    </Menu>
  );
};

const BHeader = ({ history, userInfo, dispatch }) => {
  // console.log(history);
  // console.log(userInfo);
  const { user_name, phone, token } = userInfo || {};
  const [visible, setVisible] = useState(false);
  const confirm = () => {
    logout().then((res) => {
      if (res.data.code !== 0) return message.error(res.data.message);
      message.info("成功退出");
      // window.sessionStorage.setItem('_tk', '-1');
      // window.sessionStorage.setItem('_uid', '-1');
      // window.sessionStorage.setItem('_un', '');
      // window.sessionStorage.setItem('_type', '-1');
      // window.sessionStorage.setItem('_oid', '-1');
      // window.sessionStorage.setItem('_on', '');
      // window.sessionStorage.setItem('_mu', JSON.stringify([]));
      // window.sessionStorage.setItem('org-list', JSON.stringify([]));
      // 清楚党小组
      dispatch({
        type: "partyGroup/set",
        payload: {
          org_id: null,
          org_name: null,
        },
      });
      window.location.href = `/login?region_id=${__TOG__.regionId || process.env.region_id}`;
    });
  };

  const cancel = () => { };
  const onResetPassword = () => {
    setVisible(true);
  };
  const currentOrg =
    typeof window !== "undefined"
      ? JSON.parse(window.sessionStorage.getItem("current-org")) || {}
      : {},
    orgList =
      typeof window !== "undefined"
        ? JSON.parse(window.sessionStorage.getItem("org-list")) || []
        : [];

  const condition = (orgList) => orgList.org_type !== 102815;
  const orgList1 = orgList.filter(condition);
  return (
    <div className="bheader">
      {/* style={{ float: 'left', margin: '14px 22px' }} */}
      <div className={"bheader-org-select"}>
        <Dropdown
          overlay={menu(orgList1, currentOrg, dispatch, history, token)}
          size={"large"}
        >
          <a
            style={{ fontSize: "16px", color: "#000", textDecoration: "none" }}
          >
            {/*当仅有一个组织时，默认选择第一个组织作为当前登录组织，多个组织需要由登录后的组织选择页面确定（目前没有该页面）*/}
            {currentOrg.name}
            {/*当所属组织多于1个时，下拉箭头显示*/}
            {orgList1 && orgList1.length > 1 && <Icon type="down" />}
            {/* <Icon type="down" /> */}
          </a>
        </Dropdown>
      </div>
      {/* style={{ float: 'right', margin: '10px 20px' }} */}
      <div className={"bheader-user-logout"}>
        <div className={"user-wrapper"}>
          <SelfIcon type="gsg-geren" />
          <span onClick={onResetPassword}>
            {user_name}
            {phone ? ` - ${desensitizationPhone(phone)}` : ""}
          </span>
        </div>
        <Divider
          type="vertical"
          style={{ height: "38px", margin: "0 20px 0 20px" }}
        />
        <Popconfirm
          title="您确认退出吗?"
          onConfirm={confirm}
          onCancel={cancel}
          okText="确认"
          cancelText="取消"
          placement="bottomRight"
        >
          <div className="logout-button-wrapper">
            <SelfIcon type="gsg-tuichu" />
            <span className="logout-button">退出</span>
          </div>
        </Popconfirm>
      </div>
      <PasswordResetModal
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={confirm}
      />
    </div>
  );
};

export default BHeader;
