import React, { PureComponent } from 'react';
import { Modal } from 'antd';
// export interface PreviewModalProps {
//   src?: string;
//   name?: string;
//   visible: boolean;
//   onCancel: () => void;
// }

class PreviewModal extends PureComponent {
  render() {
    const { src, name, visible, onCancel } = this.props;
    return (
      <Modal visible={visible} width='800px' onCancel={onCancel} footer={false} title={name}>
        <img style={{ width: '100%' }} src={src} alt='' />
      </Modal>
    );
  }
}

PreviewModal.defaultProps = {};

export default PreviewModal;
