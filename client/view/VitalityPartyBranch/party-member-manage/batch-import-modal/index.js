import React, { PureComponent } from 'react'
import { Mo<PERSON>, Steps, Button, Checkbox, message, Progress } from "antd";
import FileDownload, { fileDownload } from "client/components/file-download";
import "./styles.less";
import { uploadUserFile, getImportRate } from "client/apis/member-management";


const { Step } = Steps;

// 批量导入
export default class BatchImportModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      value: [],
      uploadLoading: false,
      exportLoading: false,
      errorUuid: null,
      uuid: null,
      schedule: 0,
      total: 0,
      failure: 0,
      success: 0,
      fileValue: null,
    }
    this.inputRef = null;
    this.onClickDown = this.onClickDown.bind(this);
    this.onCheckChange = this.onCheckChange.bind(this);
    this.handleSure = this.handleSure.bind(this);
    this.handleStartImport = this.handleStartImport.bind(this);
    this.onInputChange = this.onInputChange.bind(this);
    this.handleCancel = this.handleCancel.bind(this);
    this.filterFieldList = this.filterFieldList.bind(this);
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.fieldList !== this.props.fieldList) {
      this.setState({
        value: this.filterFieldList(nextProps.fieldList).filter((item) => {
          const { rules = {} } = item;
          return rules.required
        }).map(({ field_id }) => field_id)
      })
    }
  }
  /**
   * 过滤一些不能批量上传的表单项
   * 1. 必须是显示的组件
   * 2. key不为 'is_bind', 'native'
   * 3. type不为 11, 14
   */
  filterFieldList(fieldList) {
    return fieldList.filter((item) => {
      return item.show && ['is_bind', 'native'].indexOf(item.key) === -1 && [11, 14].indexOf(item.type) === -1
    })
  }
  handleCancel() {
    const { onCancel } = this.props;
    this.setState({
      uploadLoading: false,
      exportLoading: false,
      errorUuid: null,
      uuid: null,
      schedule: 0,
      total: 0,
      failure: 0,
      success: 0,
      fileValue: null,
    })
    if (this.inputRef) this.inputRef.value = '';
    onCancel()
  }
  onInputChange(e) {
    this.setState({
      fileValue: e.target.files[0],
      uuid: null,
      schedule: 0,
      total: 0,
      failure: 0,
      success: 0,
    })
  }
  onClickDown() {
    this.setState({ visible: true })
  }
  onCheckChange(value) {
    this.setState({ value })
  }
  // 开始上传
  handleStartImport() {
    const { selectOrg } = this.props;
    const { fileValue } = this.state;
    const formData = new FormData();
    if (!fileValue) {
      message.error('请选择需要上传文件')
      return;
    }
    formData.append('file', fileValue)
    formData.append('org_id', selectOrg.id)
    this.setState({ uploadLoading: true });
    uploadUserFile(formData).then((res) => {
      const { code, status, data, message: msg } = res.data;
      if (code === 0 && status === 200) {
        this.setState({ uuid: data })
        this.getImportRate(data)
      } else {
        if (this.inputRef) this.inputRef.value = '';
        message.error(msg)
      }
    }).finally(() => {
      this.setState({ uploadLoading: false });
    })
  }
  // 轮询查看上传进度
  getImportRate(uuid) {
    getImportRate(uuid).then(res => {
      const { code, status, data } = res.data;
      if (code === 0 && status === 200) {
        this.setState({ ...data })
        if (data.schedule !== '100') {
          setTimeout(() => {
            this.getImportRate(uuid)
          }, 500);
        } else {
          this.setState({ uploadLoading: false })
          message.success("导入完成！")
        }
      }
    })
  }
  handleSure() {
    const { selectOrg } = this.props;
    const { value } = this.state;
    const url = `/user/org/user/excelTemplate?org_id=${selectOrg.id}&field_ids=${value}`;
    this.setState({ exportLoading: true })
    fileDownload(url).then(res => {
      message.success("导出成功！")
      this.setState({ visible: false })
    }).finally(() => {
      this.setState({ exportLoading: false })
    })
  }
  render() {
    const { visible, selectOrg, fieldList = [] } = this.props;
    const { uploadLoading, uuid, exportLoading, total, success, failure, schedule } = this.state;
    return (
      <div>
        <Modal
          title="批量导入人员"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={750}
          footer={null}
        >
          <div className="batch-import-modal-body">
            <Steps direction="vertical">
              <Step title={
                <div>
                  "第一步：模板下载"<Button className="btn" type="primary" loading={exportLoading} onClick={this.onClickDown}>下载模板</Button>
                </div>
              } description={
                <p className="des">
                  已选： <span style={{ fontSize: 20, fontWeight: 600 }}>{selectOrg.name}</span>
                  &nbsp;&nbsp;<span className="tip des">上传时请选择本组织进行上传操作！</span>
                </p>
              } />
              <Step status="process" title={
                <div>
                  第二步：上传修改完毕的模板表
                  <Button className="btn" type="primary" loading={uploadLoading}>
                    <input type="file" ref={(ref) => this.inputRef = ref} onChange={this.onInputChange} />
                  </Button>
                </div>
              } description={<p className="tip des">注意： 请在7天内进行上传，请勿修改文件名！</p>} />
              {
                !!schedule && (
                  <Step status="process" title={
                    <div>
                      第三步：{schedule === '100' ? '导入完成' : '导入文件中...'}

                    </div>
                  } description={
                    <div>
                      {`导入数据： 总数${total}，成功${success}条，失败${failure}条`}
                      {
                        (failure && !uploadLoading) && (
                          <FileDownload
                            type="link"
                            btnName="下载失败数据"
                            filePath={`/user/org/user/import-fail?uuid=${uuid}`}
                          />
                        )
                      }
                      <Progress percent={Number(schedule)} status={uploadLoading ? 'active' : 'normal'} />

                    </div>
                  } />
                )
              }
            </Steps>

            <div className="footer">
              <Button type="primary" onClick={this.handleStartImport}>开始导入</Button>
              <Button onClick={this.handleCancel}>取消</Button>
            </div>
          </div>
        </Modal>
        <Modal
          title="请选择下载模板包含的数据项"
          visible={this.state.visible}
          onCancel={() => this.setState({ visible: false })}
          width="300px"
          onOk={this.handleSure}
        >
          <Checkbox.Group
            className="batch-import-modal-checkbox"
            value={this.state.value}
            onChange={this.onCheckChange}
          >
            {
              this.filterFieldList(fieldList).map((item) => (
                <Checkbox key={item.field_id} disabled={item.rules ? item.rules.required : false} value={item.field_id}>{item.label}</Checkbox>
              ))
            }
          </Checkbox.Group>
        </Modal>
      </div>
    )
  }
}
