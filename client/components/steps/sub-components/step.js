import React from "react";
import "./step.less";

import propTypes from "prop-types";

const Step = (props) => {
  const { title, order, separator, index, noSeparator, current } = props;
  return (
    <div className={
      current === index ? "step-wrapper step-process" :
        current < index ? "step-wrapper step-wait" :
          current > index ? "step-wrapper step-finish" :
            "step-wrapper"
    } >
      <span className="step-order">{order || index + 1}</span>
      <span className="step-title">{title}</span>
      {
        !noSeparator &&
        (separator || <span className="step-separator">......</span>)
      }
    </div >
  )
}

Step.propTypes = {
  // 标题
  title: propTypes.oneOfType([propTypes.string, propTypes.element]),
  // 序号
  order: propTypes.oneOfType([propTypes.number, propTypes.string, propTypes.element]),
  // 分隔符，接收字符串或者React元素
  separator: propTypes.oneOfType([propTypes.string, propTypes.element]),
  index: propTypes.number,
  // 不具有分隔符
  noSeparator: propTypes.bool,
  // 当前步骤
  current: propTypes.number
}

Step.defaultProps = {
  title: "步骤标题",
  noSeparator: false,
  current: 0
}

export default Step;

