import React from "react";
import { Layout, Tabs, AutoComplete, Tree } from "antd";
import "client/view/member-management/index.less";
import "./left-sider.less";

const { Sider } = Layout;
const { TabPane } = Tabs;
const TreeNode = Tree.TreeNode;

const renderTab = (data) => {
  if (!data) return null;
  return data.map((item, index) => (
    <TabPane tab={item.tree_name} key={index} />
  ));
};

export default ({
  autoCompleteProps,
  treeProps,
  _this,
  orgTreeTabIndex,
  orgTreeTabList,
  orgTreeTabSelectData,
  defaultSelectedKeys,
  currentKey,
  onTabChange,
}) => {
  const renderAutoCompleteList = (data) =>
    data.map((item) => (
      <AutoComplete.Option
        className="member-manage-autoComplete"
        key={item.value}
        text={item.name}
        value={item.name}
        onClick={(e) =>
          autoCompleteProps.onSelect({
            orgId: item.value,
            org: item.name,
            target: true,
          })
        }
      >
        {item.name}
      </AutoComplete.Option>
    ));

  let parentEl;
  const getTreeItemPos = (node, record) => {
    const el = node && node.selectHandle;
    if (!parentEl) {
      parentEl = _this.treeContainer = document.getElementById(
        "member-manager-tree-container"
      );
    }
    if (el) {
      _this.treeItemPos[record.org_id] = el.offsetTop - parentEl.offsetTop;
    }
  };

  const renderTreeNodes = (data, level) => {
    level = level || 0;
    const length = data.length;
    let cssName = "";
    if (level < 1) {
      cssName = "first-item";
    }

    const resultData = length === 0 ? [] : data;
    return resultData.map((item, index) => {
      if (!item) {
        return null;
      }
      let shortName;
      let name = (shortName = (item.short_name || item.name).trim());
      if (name && name.length > 20) {
        name = name.substring(0, 20) + "...";
      }
      let currentCssName = "";

      if (item.org_id === treeProps.currentKey) {
        // currentCssName = "current-item";
      }
      if (
        item.children &&
        item.children.length
        // || (item.child_org && item.child_org.length)
      ) {
        return (
          <TreeNode
            isLeaf={item.child_org_num === 0}
            title={
              <div
                id={item.org_id}
                className={cssName + " " + currentCssName}
                onClick={() => {
                  // 1
                  treeProps.onSelect({
                    level,
                    id: item.org_id,
                    name: item.name,
                  });
                }}
              >
                <span title={item.name || shortName}>{name}</span>
              </div>
            }
            parentId={item.parent_id}
            key={item.org_id}
            dataRef={item}
          >
            {renderTreeNodes(
              item.children,
              // || item.child_org
              ++level
            )}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf={item.child_org_num === 0}
          title={
            <div
              id={item.org_id}
              className={cssName + " " + currentCssName}
              onClick={() => {
                // 2
                treeProps.onSelect({
                  level,
                  id: item.org_id,
                  name: item.name,
                });
              }}
            >
              <span title={item.name || shortName}>{name}</span>
            </div>
          }
          parentId={item.parent_id}
          key={item.org_id}
          dataRef={item}
        />
      );
    });
  };
  return (
    <div className="ta-left-sider-container">
      <div className="left-sider-content">
        <div className="top">
          <AutoComplete
            className="ta-auto-complete"
            allowClear
            onSearch={autoCompleteProps.onSearch}
            placeholder={"请输入组织名称"}
            optionLabelProp="value"
          >
            {renderAutoCompleteList(autoCompleteProps.dataSource)}
          </AutoComplete>
        </div>
        <div
          id="member-manager-tree-container"
          className="tree-container orgContainer"
        >
          <Tree
            className="tree"
            onExpand={treeProps.onExpand}
            expandedKeys={treeProps.keys}
            autoExpandParent={treeProps.autoExpandParent}
            loadedKeys={[]}
            selectedKeys={
              treeProps.currentKey ? [String(treeProps.currentKey)] : undefined
            }
            loadData={treeProps.loadTreeData}
          >
            {renderTreeNodes(treeProps.dataSource)}
          </Tree>
        </div>
      </div>
    </div>
  );
};
