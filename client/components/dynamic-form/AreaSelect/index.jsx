import React, { PureComponent } from 'react';
import { Cascader, message } from 'antd';
import { connect } from 'dva';
import { getArea } from 'client/apis/active-organization';

class AreaSelect extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      caseValue: ''
    }
    this.loadData = this.loadData.bind(this)
  }
  componentDidMount() {
    const { dispatch } = this.props;
    console.log(this.props)
    dispatch({
      type: "AreaModal/initData"
    })
  }

  async loadData(selectedOptions ) {
    console.log(selectedOptions)
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    let params = {
      pid: targetOption.adcode
    }
    let { data } = await getArea(params)
    if(data.code === 0) {
      for(let i = 0, length = data.data.length; i < length; i++) {
        let dataItem = data.data[i]
        let params = {
          pid: dataItem.adcode
        }
        let { data: childrenData } = await getArea(params)
        dataItem.label = dataItem.area_name
        dataItem.value = dataItem.adcode.toString()
        dataItem.isLeaf = selectedOptions.length > 1 || !childrenData.data.length
      }
      targetOption.children = data.data
      targetOption.loading = false
      const { dispatch } = this.props;
      await dispatch({
        type: "AreaModal/updateStateData",
        payload: { data: [...this.props.AreaModal.data] },
      });
    } else {
      message.error(data.message)
    }
  }
  
  render() {
    const { attr = {}, label } = this.props;
    const { value } = attr;
    const val = value instanceof Array ? value : undefined;
    return (
      <Cascader
        placeholder={`请选择${label}`}
        {...attr}
        value={val}
        options={this.props.AreaModal.data}
      />
    );
  }
}

export default connect(({ AreaModal }) => ({ AreaModal }))(AreaSelect);
