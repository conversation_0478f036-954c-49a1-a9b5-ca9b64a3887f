import React, { useState, useEffect } from "react";
import "./index.less";
import {
  Form,
  Table,
  message,
  Menu, Dropdown, Icon
} from "antd";
import {
  exportMotion,
  mockAllList,
} from "client/apis/cadre-portrait";

function index({ form, history }) {
  const [dataSource, setDataSource] = useState([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
  const [rowID, setRowID] = useState(0);
  const [rowName, setRowName] = useState("");
  useEffect(() => {
    loadData();
  }, []);
  // 加载数据
  const loadData = async ({ page: _page } = {}) => {
    const res = await mockAllList();
    if (res.data.code === 0) {
      const data = res.data.data;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    } else {
      message.error(res.data.mssage);
    }
    setLoading(false);
  };
  const onExportrow = (e, row) => {
    setRowID(row.mock_id);
    setRowName(row.mock_name);
  };
  const onExport = async (name) => {
    const file_name = rowName + ".xlsx";
    try {
      await exportMotion({ mock_id: rowID }, file_name);
      message.success("下载成功!");
    } catch (error) {
      message.error("下载失败!", error);
    }
  };
  const menu = (
    <Menu>
      <Menu.Item
        key="0"
        onClick={() => onExport('动议名单')}
      >
        动议名单
      </Menu.Item>
    </Menu>
  );
  const columns = [
    {
      title: "方案名称",
      dataIndex: "mock_name",
      key: "mock_name",
      width: 300,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },

    {
      title: "方案来源",
      dataIndex: "source",
      key: "source",
      width: 100,
      align: "center",
      render: (text) => <div style={{ textAlign: "center" }}>{text}</div>,
    },
    {
      title: "更新时间",
      dataIndex: "date",
      key: "date",
      width: 100,
      align: "center",
      render: (text) => <div style={{ textAlign: "center" }}>{text}</div>,
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            {
              record.status != -1 &&
              <Dropdown overlay={menu} trigger={['click']}>
                <a className="ant-dropdown-link" onClick={(e) => onExportrow(e, record)} >
                  导出 <Icon type="down" />
                </a>
              </Dropdown>
            }

          </div>
        );
      },
    },
  ];
  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  return (
    <div className="take-advice">
      <div className="content-box">
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
    </div>
  );
}

export default Form.create()(index);
