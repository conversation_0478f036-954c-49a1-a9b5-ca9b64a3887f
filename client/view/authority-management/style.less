.authorityManagement {
  .wrap {
    display: flex;
    flex-direction: row;
    padding: 30px 32px;

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 200px;
      height: 259px;
      border: 1px solid #E5E5E5;
      cursor: pointer;

      .childIcon {
        margin-top: 34px;
        color: #FF4D4F;
      }

      .childContent {
        color: #666666;
        font-size: 14px;
        padding: 8px;
      }

      .childName {
        font-family: FZLTHK--GBK1-0;
        font-size: 18px;
        color: #000001;
        font-weight: bold;
        text-align: center;
      }
    }

    .item {
      &:not(:first-child) {
        margin-left: 48px;
      }
    }
  }
}