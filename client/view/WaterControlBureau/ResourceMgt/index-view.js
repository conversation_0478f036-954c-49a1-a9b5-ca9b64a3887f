/**
 * 资源列表
 * <AUTHOR> 朱路
 */
import React, { Component, Fragment } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Divider,
  Table,
  Popconfirm,
  DatePicker,
  Cascader,
  Checkbox,
  Modal,
  Row,
  Col,
  TreeSelect,
} from "antd";
import InputWrap from "components/layout/input-wrap";
import { mobileHost } from "apis/config";
import DatePickerLocale from "config/DatePickerLocale.json";
import Focus from "./focus";

import Utils from "client/view/news-matrix/utils";
import {
  statusTypesArray,
  publicTypesArray,
} from "client/view/news-matrix/dict";
import config from "config/const-config";

export default ({
  onBack,
  dispatch,
  props,
  listLoading,
  crownList,
  dataList,
  saveSelectNews,
  crownChange,
  searchSubmit,
  deleteNews,
  pageNew,
  changePage,
  moveNews,
  showNews,
  editIndexShow,
  minusIndex,
  addIndex,
  numberChange,
  setIndex,
  setIndexSubmit,
  selectNews,
  selectedRowKeys,
  edtiIndexCancel,
  setFocusInfoSubmit,
  focusIsShow,
  toggleFocus,
  toEditFocusImage,
  goSetTop,
  focusIsEdit,
  focusDisplayImageUrl,
  showImageSubmitOpacity,
  showPushtoDialog,
  setShowStatus,
  tabsKey,
  statusValue,
}) => {
  const { history, form } = props;

  // //点击聚焦图片
  // const editPhotos = (row) => {
  // };
  const onselectHandle = () => {
    return false;
  };
  const { getFieldDecorator } = form;

  const formItemLayout = {
      labelCol: { span: 8, offset: 1 },
      wrapperCol: { span: 10 },
    },
    Option = Select.Option,
    //查询结果的表格header
    resultColumns = [
      {
        title: "资源标题",
        dataIndex: "title",
        key: "title",
        className: "news-list-title",
      },
      // {
      //     title: '来源',
      //     dataIndex: 'source',
      //     key: 'source',
      //     width: 180,
      //     align: 'center'
      // },
      {
        title: "主栏目",
        dataIndex: "column_name",
        key: "column_name",
        width: 100,
        align: "center",
      },
      {
        title: "副栏目",
        dataIndex: "sub_column_name",
        key: "sub_column_name",
        width: 300,
        align: "center",
        // render: text => {
        //     return text.map((st, i) => {
        //         if (st) {
        //             return <span key={st.news_column_id}>{i != 0 && "、"}{st.name}</span>
        //         }
        //         return null;
        //     })
        // }
      },
      {
        title: "资源状态",
        dataIndex: "status",
        key: "status",
        width: 110,
        align: "center",
        render: (text) => {
          return Utils.columns.status(text == 0 ? 4 : 5);
        },
      },
      // {
      //     title: "发布方式",
      //     dataIndex: 'type',
      //     key: 'type',
      //     width: 80,
      //     align: 'center',
      //     render: (text, row) => {
      //         switch (text) {
      //             case 1:
      //                 return row.reprint === 1 ? "转载" : "原创";
      //             case 2:
      //                 return "转发";
      //             case 3:
      //             default:
      //                 return "推送";
      //         }
      //     }
      // },
      {
        title: "更新时间",
        dataIndex: "create_time",
        key: "create_time",
        align: "center",
        width: 175,
      },
      {
        title: "置顶",
        dataIndex: "top_level",
        key: "top_level",
        width: 80,
        align: "center",
        render: (text, row) => {
          return (
            <Checkbox
              checked={text === 1 ? true : false}
              onChange={(e) => {
                goSetTop(e, row);
              }}
              style={{ width: 50, marginTop: "-2px" }}
            ></Checkbox>
          );
        },
      },
      {
        title: "操作",
        width: 240,
        className: "news-list-oper-set",
        align: "center",
        fixed: "right",
        render: (text, row) => {
          let texts = row.tags || "";
          let textArr = [];
          let isTrue = false;
          textArr = texts.split(",");
          for (let i = 0; i < textArr.length; i++) {
            if (textArr[i] === "2") {
              isTrue = true;
              break;
            }
          }
          /*
           * 聚焦的时候有聚焦图片按钮
           * */

          let visibilityHTML;
          if (row.type == 3) {
            if (row.visibility === 1) {
              visibilityHTML = (
                <Fragment>
                  <a
                    href="javascript:void (0)"
                    onClick={() => {
                      return setShowStatus(row);
                    }}
                  >
                    隐藏
                  </a>{" "}
                  &nbsp; <Divider type="vertical" />
                </Fragment>
              );
            } else {
              visibilityHTML = (
                <Fragment>
                  <a
                    href="javascript:void (0)"
                    onClick={() => {
                      return setShowStatus(row);
                    }}
                  >
                    显示
                  </a>{" "}
                  &nbsp; <Divider type="vertical" />
                </Fragment>
              );
            }
          }

          return (
            <span>
              <a
                style={{ display: isTrue === true ? "inline" : "none" }}
                href="javascript:void (0)"
                onClick={() => toEditFocusImage(row)}
              >
                {"聚焦图片"}
              </a>
              <Divider
                style={{ display: isTrue === true ? "inline" : "none" }}
                type="vertical"
              />
              {/* NOTE：分级管理模块 根据配置是否显示*/}
              {/* {config.enableOrganizeLevelManageModule && row.type != 3 && row.type != 2 && [1, 5].indexOf(row.status) != -1 ?
                        <Fragment>
                            <a href="javascript:void (0)" onClick={() => {
                                return showPushtoDialog(row);
                            }}>推送</a>
                            <Divider type="vertical" />
                        </Fragment>
                        : ''
                    } */}

              {visibilityHTML}
              {tabsKey == 1 && (
                <Fragment>
                  <a
                    href="javascript:void(0)"
                    onClick={() => {
                      // 排序调整
                      return setIndex(row);
                    }}
                  >
                    移动排序
                  </a>
                  <Divider type="vertical" />
                </Fragment>
              )}

              {row.type != 3 && row.type != 2 && (
                <Fragment>
                  <a
                    href="javascript:void(0)"
                    onClick={() => {
                      showNews(row);
                    }}
                  >
                    编辑
                  </a>
                  <Divider type="vertical" />
                  <Popconfirm
                    title="您确认删除吗?"
                    okText="确认"
                    cancelText="取消"
                    onConfirm={() => deleteNews(row)}
                    placement="bottomRight"
                  >
                    <a href="javascript:void(0)">删除</a>
                  </Popconfirm>
                </Fragment>
              )}
            </span>
          );
        },
      },
    ],
    draftColumns = [
      {
        title: "资源标题",
        dataIndex: "title",
        key: "title",
        className: "news-list-title",
      },
      // {
      //     title: '来源',
      //     dataIndex: 'source',
      //     key: 'source',
      //     width: 180,
      //     align: 'center'
      // },
      {
        title: "主栏目",
        dataIndex: "column_name",
        key: "column_name",
        width: 100,
        align: "center",
      },
      {
        title: "副栏目",
        dataIndex: "sub_column_name",
        key: "sub_column_name",
        width: 300,
        align: "center",
        // render: text => {
        //     return text.map((st, i) => {
        //         if (st) {
        //             return <span key={st.news_column_id}>{i != 0 && "、"}{st.name}</span>
        //         }
        //         return null;
        //     })
        // }
      },
      {
        title: "资源状态",
        dataIndex: "status",
        key: "status",
        width: 110,
        align: "center",
        render: (text) => {
          return Utils.columns.status(text == 0 ? 4 : 5);
        },
      },
      // {
      //     title: "发布方式",
      //     dataIndex: 'type',
      //     key: 'type',
      //     width: 80,
      //     align: 'center',
      //     render: (text, row) => {
      //         switch (text) {
      //             case 1:
      //                 return row.reprint === 1 ? "转载" : "原创";
      //             case 2:
      //                 return "转发";
      //             case 3:
      //             default:
      //                 return "推送";
      //         }
      //     }
      // },
      {
        title: "更新时间",
        dataIndex: "create_time",
        key: "create_time",
        align: "center",
        width: 175,
      },
      {
        title: "操作",
        width: 240,
        className: "news-list-oper-set",
        align: "center",
        fixed: "right",
        render: (text, row) => {
          let texts = row.tags || "";
          let textArr = [];
          let isTrue = false;
          textArr = texts.split(",");
          for (let i = 0; i < textArr.length; i++) {
            if (textArr[i] === "2") {
              isTrue = true;
              break;
            }
          }
          /*
           * 聚焦的时候有聚焦图片按钮
           * */

          let visibilityHTML;
          if (row.type == 3) {
            if (row.visibility === 1) {
              visibilityHTML = (
                <Fragment>
                  <a
                    href="javascript:void (0)"
                    onClick={() => {
                      return setShowStatus(row);
                    }}
                  >
                    隐藏
                  </a>{" "}
                  &nbsp; <Divider type="vertical" />
                </Fragment>
              );
            } else {
              visibilityHTML = (
                <Fragment>
                  <a
                    href="javascript:void (0)"
                    onClick={() => {
                      return setShowStatus(row);
                    }}
                  >
                    显示
                  </a>{" "}
                  &nbsp; <Divider type="vertical" />
                </Fragment>
              );
            }
          }

          return (
            <span>
              <a
                style={{ display: isTrue === true ? "inline" : "none" }}
                href="javascript:void (0)"
                onClick={() => toEditFocusImage(row)}
              >
                {"聚焦图片"}
              </a>
              <Divider
                style={{ display: isTrue === true ? "inline" : "none" }}
                type="vertical"
              />
              {/* NOTE：分级管理模块 根据配置是否显示*/}
              {/* {config.enableOrganizeLevelManageModule && row.type != 3 && row.type != 2 && [1, 5].indexOf(row.status) != -1 ?
                        <Fragment>
                            <a href="javascript:void (0)" onClick={() => {
                                return showPushtoDialog(row);
                            }}>推送</a>
                            <Divider type="vertical" />
                        </Fragment>
                        : ''
                    } */}

              {visibilityHTML}
              {tabsKey == 1 && (
                <Fragment>
                  <a
                    href="javascript:void(0)"
                    onClick={() => {
                      // 排序调整
                      return setIndex(row);
                    }}
                  >
                    移动排序
                  </a>
                  <Divider type="vertical" />
                </Fragment>
              )}

              {row.type != 3 && row.type != 2 && (
                <Fragment>
                  <a
                    href="javascript:void(0)"
                    onClick={() => {
                      showNews(row);
                    }}
                  >
                    编辑
                  </a>
                  <Divider type="vertical" />
                  <Popconfirm
                    title="您确认删除吗?"
                    okText="确认"
                    cancelText="取消"
                    onConfirm={() => deleteNews(row)}
                    placement="bottomRight"
                  >
                    <a href="javascript:void(0)">删除</a>
                  </Popconfirm>
                </Fragment>
              )}
            </span>
          );
        },
      },
    ],
    rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        saveSelectNews(selectedRows, selectedRowKeys);
      },
    };

  if (!config.enableOrganizeLevelManageModule) {
    //NOTE：分级管理模块 根据配置是否显示此列
    let index = resultColumns.findIndex((item) => {
      return item.title === "发布方式";
    });
    resultColumns.splice(index, 1);

    index = resultColumns.findIndex((item) => {
      return item.title === "发布组织";
    });
    resultColumns.splice(index, 1);
  }

  const parseTreeData = (dataSource, disabled) => {
    let targetSource = [];
    if (Array.isArray(dataSource) && dataSource.length !== 0) {
      targetSource = dataSource.map((value, index) => {
        value.title = value.column_name;
        value.value = String(value.column_id);
        value.key = String(value.column_id);
        // console.log(currentNode, value);
        if (Array.isArray(value.children) && value.children.length !== 0) {
          value.children = parseTreeData(value.children, value.disabled);
        }
        return value;
      });
    }
    return targetSource;
  };
  //在外层套一个根节点
  const wrapperRoot = (treeData) => {
    return [
      {
        title: "顶级",
        value: "0",
        key: "0",
        children: treeData,
      },
    ];
  };
  const TreeSelectProps = {
    placeholder: "请选择",
    dropdownMatchSelectWidth: false,
    treeData: wrapperRoot(parseTreeData(crownList)),
    style: {
      width: "100%",
    },
    dropdownStyle: {
      maxHeight: "200px",
      minWidth: "188px",
    },
    // defaultValue: baseNode || '0',
    treeDefaultExpandedKeys: ["0"],
    // onChange(value) {
    //   console.log(value);
    // }
  };

  return (
    <div>
      {/*查询信息按钮界面*/}
      <InputWrap>
        <Form layout="inline" onSubmit={searchSubmit} style={{ margin: 0 }}>
          <Form.Item
            label="更新时间"
            {...formItemLayout}
            labelCol={{ span: 8, style: { textAlign: "left" } }}
          >
            {getFieldDecorator("start_time")(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择"
                style={{ width: 169 }}
                locale={DatePickerLocale}
              />
            )}
          </Form.Item>
          <Form.Item
            label="至"
            colon={false}
            {...formItemLayout}
            labelCol={{ span: 7 }}
          >
            {getFieldDecorator("end_time")(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择"
                style={{ width: 169 }}
                locale={DatePickerLocale}
              />
            )}
          </Form.Item>
          <Form.Item
            label="所在栏目"
            {...formItemLayout}
            labelCol={{ span: 11 }}
          >
            {getFieldDecorator("column_id")(
              <TreeSelect {...TreeSelectProps} style={{ width: 169 }} />
            )}
          </Form.Item>
          <Form.Item label="关键词" {...formItemLayout}>
            {getFieldDecorator("keyword", {
              valuePropName: "checked",
            })(
              <Input
                placeholder="资源标题"
                style={{ width: 200 }}
                maxLength={30}
              />
            )}
          </Form.Item>
          {tabsKey === 1 && 
          <Form.Item {...formItemLayout} wrapperCol={{ offset: 16 }}>
            {getFieldDecorator("top_status", {
              valuePropName: "checked",
            })(
              <Checkbox style={{ width: 60, marginTop: "-2px" }}>置顶</Checkbox>
            )}
          </Form.Item>}
          <Form.Item {...formItemLayout} wrapperCol={{ offset: 16 }}>
            <Button htmlType="submit" type="primary">
              查询
            </Button>
          </Form.Item>
        </Form>
      </InputWrap>

      {/*显示的查询结果*/}
      <Table
        rowKey={"resource_id"}
        dataSource={dataList ? dataList : []}
        columns={tabsKey === 1 ? resultColumns : draftColumns}
        rowClassName="news-list-table"
        pagination={{
          pageSize: pageNew.pageSize,
          total: pageNew.total,
          current: pageNew.pageNum,
        }}
        bordered
        rowSelection={rowSelection}
        loading={listLoading}
        onChange={changePage}
        scroll={{ x: "auto" }}
      />
      <Modal
        // title="排序调整"
        title={
          <div className="ant-modal-title" style={{ padding: "13px 24px" }}>
            排序调整
          </div>
        }
        visible={editIndexShow}
        width="500px"
        onCancel={edtiIndexCancel}
        centered={true}
        onOk={setIndexSubmit}
        maskClosable={false}
        bodyStyle={{ paddingTop: "40px" }}
        footer={
          <div style={{ paddingBottom: "45px", paddingTop: "14px" }}>
            <Row>
              <Col span="5"></Col>
              <Col span="7">
                <button
                  type="button"
                  className="ant-btn ant-btn-primary"
                  style={{ float: "left", width: "115px" }}
                  onClick={setIndexSubmit}
                >
                  <span>确 定</span>
                </button>
              </Col>
              <Col span="7">
                <button
                  type="button"
                  className="ant-btn"
                  style={{ width: "115px" }}
                  onClick={edtiIndexCancel}
                >
                  <span>取 消</span>
                </button>
              </Col>
              <Col span="5"></Col>
            </Row>
          </div>
        }
      >
        <Form layout="inline" style={{ margin: 0 }}>
          <Row>
            <Col span="3"></Col>
            <Col span="18" offset={2}>
              <div style={{ lineHeight: "32px" }}>
                <span style={{ float: "left", marginTop: "6px" }}>
                  设置序号
                </span>
                <div style={{ marginLeft: "20px" }}>
                  <span onClick={minusIndex} className="minus-a">
                    -
                  </span>
                  <Form.Item style={{ float: "left", marginRight: "15px" }}>
                    {getFieldDecorator("sort_num", {
                      rules: [{ required: true, message: "请输入序号值" }],
                    })(
                      // <InputNumber  placeholder='请输入' style={{ width: 80, float:'left' }} maxLength={30} />
                      <Input
                        onKeyUp={numberChange}
                        placeholder="请输入"
                        style={{ width: "100px", textAlign: "center" }}
                      />
                    )}
                  </Form.Item>
                  <a
                    className="plus-a"
                    onSelect={onselectHandle}
                    onClick={addIndex}
                  >
                    +
                  </a>
                </div>
                <div style={{ clear: "both" }}></div>
              </div>
            </Col>
            <Col span="3"></Col>
          </Row>
        </Form>
      </Modal>
      {Array.isArray(dataList) && dataList.length !== 0 && (
        <div className="news-table-bottom">
          <Form layout="inline" style={{ marginLeft: "1%" }}>
            <Form.Item
              label="所选资源移动到"
              labelCol={{ span: 12 }}
              wrapperCol={{ span: 10 }}
            >
              {getFieldDecorator("movenews")(
                <Cascader
                  fieldNames={{
                    label: "column_name",
                    value: "value",
                    children: "children",
                  }}
                  options={parseTreeData(crownList)}
                  style={{ width: 169 }}
                  placeholder="全部"
                  onChange={(e) => {
                    const evt = {
                      status: false,
                      value: e,
                    };
                    crownChange(evt);
                  }}
                  // changeOnSelect
                />
              )}
            </Form.Item>
            <Form.Item wrapperCol={{ offset: 12 }}>
              <Button type="primary" onClick={moveNews}>
                确认移动
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}
      {focusIsShow ? (
        <Focus
          showImageSubmitOpacity={showImageSubmitOpacity}
          focusDisplayImageUrl={focusDisplayImageUrl}
          setFocusInfoSubmit={setFocusInfoSubmit}
          dispatch={dispatch}
        />
      ) : null}
    </div>
  );
};
