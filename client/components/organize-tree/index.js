import React, { Component } from "react";
import { Tree, message as Message } from "antd";
import { fetchOrganisitionTree } from "apis/organisition";
import "./index.less";

import propTypes from "prop-types";

class OrganizeTree extends Component {
  constructor() {
    super();
    this.state = {
      treeData: []
    }
  }

  async fetchData(orgId = 0) {
    const response = await fetchOrganisitionTree({ org_id: orgId });
    // console.log(response);
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    const treeData = data;
    this.setState({
      treeData
    })
  }

  componentWillMount() {
    const { orgId } = this.props;
    this.fetchData(orgId);
    // console.log(this.state);
  }

  render() {
    const TreeNode = Tree.TreeNode;
    const treeProps = {
      checkable: true,
      multiple: true,
      showIcon: false,
      defaultExpandAll: true
    };
    const { treeData } = this.state;
    const treeNodeRender = (treeData = []) => {
      const treeChildrenRender = (node) => {
        if (node) {
          if (node.children) {
            const children = node.children;
            if (Array.isArray(children) && children.length !== 0) {
              return (
                children.map((node) => {
                  return (
                    <TreeNode title={node.name} key={node.organization_id} >
                      {treeChildrenRender(children)}
                    </TreeNode>
                  )
                })
              )
            }
          }
        }
        return null;
      }
      return (
        <Tree {...treeProps}>
          {
            (Array.isArray(treeData) && treeData) &&
            treeData.map((node, index) => {
              // console.log(node);
              if (node.children) {
                const children = node.children;
                if (Array.isArray(children) && children.length !== 0) {
                  return (
                    <TreeNode title={node.name} key={node.organization_id} >
                      {treeChildrenRender(node)}
                    </TreeNode>
                  )
                }
              }
              return (<TreeNode title={node.name} key={node.organization_id} />)
            })
          }
        </Tree>
      )
    }
    return (
      <div className="organize-tree">
        {treeNodeRender(treeData)}
      </div >
    )
  }
}

OrganizeTree.propTypes = {
  // 上级传入组织树根节点ID
  orgId: propTypes.number
}

export default OrganizeTree;