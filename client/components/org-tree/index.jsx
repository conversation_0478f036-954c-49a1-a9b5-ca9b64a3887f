import React, { Component } from "react";
import LeftSider from "./left-sider";
import { connect } from "dva";
import {
  getOrgTree,
  locateOrgTree,
  getTreeList,
  findOrgByName,
  getOrgInfo,
  findOrgByWhere,
  deleteOrgTags,
  insertOrgTags,
  getNativeArea,
  getArea,
  getOrgUserInfo,
} from "client/apis/organize";
import { message, Drawer, Row, Col, Button } from "antd";

import "./index.less";

class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isInitLoading: false,
      currentOrgId: null,
      selectedOrgId: null,
      orgTreeTabIndex: 0,
      orgTreeTabList: [],
      orgTreeTabSelectData: null,
      statisData: {
        organizeCount: 0,
        personCount: 0,
      },
      treeProps: {
        dataSource: [],
        keys: [],
        loadedKeys: [],
        currentKey: null,
        autoExpandParent: true,
      },
      autoCompleteProps: {
        dataSource: [],
      },
      search: {
        org_id: undefined,
        page: 1,
        page_size: 10,
      },
      table: {
        loading: false,
        total: 0,
        dataSource: [],
      },
      sider: {
        visible: false,
      },
      tagList: [],
      filterTagList: [], // 过滤的标签
      orgChooseList: [],
      labelModalVisible: false,
      labelModalTitle: "",
      labelDeleteStatus: false,
      labelModalLoading: false,
      /** ---------用户中心4.0-动态表单相关------------------ **/
      drawerVisible: false,
      drawerTitle: "新建组织",
      drawerLoading: false,
      extraAttr: {},
      extraValue: {},
      fieldList: [], // 动态表单数据
      showSubmitBtn: true,
      showCancelBtn: true,
      areaOptions: [], // 省市区options数据
      userDeatils: {}, // 编辑存放的用户详情
      cadreOid: null,
      /** ---------用户中心4.0-动态表单相关------------------ **/
    };
    this.setTreeData.bind(this);
    this.getTreeData.bind(this);
    this.scrollPos.bind(this);
  }
  componentDidMount() {
    const { dispatch, userInfo } = this.props;
    const orgId = userInfo.oid;
    const { orgTreeTabIndex, search, treeProps } = this.state;
    this.setState(
      {
        currentOrgId: orgId,
        selectedOrgId: orgId,
        search: {
          ...search,
          org_id: orgId,
        },
      },
      () => {
        !treeProps.currentKey &&
          this.getTreeList({ orgId, orgTreeTabIndex }, "init");
        // this.getTagList();
        // dispatch({ type: 'organizeData/loadculture' });
        // dispatch({ type: 'organizeData/loadSex' });
        // dispatch({ type: 'organizeData/loadDepment' });
        // dispatch({ type: 'organizeData/loadCensus' });
        // dispatch({ type: 'organizeData/loadpolitical' });
        // dispatch({ type: 'organizeData/loadType' });
        // dispatch({ type: 'organizeData/loadRecord' });
        // dispatch({ type: 'organizeData/loadEthnic' });
        // dispatch({ type: 'organizeData/loadLevel' });
        // dispatch({ type: 'organizeData/loadArea' });
        // dispatch({ type: 'organizeData/loadPartyMembers' });
        // dispatch({ type: 'organizeData/loadCommunistYouth' });
        // dispatch({ type: 'organizeData/loadUnion' });
        // dispatch({ type: 'organizeData/loadFede' });
        // dispatch({ type: 'organizeData/loadPositionCode' });
      }
    );
    // this.initArea();
  }
  setTreeData(treeProps) {
    this.setState({
      treeProps: {
        ...this.state.treeProps,
        ...treeProps,
      },
    });
  }
  getTreeData() {
    return this.state.treeProps;
  }
  scrollPos(orgId) {
    const el = document.getElementById(orgId);
    const pE = document.getElementById("member-manager-tree-container");
    if (!el || !pE) {
      return;
    }
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      delete this.scrollTimeout;
    }
    this.scrollTimeout = setTimeout(() => {
      console.log(el.offsetTop - pE.offsetTop - 10);
      pE.scrollTop = el.offsetTop - pE.offsetTop - 10;
      delete this.scrollToOrgId;
    }, 300);
  }
  async getTreeList(payload = {}, type) {
    const { orgId, orgTreeTabIndex, isOpt, isOptTree } = payload;
    const currentIndex = orgTreeTabIndex || 0;
    let data;
    if (!isOpt) {
      this.setState({ isInitLoading: true });
      const result = (await getTreeList(orgId)).data;
      if (result.code !== 0) {
        this.setState({ isInitLoading: true });
        return message.error(result.message);
      }
      data = result.data.sort((a, b) => {
        if (!a.tree_type || !b.tree_type) {
          return 0;
        }
        return a.tree_type - b.tree_type;
      });
    } else {
      data = this.state.orgTreeTabList;
    }
    const { treeProps } = this.state;
    const { keys } = treeProps;
    const curData = data[currentIndex];
    if (curData) {
      if (!isOptTree) {
        const params = {};
        if (curData.tree_type === 2) {
          params["orgType"] = curData.org_type;
        }
        this.loadOrgTree(
          Object.assign({ treeType: curData.tree_type }, params)
        );
      }
      this.treeItemPos = {};
      if (keys && keys.length && !type) {
        this.setState({
          treeProps: Object.assign(treeProps, {
            keys: [],
            loadedKeys: [],
          }),
        });
      }
      this.setState(
        {
          orgTreeTabIndex: currentIndex,
          orgTreeTabList: data,
          orgTreeTabSelectData: curData,
        }
        // () => {
        //   this.getFormConfigList();
        // }
      );
    }
  }
  getAutoComplete(value) {
    const { currentOrgId, autoCompleteProps, orgTreeTabSelectData } =
      this.state;
    if (this.autoCompleteTimeout) {
      clearTimeout(this.autoCompleteTimeout);
      this.autoCompleteTimeout = null;
    }
    value = value.trim();
    if (!value) {
      this.setState({
        autoCompleteProps: Object.assign(autoCompleteProps, { dataSource: [] }),
      });
    } else {
      this.autoCompleteTimeout = setTimeout(async () => {
        const result = (
          await findOrgByName({
            org_id: sessionStorage.getItem("_oid"),
            org_name: value,
            tree_type: orgTreeTabSelectData.tree_type,
          })
        ).data;
        if (result.code !== 0) {
          return message.error(result.message);
        }
        const data = result.data;
        this.setState({
          autoCompleteProps: Object.assign(autoCompleteProps, {
            dataSource: data
              .slice(0, data.length > 10 ? 10 : data.length)
              .map((val) => ({
                value: val.org_id,
                name: val.org_name,
              })),
          }),
        });
      }, 300);
    }
  }
  getOrgUserList() {
    const { search, table } = this.state;
    this.setState({ table: { ...table, loading: true } }, async () => {
      const { data: wrapData = {} } = await findOrgByWhere({ ...search });
      const { data: dataSource = [], total } = wrapData;
      this.setState({
        table: {
          ...table,
          ...search,
          total,
          dataSource,
          loading: false,
        },
      });
    });
  }
  /**
   * @description 获取组织树(不可动)
   * @param payload
   */
  async loadOrgTree(payload = {}) {
    const { treeType, orgType, isTreeDisabled, target } = payload;
    const { orgTreeTabSelectData, treeProps, isInitLoading, selectedOrgId } =
      this.state;
    const { userInfo } = this.props;
    const { oid } = userInfo;
    let ttype = treeType;
    let torgType = orgType;
    if (!treeType) {
      ttype = orgTreeTabSelectData.tree_type;
    }
    if (!orgType && orgTreeTabSelectData) {
      torgType = orgTreeTabSelectData.org_type;
    }
    let p = { ["load_root"]: 1 };
    if (ttype === 2) {
      p["org_type"] = torgType;
    }
    if (isTreeDisabled) {
      this.setState({ isInitLoading: false });
    }
    let result;
    if (selectedOrgId === oid) {
      result = (
        await getOrgTree(
          Object.assign(
            {
              org_id: selectedOrgId,
              tree_type: ttype,
            },
            p
          )
        )
      ).data;
    } else {
      result = (
        await locateOrgTree(
          Object.assign(
            {
              root_org_id: oid,
              org_id: selectedOrgId,
              tree_type: ttype,
            },
            p
          )
        )
      ).data;
    }
    if (isInitLoading) {
      this.setState({ isInitLoading: false });
    }
    if (result.code !== 0) {
      this.setState({
        statisData: {
          organizeCount: 0,
          personCount: 0,
        },
        treeProps: Object.assign(treeProps, { keys: [] }),
      });
      return message.error(result.message);
    }
    let data = result.data;
    let initValue = [];
    if (data && data[0]) {
      initValue.push(data[0].org_id + "");
    }
    const initDepartmentValue = treeProps.keys;
    if (selectedOrgId !== oid) {
      initValue.push(selectedOrgId + "");
    }
    if (initDepartmentValue && initDepartmentValue.length) {
      initValue = Array.from(new Set(initValue.concat(initDepartmentValue)));
    }
    let loadedValue = treeProps.loadedKeys;
    const renderData = (val) => {
      if (val && val.children) {
        loadedValue.push(val.org_id + "");
        val.children.forEach(renderData);
        return;
      }
      loadedValue.push(val.org_id + "");
    };
    data.forEach(renderData);
    const getOrgId = [selectedOrgId + ""];
    loadedValue = Array.from(new Set(loadedValue.concat(getOrgId)));
    this.setState(
      {
        treeProps: Object.assign(treeProps, {
          keys: initValue || [],
          loadedKeys: loadedValue || [],
          dataSource: data,
          autoExpandParent: true,
        }),
      },
      target
        ? () => {
            const currentKey = this.state.currentKey;
            const filterVal = initDepartmentValue.filter(
              (val) => currentKey !== val
            );
            const initVal = Array.from(new Set(getOrgId.concat(filterVal)));
            this.setState({
              treeProps: Object.assign(treeProps, {
                currentKey: getOrgId[0],
                keys: initVal,
              }),
            });
            target();
          }
        : null
    );
  }
  render() {
    const { userInfo } = this.props;
    const {
      isInitLoading,
      selectedOrgId,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      autoCompleteProps,
      treeProps,
      tagList,
      filterTagList,
      table,
      sider,
      labelModalVisible,
      labelModalTitle,
      labelDeleteStatus,
      orgChooseList,
      labelModalLoading,
      search,
      cadreOid,
    } = this.state;
    const leftSiderProps = {
      cadreOid,
      _this: this,
      isInitLoading,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      onTabChange: (key) =>
        this.getTreeList({
          orgId: userInfo.oid,
          orgTreeTabIndex: key,
          isOpt: true,
        }),
      autoCompleteProps: {
        dataSource: autoCompleteProps.dataSource,
        onSearch: (e) => this.getAutoComplete(e),
        onSelect: (e) => {
          console.log(e, selectedOrgId, "-------------------------e1");
          // this.searchForm.resetForm();
          if (e.orgId === selectedOrgId) {
            return;
          }
          e.target = () => {
            this.scrollToOrgId = e.orgId;
            this.scrollPos(e.orgId);
          };
          if (treeProps.loadedKeys.includes(e.orgId + "")) {
            this.setState(
              {
                cadreOid: e.orgId || e.id,
                selectedOrgId: e.orgId,
                isInitLoading: true,
                search: {
                  org_id: e.orgId,
                  org_name: "",
                  page: 1,
                  page_size: 10,
                },
                treeProps: Object.assign(treeProps, {
                  keys: Array.from(
                    new Set(treeProps.keys.concat([e.orgId + ""]))
                  ),
                  currentKey: e.orgId,
                  autoExpandParent: true,
                }),
              },
              async () => {
                e.target();
                await this.getOrgUserList();
                this.setState({ isInitLoading: false });
              }
            );
          } else {
            this.setState(
              {
                cadreOid: e.orgId || e.id,
                isInitLoading: true,
                selectedOrgId: e.orgId,
              },
              () => this.loadOrgTree({ target: e.target })
            );

            this.props.onChange([e.orgId || e.id], e);
          }
        },
      },
      treeProps: {
        dataSource: treeProps.dataSource,
        keys: treeProps.keys,
        loadedKeys: treeProps.loadedKeys,
        currentKey: Number(treeProps.currentKey),
        autoExpandParent: treeProps.autoExpandParent,
        onExpand: (e) => {
          const { treeProps } = this.state;
          this.setState({
            treeProps: Object.assign(treeProps, {
              keys: e,
              autoExpandParent: false,
            }),
          });
        },
        onSelect: (e) => {
          console.log("运行");
          if (selectedOrgId === e.id) return;
          this.setState(
            {
              cadreOid: e.id,
              isInitLoading: true,
              selectedOrgId: e.id,
              search: {
                org_id: e.id,
                org_name: "",
                page: 1,
                page_size: 10,
              },
              treeProps: Object.assign(treeProps, { currentKey: e.id }),
            },
            () => {
              // this.searchForm.resetForm();
              this.props.onChange([e.orgId || e.id], e);
              this.loadOrgTree({ isTreeDisabled: true });
            }
          );
        },
        loadTreeData: (treeNode) => {
          return new Promise(async (resolve) => {
            if (
              (treeNode.props.children && treeNode.props.children.length > 0) ||
              !treeNode.props.dataRef.child_org_num
            ) {
              resolve();
              return;
            }
            const p = {};
            const { orgTreeTabSelectData, treeProps } = this.state;
            p["org_type"] = orgTreeTabSelectData.org_type;
            const result = (
              await getOrgTree(
                Object.assign(
                  {},
                  {
                    org_id: treeNode.props.dataRef.org_id,
                    tree_type: orgTreeTabSelectData.tree_type,
                    load_root: 0,
                  },
                  p
                )
              )
            ).data;
            if (result.code !== 0) {
              return message.error(result.message);
            }
            if (!result.data.length) {
              treeNode.props.dataRef.child_org_num = 0;
            } else {
              treeNode.props.dataRef.children = result.data.map((item) => {
                treeProps.loadedKeys.push(item.org_id + "");
                return { ...item, isLeaf: item.child_org_num === 0 };
              });
            }
            const payload = {
              treeProps: Object.assign(treeProps, {
                loadedKeys: Array.from(
                  new Set(
                    treeProps.loadedKeys.concat([
                      treeNode.props.dataRef.org_id + "",
                    ])
                  )
                ),
                dataSource: treeProps.dataSource,
              }),
            };
            this.setState(payload);
            resolve();
          });
        },
      },
    };

    return (
      <div className="ta-org_tree">
        <LeftSider {...leftSiderProps}></LeftSider>
      </div>
    );
  }
}
export default connect(
  ({ organizeData, userInfo }) => ({
    organizeData,
    userInfo,
  }),
  null,
  null,
  {
    forwardRef: true,
    withRef: true,
  }
)(index);
