.panel-box {
  position: relative;
  .ant-drawer-body{
    padding: 28px;
  }
  .panel-header {
    display: flex;

    .abnormal-name {
      display: flex;
      align-items: center;
      min-width: 150px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;

      &::before {
        margin-right: 12px;
        content: "";
        display: inline-block;
        width: 3px;
        height: 14px;
        background: #f46e65;
        border-radius: 5px 5px 5px 5px;
        opacity: 1;
      }

      // &-examine{
      //   width: 160px;
      // }
    }

    .abnormal-count {
      margin-left: 166px;
      font-size: 16px;
      font-weight: 500;
      color: #333333;

      i {
        padding: 0 3px;
        color: #f46e65;
        font-size: 16px;
      }
    }
  }
  .collapse-title{
    display: flex;
    justify-content: center;
    margin-bottom: 28px;
    width: 100%;
    &-item {
      flex: 1;
    }
  }
  .ant-collapse-content-box {
    padding: 0;
    .abnormal-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 32px;
      color: #666666;
      background: #ffffff;
      border-top: 1px solid #d9d9d9;
      font-size: 14px;

      .abnormal-name {
        flex: 1;
        display: flex;
        align-items: center;
        .name {
          width: 350px;
          margin-right: 100px;
        }
      }
      .abnormal-item-btn {
        margin-left: 16px;
        width: 115px;
        height: 36px;
        font-size: 16px;
        color: #666666;
        &:hover {
          background-color: #f46e65;
          color: #fff;
          text-shadow: 0 0 1px #fff;
        }
      }

      &:nth-child(1) {
        border-top: 0px;
      }
    }
  }
  .spin-wrap {
    position: absolute;
    top: 30%;
    left: 48%;
  }
}

.collapse-modal {
  .content-box {
    width: 100%;

    & > span {
      margin-right: 5px;
    }
  }
}

.deal-modal {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .tip-text {
    margin-top: 60px;
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    line-height: 19px;
  }

  button {
    width: 160px;
    margin-top: 60px;
    margin-bottom: 30px;
  }
}
