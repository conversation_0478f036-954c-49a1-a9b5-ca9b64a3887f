import { activityHost, ucHost, workflowHost } from './config';
import { http } from 'client/tool/axios';

export const insertPrize = (postObject) => {
    return http.post(`${activityHost}/prize/insert`, postObject);
};

export const fetchArticleList = (page = 1, pageSize = 10) => {
    return http.get(`${activityHost}/prize/find-all`, { page, pagesize: pageSize });
};

export const deleteArticle = (id) => {
    return http.delete(`${activityHost}/prize/remove/${id}`);
};

export const fetchArticleById = (id) => {
    return http.get(`${activityHost}/prize/find-id/${id}`);
};

export const editArticle = (postObject) => {
    return http.post(`${activityHost}/prize/update`, postObject);
};

export const getDeliveryList = (payload) => {
    return http.get(`${activityHost}/prize/delivery-list`, payload);
}

export const sendDelivery = (payload) => {
    return http.post(`${activityHost}/prize/delivery`, payload);
}