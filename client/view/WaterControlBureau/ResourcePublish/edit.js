import React, { Component } from "react";
import { message, Form, Input, Select, TreeSelect, Button, notification } from "antd";
import { http } from "client/tool/axios";
import { cloud, fileHost } from "apis/config";
import { connect } from "dva";
import "./index.less";
import View from "./index-view";
import merge from "merge";
import { uploadBase64File } from "apis/file";
// import { addTypeListItem } from "apis/type-process";
import ApprovalProcess from "components/approval-process";
import { updateUserOperateState } from "tool/util";
import { filterParamsValue, fetch } from "client/tool/axios";
// import {
//   fetchWorkflowList,
//   fetchWorkflowItemInfo
// } from "apis/activity";

// import { insertApproval } from "apis/progress";
import config from "config/const-config";
import { fetchSourceColumns } from "apis/waterControlBureau";

class ResourcePublish extends Component {
  constructor() {
    super();
    this.state = {
      param: {
        resource_id: "",
        title: "",
        sub_title: "",
        show_type: 1,
        column_id: "",
        sub_column: [],
        source: "",
        keywords: "",
        summary: "",
        content: "",
        workflow_id: -999,
        workflow_name: '',
        must_approve: 0,
        video_title: '',
        video_name: '',
        video_url: ''
      },
      preUploadType: [],//编辑新闻的时候视频的回显示show-upload-file-type数组的对象
      cleanVideo: false,
      video_files: [],
      files_type2: [],
      files_type3: [],
      files_type4: [],
      newsColumn: [],
      status: 0, // 0为新建, 1为修改
      preview_list: false,
      preview_article: false,
      loading: false,
      preview_list_data: "",
      autoSave: false,
      // loadDataInit: false,

      action: 'new' // 新闻编辑发布时为clone  默认new 修改edit 查看view

    };

    this.approvalProcess = null;
    this.handleSubmit = this._handleSubmit.bind(this);
    this.cloumnFormat = this._cloumnFormat.bind(this);
    this.columnOnChange = this._columnOnChange.bind(this);
    this.editOnChange = this._editOnChange.bind(this);
    this.sub_columnOnChange = this._sub_columnOnChange.bind(this);
    this.clipEvent = this._clipEvent.bind(this);
    this.preview_list = this._preview_list.bind(this);
    this.preview_article = this._preview_article.bind(this);
    this.closePreview = this._closePreview.bind(this);
    this.setVideoFiles = this.setVideoFiles.bind(this);

    this.fetchWorkflowData = this.fetchWorkflowData.bind(this);
    this.t = null;

    this.oldFormJSON = '';
  }
  async componentDidMount() {
    const self = this;

    const { history } = this.props;
    const { id, action = 'new' } = history.location.query || {};
    const isPreview = action === 'view';

    this.setState({
      action,
      isPreview
    })

    if (id) {
      await this.findNews(id, isPreview);
    } else {
      await this.getColumn();

      // this.setState({
      //   loadDataInit:true
      // })
    }


    if (action === 'new' || action === 'draft') {
      this.initCheckFormDataChange();
      this.startAutoSaveDraft();
    }

  }

  initCheckFormDataChange() {
    clearInterval(this.cId);
    this.oldFormJSON = '';
    this.setState({
      autoSave: false
    })
    this.checkFormDataChange();
  }

  //表单数据检查改动 一旦改动后设置 autoSave为true 并停止检测
  checkFormDataChange() {
    const self = this;
    this.cId = setInterval(() => {
      // console.info('检测数据变化...')
      const data = self.props.form.getFieldsValue();
      let v = self.formToData(data, true);
      // console.log(self.oldFormJSON, v)
      if (self.oldFormJSON !== '') {
        if (self.oldFormJSON !== JSON.stringify(v)) {
          console.info('数据已更改，自动保存草稿功能开启');
          self.setState({
            autoSave: true
          })
          clearInterval(self.cId);
          self.oldFormJSON = JSON.stringify(v);
        }
      } else {
        self.oldFormJSON = JSON.stringify(v);
      }
    }, config.checkFormDateChangeDelayTime);
  }

  componentWillUnmount() {
    this.stopAutoSaveDraft();
    clearInterval(this.cId)
  }

  startAutoSaveDraft() {
    const self = this;
    this.stopAutoSaveDraft();
    this.tId = setInterval(() => {
      if (self.state.autoSave) {
        console.info('草稿开始自动保存...')
        const v = self.props.form.getFieldsValue(['title']);
        const { title } = v;
        if (title && title.length) {
          self.hanlderSaveDraft(null, true);
        } else {
          console.info('没标题 无法保存！')
        }
      }
    }, config.autoSaveDelayTime)//
  }
  stopAutoSaveDraft() {
    console.info('关闭自动保存')
    clearInterval(this.tId)
    this.tId = null;
  }
  onCleanVideo(bool) {
    const { param } = this.state
    this.setState({
      cleanVideo: bool,
      preUploadType: [],
      param: {
        ...param,
        video_name: "",
        video_url: ""
      },
      video_files: []
    })
  }
  setVideoFiles(files) {
    this.setState({
      video_files: files,
      param: {...this.state.param, video_url: files}
    })
  }
  hanlderSaveDraft(e, autoSave = false, btnSave = false) {
    const self = this;
    e && e.preventDefault();
    const { form } =  this.props
    form.validateFieldsAndScroll(['title'], (err, values) => {
      // if (!this.state.param.column_id) {
      //   message.error("请选择文章主栏目");
      //   return;
      // }
      if (err) {
        message.error('请填写资源标题')
        return
      }
      this.setState({
        loading: true
      });

      let v = form.getFieldsValue();
      const { video_files } = self.state
      if (video_files.length) {
        v.video_name = video_files[0].name;
      }
      self.createNews(v, true, autoSave, btnSave);//true代表草稿

      /* const v = this.props.form.getFieldsValue();
      if (values.title.length == 0) {
        this.setState({
          loading: false
        });
        return message.error("请输入文章长标题");
      }
      // console.log(values)
      // return false;

      self.createNews(v, true, autoSave, btnSave);//true代表草稿 */

    });
  }

  _handleSubmit(e) {
    const self = this;
    e.preventDefault();
    const { form } = this.props
    const { param, video_files } = this.state;
    form.validateFieldsAndScroll((err, values) => {
      if (err) {
        return
      }
      if (!param.column_id) {
        message.error("请选择文章主栏目");
        return;
      }
      // // 沙区视频上传 其余区域不需要
      // if (values.video_title && !values.video_url) {
      //   message.error('请上传视频文件!')
      //   // e.preventDefault();//阻止刷新
      //   return false;
      // }
      // if (video_files.length) {
      //   values.video_name = video_files[0].name;
      // }

      this.setState({
        loading: true
      });
      values.column_id = param.column_id
      this.createNews(values);

      /* if (!this.state.param.column_id) {
        this.setState({
          loading: false
        });
        message.error("请选择文章主栏目");
        return;
      }
      if (!err) {
        self.createNews(values);
      } else {
        this.setState({
          loading: false
        });
      } */
    });
  }
  _clipEvent(type, index) {
    const self = this;
    const upload = base64 => {
      return new Promise((r, j) => {
        uploadBase64File({
          upfile: base64,
          upType: "no-compress-image",
          is_water: 0,
          is_val: 0
        })
          .then(data => {
            const _data = data.data;
            if (_data.code != 0) {
              message.error(_data.message);
              j(_data.message);
              return;
            } else {
              const i = index ? index : 0;
              let images = self.state[`files_type${type}`] || [];
              // if (images[i] && images[i].url) {
              //   images.push(_data.data.path)
              // } else {
              //   images.push( { url: _data.data.path })
              // }
              images.push( { url: _data.data.path })
              self.setState({
                [`files_type${type}`]: images
              }, () => {
                console.log(this.state)
              });
              r("成功");
            }
          })
          .catch(e => j(e));
      });
    };
    return {
      upload
    };
  }
  _columnOnChange(e) {
    // const param = Object.assign({}, this.state.param);
    // param.column_id = e;
    console.log(e)
    this.setState({ param: { ...this.state.param, column_id: e } });
  }
  _sub_columnOnChange(e) {
    // const param = Object.assign({}, this.state.param);
    // param.sub_column = e;
    console.log(e)
    console.log({param: { ...this.state.param, sub_column: e }})
    this.setState({ param: { ...this.state.param, sub_column: e } });
  }

  async fetchColumn() {
    // console.log('获取组织数据');
    const response = await fetchSourceColumns();
    // console.log(response);
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      throw new Error(message);
    }
    for(let i = 0, length = data.length; i < length; i ++){
      const {data: res} = await fetchSourceColumns({ top_column_id: data[i].column_id });
      const { data: childrenData, code: childrenCode } = res;
      if (childrenCode !== 0) {
        throw new Error(message);
      }
      data[i].children = childrenData
      if(i === length - 1) {
        return data
      }
    }
  }

  async getColumn(isFindColumnById = false) {
    const self = this;
    let column = await this.fetchColumn()
    var list = self._cloumnFormat(column);

    //栏目列表找不到 此 column_id
    //通过ID全局查询栏目信息
    // const column_id = this.state.param.column_id;
    // if (isFindColumnById && column_id && !list.find(column => column.value == column_id)) {
    //   const res = await columnName(column_id);
    //   const name = fetch(res);
    //   list.push({
    //     label: name,
    //     value: column_id + "",
    //     key: column_id + "",
    //     children: null
    //   })
    // }

    if (this.state.action === 'clone') {
      //编辑发布（clone）用当前组织的新闻默认栏目
      const defaultColumnId = (list.find((column) => column.default) || {}).key;
      this.setState({ param: { ...this.state.param, column_id: defaultColumnId } });
    }

    // this.initSubColumn(list);

    self.setState({
      newsColumn: list
    });

  }
  _cloumnFormat(data) {
    const self = this;
    if (!data) {
      return null;
    }
    const node = data.map((v, i) => {
      if (v.children) {
        return {
          label: v.column_name,
          value: v.column_id + "",
          key: v.column_id + "",
          default: v.default,
          children: self._cloumnFormat(v.children),
          disabled: true
        };
      }
      return {
        value: v.column_id + "",
        label: v.column_name,
        key: v.column_id,
        default: v.default
      };
    });
    return node;
  }

  fetchWorkflowData(data) {
    // if(data.workflow_id && data.workflow_id !== -999){
    //   data.must_approve = 1;
    // }else{
    //   data.must_approve = 0;
    // }
    // console.log(this.state.param, data);
    this.setState({
      param: { ...this.state.param, ...data }
    })//废弃，直接 approvalProcess.exportData 返回值
  }

  formToData(param, draft) {
    param.column_id = param.column_id;
    if (this.state.param.sub_column) {
      var sub_column_id = "";
      param.sub_column_id = this.state.param.sub_column.forEach(v => {
        sub_column_id += v.value + ",";
      });
      param.sub_column_id = sub_column_id.slice(0, sub_column_id.length - 1);
    }
    // 1-文字 2-图片 3-图文 4-视频
    if ([1, 3].includes(parseInt(param.show_type))) {
      param.content = this._view ? this._view.getContent() : ''
      if (this._view && !this._view.getContent() && !draft) {
        message.error("请编辑内容");
        return;
      }
    }
    if ([2, 3].includes(parseInt(param.show_type))) {
      param.img_url = this.state.files_type2;
      if (this.state.files_type2.length === 0 && !draft) {
        message.error("请上传资源图片");
        return;
      }
    }
    if (param.show_type === 4) {
      let videoUrlArr = []
      this.state.video_files.forEach(item => {
        videoUrlArr.push(JSON.stringify(item))
      });
      param.video_url = videoUrlArr
      if (param.video_url.length === 0 && !draft) {
        message.error("请上传视频");
        return;
      }
    }
    if (this.state.param.resource_id) {
      param.resource_id = this.state.param.resource_id;
    }
    param.title = param.title || '';
    // param.sub_title = encodeURIComponent(param.sub_title || '');
    param.mode = param.show_type
    param.status = draft ? 0 : 1

    // param.source = encodeURIComponent(param.source || '');
    // param.summary = encodeURIComponent(param.summary || '');
    // param.keywords = encodeURIComponent(param.keywords || '');
  
    // param.must_approve = this.state.param.must_approve;


    if (param.column_id == 0) {
      param.column_id = undefined;
    }
    return param;
  }

  async createNews(param, draft = false, autoSave = false, btnSave = false) {

    const self = this;
    const { history } = this.props;
    let approvalData = self.state.param;
    if (this.approvalProcess) {
      approvalData = await this.approvalProcess.exportData(draft);//如果传标志位draft，草稿不提示方法内的message.error
    }

    //approvalProcess.exportData 是否返回undefined 如果
    //非草稿需要验证数据，所以要判断approvalData是否为undefined，是的话没通过验证，所以提示错误return 不执行后面处理
    if (!approvalData && !draft) {
      this.setState({
        loading: false
      });
      return;
    }

    //容错处理
    approvalData = approvalData || {};//如果是草稿关闭验证提示而已

    // const { workflow_id = -999, workflow_name = '', must_approve = 0 } = approvalData;


    // if (must_approve && workflow_name) {
    //   param.workflow_id = workflow_id;
    //   param.workflow_name = workflow_name;
    // } else {
    //   param.workflow_id = -999;
    //   param.workflow_name = '';
    // }
    if (param.show_type === undefined) {
      param.show_type = 1;
    }

    // Object.assign(param, {
    //   organization_id: window.sessionStorage.getItem("_oid"),
    //   organization_name: window.sessionStorage.getItem("_on")
    // });
    // param.reprint = param.reprint ? 1 : 0;
    param = this.formToData(param, draft);

    if (!param) {
      this.setState({
        loading: false
      });
    }

    let fetch = this.state.param.resource_id ? "update" : "add";
    if (param.img_url) {
      let imgUrlArr = []
      param.img_url.forEach(item => {
        imgUrlArr.push(item.url)
      })
      param.img_url = imgUrlArr
    }
    if (draft) {
      fetch = "draft";
    }
    console.log("🐇", param)
    const url = {
      add: "/resourceAddOrSave",
      update: "/resourceEdit",
      draft: "/resourceAddOrSave"
    }
    http
      .post(`${cloud}${url[fetch]}`, filterParamsValue(param))
      .then(data => {
        var _data = data.data;
        if (_data.code !== 0) {
          message.error(_data.message);
          return;
        }

        let msg = '操作成功'
        if (draft) {
          msg = autoSave ? '已为你自动保存' : '草稿保存成功'
        } else {
          msg = fetch === 'update' ? '更新成功' : '发布成功';
        }
        message.success(msg);
        self.initCheckFormDataChange();

        updateUserOperateState(self.props);

        if (draft) {
          //将ID存下，下次提交带回去
          if (_data.data && _data.data.resource_id) {
            self.setState({
              param: { ...self.state.param, resource_id: _data.data.resource_id }
            })
          }
          return;
        } else {
          // console.log(updateUserOperateState);
          // console.log(this.props);
          // if (btnSave) {
          //   return;
          // }
          history.push("/WaterControlBureau/ResourceMgt", {
            tabsKey: 1//draft ? 2 : 1
          });
        }

        // 发布成功后需要引导history.back(), 跳转到新闻列表页面
      }).catch(e => {
        message.error("发布资源失败");
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }
  findNews(id, isPreview) {
    const self = this;
    return http
      .get(`${cloud}/resourceDetail?resource_id=${id}`)
      .then(data => {
        // console.log(data);
        const _data = data.data;
        console.log(_data)

        if (_data.code !== 0) {
          message.error(_data.message);
        }

        if (self.state.action === 'clone') {
          _data.data.resource_id = undefined;
          _data.data.column_id = undefined;
          _data.data.sub_column = [];
          _data.data.status = undefined;
        }

        const show_type = _data.data.mode;
        _data.data.show_type = show_type;
        _data.data.content = decodeURIComponent(_data.data.content);
        this.editor.setEditorContent(_data.data.content)
        _data.data.title = decodeURIComponent(_data.data.title);
        _data.data.sub_column = _data.data.sub_column_id ? _data.data.sub_column_id.split(',').map(item => item = { value: item }) : []
        this.setState({
          files_type2: (_data.data.img_url && _data.data.img_url.length && _data.data.img_url.length > 0) ? _data.data.img_url.map(item => {return { url: item }}) : []
        })
        if(_data.data.video_url && _data.data.video_url.length > 0) {
          for(let i = 0, length = _data.data.video_url.length; i < length; i++) {
            _data.data.video_url[i] = JSON.parse(_data.data.video_url[i] || "{}")
          }
        }

        if (_data.data.workflow_id && _data.data.workflow_id !== -999) {
          _data.data.must_approve = 1;
        }

        if (_data.data.column_id === -999) {
          _data.data.column_id = '';
        }

        //workflow_id
        //workflow_name
        // let _video_name = _data.data.video_name ? _data.data.video_name : '',
        //   _video_url = _data.data.video_url ? _data.data.video_url : '',
        //   arr = [];
        // // todo:在编辑判断视频新闻
        // // console.log(_video_name)
        // if (_video_name && _video_name!='' && _video_name!=undefined && _video_name!='undefined') {
        //   let obj={
        //     file_name: _video_name,
        //     id: _data.data.resource_id,
        //     name: _video_name,
        //     path: _video_url,
        //     size: ''
        //   }
        //   arr.push(obj)
        // }

        console.log("🐇", _data.data)
        
      
        self.setState({
          param: { ...self.state.param, ..._data.data, video_url: _data.data.video_url },
          // preUploadType: arr,
          // [`files_type${show_type}`]: _data.data.imgs,
          video_files: _data.data.video_url
          // loadDataInit:true
        }, (state) => {
          self.getColumn(isPreview);
        });
      })
      .catch(e => {
        console.log(e);
        message.error("查找新闻内容失败");
      });



  }
  //初始化二级分类
  // initSubColumn(column = []) {
  //   const self = this;
  //   const { newsColumn, param } = self.state;
  //   const { sub_column } = param;

  //   const columnData = column.length ? column : newsColumn;
  //   let new_sub_column = [];

  //   if (sub_column && columnData && columnData.length) {

  //     (Array.isArray(sub_column) ? sub_column : (sub_column || '').split(","))

  //       .forEach(v => {
  //         loop_sub_column(columnData, v, new_sub_column);
  //       });
  //     this.setState({
  //       param: { ...self.state.param, sub_column: new_sub_column }
  //     })
  //   }
  //   function loop_sub_column(list, id, sub_column) {
  //     list.forEach(v => {
  //       if (v.value === id) {
  //         sub_column.push({
  //           value: v.value,
  //           label: v.label
  //         });
  //       }
  //       if (v.children) {
  //         loop_sub_column(v.children, id, sub_column);
  //       }
  //     });
  //   }
  // }

  _editOnChange(e) {
    // const param = this.state.param;
    // param.content = e;
    // this.props.form.setFieldsValue({ content: e });
    this.setState({
      param: { ...this.state.param, content: e }
    });
  }
  _preview_list() {
    const { getFieldValue } = this.props.form;
    var data = "";
    var show_type = getFieldValue("show_type");
    var imgs = this.state[`files_type${show_type}`]
      ? this.state[`files_type${show_type}`]
      : [];
    imgs.forEach(i => {
      i.type = show_type;
    });
    var title = getFieldValue("video_title")
    data += "title=" + getFieldValue("title");
    data += "&sub_title=" + getFieldValue("sub_title");
    data += "&source=" + getFieldValue("source");
    data += "&showtype=" + show_type;
    data += "&imgs=" + JSON.stringify(imgs);
    data += "&video_title=" + title;
    data += "&video_url=" + getFieldValue("video_url");
    this.setState({
      preview_list: true,
      preview_list_data: encodeURIComponent(data)
    });
  }
  _preview_article() {
    const { getFieldValue } = this.props.form;
    let data = "";
    let content = this._view.getContent();
    content = content.substring(0, 900)
    content = content.substring(0, content.lastIndexOf('<p'))
    data += "title=" + getFieldValue("title");
    data += "&sub_title=" + getFieldValue("sub_title");
    data += "&source=" + getFieldValue("source");
    data += "&content=" + content;
    const videoTitle = getFieldValue("video_title");
    if (!!videoTitle) {
      data += "&video_title=" + videoTitle;
      data += "&video_url=" + getFieldValue("video_url");
    }
    //截取小于get最大参数个数的数据
    const _data = data.slice(0, 980)
    //截取p标签最后一次出现位置前的所有数据 保证数据准确
    data = _data.slice(0, _data.lastIndexOf('<p'))
    this.setState({
      preview_article: true,
      preview_article_data: encodeURIComponent(data)
    });
  }
  _closePreview() {
    this.setState({
      preview_article: false,
      preview_list: false
    });
  }
  render() {
    const { history, userInfo: {element} } = this.props;
    const { id } = history.location.query || {};
    const formItemLayout = {
      labelCol: {
        span: 3,
        xs: 3,
        sm: 3
      },
      wrapperCol: {
        span: 21,
        xs: 21,
        sm: 21
      }
    };
    const container = () => document.getElementById("create-news");
    const approvalProcessProps = {
      // preview: (isPreviewDetail || isOnlyModifyPerson),
      ref: (ref) => {
        this.approvalProcess = ref;
      },
      isLabel: false,
      workflow_id: this.state.param.workflow_id,
      workflow_name: this.state.param.workflow_name,
      must_approve: this.state.param.must_approve,
      fetchWorkflowData: this.fetchWorkflowData.bind(this)
    }

    const props = {
      that: this,
      ApprovalProcess,
      approvalProcessProps,
      autoSave: this.state.autoSave,
      cleanVideo: this.state.cleanVideo,
      preUploadType: this.state.preUploadType,
      // approvalProcessHandler: this.approvalProcessHandler.bind(this),
      container: container,
      form: this.props.form,
      history: history,
      newsColumn: this.state.newsColumn,
      formItemLayout: formItemLayout,
      param: this.state.param,
      column_id: this.state.param.column_id,
      sub_column: this.state.param.sub_column,
      state: Object.assign({}, this.state),
      setState: this.setState.bind(this),
      _edit: this._edit,
      isEdit: !!id,
      action: this.state.action,
      isPreview: this.state.isPreview,
      files_type2: this.state.files_type2,
      files_type3: this.state.files_type3,
      files_type4: this.state.files_type4,
      preview_list: this.state.preview_list,
      preview_list_data: this.state.preview_list_data,
      preview_article: this.state.preview_article,
      preview_article_data: this.state.preview_article_data,
      hanlderSaveDraft: this.hanlderSaveDraft.bind(this),
      onCleanVideo: this.onCleanVideo.bind(this),
      setVideoFiles: this.setVideoFiles,
      events: {
        handleSubmit: this.handleSubmit,
        columnOnChange: this.columnOnChange,
        editOnChange: this.editOnChange,
        sub_columnOnChange: this.sub_columnOnChange,
        clipEvent: this.clipEvent,
        show_preview_list: this.preview_list,
        show_preview_article: this.preview_article,
        closePreview: this.closePreview
      },
      element
    };
    return <View {...props} ref={e => (this._view = e)} />;
  }
}
const mapStateToProps = ({ userInfo }) => ({ userInfo });
export default connect(mapStateToProps)(Form.create()(ResourcePublish));
