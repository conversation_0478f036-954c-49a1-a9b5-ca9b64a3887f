import { party } from "./config";
import { http } from "tool/axios";
// let pUrl = "http://192.168.2.240:10086";//开发环境
// let tUrl = "http://192.168.2.245:8141";//测试环境
// 1030002401-模糊查询
export const getPartyHistoryFuzzyQuery = (queryparams = {}) => {
  return http.get(`${party}/partyHistory/fuzzyQuery`, queryparams);
};

// 1030002402-查询未设置
export const getPartyHistoryQueryNotSet = (queryparams = {}) => {
  return http.get(`${party}/partyHistory/queryNotSet`, queryparams);
};

// 1030002306-机关-查询人员当前基数和缴费规则
export const getPartyMemberViewSeparately = (queryparams = {}) => {
  return http.get(`${party}/partyMember/viewSeparately`, queryparams);
};

// 1030002310-根据缴费基数查询比例
export const getPartyMemberFindProportion = (queryparams = {}) => {
  return http.get(`${party}/partyMember/findProportion`, queryparams);
};

// 1030002308-查询原因描述
export const getSpecialCaseFindSingle = (queryparams = {}) => {
  return http.get(`${party}/specialCase/findSingle`, queryparams);
};

// 1030002403-修改历史
export const postPartyHistoryUpdateHistory = (queryparams = {}) => {
  return http.post(`${party}/partyHistory/updateHistory`, queryparams);
};

// 1030002404-查询可删除的月份
export const getPartyHistoryQueryDelete = (queryparams = {}) => {
  return http.get(`${party}/partyHistory/queryDelete`, queryparams);
};

// 1030002405-删除欠交记录
export const getPartyHistoryDeleteUnderpayment = (queryparams = {}) => {
  return http.get(`${party}/partyHistory/deleteUnderpayment`, queryparams);
};

// ----------------------------------------党费返还-----------------------------------------------------
// 1040110101-党组织返还比例查询

const getOrgReturnRatioReturn = (queryparams = {}) => {
  return http.get(`${party}/return/orgReturnRatio`, queryparams);
};

// 1040110102-党组织返还信息查询
const getOrgInfoReturn = (queryparams = {}) => {
  return http.get(`${party}/return/orgInfo`, queryparams);
};
// 1040110103-党组织返还信息预览
const postOrgPreviewReturn = (queryparams = {}) => {
  return http.post(`${party}/return/orgPreview`, queryparams);
};
// todo:1040110104-党组织返还删除预览信息
const postOrgPreviewReturnDel = (queryparams = {}) => {
  return http.post(`${party}/return/orgPreview/del`, queryparams);
};
// todo:1040110105-添加返还组织
const postReturnRatioAdd = (queryparams = {}) => {
  return http.post(`${party}/returnRatio/insert`, queryparams);
};
//todo: 1040110106-修改返还比例
const getReturnRatioUpdate = (queryparams = {}, header) => {
  return http.get(`${party}/returnRatio/update`, queryparams, { header });
};

//todo: 1040110107-删除返还比例
const getReturnRatioDelete = (queryparams = {}) => {
  return http.get(`${party}/returnRatio/delete`, queryparams);
};
// ----------------------------------------党费对账报表-----------------------------------------------------
// 116112002-党费缴纳流水
export const postPartyFeePayingWater = (queryparams = {}) => {
  return http.post(`${party}/pay/partyFeePayingWater`, queryparams);
};
//todo: 116112001-党费对账报表
const getPartyFeeReconciliation = (queryparams = {}) => {
  return http.post(`${party}/pay/partyFeeReconciliation`, queryparams);
};
//党费标准同步情况 40102020902-上一次同步基数结果 http://wiki.aidangqun.com/project/3?p=1023
const partyMemberSyncInfoLastResult = (queryparams = {}) => {
  return http.get(
    `${party}/ppmd/syncInfo/partyMember/lastResult`,
    queryparams
  );
};

// 交纳流水同步情况  40102020903-同步交费记录错误结果 http://wiki.aidangqun.com/project/3?p=1033
const payLogSyncInfoLastResult = (queryparams = {}) => {
  return http.get(
    `${party}/ppmd/syncInfo/payLog/lastResult`,
    queryparams
  );
};
const payLogSyncInfoLastAgain = (queryparams = {}) => {
  return http.get(
    `${party}/ppmd/syncInfo/payLog/again`,
    queryparams
  );
};

export {
  getOrgReturnRatioReturn,
  getOrgInfoReturn,
  postOrgPreviewReturn,
  postOrgPreviewReturnDel,
  postReturnRatioAdd,
  getReturnRatioUpdate,
  getReturnRatioDelete,
  getPartyFeeReconciliation,
  partyMemberSyncInfoLastResult,
  payLogSyncInfoLastResult,
  payLogSyncInfoLastAgain,
};
