import React, { Component } from 'react';
import { Modal, Form, Divider, Input, Checkbox, Row, Col, Icon, Button, InputNumber, message } from 'antd';

class ModalView extends Component {
    render() {
        const {
            form,
            modalVisible,
            closeModalHandler,
            chooseCommodity,
            commodityList,
            removeCommodity,
            awardTo,
            awardFormData = {}
        } = this.props;
        const FormItem = Form.Item,
            formItemLayout = {
                labelCol: {
                    span: 4
                },
                wrapperCol: {
                    span: 20
                }
            },
            { getFieldDecorator, getFieldValue, validateFields, resetFields } = form;

        //提交表单处理
        const submitHandler = (e) => {
            // console.log(e);
            e.preventDefault();
            validateFields((error, values) => {
                if (error) {
                    return;
                }
                //如果奖励物品
                if (values.awardCommodity) {
                    if (!commodityList || commodityList.length === 0) {
                        message.error('请选择奖励物品');
                        return;
                    }
                    values.prize = commodityList;
                    //奖励物品
                    values.win_type = 2;
                }
                //如果不奖励积分
                if (!values.awardPoints) {
                    delete values.score;
                } else if (values.score === 0) {
                    message.error('请填写奖励积分');
                    return;
                } else {
                    //奖励积分
                    values.win_type = 1;
                }

                if (values.awardCommodity && values.awardPoints) {
                    //奖励积分和物品
                    values.win_type = 3;
                }
                // console.log('颁奖提交表单', values);
                //执行传入的方法awardTo，要提交一个对象给外层方法，用于颁奖操作
                let result = JSON.parse(JSON.stringify(values));
                //删除接口无用字段
                delete result.awardCommodity;
                delete result.awardPoints;
                awardTo(result);

                resetFields();
                closeModalHandler();
            })
        }

        //编辑物品数量的处理
        const changeCommodityNum = (e, commodity, index) => {
            // console.log('修改商品', commodity);
            // console.log('商品序列', commodityList, index);
            // console.log('商品数量', e);
            commodityList[index].num = e;
        }

        return (
            <Modal
                maskClosable={'true'}
                // destroyOnClose={'true'}
                wrapClassName={'modal-main'}
                style={{ padding: '33px' }}
                width={700}
                title={'颁发奖品'}
                onCancel={() => {
                    resetFields();
                    closeModalHandler();
                }}
                visible={modalVisible}
                footer={null}>
                <Form onSubmit={(e) => submitHandler(e)} hideRequiredMark={true}>
                    <FormItem label={'奖项名称'} {...formItemLayout}>
                        {
                            getFieldDecorator('reward_name', {
                                initialValue: awardFormData.reward_name,
                                rules: [
                                    {
                                        required: true, message: '请填写奖项名称'
                                    }, {
                                        max: 30, message: '最多只能输入30个字'
                                    }
                                ]
                            })(
                                <Input placeholder={'请输入'} />
                            )
                        }
                    </FormItem>
                    <Row type={'flex'} align={'middle'} className={'ant-form-item'} type="flex" align="middle" style={{ marginBottom: '0' }}>
                        <Col {...formItemLayout.labelCol} className="ant-form-item-label">
                            <label title="奖励方式">奖励方式</label>
                        </Col>
                        <Col {...formItemLayout.wrapperCol}>
                            <Row type={'flex'} align={'middle'}>
                                <Col span={5}>
                                    {
                                        getFieldDecorator('awardPoints', {
                                            initialValue: awardFormData.awardPoints,
                                            rules: [],
                                            valuePropName: 'checked'
                                        })(
                                            <Checkbox>积分奖励</Checkbox>
                                        )
                                    }
                                </Col>
                                <Col span={15}>
                                    {
                                        getFieldDecorator('score', {
                                            initialValue: awardFormData.score,
                                            rules: []
                                        })(
                                            <InputNumber
                                                min={0}
                                                max={99999}
                                                precision={0}
                                                formatter={value => `${value} 分`}
                                                disabled={!getFieldValue('awardPoints')}
                                                placeholder={'请输入'} />
                                        )
                                    }
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Row type={'flex'} align={'middle'}>
                        <Col offset={4} span={20}>
                            <Divider dashed={true} />
                        </Col>
                    </Row>
                    <Row type={'flex'} align={'middle'}>
                        <Col offset={4} span={20}>
                            <Row type={'flex'} align={'middle'} className={'modal-form-item'}>
                                <Col span={5}>
                                    {
                                        getFieldDecorator('awardCommodity', {
                                            initialValue: awardFormData.awardPoints,
                                            rules: [],
                                            valuePropName: 'checked'
                                        })(
                                            <Checkbox>奖励物品</Checkbox>
                                        )
                                    }
                                </Col>
                                <Col offset={0}>
                                    <a href="javascript:void (0)"
                                        disabled={!getFieldValue('awardCommodity')}
                                        onClick={() => chooseCommodity()}>选择物品</a>
                                </Col>
                            </Row>
                            <Row type={'flex'} align={'middle'} className={'activity-list'}>
                                <Col offset={5}>
                                    {
                                        (commodityList && commodityList.length !== 0) && commodityList.map((commodity, index) => {
                                            return (
                                                <div className={'activity-list-item'} key={commodity.prize_id}>
                                                    数量：
                                                    <InputNumber
                                                        min={1}
                                                        max={9999}
                                                        defaultValue={1}
                                                        precision={0}
                                                        onChange={(e) => changeCommodityNum(e, commodity, index)}
                                                        formatter={value => `${value}个`}
                                                        disabled={!getFieldValue('awardCommodity')}
                                                        placeholder={'请输入'} />
                                                    &nbsp;&nbsp; 已选中 【{commodity.prize_name}】&nbsp;&nbsp; <Icon type="close-circle" style={{ color: '#F46E65', cursor: 'pointer' }} onClick={() => removeCommodity(index)} />
                                                </div>
                                            )
                                        })
                                    }
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    {/* 2018-06-06 移除现金奖励 */}
                    {/* <Row type={'flex'} align={'middle'}>
                            <Col offset={4} span={19}>
                                <Divider dashed={true}/>
                            </Col>
                        </Row>
                        <Row type={'flex'} align={'middle'}>
                            <Col offset={4} span={19}>
                                <Row type={'flex'} align={'middle'} className={'modal-form-item'}>
                                    <Col span={5}>
                                        <Checkbox>
                                            奖励现金
                                        </Checkbox>
                                    </Col>
                                    <Col span={4}>
                                        奖金区间：
                                    </Col>
                                    <Col span={6}>
                                        <Input
                                            suffix={<span>元</span>}
                                            placeholder={'请输入'}/>
                                    </Col>
                                    <Col span={2} style={{textAlign: 'center'}}>
                                        至
                                    </Col>
                                    <Col span={6}>
                                        <Input
                                            suffix={<span>元</span>}
                                            placeholder={'请输入'}/>
                                    </Col>
                                </Row>
                                <Row type={'flex'} align={'middle'} className={'modal-form-item'}
                                     style={{marginBottom: '13px'}}>
                                    <Col offset={5} span={4}>
                                        奖金总额：
                                    </Col>
                                    <Col span={6}>
                                        <Input
                                            suffix={<span>元</span>}
                                            placeholder={'请输入'}/>
                                    </Col>
                                    <Col offset={2} span={6}>
                                        <span style={{color: '#FF7875'}}>余额：0.00</span>
                                    </Col>
                                </Row>
                            </Col>
                        </Row> */}
                    {/* <Row type={'flex'} align={'middle'} className={'modal-form-item'}>
                            <Col offset={4} span={19}>
                                <Row>
                                    <Col offset={5}>
                                        <span style={{color: '#999999'}}>奖励会通过普惠服务发放给活动参与人</span>
                                    </Col>
                                </Row>
                            </Col>
                        </Row> */}
                    <Row type={'flex'} align={'middle'} justify={'center'} style={{ marginTop: '40px' }}>
                        <Button type={'primary'} htmlType={'submit'} style={{ width: '115px', height: '36px', lineHeight: '36px' }}>
                            确认
                            </Button>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <Button type={'default'} style={{ width: '115px', height: '36px', lineHeight: '36px' }} onClick={() => {
                            resetFields();
                            closeModalHandler();
                        }}>
                            取消
                            </Button>
                    </Row>
                </Form>
            </Modal>
        )
    }
}

export default Form.create()(ModalView);