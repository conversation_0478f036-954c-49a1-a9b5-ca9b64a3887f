// 党费统计，选择活动类型
import React from "react";
import "./index.less";
import { Modal, message as Message, Button } from "antd";
import propTypes from "prop-types";
import Top from "./sub-components/top";
import Bottom from "./sub-components/bottom";
import { queryAllMeetingTypeList } from "apis/meeting-type-manage";
import { getConfigDetail } from "apis/party-work-report";
class Class extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      categoryList: [],
      typeList: [],
      showTypes: [],
      allTypes: [],
      loading: false,
      checkedList: [],
      checkedListCopy: []
    }
  }

  componentDidMount() {
    this.queryAllMeetingTypeList();
    // this.getConfigDetail();
    const { input } = this.props;
    if (input) {
      this.setState({
        checkedList: JSON.parse(JSON.stringify(input)),
        checkedListCopy: JSON.parse(JSON.stringify(input))
      })
    }
  }

  componentWillReceiveProps(props) {
    const { input } = props;
    if (input) {
      this.setState({
        checkedList: JSON.parse(JSON.stringify(input)),
        checkedListCopy: JSON.parse(JSON.stringify(input))
      })
    }
  }

  async queryAllMeetingTypeList(queryparams = {}) {
    this.setState({
      loading: true
    });
    const response = await queryAllMeetingTypeList();
    // console.log(response);
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      this.setState({
        loading: false
      });
      Message.error(message);
      return;
    }
    let allTypes = [];
    if (data && Array.isArray(data)) {
      data.forEach((item, index) => {
        allTypes = allTypes.concat(item.types);
      });
    }
    console.log(data, allTypes);
    // 初次加载时，显示的所有弹框
    this.setState({
      loading: false,
      categoryList: data,
      allTypes,
      showTypes: allTypes,
      typeList: allTypes
    });
  }

  // async getConfigDetail(queryparams = {}) {
  //   const response = await getConfigDetail();
  //   console.log(response);
  //   const { data: body } = response;
  //   const { code, message, data } = body;
  //   if (code !== 0) {
  //     Message.error(message);
  //     return;
  //   }
  // }

  filterShowTypes(values = {}) {
    const { allTypes } = this.state;
    if (allTypes && Array.isArray(allTypes) && allTypes.length !== 0) {
      const result = allTypes.filter((item, index) => {
        if (values.category) {
          if (values.type) {
            return item.category_id === values.category && item.type_id === values.type;
          }
          else {
            return item.category_id === values.category;
          }
        }
        else {
          if (values.type) {
            return item.type_id === values.type;
          }
        }
      });
      this.setState({
        showTypes: result
      });
    }
  }


  render() {
    const {
      showTypes,
      loading,
      typeList,
      categoryList,
      checkedList
    } = this.state;
    const {
      width,
      visible,
      updateState,
      onChange,
      onCancel,
    } = this.props;
    const modalProps = {
      title: "选择活动类型",
      visible,
      footer: null,
      maskClosable: false,
      width,
      bodyStyle: {},
      wrapClassName: "meet-type-selector-modal",
      onCancel: () => {
        // console.log("关闭模态框");
        onCancel && onCancel();
      }
    };
    const topProps = {
      typeList,
      categoryList,
      onChange: (values = {}) => {
        // console.log("onChange", values);
        const { allTypes } = this.state;
        const { category, type } = values;
        if (values.category !== -1 || values.type !== -1) {
          if (values.category === -1) {
            delete values.category;
          }
          if (values.type === -1) {
            delete values.type;
          }
          this.filterShowTypes(values);
        }
        else {
          this.setState({
            showTypes: allTypes
          })
        }

      },
      categoryChangeHandler: (value) => {
        // console.log("categoryChangeHandler", value);
        const { allTypes } = this.state;
        let result = [];
        if (categoryList && Array.isArray(categoryList) && value !== -1) {
          const target = categoryList.find((item) => {
            return item.category_id === value;
          });
          // console.log(target);
          result = target.types;
        }
        else {
          result = allTypes;
        }
        this.setState({
          typeList: result
        });
      }
    };
    const bottomProps = {
      dataSource: showTypes,
      loading,
      checkedList,
      checkHandler: (record, isCheck) => {
        // console.log("checkHandler", record, isCheck);
        const { checkedList } = this.state;
        if (checkedList && Array.isArray(checkedList)) {
          const index = checkedList.findIndex((item) => {
            return item.type_id === record.type_id;
          });
          if (isCheck && index === -1) {
            checkedList.push(record);
          }
          else if (!isCheck && index !== -1) {
            checkedList.splice(index, 1);
          }
          // console.log(checkedList);
          this.setState({
            checkedList
          });
        }
      }
    };
    return (
      <Modal {...modalProps}>
        <Top {...topProps} />
        <Bottom {...bottomProps} />
        <div className="buttons-container">
          <Button type="primary" onClick={() => {
            const { checkedList } = this.state;
            // console.log(checkedList);
            onChange && onChange(checkedList, () => {
              onCancel && onCancel();
            });
          }}>确定</Button>
          <Button onClick={() => {
            onCancel && onCancel();
          }}>取消</Button>
        </div>
      </Modal>
    )
  }
}

Class.propTypes = {
  width: propTypes.number,
  visible: propTypes.bool,
  updateState: propTypes.func,
  onChange: propTypes.func,
  onCancel: propTypes.func,
  input: propTypes.array
}

Class.defaultProps = {
  width: 800,
  visible: true,
  updateState: () => { },
  onChange: () => { },
  onCancel: () => { },
  input: []
}

export default Class;
