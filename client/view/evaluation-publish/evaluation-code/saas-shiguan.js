import QRCode from "qrcode.react";
import html2canvas from "html2canvas";
import SearchHeader from "client/components/search-header";
import { getSaasQrCodes } from "apis/evaluation-publish";
import JSZip from "jszip";

import { cadreMobile } from "client/apis/config";

const MobileUrl = `${cadreMobile}/shiguan/`;

export default function SassShiguan(props) {
  const { history, evaluationId, orgId, evaluationName } = props;

  const query = new URLSearchParams(location.search);
  const org_id = query.get("org_id");
  const evaluation_id = query.get("evaluation_id");

  const qrcodeRef = React.useRef();

  const [dss, setdss] = React.useState([]);

  React.useEffect(() => {
    getSaasQrCodes({ evaluation_id: evaluationId }).then((res) => {
      if (res.data.code === 0) {
        setdss(res.data.data);
      }
    });
  }, [evaluationId]);

  const onDownload = (title) => {
    const el = qrcodeRef.current;
    if (!el) return;
    html2canvas(el).then((canvas) => {
      const imageURL = canvas.toDataURL("image/png");
      const tempLink = document.createElement("a");
      tempLink.id = "down-a";
      tempLink.download = title;
      tempLink.href = imageURL;
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
    });
  };

  const [btnLoading, setBtnLoading] = React.useState(false);
  const multipleDownload = async () => {
    setBtnLoading(true);
    const zip = new JSZip();

    const imageUrls = dss.map(async (item, i) => {
      const el = qrcodeRef.current[i];
      if (!el) return;
      const canvas = await html2canvas(el);
      const imageURL = canvas.toDataURL("image/png");
      return imageURL;
    });

    for (let i = 0; i < imageUrls.length; i++) {
      const name = dss[i].qr_code_type_name || "image" + (i + 1);
      const url = await imageUrls[i];
      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error(`Failed to fetch image: ${url}`);
        const blob = await response.blob();
        zip.file(`${name}.png`, blob); // 根据需要修改文件名和格式
      } catch (error) {
        console.error(`Error downloading image ${url}:`, error);
      }
    }

    zip
      .generateAsync({ type: "blob" })
      .then((content) => {
        const a = document.createElement("a");
        const url = window.URL.createObjectURL(content);
        a.href = url;
        a.download = "测评码.zip";
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => console.error("Error generating zip:", err))
      .finally(() => {
        setBtnLoading(false);
      });
  };

  return (
    <div>
      <SearchHeader
        title="下载测评码"
        onBack={() => {
          history.goBack();
        }}
      />

      <Ant.Row type="flex" justify="end" style={{ margin: 16 }}>
        <Ant.Button
          type="primary"
          loading={btnLoading}
          onClick={multipleDownload}
        >
          批量下载测评码
        </Ant.Button>
      </Ant.Row>
      <Ant.Row gutter={24} style={{ margin: "0 16px" }}>
        {dss.map((item) => {
          return (
            <Ant.Col span={6} key={item.id} style={{ marginBottom: 24 }}>
              <div style={{ padding: 20, height: 436, background: "#F7F8F9" }}>
                <Ant.Row
                  type="flex"
                  justify="space-between"
                  style={{ marginBottom: 20 }}
                >
                  <div style={{ fontSize: 18, fontWeight: 800 }}>
                    {item.qr_code_type_name}测评码
                  </div>
                  <div
                    style={{
                      color: item.state === 1 ? "#01BF40" : "rgba(0,0,0,0.35)",
                    }}
                  >
                    <Ant.Icon
                      type="check-circle"
                      theme="filled"
                      style={{ marginRight: 4 }}
                    />
                    {item.state === 1 ? "已测评" : "未测评"}
                  </div>
                </Ant.Row>
                <div
                  style={{
                    height: 350,
                    border: "1px solid #F46E65",
                    borderRadius: 4,
                  }}
                >
                  <div
                    ref={qrcodeRef}
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      height: 294,
                    }}
                  >
                    <QRCode
                      value={
                        item.qr_code_type === "management_code"
                          ? `${MobileUrl}managecode?evaluation_id=${evaluationId}&org_id=${orgId}&eval_partyment_id=${19}&title=${evaluationName}`
                          : `${MobileUrl}?evaluation_id=${evaluation_id}&org_id=${org_id}&eval_partyment_id=${19}&qrcode_type=${
                              item.qr_code_type
                            }`
                      }
                      size={240}
                      fgColor="#000000"
                      style={{ margin: "auto" }}
                    />
                  </div>
                  <Ant.Row
                    type="flex"
                    justify="space-between"
                    align="middle"
                    style={{
                      padding: "0 16px",
                      height: 54,
                      background: "#F46E65",
                      color: "#fff",
                      fontSize: 18,
                    }}
                  >
                    <div>{item.qr_code_type_name}测评码</div>
                    <div
                      onClick={() => onDownload(item.qr_code_type_name)}
                      style={{ fontSize: 14, cursor: "pointer" }}
                    >
                      <Ant.Icon type="download" />
                      下载二维码
                    </div>
                  </Ant.Row>
                </div>
              </div>
            </Ant.Col>
          );
        })}
      </Ant.Row>
    </div>
  );
}
