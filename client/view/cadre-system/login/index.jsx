import React, { useState, useEffect, useRef } from "react";
import "./index.less";
import md5 from "js-md5";
import { createHeaders, getRegionId } from "client/tool/axios";
import { Form, Input, Modal } from "antd";
import { connect } from "dva";
import { uuid } from "client/tool/uuid";
import { ucHost } from "client/apis/config";
import { Base64 } from "client/tool/util";
import PasswordResetModal from "client/components/ResetPassword";
const { message } = Ant;
const FormItem = Form.Item;
function index(props) {
  const { userInfo } = props;

  const [_uuid, setUUID] = useState(uuid(32));
  const [submiting, setSubmiting] = useState(false);

  const [loginStatus, setLoginStatus] = useState(false);

  const [visible, setVisible] = useState(false);
  const [reset_user_id, setResetUserId] = useState('');
  const [headers, setHeaders] = useState({});
  const [data, setData] = useState({
    password: "",
    //当前登录表单是否正在提交
    userName: "",
  });

  useEffect(() => {
    props.dispatch({
      type: "userInfo/getInitConfig",
    });
  }, []);

  const refreshVerificationCode = () => {
    setUUID(uuid(32));
  };
  const loginOnce = useRef(true);

  const headerStr = () => encodeURIComponent(
    Base64.encode(JSON.stringify(headers()))
  );
  const resetPasswordModal = ({ pwd_check_message, header, user_id }) => {
    Modal.info({
      title: '提示',
      content: <div> <p>{pwd_check_message}</p></div>,
      okText: '更改密码',
      onOk() {
        setVisible(true);
        setResetUserId(user_id)
        setHeaders(header)
      },
    })
  }
  const handleSubmit = (e) => {
    e.preventDefault();
    // console.log(this.state.submiting);
    if (submiting) {
      return;
    }

    loginOnce.current = false;

    const { dispatch, form } = props;
    const { resetFields } = form;

    form.validateFields((err, values) => {
      if (!err) {
        // values.passWord = md5(values.passWord);
        if (values.userName.length > 0) {
          if (!submiting) {
            setSubmiting(true);
            values.passWord = md5(values.password);

            values.uuid = _uuid;

            dispatch({
              type: "userInfo/login",
              payload: {
                ...values,
                props,
                callback: (data) => {
                  const { userName, mu, pwd_check_code, pwd_check_message, login_form, more_org_form } = data
                  if (pwd_check_code !== 0 && pwd_check_message) {

                    let { org_list, token } = more_org_form || {}


                    let user = org_list && org_list[0] || login_form

                    if (user) {
                      const { name, oid, org_type, type, user_id, token: _token } = user

                      token = _token || token

                      const userInfo = {
                        _uid: user_id,
                        _un: encodeURIComponent(values.userName),
                        _tk: token,
                        _oid: oid,
                        _type: type,
                        _region_id: getRegionId(),
                        _org_type: type,
                        _org_name: encodeURIComponent(name),
                        org_nme: name,
                      }
                      sessionStorage.clear()

                      resetPasswordModal({
                        pwd_check_code,
                        pwd_check_message,
                        user_id,
                        header: createHeaders(userInfo),
                      })
                    }

                    throw ""
                  }

                  if (userName) {
                    // 单组织
                    message.success(
                      "登录成功，欢迎" + userName || "" + "!",
                      1,
                      () => {
                        setSubmiting(false);

                        setLoginStatus(true);
                        const url = window.location.origin
                        if (mu.length === 1 && mu[0].id === 'PMS07') {
                          window.open(
                            `${url}/inspection/#/data-screen/home?_h=${headerStr()}`);
                        }

                        props.history.replace("/");
                      }
                    );
                  }
                },
                errorHandler(msg) {
                  message.error(msg || "获取菜单为空，登录失败");
                  setSubmiting(false);
                },
              },
            })
              .then((response) => {
                if (!!response.data && response.data.type === 1) return;
                if (response) {
                  const { code } = response;
                  if (code === 177 || code === 176) {
                    resetFields(["captcha"]);
                    refreshVerificationCode();
                  }
                  setSubmiting(false);
                  if (code === 0) {
                    setLoginStatus(true);
                  }
                }
              })
              .catch((error) => {
                setSubmiting(false);
                console.error(error);
              });
          }
        }
      }
      console.log(err);
    });
  };

  const chooseOid = async (val, token) => {
    const { dispatch, history } = props;
    //如果判断登录的组织为家庭互助会，就跳外部链接并带参数
    dispatch({
      type: "userInfo/chooseOid",
      payload: {
        ...val,
        token,
        callback({ userName: loginUsername, mu }) {
          const url = window.location.origin

          message.success(
            "登录成功，欢迎" + (loginUsername || "") + "!",
            1,
            () => {
              if (mu.length === 1 && mu[0].id === 'PMS07') {
                window.open(
                  `${url}/inspection/#/data-screen/home?_h=${headerStr()}`);
              }
              history.push("/");
            }
          );
        },
        errorHandler(code) {
          if (code) {
            message.error("登录超时，请重新登录");
          } else {
            message.error("获取菜单为空，登录失败");
          }
          window.location.href = `/login?region_id=${__TOG__.regionId}`;
        },
      },
    });
  };
  // useEffect(() => {
  //   if (loginOnce.current) return;

  //   const { Ouser } = userInfo;
  //   if (Ouser) {
  //     const { org_list, token } = Ouser;

  //     const fengdu = org_list.find((item) => item.oid === 1);

  //     loginOnce.current = true;

  //     fengdu && token && chooseOid(fengdu, token);
  //   }
  // }, [userInfo]);

  const onSelectOrg = (org) => {
    const { Ouser } = userInfo;

    const { token } = Ouser;

    chooseOid(org, token);
  };
  const FormStyle = {
    marginBottom: "25px",
  };
  const { form } = props;
  const { getFieldDecorator } = form;
  return (
    <div className="new-login">
      <div className="inner-box">
        <div className="header"></div>
        {!loginStatus && (
          <div className="form-box">
            <Form onSubmit={handleSubmit} className="login-form-panel">
              <FormItem style={FormStyle} hasFeedback>
                {getFieldDecorator("userName", {
                  // rules: [
                  //   {
                  //     required: true,
                  //     message: "请输入您的账号",
                  //   },
                  //   {
                  //     pattern: phoneReg,
                  //     message: "电话格式错误",
                  //   },
                  // ],
                  validateTrigger: "onBlur",
                })(
                  <Input
                    onChange={(val) => {
                      setData({ ...data, userName: val });
                    }}
                    placeholder="账号"
                    autoComplete="off"
                    prefix={
                      <span
                        className={"input-icon input-icon-username"}
                        style={{
                          backgroundImage: `url(${require("./image/user.png")})`,
                        }}
                      />
                    }
                  />
                )}
                <span className={data.userName ? "inp-tip" : "hide"}>
                  账 号
                </span>
              </FormItem>
              <FormItem style={FormStyle}>
                {getFieldDecorator("password", {
                  rules: [{ required: true, message: "请输入您的密码" }],
                })(
                  <Input
                    onChange={(val) => setData({ ...data, password: val })}
                    type="password"
                    placeholder="密码"
                    autoComplete="off"
                    prefix={
                      <span
                        className={"input-icon input-icon-password"}
                        style={{
                          backgroundImage: `url(${require("./image/password.png")})`,
                        }}
                      />
                    }
                  />
                )}
                <span className={data.password ? "inp-tip" : "hide"}>
                  密 码
                </span>
              </FormItem>
              <FormItem
                style={{ ...FormStyle }}
                className={"verification-code-input"}
              >
                {getFieldDecorator("captcha", {
                  rules: [
                    { required: true, message: "请输入右侧图片中的文字" },
                  ],
                })(
                  <Input
                    autoComplete="off"
                    placeholder="请输入右侧图片中的文字"
                    className={"verification-code-input"}
                    prefix={
                      <span
                        className={"input-icon input-icon-code"}
                        style={{
                          backgroundImage: `url(${require("./image/code.png")})`,
                        }}
                      />
                    }
                  />
                )}
                <div className={"verification-code-wrapper"}>
                  {_uuid && (
                    <img
                      src={`${ucHost}/show-captcha.png?uuid=${_uuid}`}
                      alt=""
                      className={"verification-code"}
                      onClick={refreshVerificationCode}
                    />
                  )}
                </div>
              </FormItem>
              <footer className={"submit-wrapper"}>
                <input
                  type="submit"
                  className={`login-form-button image-button`}
                  value={submiting ? "登录中..." : "登录"}
                />
              </footer>
            </Form>
          </div>
        )}
        {loginStatus && userInfo.Ouser && (
          <div className="org-box">
            <div className="title">选择组织</div>
            <div className="select-org">
              {userInfo.Ouser.org_list.map((item) => {
                return (
                  <div
                    className="org-item"
                    onClick={() => {
                      onSelectOrg(item);
                    }}
                  >
                    <span className="org-icon"></span>
                    <div className="org-name">{item.name}</div>
                    <span className="to-icon"></span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
      <PasswordResetModal
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={() => {
          setVisible(false);

          window.location.reload()
        }}
        headers={headers}
      />
    </div>
  );
}

const mapStateToProps = ({ userInfo }) => ({ userInfo });

export default connect(mapStateToProps)(Form.create()(index));
