import { Button, Dropdown, Form, Menu, message, Tabs } from "antd";
import {
  addOrUpdateCadreInfo,
  addOrUpdateEducationInfo,
  addOrUpdateWorkInfo,
  apiExportCadreInfo,
  exportCadreInfoLrmx,
  getQueryCadreInfo,
} from "client/apis/cadre-portrait";
import {
  convertMomentToFormattedDate,
  getUrlQuery,
  replaceVaule,
} from "client/tool/util";
import Header from "components/search-header";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import BaseInfo from "./components/BaseInfo";
import EduInfo from "./components/EduInfo";
import Responsibility from "./components/Responsibility";
import ResumeInfo from "./components/ResumeInfo";
import SociaRelations from "./components/SocialRelations";
import "./index.less";

const { TabPane } = Tabs;

function index({ form, ...props }) {
  const formRef = useRef();

  const eduinfoRef = useRef();
  const resumeinfoRef = useRef();
  const responsibilityRef = useRef();
  const sociaRelationsRef = useRef();

  const [loading, setLoading] = useState(false);
  const [downloading, setDownLoading] = useState(false);

  const [tabKeys, setTabKeys] = useState("1");

  let { user_id, org_id, is_detail, sel_org_id } = getUrlQuery();

  org_id = sel_org_id || org_id;
  // 基础信息
  const [baseInfo, setBaseInfo] = useState({
    base_info: {
      current_job_time: [
        {
          current_job_time1: "",
        },
        {
          current_job_time2: "",
        },
        {
          current_job_time3: "",
        },
      ],
    },
  });
  // 任现职务时间
  const [currentJobTime, setCurrentJobTime] = useState([]);

  /**
   * @description:
   * @param {*} keys
   * @return {*}
   */
  const onTabChange = (keys) => {
    setTabKeys(keys);
  };

  const onSave = () => {
    const { getFieldsValue, validateFields } = form;
    validateFields(async (err, values) => {
      if (!err) {
        // 分管领域验证
        let err = await responsibilityRef.current.validate();

        let baseInfo = getFieldsValue();

        const {
          birthplace,
          native,
          current_job_time,
          current_rank_time,
          work_time,
          birthday,
          joining_time,
          current_job,
        } = baseInfo;

        if (native) {
          baseInfo.native = native.slice(0, 3).join("-");
        }
        if (birthplace) {
          baseInfo.birthplace = birthplace.slice(0, 3).join("-");
        }

        if (current_job) {
          baseInfo.current_job = baseInfo.current_job_text;
        }

        baseInfo.org_id = org_id;

        baseInfo = convertMomentToFormattedDate(baseInfo, "YYYY.MM");
        // 处理任现职务时间
        if (currentJobTime.length !== 0) {
          const keys = Object.keys(baseInfo);
          for (const key of keys) {
            currentJobTime.forEach((item) => {
              const itemKey = Object.keys(item)[0];
              if (key === itemKey) {
                item[key] = baseInfo[key];
              }
            });
          }
        }
        // 提取值并拼接成字符串
        const arr = currentJobTime.map((item) => {
          const val = Object.values(item)[0];
          if (val && val !== "") {
            return val;
          }
        });
        baseInfo.current_job_time = arr
          .filter((item) => item !== undefined)
          .join(",");
        delete baseInfo.current_job_time1;
        delete baseInfo.current_job_time2;
        delete baseInfo.current_job_time3;

        const baseData = replaceVaule(baseInfo);
        !baseData.job_id && delete baseData.job_id;
        //级联字典表只取最后一个结果给后端  这块明明看你那个需要级联就改成相对于的名字
        baseData.cadre_rank =
          baseData.cadre_rank[baseData.cadre_rank.length - 1];
        const res = await addOrUpdateCadreInfo(baseData);
        if (res.data.code === 0) {
          const _user_id = res.data.data.user_id;
          message.success("保存成功");
          // 修改地址栏参数

          const resume = resumeinfoRef.current.getData();
          const eduData = eduinfoRef.current.getData();

          const { edu, fullEdu } = eduData;

          let work_resume = [];

          resume.workResume.map((item) => {
            const {
              start_time,
              end_time,
              position,
              work_unit,
              job_level,
              job_category,
              unit_property,
              areas_responsibility,
              remarks,
              content,
              position_or_content,
            } = item;
            console.log(item);

            if (!position) {
              return;
            }
            console.log(start_time, "start_time");

            work_resume.push({
              ...item,
              ...(start_time && {
                start_time: moment(start_time).format("YYYY.MM"),
              }),
              ...(end_time && {
                end_time: moment(end_time).format("YYYY.MM"),
              }),
              // position: position,
              // work_unit: work_unit,
              // job_level: job_level,
              // job_category: job_category,
              // unit_property: unit_property,
              // areas_responsibility: areas_responsibility,
              // remarks: remarks,
              // content: content,
              user_id: _user_id,
            });
          });
          const work_resume_remark = [];
          resume.workResumeRemark.map((item) => {
            const { start_time, end_time,position_or_content, content } = item;
            if (!content) {
              return;
            }

            work_resume_remark.push({
              ...item,
              ...(start_time && {
                start_time: moment(start_time).format("YYYY.MM"),
              }),
              ...(end_time && {
                end_time: moment(end_time).format("YYYY.MM"),
              }),
              // start_time: start_time,
              // end_time: end_time,
              // content: content,
              org_id: org_id,
              user_id: _user_id,
            });
          });
          const education_info = [];

          edu.map((item) => {
            const {
              school,
              specialty,
              department,
              start_time,
              end_time,
              diploma,
              degree,
              remark,
            } = item;

            // if (!school) {
            //   return;
            // }
            //接口传参如果不传默认不替换，所以就算是null也要传过去
            education_info.push({
              ...item,
              ...(start_time && {
                start_time: moment(start_time).format("YYYY.MM"),
              }),
              ...(end_time && {
                end_time: moment(end_time).format("YYYY.MM"),
              }),
              // ...start_time && { start_time: moment(start_time).format("YYYY.MM") },
              // ...end_time && { end_time: moment(end_time).format("YYYY-MM-DD") },
              // start_time: start_time,
              // end_time: end_time,
              school: school,
              department: department,
              specialty: specialty,
              diploma: diploma,
              degree: degree,
              remark: remark,
              user_id: _user_id,
              type: 2,
              org_id: org_id,
            });
          });

          fullEdu.map((item) => {
            const {
              school,
              specialty,
              department,
              start_time,
              end_time,
              diploma,
              degree,
              remark,
            } = item;

            // if (!school) {
            //   return;
            // }
            //接口传参如果不传默认不替换，所以就算是null也要传过去
            education_info.push({
              ...item,
              ...(start_time && {
                start_time: moment(start_time).format("YYYY.MM"),
              }),
              ...(end_time && {
                end_time: moment(end_time).format("YYYY.MM"),
              }),
              // ...start_time && { start_time: moment(start_time).format("YYYY-MM") },
              // ...end_time && { end_time: moment(end_time).format("YYYY-MM") },
              // start_time: start_time,
              // end_time: end_time,
              school: school,
              department: department,
              specialty: specialty,
              diploma: diploma,
              degree: degree,
              remark: remark,
              org_id,
              user_id: _user_id,
              type: 1,
            });
          });

          const educationData = education_info.map((item) => {
            return (item = { ...replaceVaule(item) });
          });
          addOrUpdateEducationInfo({
            education_info: educationData,
          }).then((res) => {
            if (res.data.code === 0) {
            } else {
              message.error(res.data.message);
            }
          });

          const workData = work_resume.map((item) => {
            return (item = { ...replaceVaule(item) });
          });
          addOrUpdateWorkInfo({
            work_resume: workData,
            work_resume_remark,
          }).then((res) => {
            if (res.data.code === 0) {
            } else {
              message.error(res.data.message);
            }
          });
          responsibilityRef.current.onSubmit(res.data.data.user_id);

          sociaRelationsRef.current.onSubmit(res.data.data.user_id);

          if (!user_id) {
            const _query = new URLSearchParams(location.href.split("?")[1]);

            _query.append("user_id", res.data.data.user_id);

            props.history.replace(location.pathname + "?" + _query.toString());
          } else {
            setTimeout(() => {
              // window.location.reload()
              loadData(res.data.data.user_id);
            }, 200);
          }
        } else {
          message.error(res.data.message);
        }
      } else {
        if (tabKeys != "1") {
          for (const keys of Object.keys(err)) {
            message.error(err[keys].errors[0].message);

            break;
          }
        }
      }
    });
  };

  useEffect(() => {
    setCurrentJobTime(baseInfo.base_info.current_job_time);
    if (!user_id) return;

    loadData();
  }, []);

  const loadData = (id) => {
    getQueryCadreInfo({
      user_id: id || user_id,
      org_id: sel_org_id || org_id,
    }).then((res) => {
      if (res.data.code === 0) {
        try {
          const jobTime = res.data.data.base_info.current_job_time.split(",");
          res.data.data.base_info.current_job_time = [
            {
              current_job_time1: jobTime[0] ? moment(jobTime[0]) : "",
            },
            {
              current_job_time2: jobTime[1] ? moment(jobTime[1]) : "",
            },
            {
              current_job_time3: jobTime[2] ? moment(jobTime[2]) : "",
            },
          ];
          if (res.data.data.base_info.user_type === -1) {
            res.data.data.base_info.user_type = undefined;
          }

          setBaseInfo(res.data.data);
        } catch (err) {
          console.log(err);
        }
      } else {
        message.error(res.data.message);
      }
    });
  };

  const onCancel = () => {
    history.back();
  };

  const onExport = async () => {
    setDownLoading(true);
    const { data: res } = await exportCadreInfoLrmx({ user_id });

    setDownLoading(false);

    if (res.code === 0) {
    } else {
      message.error(res.message);
    }
  };

  const {
    base_info,
    education_info,
    resume_info,
    charge_range,
    relationship_graph,
  } = baseInfo;
  const doDownload = async () => {
    const file_name = base_info.name + "干部任免审批表" + ".doc";
    try {
      await apiExportCadreInfo({ user_id }, file_name);
      message.success("下载成功!");
    } catch (error) {
      message.error("下载失败!", error);
    }
  };
  const doResumeDownload = async () => {
    const file_name = base_info.name + ".lrmx";
    try {
      await exportCadreInfoLrmx({ user_id }, file_name);
      message.success("下载成功!");
    } catch (error) {
      message.error("下载失败!", error);
    }
  };

  const menu = (
    <Menu>
      <Menu.Item onClick={doDownload}>.doc 格式</Menu.Item>
      <Menu.Item onClick={doResumeDownload}>.lrmx 格式</Menu.Item>
    </Menu>
  );
  return (
    <div className="cadre-information-maintenance">
      <Header
        title="干部信息维护"
        onBack={() => {
          history.back();
        }}
        renderRight={() => (
          <div>
            {is_detail === "1" && (
              <Dropdown overlay={menu} placement="bottomLeft">
                <Button style={{ marginRight: "2rem" }} type="primary">
                  导出
                </Button>
              </Dropdown>
            )}
          </div>
        )}
      />
      <Tabs defaultActiveKey="1" onChange={onTabChange}>
        <TabPane tab="基础信息" key="1"></TabPane>
        <TabPane tab="教育信息" key="2"></TabPane>
        <TabPane tab="简历信息" key="3"></TabPane>
        <TabPane tab="分管领域" key="4"></TabPane>
        <TabPane tab="社会关系" key="5"></TabPane>
      </Tabs>
      <div className="cim-content-box">
        <Form ref={formRef}>
          <div style={{ display: tabKeys === "1" ? "block" : "none" }}>
            <BaseInfo
              {...props}
              form={form}
              data={base_info}
              org_id={org_id}
              disabled={is_detail}
            />
          </div>
          <div style={{ display: tabKeys === "2" ? "block" : "none" }}>
            <EduInfo
              {...props}
              data={education_info}
              ref={eduinfoRef}
              disabled={is_detail}
            />
          </div>
          <div style={{ display: tabKeys === "3" ? "block" : "none" }}>
            <ResumeInfo
              {...props}
              data={resume_info}
              ref={resumeinfoRef}
              disabled={is_detail}
            />
          </div>
          <div style={{ display: tabKeys === "4" ? "block" : "none" }}>
            <Responsibility
              {...props}
              data={charge_range}
              wrappedComponentRef={responsibilityRef}
              disabled={is_detail}
            />
          </div>
          <div style={{ display: tabKeys === "5" ? "block" : "none" }}>
            <SociaRelations
              {...props}
              data={relationship_graph}
              wrappedComponentRef={sociaRelationsRef}
              disabled={is_detail}
            />
          </div>
        </Form>
      </div>
      <div className="save-button-box">
        {!is_detail && (
          <Button type="primary" onClick={onSave}>
            保存
          </Button>
        )}
        <Button style={{ marginLeft: "10px" }} onClick={onCancel}>
          取消
        </Button>
      </div>
    </div>
  );
}
export default Form.create()(index);
