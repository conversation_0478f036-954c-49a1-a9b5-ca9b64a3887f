/**
 * 
 * 
 * 考核计划管理
 * 
 */

const { http } = require('client/tool/axios');
import { mtmHost } from './config';


// 新增考核计划
export const addAssessPlan = data => {
  return http.post(`${mtmHost}/plan/add`, data);
}

// 查询考核计划列表
export const queryAssessPlanList = data => {
  return http.get(`${mtmHost}/plan/list`, data);
}

// 停启用考核计划
export const switchAssessPlan = data => {
  return http.post(`${mtmHost}/plan/execute`, data);
}

// 删除考核计划
export const deleteAssessPlan = data => {
  return http.delete(`${mtmHost}/plan/del/${data}`);
}

// 查询考核计划详情
export const queryAssessPlanDetail = data => {
  return http.get(`${mtmHost}/plan/detail/${data}`);
}