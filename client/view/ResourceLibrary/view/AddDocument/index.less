.add-case {
  &-form {
    padding: 34px 0 25px;

    .image {
      .image-view {
        .upload-done-action-shadow {
          transition: 0.2s;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          left: 0;
          top: 0;
          background-color: rgba(0, 0, 0, 0.3);

          .ant-upload {
            width: 30px;
          }

          &>span {
            cursor: pointer;
            margin: 0 17px;
          }

          .anticon {
            font-size: 30px;
            color: #fff;
          }
        }
      }

      .view {
        cursor: pointer;
      }

      .text {
        cursor: pointer;
        padding-top: 15px;
        width: 82px;
        height: 82px;
        background: #EEEEEE;
        text-align: center;
        color: #999999;
        font-size: 12px;

        .anticon {
          margin-bottom: 4px;
          font-size: 25px;
        }

        &>div {
          height: 12px;
          line-height: 12px;
        }
      }
    }

    .annotation {
      font-size: 12px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #BFBFBF;
      line-height: 24px;
    }

    .case-upload-btn {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      margin-bottom: 6px;
      width: 82px;
      height: 82px;
      background: #EEEEEE;
      font-size: 12px;
      line-height: 1.5;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #999999;

      .anticon {
        margin-bottom: 4px;
        font-size: 25px;
      }
    }

    .clipping {
      position: fixed;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      z-index: 99999;

      iframe {
        width: 100%;
        height: 100%;
      }
    }

    .select-org {
      .ant-form-item-control-wrapper {
        width: 46.83333333%;

        .ant-form-item-children {
          display: flex;
        }
      }
    }

    .ant-form-item {
      margin-bottom: 27px;
    }

    .sort-line {
      margin-bottom: 10px;
    }

    .seq-line {
      margin: 10px 0 10px;
    }

    textarea.ant-input {
      resize: none;
    }

    .ant-radio-wrapper:hover .ant-radio,
    .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus+.ant-radio-inner {
      border-color: #F46E65;
    }

    .ant-radio-checked {
      .ant-radio-inner {
        border-color: #F46E65;
      }
    }

    .ant-radio-inner {
      &::after {
        background-color: #F46E65;
      }
    }

    .ant-checkbox-checked>.ant-checkbox-inner {
      background-color: #F46E65;
      border-color: #F46E65;
    }

    .btn {
      .ant-btn {
        width: 90px;
        height: 36px;
        border-radius: 4px;
        font-size: 16px;
      }

      .ant-btn-primary {
        background: #F46E65;
      }

      .ant-btn-default {
        color: #999999;
        margin-left: 24px;
      }
    }
  }
}

.case-preview-modal {
  .ant-modal-body {
    padding: 24px 140px 48px;
  }

  .preview-modal-tab {
    margin-bottom: 36px;
    text-align: center;

    &>:first-child {
      margin-right: 60px;
    }

    &-item {
      display: inline-block;
      position: relative;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #999999;
      cursor: pointer;
    }

    .active {
      font-weight: bold;
      color: #FF4D4F;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -6px;
        width: 100%;
        height: 3px;
        background: #FF4D4F;
        border-radius: 2px;
      }
    }
  }

  .preview-modal-content {
    &-pc {
      &-title {
        margin-bottom: 16px;
        text-align: center;
        font-size: 24px;
        font-family: PingFang SC-Heavy, PingFang SC;
        font-weight: 800;
        color: #333333;
      }

      &-info {
        padding-bottom: 30px;
        border-bottom: 1px solid #EEEEEE;
        text-align: center;
        font-size: 16px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;

        &-v1,
        &-v2 {
          margin-right: 24px;
        }
      }

      &-abs {
        margin-top: 24px;
        padding: 24px 29px;
        background: #F1F1F1;
        border-radius: 10px;
        font-size: 18px;
        font-family: PingFang SC-Bold, PingFang SC;
        color: #666666;

        &>span:first-child {
          font-weight: bold;
        }
      }

      &-carousel {
        margin: 24px auto;
        width: 583px;
        height: 388px;

        .ant-carousel {
          .slick-dots {
            li:not(:first-child) {
              margin-left: 21px;
            }

            li>button {
              width: 9px;
              height: 9px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.6);
            }

            li.slick-active>button {
              background: #ffffff;
            }
          }
        }

        &-item {
          width: 100%;
          height: 388px;
          object-fit: cover;
        }
      }

      &-wrap {
        margin-bottom: 40px;
        font-size: 18px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }

      &-src {
        font-size: 18px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }

      &-file {
        margin-top: 32px;
        display: flex;

        .label {
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #262626;
          line-height: 24px;
        }

      }
    }

    &-mobile {
      position: relative;
      width: 100%;
      height: 700px;
      background: url(../../image/rectangle.png) no-repeat center/contain;

      .preview-modal-content-phone {
        position: absolute;
        width: 100%;
        height: 600px;
        top: 50%;
        transform: translateY(-50%);
        padding: 0px 465px;
        font-family: 'PingFang SC';
        font-style: normal;
        -ms-overflow-style: none;
        overflow-x: hidden;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        &-info {
          padding-bottom: 12px;
          border-bottom: 1px solid #EEEEEE;
          margin-bottom: 16px;

          &-v1 {
            margin-bottom: 12px;
            font-weight: 900;
            font-size: 16px;
          }

          &-v2 {
            color: #666666;
            font-weight: 400;
            font-size: 14px;
          }

          &-v3 {
            margin-top: 12px;

            span {
              padding: 2px 4px;
              margin-right: 16px;
              background: #EEEEEE;
              border: 1px solid #EEEEEE;
              color: #999999;
              border-radius: 3px;
              font-weight: 400;
              font-size: 14px;
            }
          }
        }

        &-abs {
          margin-bottom: 18px;
          padding: 10px 8px;
          background: #F8F8F8;
          border-radius: 5px;
          font-size: 15px;
          color: #666666;

          &>span:first-child {
            font-weight: bold;
          }
        }

        &-img {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          margin: 15px 0;

          &-item {
            width: 97px;
            height: 97px;
            object-fit: cover;
            border-radius: 9px;
          }
        }

        &-video {
          padding-top: 15px;
          border-top: 1px solid #EEEEEE;

          &>span {
            display: block;
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
          }

          .video-item {
            &:not(:last-child) {
              border-bottom: 1px solid #EEEEEE;
            }

            &-container {
              margin-top: 16px;
              width: 320px;
              height: 174px;
              border-radius: 9px;
              object-fit: contain;
            }

            &-title {
              padding: 8px 0 12px;

              font-size: 14px;
              font-family: PingFang SC-Bold, PingFang SC;
              font-weight: bold;
              color: #666666;
            }
          }


        }

        &-doc {
          border-top: 1px solid #eeeeee;

          &>span {
            display: block;
            margin: 16px 0 13px;
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
          }

          &-item {
            &>img {
              width: 15px;
              height: 15px;
              margin-right: 8px;
            }

            &>span {
              word-break: break-all;
              font-size: 12px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #333333;
            }

            .ant-btn {
              font-size: 12px;
              color: #2C8AFB;
            }
          }
        }

        &-wrap,
        &-src,
        &-date {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }

        &-src {
          margin: 18px 0 14px;
        }

        &-wrap {
          img {
            width: 100% !important;
            height: auto !important;
          }
        }
      }
    }

  }
}