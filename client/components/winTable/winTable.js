import PropTypes from "prop-types";

export const Table = (props) => {
  const { children } = props;
  return (
    <div className="w-table">
      { children }
    </div>
  )
};

export const Thead = (props) => {
  const { children } = props;
  return (
    <div className="w-thead">
      { children }
    </div>
  )
};

export const TBody = (props) => {
  const { children } = props;
  return (
    <div className="w-tbody">
      { children }
    </div>
  )
};

export const Tr = (props) => {
  const {
    children
  } = props;
  return (
    <div className="w-tr">
      { children }
    </div>
  )
};

export const Td = (props) => {
  const {
    children,
    align,
    width
  } = props;
  return (
    <div className="w-td" style={{ textAlign: align || "center", flexBasis: `${width}%` || "auto" }}>
      { children }
    </div>
  )
};

Td.propTypes = {
  align: PropTypes.string,
  width: PropTypes.number
}
