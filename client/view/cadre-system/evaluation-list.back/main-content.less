.org-modal {
  min-width: 960px;

  .org-modal-content {
    background: #ffffff;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;

    .content-date {
      width: 263px;
    }

    .input-box {
      display: flex;
      align-items: center;
      gap: 100px;
      padding-bottom: 5px;
      div {
        display: flex;
        align-items: center;
      }

      .label {
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
        position: relative;
        padding-left: 10px;
      }

      .label::before {
        content: "*";
        display: inline-block;
        width: 5px;
        height: 5px;
        position: absolute;
        left: 0px;
        top: 0px;
        color: red;
      }

      .label-name {
        input {
          padding: 4px 10px;
          width: 400px;
          height: 32px;
          background: #ffffff;
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          border: 1px solid #d9d9d9;
          outline: none;
          font-size: 14px;
          font-weight: 400;
          // color: #999999;
        }
      }
    }

    .table-box {
      height: 750px;
      overflow-y: auto;
      .org_name {
        margin: 20px 0 10px;
        font-size: 18px;
        font-weight: bold;
      }
      .ant-empty{
        margin-top: 100px;
      }
      // tr {
      //   background-color: #efefef !important;
      // }
    }
  }

  // .ant-table-thead {
  //   th {
  //     background: #EFEFEF !important;
  //   }
  // }
  .ant-modal-footer {
    border-top: none;
  }

  .ant-table-tbody {
    .table-item-name {
      background-color: #ffffff !important;

      .just {
        // padding: 19px 50px;
        // border-bottom: 1px solid #d9d9d9;
      }

      .vice {
        // padding: 19px 50px;
      }
    }

    .table-item-delete {
      background-color: #f9f9f9 !important;
    }

    .ant-table-row-cell-break-word {
      padding: 0;
    }

    .opretion-delete {
      // padding: 0 24px;
      font-size: 16px;
      font-weight: 400;
      color: #f46e65;
      line-height: 22px;
      cursor: pointer;
    }

    .test-person {
      .text-person-item {
        display: flex;
        flex-wrap: wrap;
        // padding: 0 0 0 51px;
        padding: 0 0 0 19px;
        display: flex;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        // border-bottom: 1px solid #d9d9d9;
        background-color: #ffffff;

        .label {
          margin: 19px 21px 19px 0;
        }

        .person-box {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          border-bottom: #d9d9d9;

          .personal {
            padding: 19px 0;
            margin-right: 52px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 400;
            color: #333333;

            .name {
              display: inline-block;
              width: 50px;
            }

            .delete-icon {
              margin-left: 10px;
              vertical-align: middle;
              display: inline-block;
              width: 20px;
              height: 20px;
              background: url(./images/delete.png) no-repeat center center;
              cursor: pointer;
              &-gray {
                -webkit-filter: grayscale(100%);
                -moz-filter: grayscale(100%);
                -ms-filter: grayscale(100%);
                -o-filter: grayscale(100%);

                filter: grayscale(100%);
                filter: gray;
                cursor: not-allowed;
              }
            }
          }
        }
      }

      // .text-person-item:last-child {
      //   border-bottom: none;
      // }
    }
  }
}

.org-modal-qr {
  min-width: 1080px;

  .Qr-box {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-around;
    max-height: 850px;
    overflow-y: auto;
    gap: 30px;
    .box {
      position: relative;
      width: 47%;
      border: 1px solid #f46e65;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      // margin-bottom: 30px;
      .qr-warp {
        padding-top: 30px;
      }
    }

    .box:nth-child(3) {
      margin-bottom: 0px;
    }

    .box:nth-child(4) {
      margin-bottom: 0px;
    }

    .qr-text {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      background: #f46e65;
      border-radius: 0px 0px 4px 4px;
      opacity: 1;
      border: 1px solid #f46e65;
      margin-top: 26px;
      padding: 13px 110px 13px 36px;
      flex: 0 0 80%;
      font-size: 16px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      color: #ffffff;
      text-align: left;

      .text {
      }
    }
    .down-text {
      display: flex;
      align-items: center;
      position: absolute;
      right: 15px;
      bottom: 16px;
      cursor: pointer;

      > div {
        font-size: 12px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
      }
      .down-icon {
        display: inline-block;
        color: rgb(255, 255, 255);
        margin-right: 10px;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        border: 1px solid #fff;
        border-radius: 50%;
        line-height: 20px;
        font-size: 12px;
      }
    }
  }
}
