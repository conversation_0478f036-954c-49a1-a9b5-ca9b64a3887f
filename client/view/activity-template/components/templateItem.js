import React, {Component} from 'react';

import PreviewQtn from '../../questionnaire-new/component/modal/previewQtn';
import { getTemplateDetail } from '../../../apis/questionnaire';
import { Popconfirm } from 'antd';

const {Icon} = Ant


export default class Class extends Component {
	constructor(props) {
		super(props)
		this.state = {
			visible: false,
			data: {}
		}
	}
	setTemplate(isUpdate) {
		event.stopPropagation()
		const {push, coupon_template_id} = this.props
		push({pathname:'/questionnaire-new', query: {templateId: coupon_template_id, isUpdate}})
	}
	async previewSub() {
		const {coupon_template_id} = this.props
		const {data: {code = 0, data}} = await getTemplateDetail(coupon_template_id)
		if(code == 0){
			this.setState({
				visible: true,
				data
			})
		}
	}
	setVisible(){
		this.setState({
			visible: false
		})
	}
	render() {
		const SelfIcon = Icon.createFromIconfontCN({
			scriptUrl: '/plugin/js/font_1405225_2rhnjzz1bnw.js',
		});
		const {label = '问卷', title, delTemplate , coupon_template_id } = this.props;
		const { visible, data } = this.state
		return (
			<div className="activity-item">
				<div>
					<span>{label}</span>
				</div>
				<div onDoubleClick={() => this.setTemplate(1)}>{title}</div>
				<div>
					<SelfIcon className="selfIcon" type="iconbianji" title="编辑" onClick={() => this.setTemplate(1)}/>
					<Popconfirm
						title="是否删除该模板？"
						onConfirm={() => delTemplate(coupon_template_id)}
					>
						<SelfIcon className="selfIcon" type="iconshanchu2" title="删除" />
					</Popconfirm>
					<SelfIcon className="selfIcon" type="iconyulan" title="预览" onClick={()=> this.previewSub()}/>
					<SelfIcon className="selfIcon" type="iconshiyongmoban" title="使用模板" onClick={() => this.setTemplate(2)}/>
				</div>
				{
					visible && 
					<PreviewQtn visible={visible} questionnaire={data} setVisible={()=> this.setVisible()}/>
				}
			</div>
		)
	}
}
