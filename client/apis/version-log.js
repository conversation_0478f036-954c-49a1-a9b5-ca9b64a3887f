const { http } = require('client/tool/axios');
import { host } from './config';
// const host = 'http://*************:19802';
//查询升级日志列表
export const getList = (data) => {
    return http.post(`${host}/uc/upgrade-log/list`, data);
}
//根据ID查询日志详情
export const getDetails = (data) => {
    return http.post(`${host}/uc/upgrade-log/details/${data}`);
}
//查询最新的版本记录
export const getLastDetails = () => {
    return http.post(`${host}/uc/upgrade-log/last-details`);
}
export const addLog = (data) => {
    return http.post(`${host}/uc/upgrade-log/add`, data);
}
export const delLog = (data) => {
    return http.delete(`${host}/uc/upgrade-log/del/${data}`);
}
export const editLog = (data) => {
    return http.post(`${host}/uc/upgrade-log/edit`, data);
}
