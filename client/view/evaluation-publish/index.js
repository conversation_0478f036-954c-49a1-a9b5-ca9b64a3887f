import IntranetPublishEvaluation from "./publish-index/intranet";
import SaasPublishEvaluation from "./publish-index/saas";
import { INTRANET_LIST } from "./constants";

export default function PublishEvaluation(props) {
  const hostname = window.location.hostname;
  const isSaas = !INTRANET_LIST.some((item) => hostname.includes(item));

  return isSaas ? (
    <SaasPublishEvaluation {...props} />
  ) : (
    <IntranetPublishEvaluation {...props} />
  );
}
