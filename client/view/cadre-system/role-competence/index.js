import React, { Component } from "react";
import SearchHeader from "components/search-header";
import {
  Button,
  Table,
  Row,
  Col,
  Input,
  Select,
  message,
  Popconfirm,
} from "antd";
import Role from "./component/role";
import "./index.less";
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
import {
  getRoleList,
  getRolePag,
  updateRoleStatus,
  delRole,
} from "client/apis/cadre-portrait";

const Option = Select.Option;

export default class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      search: {
        name: "",
        status: 1,
        page: 1,
        pageSize: 10,
      },
      dataSource: [],
      selectRows: [],
      selectkeys: [],
      total: 1,
      roleVisible: false,
      activeRole: {},
    };
    this.rowSelection = this.rowSelection.bind(this);
    this.pageOnChange = this.pageOnChange.bind(this);
    this.statusOnchange = this.statusOnchange.bind(this);
    this.keyWordOnChange = this.keyWordOnChange.bind(this);
    this.getRoleList = this.getRoleList.bind(this);
    this.batchChangeStatus = this.batchChangeStatus.bind(this);
    this.changeStatus = this.changeStatus.bind(this);
    this.showRoleVisible = this.showRoleVisible.bind(this);
    this.hideRoleVisible = this.hideRoleVisible.bind(this);
  }
  componentDidMount() {
    this.getRoleList();
  }
  rowSelection(selectkeys, selectRows) {
    this.setState({
      selectRows,
      selectkeys,
    });
  }
  showRoleVisible(r) {
    this.setState({
      roleVisible: true,
      activeRole: r || {},
    });
  }
  hideRoleVisible() {
    this.setState({
      roleVisible: false,
      activeRole: {},
    });
    this.getRoleList();
  }
  getRoleList(page) {
    const postData = Object.assign(this.state.search);
    postData.page = page ? page : postData.page;
    getRolePag(this.state.search).then((data) => {
      const _data = data.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        this.setState({
          dataSource: _data.data.data,
          total: _data.data.total,
          selectkeys: [],
          selectRows: [],
        });
      }
    });
  }
  batchChangeStatus(status) {
    if (this.state.selectRows.length == 0) {
      return;
    }
    const postData = {
      role_ids: this.state.selectRows.map((d) => d.role_id),
      status: status,
    };
    updateRoleStatus(postData).then((data) => {
      const _data = data.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        message.success("操作成功");

        this.getRoleList();
      }
    });
  }
  async removeRoleCheck(row) {
    let params = [];
    if (row) {
      params.push(row.role_id);
    } else {
      params = this.state.selectRows.map((d) => d.role_id);
    }

    const res = await delRole(params);

    if (res.data.code === 0) {
      message.success("操作成功");

      this.getRoleList();
    } else {
      message.error(res.data.msg);
    }
  }
  changeStatus(row, status) {
    const postData = {
      role_ids: [row.role_id],
      status: status,
    };
    updateRoleStatus(postData).then((data) => {
      const _data = data.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        message.success("操作成功");

        this.getRoleList();
      }
    });
  }
  keyWordOnChange(e) {
    const search = this.state.search;
    search.name = e.target.value;
    this.setState({
      search,
    });
  }
  statusOnchange(val) {
    const search = this.state.search;
    search.status = val;
    this.setState({
      search,
    });
  }
  pageOnChange(page) {
    const search = this.state.search;
    search.page = page;
    this.setState(
      {
        search,
      },
      this.getRoleList
    );
  }
  render() {
    const columns = [
      {
        title: "序号",
        dataIndex: "index",
        key: "index",
        align: "center",
        render: (t, row, i) => {
          return i + 1;
        },
      },
      {
        title: "角色名称",
        dataIndex: "name",
        key: "name",
        align: "center",
      },
      {
        title: "描述",
        dataIndex: "remark",
        key: "remark",
        align: "center",
      },
      {
        title: "角色状态",
        dataIndex: "status",
        key: "status",
        width: 290,
        align: "center",
        render: (t, row) => {
          if (row.status == 1) {
            return <span className="user-status-normal">正常</span>;
          } else if (row.status == 2) {
            return <span className="user-status-disable">停用</span>;
          }
        },
      },
      {
        title: "操作",
        dataIndex: "handler",
        key: "handler",
        align: "center",
        width: "20%",
        render: (t, r) => {
          return (
            <React.Fragment>
              <a
                href="javascript:;"
                className="table-option-btn"
                onClick={() => {
                  this.showRoleVisible(r);
                }}
              >
                编辑
              </a>
              <Popconfirm
                placement="top"
                title={"确定你的操作"}
                onConfirm={() => {
                  this.removeRoleCheck(r, 3);
                }}
                okText="确定"
                cancelText="取消"
              >
                <a href="javascript:;" className="table-option-btn">
                  移除
                </a>
              </Popconfirm>
              {r.status == 2 ? (
                <Popconfirm
                  placement="top"
                  title={"确定你的操作"}
                  onConfirm={() => {
                    this.changeStatus(r, 1);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <a href="javascript:;" className="table-option-btn">
                    启用
                  </a>
                </Popconfirm>
              ) : (
                <Popconfirm
                  placement="top"
                  title={"确定你的操作"}
                  onConfirm={() => {
                    this.changeStatus(r, 2);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <a href="javascript:;" className="table-option-btn">
                    停用
                  </a>
                </Popconfirm>
              )}
            </React.Fragment>
          );
        },
      },
    ];
    const { history } = this.props;
    return (
      <div className="role-competence">
        <SearchHeader onBack={history.goBack} title="角色管理" />
        <div className="warp">
          <Button
            className="add-user-group"
            type="primary"
            onClick={this.showRoleVisible}
          >
            新增角色
          </Button>
          <Row className="search-group">
            <Col span={4}>
              角色状态：
              <Select
                defaultValue="1"
                style={{ width: 120 }}
                onChange={this.statusOnchange}
              >
                <Option value="1">正常</Option>
                <Option value="2">停用</Option>
                <Option value="">全部</Option>
              </Select>
            </Col>

            <Col span={4}>
              关键字：
              <Input
                style={{ width: 180 }}
                onChange={this.keyWordOnChange}
                value={this.state.search.name}
                placeholder="请输入角色名称或描述"
              />
            </Col>
            <Col span={2}>
              <Button
                type="primary"
                onClick={() => {
                  this.getRoleList(1);
                }}
              >
                查询
              </Button>
            </Col>
          </Row>
          <div className="option-btn">
            <Popconfirm
              placement="top"
              title={"确定你的操作"}
              onConfirm={() => {
                this.batchChangeStatus(2);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button className="btn">批量停用</Button>
            </Popconfirm>
            <Popconfirm
              placement="top"
              title={"确定你的操作"}
              onConfirm={() => {
                this.batchChangeStatus(1);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button className="btn">批量启用</Button>
            </Popconfirm>
            <Popconfirm
              placement="top"
              title={"确定你的操作"}
              onConfirm={() => {
                this.removeRoleCheck();
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button className="btn">批量移除</Button>
            </Popconfirm>
          </div>

          <Table
            rowKey="role_id"
            columns={columns}
            bordered
            dataSource={this.state.dataSource}
            rowSelection={{
              onChange: this.rowSelection,
              selectedRowKeys: this.state.selectkeys,
            }}
            pagination={{
              current: this.state.search.page,
              pageSize: this.state.search.pageSize,
              onChange: this.pageOnChange,
              total: this.state.total,
            }}
          />
        </div>
        {this.state.roleVisible ? (
          <Role
            visible={this.state.roleVisible}
            hideRoleVisible={this.hideRoleVisible}
            activeRole={this.state.activeRole}
            reload={this.getRoleList}
          />
        ) : null}
      </div>
    );
  }
}
