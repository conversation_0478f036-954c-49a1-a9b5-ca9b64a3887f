import { http, headers } from "client/tool/axios";
import { ucHost } from "./config";

// 风采/发展史 查询
export const mien = (params) => {
  return http.get(`${ucHost}/person-center/org/highlight/list`, params);
};
// 风采 / 发展史 添加
export const addData = (params) => {
  return http.post(`${ucHost}/person-center/org/highlight/add`, params);
};
// 风采 / 发展史 删除
export const delData = (params) => {
  return http.post(`${ucHost}/person-center/org/highlight/del`, params);
};
// 风采 / 发展史 编辑
export const editMienData = (params) => {
  return http.post(`${ucHost}/person-center/org/highlight/edit`, params);
};

// 4010202020801-查看推优标准 http://wiki.aidangqun.com/project/4?p=810
export const searchStandar = (params) => {
  return http.get(`${ucHost}/push-excellent/list`, params);
};

// 40002030306-选择推优列表 http://wiki.aidangqun.com/project/4?p=805
export const getExcellentList = (params) => {
  return http.get(`${ucHost}/person-center/org/wait_excellent/list`, params);
};

// 40002030307-确定推优风采 http://wiki.aidangqun.com/project/4?p=806
export const setSureExcellent = (params) => {
  return http.post(`${ucHost}/person-center/sure_excellent`, params);
};
// 40002030308-取消推优优风采 http://wiki.aidangqun.com/project/4?p=807
export const setCancelExcellent = (params) => {
  return http.post(`${ucHost}	/person-center/cancel_excellent`, params);
};