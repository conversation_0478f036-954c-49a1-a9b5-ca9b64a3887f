/**
 * 2020/1/15 JiaoXiWei整理
 */
import React, { Component } from "react";
import {
  Modal,
  Button,
  Tree,
  message as Message,
  AutoComplete,
  Popover,
  Tag,
  Icon,
} from "antd";
import TopForm from "./top-form";
import PersonTable from "./person-table";
import { fetchOrganisitionTree } from "apis/organisition";
import { locateOrgTree, getTreeList, findOrgByName } from "apis/organize";
import { fetchStorage } from "tool/util";
import propTypes from "prop-types";
import "./person-modal.less";

const TreeNode = Tree.TreeNode;
const isObjEqual = (o1, o2) => {
  var props1 = Object.getOwnPropertyNames(o1);
  var props2 = Object.getOwnPropertyNames(o2);
  if (props1.length != props2.length) {
    return false;
  }
  for (var i = 0, max = props1.length; i < max; i++) {
    var propName = props1[i];
    if (o1[propName] !== o2[propName]) {
      return false;
    }
  }
  return true;
};
class PersonModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      org_type: -1,
      org_id: -1,
      // 1为父树，2为子树
      tree_type: 1,
      treeData: [],
      loadedKeys: [],
      expandedKeys: [],
      selectedKeys: [],
      autoExpandParent: false,
      keyword: null,
      selected_org_id: null,
      selected_org_type: null,
      tree_list: null,
      autoCompleteData: [],
      propverVisible: false,
      organizeName: null,
      selectData: [],
      selectedRowKeys: [],
      selectedRows: [],
      org_tree_id: null,
      dataTotal: 0, // 当前查询结果总数
      isSelectAll: false, //是否选中当前所有查询结果
      queryCriteria: {}, //查询后保存当前查询条件
      query_users: [], //全选后保存当前查询条件
      selectedIndex: -1,
    };
    this.treeItemPos = { a: 111 };
    this.init = this.init.bind(this);
  }

  componentWillMount() {
    this.initLoginOrgId();
  }

  componentWillUpdate(nextProps, nextState) {
    // 特定id查询处理
    if (nextProps.orgId != -1 && nextProps.orgId !== nextState.org_id) {
      this.setState({
        org_id: nextProps.orgId,
      });
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.visible != nextProps.visible && nextProps.visible) {
      this.topForm && this.topForm.resetHandler();
    }
  }

  init(inputData) {
    if (inputData.selectAll) {
      const queryCriteria = { ...this.state.queryCriteria };
      const query_users = inputData.query_users;
      let isSelected = false;
      queryCriteria.total && delete queryCriteria.total;
      for (const item of query_users) {
        const _item = { ...item };
        delete _item.total;
        //判断当前条件是否已添加
        isSelected = isObjEqual(_item, queryCriteria);
        if (isSelected) break;
      }
      this.setState({
        isSelectAll: isSelected,
        selectAllNum: query_users.length,
        query_users,
      });
      return;
    }
    // phone_secret 必要属性
    inputData.forEach((item) => {
      item.phone_secret = item.phone;
    });
    // 保证this.personTable已加载
    setTimeout(() => {
      this.personTable && this.personTable.init(inputData);
    }, 300);
  }

  async initLoginOrgId() {
    const { orgId } = this.props;
    let org_id =
      orgId && orgId !== -1
        ? orgId
        : (typeof window !== "undefined" &&
            window.sessionStorage.getItem("_oid")) ||
          0;
    org_id = Number(org_id);
    const org_data = (await getTreeList()).data;
    if (org_data.code === 0 && org_data.data.length) {
      this.setState({
        tree_list: org_data.data,
        organizeName: org_data.data[0].tree_name,
        org_tree_id: org_data.data[0].organization_id,
      });
    }
    this.setState({ org_id });
    return org_id;
  }

  // 如果外部有获取数据方法，则将数据导出
  onChange(data) {
    const { onChange } = this.props;
    onChange && onChange(data);
  }
  // 提交方法
  submitHandler() {
    const { hideModal, onOk } = this.props;
    const { selectedRows, query_users } = this.state;
    console.log(selectedRows, "selectedRows");
    console.log(query_users, "query_users");
    let param = [...selectedRows];
    param.forEach((item) => {
      // const phone = item.phone
      item.phone = item.phone_secret;
    });
    // 默认取person-table组件的值
    if (query_users.length) {
      param = {
        selectAll: true,
        data: query_users,
      };
    }
    this.setState({ propverVisible: false });
    this.onChange(param);
    onOk ? onOk(param) : hideModal();
  }

  onHideModal() {
    const { hideModal, onCancel } = this.props;
    this.setState({ propverVisible: false });
    onCancel ? onCancel() : hideModal();
  }

  async fetchOrganisition(queryParams = {}, hasRoot) {
    console.log(queryParams, "queryParams");
    const { loadedKeys } = this.state;
    queryParams.load_root = hasRoot ? 1 : 0;
    const response = await fetchOrganisitionTree(queryParams);
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (hasRoot) {
      if (data[0]) {
        let expandedKeys = [String(data[0].org_id)];
        this.setState({ expandedKeys });
      }
    }
    if (!loadedKeys.length) {
      let loadedKey = [];
      for (const org of data) {
        org.children.map((val) => loadedKey.push(val.org_id));
      }
      if (!loadedKey.length) {
        this.setState({
          treeData: [],
          loadedKeys: [],
        });
        return;
      }
      this.setState({
        treeData: data,
        loadedKeys: loadedKey,
      });
    } else {
      alert(1);
      this.setState({ treeData: data });
    }
  }

  getAutoComplete(value) {
    const { org_id, tree_type } = this.state;
    if (this.autoCompleteTimeout) {
      clearTimeout(this.autoCompleteTimeout);
      this.autoCompleteTimeout = null;
    }
    value = value.trim();
    if (!value) {
      this.setState({ autoCompleteData: [] });
    } else {
      this.autoCompleteTimeout = setTimeout(async () => {
        const result = (
          await findOrgByName({
            org_id,
            org_name: value,
            tree_type,
          })
        ).data;
        if (result.code != 0) {
          Message.error(result.message);
          return;
        }
        const data = result.data;
        this.setState({
          autoCompleteData: data
            .slice(0, data.length > 10 ? 10 : data.length)
            .map((val) => {
              return {
                value: val.org_id,
                name: val.org_name,
              };
            }),
        });
      }, 300);
    }
  }

  _scrollPos(orgId) {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      delete this.scrollTimeout;
    }
    this.scrollTimeout = setTimeout(() => {
      const treeContainer = document.getElementById(
        "member-manager-tree-container"
      );
      const el = this.refs[orgId];
      if (el) {
        treeContainer.scrollTop = el.offsetTop - treeContainer.offsetTop - 10;
      }
      delete this.scrollToOrgId;
    }, 300);
  }

  async loadOrgTree(payload = {}) {
    const oid =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";
    const { target } = payload;
    const { isPartyGroup, isLeaderGroup, isEmployee, rootOrgId } = this.props;
    const { tree_type, selected_org_id, expandedKeys, org_type, treeData } =
      this.state;
    //定位当前节点的父节点
    let result = (
      await locateOrgTree(
        Object.assign({
          root_org_id: rootOrgId ? rootOrgId : isLeaderGroup ? 1 : oid,
          org_id: selected_org_id,
          org_type: org_type ? org_type : treeData[0].treeData,
          tree_type,
          load_root: 1,
        })
      )
    ).data;
    const data = result.data;
    let key = [];
    const renderData = (val) => {
      if (val && val.children) {
        key.push(val.org_id + ""), val.children.forEach(renderData);
        return;
      }
    };
    isLeaderGroup
      ? data[0].children.forEach(renderData)
      : data.forEach(renderData);
    this.setState(
      {
        treeData: data,
        keyword: null,
        selected_org_id,
        autoExpandParent: true,
        selectedKeys: [selected_org_id + ""],
        expandedKeys: Array.from(new Set(expandedKeys.concat(key))),
      },
      async () => {
        target();
        this.topForm.resetHandler(true);
        this.personTable &&
          this.personTable.fetchUserList({
            org_id: selected_org_id,
            page: 1,
            pagesize: 10,
            is_employee: isEmployee,
          });
      }
    );
  }

  onChangeOrgTree(e) {
    const { tree_list, org_id } = this.state;
    const { isLeaderGroup } = this.props;
    const clickEl = e.target;
    //已加载
    if (clickEl.getAttribute("class").includes("propver-active")) {
      this.setState({ propverVisible: false });
      return;
    }
    const siblingss = clickEl.parentNode.children;
    for (const el of siblingss) {
      el.classList.remove("propver-active");
    }
    clickEl.classList.add("propver-active");
    const index = clickEl.getAttribute("value");
    const value = tree_list[index];
    let orgId =
      typeof window !== "undefined" && window.sessionStorage.getItem("_oid");
    this.setState(
      {
        loadedKeys: [],
        expandedKeys: [],
        selectedKeys: [],
        org_type: value.org_type || null,
        tree_type: value.tree_type,
        propverVisible: false,
        organizeName: value.tree_name,
        org_tree_id: value.organization_id,
      },
      () => {
        console.log(1);
        this.fetchOrganisition(
          {
            org_id: isLeaderGroup ? orgId : value.organization_id,
            org_type: value.org_type,
            tree_type: value.tree_type,
          },
          true
        );
      }
    );
  }

  //组织选择列表
  onChangePopVisible() {
    this.setState({ propverVisible: !this.state.propverVisible });
  }

  //清空已选
  onClearSelected() {
    this.setState({
      selectedRowKeys: [],
      selectedRows: [],
      isSelectAll: false,
      // queryCriteria: {},
      query_users: [],
    });
    this.personTable.isAll([]);
  }

  // 删除已选
  onCloseTag(value, onClose) {
    console.log(
      "🚀 ~ file: person-modal.js ~ line 367 ~ value",
      value,
      value.split("-")
    );
    const { selectedRows, selectedRowKeys } = this.state;
    const keys = selectedRowKeys.filter((val) => val !== value);
    console.log(keys);
    const rows = selectedRows.filter(
      (item) => value.split("-")[0] != item.user_id
    );
    console.log(rows);
    this.setState({
      selectedRowKeys: keys,
      selectedRows: rows,
    });
    this.personTable.isAll(keys);
  }
  onCloseAllTag(index) {
    const { query_users, queryCriteria, isSelectAll } = this.state;
    const query = query_users.filter((_, i) => i != index);

    //判断删除的是否当前选中的条件
    const _item = { ...query_users[index] };
    const _query = { ...queryCriteria };
    delete _item.total;
    delete _query.total;
    const isSelected = isObjEqual(_item, _query);

    this.setState({
      isSelectAll: isSelected ? !isSelected : isSelectAll,
      query_users: query,
    });
    this.personTable.isAll([]);
  }
  //选择所有查询结果
  selectAll() {
    const {
      query_users = [],
      queryCriteria,
      org_id,
      selectedKeys,
      dataTotal,
      isSelectAll,
    } = this.state;
    if (!isSelectAll && dataTotal > 0) {
      queryCriteria.org_id = selectedKeys.length
        ? selectedKeys[0] * 1
        : org_id * 1;
      queryCriteria.total = dataTotal;
      query_users.push(queryCriteria);
      this.setState({
        isSelectAll: true,
        query_users,
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  }
  queryCriteria(param) {
    const { keyword = {}, query_users } = this.state;
    let isSelected = false;
    let selectedIndex = -1;
    const queryCriteria = { ...param, ...keyword };
    //查询条件只有一个时  默认删除
    if (keyword && Object.keys(keyword).length === 1 && keyword.cert_type) {
      delete queryCriteria.cert_type;
    }
    delete queryCriteria.page;
    delete queryCriteria.pagesize;
    !queryCriteria.is_employee && delete queryCriteria.is_employee;
    for (const [key, item] of query_users.entries()) {
      const _item = { ...item };
      delete _item.total;
      //判断当前条件是否已添加
      isSelected = isObjEqual(_item, queryCriteria);
      if (isSelected) {
        selectedIndex = key;
        break;
      }
    }
    this.setState({
      isSelectAll: isSelected,
      queryCriteria,
      selectedIndex,
    });
  }
  render() {
    const {
      treeData,
      expandedKeys,
      loadedKeys,
      org_id,
      org_type,
      keyword,
      selectedKeys,
      autoExpandParent,
      tree_type,
      selected_org_id,
      selected_org_type,
      tree_list,
      autoCompleteData,
      propverVisible,
      organizeName,
      selectedRowKeys,
      selectedRows,
      dataTotal,
      isSelectAll,
      query_users,
      selectedIndex,
    } = this.state;
    const {
      visible,
      hideModal,
      dataSource,
      title,
      isUpdateMeeting,
      radio,
      isPartyGroup,
      isLeaderGroup,
      onCancel,
      isEmployee,
      selectAll,
      orgId,
    } = this.props;
    const modalProps = {
      footer: null,
      title: title || "",
      visible,
      // destroyOnClose: true,
      wrapClassName: "person-modal-new",
      width: 1124,
      onCancel: () => {
        this.setState({ propverVisible: false });
        onCancel ? onCancel() : hideModal();
      },
    };
    const topFormProps = {
      wrappedComponentRef: (ref) => (this.topForm = ref),
      isPartyGroup,
      isLeaderGroup,
      queryCriteria: (param) => {
        this.queryCriteria(param);
      },
      onSearchHandler: (value) => {
        const userInfoString = fetchStorage(2, "userInfo");
        let org_type = -1,
          org_id = -1,
          selected_org_id = -1,
          selected_org_type = -1;
        try {
          const userInfo = JSON.parse(userInfoString);
          org_type = userInfo.org_type;
          selected_org_type = userInfo.org_type;
          org_id = orgId && orgId != -1 ? orgId : userInfo.oid; //调用模块传递orgid
          selected_org_id = orgId && orgId != -1 ? orgId : userInfo.oid;
        } catch (err) {
          console.warn(err);
        }
        if (isLeaderGroup) {
          this.setState(
            {
              org_type,
              org_id,
              selected_org_id,
              selected_org_type,
              selectedKeys: [selected_org_id + ""],
              keyword: value,
            },
            () => {
              this.personTable &&
                this.personTable.getFindAllUser({
                  param: value,
                  org_type,
                  page: 1,
                });
            }
          );
        } else {
          let _orgId = 0;
          if (value) {
            const { selected_org_id, selected_org_type } = this.state;
            _orgId = selected_org_id ? selected_org_id : org_id;
            const orgType = selected_org_type ? selected_org_type : org_type;
            // orgId = value.only_current_org ? org_id : selected_org_id ? selected_org_id : org_id;
            // const orgType = value.only_current_org ? org_type : selected_org_type? selected_org_type: org_type;
            this.setState({
              org_type: orgType,
              org_id: _orgId,
              selected_org_id: _orgId,
              selected_org_type: orgType,
              selectedKeys: [_orgId + ""],
            });
            this.personTable &&
              this.personTable.fetchUserListByKeyword({
                ...value,
                org_id: _orgId,
                is_employee: isEmployee,
              });
          } else {
            const { selected_org_id, org_id } = this.state;
            _orgId = selected_org_id ? selected_org_id : org_id;
            this.personTable &&
              this.personTable.fetchUserList({
                org_id: _orgId,
                page: 1,
                pagesize: 10,
                is_employee: isEmployee,
              });
          }
          this.setState({ keyword: value }, () => {
            this.queryCriteria({ org_id: _orgId, is_employee: isEmployee });
          });
        }
      },
      orgChangeHandler: (values = {}) => {
        const { org_id } = this.state;
        const { org_type = null, tree_type = 1 } = values;
        this.setState(
          {
            loadedKeys: [],
            expandedKeys: [],
            selectedKeys: [],
            org_type: values.org_type || null,
            tree_type,
          },
          () => {
            console.log(2);
            this.fetchOrganisition(
              {
                org_id: isLeaderGroup ? 0 : org_id,
                org_type: org_type,
                tree_type,
              },
              true
            );
          }
        );
      },
      initOrgType: (payload = {}) => {
        const { org_id } = this.state;
        const { org_type, tree_type } = payload;
        this.setState(
          {
            org_type,
            tree_type,
          },
          () => {
            console.log(3,org_type);
            this.fetchOrganisition(
              { org_id: isLeaderGroup ? 0 : org_id, org_type, tree_type },
              true
            );
          }
        );
      },
      setKeyword: (values = {}) => this.setState({ keyword: values }),
    };
    const TreeProps = {
      selectedKeys,
      expandedKeys,
      loadedKeys,
      autoExpandParent,
      loadData: (treeNode) => {
        return new Promise(async (resolve) => {
          if (
            (treeNode.props.children && treeNode.props.children.length > 0) ||
            !treeNode.props.dataRef.child_org_num
          ) {
            resolve();
            return;
          }
          const { org_id } = treeNode.props.dataRef;
          const { org_type, tree_type } = this.state;
          console.log(org_type, "org_type");
          const response = await fetchOrganisitionTree({
            org_id,
            org_type,
            tree_type,
          });
          const { data: body } = response;
          const { code, data, message } = body;
          if (code !== 0) {
            Message.error(message);
            return;
          }
          let loadedKey = [];
          if (!data.length) {
            treeNode.props.dataRef.child_org_num = 0;
          } else {
            treeNode.props.dataRef.children = data.map((item) => {
              loadedKey.push(item.org_id);
              return { ...item, isLeaf: item.child_org_num === 0 };
            });
          }
          this.setState({
            treeData: [...this.state.treeData],
            loadedKeys: Array.from(new Set(loadedKeys.concat(loadedKey))),
          });
          resolve();
        });
      },
      onSelect: (selectedKeys, { node }) => {
        if (!selectedKeys.length) {
          return;
        }
        const { props = {} } = node;
        const { dataRef = {} } = props;
        this.setState({ selectedKeys });
        let org_id = dataRef.org_id;
        let org_type = dataRef.org_type;
        if (
          !selectedKeys ||
          !Array.isArray(selectedKeys) ||
          selectedKeys.length === 0
        ) {
          const userInfoString = fetchStorage(2, "userInfo");
          org_id = this.initLoginOrgId();
          try {
            const userInfo = JSON.parse(userInfoString);
            org_type = userInfo.org_type || -1;
          } catch (err) {
            org_type = -1;
          }
        } else if (!org_id) {
          org_id = this.initLoginOrgId();
        }
        this.setState(
          {
            selected_org_id: org_id,
            selected_org_type: org_type,
          },
          () => {
            let param = {
              org_id,
              is_employee: isEmployee,
            };
            if (keyword) {
              param = {
                ...keyword,
                ...param,
              };
              param.only_manager = param.only_manager ? 1 : 0;
              param.only_current_org = param.only_current_org ? 1 : 0;
              this.personTable &&
                this.personTable.fetchUserListByKeyword(param);
            } else {
              this.personTable &&
                this.personTable.fetchUserList({
                  org_id,
                  page: 1,
                  pagesize: 10,
                  is_employee: isEmployee,
                });
            }
            this.queryCriteria(param);
          }
        );
      },
      onExpand: (expandedKeys) =>
        this.setState({
          expandedKeys,
          autoExpandParent: false,
        }),
    };

    const personTableProps = {
      isEmployee,
      isSelectAll,
      selectAllNum: query_users.length,
      ref: (ref) => (this.personTable = ref),
      radio,
      isPartyGroup,
      isLeaderGroup,
      isUpdateMeeting,
      dataSource,
      selectedRowKeys,
      selectedRows,
      selectData: (key, rows) =>
        this.setState({
          selectedRowKeys: key,
          selectedRows: rows,
        }),
      dataTotal: (total) => {
        this.setState({
          dataTotal: total,
        });
      },
      queryCriteria: (param) => {
        const _param = param;
        this.queryCriteria(_param);
      },
      org_id,
      org_type,
      selected_org_id,
      selected_org_type,
      keyword,
      tree_type,
    };

    const treeNodesRender = (treeData) => {
      return treeData.map((node) => {
        let currentCssName = "";
        if (node.org_id == selected_org_id) {
          currentCssName = "current-item";
        }
        if (node.children) {
          return (
            <TreeNode
              isLeaf={node.child_org_num === 0}
              title={
                <div ref={node.org_id} className={currentCssName}>
                  <span>{node.short_name || node.name}</span>
                </div>
              }
              key={node.org_id}
              dataRef={node}
            >
              {treeNodesRender(node.children)}
            </TreeNode>
          );
        }
        return (
          <TreeNode
            isLeaf={node.child_org_num === 0}
            title={
              <div ref={node.org_id} className={currentCssName}>
                <span>{node.short_name || node.name}</span>
              </div>
            }
            key={node.org_id}
            dataRef={node}
            ref={(nodes) => this.getTreeItemPos(nodes, node)}
          />
        );
      });
    };

    const autoCompleteProps = {
      onSearch: (e) => this.getAutoComplete(e),
      onSelect: (e) => {
        if (e.orgId == selected_org_id) {
          return;
        }
        const { expandedKeys } = this.state;
        e.target = () => {
          this.scrollToOrgId = e.orgId;
          this._scrollPos(e.orgId);
        };
        if (loadedKeys.includes(e.orgId)) {
          this.setState(
            {
              keyword: null,
              selected_org_id: e.orgId,
              autoExpandParent: true,
              selectedKeys: [e.orgId + ""],
              expandedKeys: Array.from(
                new Set(expandedKeys.concat([e.orgId + ""]))
              ),
            },
            async () => {
              e.target();
              // this.topForm.resetPersonSearcher(true);
              this.topForm.resetHandler(true);
              // 清空搜索内容
              this.personTable &&
                this.personTable.fetchUserList({
                  org_id: e.orgId,
                  page: 1,
                  pagesize: 10,
                  is_employee: isEmployee,
                });
            }
          );
        } else {
          this.setState({ selected_org_id: e.orgId }, () =>
            this.loadOrgTree({ target: e.target })
          );
        }
      },
    };
    const renderAutoCompleteList = () =>
      autoCompleteData.map((item) => (
        <AutoComplete.Option
          className="member-manage-autoComplete"
          key={item.value}
          text={item.name}
          value={item.name}
          title={item.name}
          onClick={(e) =>
            autoCompleteProps.onSelect({
              orgId: item.value,
              org: item.name,
              target: true,
            })
          }
        >
          {item.name}
        </AutoComplete.Option>
      ));
    const popoverEl = () => (
      <div className="items" onClick={(e) => this.onChangeOrgTree(e)}>
        {tree_list &&
          tree_list.map((val, index) => (
            <div
              className={"item " + (index === 0 ? "propver-active" : "")}
              value={index}
              key={index}
            >
              {val.tree_name}
            </div>
          ))}
      </div>
    );
    const selectedNum = () => {
      let num = query_users.length ? 0 : selectedRowKeys.length;
      query_users.length &&
        query_users.forEach((item) => {
          num += item.total;
        });
      return num;
    };

    return (
      <Modal {...modalProps}>
        <div className="main-content-wrapper">
          <div className="organize-left-wrapper">
            <div className="tree-wrapper-title">
              <span className="organize-tree-name">{organizeName}</span>
              <Popover
                overlayClassName="tree-wrapper-propver"
                placement="bottomRight"
                content={popoverEl()}
                visible={propverVisible}
                trigger="click"
                arrowPointAtCenter
              >
                <Button
                  className="tree-wrapper-title_btn"
                  icon="swap"
                  onClick={() => this.onChangePopVisible()}
                ></Button>
              </Popover>
              <AutoComplete
                allowClear
                onSearch={autoCompleteProps.onSearch}
                placeholder={"请输入组织名称"}
                optionLabelProp="value"
              >
                {renderAutoCompleteList()}
              </AutoComplete>
            </div>
            <div
              id="member-manager-tree-container"
              className="organize-tree-wrapper"
            >
              {treeData && Array.isArray(treeData) && treeData.length !== 0 ? (
                <Tree {...TreeProps}>{treeNodesRender(treeData)}</Tree>
              ) : (
                <div style={{ padding: 10 }}>暂无数据</div>
              )}
            </div>
          </div>
          <div className="person-table-wrapper">
            <div className="top-form-wrapper">
              <TopForm {...topFormProps} />
            </div>

            <div style={{ marginBottom: 10 }}>
              {selectAll ? (
                <span>
                  筛选结果{dataTotal}人，您可以
                  <a onClick={() => this.selectAll()}>选择所有筛选人员</a>
                </span>
              ) : (
                ""
              )}
            </div>
            <div className="select-content-wrapper">
              <PersonTable {...personTableProps} />
              <div className="person-selected-list">
                <div className="selected-list-title">
                  <span className="selected-num">
                    已选择人员({selectedNum()})
                  </span>
                  <Button
                    style={{ color: "#666" }}
                    onClick={() => this.onClearSelected()}
                  >
                    清空已选
                  </Button>
                </div>
                <div className="selected-list">
                  {query_users.length
                    ? query_users.map((item, index) => (
                        <Button.Group key={index}>
                          <Button
                            className={`selected-list_tag ${
                              selectedIndex == index && "selected"
                            }`}
                            data-key={index}
                            onClick={() => this.onCloseAllTag(index)}
                          >
                            已选择{item.total}人
                            <Icon type="close" />
                          </Button>
                        </Button.Group>
                      ))
                    : selectedRows.map((val, i) => (
                        <Button.Group key={i}>
                          <Button
                            className="selected-list_tag"
                            data-key={val.key}
                            onClick={() => this.onCloseTag(val.key)}
                          >
                            {val.name || val.user_name}
                            <Icon type="close" />
                          </Button>
                        </Button.Group>
                      ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="buttons-wrapper">
          <Button type="primary" onClick={() => this.submitHandler()}>
            确定
          </Button>
          <Button onClick={() => this.onHideModal()}>取消</Button>
        </div>
      </Modal>
    );
  }
}

PersonModal.propTypes = {
  visible: propTypes.bool,
  hideModal: propTypes.func,
  dataSource: propTypes.array,
  title: propTypes.string,
  onChange: propTypes.func,
  multiple: propTypes.bool,
  radio: propTypes.bool,
  isPartyGroup: propTypes.bool,
  isLeaderGroup: propTypes.bool,
  isEmployee: propTypes.number,
  selectAll: propTypes.bool, //全选所有
  orgId: propTypes.number, //组织id
  rootOrgId: propTypes.number, //顶级ID
};

PersonModal.defaultProps = {
  isEmployee: undefined,
  visible: false,
  hideModal: () => {},
  dataSource: [],
  title: "",
  onChange: () => {},
  multiple: false,
  radio: false,
  isPartyGroup: false,
  isLeaderGroup: false,
  selectAll: false,
  orgId: -1,
};

export default PersonModal;
