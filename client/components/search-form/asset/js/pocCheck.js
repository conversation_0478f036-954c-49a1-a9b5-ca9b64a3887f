import { useEffect, useState } from 'react';
import './asset/formLess/check.less';
const PocCheck = ({ value, type = 'check', options = [] }) => {
    const [checkOptions, setCheckOptions] = useState([]);

    useEffect(() => {
        setCheckOptions(
            options.map((item) => ({
                ...item,
                check: false,
            }))
        );
    }, [options]);

    const onActive = (itemValue) => {
        let newValue;

        if (type === 'radio') {
            newValue = itemValue === value ? undefined : itemValue;
        } else {
            newValue = Array.isArray(value) ? [...value] : [];
            const index = newValue.findIndex((val) => val === itemValue);

            if (index !== -1) {
                newValue.splice(index, 1);
            } else {
                newValue.push(itemValue);
            }
            newValue = newValue.filter((val) => !!val);
        }

        // 触发事件，更新父组件的值
        const onChange = (newValue) => {
            // 这里可以触发父组件的事件来更新 value
            // 例如：this.props.onChange(newValue)
            console.log('New value:', newValue);
        };

        onChange(newValue);
    };

    const isActive = (itemValue) => {
        return type === 'radio' ? itemValue === value : (value || []).includes(itemValue);
    };

    return (
        <div className="poc-check">
            {checkOptions.map((item, index) => (
                <span
                    key={index}
                    className={`check ${isActive(item.value) ? 'active' : ''}`}
                    onClick={() => onActive(item.value)}
                >
                    {item.label}
                </span>
            ))}
        </div>
    );
};

export default PocCheck;