import React, { Component } from 'react'
import './style.less'
import PropTypes from 'prop-types'
import classnames from 'classnames'

class VerifyCodeButton extends Component {
  constructor(props) {
    super(props)
    this.state = {
      timer: 0,
      text: '获取验证码',
      tid: null,
      disabled: false
    }
  }

  handleClick() {
    if (this.state.tid) {
      return
    }

    const callback = (isSuccess) => {
      if (isSuccess) {
        const tid = setInterval(() => {
          const timer = this.state.timer
    
          if (timer < 60) {
            this.setState({
              ...this.state,
              timer: timer + 1,
              text: `${60 - (timer + 1)}s`
            })
          } else {
            this.setState({
              tid: null,
              timer: 0,
              text: '获取验证码',
              disabled: false
            })
            clearInterval(tid)
          }
        }, 1000)
    
        this.setState({
          ...this.state,
          tid,
          disabled: true,
          text: '60s'
        })
      }
    }
    
    this.props.verifyCode(callback)
  }

  componentWillUnmount() {
    if (this.state.tid) {
      clearInterval(this.state.tid)
    }
  }

  render() {
    return (
      <a className={classnames({
          ['verifycode-vfcode']: true,
          ['verifycode-vfcode-disabled']: this.state.disabled
        })} 
        onClick={this.handleClick.bind(this)}
      >
        {this.props.text||this.state.text}
      </a>
    )
  }
}

VerifyCodeButton.propTypes = {
  verifyCode: PropTypes.func,
  text: PropTypes.string
}

export default VerifyCodeButton