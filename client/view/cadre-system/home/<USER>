.cadre-home {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 68px;
    font-size: 36px;
    font-family: FZChaoCuHei-M10S-Regular, FZChaoCuHei-M10S;
    font-weight: 400;
    color: #ffffff;
    background: url(./image/header.png) center / cover no-repeat;
  }
  .menu {
    display: flex;
    .menu-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      height: 76px;
      background: #ffffff;
      box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.05);
      border-bottom: 4px solid transparent;
      .label {
        font-size: 20px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: bold;
        color: #222222;
        cursor: pointer;
      }
      img {
        width: 30px;
        height: 30px;
        object-fit: contain;
      }
    }
    .active-menu {
      border-bottom: 4px solid #008eff;
      .label {
        color: #008eff;
      }
    }
  }
  .content {
    padding: 20px 0;
    flex: 1;
    overflow: hidden;
  }
}
