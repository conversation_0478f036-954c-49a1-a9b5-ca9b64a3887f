import React from "react";
import SearchHeader from "components/search-header";
import StatusIcon from "components/status-icon";
import moment from "moment";
import "./style.less";
import LookDetail from "./components/look-detailt";
import {
  Form,
  Input,
  Button,
  Table,
  Popconfirm,
  message,
  Spin
} from "antd";
import {
  queryAssessPlanList,
  switchAssessPlan,
  deleteAssessPlan,
  queryAssessPlanDetail
} from "apis/assess-plan-manage";

const FormItem = Form.Item;

class AssessPlanManage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      planList: [], // 计划列表
      total: 0, // 总数
      pageIndex: 1, // 当前页
      pageSize: 10, // 每页条数
      tableLoading: false, // table 家载状态
      name: "", // 查询的计划名称
      pageLoading: false, // 页面加载状态
      // 查看详情
      visible: false,
      detailData: [], // 详情 modal 数据
      // 是否已经进行过查询
      isSearched: false,
      execute_orgs_filtered: [],
      filterValue: "",
    }
    this.renderHeader = this.renderHeader.bind(this);
    this.updateState = this.updateState.bind(this);
    this.getAssessPlanList = this.getAssessPlanList.bind(this);
    this.getAssessPlanDetail = this.getAssessPlanDetail.bind(this);
  }

  componentDidMount() {
    this.getAssessPlanList();
  }


  updateState(payload, callback) {
    this.setState(payload, () => callback && callback())
  }

  // 或取考核计划列表
  // todo
  getAssessPlanList(_data = {}) {
    const { name } = this.state;
    this.setState({
      tableLoading: true,
      // planList: []
    });
    if (_data && !_data.name && name) {
      _data = { ..._data, name };
    }
    queryAssessPlanList(_data).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        this.setState({
          planList: data.data,
          total: data.total,
          pageIndex: data.pageNum,
          pageSize: data.pageSize,
          tableLoading: false
        });
      } else {
        message.error(data.message);
        this.setState({
          tableLoading: false
        })
      }
    })
  }

  // 停启用考核计划
  switch_assess_plan(meeting_plan_id, is_execute) {
    const { pageIndex, pageSize } = this.state;
    switchAssessPlan({ meeting_plan_id, is_execute }).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        if (data.data) {
          message.success(is_execute === 1 ? "启用成功" : "停用成功");
          this.getAssessPlanList({ page_no: pageIndex, page_size: pageSize });
        } else {
          message.error(is_execute === 1 ? "启用失败" : "停用失败");
        }
      }
    })
  }

  // 删除考核计划
  delAssessPlan(_data) {
    this.setState({
      tableLoading: true
    });
    deleteAssessPlan(_data).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        message.success("删除成功");
        this.getAssessPlanList();
      } else {
        this.setState({
          tableLoading: false
        })
        message.error("删除失败")
      }
    }).catch(
      (error) => {
        this.setState({
          tableLoading: false
        });
        console.error(error);
      }
    )
  }

  // 查询考核计划详情
  getAssessPlanDetail(_data = {}) {
    this.setState({
      pageLoading: true
    })
    queryAssessPlanDetail(_data).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        this.setState({
          visible: true,
          detailData: data.data,
          pageLoading: false
        });
        // console.log(data.data);
        if (data.data) {
          const execute_orgs = data.data.execute_orgs;
          if (execute_orgs && Array.isArray(execute_orgs)) {
            this.setState({
              execute_orgs_filtered: execute_orgs
            });
          }
        }
      } else {
        this.setState({
          pageLoading: false
        })
        message.error(data.message);
      }
    })
  }

  // 头部 element node
  renderHeader() {
    const { isSearched } = this.state;
    const { history, form } = this.props;
    const {
      getFieldDecorator,
      validateFields,
      resetFields,
      getFieldValue
    } = form;
    const getName = () => {
      validateFields((err, value) => {
        if (!err) {
          this.updateState({
            name: value.name
          }, () => {
            if (value.name) {
              this.updateState({
                isSearched: true
              });
            } else {
              this.updateState({
                isSearched: false
              });
            }
            this.getAssessPlanList({ name: value.name });
          })
        }
      })
    }
    const onReset = () => {
      resetFields();
      this.setState({
        name: ""
      }, () => {
        getName();
      })
    }
    return (
      <div className="operatorHeader">
        <Button
          type="primary"
          icon="plus"
          className="btnBg"
          onClick={() => history.push("/add-assess-plan")}
        >
          {/* 新增考核计划 */}
          {/* 新增组织生活 */}
          新增
        </Button>
        <Form
          layout="inline"
          className="headerForm"
        >
          <FormItem
            // label="计划名称"
            // label="组织生活名称"
            label="活动类型名称"
          >
            {getFieldDecorator("name", {})(
              <Input placeholder="请输入" className="queryKeyword" onPressEnter={getName} />
            )}
          </FormItem>
          <FormItem
            wrapperCol={{
              span: 24
            }}
          >
            <Button
              type="primary"
              className="queryBtn"
              onClick={getName}
            >
              查询
            </Button>
            {
              isSearched &&
              <Button
                className="queryBtn"
                style={{ marginLeft: 10 }}
                onClick={onReset}
              >
                重置
              </Button>
            }
          </FormItem>
        </Form>
      </div>
    )
  }

  render() {

    const {
      planList,
      tableLoading,
      total,
      pageIndex,
      pageSize,
      name,
      pageLoading,
      visible,
      detailData,
      filterValue,
      execute_orgs_filtered
    } = this.state;


    const lookDetail = {
      execute_orgs_filtered,
      filterValue,
      visible,
      detailData,
      cancel: () => this.updateState({ visible: false }),
      updateState: (payload = {}, callback) => {
        this.setState({
          ...payload
        }, () => {
          callback && callback();
        })
      },
      init: () => {

      }

    }

    const columns = [
      {
        title: "序号",
        align: "center",
        width: 80,
        render: (val, record, index) => (index + 1)
      },
      {
        // title: "计划名称",
        // title: "组织生活名称",
        title: "活动类型名称",
        dataIndex: "name",
        align: "left"
      },
      {
        // title: "执行方式",
        title: "任务周期",
        dataIndex: "execute_type",
        align: "center",
        width: 140,
        render: (val, record) => {
          switch (val) {
            case 1:
              return "自定义时间段";
            case 2:
              return "自然月周期";
            case 3:
              return "季度周期";
            case 4:
              return "年度周期";
            default:
              return "——————"
          }
        }
      },
      {
        title: "开始时间",
        dataIndex: "start_time",
        align: "center",
        width: 140,
      },
      {
        title: "结束时间",
        dataIndex: "end_time",
        align: "center",
        width: 140,
        render: (val) => {
          if (val === undefined || val === "") {
            return "—————";
          }
          return val
        }
      },
      {
        title: "状态",
        dataIndex: "is_execute",
        align: "center",
        width: 140,
        render: (val) => {
          if (val === 0) {
            return (
              <StatusIcon text="已停用" type={"disable"} />
            )
          } else {
            return (
              <StatusIcon text="已启用" type={"success"} />
            )
          }
        }
      },
      {
        title: "操作",
        align: "center",
        width: 200,
        render: (val, record) => {
          return (
            <div className="operatorPlan">
              {record.is_execute === 0
                ? <Popconfirm
                  title={"启用后将立即派发相关活动，是否继续？"}
                  onConfirm={() => this.switch_assess_plan(record.meeting_plan_id, 1)}
                >
                  <a>启用</a>
                </Popconfirm>
                : <Popconfirm
                  // title={`是否停用组织生活: ${record.name}`}
                  title={`是否停用: ${record.name}`}
                  onConfirm={() => this.switch_assess_plan(record.meeting_plan_id, 0)}
                >
                  <a>停用</a>
                </Popconfirm>}
              <a
                onClick={() => this.getAssessPlanDetail(record.meeting_plan_id)}
              >查看详情</a>
              <Popconfirm
                title={"删除后，下个执行周期将不再派发相关活动，确认继续吗？"}
                onConfirm={() => this.delAssessPlan(record.meeting_plan_id)}
              >
                <a>删除</a>
              </Popconfirm>

            </div>
          )
        }
      }
    ]

    const tableProps = {
      columns,
      bordered: true,
      className: "planList",
      dataSource: planList,
      loading: tableLoading,
      rowKey: "meeting_plan_id",
      pagination: {
        total,
        current: pageIndex,
        pageSize,
        showTotal: total => `共${total}条记录， ${Math.ceil(total / pageSize)}页`,
        pageSizeOptions: ["10", "20", "30", "40", "50"],
        showQuickJumper: true,
        showSizeChanger: true,
        onChange: (_page, _pageSize) => {
          this.getAssessPlanList({ name, page_no: _page, page_size: _pageSize });
        },
        onShowSizeChange: (_page, _pageSize) => {
          this.getAssessPlanList({ name, page_no: _page, page_size: _pageSize })
        }
      }
    }

    return (
      <div className="AssessPlanManage">
        <SearchHeader
          onBack={false}
          // title="考核计划管理"
          // title="组织生活设置"
          title="活动类型设置"
          style={{ fontWeight: "bold" }}
        />
        <Spin spinning={pageLoading}>
          <div className="body">
            {this.renderHeader()}
            <Table {...tableProps} />
            <LookDetail {...lookDetail} />
          </div>
        </Spin>
      </div>
    )
  }
}

export default Form.create()(AssessPlanManage);