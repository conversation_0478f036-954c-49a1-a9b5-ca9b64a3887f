import { useState, useEffect } from "react";
import { Spin, Table, Button, message, Popconfirm } from "antd";
import DrawerComn from "./components/drawerComn";
import { connect } from "dva";
import SearchHeader from "components/search-header";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import { findServiceOrgList, removeMmaOrg, remexportServiceListoveMmaOrg } from "apis/organize";
import FileDownload from 'client/components/file-download';
import { getRootOrgId } from 'apis/users';
import "./index.less";

const PartyServiceCenter = (props) => {
  const { name: org_name, oid: org_id } = props.userInfo;
  const { history } = props;
  const [open, setOpen] = useState(false);
  const [parentId, setParentId] = useState();
  const [operationType, setOperationType] = useState(1); // 1 新增  2 编辑 3 详情
  const [dataSource, setDataSource] = useState([]);
  console.log('🚀 ~ file: index.jsx:18 ~ dataSource:', dataSource);
  const [pagination, setPagination] = useState({ page: 1, page_size: 10000 });
  // const [total, setTotal] = useState(0);
  const [orgInfo, setOrgInfo] = useState({});
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false)
  const [orgData, setOrgData] = useState(() => ({ org_id, org_name }))

  useEffect(() => {
    getRootOrgId(102817).then(({ data: res }) => {
      res.code === 0 && setParentId(res.data)
    })
  }, []);

  useEffect(() => {
    findServiceOrgListFn();
  }, [orgData.org_id, pagination]);

  const findServiceOrgListFn = () => {
    setLoading(true);
    setDataSource([])
    findServiceOrgList({
      org_id: orgData.org_id,
      ...pagination,
    }).then(({ data: res }) => {
      setLoading(false);
      if (res.code === 0) {
        // setDataSource([...res.data]);
        setDataSource(res.data);
        setTotal(res.total);
      } else {
        message.error(res.message);
      }
    });
  };


  const handleChildInfo = (record, type) => {
    setOperationType(type);
    setOrgInfo(record);
    setOpen(true);
  };

  const removeMmaOrgFn = (org_id) => {
    removeMmaOrg({ org_id }).then(({ data: res }) => {
      if (res.code === 0) {
        message.success("删除成功！");
        findServiceOrgListFn();
      } else {
        message.error(res.message || "删除失败,请重试！");
      }
    });
  };

  const columns = [
    {
      title: "名称",
      dataIndex: "org_name",
      align: "center",
    },
    {
      title: "负责人",
      dataIndex: "charge",
      align: "center",
    },
    {
      title: "负责人手机号",
      dataIndex: "charge_phone",
      align: "center",
    },
    {
      title: "政治面貌",
      dataIndex: "charge_political",
      align: "center",
    },
    {
      title: "联系人",
      dataIndex: "contacts",
      align: "center",
    },
    {
      title: "联系人手机号",
      dataIndex: "contacts_phone",
      align: "center",
    },
    {
      title: "挂靠党组织",
      dataIndex: "affiliation_name",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      align: "center",
      width: 250,
      render: (_, record) => (
        <div>
          <Button
            type="link"
            onClick={() => handleChildInfo(record, 3)}
          >
            详情
          </Button>
          <Button
            type="link"
            onClick={() => handleChildInfo(record, 2)}
          >
            修改
          </Button>
          <Popconfirm
            placement="topRight"
            title="确认删除?"
            okText="确定"
            cancelText="取消"
            onConfirm={() => removeMmaOrgFn(record.org_id)}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  const drawerProps = {
    open,
    operationType,
    parentId,
    orgInfo,
    setOrgInfo,
    findServiceOrgListFn,
    setOpen,
  };

  const organizeModalProps = {
    radio: true,
    visible,
    dataSource: [orgData],
    hideModal: () => {
      setVisible(false)
    },
    loadOrganizeData: ([data]) => {
      setOrgData(data)
      setVisible(false)
    },
  }

  return (
    <div className="PartyServiceCenter">
      <SearchHeader title="党群服务中心" />
      <main className="PartyServiceCenter-main">
        <Spin spinning={loading}>
          <div className="header-btn">
            <div className='header-btn-left'>
              <Button
                type="primary"
                onClick={() => {
                  setOperationType(1);
                  setOpen(true);
                }}
              >
                添加
              </Button>
              <Button
                className='btn-skip'
                onClick={() => {
                  history.push('/field-manage')
                  // window.open('http://**************:8004/ResourceLibrary/FieldLibraryDocument')
                }}
              >
                场地库
              </Button>
            </div>

            {/* <Button
              onClick={() => {
                remexportServiceListoveMmaOrg(orgData.org_id)
              }}
            >
              导出
            </Button> */}
            <FileDownload
              filePath={`user/mma/org/export-service-list?org_id=${orgData.org_id}`}
              btnName="导出"
            />
          </div>
          <div className="selected-org-wrap">
            <span>{orgData.org_name || orgData.name}</span>
            <Button type="link" onClick={() => setVisible(true)}>选择组织</Button>
          </div>
          <Table
            style={{ marginTop: 15 }}
            rowKey="org_id"
            bordered
            dataSource={dataSource}
            columns={columns}
            pagination={false
              /* {
              total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条数据`,
              onChange: (page, page_size) => setPagination({ page, page_size }),
            } */
            }
          />
        </Spin>
      </main>
      <OrganizeModal {...organizeModalProps} />
      <DrawerComn {...drawerProps} />
    </div>
  );
};

// export default PartyServiceCenter;

const mapStateToProps = ({ userInfo }) => ({ userInfo });
export default connect(mapStateToProps)(PartyServiceCenter);
