.Teacher-library {
  &-content {
    margin-top: 24px;

    &-tabs {
      padding: 0 31px 27px;
      border-bottom: 1px solid #e4e4e4;

      &-item {
        margin-right: 60px;
        display: inline-block;
        position: relative;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
        cursor: pointer;
      }

      .active {
        font-weight: bold;
        color: #ff4d4f;

        &::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: -6px;
          width: 100%;
          height: 3px;
          background: #ff4d4f;
          border-radius: 2px;
        }
      }
    }

    &-search {
      padding: 31px;

      .ant-select-selection--multiple > ul > li,
      .ant-select-selection--multiple
        .ant-select-selection__rendered
        > ul
        > li {
        margin-top: 5px;
      }

      &-org {
        margin-bottom: 32px;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4a4a4a;

        .ant-btn-link {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #359af7;
        }
      }

      &-form {
        margin-bottom: 20px;

        .ant-select-selection {
          min-width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-select-selection__rendered {
          line-height: 36px;
        }

        .ant-input {
          width: 600px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-btn-primary {
          height: 36px;
          background: #f46e65;
          border-radius: 4px;
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
        }
      }
    }

    &-list {
      padding: 0 31px;
      display: flex;
      flex-wrap: wrap;
      gap: 26px;

      .document-item {
        display: flex;
        flex: 0 1 32%;
        padding: 14px 25px 0px 24px;
        background: #f6f8f9;
        border-radius: 8px;
        cursor: pointer;

        &-img {
          margin-top: -4px;
          margin-bottom: 9px;
          width: 183px;
          height: 224px;
          margin-right: 22px;
          border-radius: 4px;
        }

        .document-img {
          object-fit: cover;
        }

        .document-default {
          padding: 27px 16px 47px 17px;
          background: url("../../image/document-default.png") no-repeat
            center/contain;
          text-align: center;
          font-size: 16px;
          font-family: PingFang SC-Heavy, PingFang SC;
          font-weight: 800;
          color: #ffffff;
        }

        &-info {
          flex: 1;

          &-title {
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            font-size: 36px;
            font-family: PingFang SC-Heavy, PingFang SC;
            font-weight: 800;
            color: #000000;
            line-height: 36px;
          }
          // &-content ::first-letter,
          // &-title ::first-letter {
          //   color: #f5222d;
          // }
          &-belong_unit {
            margin-bottom: 8px;
            font-size: 18px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #666666;
            line-height: 24px;
          }

          &-tags {
            margin-bottom: 12px;
            height: 24px;
            > span {
              padding: 5px;
              margin-right: 8px;
              background: #eeeeee;
              border-radius: 10px;
              font-size: 16px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #9ea09e;
            }
          }
          &-contact_details {
            margin: 12px 0;
            font-size: 18px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
          }
          &-content {
            font-size: 18px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            line-height: 24px;
            overflow: hidden;
            color: #333333;
            text-overflow: ellipsis; //文本溢出显示省略号
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
            display: -webkit-box;
            -webkit-line-clamp: 2;
          }
        }
      }
    }

    .ant-pagination {
      margin: 77px 0 65px;
      padding: 0 31px;
      text-align: right;

      .ant-select-selection {
        border-radius: 4px;
      }
    }
  }
}
