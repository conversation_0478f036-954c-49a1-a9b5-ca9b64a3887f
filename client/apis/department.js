import { ucHost } from './config';
import {http} from 'client/tool/axios';
//
// const ucHost = 'http://*************:8080';

export const getDep = () => {
    return http.get(`${ucHost}/uc/dep/get-dep`);
};

export const addDep = (data) => {
    return http.post(`${ucHost}/uc/dep/add`, data);
}

export const updateDep = (data) => {
    return http.post(`${ucHost}/uc/dep/update`, data);
}

export const moveDep = (data) => {
    return http.post(`${ucHost}/uc/dep/move`, data);
}

export const delDep = (id) => {
    return http.delete(`${ucHost}/uc/dep/del/${id}`);
}

export const getDepManager = (id) => {
    return http.get(`${ucHost}/uc/user/getDepManager/${id}`);
}

export const delManager = (data) => {
    return http.post(`${ucHost}/uc/user/setManager`, {...data, type: 2});
}
