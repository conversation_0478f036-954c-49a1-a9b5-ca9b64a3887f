/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-08 14:38:39
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-16 17:21:40
 * 数据体检
 */
import React, { useEffect, useState, useMemo } from "react";
import "./index.less";
import TreeSelect from "./tree-select";
import CheckView from "./check-view";
import { message, Spin } from "antd";
import { getExamineStart } from "apis/data-physical-examination";

export default function index({ history }) {
  const orgId =
    typeof window !== "undefined" && window.sessionStorage.getItem("_oid");
  const orgName =
    typeof window !== "undefined" &&
    unescape(window.sessionStorage.getItem("_org_name"));
  const [loading, setLoading] = useState(true);
  // 0.未检测  1.有检测数据 有异常  2. 检测中  3.无异常
  const [checkStatus, setCheckStatus] = useState(2);
  //检测结果
  const [checkResultData, setCheckResultData] = useState({});

  useEffect(() => {
    onHandClick();
  }, []);
  /**
   * 级联下拉选中
   *
   */
  const onTreeChange = (orgID, orgName, orgShortName) => {
    onModifyLoading(true);
    onHandClick(orgID, 0, orgName, orgShortName);
  };

  /**
   * 开始检测
   * @param {*} oid 检测组织id
   * @param {*} flag 0首次或1重新检测
   * @param {*} org_name 检测组织名称
   * @returns
   */
  const onHandClick = async (
    oid = orgId,
    flag = 0,
    org_name = orgName,
    orgShortName
  ) => {
    // setCheckStatus(2)
    flag && onModifyLoading(true);
    const { data: res } = await getExamineStart({ org_id: oid, flag });
    onModifyLoading();
    const { code, data } = res;
    if (code !== 0) {
      message.error(res.message);
      return false;
    }
    // if (Number(data.status) === 1) {
    data.org_id = oid;
    data.org_name = org_name;
    data.org_short_name = orgShortName;
    // }
    setCheckStatus(data.status);
    setCheckResultData(data);
    if (data.status === 2 && flag === 1) {
      message.success("检测时间可能较长，请稍后查看");
      setTimeout(() => {
        onHandClick(oid, 0, org_name, orgShortName);
      }, 30000);
    }
  };

  /**
   * 修改加载状态
   *
   */
  const onModifyLoading = (state = false) => {
    setLoading(state);
  };

  const checkViewProps = {
    orgId,
    className: "flex-width",
    history,
    checkStatus,
    checkResultData,
    setCheckStatus,
    onHandClick,
  };
  const tree = useMemo(() => {
    const TreeProps = { orgId, checkStatus, onTreeChange, onModifyLoading };
    return <TreeSelect {...TreeProps} />;
  }, []);

  // console.log('====================================');
  // console.log("组织id",orgId,orgName);
  // console.log('====================================');
  return (
    <div className="data-physical-examination">
      {tree}
      <CheckView {...checkViewProps} />
      <div className={`spin-box ${loading ? "" : "loading-hidden"}`}>
        <Spin size="large" delay={50} />
      </div>
    </div>
  );
}
