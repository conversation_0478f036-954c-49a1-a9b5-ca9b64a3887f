import React from "react";
// import OperationHeader from "../assessment-statistical/components/operationHeader";
import TableView from "./components/table-view";
// import NavHeader from "./components/nav-header";
import SearchHeader from "components/search-header";
import TopForm from "./components/top-form";
import "./index.less";
import "./components/style.less";
import { connect } from "dva";
import { Spin, message as Message, Button, Modal } from "antd";
import qs from "querystring";
import { evalHost } from "apis/config";
import { fileDownload } from 'client/components/file-download';

import DetailModal from "./components/detail-modal";

class AssessmentOrgDetail extends React.Component {
  constructor(props) {
    super(props);
  }
  componentWillMount() {
    const { history, location = {}, dispatch, assessmentOrgDetail } = this.props,
      { queryParams } = assessmentOrgDetail || {},
      { state = {} } = location,
      { org } = state;
    // console.log(org);
    // 从路由中获取id
    // console.log(assessmentOrgDetail)
    if (!org || !org.org_id) {
      Message.error("组织信息查询失败");
      // history.goBack();
      return;
    } else {
      queryParams.year = org.year
    }
    dispatch({
      type: "assessmentOrgDetail/save",
      payload: {
        org
      }
    });
    // console.log(queryParams, org.org_id);
    if (queryParams.time === -1) {
      delete queryParams.time;
    }
    if (queryParams.deduct_type === -1) {
      delete queryParams.deduct_type;
    }
    dispatch({
      type: "assessmentOrgDetail/getCountDeductLog",
      payload: {
        org_id: org.org_id,
        ...queryParams
      }
    })
  }
  componentDidMount() {
    // this.AsmentDetail.tableLoading = true;
    // this.AsmentDetail.pageLoading = true;
    // let tLoading = this.AsmentDetail.tableLoading,
    //   pLoading = this.AsmentDetail.pageLoading;
    // this.props.dispatch({ type: "AssessmentOrgDetail/tableLoading", tLoading });
    // this.props.dispatch({ type: "AssessmentOrgDetail/tableLoading", pLoading });
  }

  componentWillUnmount() {
    // console.log("组件卸载");
    const { dispatch } = this.props;
    dispatch({
      type: "assessmentOrgDetail/resetQueryParams"
    });
  }

  render() {
    const { dispatch, history, assessmentOrgDetail } = this.props;
    const {
      dataSource,
      tableLoading,
      queryParams,
      org = {},
      modalVisible,
      deductLogId,
      deductLogData,
      dataCollectData,
      updateScoreData,
      modalTitle
    } = assessmentOrgDetail || {};

    const topFormProps = {
      dispatch,
      queryParams,
      org,
    };

    const tableViewProps = {
      dataSource,
      tableLoading,
      dispatch
    };

    const downloadPath = (queryParams = {}, org = {}) => {
      // time: -1,
      // deduct_type: -1
      if (queryParams.time === -1) {
        delete queryParams.time;
      }
      if (queryParams.deduct_type === -1) {
        delete queryParams.deduct_type;
      }
      if (org && org.org_id) {
        queryParams.org_id = org.org_id;
      }
      // console.log("导出条件", evalHost, qs.stringify(queryParams));
      return `/eval/count/deduct-log/export?${qs.stringify(queryParams)}`;
    }

    const detailModalProps = {
      title: modalTitle || "查看",
      visible: modalVisible,
      detailData: deductLogData,
      dataCollectData: dataCollectData,
      updateScoreData,
      cancelHandler() {
        dispatch({
          type: "assessmentOrgDetail/reset",
          payload: {}
        });
      }
    };

    return (
      <div className="AsmentSta">
        <SearchHeader title={org ? org.org_name : ""} onBack={() => {
          history && history.goBack();
        }} />
        {/* <NavHeader headerText="市政府办公厅" /> */}
        <div className="body">
          <div className="top-form-container">
            <TopForm {...topFormProps} />
            <a className="ant-btn query-btn"
            onClick={()=>{
              fileDownload(downloadPath(queryParams, org))
            }}
            href="javascript:void(0);"
            >导出</a>
          </div>
          <TableView {...tableViewProps} />
        </div>
        <DetailModal {...detailModalProps} />
      </div>
    );
  }
}
const mapStateToProps = ({ assessmentOrgDetail }) => ({ assessmentOrgDetail });
// const propsProxyHoc = WrappedComponent => class extends Component {
//   render() {
//     return (<WrappedComponent
//       {...this.props}
//       handleClick={this.handleClick}
//     />);
//   }
// };
export default connect(mapStateToProps)(AssessmentOrgDetail);
