.vitalityIndex {
  // font-family: '.PingFang SC-Semibold', 'PingFang SC';
  background-color: #f1f5f8;
  min-width: 980px;
  overflow: auto;

  .ant-spin {
    top: 50%;
    left: 50%;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 9px 32px;
    font-size: 16px;

    .update-time {
      color: #999;
    }
  }

  &-main {
    .ant-card {
      margin-top: 20px;

      .ant-card-head-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        color: #333;
        font-weight: 600;

        &::before {
          content: "";
          margin-right: 12px;
          display: inline-block;
          width: 4px;
          height: 20px;
          background-color: #3a65fe;
          border-radius: 2px;
          vertical-align: -14%;
        }
      }

      &-body {
        padding: 1px;

        >.ant-card {
          margin-top: 0;
        }
      }
    }

    .title-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px 32px;

      .title {
        font-size: 16px;
        line-height: 40px;
        font-weight: 600;
        color: #333333;
      }

      .ant-radio-group {
        .ant-radio-button-wrapper {
          border-color: #f46e65;
          color: #f46e65;

          &:hover {
            color: #fff;
            background-color: #ff9c91;
          }
        }

        .ant-radio-button-wrapper-checked {
          color: #fff;
          background-color: #f46e65;

          &::before {
            background-color: #f46e65 !important;
            opacity: 1;
          }
        }
      }

      .ant-select,
      .ant-calendar-picker {
        // font-family: "Monospaced Number";
        margin: 0 20px;
        width: 110px;
        font-weight: 500;
      }

      .anticon-right {
        padding: 5px;
        color: #999;
        cursor: pointer;
      }
    }

    .category-wrap {
      padding: 32px 0;

      .category-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 18px;

        .itemWrap .item {
          padding: 13px;
        }
      }
    }

    .itemWrap {
      flex: 1;
      padding: 0 30px;
      width: 100%;
      box-sizing: border-box;

      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        border-radius: 2px;
        color: rgba(0, 0, 0, 0.65);

        .text {
          display: flex;
          align-items: center;
          width: 40px;
          height: 48px;
          margin: 0 12%;
          font-size: 16px;
        }

        .number-wrap {
          flex: 1;
          font-size: 32px;
          line-height: 54px;
          text-align: center;
          border-left: 1px solid rgba(0, 0, 0, 0.15);
        }
      }

      &-1 .item {
        color: rgba(255, 255, 255, 0.85);

        .number-wrap {
          border-color: rgba(255, 255, 255, 0.25);
        }
      }

      &-2 .item {

        /* .text {
          margin: 0 12%;
        } */
        .number-wrap {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          padding-left: 30px;
          line-height: 38px;

          .extra-wrap {
            display: flex;
            width: 100%;
            font-size: 14px;
            line-height: 16px;

            .icon1,
            .icon2 {
              position: relative;

              &::after {
                content: "";
                position: absolute;
                bottom: 3px;
                margin-left: 2px;
                width: 8px;
                height: 8px;
                background: url(./image/slice2.png);
              }
            }

            .icon1 {
              margin-right: 16px;

              &::after {
                background: url(./image/slice1.png);
              }
            }
          }
        }

        @media screen and (max-width: 1700px) {
          .text {
            margin: 0 6%;
          }

          .number-wrap {
            padding-left: 10px;
          }
        }
      }
    }

    .cardList-wrap {
      .title-wrap {
        margin: 0 14px 0 0;
        flex: 1;

        .ant-select,
        .ant-calendar-picker {
          margin-right: 0;
        }
      }

      .ant-card-body::-webkit-scrollbar {
        height: 5px;
      }

      .ant-card-body {
        // text-align: center;
        white-space: nowrap;
        // overflow-x: hidden;
        overflow-y: hidden;
        // display: flex;
        height: auto;
        padding: 18px;
        padding-bottom: 32px;
        width: 100%;



        .card-item {
          display: inline-block;
          white-space: nowrap;
          height: 150px;
          width: 377px;
          // flex: 1;
          padding: 14px 18px;
          // animation: 10s wordsLoop linear infinite normal;
          // -webkit-animation: roll 15s linear infinite;
          // animation: roll 15s linear infinite;

          @keyframes roll {
            0% {
              transform: translateX(0%);
              -webkit-transform: translateX(800px);
            }

            100% {
              transform: translateX(-3770px);
              -webkit-transform: translateX(-3770px);
            }
          }

          .item {
            display: flex;
            padding: 18px 18px 18px 0;
            border-radius: 8px;
            background-color: #e9f3ff;

            .rank-icon {
              display: inline-block;
              width: 70px;
              height: 70px;
              text-align: center;
            }

            .rank-4 {
              padding: 0 8px 0 12px;
              width: 70px;
              height: 70px;
              box-sizing: border-box;
              text-align: center;
              font-size: 45px;
              line-height: 45px;
              color: #7ea4d1;
            }

            .item-info {
              flex: 1;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.65);
              font-size: 12px;

              .org {
                display: flex;
                justify-content: space-between;

                .ord-name {
                  display: inline-block;
                  white-space: normal;
                  color: #000;
                  width: 80%;
                  font-size: 14px;
                }

                .org-score {
                  width: 20%;
                  display: inline-block;
                  margin-left: 8px;
                  color: #fe3a3b;
                  font-size: 16px;
                  line-height: 22px;
                  text-align: right;
                }
              }

              .org-type {
                margin: 17px 0 8px;
              }
            }

          }
        }
      }
    }
  }
}