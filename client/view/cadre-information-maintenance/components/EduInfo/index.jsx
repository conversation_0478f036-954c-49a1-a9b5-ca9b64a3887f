import {
  But<PERSON>,
  Icon
} from "antd";
import { queryByCode } from "client/apis/cadre-portrait";
import { uuid } from "client/tool/uuid";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import FormRow from "./FormRow";
import "./index.less";
function index(props, ref) {
  const { getFieldDecorator, history, data, disabled } = props;
  const { location } = history;

  const formRef = useRef({});
  const formEduRef = useRef({});

  // 教育信息
  const [education, setEducation] = useState([
    {
      key: uuid(),
    },
  ]);
  // 全日制教育
  const [fullTimeEdu, setFullTimeEdu] = useState([
    {
      key: uuid(),
    },
  ]);

  const [eduOption, setEduOption] = useState([]);
  const [degreeOption, setDiplomaOption] = useState([]);

  useImperativeHandle(ref, () => ({
    getData: () => {
      return {
        fullEdu: getData(),
        edu: getData1(),
      };
    },
  }));

  const getData = () => {
    const _formRef = formRef.current;

    const data = [];

    Object.keys(_formRef).forEach((item) => {
      _formRef[item] &&
        _formRef[item].validateFields((err, values) => {
          console.log(values);

          data.push(values);
        });
    });

    return data;
  };

  const getData1 = () => {
    const _formEduRef = formEduRef.current;

    const data1 = [];

    Object.keys(_formEduRef).forEach((item) => {
      _formEduRef[item] &&
        _formEduRef[item].validateFields((err, values) => {
          data1.push(values);
        });
    });

    return data1;
  };

  useEffect(() => {
    if (data) {
      // 全日制1.全日制教育 2.在职教育
      const fullEdu = data.filter((item) => item.type === 1);

      const edu = data.filter((item) => item.type === 2);

      edu.length &&
        setEducation(
          edu.map((item) => {
            return { ...item, key: uuid() };
          })
        );

      if (fullEdu.length) {
        const data = fullEdu.map((item) => {
          return { ...item, key: uuid() };
        });

        setFullTimeEdu(data);

        // setFullTimeEdu1(data);
      }
    }
    queryByCode({ code: 100301 }).then((res) => {
      if (res.data.code === 0) {
        setEduOption(res.data.data);
      }
    });

    queryByCode({ code: 100302 }).then((res) => {
      if (res.data.code === 0) {
        setDiplomaOption(res.data.data);
      }
    });
  }, [data]);

  const Card = ({ title, children }) => {
    return (
      <div className="edu-card">
        <div className="title">{title}</div>
        <div className="gap">{children}</div>
      </div>
    );
  };
  const onBlur = () => {
    setFullTimeEdu([...getData()]);
  };
  const onBlur1 = () => {
    setEducation([...getData1()]);
  };
  const onAddEdu = () => {
    setFullTimeEdu([...getData(), { key: uuid() }]);
  };
  const onAddEdu1 = () => {
    setEducation([...getData1(), { key: uuid() }]);
  };
  const onChange = useCallback((key, value, index) => {
    const current = fullTimeEdu1.find((item) => item.key === index);
    if (!current) return;
    current[key] = value;
    // fullTimeEdu[index][key] = value;

    setFullTimeEdu1([...fullTimeEdu1]);
  }, []);
  // const FormRow = useMemo(
  //   () =>
  //     ({ onChange, data = {} }) => {
  //       return (
  //         <Row span={24} type="flex" align="middle" justify="space-around">
  //           <Col span={2}>
  //             <div className="form-col">
  //               <div className="form-label">开始时间</div>
  //               <div className="form-content">
  //                 <DatePicker
  //                   onChange={(date) => {
  //                     onChange("start_time", date.format("YYYY-MM-DD"));
  //                   }}
  //                   placeholder="请选择"
  //                   defaultValue={
  //                     data.start_time ? moment(data.start_time) : undefined
  //                   }
  //                   className="form-input"
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //           <Col>
  //             <div className="driver">-</div>
  //           </Col>
  //           <Col span={2}>
  //             <div className="form-col">
  //               <div className="form-label">结束时间</div>
  //               <div className="form-content">
  //                 <DatePicker
  //                   onChange={(date) => {
  //                     onChange("end_time", date.format("YYYY-MM-DD"));
  //                   }}
  //                   placeholder="请选择"
  //                   defaultValue={
  //                     data.end_time ? moment(data.end_time) : undefined
  //                   }
  //                   className="form-input"
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //           <Col span={3}>
  //             <div className="form-col">
  //               <div className="form-label">学校</div>
  //               <div className="form-content">
  //                 <Input
  //                   onChange={(e) => {
  //                     onChange("school", e.target.value);
  //                   }}
  //                   placeholder="请选择"
  //                   defaultValue={data.school}
  //                   className="form-input"
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //           <Col span={3}>
  //             <div className="form-col">
  //               <div className="form-label">院系</div>
  //               <div className="form-content">
  //                 <Input
  //                   onChange={(e) => {
  //                     onChange("department", e.target.value);
  //                   }}
  //                   placeholder="请选择"
  //                   defaultValue={data.department}
  //                   className="form-input"
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //           <Col span={4}>
  //             <div className="form-col">
  //               <div className="form-label">专业</div>
  //               <div className="form-content">
  //                 <Input
  //                   onChange={(e) => {
  //                     onChange("specialty", e.target.value);
  //                   }}
  //                   placeholder="请选择"
  //                   defaultValue={data.specialty}
  //                   className="form-input"
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //           <Col span={4}>
  //             <div className="form-col">
  //               <div className="form-label">学历</div>
  //               <div className="form-content">
  //                 <Select
  //                   placeholder="请选择"
  //                   defaultValue={data.diploma}
  //                   onChange={(value) => onChange("diploma", value)}
  //                 >
  //                   {eduOption.map((item) => {
  //                     return (
  //                       <Option value={item.op_key}>{item.op_value}</Option>
  //                     );
  //                   })}
  //                 </Select>
  //               </div>
  //             </div>
  //           </Col>
  //           <Col span={4}>
  //             <div className="form-col">
  //               <div className="form-label">备注说明</div>
  //               <div className="form-content">
  //                 <Input
  //                   onChange={(e) => onChange("remark", e.target.value)}
  //                   defaultValue={data.remark}
  //                 />
  //               </div>
  //             </div>
  //           </Col>
  //         </Row>
  //       );
  //     },
  //   []
  // );

  const FormCom = useMemo(
    () => () => {
      {
        return fullTimeEdu.map((item, index) => {
          return (
            <FormRow
              ref={(ref) => {
                formRef.current[index] = ref;
              }}
              data={item}
              index={item.key}
              key={item.key}
              eduOption={fullTimeEdu}
            />
          );
        });
      }
    },
    [fullTimeEdu]
  );
  const FormCom1 = useMemo(() => () => { }, [education]);
  return (
    <div className="edu-info">
      <Card title="全日制教育">
        {/* {fullTimeEdu.map((item, index) => {
          return (
            <FormRow
              data={item}
              index={item.key}
              key={item.key}
              onChange={onChange}
              eduOption={eduOption}
            />
          );
        })} */}
        {fullTimeEdu.map((item, index) => {
          return (
            <FormRow
              ref={(ref) => {
                formRef.current[index] = ref;
              }}
              disabled={disabled}
              data={item}
              index={item.key}
              key={item.key}
              onBlur={onBlur}
              eduOption={eduOption}
              degreeOption={degreeOption}
            />
          );
        })}
        {!disabled && (
          <div className="btn-box">
            <Button type="primary" onClick={onAddEdu}>
              <Icon type="plus" />
              添加
            </Button>
          </div>
        )}
      </Card>
      <Card title="在职教育">
        {education.map((item, index) => {
          return (
            <FormRow
              disabled={disabled}
              data={item}
              key={item.key}
              onBlur={onBlur1}
              ref={(ref) => {
                formEduRef.current[index] = ref;
              }}
              eduOption={eduOption}
              degreeOption={degreeOption}
            />
          );
        })}

        {!disabled && (
          <div className="btn-box">
            <Button type="primary" onClick={onAddEdu1}>
              <Icon type="plus" />
              添加
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}

export default forwardRef(index);
