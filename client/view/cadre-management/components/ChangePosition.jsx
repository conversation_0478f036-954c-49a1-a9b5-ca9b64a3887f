import { Checkbox, Input } from "antd"; // 假设你在项目中使用 Ant Design
import { queryJobList } from "client/apis/cadre-portrait";
import { useEffect, useState } from "react";
import "./ChangePosition.less";

import TreeIcon from "../images/tree-icon.png";

const ChangePosition = ({ onChange, value, current_job = {}, form, onCheckChange }) => {
  const { getFieldDecorator } = form
  const [searchText, setSearchText] = useState("");
  const [saveOriginPosition, setSaveOriginPosition] = useState(true)
  const [sequence, setSequence] = useState("");
  const [treeData, setTreeData] = useState([]); // 假设 treeData 是你的树形数据
  const [expandedKeys, setExpandedKeys] = useState([]); // 用于存储展开的项目的状态
  const [treeSelectData, setSelectData] = useState([]);
  const [canEdit, setCanEdit] = useState(false)

  //组织树选中0 为搜索 1为 组织
  const [inputType, setInputType] = useState(0);
  const [jobName, setJobName] = useState();
  useEffect(() => {
    // 获取职位列表
    loadData();
  }, []);

  useEffect(() => {
    if (current_job && inputType === 1) {
      setJobName(current_job.current_job);
    }
  }, [current_job, inputType])

  useEffect(() => {
    if (!treeSelectData.length && value) {
      setSelectData(value);
    }
  }, [value]);

  const loadData = () => {
    // 加载数据
    queryJobList({
      job_name: searchText,
    }).then((res) => {
      if (res.data.code === 0) {
        setTreeData(res.data.data);
      }
    });
  };

  const onSearch = () => {
    // 处理搜索逻辑

    loadData();
  };

  const onSequence = (key) => {
    // 处理排序逻辑
  };

  const onExpand = (org) => {
    // 处理展开/折叠逻辑
    const { org_id } = org;

    const index = expandedKeys.findIndex((item) => {
      return item === org_id;
    });

    if (index === -1) {
      expandedKeys.push(org_id);

      setExpandedKeys([...expandedKeys]);
    } else {
      // 直接替换
      setExpandedKeys([org]);
    }
  };

  const isExpand = (key) => {
    // 判断是否展开的函数
    return expandedKeys.includes(key);
  };

  const onPositionActive = (org, org1) => {
    setInputType(0);

    // 处理职位激活逻辑
    const { job_id, user_id } = org;
    const index = treeSelectData.findIndex((item) => {
      return item.job_id === job_id && user_id === item.user_id;
    });

    let select = [];

    if (index === -1) {
      if (treeSelectData.length === 3) return;
      // 多选
      // treeSelectData.push(org);
      // 单选
      // treeSelectData.value = [org]
      select = [{ ...org, org_id: org1.org_id }];

      onCheckChange(saveOriginPosition)

      setCanEdit(false)

      setJobName(org.job_name)
      // setSelectData([org]);
    } else {
      setJobName("")

      select = [];
    }

    setSelectData(select);

    onChange && onChange(select);
  };

  const onOrgActive = (org) => {
    console.log("🚀 ~ org:", org);
    const index = treeSelectData.findIndex((_item) => {
      return org.org_id === _item.org_id && !_item.user_id && !_item.job_id;
    });
  
    let select = [];
  
    if (index === -1) {
      if (treeSelectData.length === 3) return;
      select = [{ ...org, job_name: jobName || current_job.current_job }];
  
      setInputType(1);
      setCanEdit(true);
  
      onCheckChange(saveOriginPosition); // 传递布尔值
    } else {
      select = [];
      setInputType(0);
    }
  
    setSelectData(select);
    onChange && onChange(select);
  };

  const isTreeSelect = (item1) => {
    // 判断是否选中的函数
    return treeSelectData.some((item) => {
      if (item1.user_id) {
        return item1.job_id === item.job_id && item1.user_id === item.user_id;
      } else if (item1.job_id) {
        return item1.job_id === item.job_id;
      } else if (item1.org_id) {
        return item1.org_id === item.org_id;
      }
    });
  };
  const onJobInput = (_value) => {
    setJobName(_value);
    console.log(value)
    onChange && onChange([{ ...(value[0] || {}), job_name: _value }]);
  }
  // 保留原职务。现任职务填充
  const onSetCurrentJob = (e) => {
    setSaveOriginPosition(e.target.checked);
    onCheckChange(e.target.checked);
  };
  return (
    <div className="position-content">
      <Input className="pc-job-name" disabled={!canEdit} value={jobName} onChange={(e) => onJobInput(e.target.value)} />
      {
        inputType === 1 && <div className="check-box-job">
          <Checkbox onChange={onSetCurrentJob} checked={saveOriginPosition} /> &nbsp;原职务保留 <span className="remark">（注：如不保留原职务，则去掉勾选!）</span>
        </div>
      }
      <div className="pc-main-box">
        <div className="search-box-change">
          <Input
            className="search-input"
            placeholder="请输入关键字"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<span className="search-icon"></span>}
            suffix={
              <span className="search-text" onClick={onSearch}>
                搜索
              </span>
            }
          />
        </div>
        {/* <div className="check-box m-top-12">
        {sequenceOption.map((item) => (
          <div
            className={`check-item ${
              item.key === sequence ? "check-item-active" : ""
            }`}
            onClick={() => onSequence(item.key)}
            key={item.key}
          >
            {item.label}
          </div>
        ))}
      </div> */}
        <div className="select-box m-top-18">
          <div className="tree-list">
            {treeData.map((item, index) => (
              <div className="tree-item" key={index}>
                <div className="tree-header" onClick={() => onExpand(item)}>
                  <div className="left-box">
                    <img className="sub-org-icon" src={TreeIcon} />
                    <span className={`org-name ${isTreeSelect(item) ? "org-name-active" : ""}`}>{item.org_name}</span>
                  </div>
                  <div className="right-box" onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={isTreeSelect(item)}
                      onClick={() => onOrgActive(item)}
                    />
                  </div>
                </div>
                <div
                  className="sub-org-list"
                  style={{ display: isExpand(item.org_id) ? "block" : "none" }}
                >
                  {item.job_list.map((item1, index) => (
                    <div className="sub-item" key={index}>
                      <div
                        className={`position-box ${isTreeSelect(item1) ? "position_box_active" : ""
                          }`}
                      >
                        <div className="pos" title={item1.job_name}>{item1.job_name}</div>
                        <div className="line"></div>
                        <div className="name">{item1.user_name}</div>
                      </div>
                      <div className="check-b">
                        <Checkbox
                          checked={isTreeSelect(item1)}
                          onClick={() => onPositionActive(item1, item)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePosition;
