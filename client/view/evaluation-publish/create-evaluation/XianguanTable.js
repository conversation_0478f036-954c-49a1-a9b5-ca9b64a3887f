import DropText from "./components/DropText";
import { EditableFormRow, EditableCell } from "./components/EditableRow";

const { message } = Ant;

export default function XianguanTable(props) {
  const { type, dataSource, onUpdateData } = props;

  const options =
    type === "8"
      ? [
          { label: "县管正职", key: 1 },
          { label: "县管副职", key: 2 },
          { label: "改非干部", key: 3 },
          { label: "新提拔中层干部", key: 4 },
        ]
      : [{ label: "中层干部", key: 5 }];

  const columns = [
    {
      title: "测评序号",
      dataIndex: "seq",
      key: "seq",
      align: "center",
      editable: true,
    },
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
    },
    {
      title: "测评职务类别",
      dataIndex: "user_type",
      key: "user_type",
      render(t, record) {
        return (
          <DropText
            value={t}
            option={options}
            onChange={(key) => {
              const data = [...dataSource];
              const _index = data.findIndex(
                (item) => item.user_id === record.user_id
              );
              if (_index !== -1) data[_index].user_type = key;
              if (onUpdateData) onUpdateData(data);
            }}
          />
        );
      },
    },
    {
      title: "测评职务",
      dataIndex: "job",
      key: "job",
      editable: true,
    },
    {
      title: "人员分组",
      dataIndex: "group",
      key: "group",
      render(t, record) {
        const option = [
          { label: "第1组", key: 1 },
          { label: "第2组", key: 2 },
          { label: "第3组", key: 3 },
          { label: "第4组", key: 4 },
          { label: "第5组", key: 5 },
        ];
        return (
          <DropText
            value={t}
            option={option}
            onChange={(key) => {
              const data = [...dataSource];
              const _index = data.findIndex(
                (item) => item.user_id === record.user_id
              );
              if (_index !== -1) data[_index].group = key;
              if (onUpdateData) onUpdateData(data);
            }}
          />
        );
      },
    },
    {
      title: (
        <Ant.Row type="flex" align="middle">
          <div style={{ marginRight: 10 }}>测评开关</div>
          <Ant.Switch
            defaultChecked
            size="small"
            onChange={(checked) => {
              const data = [...dataSource];
              data.forEach((item) => {
                item.enable = checked ? 1 : 2;
              });
              if (onUpdateData) onUpdateData(data);
            }}
          />
        </Ant.Row>
      ),
      dataIndex: "enable",
      key: "enable",
      render: (t, r) => {
        return (
          <Ant.Switch
            size="small"
            checked={t === 1}
            onChange={(checked) => {
              const data = [...dataSource];
              const _index = data.findIndex(
                (item) => item.user_id === r.user_id
              );
              if (_index !== -1) data[_index].enable = checked ? 1 : 2;
              if (onUpdateData) onUpdateData(data);
            }}
          />
        );
      },
    },
  ].map((item) => ({ align: "center", ...item }));

  const handleSave = (row) => {
    const newData = [...dataSource];
    // if (newData.find((item) => item.seq.toString() === row.seq.toString())) {
    //   message.warning("该测评序号已存在");
    //   return;
    // }
    let _index = -1;
    // 没有user_id的是手动添加的
    if (!row.user_id) {
      _index = newData.findIndex((item) => item.seq === row.o_seq);
    } else {
      _index = newData.findIndex((item) => row.user_id === item.user_id);
    }
    // 有修改才更新
    if (
      row.seq != dataSource[_index].seq ||
      row.job !== dataSource[_index].job
    ) {
      delete row.o_seq;

      if (_index !== -1) newData.splice(_index, 1, row);
      if (onUpdateData) {
        onUpdateData(newData);
      }
    }
  };

  const newColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave: handleSave,
      }),
    };
  });

  return (
    <div>
      <Ant.Table
        bordered
        components={{
          body: {
            row: EditableFormRow,
            cell: EditableCell,
          },
        }}
        rowKey="user_id"
        columns={newColumns}
        dataSource={dataSource}
        scroll={{ y: 400 }}
        pagination={false}
      />
    </div>
  );
}
