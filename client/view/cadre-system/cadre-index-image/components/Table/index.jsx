import React, { useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import './index.less';

// 自定义块组件
const CustomBlock = ({ value }) => {
    const _value = Number(value);
    let color = '#FF9900';

    if (_value >= 80) {
        color = '#60CA71';
    } else if (_value < 80 && _value >= 70) {
        color = '#FFA300';
    }

    return value ? (
        <span
            className="custom-block"
            style={{ backgroundColor: color }}
        />
    ) : null;
};

// 头像组件
const Portrait = () => {
    return <span className="custom-portrait" />;
};

const Table = ({
    columns = [],
    rowId = null,
    rowColor,
    colColor,
    showHead = true,
    dataSource = [],
    tabStyle = {},
    tdStyle = {},
    headerStyle = {},
    bodyScroll = false,
    rowClick,
    showMax = false,
    showMaxMin = false,
    filterRowNameInMaxMin,
    rowspan = false,
    expand = false,
    onSort
}) => {
    const [innerData, setInnerData] = useState([]);
    const [sortCurrent, setSortCurrent] = useState({
        currentKey: '',
        currentStatus: false
    });
    const [isExpand, setIsExpand] = useState(false);
    const [activeRow, setActiveRow] = useState(null);
    const [columnsMax, setColumnsMax] = useState({});
    const [columnsMin, setColumnsMin] = useState({});

    const bodyRef = useRef();
    const tableId = Math.random().toString().substr(2);

    useEffect(() => {
        const _data = [...dataSource];

        if (showMaxMin) {
            let __data = [..._data];

            if (filterRowNameInMaxMin) {
                const filterContainer = filterRowNameInMaxMin.split(',');
                __data = _data.filter(item => !filterContainer.includes(item.name));
            }

            const newColumnsMax = {};
            const newColumnsMin = {};

            columns.forEach(col => {
                if (col.showMax || col.showMin) {
                    const key = col.key;
                    const values = __data.map(item => Number(item[key]));
                    const max = Math.max(...values);
                    const min = Math.min(...values);

                    __data.forEach(item => {
                        if (col.showMax && Number(item[key]) === max) {
                            newColumnsMax[key] = item.name;
                        } else if (col.showMin && Number(item[key]) === min) {
                            newColumnsMin[key] = item.name;
                        }
                    });
                }
            });

            setColumnsMax(newColumnsMax);
            setColumnsMin(newColumnsMin);
        }

        setInnerData(_data);
    }, [dataSource, showMaxMin, columns, filterRowNameInMaxMin]);

    const handleSort = (key) => {
        onSort && onSort(key);

        setSortCurrent(prev => ({
            currentKey: key,
            currentStatus: key !== prev.currentKey ? true : !prev.currentStatus
        }));

        const newSortStatus = key !== sortCurrent.currentKey ? true : !sortCurrent.currentStatus;
        setInnerData(sortDataByKey(key, newSortStatus));
    };

    const sortDataByKey = (key, sortType) => {
        let tempList = [];
        let tempData = [...innerData];

        if (filterRowNameInMaxMin) {
            const filterList = filterRowNameInMaxMin.split(',');
            tempData = tempData.filter(item => {
                if (filterList.includes(item.name)) {
                    tempList.push(item);
                    return false;
                }
                return true;
            });
        }

        tempData.sort((a, b) => {
            return sortType ? a[key] - b[key] : b[key] - a[key];
        });

        return tempData.concat(tempList);
    };

    const scrollToElementById = (id) => {
        const dataId = `tr[data-id="${tableId}_${id}"]`;
        const element = document.querySelector(dataId);

        if (element && bodyRef.current) {
            bodyRef.current.scrollTo(0, element.offsetTop);
        } else {
            bodyRef.current && bodyRef.current.scrollTo(0, 0);
        }
    };

    const rowHighLight = (id) => {
        setActiveRow(id);
    };

    return (
        <div className="table-box">
            {showHead && (
                <div>
                    <table cellPadding="0">
                        <thead>
                            <tr>
                                {columns.map(item => (
                                    <td
                                        key={item.key}
                                        className="header-td"
                                        style={{
                                            width: item.width,
                                            textAlign: item.align,
                                            ...headerStyle
                                        }}
                                    >
                                        <div className="table-header-td" style={{ justifyContent: 'center', width: '100%' }}>
                                            <span>{item.title}</span>
                                            {item.sort && (
                                                <span
                                                    className={classNames(
                                                        'sort-icon',
                                                        sortCurrent.currentKey === item.key &&
                                                        (sortCurrent.currentStatus ? 'sort-active-top' : 'sort-active-bottom')
                                                    )}
                                                    onClick={() => handleSort(item.key)}
                                                />
                                            )}
                                        </div>
                                    </td>
                                ))}
                            </tr>
                        </thead>
                    </table>
                </div>
            )}

            {(!expand || isExpand) && (
                <div
                    className={classNames('table-body', { 'body-scroll': bodyScroll })}
                    ref={bodyRef}
                >
                    <table style={tabStyle}>
                        <colgroup>
                            {columns.map(row => (
                                <col
                                    key={row.key}
                                    style={{
                                        width: row.width,
                                        textAlign: row.align || 'center'
                                    }}
                                />
                            ))}
                        </colgroup>
                        <tbody>
                            {innerData.map((item, index) => (
                                <tr
                                    key={index}
                                    data-index={index}
                                    data-id={rowId ? `${tableId}_${item[rowId]}` : ''}
                                    className={rowId && item[rowId] === activeRow ? 'active' : ''}
                                    onClick={() => rowClick && rowClick(item, index)}
                                >
                                    {columns.map(col => (
                                        <td
                                            key={col.key}
                                            className={col.colClass || ''}
                                            onClick={(e) => col.colClick && col.colClick(item, e)}
                                            style={{
                                                textAlign: col.align || 'center',
                                                height: col.height,
                                                ...tdStyle
                                            }}
                                            rowSpan={col.rowSpan}
                                        >
                                            {col.customization ? (
                                                <CustomBlock value={item[col.key]} />
                                            ) : (
                                                <span
                                                    style={{
                                                        color: (col.color && col.color(item[col.key])) ||
                                                            (rowColor && rowColor(item, index)) || ''
                                                    }}
                                                    className={classNames('table-body-td', {
                                                        'table-column-max': showMaxMin && columnsMax[col.key] === item.name,
                                                        'table-column-min': showMaxMin && columnsMin[col.key] === item.name
                                                    })}
                                                >
                                                    {item[col.key]}
                                                </span>
                                            )}
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                    {dataSource && !dataSource.length && (
                        <p className="table-box-body-nodata">暂无数据</p>
                    )}
                </div>
            )}

            {expand && (
                <div className="expand-click" onClick={() => setIsExpand(!isExpand)}>
                    <span className="expand-text">
                        点击{!isExpand ? '展开' : '收起'}
                    </span>
                    <span className={classNames('expand-icon', { 'rotate': isExpand })} />
                </div>
            )}
        </div>
    );
};

export default Table;