import React, { Component } from "react";
import {
  Modal,
  Form,
  Button,
  Input,
  TreeSelect,
  Upload,
  Checkbox,
  Radio,
} from "antd";
import Clip from "components/clip/clipping";
import { uploadBase64File } from "apis/file";
import ImageUploader from "client/view/questionnaire-new/component/imageUploader";
import { fetch } from "tool/axios";
import { CDN } from "apis/config";
import FileUpload from "client/components/file-upload";

const FormItem = Form.Item;
const formLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 13,
  },
};

class ModalView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      clipVisible: false,
    };
  }

  render() {
    const props = this.props;
    // console.log(props);
    const {
      modalVisible,
      modalLoading,
      closeHandler,
      submitHandler,
      treeData,
      baseNode,
      currentNode,
      currentNodeName,
      form,
      editModal,
      img_url,
      is_default,
    } = props;
    // console.log(form);
    const {
      getFieldDecorator,
      resetFields,
      validateFields,
      setFieldsValue,
      getFieldValue,
    } = form;
    const ModalProps = {
      visible: modalVisible,
      wrapClassName: "modal-view-wrapper",
      getContainer: () => {
        return document.getElementById("modal-view-container");
      },
      width: 500,
      footer: null,
      destroyOnClose: true,
      maskClosable: false,
      title: editModal ? "编辑栏目" : "新建栏目",
      onCancel() {
        closeHandler();
      },
      afterClose() {},
    };
    const parseTreeData = (dataSource, disabled) => {
      let targetSource = [];
      if (Array.isArray(dataSource) && dataSource.length !== 0) {
        targetSource = dataSource.map((value, index) => {
          value.title = value.column_name;
          value.value = String(value.column_id);
          value.key = String(value.column_id);
          // console.log(currentNode, value);
          //如果是编辑栏目，则选择父级栏目时，禁止选择本级及本级的子集栏目
          if (editModal) {
            if (disabled) {
              value.disabled = disabled;
            } else {
              value.disabled = currentNode
                ? currentNode.news_column_id === value.news_column_id
                : false;
            }
          }
          if (Array.isArray(value.children) && value.children.length !== 0) {
            value.children = parseTreeData(value.children, value.disabled);
          }
          return value;
        });
      }
      return targetSource;
    };
    //在外层套一个根节点
    const wrapperRoot = (treeData) => {
      return [
        {
          title: "顶级",
          value: "0",
          key: "0",
          children: treeData,
        },
      ];
    };
    const TreeSelectProps = {
      placeholder: "请选择",
      dropdownMatchSelectWidth: false,
      treeData: wrapperRoot(parseTreeData(treeData)),
      style: {
        width: "100%",
      },
      dropdownStyle: {
        maxHeight: "200px",
        minWidth: "188px",
      },
      // defaultValue: baseNode || '0',
      treeDefaultExpandedKeys: ["0"],
      // onChange(value) {
      //   console.log(value);
      // }
    };

    // console.log(baseNode)

    const onSubmit = (e) => {
      e.preventDefault();
      validateFields((error, values) => {
        if (!error) {
          submitHandler(values, editModal, currentNode);
        }
      });
    };

    const size = {
      w: 1920,
      h: 1080,
    };

    return (
      <div className="modal-view-container" id="modal-view-container">
        <Modal {...ModalProps}>
          <Form onSubmit={onSubmit}>
            <FormItem label={"上级栏目"} {...formLayout}>
              {getFieldDecorator("top_column_id", {
                rules: [{ required: true, message: "请选择上级栏目" }],
                initialValue: baseNode || "0",
              })(<TreeSelect {...TreeSelectProps} />)}
            </FormItem>
            <FormItem label={"栏目名称"} {...formLayout}>
              {getFieldDecorator("column_name", {
                rules: [
                  { required: true, message: "请输入栏目名称" },
                  { max: 10, message: "栏目名称最长10个字" },
                ],
                initialValue: currentNodeName,
              })(<Input placeholder={"请输入"} />)}
            </FormItem>

            <FormItem className="item-mt0" label={"栏目图标"} {...formLayout}>
              {getFieldDecorator("background_url", {
                initialValue: img_url.replace(/^\s*/g, ""),
              })(
                <div>
                  {getFieldValue("background_url") ? (
                    <img
                      style={{ marginRight: "8px" }}
                      width={1920/ 40}
                      height={1080/ 40}
                      src={CDN + "/" + getFieldValue("background_url")}
                      alt=""
                    />
                  ) : (
                    ""
                  )}

                  <FileUpload
                    // max={9}
                    children={<div style={{ marginRight: "10px" }}>上传图片</div>}
                    // limitSize={limitSize}
                    upType={"files"}
                    uploadedList={getFieldValue("background_url")}
                    accept={"image/*"}
                    onChange={(a, fileList) => {
                      setFieldsValue({ background_url: fileList[0].path });
                    }}
                  />

                  {!!getFieldValue("background_url") && (
                    <a
                      href="javascript:void(0)"
                      onClick={() => {
                        setFieldsValue({ background_url: " " });
                      }}
                    >
                      删除图片
                    </a>
                  )}
                </div>
              )}
            </FormItem>
            {/* extra={''/*"上级组织推送新闻会放到此默认栏目"*/}
            {/* { baseNode === '0' || getFieldValue('parent_id') === '0' ?
          <FormItem className="item-mt0" label={'默认栏目'} extra={''} {...formLayout}>
            {
              getFieldDecorator('is_default', {
                valuePropName: 'checked',
                initialValue: is_default
              })(
                <Checkbox >设为默认栏目</Checkbox>
              )
            }
          </FormItem>
        : ''} */}
            <FormItem
              className="item-mt0"
              label={"状态"}
              extra={""}
              {...formLayout}
            >
              {getFieldDecorator("column_status", {
                initialValue: 1,
              })(
                <Radio.Group>
                  <Radio value={1}>显示</Radio>
                  <Radio value={2}>隐藏</Radio>
                </Radio.Group>
              )}
            </FormItem>


            <div className="button-wrapper">
              <Button
                type={"primary"}
                htmlType={"submit"}
                loading={modalLoading}
              >
                提交
              </Button>
              <Button onClick={ModalProps.onCancel} loading={modalLoading}>
                取消
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(ModalView);
