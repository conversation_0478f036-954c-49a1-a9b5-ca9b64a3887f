/**
 * 志愿者管理-相关接口
 * http://************:8090/pages/viewpage.action?pageId=39289706
 */

import { http } from "client/tool/axios";
import { orgHost } from './config';

const MOCK_URL = orgHost;

// 志愿者组织类型code
const ORG_TYPE_CODE = 102811;

/**
 * 10630202010106-志愿者查询列表
 * @param {*} params 
 */
export const getVolunteerList = (params) => {
  return http.get(`${MOCK_URL}/volunteer/user/query-volunteer-list`, params);
};

/**
 * 10630202010101-后台添加志愿者
 * @param {*} id 
 * @param {*} params 
 */
export const addVolunteer = (params) => {
  return http.post(`${MOCK_URL}/volunteer/user/add`, params);
};

/**
 * 10630202010102-查看志愿者信息
 * @param {*} user_id 志愿者id
 */
export const getVolunteerDetails = (user_id) => {
  return http.get(`${MOCK_URL}/volunteer/user/find-by-id`, { user_id });
};



/**
 * 搜索志愿者团队
 * @param {*} org_name 搜索关键
 */
export const searchVolunteerTeam = (org_name) => {
  return http.get(`${MOCK_URL}/org/find-by-name`, { org_name, tree_type: 2, org_type: ORG_TYPE_CODE });
};

/**
 * 10630202010216-查询组织树
 * @param {*} org_id 
 */
export const getVolunteerTeamTree = (org_id) => {
  return http.get(`${MOCK_URL}/org/tree`, { org_id, tree_type: 2, load_root: 1, org_type: ORG_TYPE_CODE});
};

/**
 * 定位组织树组织
 * @param {*} params 
 */
export const getVolunteerTeamTreeBychildren = (params) => {
  return http.get(`${MOCK_URL}/org/locate`, Object.assign(params, { org_type: ORG_TYPE_CODE }));
};

/**
 * 根据组织ID查询团体信息
 * @param {*} org_id 组织id
 */
export const getTeamByOrg = (org_id) => {
  return http.get(`${MOCK_URL}/volunteer/team/find-team-by-org-id`, { org_id });
};

/**
 * 10630202010108-移除志愿者
 * @param {*} volunteer_user_id 志愿者Id
 * @param {*} volunteer_team_id 志愿者团队ID
 */
export const removeVolunteer = (user_id_list) => {
  return http.post(`${MOCK_URL}/volunteer/user/remove`, user_id_list);
};

/**
 * 106302020208-志愿者信息回填
 * @param {*} phone 手机号码
 */
export const syncVolunteerByPhone = (phone) => {
  return http.get(`${MOCK_URL}/volunteer/user/backfill`, { phone });
};