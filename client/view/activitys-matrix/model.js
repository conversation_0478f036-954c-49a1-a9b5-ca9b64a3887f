
import { getAllOrg<PERSON><PERSON>dren } from "apis/organisition";

import { matrix, pushto, pushResults, pushshow } from "apis/activity";

import { fetch, fetchList, fetchOP } from "tool/axios";

import { message } from 'antd';



export default {
    namespace: 'activitysMatrix',
    state: {
        title: '互动矩阵',
        list_type : 3,
        table_1:{
          data:[],
          page:1,
          //pages
          pagesize:10,
          total:0,
          keyword:undefined,
          type:undefined,
          status:undefined,
        },
        table_2:{
          data:[],
          page:1,
          pagesize:10,
          total:0,
          keyword:undefined,
          type:undefined,
          status:undefined
        },
        table_3:{
          data:[],
          page:1,
          pagesize:10,
          total:0,
          keyword:undefined,
          type:undefined,
          status:undefined
        },
        loading:false,  //列表读取中
        pushing:false,  //推送中
    },
    subscriptions: {
      setup({ history, dispatch }) {
        // 监听 history 变化，当进入 `/` 时触发 `load` action
        return history.listen(({ pathname }) => {

          // if (pathname === '/weixin-bind') {

            // dispatch({ type: 'setAuthorizerTree' });
          // }

        });
      },
    },
    effects: {

        * getMatrixList({ payload }, { put, select }){

          const activitysMatrix = yield select(state => state.activitysMatrix);

          const { list_type } = activitysMatrix;

          const table = activitysMatrix["table_" + list_type];

          // console.log( table, list_type);

          yield put({type:'save', payload:{loading:true}})

          const { page=1, pagesize=10, keyword, type, status } = table;


          const res = yield matrix({ list_type, page, pagesize,
            keyword: keyword==="" ? undefined : keyword,
            type: type==="0" ? undefined : type,
            status: status==="0" ? undefined : status
          });

          const list = fetchList(res);

          if(list){

            const { data, pageNum: new_page, pageSize: new_pagesize, total } = list;

            let new_tables = {
              loading : false
            };

            new_tables["table_"+list_type] = {
              data,
              keyword,
              type,
              status,
              page: new_page,
              pagesize: new_pagesize,
              total
            }
            // console.log("list::", new_tables)

            yield put({
                type: 'save',
                payload: new_tables
            });

            return data;
          }
        },

        * pushResults({ payload }, { put, select }){

          const {activity_id, page=1, pagesize=10} = payload;

          const res = yield pushResults({
            activity_id,
            page,
            pagesize
          })

          return fetchList(res);
        },

        * pushto({ payload }, { put, select }){

          yield put({
              type: 'save',
              payload: {pushing: true}
          });

          const res = yield pushto(payload);
          const result = res.data;
          // fetchOP(res);
          yield put({
              type: 'save',
              payload: {pushing: false}
          });
          // return res
            if(result.code != 0){
                return message.error(result.message);
            }

          yield put({type: "getMatrixList"});
        },

        * show({ payload }, { put, select }){

          const res = yield pushshow(payload)

          return res;
          // return fetchOP(res);
        }

    },
    reducers: {
        save(state, { payload }) {
            return { ...state, ...payload };
        },
        update(state, { payload }) {
            return { ...state, ...payload };
        }
    }
}
