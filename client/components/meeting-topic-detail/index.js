import React from "react";
import SearchHeader from "components/search-header";
import ShowUploadFileType from "components/show-upload-file-type";
import "./style.less";
import { Form, Modal, Radio, Checkbox, Table } from "antd";

const RadioGroup = Radio.Group;
const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;

/**
 * 
 * @param visible boolean
 * 
 * @param  
 *  
 */

const MeetTopicDetail = (props) => {
  // console.log(props);
  const { history } = props;
  const {
    visible,
    cancel,
    name,
    description,
    files,
    contents,
    start_time,
    end_time,
    orgs
  } = props;

  const formItemLayout = {
    labelCol: {
      span: 3,
    },
    wrapperCol: {
      span: 20,
    }
  }
  const radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px',
  };

  const formItemStyle = {
    margin: 0
  }

  const columns = [
    {
      title: "序号",
      align: "center",
      dataIndex: "index",
      render: (val, record, index) => (index + 1)
    },
    {
      title: "组织名称",
      dataIndex: "org_name",
      align: "center"
    },
    {
      title: "完成情况",
      dataIndex: "status",
      align: "center",
      render: (val, record, index) => {
        return val === 1 ? "未完成" : val === 2 ? "已完成" : val === 3 ? "逾期未完成" : "-";
      }
    }
  ];

  // 执行组织, 表格属性
  const excuteTypeProps = {
    columns,
    dataSource: orgs,
    pagination: false,
    bordered: true,
    rowKey: record => record.topic_org_id
  }

  // 议题内容渲染方法
  const contentRender = (data) => {
    if (data && data.length > 0) {
      return data.map((item, index) => {
        // console.log(item);
        if (item.type === 1) {
          return (
            <div
              key={index}
              style={{
                // marginTop: "-3.5px"
              }}>
              <p
                style={{
                  fontSize: "16px",
                  marginBottom: "0px"
                }}
              >{`${(index + 1)}.填写内容：${item.name}`}</p>
              <div
                style={{
                  paddingLeft: "15px",
                  fontSize: "14px",
                }}
              >
                <p
                  style={{
                    marginBottom: "0px"
                  }}
                >{item.description}</p>
                <div
                  style={{
                    overflow: "hidden",
                    display: `${item.files.length > 0 ? "block" : "none"}`
                  }}
                >
                  <span
                    style={{
                      color: "#999",
                      fontSize: "14px",
                      float: "left",
                    }}
                  >附件: &nbsp;</span>
                  <div style={{
                    float: "left"
                  }}>
                    <ShowUploadFileType loading={false} data={item.files} />
                  </div>
                </div>
              </div>
            </div>

          )
        } else if (item.type === 2) {
          return (
            <div key={index}>
              <p
                style={{
                  fontSize: "16px"
                }}
              >{`${(index + 1)}.单选：${item.name}`}</p>
              <p style={{
                paddingLeft: "15px"
              }}>
                <RadioGroup disabled value={item.answer[0]}>
                  {item.opts.map(_item => {
                    return (
                      <Radio style={radioStyle} value={_item.opts_id}>{_item.opts_name}</Radio>
                    )
                  })}
                </RadioGroup>
              </p>
            </div>
          )
        } else {
          const option = []
          if (item.opts.length > 0) {
            item.opts.map(_item => {
              option.push({
                label: _item.opts_name,
                value: _item.seq
              })
            })

          }
          return (
            <div key={index}>
              <p
                style={{
                  fontSize: "16px"
                }}
              >{`${(index + 1)}.多选：${item.name}`}</p>
              <p style={{
                paddingLeft: "15px"
              }}>
                {/* options={option} */}
                <CheckboxGroup disabled value={item.answer}>
                  {item.opts.map(_item => {
                    return (
                      <Checkbox className="checkbox-item-style" value={_item.opts_id}>{_item.opts_name}</Checkbox>
                    )
                  })}
                </CheckboxGroup>
              </p>
            </div>
          )
        }
      })
    } else {
      return (null)
    }
  }
  return (
    <div className="MeetTopicDetail">
      <Modal
        visible={visible}
        // title="会议议题详情"
        title="工作任务详情"
        width={1124}
        footer={null}
        className="modalContent MeetTopicDetail"
        onCancel={cancel}
      >
        <div
          style={{
            height: "600px",
            overflow: "auto"
          }}
        >
          <Form>
            <FormItem
              wrapperCol={{
                span: 17,
                offset: 1
              }}
            >
              <p
                style={{
                  fontSize: "20px",
                  margin: 0,
                  color: "#000",
                  fontWeight: "bold",
                  fontFamilay: "微软雅黑"
                }}
              >{name}</p>
            </FormItem>
            <FormItem
              label="开始时间"
              {...formItemLayout}
              style={formItemStyle}
            >
              <p style={{
                // marginTop: "-3.5px"
              }}>{start_time}</p>
            </FormItem>
            <FormItem
              // label="结束时间"
              label="截止时间"
              {...formItemLayout}
              style={formItemStyle}
            >
              <p style={{
                // marginTop: "-3.5px"
              }}>{end_time}</p>
            </FormItem>
            <FormItem
              // label="议题描述"
              label="备注"
              {...formItemLayout}
              style={formItemStyle}
            >
              <p style={{
                // marginTop: "-3.5px"
              }}>{description}</p>
            </FormItem>
            <FormItem
              // label="关联附件"
              label="相关资料"
              {...formItemLayout}
            >
              <ShowUploadFileType loading={false} data={files} />
            </FormItem>
            <FormItem
              // label="议题内容"
              label="任务内容"
              {...formItemLayout}
            >
              {contentRender(contents)}
            </FormItem>
            <FormItem
              label="执行组织"
              {...formItemLayout}
              style={formItemStyle}
              className={"no-margin"}
            >
              <div>
                <p>
                  已选（{orgs ? orgs.length : 0}）
                </p>
                <Table {...excuteTypeProps} />
              </div>
            </FormItem>
          </Form>
        </div>
      </Modal>
    </div>
  )
}

export default MeetTopicDetail;