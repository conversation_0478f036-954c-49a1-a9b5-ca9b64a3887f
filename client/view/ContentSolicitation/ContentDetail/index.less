.content-detail {
  .wrap-content {
    margin: 45px 33px;

    .second-header {
      margin: 0 0 34px 33px;
      font-size: 20px;
      font-family: Source <PERSON>s CN;
      font-weight: bold;
      color: #515151;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        width: 7px;
        height: 20px;
        background: #FF4D4F;
        left: -25px;
        top: 50%;
        transform: translate(0, -50%);
      }
    }

    .approve-btn {
      display: flex;
      justify-content: center;

      .go-back-btn {
        width: 140px;
        height: 36px;
        margin-top: 106px;
        background: #4E79F4 !important;
        border-radius: 3px;
        color: #ffffff;
        border: none;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
      }
    }


    .ant-btn {
      width: 140px;
      height: 36px;
      border-radius: 3px;
      color: #ffffff;
      border: none;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
    }

    .ant-btn-primary {
      margin: 0 99px 0 102px;
      background: #1BB760;
    }

    .ant-btn-default {
      background: #F4584E;
    }

    .ant-row {
      display: flex;
    }

    .ant-form-item-label {
      min-width: 102px;

      &>label {
        margin-right: 13px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #515151;
      }
    }

    .btn {
      margin-top: 106px;
    }

    .ant-input {
      width: 612px;
      border: 1px solid #DDDDDD;
      border-radius: 4px;
    }

    .approve-record {
      margin-left: 44px;
    }
  }
}