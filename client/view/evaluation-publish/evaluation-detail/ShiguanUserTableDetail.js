import DropText from "../create-evaluation/components/DropText";
import { getRelationshipsDict } from "apis/evaluation-publish";

export default function ShiguanTable(props) {
  const { record, index, onUpdateData } = props;

  const [visible, setVisible] = React.useState(false);
  const [ticketType, setTicketType] = React.useState([]);

  const formRef = React.useRef(null);

  const handleDelete = (user) => {
    const { user_id } = user;
    const newData = record.data.filter((item) => item.user_id != user_id);
    onUpdateData({ ...record, data: newData }, index);
  };

  const handleNumChange = (value) => {
    const newData = { ...record, num: value };
    onUpdateData(newData, index);
  };
  const loadDictTypeForRela = () => {
    getRelationshipsDict({ eval_type: 10 }).then((res) => {
      if (res.data.code === 0) {
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.value,
            key: item.key,
          };
        });
        setTicketType(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  React.useEffect(() => {
    // loadData();
    // loadDictType();
    loadDictTypeForRela();
  }, []);

  const handleAdd = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        const maxSeq = record.data.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 0);
        }, 0);
        const dataSource = [...record.data];
        dataSource.push({
          ...values,
          seq: maxSeq + 1,
        });
        onUpdateData({ ...record, data: dataSource }, index);
        setVisible(false);
      } else {
        message.error("请先选择");
      }
    });
  };

  const handleSave = (row) => {
    const newData = [...record.data];
    const idx = newData.findIndex((item) => row.org_id === item.org_id);
    if (idx > -1) {
      newData[idx] = row;
    }
    onUpdateData({ ...record, data: newData }, index);
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "20%",
      title: "被测评人",
    },
    {
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      title: "干部类别",
      render: (_, record) => {
        const option1 = [
          { label: "县管正职", key: 1 },
          { label: "县管副职", key: 2 },
          { label: "改非干部", key: 3 },
          { label: "新提拔中层干部", key: 4 },
        ];
        if (![3, 4].includes(Number(_))) {
          option1.splice(2, 2);
        }
        return (
          <DropText
            disabled={true}
            value={_}
            // disabled={[3, 4].includes(Number(_))}
            disableList={[3, 4]}
            option={option1}
            onChange={(key) => {
              dataSource.forEach((item) => {
                if (item.user_id === record.user_id) {
                  item.user_type = key;
                }
              });
              onUpdateData &&
                onUpdateData({ org_id, user_list: dataSource, index });
            }}
          />
        );
      },
    },
    {
      dataIndex: "job_name",
      key: "job_name",
      editable: true,
      title: "测评职务",
    },
    {
      title: "打票类型",
      key: "action",
      width: "10%",
      align: "center",
      render: (text, record) => {
        return (
          <DropText
            value={record.relationship}
            onSelect={(value) => {
              onUpdateData &&
                onUpdateData({
                  ...record,
                  relationship: value,
                  updatetype: "relationship",
                });
            }}
            option={ticketType}
          />
        );
      },
    },
    {
      title: "测评开关",
      key: "action2",
      width: "10%",
      align: "center",
      render: (text, record) => (
        <Ant.Switch
          checked={record.enable == 1 ? true : false}
          size="small"
          onChange={(r) =>
            onUpdateData({ ...record, enable: r, updatetype: "enable" })
          }
        ></Ant.Switch>
      ),
    },
    {
      title: "一言素描",
      key: "action3",
      width: "10%",
      align: "center",
      render: (text, record) => (
        <div>
          <Ant.Switch
            checked={record.sketch == 1 ? true : false}
            size="small"
            onChange={(r) =>
              onUpdateData({
                ...record,
                sketch: r,
                updatetype: "sketch",
              })
            }
          ></Ant.Switch>
        </div>
      ),
    },
  ];

  const newColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      // onCell: (record) => ({
      //   record,
      //   editable: col.editable,
      //   dataIndex: col.dataIndex,
      //   title: col.title,
      //   handleSave,
      // }),
    };
  });

  return (
    <div style={{ marginBottom: 20 }}>
      <Ant.Row type="flex" align="middle" style={{ marginBottom: 10 }}>
        <div style={{ fontSize: 16, fontWeight: 800 }}>
          {record.sequence_name}
        </div>
        <div style={{ marginLeft: 52, marginRight: 20 }}>
          好等次个数：
          <Ant.InputNumber
            value={record.num}
            precision={0}
            size="small"
            onChange={handleNumChange}
          />
        </div>
      </Ant.Row>
      <Ant.Table
        bordered
        // components={{
        //   body: {
        //     row: EditableFormRow,
        //     cell: EditableCell,
        //   },
        // }}
        columns={newColumns}
        dataSource={record.data}
        scroll={{ y: 300 }}
        pagination={false}
      />
    </div>
  );
}
