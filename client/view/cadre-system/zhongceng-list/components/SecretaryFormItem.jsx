import React, { PureComponent } from 'react';
import { Button, Form, message, Table, Popconfirm, Modal  } from 'antd';
import MemberModal from './MemberModal';
import { addCommitteeMemberUrl, deleteCommitteeMemberUrl, changeCommitteeMemberUrl } from "../../../apis/party-organization";
import { ExclamationCircleOutlined } from '@ant-design/icons';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 9 },
};

/**
 * 书记表单项
 */
export default class SecretaryFormItem extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      currentMember: {},
      editIndex: -1,
    };
    this.onBtnClick = this.onBtnClick.bind(this);
    this.onSure = this.onSure.bind(this);
    this.columns = [
      {
        title: '序号',
        dataIndex: 'index',
        align: 'center',
        width: '10%',
        render: (text, record, index) => <span>{index + 1}</span>,
      },
      {
        title: '姓名',
        dataIndex: 'user_name',
        align: 'center',
        width: 120,
      },
      {
        title: '行政职务',
        dataIndex: 'grade_name',
        align: 'center',
      },
      {
        title: '党内职务',
        dataIndex: 'position_name',
        align: 'center',
      },
      {
        title: '操作',
        key: 'action',
        align: 'center',
        width: '150px',
        render: (text, record, index) => {
          const { operation_type } = this.props;
          return (
            <span>
              <Button type="link" disabled={operation_type === 3} onClick={() => this.handleEdit(record, index)}>
                编辑{' '}
              </Button>
              <Popconfirm
                disabled={operation_type === 3}
                title={this.props.operation_type == 2 ? '当前状态确认后无法撤销!' : '是否删除？'}
                onConfirm={() => this.handleDel(index, record)}>
                <Button type="link" disabled={operation_type === 3}>删除</Button>
              </Popconfirm>
            </span>
          );
        },
      },
    ];
  }
  onBtnClick() {
    this.setState({ visible: true, currentMember: {}, editIndex: -1 });
  }
  handleEdit(row, index) {
    this.setState({
      currentMember: row,
      editIndex: index,
      visible: true,
    });
  }
  handleDel(index, record) {
    const { operation_type, onChange, prop, value, onRefresh, required, title } = this.props;
    if (operation_type === 1) {
      const newList = [...value];
      newList.splice(index, 1);
      onChange && onChange(newList, prop);
    } else {
      // if (required && value.length <= 1) {
      //   Modal.error({
      //     title: `删除失败，至少添加一个${title}`,
      //     content: `可先添加一个${title}后再删除`,
      //   });
      //   return;
      // }
      deleteCommitteeMemberUrl(record.period_member_id).then((res) => {
        const {  code, status, } = res.data;
        if (status === 200 && code === 0) {
          message.success("操作成功！");
          onRefresh && onRefresh();
        } else {
          message.error(res.data.message || "请求失败，请联系管理员！");
        }
      });
    }
  }
  onSure(data) {
    const { operation_type, onChange, prop, value, period_id, onRefresh } = this.props;
    const { editIndex, currentMember } = this.state;
    if (operation_type === 1) {
      // 新增
      const newList = [...value];
      for (let index = 0; index < newList.length; index++) {
        const item = newList[index];
        if (item.user_id === data.user_id && currentMember.user_id !== item.user_id) {
          message.error('当前人员已存在，请重新选择');
          return;
        }
      }
      if (editIndex === -1) {
        newList.push(data);
      } else {
        newList[editIndex] = data;
      }
      onChange && onChange(newList, prop);
      this.setState({ visible: false });
    } else {
      // 编辑情况
      const reqFn = editIndex === -1 ? addCommitteeMemberUrl : changeCommitteeMemberUrl;

      const params = {period_id, period_member_id: currentMember.period_member_id, ...data};
      if (editIndex === -1) {

      } else {
        delete params.period_id;
        delete params.user_name;
      }

      reqFn(params).then((res) => {
        const {  code, status, } = res.data;
        if (status === 200 && code === 0) {
          onRefresh && onRefresh();
          message.success("操作成功！");
          this.setState({ visible: false });
        } else {
          message.error(res.data.message || "请求失败，请联系管理员！");
        }
      });
    }
  }
  render() {
    // operation_type   1 添加 2 编辑 3 详情
    const { visible, currentMember } = this.state;
    const { filter, operation_type, value = [], title } = this.props;
    return (
      <React.Fragment>
        {
          operation_type !== 3 && (
            <Button type='link' onClick={this.onBtnClick} style={{padding: 0}}>
              选择人员
            </Button>
          )
        }
        {
          operation_type === 3 && value.length === 0 && "暂无"
        }
        {value && value.length > 0 && (
          <Table rowKey='user_id' bordered columns={this.columns} dataSource={value} pagination={false} />
        )}
        <MemberModal
          operation_type={operation_type}
          filter={filter}
          visible={visible}
          onCancel={() => this.setState({ visible: false })}
          onSure={this.onSure}
          defaultValue={currentMember}
          title={title}
        />
      </React.Fragment>
    );
  }
}
