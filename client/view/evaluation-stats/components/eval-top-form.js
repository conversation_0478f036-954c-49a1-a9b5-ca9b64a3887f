import React from "react";
import "./index.less";
import {Form, DatePicker, Button, Input} from "antd";
import moment from "moment";

const {RangePicker} = DatePicker;
const FormItem = Form.Item

const
    EvalTopForm = ({form, queryFields = [], queryHandler, handleExport}) => {
        const {validateFields, getFieldDecorator, resetFields} = form;
        const renderFormItem = () => {
            return queryFields.map((item, index) => {
                switch (item.type) {
                    case "RangePicker":
                        return (
                            <FormItem key={index} label={item.label}>
                                {
                                    getFieldDecorator(item.field, {})(
                                        <RangePicker></RangePicker>
                                    )
                                }
                            </FormItem>
                        )
                    case "Input":
                        return (
                            <FormItem key={index} label={item.label}>
                                {
                                    getFieldDecorator(item.field, {})(
                                        <Input placeholder="请输入" style={{width: 200}}/>
                                    )
                                }
                            </FormItem>
                        )
                }
            })
        }
        const onSubmit = () => {
            validateFields((error, values) => {
                if (!error) {
                    queryHandler(values);
                }
            });
        }
        const handleResetFields = () => {
            resetFields();
            onSubmit()
        }
        return (
            <div className="eval-top-form-container">
                <Form layout="inline">
                    {renderFormItem()}
                    <FormItem>
                        <Button type="primary" className="handle-btn" icon="search" onClick={onSubmit}>查询</Button>
                        <Button className="handle-btn" icon="redo" onClick={handleResetFields}>重置</Button>
                        <Button className="handle-btn" onClick={handleExport}>导出</Button>
                    </FormItem>
                </Form>
            </div>
        )
    }

export default Form.create()(EvalTopForm);

