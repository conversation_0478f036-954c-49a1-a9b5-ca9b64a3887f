const DICT_MAP = {
  1: "x",
  2: "x1",
  3: "x2",
};

const DICT_AVG_MAP = {
  1: "x_avg",
  2: "x1_avg",
  3: "x2_avg",
};

const DICT_LABEL_MAP = {
  1: "能力+口碑",
  2: "能力",
  3: "口碑",
};

export const getChartOption = (data, selectValue, selectedUserId) => {
  if (!data || !data.mine) return {};

  const keyName = DICT_MAP[selectValue];
  const { mine, others } = data;
  const selfData = [mine[keyName], mine.y, mine.name, mine.user_id];
  const x_avg = data[DICT_AVG_MAP[selectValue]];

  let otherData = [...others, mine].map((item) => {
    const x = Number(item[DICT_MAP[selectValue]]);
    return [x, item.y, item.name, item.user_id];
  });

  return {
    grid: {
      top: "9%",
      left: "4%",
      right: "5%",
      bottom: "2%",
      containLabel: true,
      show: false,
    },
    tooltip: {
      show: false,
      trigger: "item",
      showDelay: 0,
      formatter: function (params) {
        const { value } = params;
        return `
          <div class="tooltips-box">
            <div class="data-name">${value[2]}</div>
            <div class="data-item">业绩：${value[1]}</div>
            <div class="data-item">${DICT_LABEL_MAP[selectValue]}：${value[0]}</div>
          </div>
        `;
      },
    },
    xAxis: {
      type: "value",
      axisLabel: {
        formatter: (value) => {
          value = Number(value);
          return value > 100 ? "" : value.toFixed(2);
        },
        color: "#666666",
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#EEEEEE",
          type: "dotted",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#666666",
        },
        symbol: ["none", "arrow"],
        symbolOffset: 7,
        symbolSize: [7, 10],
      },
      min: (value) => {
        return value ? (value.min === 0 ? 0 : value.min - 0.1) : 0;
      },
      max: (value) => {
        return value ? value.max + 0.1 : 0;
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      name: "业绩",
      type: "value",
      scale: true,
      axisLabel: {
        color: "#666666",
        formatter: (value) => Number(value).toFixed(2),
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#EEEEEE",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#666666",
        },
        symbol: ["none", "arrow"],
        symbolOffset: 7,
        symbolSize: [7, 10],
      },
      axisTick: {
        show: false,
      },
      min: (value) => {
        return value ? (value.min === 0 ? 0 : value.min - 0.1) : 0;
      },
      max: (value) => {
        return value ? value.max + 0.1 : 0;
      },
    },
    animation: false,
    series: [
      {
        type: "scatter",
        data: otherData,
        // 取消动画
        symbol: (value) => {
          return value[0] === selfData[0] && value[1] === selfData[1]
            ? "none"
            : "circle";
        },
        itemStyle: {
          color: (params) => {
            const [, , , userId] = params.data;
            return userId === selectedUserId ? "#FE533A" : "#60CA71";
          },
        },
        symbolSize: 14,
        markLine: {
          symbol: "none",
          label: { show: false },
          lineStyle: {
            color: "#FE533A",
            type: "dashed",
          },
          silent: true,
          data: [{ type: "average", name: "平均值" }, { xAxis: x_avg }],
        },
      },
      {
        type: "scatter",
        data: selfData[2] ? [selfData] : [],
        symbolSize: 14,
        itemStyle: {
          color: (params) => {
            const [, , , userId] = params.data;
            return selectedUserId === -1 || selectedUserId === selfData[3]
              ? "#FE533A"
              : "#60CA71";
          },
        },
      },
    ],
  };
};
