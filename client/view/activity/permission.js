
//活动权限配置表
//1.0.3-04-08-活动状态说明文档
//http://10.10.100.12:8090/pages/viewpage.action?pageId=16257853

//活动状态：
//1:"活动中", 2:"审批中", 3:"未通过", 4:"已结束", 5:"未开始", 6:"草稿"
//类型：
//1:"投票", 2:"问卷调查", 3:"有奖竞答", 4:"线下活动"

const defaultAccess = {permission:[1,0,1], option:{edit:''}};


//线下活动	草稿	需要审 批	√	√	√	可编辑所有，不发起审批

// 活动状态 报名状态 是否需要审批    权限：查看 编辑 删除    备注：edit: *编辑所有  +ticket仅加票  -approval不能修改审批
const physical = [
  {key:[6,undefined,1], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[6,undefined,0], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[2,undefined,0], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[1,0,1], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[1,0,0], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[1,1,1], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[1,1,0], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[1,2,0], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[1,2,1], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[5,0,1], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[5,0,0], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[5,1,0], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[5,1,1], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[5,2,0], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[5,2,1], access:{permission:[1,0,1], option:{edit:'+ticket'}}},
  {key:[3,undefined,1], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[4,undefined,1], access:{permission:[1,0,1], option:{edit:''}}},
  {key:[4,undefined,0], access:{permission:[1,0,1], option:{edit:''}}}
];

//除线下活动外的其它活动统一规则
const activity = [
  {key:[6,undefined,1], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[6,undefined,0], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[2,undefined,1], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[1,undefined,1], access:{permission:[1,0,1], option:{edit:''}}},
  {key:[1,undefined,0], access:{permission:[1,0,1], option:{edit:''}}},
  {key:[5,undefined,1], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[5,undefined,0], access:{permission:[1,1,1], option:{edit:'*'}}},
  {key:[3,undefined,1], access:{permission:[1,1,1], option:{edit:'-approval'}}},
  {key:[4,undefined,1], access:{permission:[1,0,1], option:{edit:''}}},
  {key:[4,undefined,0], access:{permission:[1,0,1], option:{edit:''}}},
];


const getAccess = (type, status, approve, sign)=>{
  const dict = parseInt(type)===4 ? physical : activity;
  status = parseInt(status);
  approve = approve ? 1 : 0;
  // sign   = sign===undefined ? undefined : sign;
  const condition = [status,sign,approve].map(function(t){return t===undefined ? '' : t}).join('|');

  console.log('find permission:', condition);
  const res = dict.find((rule)=>{
    const dictKey = rule.key.map(function(t){return t===undefined ? '' : t}).join('|');
    // console.log("permissionKey:",condition, '=>', dictKey);

    return dictKey === condition;
  })
  return res ? res.access : defaultAccess;
}


export const getPhysicalAccess = (status, approve, sign) => {
  return getAccess(4, status, approve, sign)
}

export const getActivityAccess = (status, approve) => {
  return getAccess(1, status, approve) //1,2,3（1:"投票", 2:"问卷调查", 3:"有奖竞答"）规则一样
}

// export default  {
//   //线下活动
//   physical: physical,
//   //除线下活动外的活动统一规则
//   activity: activity,
// }
