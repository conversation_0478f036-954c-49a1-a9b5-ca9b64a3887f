import { activityHost, ucHost, workflowHost } from './config';
import { http, headers, filterParamsValue } from 'client/tool/axios';
import qs from 'querystring';

export const testActivity = () => {
    console.log(activityHost);
};

export const getActivity = (activityArr) => {
    // console.log(activityArr);
    return http.get(`${activityHost}/activity/get`, { id: activityArr });
};
export const loadActivityList = (page = 1) => {
    return http.get(`${activityHost}/column/find-all`, { page });
};
//插入一条活动栏目
export const insertActivity = (postObject) => {
    return http.post(`${activityHost}/column/insert`, postObject);
};
//获取编辑栏目的信息
export const fetchEditColumn = (activityId) => {
    return http.get(`${activityHost}/column/find-id/${activityId}`);
};
//编辑一个栏目
export const editActivity = (postObject) => {
    return http.post(`${activityHost}/column/update`, postObject);
};
//删除一个栏目
export const deleteActivity = (activityId) => {
    return http.delete(`${activityHost}/column/remove/${activityId}`);
};

//加载全部活动管理页面列表
export const loadAllActivity = (params = { page }) => {
    return http.get(`${activityHost}/activity/find-condition`, filterParamsValue(params));
};
//加载活动详情页面多维度统计表格数据
export const fetchDetail = (activity_type, activity_id, org_id) => {
    return http.get(`${activityHost}/activity/count/${activity_type}/${activity_id}`, {aid: activity_id, org_id});
};
//加载活动详情页面总体统计数据
export const fetchCount = (activity_type, activity_id, org_id) => {
    return http.get(`${activityHost}/activity/count/all`, { aid: activity_id, type: activity_type, org_id })
};
//103100020105 PC端捐赠结果查询
export const countDonateDetail = (aid, page=1,pagesize=20) => {
    return http.get(`${activityHost}/activity/count/donate/${aid}/${page}/${pagesize}`)
}
//下载活动结果：返回url地址
export const downloadResult = (activity_type, activity_id) => {
    const path = `/ac/activity/count/export/${activity_type}/${activity_id}`;
    // return `${activityHost}/activity/count/export/${activity_type}/${activity_id}`;
    return path;
};
//查询活动
export const searchActivity = ({ page, status, type, cid, timepoint, pm, keyword }) => {
    return http.get(`${activityHost}/activity/find-condition`, { page, status, type, cid, timepoint, pm, keyword });
};
// 主题推送查询活动
export const pushMsgSearchActivity = ({ page, status, type, cid, timepoint, pm, keyword,search_tag }) => {
    if(status.indexOf(',')>-1){
        const statuses = status.split(',').map((item)=>`status=${item}`)
        return http.get(`${activityHost}/activity/find-condition?${statuses.join('&')}`, { page, type, cid, timepoint, pm, keyword,search_tag });
    }else{
        return http.get(`${activityHost}/activity/find-condition`, { page, status, type, cid, timepoint, pm, keyword,search_tag });
    }
};
//获取活动列表
export const getColumn = () => {
    return http.get(`${activityHost}/column/list`);
};
//查询栏目列表,新增参数传入oid
export const fetchColumn = (pageNum = 1, pageSize = 10) => {
    const { _oid } = headers();
    return http.get(`${activityHost}/column/find-all`, { page: pageNum, pagesize: pageSize, oid: _oid });
};
//删除活动
export const deleteActive = ({ id = '', type = '' }) => {
    return http.delete(`${activityHost}/activity/delete/${id}/${type}`);
};
//下载问答答案：返回url地址
export const downAnswer = (download_aid, download_opts_id) => {
    const queryParams = {
        aid: download_aid,
        oid: download_opts_id,
    }
    return `/ac/activity/count/download/ans?${qs.stringify(queryParams)}`;
    // return `${activityHost}/activity/count/download/ans?aid=${download_aid}&oid=${download_opts_id}`;
};
//获取工作流程,获取全部的默认审批流程模板
export const fetchWorkflowList = (name = '', page = 1, pagesize = 999) => {
    return http.get(`${workflowHost}/type/list`, { name, page, pagesize });
};
//根据ID获取工作流程用户列表和抄送列表
export const fetchWorkflowItemInfo = (workflowId = '') => {
    return http.get(`${workflowHost}/type/get/${workflowId}`);
};

//奖励物品查询
export const requestPrizeList = (page, pageSize) => {
    return http.get(`${activityHost}/prize/find-all`, { page, pagesize: pageSize });
};
//移动活动到其他栏目
export const moveActive = ({ aids, cid }) => {
    return http.post(`${activityHost}/activity/move`, { aids, cid });
};

//获取中奖列表
export const loadWinnerList = ({ param, page, oid = '', source = 0 }) => {
    const { _oid } = headers();
    return http.get(`${activityHost}/prize/winner/list`, { param, page, oid: _oid, source });
};

//新增工作流程
export const workflowAdd = (postObject) => {
    return http.post(`${workflowHost}/type/add`, postObject);
};


//103000701 发布活动-保存草稿
//http://************:8090/pages/viewpage.action?pageId=13009005
export const activityDraft = (param) => {
    return http.post(`${activityHost}/activity/draft`, filterParamsValue(param));
}
//103000702-查看草稿
//http://************:8090/pages/viewpage.action?pageId=13009051
export const getDraft = (param) => {
    const { draft_id } = param;
    return http.get(`${activityHost}/activity/draft/${draft_id}`, param);
}
//103000703-删除草稿
//http://************:8090/pages/viewpage.action?pageId=13009116
export const deleteDraft = (param) => {
    const { draft_id } = param;
    return http.delete(`${activityHost}/activity/draft/${draft_id}`, param);
}

//发布投票
export const newVoteRelease = (param) => {
    return http.post(`${activityHost}/vote/insert`, param);
}
//修改投票
export const newVoteEdit = (param) => {
    return http.post(`${activityHost}/vote/update`, param);
}
// 发起问卷调查
export const newQuestionnaireRelease = (param) => {
    return http.post(`${activityHost}/questionnaire/insert`, param);
}
// 修改问卷调查
export const newQuestionnaireReleaseEdit = (param) => {
    return http.post(`${activityHost}/questionnaire/update`, param);
}
// 发起有奖竞答
export const newContest = (param) => {
    return http.post(`${activityHost}/giveaway/insert`, param);
}
// 修改有奖竞答
export const newContestEdit = (param) => {
    return http.post(`${activityHost}/giveaway/update`, param);
}
// 有奖竞答查询
export const GetContest = (id) => {
    return http.get(`${activityHost}/giveaway/find-id/${id}`);
}
//获取投票数据
export const requestNewVote = (id) => {
    // return http.get(`${activityHost}/vote/find-id/${id}`);
    return http.get(`${activityHost}/vote/find-id/${id}`);
}
// 获取问卷调查数据
export const requestNewQuestionnaire = (id) => {
    return http.get(`${activityHost}/questionnaire/find-id/${id}`);
}

// 下载人员限制模板
export const download_model = () => {
    return http.get(`${activityHost}/activity/download-model/user-list`)
}

// 获取组织数
export const get_org = ({ id = 0 }) => {
    return http.get(`${ucHost}/org/tree`, { org_id: id, show_code: 1 })
}

// 上传参加人员范围限制
export const upUserSccope = (taskId, orgIds) => {
    const data = new FormData();
    if (taskId) {
        data.append('task_id', taskId)
    }
    data.append('type', 2)
    data.append('organization_ids', orgIds)
    return http.post(`${activityHost}/activity/limit/user-scope`, data)
}

// 参加人员限制
export const limitUser = (param) => {
    const data = {
        activity_limit_user_id: param.activity_limit_user_id,
        rule: param.editRule
    }
    return http.post(`${activityHost}/activity/limit/user`, data)
}
// 新建线下活动
export const newPhysical = (param) => {
    return http.post(`${activityHost}/offline/insert`, param);
}

// 下线活动查询
export const GetPhysical = (id) => {
    return http.get(`${activityHost}/offline/find-id/${id}`);
}
// 线下活动修改
export const newPhysicalEdit = (param) => {
    return http.post(`${activityHost}/offline/update`, param);
}

//sendNotice
export const sendNotice = (postObject = {}) => {
    return http.post(`${activityHost}/activity/notice`, postObject);
}


//103100020101PC端新增捐赠活动
export const contributeRelease = (param) => {
    return http.post(`${activityHost}/donate/insert`, param);
}
//103100020104PC修改捐赠活动
export const contributeEdit = (param) => {
    return http.post(`${activityHost}/donate/update`, param);
}
//103100020102PC端捐赠活动查看
export const getContribute = (id) => {
    return http.get(`${activityHost}/donate/find-id/${id}`);
}


//矩阵列表  103000707-活动矩阵列表
export const matrix = (param) => {
    return http.get(`${activityHost}/activity/matrix/list`, filterParamsValue(param));
}

//103000710-活动矩阵-接受列表-设置推送设置
export const activityPushsetup = (param) => {
    return http.post(`${activityHost}/activity/push/set-up`, param);
}
//103000711-活动矩阵-接受列表-获取当前推送设置
export const activityGetpushsetup = (param) => {
    return http.get(`${activityHost}/activity/push/set-up`, param);
}

//103000705-活动列表-推送活动-发起、修改
export const pushto = (param) => {
    return http.post(`${activityHost}/activity/push`, param);
}


//103000712-查看推送结果
export const pushResults = (param) => {
    const { activity_id } = param;
    return http.get(`${activityHost}/activity/push/${activity_id}`, param);
}

//103000708-活动列表/活动矩阵-接受列表-显示设置
export const pushshow = (param) => {
    // activity_push_id visibility cid
    return http.get(`${activityHost}/activity/push/show`, param);
}

//103000712-查看推送结果
export const activityPush = (id, param) => {
    return http.get(`${activityHost}/activity/push/${id}`, param)
}

// 有奖竞答/问卷调查 结果明细列表
export const activityDetailQuery = (param) => {
    return http.get(`${activityHost}/interactive/situation/list`, param)
}

// 有奖竞答/问卷调查 结果明细下载
export const acDetailDownUrl = (activity_type, activity_id) => {
        const path = `/ac/interactive/situation/export/${activity_id}/${activity_type}`;
    return path;
}

// 投票 结果明细列表
export const voteDetailQuery = (param) => {
    return http.get(`${activityHost}/activity/count/vote/detail`, param)
}

// 投票 结果明细下载
export const voteDetailDownUrl = (activity_id) => {
    const path = `/ac/activity/count/vote/detail-download?aid=${activity_id}`;
    return path;
}
