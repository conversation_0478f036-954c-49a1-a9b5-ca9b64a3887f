import React from "react";
import HeaderForm from "./HeaderForm";
import ButtonGroup from "./ButtonGroup";
import { Table } from "antd";
import "../index.less";
import propTypes from "prop-types";

const Step1 = (props) => {

  const {
    state,
    updateState,
    nextStep,
    form,
    getStep1TableData,
    formatMeetingType,
    changeShowSize,
    handleCancel
  } = props;
  const {
    step1PageIndex,
    step1PageSize,
    step1Total,
    dataSource,
    type_id,
    category_id,
    step1TableLoading,
    meetingCategoryList
  } = state;

  const currentSelecteds = state.selected || [];
  // const next = () => {
  //   updateState({
  //     selected: currentSelecteds
  //   }, nextStep);
  // }

  // 底部按钮组
  const data = [
    {
      name: "下一步",
      onClick: nextStep,
      style: {
        width: "115px",
        height: "36px",
        backgroundColor: "#F46E65",
        border: "none"
      }
    },
    {
      name: "取消",
      onClick: () => handleCancel(),
      style: {
        width: "115px",
        height: "36px",
        marginLeft: "48px"
      }
    },
  ];
  const buttonGroup = {
    data,
  };
  // -- end --

  // 头部搜索 form 属性
  const headerFormGroup = [
    {
      label: "所属类别",
      getField: "category_id",
      type: "Select",
      valueKey: "category_id",
      nameKey: "category",
      init: null,
      onChange: (val) => {
        meetingCategoryList.map(item => {
          if (item.category_id === val && item.types && item.types.length > 0) {
            updateState({
              meetingTypeList: item.types
            })
          }
          if (val === null) {
            formatMeetingType(meetingCategoryList)
          }
        })
      },
      mayBeSelect: state.meetingCategoryList
    },
    {
      // label: "会议类型",
      label: "活动类型",
      getField: "type_id",
      type: "Select",
      valueKey: "type_id",
      nameKey: "type",
      init: null,
      mayBeSelect: state.meetingTypeList
    }
  ];
  const headerFormProps = {
    form: form,
    data: headerFormGroup,
    search: (data) => {
      updateState(data, () => {
        getStep1TableData({ ...data, ...{ page_size: 9999 } })
      })
    }
  }
  // -- end --

  const columns = [
    {
      title: "序号",
      dataIndex: "none",
      align: "center",
      width: 90,
      render: (val, record, index) => index + 1
    },
    {
      // title: "会议类型",
      title: "活动类型",
      dataIndex: "type",
      align: "center",
      width: 300
    },
    {
      title: "所属类别",
      dataIndex: "category",
      align: "center",
      width: 300
    }
  ];

  // 表格 属性
  const tableProps = {
    columns,
    dataSource: dataSource,
    scroll: { y: 400 },
    bordered: true,
    style: { marginTop: "20px" },
    loading: step1TableLoading,
    rowKey: "type_id",
    rowSelection: {
      type: "checkbox",
      columnTitle: "选择",
      onChange: () => {

      },
      onSelect: (record, isChecked, selectedRows) => {
        // console.log(state, record, isChecked);
        const { selected } = state;

        if (selected && Array.isArray(selected)) {
          const index = selected.findIndex((item) => { return item.type_id === record.type_id });
          if (isChecked) {
            if (index === -1) {
              selected.push(record);
            }
          } else {
            if (index !== -1) {
              selected.splice(index, 1);
            }
          }
          updateState({
            selected
          });
        }
      }
    },
    pagination: {
      // current: step1PageIndex,
      pageSize: step1PageSize,
      total: step1Total,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => {
        return `共${total}条记录，${Math.ceil(total / step1PageSize)}页`
      },
      onShowSizeChange: (current, size) => {
        changeShowSize(size);
      }
      // onChange: (_page, _pageSize) => {
      //   getStep1TableData({
      //     type_id,
      //     category_id,
      //     // page_no: _page,
      //     page_size: 10000
      //   })
      // }
    }
  }

  return (
    <div className="step1">
      <HeaderForm {...headerFormProps} />
      <Table {...tableProps} />
      <ButtonGroup {...buttonGroup} />
    </div>
  )
}

Step1.propTypes = {
  state: propTypes.object,
  updateState: propTypes.func,
  nextStep: propTypes.func,
  form: propTypes.object,
  getStep1TableData: propTypes.func,
  formatMeetingType: propTypes.func,
  changeShowSize: propTypes.func,
  handleCancel: propTypes.func
}

export default Step1;