import { http } from 'client/tool/axios';
import {
    ucHost,
    workflowHost,
    fileHost,
    activityHost
} from './config';


export const testProgress = () => {
    return {
        msg: '异步执行完成'
    }
};

//获取所有部门列表
export const fetchAllDepartment = () => {
    return http.get(`${ucHost}/uc/dep/all`);
};

//获取选择活动分页列表信息
export const fetchActiveList = (pageNum = 1, pageSize = 10, keyword = '', type = '', nd = null) => {
    return http.get(`${activityHost}/activity/find-condition`, { pageszie: pageSize, page: pageNum, keyword, type, nd });
};

//新建一个工作审批
export const insertApproval = (postObject) => {
    return http.post(`${workflowHost}/approval/add`, postObject);
};
