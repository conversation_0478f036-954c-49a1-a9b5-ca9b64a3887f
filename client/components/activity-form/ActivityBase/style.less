.gray-box {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #e5e5e5;
  background-color: #f7f8f9;
  span.mormal {
    color: #999999;
    margin-right: 20px;
  }
}

.custom-tags {
  border-bottom: 2px dotted #dddddd;
  padding: 10px 0;
  position: relative;
  .note-description {
    color: #999999;
    font-size: 13px;
  }
}
.custom-margin-top-none .ant-form-item-control-wrapper .ant-form-item-control {
  margin-top: 0;
}

.upload-image-container {
  .upload-image-container-wrapper {
    height: 123px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    padding: 15px;
    background-color: #f7f8f9;
    color: #999;
    .upload-image-container-uploadbtn {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      font-size: 18px;
      font-weight: 400;
      margin: 20px 0;
      i.anticon {
        margin-right: 10px;
      }
      i.anticon::before {
        color: #ccc;
        font-size: 32px;
      }
    }
  }
  .ant-upload {
    width: 100%;
  }
  .upload-done-container {
    border-radius: 3px;
    height: 123px;
    overflow: hidden;
    position: relative;
    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
    }
    img {
      width: 100%;
    }
    .upload-done-action-shadow {
      transition: 0.2s;
      width: 100%;
      height: 100%;
      display: none;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.3);
      .ant-upload {
        width: 30px;
      }
      & > span {
        cursor: pointer;
        margin: 0 30px;
      }
      .anticon {
        font-size: 30px;
        color: #fff;
      }
    }
    &:not([data-disabled="disabled"]):hover .upload-done-action-shadow {
      display: flex;
    }
  }
}
