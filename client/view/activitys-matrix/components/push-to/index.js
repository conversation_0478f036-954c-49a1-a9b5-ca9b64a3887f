import React, { Component } from "react";

import {
  Modal,
  Button,
  Form,
  Select,
  message,
  Input,
  Checkbox,
  Radio,
} from "antd";

import OrganizeSelector from "components/organize-selector";

import OrganizeModal from "components/organize-selector/sub-components/organize-modal";

import PropTypes from "prop-types";

const FormItem = Form.Item;
const Option = Select.Option;
const RadioGroup = Radio.Group;

import "./index.less";

const propTypes = {
  form: PropTypes.object,
  currentData: PropTypes.object,
  title: PropTypes.string,
  onSave: PropTypes.func,
  onCancel: PropTypes.func,
  visible: PropTypes.bool,
  pushing: PropTypes.bool,
  isLoading: PropTypes.bool,
  // type: PropTypes.string,
  // foo : PropTypes.string
};
const defaultProps = {
  title: "新闻",
  // type: "news"
};

export default Form.create({})(
  Object.assign(
    class PushTo extends Component {
      constructor(props) {
        super(props);
        this.state = {
          dataSource: [],
          // total: 50,
          // pageSize: 10,
          // current: 1,
          visible: false,
          organizeVisible: false,

          rangeType: props.rangeType || 0, //推送范围 0 矩阵   1 指定组织
          visibleType: props.visibleType || 1, //可见性 1：所有组织可见；2：接收组织可见
        };
        // console.log(props);

        this.handleSave = this.handleSave.bind(this);
        this.handleCancel = this.handleCancel.bind(this);
        this.showModal = this.showModal.bind(this);

        const { setFieldsValue, getFieldValue } = this.props.form;
      }

      showModal(e) {
        e.preventDefault();
        this.setState({
          organizeVisible: true,
        });
        // const { dataSource } = this.state;
        // console.log(dataSource);
        // 当前如果表格有数据，则将表格数据初始化到组件中
        // if (dataSource && Array.isArray(dataSource) && dataSource.length !== 0) {
        //   this.organizeModal.initData(dataSource);
        // }
      }
      // todo: 更新推送的组建
      // componentWillReceiveProps(nextProps){
      //   const {setFieldsValue}  =this.props.form;
      //   // console.log('nextProps---',nextProps)
      //   // console.log('visibleType---',nextProps.visibleType)
      //   if(nextProps.disabledList && nextProps.disabledList.length>0){
      //     if(this.state.dataSource!=nextProps.disabledList){
      //       // ,rangeType:1,visibleType:rangeType
      //       this.setState({dataSource:nextProps.disabledList})
      //       setFieldsValue({rangeType:1,visibleType:nextProps.visibleType})
      //     }
      //   }
      // }

      componentDidMount() {
        const { disabledList, rangeType, visibleType } = this.props;
        // console.log('visibleType---',visibleType)
        this.setState({
          dataSource: disabledList,
          rangeType: rangeType,
          visibleType: rangeType ? visibleType : 1,
        });
      }

      hideModal() {
        this.setState({
          organizeVisible: false,
        });
      }

      handleSave(e) {
        e.preventDefault();
        const { searchSubmit } = this.props;
        const { getFieldValue } = this.props.form;

        if (this.props.onSave) {
          // console.log("handleSave:", this.state.dataSource)
          const currentData = this.props.currentData;
          const rangeType = getFieldValue("rangeType");
          const visibleType = getFieldValue("visibleType");

          if (rangeType === 1) {
            if (!this.state.dataSource) {
              message.error("请选择组织");
              return;
            }
            if (this.state.dataSource && this.state.dataSource.length < 1) {
              message.error("请选择组织");
              return;
            }
          }

          this.props.onSave({
            selectedData: this.state.dataSource,
            currentData,
            rangeType,
            visibleType,
          });
        }

        return false;
      }
      handleCancel() {
        if (this.props.onCancel) {
          this.props.onCancel();
        }
        // const {dispatch} = this.props;
        // const {dataSource} =this.state;
        // if(!dataSource.length)
        this.cleanPushResult();
      }
      // todo:清空推送的结果
      cleanPushResult() {
        const { setFieldsValue } = this.props.form;
        setFieldsValue({
          rangeType: 0,
          visibleType: 1,
        });
        this.setState({ dataSource: [], organizeModalProps: [] });
      }

      matrixClick() {
        const { setFieldsValue } = this.props.form;
        setFieldsValue({
          visibleType: 1,
        });
      }

      render() {
        const {
          visible,
          form,
          pushing,
          disabledList,
          searchSubmit,
          rangeType,
          isLoading,
        } = this.props;
        const { getFieldDecorator, getFieldValue, setFieldsValue } = form;

        const { dataSource, organizeVisible } = this.state;

        const organizeModalProps = {
          // 是否区分子树父树，如果要查父树，则这个字段需要为true
          distinguishOwnerTree: true,
          visible: organizeVisible,
          dataSource: dataSource,
          disabledOrgList: dataSource,
          checkAll: false,
          hideModal: () => {
            this.hideModal();
          },
          loadOrganizeData: (data) => {
            // addKeyToTableDataSource(data);
            // getSelectedValues && getSelectedValues(data);
            this.setState({
              dataSource: data,
              organizeVisible: false,
            });
          },
          ref: (ref) => {
            this.organizeModal = ref;
          },
        };

        const selectMenu = sessionStorage.getItem("_menu_id");
        console.log('selectMenu == 1700902', selectMenu != 1700902, selectMenu);

        return (
          <Modal
            className="com-push-to-modal"
            title={this.props.title + "推送"}
            width={600}
            visible={visible}
            onCancel={this.handleCancel}
            footer={false}
          >
            <OrganizeModal {...organizeModalProps} />

            <Form>
              <FormItem
                label="推送范围："
                labelCol={{ span: 7 }}
                wrapperCol={{ span: 16 }}
              >
                {getFieldDecorator("rangeType", {
                  initialValue: this.state.rangeType,
                  rules: [],
                })(
                  <RadioGroup>
                    {selectMenu != 1700902 && (
                      <Radio onClick={this.matrixClick.bind(this)} value={0}>
                        推送到{this.props.title}矩阵
                      </Radio>
                    )}
                    <Radio value={1}>推送给指定组织</Radio>
                  </RadioGroup>
                )}
              </FormItem>

              {/* 选择了推送给指定组织方式时，联动显示此选组织组件 */}
              {getFieldValue("rangeType") == 1 ? (
                <FormItem
                  className="check-item"
                  label=""
                  labelCol={{ span: 2 }}
                  wrapperCol={{ span: 16, offset: 4 }}
                >
                  {getFieldDecorator("orgids", {
                    rules: [],
                  })(
                    <div className="org-selector">
                      {/* 如果指定有组织，统计组织个数 */}
                      {dataSource && dataSource.length > 0 ? (
                        <div className="selected-orgs">
                          指定了 <span>{dataSource.length}</span> 个组织{" "}
                          <a href="#" onClick={this.showModal}>
                            编辑
                          </a>
                        </div>
                      ) : (
                        <div className="no-selected-orgs">
                          还未指定组织{" "}
                          <a href="#" onClick={this.showModal}>
                            选择接收组织
                          </a>
                        </div>
                      )}
                    </div>
                  )}
                </FormItem>
              ) : null}

              <FormItem
                label="矩阵列表可见"
                labelCol={{ span: 7 }}
                wrapperCol={{ span: 16 }}
              >
                {getFieldDecorator("visibleType", {
                  initialValue:
                    selectMenu == 1700902 ? 2 : this.state.visibleType,
                  rules: [],
                })(
                  <RadioGroup>
                    {selectMenu != 1700902 && (
                      <Radio value={1}>所有组织可见</Radio>
                    )}
                    <Radio value={2} disabled={getFieldValue("rangeType") != 1}>
                      接收推送的组织可见
                    </Radio>
                  </RadioGroup>
                )}
              </FormItem>

              <FormItem
                className="button-item"
                wrapperCol={{ span: 12, offset: 7 }}
              >
                <Button
                  type="primary"
                  htmlType="submit"
                  onClick={this.handleSave}
                  loading={isLoading || false}
                >
                  确定推送
                </Button>
                <Button onClick={this.handleCancel}>取消</Button>
              </FormItem>
            </Form>
          </Modal>
        );
      }
    },
    { propTypes, defaultProps }
  )
);
