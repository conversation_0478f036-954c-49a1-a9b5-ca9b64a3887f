import React, {Component} from 'react';
import {Table, Divider, Popconfirm} from 'antd';

import SearchHeader from 'components/search-header';

export default ({
                    onBack,
                    props,
                    dataList,
                    editHandler,
                    deleteHandler,
                    pageChangeHandler,
                    pageNum,
                    pageSize,
                    total
                }) => {
    const columns = [{
        title: '奖品',
        align: 'center',
        dataIndex: 'name'
    }, {
        title: '操作',
        align: 'center',
        render: (value, row, index) => (
            <span>
			<a onClick={() => editHandler(row)}>编辑</a>
			<Divider type="vertical"/>
            <Popconfirm title={'确认删除互动分类？'} onConfirm={() => deleteHandler(row)} okText="删除" cancelText="取消">
                {/*onClick={() => onDelete(row)}*/}
                <a href="javascript:void(0)">删除</a>
            </Popconfirm>
                {/*<a onClick={() => deleteHand<PERSON>(row)}>删除</a>*/}
		</span>
        )
    }];

    return (
        <div>
            <SearchHeader title="奖品管理" onBack={onBack}/>
            <div className="main">
                <Table
                    rowKey="prize_id"
                    // style={{padding: 0}}
                    dataSource={dataList}
                    columns={columns}
                    bordered
                    pagination={{
                        pageSize: pageSize,//添加一行显示添加栏目按钮
                        current: pageNum,
                        total,
                        onChange: pageChangeHandler,
                        showTotal: (total, range) => {
                            // console.log(total, range);
                            return `数据总数：${total}条，当前显示${range.join('至')}条`;
                        }
                    }}
                />
            </div>
        </div>
    );
}
