// http://localhost:8001/plugin/location-selector

function LocationSelector() {
    // 默认天安门坐标
    this.setPoint = function ({ lng = 116.397468, lat = 39.9087, bounds = true }) {
        this.map.clearMap();
        var point = new AMap.LngLat(lng, lat);
        this.map.setCenter(point);
        this.map.setZoom(15);
        this.marker.setPosition(point);
        this.marker.setMap(this.map);
    }

    this.searchByKeyword = function (keyword) {
        this.placeSearch.search(keyword);
    }

    this.cleanKeyword = function () {
        this.map.clearMap();
        this.geolocation.getCurrentPosition();
    }
}
LocationSelector.prototype.init = function () {

    if (!window.parent.mapReady) {
        window.parent.mapReady = function () { console.log("地图就绪") }
    }

    if (!window.parent.searchLocation) {
        window.parent.searchLocation = function () { console.log("关键字搜索") }
    }

    const This = this;
    // console.log("定位组件初始化", window);
    this.map = new AMap.Map("map-container", {
        resizeEnable: true
    });

    // this.map.on("click", function (e) {
    //     var target = e.target;
    //     var lnglat = e.lnglat;
    //     var pixel = e.pixel;
    //     var type = e.type;
    //     console.log(target, lnglat, pixel, type);
    //     if (lnglat) {
    //         This.setPoint(lnglat);
    //     }
    // });

    AMap.service(["AMap.PlaceSearch"], function () {
        this.placeSearch = new AMap.PlaceSearch({
            pageSize: 10,
            pageIndex: 1,
            map: this.map,
            autoFitView: true,
            panel: "panel"
        });
    });

    this.marker = new AMap.Marker({
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
        position: [0, 0]
    });

    this.map.plugin(["AMap.Geolocation", "AMap.CitySearch", "AMap.MapType", "AMap.Scale", "AMap.PlaceSearch"], function () {
        This.geolocation = new AMap.Geolocation({
            // 是否使用高精度定位，默认：true
            enableHighAccuracy: true,
            // 设置定位超时时间，默认：无穷大
            timeout: 5000,
            // 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)
            buttonOffset: new AMap.Pixel(10, 20),
            //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            zoomToAccuracy: true,
            //  定位按钮的排放位置,  RB表示右下
            buttonPosition: 'RB'
        });
        This.placeSearch = new AMap.PlaceSearch({
            pageSize: 10,
            pageIndex: 1,
            map: this.map,
            autoFitView: true
        });

        This.map.addControl(This.geolocation);
        This.map.addControl(new AMap.Scale());
        // This.map.addControl(new AMap.MapType());

        This.citySearch = new AMap.CitySearch();
        This.geolocation.getCurrentPosition();

        AMap.event.addListener(This.geolocation, "complete", function (data) {
            const position = data.position || {};
            This.setPoint(position);
        });
        AMap.event.addListener(This.geolocation, "error", function (error) {
            console.error("定位失败", error);
            This.citySearch.getLocalCity();
        });
        AMap.event.addListener(This.citySearch, "complete", function (data) {
            // console.log(data);
            const bounds = data.bounds;
            This.map.setBounds(bounds);
        });
        AMap.event.addListener(This.citySearch, "error", function () {
            console.error("获取城市失败");
            // This.setPoint();
        });
        AMap.event.addListener(This.placeSearch, "complete", function (data) {
            // console.log(data);
            window.parent && window.parent.searchLocation(data);
        });
        AMap.event.addListener(This.placeSearch, "error", function (error) {
            console.error("关键字查询失败", error);
        });
    });
    // 地图加载就绪
    window.parent.mapReady();
}

var ls = new LocationSelector();

ls.init();
// const point = new AMap.LngLat(_calendar.lng, _calendar.lat);
// map.setCenter(point);