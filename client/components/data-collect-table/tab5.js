import React from 'react';
import { Table, Button } from 'antd';
import propTypes from 'prop-types';

const Tab = (props) => {
  const {
    data,
    eval_year,
    eval_cycle,
    months: time,
    isFinish,
    eventsObj,
    loading,
    isCollect
  } = props;
  // console.log('统计情况，是否完成', isFinish);
  const { type_id, total_deduct_points, list_detail: rows, type_name, total_deduct_points_sum, eval_num } = data;
  const renderColumnTitle = (item) => {
    const cycleText = '月';
    // 默认按季度统计，时间单位粒度为月
    // if (eval_cycle === 2) {

    // }
    return `${eval_year}年${item}${cycleText}`;
  }
  const renderDynamicColumns = (time = [], isFinish = false) => {
    let result = [];
    if (time && Array.isArray(time)) {
      let tCol;
      time.forEach((item, index) => {
        tCol = {
          // dataIndex: `time${index}`,
          title: renderColumnTitle(item),
          align: 'center',
          // width: 90,
          children: [
            {
              dataIndex: `time${index}`,
              title: '开展次数',
              align: 'center',
              width: 120,
              render: (text, record, i) => {
                // 默认按季度统计，时间粒度为月
                let result = record[`month${item}`];
                // if (eval_cycle === 1) {
                //   result = record[`month${item}`];
                // }
                return (
                  <span>
                    {result}
                  </span>
                )
              },
            }
          ]
        }
        result.push(tCol);
      });
      if (isFinish) {
        result = result.concat([
          {
            dataIndex: 'is_finished',
            title: '是否完成',
            align: 'center',
            width: 90,
            render: (text, record, i) => {
              return (
                <span>{text === 1 ? '完成' : text === 2 ? '未完成' : text === 3 ? '不考核' : '-'}</span>
              )
            }
          },
          {
            dataIndex: 'deduct_points',
            title: type_id != 1 ? '支部扣分(系统)' : '领导干部扣分(系统)',
            align: 'center',
            width: 80,
            render: (text, record, i) => {
              let style;
              if (text < 0) {
                style = { color: '#FF4D4F' };
              }
              return (
                <span style={style}>{text}</span>
              )
            }
          },
          {
            dataIndex: 'deduct_points_modify',
            title: type_id != 1 ? '支部扣分(修正)' : '领导干部扣分(修正)',
            align: 'center',
            width: 80,
            render: (text, record, i) => {
              let style;
              if (text < 0) {
                style = { color: '#FF4D4F' };
              }
              return (
                <span style={style}>{text}</span>
              )
            }
          },
          {
            dataIndex: 'total_deduct_points',
            title: type_id != 1 ? '支部扣分' : '领导干部扣分',
            align: 'center',
            width: 80,
            render: (text, record, i) => {
              let style;
              if (text < 0) {
                style = { color: '#FF4D4F' };
              }
              return (
                <span style={style}>{text}</span>
              )
            }
          },
          {
            dataIndex: 'deduct_points',
            title: '操作',
            align: 'center',
            width: 80,
            render: (text, record, i) => {
              return <a href="javascript:void(0)" onClick={eventsObj.showModal.bind(null, record)}>修正扣分值</a>
            }
          },
        ]);
      }
      // 统计采集
      if (isCollect === 4) result.pop();
    }
    return result;
  }
  const columns = [
    {
      dataIndex: 'org_name',
      title: '考核支部',
      align: 'center',
      width: 200,
    },
    ...renderDynamicColumns(time, isFinish)
  ];
  const tableProps = {
    rowKey: (record, index) => {
      return `data-collect-${record.org_id}-${index}`
    },
    bordered: true,
    columns,
    dataSource: rows,
    pagination: false,
    scroll: { x: 'max-content', y: 350 }
  }
  return (
    <div className="tab-content-container">
      <Table {...tableProps} />
      <footer>
        {
          isFinish &&
          <div className='deduct-wrapper'>
            <span>扣分合计: {total_deduct_points_sum}</span>
            <span>考核支部数量: {eval_num}</span>
            <span>本项产生扣分: {total_deduct_points}</span>
            {
              isCollect === 4 ? null :
                <Button type="danger" onClick={eventsObj.reload} className="btn-last" loading={loading} >刷 新</Button>
            }
          </div>
        }
        <div className='intro-wrapper'>
          说明：要求每支部每季度至少1次，未达到次数要求的支部每季度扣6分；单位扣分取各支部扣分平均值（保留小数点后1位）；新成立的支部从次月开始纳入考核。（例：支部3月才成立，4月起纳入考核，故一季度不考核。）
        </div>
      </footer>
    </div>
  )
}

Tab.propTypes = {
  time: propTypes.array,
  data: propTypes.object,
  isFinish: propTypes.bool
};
Tab.defaultProps = {
  time: [],
  data: {},
  isFinish: false
};

export default Tab;