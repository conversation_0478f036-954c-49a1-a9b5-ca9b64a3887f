/**
 * 活动栏目管理界面
 * <AUTHOR>
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, message } from 'antd';
import './index.less';
import IndexView from './index-view';
import SideView from './side-view';
import CenterCon from 'components/center-content';

// 暂不明确为何设置该标志位
let isSport = false;

// 切换边栏显示或者消失
const toggleSide = (dispatch, isShow = false) => {
    if (isShow) {
        // 切换外壳显示动画
        dispatch({ type: 'activityList/toggleHideWrap' });
        setTimeout(() => {
            dispatch({ type: 'activityList/toggleCollapsed' });
        }, 0);
    } else {
        // 关闭的时候 等待动画完毕的时候执行隐藏外壳
        dispatch({ type: 'activityList/toggleCollapsed' });
        setTimeout(() => {
            isSport = false;
            dispatch({ type: 'activityList/toggleHideWrap' });
        }, 300);
    }
};
// 变更侧边栏标题
const toggleModalTitle = (dispatch, title = '') => {
    dispatch({ type: 'activityList/toggleModalTitle', title });
};

class ActivityList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            //用于存放标识侧边栏是新增一级栏目1/新增子集栏目2/编辑栏目3
            asideViewType: 0,
            //暂存当前添加子节点时的父节点id
            parent_column_id: 0,
            //暂存当前编辑节点的活动id
            activity_column_id: 0,
            //选人框框是否可见标志位
            centerConVisible: false,
            //当前工作流程是否发生改变
            isWorkflowChange: false,
            //当前操作的人员列表种类:'users或者cc'
            peopleListType: ''
        }
    }

    componentDidMount() {
        const { dispatch, activityList } = this.props,
            { pageNum, pageSize } = activityList;
        // console.log(pageNum, pageSize);
        //获取活动栏目列表onOverview
        dispatch({ type: 'activityList/fetchColumn', payload: { pageNum, pageSize } });
        //获取工作流程列表
        dispatch({ type: 'activityList/fetchWorkflowList', payload: {} });
    }

    render() {
        //获取节点路径
        const fetchNodePath = (id, list) => {
            let result = [];
            return fetchLeaf(result, id, list);
        };
        const fetchLeaf = (result, id, list, activity_column_id) => {
            let resultCopy = JSON.parse(JSON.stringify(result));
            if (activity_column_id) {
                resultCopy.push(activity_column_id);
            }
            if (Array.isArray(list) && list.length !== 0) {
                for (let i = 0, len = list.length; i < len; i++) {
                    if (list[i].activity_column_id !== id) {
                        if (list[i].children && Array.isArray(list[i].children) && list[i].children.length !== 0) {
                            let path = fetchLeaf(resultCopy, id, list[i].children, list[i].activity_column_id);
                            if (path) {
                                return path;
                            }
                        }
                    } else {
                        resultCopy.push(id);
                        return resultCopy;
                    }
                }
            }
        };

        // activityList

        const { activityList, dispatch, form, history, props } = this.props,
            _this = this,
            {
                dataList,
                isHideWrap,
                collapsed,
                modalTitle,
                approveList,
                copyToList,
                pageNum,
                pageSize,
                total,
                workflowList,
                formData,
                department_name,
                department_id,
                parent_column_id,
                workflow_id,
                workflow_name
            } = activityList,
            indexViewProps = {
                workflowList,
                dataList,
                pageNum,
                pageSize,
                total,
                props,
                onAppendChildren(row) {
                    // console.log('添加子栏目', row);
                    toggleSide(dispatch, true);
                    toggleModalTitle(dispatch, '添加子栏目');

                    let { must_approve, workflow_id: workflowId, workflow_name } = row;
                    formData.must_approve = must_approve;
                    formData.workflow_id = workflowId;
                    formData.workflow_name = workflow_name;
                    const { setFieldsValue } = form;

                    setTimeout(() => {
                        setFieldsValue({ 'workflow_name': workflow_name });
                    }, 0);

                    //添加子栏目时，默认子栏目继承父级栏目的审批流程和是否必须审批状态
                    dispatch({
                        type: 'activityList/fetchWorkflowItemInfo',
                        payload: { workflowId }
                    }).then(
                        (data = {}) => {
                            let { name } = data;

                            dispatch({
                                type: 'activityList/save',
                                payload: {
                                    workflow_id: workflowId,
                                    workflow_name: name || '自定义',
                                    formData
                                }
                            });
                        }
                    );

                    //设置parent_column_id
                    _this.setState({
                        asideViewType: 2,
                        parent_column_id: row.activity_column_id
                    });
                },
                onAppendRoot() {
                    // console.log(formData);
                    // console.log('添加一级栏目');
                    toggleSide(dispatch, true);
                    toggleModalTitle(dispatch, '添加栏目');
                    _this.setState({
                        asideViewType: 1,
                        parent_column_id: 0
                    });
                },
                onOverview(row, index) {
                    //活动分类id，带到活动列表路由
                    let { activity_column_id } = row;
                    // console.log('根据活动类型，转向活动详情页面路由，查看活动', row, index);

                    //查看活动
                    if (row.parent_column_id === 0) {
                        history.push({
                            pathname: '/all-activity',
                            query: {
                                activity_column_id: [activity_column_id],
                                onBack() {
                                    history.goBack();
                                }
                            }
                        });
                    } else {
                        // 非一级栏目查看活动，需要获取栏目路径，包含父级栏目
                        history.push({
                            pathname: '/all-activity',
                            query: {
                                activity_column_id: fetchNodePath(activity_column_id, dataList),
                                onBack() {
                                    history.goBack();
                                }
                            }
                        });
                    }
                },
                onEdit(row) {
                    // console.log('编辑栏目', row);
                    let { activity_column_id: activityId, workflow_name } = row;
                    toggleSide(dispatch, true);
                    toggleModalTitle(dispatch, '编辑栏目');
                    setTimeout(() => {
                        form.setFieldsValue({ 'workflow_name': workflow_name });
                    }, 0);

                    //通过id获取该活动栏目的内容
                    dispatch({
                        type: 'activityList/fetchEditColumn',
                        payload: { activityId }
                    }).then(
                        (data) => {
                            let { workflow_id: workflowId } = data;
                            dispatch({
                                type: 'activityList/fetchWorkflowItemInfo',
                                payload: { workflowId }
                            }).then(
                                (data = {}) => {
                                    let { name } = data;
                                    dispatch({
                                        type: 'activityList/save',
                                        payload: {
                                            workflow_id: workflowId,
                                            workflow_name: name || '自定义'
                                        }
                                    });
                                }
                            )
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    );

                    _this.setState({
                        asideViewType: 3,
                        activity_column_id: activityId
                    });
                },
                onDelete(row) {
                    // 删除一个活动分类
                    let { activity_column_id } = row;
                    dispatch({
                        type: 'activityList/deleteActivity',
                        payload: { activityId: activity_column_id }
                    }).then(
                        () => {
                            message.success('删除栏目成功');
                            dispatch({
                                type: 'activityList/fetchColumn',
                                payload: {
                                    pageNum: 1,
                                    pageSize
                                }
                            }).then(
                                () => {
                                    dispatch({ type: 'activityList/save', payload: { pageNum: 1 } });
                                }
                            ).catch(
                                (error) => {
                                    console.error(error);
                                }
                            );
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    );
                },
                // 页码组件 需要参考  antd Pagination分页组件  在table中使用
                pageChangeHandler(pageNum, pageSize) {
                    dispatch({
                        type: 'activityList/fetchColumn',
                        payload: { pageNum, pageSize }
                    }).then(
                        () => {
                            dispatch({ type: 'activityList/save', payload: { pageNum } });
                        }
                    ).catch(
                        (error) => {
                            console.error(error);
                        }
                    );
                }
            },
            sideViewProps = {
                form,
                collapsed,
                isHideWrap,
                modalTitle,
                approveList,
                copyToList,
                formData,
                workflowList,
                onClose() {
                    //重置表单状态
                    form.resetFields();
                    dispatch({
                        type: 'activityList/resetFormData'
                    });
                    form.setFieldsValue({
                        workflow_name: '自定义'
                    });
                    // 关闭的时候 加一个开关  解决 当点击背景的时候 多次点击 会造成事件重复执行
                    if (isSport) return;
                    isSport = true;
                    toggleSide(dispatch, false);
                },
                //点击添加，弹出选择框
                onAddHandler(type) {
                    _this.setState({
                        centerConVisible: true,
                        peopleListType: type
                    });
                },
                //点击移除用户
                onRemoveHandler(people = {}, index = 0) {
                    //审批流程经过变更，调整标志位
                    if (!_this.state.isWorkflowChange) {
                        _this.setState({
                            isWorkflowChange: true
                        });
                    } else {
                        //如果审批流程经过变更
                    }
                    if (people.type === 'approveList') {
                        // console.log('从审批列表移除', people, index);
                        // console.log(approveList);
                        //移除用户属于的审批轮次由index确定
                        let approveTurn = approveList[index];
                        //该轮次中，用户所处的位置由position确定
                        let { position } = people;
                        if (Array.isArray(approveTurn.user_id) && approveTurn.user_id.length !== 0
                            && Array.isArray(approveTurn.user_name) && approveTurn.user_name.length !== 0) {
                            if (approveTurn.user_id.length === 1 && approveTurn.user_name.length === 1) {
                                //当删除的轮次中仅有一个审批人时，删除该轮次
                                approveList.splice(index, 1);
                            } else if (approveTurn.user_id.length > 1 && approveTurn.user_name.length > 1
                                && approveTurn.user_id.length === approveTurn.user_name.length) {
                                approveTurn.user_id.splice(position, 1);
                                approveTurn.user_name.splice(position, 1);
                            }
                        }
                    } else if (people.type === 'copyToList') {
                        // console.log('从抄送列表移除', people, index);
                        // console.log(copyToList);
                        if (Array.isArray(copyToList)) {
                            copyToList.splice(index, 1);
                        }
                    }

                    //流程调整：操作成功后先将本地的列表进行替换，将当前的workflow_id设置为-999“不设置(自定义)”
                    dispatch({
                        type: 'activityList/save',
                        payload: {
                            approveList,
                            copyToList
                        }
                    });
                    // form.setFieldsValue({
                    //     workflow_id: -999
                    // });
                },
                handleSubmit(e) {
                    // e.preventDefault();
                    // 调用这个方法之后表单数据无法再被改变
                    // console.log(form);
                    // return;
                    const { getFieldValue, validateFields, resetFields, setFieldsValue } = form;
                    validateFields((errors, values) => {
                        if (!values.must_approve) {
                            values.must_approve = 0;
                        } else {
                            values.must_approve = 1;
                        }
                        //2018-7-16,toG二期取消部门管理功能，设置发起审批部门信息未固定值
                        values.department_id = typeof window !== 'undefined' && window.sessionStorage.getItem('_did') || -999;
                        values.department_name = typeof window !== 'undefined' && window.sessionStorage.getItem('_dn') || 'root';
                        //向表单数据添加从登陆获取的部门名称，部门id等信息
                        // values.department_name = department_name;
                        // values.department_id = department_id;

                        //因为隐藏注释了功能为实现的表单控件，导致提交数据缺项，设置为默认值之后提交
                        if (!values.keyword) {
                            values.keyword = formData.keyword || '未设置';
                        }
                        if (!values.description) {
                            values.description = formData.description || '未设置';
                        }
                        if (!values.title) {
                            values.title = formData.title || '未设置';
                        }
                        if (!values.template) {
                            values.template = formData.template || {
                                'page_template': -999,//'long|必填|频道页模板id'
                                'list_template': -999,//'long|必填|列表模板id'
                                'doc_template': -999,//'long|必填|文章模板id'
                                'page_template_app': -999,//'long|必填|频道页模板id（app）'
                                'list_template_app': -999,//'long|必填|列表模板id（app）'
                                'doc_template_app': -999//'long|必填|文章模板id（app）'
                            }
                        }
                        if (!values.workflow_name) {
                            values.workflow_name = (formData.workflow_name === getFieldValue('workflow_name') ? formData.workflow_name : getFieldValue('workflow_name') || '未设置');

                        }
                        if (!values.hid) {
                            values.hid = formData.hid || 0;
                        }
                        if (!values.jurisdiction) {
                            values.jurisdiction = formData.jurisdiction || 1;
                        }
                        if (!values.home_page) {
                            values.home_page = formData.home_page || 1;
                        }

                        values.is_default = [false, true].indexOf(  !! (values.is_default || formData.is_default || false)  )

                        if (!errors) {
                            //传入新的工作流程
                            let handler = (newFlow) => {
                                if (newFlow) {
                                    values.workflow_id = newFlow.workflow_id;
                                    values.workflow_name = newFlow.name;
                                } else {
                                    values.workflow_id = workflow_id;
                                    values.workflow_name = workflow_name;
                                }

                                if (_this.state.asideViewType === 1) {
                                    values.parent_column_id = 0;
                                    //新增一级栏目
                                    dispatch({
                                        type: 'activityList/insertActivity',
                                        payload: { postObject: values }
                                    }).then(
                                        () => {
                                            message.success('添加一级栏目成功');
                                            dispatch({
                                                type: 'activityList/fetchColumn',
                                                payload: {
                                                    pageNum,
                                                    pageSize
                                                }
                                            });
                                            dispatch({ type: 'activityList/resetFormData' });
                                            //重置表单状态
                                            resetFields();
                                            setFieldsValue({
                                                workflow_name: '自定义'
                                            });
                                            toggleSide(dispatch, false);
                                        }
                                    ).catch(
                                        (error) => {
                                            console.error(error);
                                        }
                                    );
                                }
                                else if (_this.state.asideViewType === 2) {
                                    values.parent_column_id = _this.state.parent_column_id;
                                    //新增子栏目
                                    dispatch({
                                        type: 'activityList/insertActivity',
                                        payload: { postObject: values }
                                    }).then(
                                        () => {
                                            //重载当前页
                                            message.success('添加子栏目成功');
                                            dispatch({
                                                type: 'activityList/fetchColumn',
                                                payload: {
                                                    pageNum,
                                                    pageSize
                                                }
                                            });
                                            dispatch({ type: 'activityList/resetFormData' });
                                            //重置表单状态
                                            resetFields();
                                            setFieldsValue({
                                                workflow_name: '自定义'
                                            });
                                            toggleSide(dispatch, false);
                                        }
                                    ).catch(
                                        (error) => {
                                            console.error(error);
                                        }
                                    );
                                }
                                else if (_this.state.asideViewType === 3) {
                                    values.activity_column_id = _this.state.activity_column_id;
                                    //编辑栏目
                                    dispatch({
                                        type: 'activityList/editActivity',
                                        payload: { postObject: values }
                                    }).then(
                                        () => {
                                            message.success('编辑栏目成功');
                                            //编辑成功，重载当前页面
                                            dispatch({
                                                type: 'activityList/fetchColumn',
                                                payload: {
                                                    pageNum,
                                                    pageSize
                                                }
                                            });
                                            dispatch({ type: 'activityList/resetFormData' });
                                            //重置表单状态
                                            resetFields();
                                            setFieldsValue({
                                                workflow_name: '自定义'
                                            });
                                            toggleSide(dispatch, false);
                                        }
                                    ).catch(
                                        (error) => {
                                            console.error(error);
                                        }
                                    )
                                }
                            };
                            // 当表单校验通过后，首先判断审批流程是否发生过自定义变化，
                            // 如果发生过自定义变化，需要首先提交并绑定工作流程
                            // console.log(workflow_name, getFieldValue('workflow_name'));
                            if (values.must_approve && ( _this.state.isWorkflowChange || workflow_name !== getFieldValue('workflow_name') ) ) {
                                // console.log('有自定义流程', workflow_name);
                                dispatch({
                                    type: 'activityList/workflowAdd',
                                    payload: {
                                        postObject: {
                                            name: (workflow_name === getFieldValue('workflow_name') ? workflow_name : getFieldValue('workflow_name')) || '自定义',
                                            type: 2,
                                            users: approveList,
                                            cc: copyToList
                                        }
                                    }
                                }).then(
                                    (newFlow) => {
                                        // console.log(newFlow);
                                        // return;
                                        if (newFlow) {
                                            handler(newFlow);
                                        }
                                    }
                                ).catch(
                                    (error) => {
                                        console.error(error);
                                    }
                                );
                                //添加成功后需要重置isWorkflowChange为false
                                _this.setState({
                                    isWorkflowChange: false
                                });
                            } else {
                                // console.log('没有自定义流程');
                                handler();
                            }
                        } else {
                            console.error(errors, values);
                        }
                    });
                },
                handleCancel(e) {
                    // e.preventDefault();
                    // console.log('取消表单提交动作');
                    toggleSide(dispatch, false);
                    //重置表单状态
                    form.resetFields();
                    dispatch({
                        type: 'activityList/resetFormData'
                    });
                    form.setFieldsValue({
                        workflow_name: '自定义'
                    });
                },
                workflowChangeHandler(value, event) {
                    //value代表选中的流程名称
                    const { key, props } = event,
                        { title } = props,
                        { setFieldsValue } = form,
                        workflow_id = parseInt(key);

                    setTimeout(() => {
                        setFieldsValue({ 'workflow_name': title });
                    }, 0);

                    // return;
                    // 从审批类型列表中，
                    // 找到相应的工作流程
                    // 并将该工作流程的审批人员列表和抄送人员列表赋值给相应数组
                    // 经过这一步操作，应该把工作流程是否改变标志位重置为false

                    if (_this.state.isWorkflowChange) {
                        _this.setState({
                            isWorkflowChange: false
                        });
                    }

                    if (workflow_id !== -999) {
                        dispatch({
                            type: 'activityList/fetchWorkflowItemInfo',
                            payload: { workflowId: workflow_id }
                        }).then(
                            (data = {}) => {
                                let { name } = data;
                                dispatch({
                                    type: 'activityList/save',
                                    payload: {
                                        workflow_id,
                                        workflow_name: name || ''
                                    }
                                })
                            }
                        );
                    } else {
                        //当选择不设置审批时，将两个列表清空，理论上这步操作可以不做
                        dispatch({
                            type: 'activityList/save',
                            payload: { workflow_id: -999, workflow_name: '自定义', approveList: [], copyToList: [] }
                        });
                    }
                }
            },
            centerConProps = {
                visible: _this.state.centerConVisible,
                //选择插件单选和多选的开关，为true时可以多选
                singleSlec: false,
                handleOk(peoples) {
                    if (!_this.state.isWorkflowChange) {
                        _this.setState({
                            isWorkflowChange: true
                        });
                    }
                    let peopleListType = _this.state.peopleListType;
                    if (peopleListType === 'approveList') {
                        // console.log('向审批人员添加', peoples, approveList);
                        //目前添加人员时只能新增审批轮次
                        let target = { user_id: [], user_name: [] };
                        if (peoples && Array.isArray(peoples) && peoples.length !== 0) {
                            peoples.forEach((people, index) => {
                                target.user_id.push(people.user_id);
                                target.user_name.push(people.name);
                            });
                        }
                        approveList.push(target);
                    } else if (peopleListType === 'copyToList') {
                        // console.log('向抄送列表添加', peoples, copyToList);
                        let target = { user_id: null, user_name: null };
                        if (peoples && Array.isArray(peoples) && peoples.length !== 0) {
                            peoples.forEach((people, index) => {
                                target.user_id = people.user_id;
                                target.user_name = people.name;
                                copyToList.push(target);
                            });
                        }
                    }
                    _this.setState({
                        centerConVisible: false
                    });
                    //流程调整：操作成功后先将本地的列表进行替换，将当前的workflow_id设置为-999“不设置(自定义)”
                    dispatch({
                        type: 'activityList/save',
                        payload: {
                            approveList,
                            copyToList
                        }
                    });
                    // form.setFieldsValue({
                    //     workflow_name: '自定义'
                    // });
                },
                handleCancel() {
                    _this.setState({
                        centerConVisible: false
                    });
                }
            };
        return <div className="activity-list">
            {/*{this.state.title}*/}
            <IndexView {...indexViewProps} />
            <SideView {...sideViewProps} />
            <CenterCon {...centerConProps} />
        </div>
    }
}

const mapStateToProps = ({ activityList }) => ({ activityList });

// 由dva提供的connect连接方法  去连接 当前组件需要用到的状态
export default connect(mapStateToProps)(Form.create()(ActivityList));
