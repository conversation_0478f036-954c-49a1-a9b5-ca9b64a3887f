/**
 * 投票结果
 * <AUTHOR>
 * 2018/4/23
 */
import React from 'react'
import PropTypes from 'prop-types'
import {
    Row,
    Col,
    Form,
    Radio,
    Select,
    InputNumber
} from 'antd'

import adaptiveLayout from '../gridLayout'
import './style.less'

const Option = Select.Option
const radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px'
}

const resultColWrap = {
    style: { paddingTop: 6 }
}

class VoteResult extends React.Component {
    render() {
        const {
            form,
            emuns,
            preview,
            formFields,
            activityFormState
        } = this.props
        const { voteResultPublic, voteSortingSetting, voteSortingSettingOnlyShow, voteStatisticsShowSetting, voteSumPeople } = emuns
        const { getFieldDecorator, getFieldValue } = form
        const voteSortingSettingOnlyShowMax = (activityFormState.voteOptionData.groups || []).length || 3
        const handleKeyUp = (key) => {
            var reg = /^[1-3]{1}/g, value = key && key.key ? key.key : null
            value = isNaN(value) ? 3 : value < 3 && value > 0 ? parseInt(value) : 3
            if (value) {
                reg.test(parseInt(value)) ? handleSetFormValue(value) : handleSetFormValue(3)
            }
        }
        const handleSetFormValue = (val) => {
            form.setFieldsValue({
                [formFields.voteSortingSettingOnlyShow]: val
            })
        }
        return <div>
            <Form.Item {...adaptiveLayout.formWrapper} label="是否公布" className="custom-form-item-col3">
                {getFieldDecorator(formFields.voteResultPublic, {
                    initialValue: activityFormState.voteResultPublic
                })(
                    <Radio.Group disabled={preview} options={voteResultPublic} />
                )}
                <hr style={{ marginBottom: 0 }} />
            </Form.Item>
            <Row className="ant-form-item">
                <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                    <label title="显示设置">显示设置</label>
                </Col>
                <Col {...adaptiveLayout.formWrapper.wrapperCol} className="custom-form-item-col">
                    <Row>
                        <Col span={12} {...resultColWrap}>
                            排序设置
                            <div className="vote-result-section">
                                {getFieldDecorator(formFields.voteSortingSetting, {
                                    initialValue: activityFormState.voteSortingSetting.type
                                })(
                                    <Radio.Group disabled={preview}>
                                        <Radio value={voteSortingSetting.normal.value}>{voteSortingSetting.normal.label}</Radio>
                                        <Radio value={voteSortingSetting.down.value}>{voteSortingSetting.down.label}</Radio>
                                        <Radio value={voteSortingSetting.up.value}>{voteSortingSetting.up.label}</Radio>
                                        <Radio value={voteSortingSetting.only.value}>
                                            {voteSortingSetting.only.label}
                                            {getFieldDecorator(formFields.voteSortingSettingOnlyShow, {
                                                initialValue: activityFormState.voteSortingSetting.onlyShow
                                            })(
                                                <InputNumber
                                                    placeholder="请输入"
                                                    disabled={getFieldValue(formFields.voteSortingSetting) !== voteSortingSetting.only.value || preview}
                                                    min={3}
                                                    onKeyUp={handleKeyUp}
                                                    max={voteSortingSettingOnlyShowMax < 3 ? 3 : voteSortingSettingOnlyShowMax}
                                                    style={{ width: 70, marginLeft: 10 }} />
                                            )}
                                        </Radio>
                                    </Radio.Group>
                                )}
                            </div>
                        </Col>
                        <Col span={6} {...resultColWrap}>
                            统计显示设置
                            <div className="vote-result-section">
                                {getFieldDecorator(formFields.voteStatisticsShowSetting, {
                                    initialValue: activityFormState.voteStatisticsShowSetting
                                })(
                                    <Radio.Group disabled={preview} options={voteStatisticsShowSetting} />
                                )}
                            </div>
                        </Col>
                        <Col span={6} {...resultColWrap}>
                            总投票人次设置
                            <div className="vote-result-section">
                                {getFieldDecorator(formFields.voteSumPeople, {
                                    initialValue: activityFormState.voteSumPeople
                                })(
                                    <Radio.Group disabled={preview} options={voteSumPeople} />
                                )}
                            </div>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </div>
    }
}

VoteResult.propTypes = {
    //预览模式
    preview: PropTypes.bool,

    //antd表单对象
    form: PropTypes.object.isRequired,

    //表单字段
    formFields: PropTypes.shape({
        //是否公布投票结果
        voteResultPublic: PropTypes.string.isRequired,
        //排序设置                           
        voteSortingSetting: PropTypes.string.isRequired,
        //排序设置只显示票数前
        voteSortingSettingOnlyShow: PropTypes.string.isRequired,
        //统计显示设置         
        voteStatisticsShowSetting: PropTypes.string.isRequired,
        //总投票人次设置
        voteSumPeople: PropTypes.string.isRequired
    }).isRequired,

    //表单默认数据
    emuns: PropTypes.shape({
        //是否公开结果
        voteResultPublic: PropTypes.arrayOf(PropTypes.object).isRequired,
        //投票结果排序设置
        voteSortingSetting: PropTypes.shape({
            //投票项排序
            normal: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired,
            //票数从大到小
            down: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired,
            //票数从小到大
            up: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired,
            //只显示票数前几
            only: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired
        }),
        //总投票人次设置
        voteStatisticsShowSetting: PropTypes.arrayOf(PropTypes.object).isRequired,
        voteSumPeople: PropTypes.arrayOf(PropTypes.object).isRequired
    }).isRequired,

    //表单model
    activityFormState: PropTypes.shape({
        //是否公开结果
        voteResultPublic: PropTypes.number.isRequired,
        //投票结果排序设置
        voteSortingSetting: PropTypes.shape({
            //排序设置类型
            type: PropTypes.number.isRequired,
            //排序设置类型->只显示票数前
            onlyShow: PropTypes.number.isRequired
        }),
        //统计显示设置
        voteStatisticsShowSetting: PropTypes.number.isRequired,
        //总投票人次设置
        voteSumPeople: PropTypes.number.isRequired,
        voteOptionData: PropTypes.shape({
            groups: PropTypes.oneOfType([
                PropTypes.any,
                PropTypes.arrayOf(PropTypes.object).isRequired
            ])
        }).isRequired
    }).isRequired
}

VoteResult.defaultProps = {
    preview: false
}

export default VoteResult