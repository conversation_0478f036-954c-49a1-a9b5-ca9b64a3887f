.teacher-resources {
  .teacher-resources-header-button {
    .ant-btn {
      height: 36px;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #666666;
    }

    .upload-btn {
      border: 1px solid #d5d5d5;
      margin-right: 24px;
    }

    .del-btn {
      border: none;
      background: #e2e2e2;
    }

    .active-del-btn {
      background: #f46e65;
      color: #ffffff;
    }
  }

  &-content {
    padding: 34px 31px;

    &-search {
      &-org {
        margin-bottom: 32px;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4a4a4a;

        .ant-btn-link {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #359af7;
        }
      }

      &-form {
        .ant-form-item {
          margin-right: 24px;

          &-label {
            margin-bottom: 24px;

            label {
              font-size: 16px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #4a4a4a;
            }
          }
        }

        .ant-select-selection {
          min-width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-select-selection__rendered {
          line-height: 36px;
        }

        .ant-input {
          width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-btn-primary {
          height: 36px;
          background: #f46e65;
          border-radius: 4px;
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
        }
      }
    }

    &-table {
      margin-top: 9px;

      .ant-btn-link {
        color: #359af7;
      }

      .ant-table-thead {
        th {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #3d3d3d;
          text-align: center;
          background: #f3f3f3;
        }
      }

      .teacher-resources-table-title {
        width: 70px;
        height: 84px;
        // overflow: hidden;
        // text-overflow: ellipsis; //文本溢出显示省略号
        // /*! autoprefixer: off */
        // -webkit-box-orient: vertical;
        // /* autoprefixer: on */
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
      }

      .ant-pagination {
        margin-top: 20px;
      }
      .suit {
        display: flex;
        justify-content: center;
        color: #666666;
        span {
          display: table;
          margin-left: 6px;
          padding: 4px 6px;
          background: rgba(217, 217, 217, 0.3);
          border-radius: 4px;
        }
      }
    }
  }
}
