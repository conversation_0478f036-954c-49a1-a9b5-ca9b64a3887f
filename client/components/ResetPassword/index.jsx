import React, { useState } from "react";
import { Modal, Form, Input, Button, message, ConfigProvider } from "antd";
import { updateUserPwd } from "client/apis/cadre-portrait";
import md5 from "js-md5";
import zhCN from "antd/lib/locale-provider/zh_CN";
const PasswordResetModal = ({ visible, onClose, headers, form, onConfirm }) => {
  const { getFieldDecorator, validateFields, getFieldValue } = form;


  const handleSubmit = () => {
    validateFields(async (err, values) => {
      console.log("🚀 ~ validateFields ~ err:", err)
      if (!err) {
        // 新旧密码比对
        const { password, password_old, confirmPassword } = values;

        if (password !== confirmPassword) {
          message.error("两次密码不一致");
          return;
        }

        // 提交重置密码
        // TODO
        const _uid = sessionStorage.getItem("_uid") || headers._uid;
        const res = await updateUserPwd({
          sys_user_id: _uid,
          password_old: md5(password_old),
          password: md5(password),
        }, { headers });
        if (res.data.code === 0) {
          message.success("密码重置成功", 1, () => {
            onConfirm();

            onClose();
          });
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const validateByRegex = (password) => {
    // 定义正则表达式
    const passwordRegex =
      /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,30}$/

    return passwordRegex.test(password)
  }

  const validatePassword = async (_rule, value, callback) => {
    try {
      if (value === '') {
        return callback('请输入密码')
      } else {
        const res = validateByRegex(value)

        if (res) {
          return callback()
        } else {
          return callback('密码应包含大小写字母、数字和特殊字符中的至少三种，且长度为8-30个字符。')
        }
      }
    } catch (err) {
      console.log(err)
    }
  }
  return (
    <ConfigProvider locale={zhCN}>
      <Modal
        title="重置密码"
        visible={visible}
        onCancel={onClose}
        onOk={handleSubmit}
        destroyOnClose

      >
        <Form layout="vertical">
          <Form.Item
            name="password_old"
            label="旧密码"
            colon
          >
            {getFieldDecorator("password_old", {
              rules: [{ required: true, message: "请输入旧密码!" }],
            })(<Input autocomplete="new-password" />)}
          </Form.Item>
          <Form.Item
            name="password"
            label="新密码"
            colon
          >
            {getFieldDecorator("password", {
              rules: [{
                required: true, trigger: 'onChange', validator: validatePassword
              }],
            })(<Input.Password autocomplete="new-password" />)}
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            colon
          >
            {getFieldDecorator("confirmPassword", {
              rules: [{
                required: true, trigger: 'onChange', validator: (rule, value, callback) => {
                  try {
                    if (!value || !value.trim()) {
                      return callback('请再次确认新密码!')
                    } else {
                      return value !== getFieldValue('password') ? callback('两次密码不一致!') : callback()
                    }
                  } catch (err) {
                    console.log(err)
                  }
                }
              }],
            })(<Input.Password />)}
          </Form.Item>
        </Form>
      </Modal>

    </ConfigProvider>
  );
};

export default Form.create()(PasswordResetModal);
