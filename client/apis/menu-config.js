import { http } from 'client/tool/axios';
import { ucHost } from "./config";


/**
 * 获取新闻列表
 * http://*************:38080/doc-view-98.html
 */
export const getMenuList = (params) => {
  return http.get(`${ucHost}/uc/menu/query-list`, params);
};


/**
 * 获取菜单详情
 * http://*************:38080/doc-view-99.html
 */
export const getMenuDetails = (params) => {
  return http.get(`${ucHost}/uc/menu/query`, {
    params,
  });
};

/**
 * 新增菜单
 * http://*************:38080/doc-view-94.html
 */
export const addMenu = (data) => {
  return http.post(`${ucHost}/uc/menu/insert`, data);
};

/**
 * 更新菜单
 * http://*************:38080/doc-view-94.html
 */
export const updateMenu = (data) => {
  return http.post(`${ucHost}/uc/menu/update`, data);
};

/**
 * 删除菜单
 * http://*************:38080/doc-view-94.html
 */
export const delMenu = (menu_id) => {
  return http.get(`${ucHost}/uc/menu/del`, {
    menu_id
  });
};