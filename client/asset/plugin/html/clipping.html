<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <script src="/plugin/js/fabric.min.js"></script>
  <title>图片裁剪</title>
  <style>
    body {
      background: #fff;
    }

    #loading {
      display: none;
      position: fixed;
      z-index: 999;
      height: 2em;
      width: 2em;
      overflow: show;
      margin: auto;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    }

    #loading:before {
      content: '';
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3);
    }

    #loading:not(:required) {
      font: 0/0 a;
      color: transparent;
      text-shadow: none;
      background-color: transparent;
      border: 0;
    }

    #loading:not(:required):after {
      content: '';
      display: block;
      font-size: 10px;
      width: 1em;
      height: 1em;
      margin-top: -0.5em;
      -webkit-animation: spinner 1500ms infinite linear;
      -moz-animation: spinner 1500ms infinite linear;
      -ms-animation: spinner 1500ms infinite linear;
      -o-animation: spinner 1500ms infinite linear;
      animation: spinner 1500ms infinite linear;
      border-radius: 0.5em;
      -webkit-box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
      box-shadow: rgba(255, 255, 255, 1) 1.5em 0 0 0, rgba(255, 255, 255, 1) 1.1em 1.1em 0 0, rgba(255, 255, 255, 1) 0 1.5em 0 0, rgba(255, 255, 255, 1) -1.1em 1.1em 0 0, rgba(255, 255, 255, 1) -1.5em 0 0 0, rgba(255, 255, 255, 1) -1.1em -1.1em 0 0, rgba(255, 255, 255, 1) 0 -1.5em 0 0, rgba(255, 255, 255, 1) 1.1em -1.1em 0 0;
    }

    @-webkit-keyframes spinner {
      0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @-moz-keyframes spinner {
      0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @-o-keyframes spinner {
      0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spinner {
      0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    * {
      padding: 0px;
      margin: 0px;
      box-sizing: content-box;
    }

    .button {
      display: inline-block;
      background: #F7F8F9;
      line-height: 40px;
      border: none;
      margin-right: 30px;
      text-align: center;
      font-size: 16px;
      border-radius: 5px;
      border: 1px solid #E5E5E5;
      cursor: pointer;
    }

    #tool {
      position: fixed;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0px;
      width: 530px
    }

    #upload {
      position: relative;
      width: 130px;
      height: 40px;
      cursor: pointer;
    }

    #submit {
      width: 100px;
      height: 40px;
    }

    #remove {
      width: 100px;
      height: 40px;
    }

    #cancel {
      width: 100px;
      height: 40px;
      margin-right: 0px;
    }

    #file {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      opacity: 0;
    }

    .desc {
      height: 50px;
      color: #ffff;
      font-size: 20px;
      position: absolute;
      top: 73px;
    }
  </style>
</head>

<body>

  <canvas id="c"></canvas>
  <div id="tool">
    <span id="upload" class="button">选择图片<input type="file" id="file" /></span>
    <span id="submit" class="button">提交图片</span>
    <span id="remove" class="button">删除图片</span>
    <span id="cancel" class="button">关闭</span>
    <div class="desc"><span id="title"></span>(<span id="size"> </span>px、jpg或png)</div>
    <!-- <div class="desc">新闻聚焦图(<span id="size"> </span>px、jpg或png)</div> -->
    <!-- <select id="zoom">
      <option value="25">25%</option>
      <option value="50">50%</option>
      <option value="75">75%</option>
      <option value="100">100%</option>
    </select> -->
  </div>
  <div id="loading">Loading&#8230;</div>
  <script src="/plugin/js/clipping.js"></script>
</body>

</html>