import React, { Component } from "react";
import { Form, Modal, Radio, Input, message as Message, Button } from "antd";
import ContentItem from "./content-item";
import ContinueModal from "./continue-modal";
import "./add-modal.less";

import propTypes from "prop-types";

class AddModal extends Component {
  constructor() {
    super();
    this.state = {
      continueVisible: false
    }
    this.showContinueModal = this.showContinueModal.bind(this);
    this.hideContinueModal = this.hideContinueModal.bind(this);
  }

  showContinueModal() {
    this.setState({
      continueVisible: true
    });
  }

  hideContinueModal(callback) {
    this.setState({
      continueVisible: false
    }, () => {
      if (callback) {
        callback();
      }
    });
  }

  submitHandler() {
    const _this = this;
    const { form, addContent, isEdit, hideAddModal } = this.props;
    const { validateFields, getFieldValue } = form;
    validateFields((error, values) => {
      if (!error) {
        // 当处理方式不是填写内容时，装入选项
        if (getFieldValue("type") !== 1) {
          const opts = _this.contentItem.exportData();
          if (!opts || opts.length === 0) {
            Message.error("请设置选项");
            return;
          }
          // 当为多选题，并且选项少于两条时给与提示
          if (getFieldValue("type") === 2 || getFieldValue("type") === 3) {
            if (opts.length < 2) {
              Message.error("多选至少需要两个选项");
              return;
            }
          }
          values.opts = opts;
        } else {
          delete values.opts;
        }
        // 将数据存储到外层父组件中(第二个参数为true时表示为编辑状态)
        // console.log(values);
        addContent(values, isEdit);
        if (isEdit) {
          Message.success("编辑成功");
          hideAddModal();
        } else {
          // 继续添加确认框
          _this.showContinueModal();
        }
      }
    });
  }


  render() {
    const FormItem = Form.Item;
    const RadioGroup = Radio.Group;
    const Textarea = Input.TextArea;
    const { continueVisible } = this.state;
    const { visible, hideAddModal, form, modalType = 1, isEdit, currentEdit } = this.props;
    const { getFieldDecorator, getFieldValue, resetFields } = form;
    const modalProps = {
      destroyOnClose: true,
      wrapClassName: "add-modal",
      width: 800,
      // title: `${isEdit ? "编辑" : "新增"}${modalType === 1 ? "议题" : "报表"}内容`,
      title: `${isEdit ? "编辑" : "新增"}${modalType === 1 ? "任务" : "报表"}内容`,
      visible,
      footer: null,
      onCancel() {
        hideAddModal();
      }
    };
    const formItemLayout = {
      labelCol: {
        span: 4
      },
      wrapperCol: {
        span: 20
      }
    }
    const continueModalProps = {
      visible: continueVisible,
      hideContinueModal: this.hideContinueModal,
      hideAddModal,
      resetFields
    }
    return (
      <Modal {...modalProps} >
        <ContinueModal {...continueModalProps} />
        <Form>
          <FormItem label="完成方式" className="no-margin" {...formItemLayout}>
            {
              getFieldDecorator("type", {
                initialValue: isEdit ? currentEdit.type : 1,
                rules: [
                  { required: true, message: "请选择完成方式" }
                ]
              })(
                <RadioGroup>
                  <Radio value={1}>填写内容</Radio>
                  <Radio value={2}>单选</Radio>
                  <Radio value={3}>多选</Radio>
                </RadioGroup>
              )
            }
          </FormItem>
          <FormItem label="内容名称" {...formItemLayout}>
            {
              getFieldDecorator("name", {
                initialValue: isEdit ? currentEdit.name : "",
                rules: [
                  { required: true, message: "请填写内容" },
                  { max: 500, message: "内容名称长度超过500" }
                ]
              })(
                <Textarea placeholder="请输入..." rows={4} />
              )
            }
          </FormItem>
        </Form>
        {
          getFieldValue("type") !== 1 &&
          <div className="ant-row ant-form-item no-margin">
            <div className="ant-col-4 ant-form-item-label">
              <label className="" title="选项内容">选项内容</label>
            </div>
            <div className="ant-col-20 ant-form-item-control-wrapper">
              <div className="ant-form-item-control">
                <span className="ant-form-item-children">
                  {/* 这里放置选项组件 */}
                  <ContentItem
                    isEdit={isEdit}
                    opts={
                      isEdit ?
                        ((currentEdit.opts && Array.isArray(currentEdit.opts) && currentEdit.opts.length !== 0) ? currentEdit.opts : [
                          { opts_name: "", seq: 1, key: 1 },
                          { opts_name: "", seq: 2, key: 2 }
                        ]) : [
                          { opts_name: "", seq: 1, key: 1 },
                          { opts_name: "", seq: 2, key: 2 }
                        ]
                    }
                    wrappedComponentRef={
                      (contentItem) => {
                        this.contentItem = contentItem;
                      }
                    } />
                </span>
              </div>
            </div>
          </div>
        }
        <div className="buttons-wrapper">
          <Button type="primary" onClick={() => this.submitHandler()}>提交</Button>
          <Button onClick={() => hideAddModal()}>取消</Button>
        </div>
      </Modal >
    )
  }
}

AddModal.propTypes = {
  // 控制模态框可见与否
  visible: propTypes.bool,
  // 隐藏本模态框
  hideAddModal: propTypes.func,
}

export default Form.create()(AddModal);