/*
 * @Description:
 * @Author: baichao
 * @Date: 2020-12-10 08:52:10
 * @LastEditTime: 2021-02-08 11:45:36
 * @LastEditors: baichao
 */
const { http } = require("tool/axios");
import { getParam } from "tool/util";
import { host, CDN } from "./config";
import { message } from "antd";
// 获取saas前端配置
export const getInitConfig = async () => {
  let configs = {};
  const regionId = getParam().region_id;
  /*  if (regionId == 21) {
    configs = {
      regionId: 21,
      // 系统名称
      systemName: "机关智慧党建",
      // 网站 title
      documentTitle: "水环先锋智慧党建平台",
      // 登录页form表单背景  扩展名：png
      // loginFromBg: require(`../view/login/images/customization/login-from-bg21.png`),
      // 登录页背景  扩展名：jpg
      loginBg: require(`../view/login/images/customization/login-bg21.png`),
    };
  } else {
    configs = {
      regionId: regionId || region,
      // 系统名称
      systemName: loginConfig.systemName || "机关智慧党建",
      // 网站 title
      documentTitle: loginConfig.documentTitle || "智慧党建服务平台",
      // 浏览器选项卡显示图标
      documentIcon: require("../asset/images/favicon.png"),
      // 登录页form表单背景  扩展名：png
      // loginFromBg: require(`../view/login/images/customization/login-from-bg${
      //   region || ""
      // }.png`),
      // 登录页背景  扩展名：jpg
      loginBg: require(`../view/login/images/customization/login-bg${
        loginConfig.customLoginBg ? region || "" : ""
      }.jpg`),
      // 登录页底部备案号
      // icp: loginConfig.loginIcp,
    };
  } */
  configs = {
    regionId: regionId || region,
    // 系统名称
    systemName: loginConfig.systemName || "机关智慧党建",
    // 网站 title
    documentTitle: loginConfig.documentTitle || "智慧党建服务平台",
    // 浏览器选项卡显示图标
    documentIcon: require("../asset/images/favicon.png"),
    // 登录页底部备案号
    icp: loginConfig.loginIcp,
  };
  try {
    // 登录页form表单背景  扩展名：png
    configs.loginFromBg = require(`../view/login/images/customization/login-from-bg${
      region || ""
    }.png`);
  } catch (error) {
    console.log("🚀 ~ file: saas.js ~ line 54 ~ error", error);
  }
  try {
    // 登录页背景  扩展名：jpg
    configs.loginBg = require(`../view/login/images/customization/login-bg${
      loginConfig.customLoginBg ? region || "" : ""
    }.jpg`);
  } catch (error) {
    console.log("🚀 ~ file: saas.js ~ line 46 ~ error", error);
  }
  try {
    const { data: res } = await http.get(`${host}/app/config/getConfig/pc`);
    if (res.code === 0 && res.data) {
      const data = res.data;
      configs.regionId = data.region_id;
      configs.systemName = data.system_name;
      configs.documentTitle = data.document_title;
      configs.documentIcon = data.icon;
      configs.loginFromBg = data.login_bg;
      configs.homeBg = data.home_bg;
      configs.icp = data.icp;
    } else {
      message.error(res.code === 0 ? "您的所在组织未激活" : res.message);
    }
    document.title = configs.documentTitle; // 标题
    // 图标
    let $favicon = document.querySelector('link[rel="shortcut icon"]');
    if ($favicon !== null) {
      $favicon.href = configs.documentIcon.includes("static")
        ? configs.documentIcon
        : `${CDN}/${configs.documentIcon}`;
    } else {
      $favicon = document.createElement("link");
      $favicon.rel = "shortcut icon";
      $favicon.href = configs.documentIcon.includes("static")
        ? configs.documentIcon
        : `${CDN}/${configs.documentIcon}`;
      document.head.appendChild($favicon);
    }
    return configs;
  } catch (error) {
    message.error(`${error}`);
    return configs;
  }
};
