import React, { useEffect, useState, useRef } from "react";
import {
  Form,
  Input,
  Row,
  Col,
  Select,
  DatePicker,
  Radio,
  Cascader,
  Upload,
} from "antd";
import "./index.less";
import avatarPng from "../../images/avatar.png";
import moment from "moment";
const FormItem = Form.Item;
const { Option } = Select;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;

import {
  queryByCode,
  queryDicitemArea,
  queryJobInfo,
  uploadAvatar,
} from "client/apis/cadre-portrait";
import { uploadFile } from "client/apis/file";
import { CDN as cdn } from "client/apis/config"
import { convertTimeValues } from "client/tool/util"

function index(props) {
  const { form, data, org_id, disabled } = props;

  const area = useRef();

  const { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue } =
    form;

  const [positionList, setPositionList] = useState([]);

  const [codeMap, setCodeMap] = useState({
    cadreTypeOption: [],
    ethnicOption: [],
    politicalTypeOption: [],
    currentRankOption: [],
    sourceOption: [],
  });

  const [areaTree, setAreaTree] = useState([]);

  const wrapperCol = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
  };

  useEffect(() => {
    if (!data) return;

    const {
      birthday,
      current_job_time,
      current_rank_time,
      joining_time,
      work_time,
      native,
      birthplace,
    } = data;
    form.setFieldsValue({
      ...convertTimeValues(data),
      native: native
        ? native.split("-").map((item) => Number(item))
        : undefined,
      birthplace: birthplace
        ? birthplace.split("-").map((item) => Number(item))
        : undefined,
      current_job_text: data.current_job,
    });

    initAreaKey(native);

    if (birthplace) {
      initAreaKey(birthplace);
    }
  }, [data]);

  useEffect(() => {
    initCode(2001, "cadreTypeOption");
    initCode(1004, "ethnicOption");
    initCode(1013, "politicalTypeOption");
    initCode(2003, "currentRankOption");
    initCode(96140, "sourceOption");
    initPosition();

    initAreaCode({
      type: 1,
    });
  }, []);

  const initPosition = () => {
    queryJobInfo({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setPositionList(res.data.data);
      }
    });
  };
  const initAreaKey = async (native) => {
    await initAreaCode({
      type: 1,
    });
    if (!native) return;

    const nativeList = native.split("-");

    nativeList.forEach((key, index) => {
      loadData([
        {
          adcode: Number(key),
          type: index + 1,
        },
      ]);
    });
  };
  const initCode = async (code, key) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      setCodeMap((codeMap) => {
        codeMap[key] = res.data.data;

        return { ...codeMap };
      });
    }
  };

  //
  const initAreaCode = async ({ type, adcode }) => {
    const res = await queryDicitemArea({ type, adcode });
    if (res.data.code === 0) {
      const data = res.data.data.map((item) => {
        return { ...item, isLeaf: false, type: 1 };
      });
      setAreaTree(data);

      area.current = data;
    }
  };

  const loadData = async (selectedOptions, type) => {
    const selectOption = selectedOptions[selectedOptions.length - 1];

    const res = await queryDicitemArea({
      type: selectOption.type + 1,
      adcode: selectOption.adcode,
    });

    if (res.data.code === 0) {
      // 根据adcode找寻下级

      if (!res.data.data.length) {
        delete selectOption.isLeaf;

        setAreaTree([...areaTree]);

        return;
      }

      const findByCode = (areaTree, children) => {
        areaTree.forEach((item) => {
          if (item.children) {
            findByCode(item.children, children);
          } else {
            if (item.adcode === selectOption.adcode) {
              item.children = children.map((item) => {
                return { ...item, isLeaf: false, type: selectOption.type + 1 };
              });
            }
          }
        });
      };
      findByCode(area.current, res.data.data);

      console.log("🚀 ~ loadData ~ areaTree:", area.current);

      setAreaTree([...area.current]);
    }
  };

  const onChange = () => {
    console.log("Onchange");
  };

  const onUpload = async (file) => {
    const formdata = new FormData();

    formdata.append("avatar", file);

    formdata.append("head_url", data ? data.head_url : undefined);

    const res = await uploadAvatar(formdata);

    if (res.data.code === 0) {
      const data = res.data.data;
      setFieldsValue({
        head_url: data,
      });
    }

    return false;
  };

  const fieldNames = {
    label: "area_name",
    value: "adcode",
    children: "children",
  };

  const getAvatar = (head_url) => {
    return head_url ? `${cdn}/${head_url}` : avatarPng;
  };
  const children = positionList.map((item) => {
    return (
      <Option
        onClick={() => {
          const params = getFieldsValue();
          setFieldsValue({
            has_divided: item.has_divided,
            current_job_text: item.job_name,
            job_id: item.job_id,
          });
        }}
        value={String(item.job_id)}
      >
        {item.job_name}
      </Option>
    );
  });
  return (
    <div className="base-info-box">
      <Row span={24}>
        {getFieldDecorator("user_id", {})(<Input type="hidden" />)}
        {getFieldDecorator("has_divided")(<Input type="hidden" />)}
        {getFieldDecorator("head_url")(<Input type="hidden" />)}
        <Col span={9}>
          <FormItem label="姓名" {...wrapperCol} required colon>
            {getFieldDecorator("name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<Input placeholder="请输入" disabled={disabled} />)}
          </FormItem>
          <FormItem label="干部类别" {...wrapperCol} required colon>
            {getFieldDecorator("category", {
              rules: [{ required: true, message: "请选择干部类别" }],
            })(
              <Select
                style={{ width: 120 }}
                placeholder="请输入"
                disabled={disabled}
              >
                {codeMap.cadreTypeOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="籍贯" {...wrapperCol} required colon>
            {getFieldDecorator("native", {
              rules: [{ required: true, message: "请选择籍贯" }],
            })(
              <Cascader
                disabled={disabled}
                placeholder="请输入"
                fieldNames={fieldNames}
                options={areaTree}
                loadData={(option) => {
                  loadData(option, 1);
                }}
                changeOnSelect
              />
            )}
          </FormItem>
          <FormItem label="民族" {...wrapperCol} required colon>
            {getFieldDecorator("ethnic", {
              rules: [{ required: true, message: "请选择民族" }],
            })(
              <Select
                style={{ width: 120 }}
                placeholder="请输入"
                disabled={disabled}
              >
                {codeMap.ethnicOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="入党时间" {...wrapperCol} colon>
            {getFieldDecorator("joining_time", {
              rules: [{ required: false, message: "请选择入党时间" }],
            })(<MonthPicker disabled={disabled} placeholder="请输入" />)}
          </FormItem>
        </Col>
        <Col span={9}>
          <FormItem label="性别" {...wrapperCol} required colon>
            {getFieldDecorator("gender", {
              rules: [{ required: true, message: "请选择性别" }],
            })(
              <Radio.Group placeholder="请输入" disabled={disabled}>
                <Radio value={"1"}>男</Radio>
                <Radio value={"2"}>女</Radio>
              </Radio.Group>
            )}
          </FormItem>
          <FormItem label="出生年月" {...wrapperCol} required colon>
            {getFieldDecorator("birthday", {
              rules: [{ required: true, message: "请选择出生年月" }],
            })(<MonthPicker placeholder="请输入" disabled={disabled} />)}
          </FormItem>
          <FormItem label="出生地" {...wrapperCol} required colon>
            {getFieldDecorator("birthplace", {
              rules: [{ required: true, message: "请选择出生地" }],
            })(
              <Cascader
                disabled={disabled}
                placeholder="请输入"
                fieldNames={fieldNames}
                options={areaTree}
                loadData={(option) => {
                  loadData(option, 2);
                }}
                // onChange={onChange}
                changeOnSelect
              />
            )}
          </FormItem>
          <FormItem label="政治面貌" {...wrapperCol} required colon>
            {getFieldDecorator("political_type", {
              rules: [{ required: true, message: "请选择政治面貌" }],
            })(
              <Select
                disabled={disabled}
                style={{ width: 120 }}
                placeholder="请选择"
              >
                {codeMap.politicalTypeOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="参加工作时间" {...wrapperCol} colon>
            {getFieldDecorator("work_time", {
              rules: [{ required: false, message: "请选择参加工作时间" }],
            })(<MonthPicker placeholder="请输入" disabled={disabled} />)}
          </FormItem>
        </Col>
        <Col span={6}>
          <div className="avatar-box">
            <div className="title">头像</div>
            <div className="image">
              <Upload
                beforeUpload={onUpload}
                disabled={disabled}
                showUploadList={false}
              >
                <img src={getAvatar(getFieldValue("head_url"))} />
                <span className="upload">上传头像</span>
              </Upload>
            </div>
          </div>
        </Col>
      </Row>
      <Row span={24}>
        <Col span={9}>
          <FormItem label="熟悉专业和特长" {...wrapperCol} colon>
            {getFieldDecorator("profession_specialty", {
              rules: [{ required: false, message: "请输入熟悉专业和特长" }],
            })(<Input placeholder="请输入" disabled={disabled} />)}
          </FormItem>
          <FormItem label="专业技术职务" {...wrapperCol} colon>
            {getFieldDecorator("technical_position", {
              rules: [{ required: false, message: "请输入专业技术职务" }],
            })(<Input placeholder="请输入" disabled={disabled} />)}
          </FormItem>
        </Col>
        <Col span={9}>
          <FormItem label="技能证书和称号" {...wrapperCol} colon>
            {getFieldDecorator("skill_info", {
              rules: [{ required: false, message: "请输入技能证书和称号" }],
            })(<Input placeholder="请输入" disabled={disabled} />)}
          </FormItem>
          <FormItem label="兴趣爱好" {...wrapperCol} colon>
            {getFieldDecorator("hobby", {
              rules: [{ required: false, message: "请输入兴趣爱好" }],
            })(<Input placeholder="请输入" disabled={disabled} />)}
          </FormItem>
        </Col>
      </Row>
      <Row span={24}>
        <FormItem
          label="现任职务"
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          required
          colon
        >
          <Row span={24}>
            <Col span={6}>
              {getFieldDecorator("job_id")(<Input type="hidden" />)}
              <Form.Item>
                {getFieldDecorator("current_job", {
                  rules: [{ required: true, message: "请选择现任职务" }],
                })(
                  <Select
                    style={{ width: "100%" }}
                    placeholder="请选择"
                    disabled={disabled}
                    allowClear
                  >
                    {children}
                  </Select>
                )}
              </Form.Item>
            </Col>
            <Col span={15} offset={1}>
              {getFieldDecorator(
                "current_job_text",
                {}
              )(<Input placeholder="请输入" disabled={disabled} />)}
              <div>注：此处填写信息用于领导职务显示</div>
            </Col>
          </Row>
        </FormItem>
      </Row>
      <Row span={24}>
        <Col span={9}>
          <FormItem label="任现职务时间" {...wrapperCol} required colon>
            {getFieldDecorator("current_job_time", {
              rules: [{ required: true, message: "请选择任现职务时间" }],
            })(<MonthPicker disabled={disabled} />)}
          </FormItem>{" "}
          <FormItem label="干部来源" {...wrapperCol} colon>
            {getFieldDecorator("source", {
              rules: [{ required: false, message: "请选择干部来源" }],
            })(
              <Select placeholder="请选择" disabled={disabled}>
                {codeMap.sourceOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
        </Col>
        <Col span={9}>
          <FormItem label="干部职级" {...wrapperCol} required colon>
            {getFieldDecorator("current_rank", {
              rules: [{ required: true, message: "请选择干部职级" }],
            })(
              <Select placeholder="请选择" disabled={disabled}>
                {codeMap.currentRankOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>{" "}
          <FormItem label="现职级任职时间" {...wrapperCol} required colon>
            {getFieldDecorator("current_rank_time", {
              rules: [{ required: true, message: "请选择现职级任职时间" }],
            })(<MonthPicker placeholder="请选择" disabled={disabled} />)}
          </FormItem>{" "}
        </Col>
      </Row>
    </div>
  );
}

export default index;
