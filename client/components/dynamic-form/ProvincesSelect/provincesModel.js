import { getProvincesList } from "client/apis/dynamic-form";
import { getArea, getNativeArea } from "client/apis/organize";


const provincesModel = {
  namespace: 'provincesModel',
  state: {
    loading: false,
    data: [],
  },
  reducers: {
    updateData(state, { payload }) {
      return { ...state, data: payload };
    },
    updateStatus(state, { payload }) {
      return { ...state, dataStatus: payload };
    },
  },
  effects: {
    *initData({ payload }, { put, call, select }) {
      const { loading, data } = yield select(
        (state) => state.provincesModel,
      );
      if (loading) {
        return;
      }
      if (data.length > 0) {
        return
      }
      yield put({ type: 'updateStatus', payload: true });
      try {
        const list = yield call(initArea);
        yield put({ type: 'updateData', payload: list });
      } catch (error) {
        console.error(error);
      }
      yield put({ type: 'updateStatus', payload: false });

    }
  }
}

const initArea = () => {
  return new Promise((resolve, reject) => {
    getArea().then(res => {
      const { data, status } = res.data;
      if (status === 200) {
        Promise.all(data.map(async (item) => {
          const child = (await getNativeArea({ parentid: item.adcode })).data.data;
          return child.map(({ area_name, adcode }) => ({ label: area_name, value: String(adcode) }));
        })).then(child_list => {
          const formatData = data.map(({ area_name, adcode }, index) => ({
            label: area_name,
            value: String(adcode),
            children: child_list[index]
          }));
          resolve(formatData)
        }).catch((err) =>  {
          reject(err)
        });
      } else {
        reject(res.data)
      }
    }).catch((err) => {
      reject(err)
    });
  })
}


export default provincesModel;