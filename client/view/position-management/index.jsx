import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";
import {
  addPosition,
  deletePosition,
  getPositionList,
  queryByCode,
  updatePosition,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import { useEffect, useRef, useState } from "react";
import "./index.less";
const { Option } = Select;
const FormItem = Form.Item;
const PositionManagement = ({ form, history }) => {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pageState, setPageState] = useState({
    org_type: -1,
    org_name: "",
    org_id: -1,
  });
  const [exportLoading, setExportLoading] = useState(false);
  const [position, setPosition] = useState({
    name: "",
  });
  const [buttonStatus, setButtonStatus] = useState({
    save: false,
    modalSave: false,
  });
  const [codeMap, setCodeMap] = useState({
    categoryOption: [],
    classificationOption: [], //分类
  });
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    initCodeMap(96160, "categoryOption");
    initCodeMap(96400, "classificationOption");
  }, []);

  const initCodeMap = async (code, key) => {
    const res = await queryByCode({
      code,
    });
    if (res.data.code === 0) {
      codeMap[key] = res.data.data;
      setCodeMap({
        ...codeMap,
      });
    }
  };

  const initData = async (org_id) => {
    setLoading(true);
    const res = await getPositionList({ org_id });
    if (res.data.code === 0) {
      setData(res.data.data);
    }
    setLoading(false);
  };

  const initSelectOption = async (org_id) => {
    // const res = await getPositionTypeList({ org_id });
    // console.log("🚀 ~ res:", res);
    // if (res.data.code === 0) {
    //   setSelectList(
    //     res.data.data.map((item) => {
    //       return _optionArray.find((_item) => _item.value === item);
    //     })
    //   );
    // }
  };

  const handleOrgChange = (org) => {
    const { id: org_id, name: org_name, orgType: org_type } = org;
    initData(org_id);
    // initSelectOption(org_id);
    setPageState({ org_id, org_name, org_type });
  };
  //添加
  const handleAdd = () => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 1 }));
  };

  const handleDelete = async (record) => {
    const res = await deletePosition({
      pms_job_id: record.pms_job_id,
    });
    if (res.data.code === 0) {
      message.success("删除成功");
      initData(pageState.org_id);
    } else {
      message.error(res.data.message);
    }
  };

  const handleEdit = (record) => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 2 }));
    setPosition(record);
    setTimeout(() => {
      formRef.current.setFieldsValue(record);
    });
  };

  const formRef = useRef(null);
  //搜索
  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData(1);
  };
  //重置
  const onReset = () => {
    resetFields();
    onSearch();
  };
  //导出
  const onExport = async () => {
    // setExportLoading(true);
    // const params = getFieldsValue();
    // const res = await exportAnnualEval({
    //   ...params,
    //   org_id: org_ids[0],
    // });
    // setExportLoading(false);
  };
  const onInputFile = () => {
    // history.push(
    //   `/import-page?type=2&org_id=${org_ids[0]}&org_name=${org_name}`
    // );
  };
  const handleModalOk = async () => {
    formRef.current.validateFields(async (err, values) => {
      if (err) return;
      setButtonStatus((state) => ({ ...state, modalSave: true }));
      const { page_status } = pageState;
      if (page_status === 1) {
        const res = await addPosition(values);
        if (res.data.code === 0) {
          message.success("新增成功");
          initData(pageState.org_id);
          setPosition({
            type: 4,
            name: "",
          });
        } else {
          message.error(res.data.message);
        }
      } else if (page_status === 2) {
        const res = await updatePosition({
          ...values,
          pms_job_id: position.pms_job_id,
        });
        if (res.data.code === 0) {
          message.success("保存成功");
          setPosition({
            type: 4,
            name: "",
          });
        } else {
          message.error(res.data.message);
        }
      }
      setButtonStatus((state) => ({ ...state, modalSave: false }));
      setVisible(false);
    });
  };

  const handleModalCancel = () => {
    setPosition({
      type: 4,
      name: "",
    });
    setButtonStatus((state) => ({ ...state, modalSave: false }));
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, setFieldsValue } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} ref={formRef}>
        <FormItem label="分类">
          {getFieldDecorator("type", {
            rules: [{ required: true, message: "请选择分类" }],
          })(
            <Select disabled={pageState.page_status === 2}>
              {codeMap.classificationOption.map((item) => (
                <Option key={item.op_key}>{item.op_value}</Option>
              ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="职务全称">
          {getFieldDecorator("name", {
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="职务简称">
          {getFieldDecorator("short_name", {
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="兼职全称">
          {getFieldDecorator("part_job_name", {
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="兼职简称">
          {getFieldDecorator("part_job_short_name", {
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="发文抬头">
          {getFieldDecorator("title")
          (<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="职务类别">
          {getFieldDecorator("category", {
            initialValue: position.category,
            rules: [{ required: true, message: "请选择职务类别" }],
          })(
            <Select>
              {codeMap.categoryOption.map((item) => (
                <Option key={item.op_key}>{item.op_value}</Option>
              ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="职务数量">
          {getFieldDecorator("num")(<InputNumber placeholder="请输入" />)}
        </FormItem>
      </Form>
    );
  });

  const columns = [
    {
      title: "分类",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        const option = codeMap.classificationOption.find(
          (item) => item.op_key === type
        );
        return option ? option.op_value : "";
      },
    },
    {
      title: "职务全称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "职务简称",
      dataIndex: "short_name",
      key: "short_name",
    },
    {
      title: "操作",
      key: "operation",
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => handleDelete(record)}
            okText="是"
            cancelText="否"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div className="position-management">
      <div className="org-tree-box">
        <OrgTree onChange={handleOrgChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "200px" }} placeholder="请输入" />
              )}
            </Form.Item>

            <Form.Item label="干部序列">
              {getFieldDecorator("group_id")(
                <Select
                  style={{ minWidth: "200px" }}
                  placeholder="请选择"
                ></Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading} disabled={true}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={handleAdd} type="primary" icon="plus">
            添加职务
          </Button>
          <Button onClick={onInputFile} className="input-file" disabled={true}>
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="pms_job_id"
        />
      </div>
      <Modal
        title={pageState.page_status === 1 ? "添加职务" : "修改职务"}
        visible={visible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        footer={[
          <Button key="cancel" onClick={handleModalCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={buttonStatus.modalSave}
            onClick={handleModalOk}
          >
            保存
          </Button>,
        ]}
      >
        <ModalForm form={form} />
      </Modal>
    </div>
  );
};

export default Form.create()(PositionManagement);
