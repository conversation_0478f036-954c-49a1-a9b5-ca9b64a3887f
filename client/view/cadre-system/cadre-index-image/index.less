.party-member-image {
  padding: 20px;
  position: fixed;
  top: -100vh;
  left: 0;
  height: 100vh;
  width: 100vw;
  overflow: auto;
  z-index: 10000;
  background: #fff;
  // 菜单样式
  .data-menu {
    display: flex;
    align-items: center;

    .menu-item {
      margin-left: 12px;
      font-size: 18px;
      line-height: 18px;
      font-weight: 500;
      color: #666666;
      cursor: pointer;
      line-height: 28px;

      &.menu-active {
        color: #333333;
        position: relative;
        font-weight: bold;

        &::after {
          content: "";
          display: inline-block;
          width: 70%;
          height: 2px;
          background: #ff2121;
          border-radius: 2px;
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .charts-box {
    width: 1200px;
    // margin: 0 auto;
    .feedback-form {
      font-size: 40px;
      font-weight: bold;
      font-family: "宋体";
      text-align: center;
      margin-bottom: 20px;
    }
    .charts-container {
      width: 100%;
      .charts-row {
        display: flex;
        width: 100%;
        .chart-box {
          flex: 1;
        }

        gap: 20px;
        margin-bottom: 20px;
      }

      .chart-box {
        width: 100%;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.full-width {
          width: 100%;
        }
      }

      .chart-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
      }

      .chart-content {
        min-height: 300px;
      }
    }
  }
  .w-550 {
    .card-box {
      width: 590px;
      height: 100%;
    }
  }
  .h-300 {
    height: 300px;
  }
  .w-1500 {
    .card-box {
      width: 1200px;
      height: 100%;
    }
  }
  .margin-top-20 {
    margin-top: 20px;
  }
}
