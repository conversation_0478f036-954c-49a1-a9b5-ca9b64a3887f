import React, { Component } from "react";
import { Mo<PERSON>, Button } from "antd";
import "./continue-modal.less";

import propTypes from "prop-types";
class ContinueModal extends Component {
  constructor() {
    super();
  }
  render() {
    // console.log(this.props);
    const { hideContinueModal, visible, hideAddModal, resetFields } = this.props;
    const continueModalProps = {
      destroyOnClose: true,
      wrapClassName: "continue-modal",
      width: 400,
      title: null,
      closable: false,
      footer: null,
      visible
    }
    return (
      <Modal {...continueModalProps}>
        <div className="success-message">
          {/* 提交成功，是否继续新增议题内容？ */}
          提交成功，是否继续新增任务内容？
        </div>
        <div className="buttons-wrapper">
          <Button className="continue-button" type="primary" onClick={() => hideContinueModal(resetFields)}>继续新增</Button>
          <Button className="cancel-button" onClick={() => hideContinueModal(hideAddModal)}>完成</Button>
        </div>
      </Modal>
    )
  }
}

ContinueModal.propTypes = {
  // 隐藏本模态框
  hideContinueModal: propTypes.func,
  // 隐藏父级添加议题选项框
  hideAddModal: propTypes.func,
  // 重置父级表单，用于继续添加功能
  resetFields: propTypes.func,
  // 控制本模态框是否显示
  visible: propTypes.bool,
}

export default ContinueModal;