import React, { Component } from 'react';
import { Form, Input, Button, Table, Popconfirm } from 'antd';
import SiderContent from 'client/components/sider-content';
import { fileDownload } from 'client/components/file-download';

class SideView extends Component {
    constructor(props) {
        super(props);
        //本地状态维护查询关键字
        this.state = {
            name: '',
            phone: '',
            team_name: '',
            ticket_name: ''
        }
    }

    render() {
        const {
            currentPlan,
            activityType,
            activityNameListType,
            collapsed,
            isHideWrap,
            onClose,
            modalTitle,
            handleSubmit,
            handleCancel,
            approveList,
            copyToList,
            form,
            // formData,
            peopleOverviewDataSource,
            conditionQuery,
            onPageChange,
            riseRankHandler,
            awardingHandler,
            approvalHandler,
            downloadUsers,
            userInfo,
            baseInfo,
        } = this.props,
            // {
            //     name,
            //     phone,
            //     team,
            //     ticket
            // } = formData,
            params = {
                collapsed,
                onClose,
                title: activityNameListType === 1 ? '待审批' : activityNameListType === 2 ? '预计参加名单' : activityNameListType === 3 ? '实际签到名单' : activityNameListType === 4 ? '颁奖' : '查看人员',
                isHideWrap,
                width: 875,
            },
            FormItem = Form.Item,
            { getFieldDecorator, getFieldsValue } = form;
            //所有列信息
            let allColumns = [
                {
                    title: '序',
                    width: 60,
                    align: 'center',
                    dataIndex: 'index'
                },
                {
                    title: '队伍名称',
                    align: 'center',
                    dataIndex: 'team_name'
                },
                {
                    title: '姓名',
                    align: 'center',
                    dataIndex: 'user_name'
                },
                {
                    title: '电话',
                    align: 'center',
                    dataIndex: 'phone'
                },
                {
                    title: '所在组织',
                    align: 'center',
                    dataIndex: 'org_name'
                },
                {
                    title: '所在党委',
                    align: 'center',
                    dataIndex: 'party_org_name'
                },
                {
                    title: '票名',
                    align: 'center',
                    dataIndex: 'offline_ticket_name'
                },
                {
                    title: '票券',
                    align: 'center',
                    dataIndex: 'serial_num'
                },
                {
                    title: '审批',
                    align: 'center'
                },
                {
                    title: '晋级',
                    align: 'center'

                },
                {
                    title: '颁奖',
                    align: 'center'
                }
            ];
        // 屏蔽颁奖
        if (baseInfo.org_id !== userInfo.root_oid) {
            allColumns = allColumns.filter((item) => item.title !== '颁奖');
        }
        // 屏蔽晋级
        if (userInfo.oid !== baseInfo.org_id) {
           allColumns = allColumns.filter((item) => item.title !== '晋级');
        }
        // 屏蔽所在组织所在党委
        if (activityNameListType !== 2 && activityNameListType !== 3) {
           allColumns = allColumns.filter((item) => {
               return (item.dataIndex !== 'org_name' && item.dataIndex !== 'party_org_name');
           });
        }

        //获取是否有审批操作列
        const fetchApproveHandleColumnsRender = () => {
            return (value, row, index) => {
                return (
                    <div>
                        <Popconfirm
                            title={
                                <span style={{ color: '#009944' }}>是否确认
                                        {row.user_name && `【${row.user_name}】`}
                                    通过审批
                                    </span>
                            }
                            okText={'确定'} cancelText={'取消'}
                            //"status": "int|必填|2.已审批通过 4,审核不通过"
                            onConfirm={() => approvalHandler(row, index, 2)}>
                            <a href="javascript:void(0)">
                                通过
                                </a>
                        </Popconfirm>
                        &nbsp;&nbsp;
                            <Popconfirm
                            title={
                                <span style={{ color: '#EB6100' }}>是否确认
                                        {row.user_name && `【${row.user_name}】`}
                                    不通过审批
                                    </span>
                            }
                            okText={'确定'} cancelText={'取消'}
                            //"status": "int|必填|2.已审批通过 4,审核不通过"
                            onConfirm={() => approvalHandler(row, index, 4)}>
                            <a href="javascript:void(0)">
                                不通过
                                </a>
                        </Popconfirm>
                    </div>
                )
            }
        }

        //获取是否有晋级操作列
        const riseHandleColumnRender = (activityType) => {
            const {org_id} = baseInfo;
            const {oid} = userInfo;
            if(org_id !== oid) {return null;}
            return (value, row, index) => {
                const object = {
                    children: (
                        <div>
                            {
                                (row.status === 1 || !row.status) && <Popconfirm
                                    title={
                                        <span style={{ color: '#009944' }}>
                                            是否确认{(activityType === 1 && row.user_name) ? `【${row.user_name}】` : (activityType === 2 && row.team_name) ? `【${row.team_name}】` : ''}晋级
                                                 </span>
                                    }
                                    okText={'确定'} cancelText={'取消'}
                                    //"status": "int|必填|2.已审批通过 4,审核不通过"
                                    onConfirm={() => riseRankHandler(row, index)}>
                                    <a href="javascript:void(0)">
                                        晋级
                                             </a>
                                </Popconfirm>
                            }
                            {
                                (row.status === 2) &&
                                <span style={{ color: '#009944' }}>已晋级</span>
                            }
                        </div>
                    ),
                    props: {
                        rowSpan: (row.rowspan || row.rowspan === 0) ? row.rowspan : 1
                    }
                }
                return object;
            };
        }

        //获取是否有颁奖操作列
        const awardHandleColumnRender = () => {
            if(baseInfo.org_id !== userInfo.root_oid) {return null};
            return (value, row, index) => {
                return (
                    <span>
                        <Popconfirm
                            title={
                                <span style={{ color: '#009944' }}>是否确认
                                    向{row.user_name && `【${row.user_name}】`}颁发奖品
                                </span>
                            }
                            okText={'确定'} cancelText={'取消'}
                            //"status": "int|必填|2.已审批通过 4,审核不通过"
                            onConfirm={() => awardingHandler(row, index)}>
                            <a href="javascript:void(0)">
                                {
                                    (row.r_status === 1 || !row.r_status) ? '颁发奖品' : '继续颁发'
                                }
                            </a>
                        </Popconfirm>
                    </span>
                )
            }
        }

        //获取表格所有列数组
        const fetchTargetColumns = (activityType, activityNameListType) => {
            // console.log(this.props);
            let { currentPlan } = this.props;
            // console.log(currentPlan);
            const { is_sign_in, is_level_up } = currentPlan || {};
            //拷贝所有列信息
            let copyAllColumns = JSON.parse(JSON.stringify(allColumns));
            let result = [];

            for (let i = 0, len = copyAllColumns.length; i < len; i++) {
                if (copyAllColumns[i].title === '序') {
                    copyAllColumns[i].render = (value, row, index) => {
                        // console.log(row.rowspan);
                        const object = {
                            children: (value || value === 0) ? ++value : row.key,
                            props: {
                                rowSpan: row.rowspan
                            }
                        }
                        return object;
                    }
                }
                //当为个人活动时，不存在队伍名称一列
                if (activityType === 1 && copyAllColumns[i].title === '队伍名称') {
                    continue;
                } else if (activityType === 2) {
                    if (copyAllColumns[i].title === '队伍名称' || copyAllColumns[i].title === '票名' || copyAllColumns[i].title === '票券') {
                        copyAllColumns[i].render = (value, row, index) => {
                            const object = {
                                children: value ? value : '暂无',
                                props: {
                                    rowSpan: (row.rowspan || row.rowspan === 0) ? row.rowspan : 1
                                }
                            }
                            return object;
                        }
                    }
                }
                // 对于每一个日程，根据是否需要签（is_sign_in）到和是否可以晋级（is_level_up），可能存在以下状况

                // 1.需要签到，可以晋级
                // 实际签到人数名单中，包含晋级操作，颁奖操作；预计签到人数名单，不包含任何操作

                // 2.不需要签到，可以晋级
                // 实际签到人数名单不存在；预计参加人员名单中，包含晋级操作，颁奖操作

                // 3.需要签到，不可以晋级
                // 实际签到人数中，包含颁奖操作；预计参与人员名单，不包含任何操作

                // 4.不需要签到，不可以晋级
                // 实际签到人数名单不存在；预计参加人员名单中，包含颁奖操作

                if (activityNameListType === 1) {
                    //审批人员列表，没有晋级和颁奖操作列
                    if (copyAllColumns[i].title === '晋级' || copyAllColumns[i].title === '颁奖') {
                        continue;
                    } else if (copyAllColumns[i].title === '审批') {
                        //审批人员名单，始终有审批操作列
                        copyAllColumns[i].render = fetchApproveHandleColumnsRender();
                    }
                }
                else if (activityNameListType === 2) {
                    //预计参与人员列表,没有审批,晋级，颁奖操作列
                    if (copyAllColumns[i].title === '审批' || copyAllColumns[i].title === '晋级' || copyAllColumns[i].title === '颁奖') {
                        continue;
                    }
                    // if (copyAllColumns[i].title === '审批') {
                    //     continue;
                    // } else {
                    //     //预计参加人员列表，当不需要签到，但是可以晋级时，有晋级操作列
                    //     if (copyAllColumns[i].title === '晋级') {
                    //         if (is_sign_in === 0 && is_level_up === 1) {
                    //             copyAllColumns[i].render = riseHandleColumnRender(activityType);
                    //         } else {
                    //             continue;
                    //         }
                    //     }
                    //     //预计参加人员列表，当不需要签到，有颁奖操作
                    //     else if (copyAllColumns[i].title === '颁奖') {
                    //         if (is_sign_in === 0) {
                    //             copyAllColumns[i].render = awardHandleColumnRender();
                    //         } else {
                    //             continue;
                    //         }
                    //     }
                    // }
                }
                else if (activityNameListType === 3) {
                    //实际签到人员列表,没有审批，颁奖操作列
                    if (copyAllColumns[i].title === '审批') {
                        continue;
                    } else {
                        //实际签到人员列表，当允许晋级，拥有晋级操作列
                        if (copyAllColumns[i].title === '晋级') {
                            if (is_level_up === 1) {
                                copyAllColumns[i].render = riseHandleColumnRender(activityType);
                            } else {
                                continue;
                            }
                        }
                        //实际签到人员列表，始终存在颁奖操作列
                        else if (copyAllColumns[i].title === '颁奖') {
                            copyAllColumns[i].render = awardHandleColumnRender();
                        }
                    }
                }
                else if (activityNameListType === 4) {
                    //晋级人员列表,没有审批和晋级操作列
                    if (copyAllColumns[i].title === '审批' || copyAllColumns[i].title === '晋级') {
                        continue;
                    } else {
                        //晋级人员列表，始终存在颁奖操作列
                        if (copyAllColumns[i].title === '颁奖') {
                            copyAllColumns[i].render = awardHandleColumnRender();
                        }
                    }
                }
                result.push(copyAllColumns[i]);
            }
            return result;
        };

        let targetColumns = [];
        if (activityType && activityNameListType) {
            targetColumns = fetchTargetColumns(activityType, activityNameListType);
        }
        return (
            <SiderContent {...params}>
                <section className={'sider-main'}>
                    <header className={'sider-content-header'}>
                        <Form layout="inline"
                            onSubmit={(e) => {
                                e.preventDefault();
                                let queryParams = getFieldsValue();
                                return conditionQuery(queryParams);
                            }}>
                            <FormItem label="姓名">
                                {
                                    getFieldDecorator('name', {
                                        initialValue: '',
                                        rules: [
                                            {
                                                max: 30, message: '最多只能输入30个字'
                                            }
                                        ]
                                    })(<Input type="text" name={'name'} placeholder={'请输入'} maxLength={30} />)
                                }
                            </FormItem>
                            <FormItem label="电话">
                                {
                                    getFieldDecorator('phone', {
                                        initialValue: '',
                                        rules: [{
                                            max: 30, message: '最多只能输入30个字'
                                            // pattern: /^1\d{10}$/,
                                            // message: '请输入正确的手机号码'
                                        }]
                                    })(<Input type="text" name={'phone'} placeholder={'请输入'} />)
                                }
                            </FormItem>
                            {
                            activityType === 2 &&
                            <FormItem label="队伍名称">
                                {
                                    getFieldDecorator('team_name', {
                                        initialValue: '',
                                        rules: [{
                                            max: 10,
                                            message: '最多只能输入10个字'
                                        }]
                                    })(<Input type="text" name={'team_name'} placeholder={'请输入'} maxLength={10} />)
                                }
                            </FormItem>
                            }
                            <FormItem className={'ticket'} label="票券">
                                {
                                    getFieldDecorator('serial_num', {
                                        initialValue: '',
                                        rules: [{
                                            max: 10,
                                            message: '最多只能输入10个字'
                                        }]
                                    })(<Input type="text" name={'serial_num'} placeholder={'请输入'} maxLength={10} />)
                                }
                            </FormItem>
                            {
                            (activityNameListType === 2 || activityNameListType === 3) &&
                                <FormItem label="所在组织" >
                                    {
                                        getFieldDecorator('org_name', {
                                            initialValue: '',
                                            rules: [{
                                                message: '请输入所在组织'
                                            }]
                                        })(<Input type="text" name={'serial_num'} placeholder={'请输入'}/>)
                                    }
                                </FormItem>
                            }
                            {
                            (activityNameListType === 2 || activityNameListType === 3) &&
                                <FormItem label="所在党委">
                                    {
                                        getFieldDecorator('party_org_name', {
                                            initialValue: '',
                                            rules: [{
                                                message: '请输入所在党委'
                                            }]
                                        })(<Input type="text" name={'serial_num'} placeholder={'请输入'}/>)
                                    }
                                </FormItem>
                            }
                            <FormItem className={'operate-btn-item'}>
                                <div className={'btn-wrap'}>
                                  <Button
                                      htmlType={'submit'}
                                      type={'primary'}
                                  >
                                      查&nbsp;&nbsp;&nbsp;&nbsp;询
                                  </Button>
                                    {(activityNameListType === 2 || activityNameListType === 3) &&
                                      <a
                                        className={'download-btn detail-download-btn export'}
                                        onClick={()=>{
                                            fileDownload(downloadUsers(getFieldsValue()))
                                          }}
                                          href="javascript:void(0);"
                                        >
                                        导&nbsp;&nbsp;&nbsp;&nbsp;出
                                      </a>
                                    }
                                </div>
                            </FormItem>
                        </Form>
                    </header>
                    <section className="sider-content-section">
                        <Table
                            columns={targetColumns}
                            dataSource={peopleOverviewDataSource}
                            bordered={true}
                            pagination={false}
                        // {
                        //     pageSize: 1,
                        //     onChange: onPageChange
                        // }
                        />
                    </section>
                </section>
            </SiderContent>
        )
    }
}

export default SideView;