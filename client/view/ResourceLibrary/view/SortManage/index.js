import React, { useState, useEffect } from "react";
import { Button, message, Table, Modal } from "antd";
import SearchHeader from "components/search-header";
import AddModal from "./components/addModal";
import { deepClone } from "client/tool/deepClone";
import {
  findCategoryList,
  deleteCategoryBind,
  deleteCategory,
  upDownCategory,
} from "apis/resource-library";
import "./index.less";

// 下拉列表显示一二级
const formatData = (value) => {
  value.title = value.resource_category_name;
  value.value = value.resource_category_id;
  value.key = value.resource_category_id;
  if ([1, 2, 3, 4].includes(value.parent_id)) {
    delete value.children;
  }
  if (value.children) {
    value.children.forEach(formatData);
  }
};

// 清除空children，格式化数据
const clearEmptyChildren = (value, index, arr) => {
  value.data = arr;
  if (value.children.length === 0) {
    delete value.children;
  } else {
    value.children.forEach(clearEmptyChildren);
  }
};

const SortManage = (props) => {
  const [data, setData] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [selectKey, setSelectKey] = useState([]);
  const [addModalProps, setAddModalProps] = useState({});
  const [addModalVisible, setAddModalVisible] = useState(false);

  useEffect(() => {
    initDataSource();
  }, []);

  const initDataSource = () => {
    findCategoryList().then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      let newData = deepClone(res.data);
      newData.forEach(clearEmptyChildren);
      setDataSource(newData);
      setData(res.data);
    });
  };

  /**
   * 上移下移
   * @param {Boolean} isUp 是否上移
   * @param {Object} row 行数据
   * @param {Number} index 行数据索引
   */
  const moveCategory = (isUp, row, index) => {
    const params = {
      asc_resource_category_id: isUp
        ? row.data[index - 1].resource_category_id
        : row.resource_category_id,
      asc_seq: isUp ? index : index + 1,
      asc_parent_id: row.parent_id,
      desc_resource_category_id: isUp
        ? row.resource_category_id
        : row.data[index + 1].resource_category_id,
      desc_seq: isUp ? index + 1 : index + 2,
      desc_parent_id: row.parent_id,
    };
    upDownCategory(params).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      message.success("操作成功");
      initDataSource();
    });
  };

  const delCategory = (resource_category_id) => {
    Modal.confirm({
      title: "提示",
      content: "确认删除这条分类吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        deleteCategory({ resource_category_id }).then(({ data: res }) => {
          if (res.code !== 0) {
            message.warning(res.message);
            return;
          }
          message.success("删除成功");
          initDataSource();
        });
      },
    });
  };

  const addCategory = (type, row = {}) => {
    let treeData = deepClone(data);
    treeData.forEach(formatData);
    setAddModalProps({
      type,
      isRowOpera: Object.keys(row).length !== 0,
      treeData,
      initDataSource,
      category_type: row ? row.category_type : null,
      parent_id: row
        ? type === 1
          ? row.resource_category_id
          : row.parent_id
        : null,
      resource_category_id: row ? row.resource_category_id : null,
      resource_category_name:
        row && type === 2 ? row.resource_category_name : null,
    });
    setAddModalVisible(true);
  };

  const delCategoryBind = () => {
    Modal.confirm({
      title: "提示",
      content: "确认清除该分类下所有资源数据吗？确定删除后将无法恢复？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        deleteCategoryBind({ resource_category_id: selectKey.join(",") }).then(
          ({ data: res }) => {
            if (res.code !== 0) {
              message.error("删除失败");
              return;
            }
            message.success("删除成功");
          }
        );
      },
    });
  };

  const columns = [
    {
      title: "分类名称",
      dataIndex: "resource_category_name",
      key: "resource_category_name",
    },
    {
      title: "资源数",
      dataIndex: "resource_total",
      key: "resource_total",
      width: "12%",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operate",
      width: "25%",
      key: "operate",
      render: (_, row, index) => {
        return (
          <span>
            {/* 同级第一位没有上移操作 */}
            {index !== 0 && (
              <Button
                type="link"
                onClick={() => moveCategory(true, row, index)}
              >
                上移
              </Button>
            )}
            {/* 同级最后一位没有下移操作 */}
            {index !== row.data.length - 1 && (
              <Button
                type="link"
                onClick={() => moveCategory(false, row, index)}
              >
                下移
              </Button>
            )}
            {/* 三级分类没有新建子分类 */}
            {[0, 1, 2, 3, 4].includes(row.parent_id) && (
              <Button type="link" onClick={() => addCategory(1, row)}>
                新建子分类
              </Button>
            )}
            {/* 一级分类不能进行编辑、删除操作 */}
            {row.parent_id !== 0 && (
              <span>
                <Button type="link" onClick={() => addCategory(2, row)}>
                  编辑
                </Button>
                <Button
                  type="link"
                  onClick={() => delCategory(row.resource_category_id)}
                >
                  删除
                </Button>
              </span>
            )}
          </span>
        );
      },
    },
  ];

  const tableProps = {
    columns,
    dataSource,
    bordered: true,
    pagination: false,
    defaultExpandAllRows: true,
    rowKey: "resource_category_id",
    rowSelection: {
      // 禁用一级分类、资源数为0的勾选框
      getCheckboxProps: (row) => ({
        disabled: row.parent_id === 0 || row.resource_total === 0,
      }),
      onChange: (selectedRowKeys) => {
        setSelectKey(selectedRowKeys);
      },
    },
  };

  return (
    <div className="sort-manage">
      <SearchHeader
        onBack={false}
        title={"分类管理"}
        renderRight={() => {
          return (
            <div>
              <Button
                type="default"
                className="upload-btn"
                icon="plus"
                onClick={() => addCategory(1)}
              >
                新建分类
              </Button>
              <Button
                type="default"
                className="upload-btn"
                disabled={selectKey.length === 0}
                onClick={() => delCategoryBind()}
              >
                清除分类资源
              </Button>
            </div>
          );
        }}
      />
      <div className="sort-manage-content">
        {dataSource.length !== 0 && <Table {...tableProps} />}
      </div>
      <AddModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        {...addModalProps}
      />
    </div>
  );
};

export default SortManage;
