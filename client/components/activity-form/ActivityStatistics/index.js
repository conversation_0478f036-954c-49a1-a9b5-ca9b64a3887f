/**
 * 参加活动人员统计
 * <AUTHOR>
 * 2018/4/23
 */
import React from "react";
import PropTypes from "prop-types";
import { Row, Col, Checkbox, InputNumber } from "antd";

import adaptiveLayout from "../gridLayout";
import { CircleButton } from "../sub-components/CustomizeButton";
import InputNumberAttributes from "../sub-components/InputNumberAttributes";

class AgeInput extends React.Component {
  render() {
    return (
      <InputNumber
        placeholder="请输入"
        min={1}
        max={119}
        style={{ width: 70 }}
        {...this.props}
      />
    );
  }
}

class ActivityStatistics extends React.Component {
  _renderAgeRange(disabled) {
    const {
      activityFormState,
      formFields,
      form,
      handleEvents,
      preview
    } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    return activityFormState.statisticalDimensionAge.map((item, index) => {
      return (
        <div style={{ margin: "15px 0" }} key={index}>
          <InputNumberAttributes unit="岁" disabled={disabled}>
            {getFieldDecorator(formFields.dimensionAgeBegin + item.rank, {
              initialValue: item.ageBegin,
              rules: [
                {
                  required: true,
                  message: "请输入年龄区间"
                }
              ]
            })(<AgeInput disabled={disabled || preview} />)}
          </InputNumberAttributes>
          <span style={{ margin: "0 10px" }}>至</span>
          <InputNumberAttributes unit="岁" disabled={disabled}>
            {getFieldDecorator(formFields.dimensionAgeEnd + item.rank, {
              initialValue: item.ageEnd,
              rules: [
                {
                  required: true,
                  message: "请输入年龄区间"
                }
              ]
            })(<AgeInput disabled={disabled || preview} />)}
          </InputNumberAttributes>
          <CircleButton
            style={{ marginLeft: 15 }}
            icon="gsg-shanchu"
            size="small"
            disabled={disabled || preview}
            onClick={handleEvents.deleteAgeRange.bind(this, item, index)}
          />
        </div>
      );
    });
  }
  componentDidMount() {
    const { activityFormState, formFields } = this.props;
    const { setFieldsValue, getFieldValue } = this.props.form;
    if (
      activityFormState.statisticalDimension &&
      !getFieldValue(formFields.statisticalDimension)
    ) {
      setFieldsValue(
        formFields.statisticalDimension,
        activityFormState.statisticalDimension
      );
    }
  }
  render() {
    const {
      form,
      emuns,
      preview,
      formFields,
      handleEvents,
      activityFormState
    } = this.props;
    const { statisticalDimension } = emuns;
    const { getFieldDecorator, getFieldValue } = form;
    const statisticalDimensionValues =
      getFieldValue(formFields.statisticalDimension) || [];
    //年龄数据
    const dimensionAgeData = statisticalDimensionValues.filter(val => {
      return val === statisticalDimension.age.value;
    });
    const statisticalDimensionAgeDisabled = statisticalDimensionValues[0] !== 1

    return (
      <div>
        <Row className="ant-form-item">
          <Col
            {...adaptiveLayout.formWrapper.labelCol}
            className="ant-form-item-label"
          >
            <label title="统计维度">统计维度</label>
          </Col>
          <Col
            {...adaptiveLayout.formWrapper.wrapperCol}
            className="custom-form-item-col"
            style={{ paddingTop: 9 }}
          >
            {getFieldDecorator(formFields.statisticalDimension, {
              initialValue: activityFormState.statisticalDimension
            })(
              <Checkbox.Group style={{ width: "100%" }}>
                <Row>
                  <Col span={15}>
                    <Checkbox
                      disabled={preview}
                      value={statisticalDimension.age.value}
                    >
                      {statisticalDimension.age.label}
                    </Checkbox>
                    <span style={{ color: "#999" }}>
                      以下是默认配置，您可以根据您的需要调整
                    </span>
                    <div className="gray-box">
                      <div>
                        <InputNumberAttributes
                          unit="岁"
                          disabled={statisticalDimensionAgeDisabled}
                        >
                          {getFieldDecorator(formFields.dimensionAgeBegin, {
                            initialValue:
                              activityFormState.statisticalDimensionAgeBegin ||
                              20
                          })(
                            <AgeInput
                              disabled={
                                statisticalDimensionAgeDisabled || preview
                              }
                            />
                          )}
                        </InputNumberAttributes>
                        <span style={{ marginLeft: 10 }}>以下</span>
                      </div>
                      {this._renderAgeRange(statisticalDimensionAgeDisabled)}
                      <a
                        href="javascript:;"
                        style={{ margin: "10px 0", display: "block" }}
                        disabled={statisticalDimensionAgeDisabled || preview}
                        onClick={handleEvents.addAgeRange}
                      >
                        增加区间
                      </a>
                      <div>
                        <InputNumberAttributes
                          unit="岁"
                          disabled={statisticalDimensionAgeDisabled}
                        >
                          {getFieldDecorator(formFields.dimensionAgeEnd, {
                            initialValue:
                              activityFormState.statisticalDimensionAgeEnd || 60
                          })(
                            <AgeInput
                              disabled={
                                statisticalDimensionAgeDisabled || preview
                              }
                            />
                          )}
                        </InputNumberAttributes>
                        <span style={{ marginLeft: 10 }}>以上</span>
                      </div>
                    </div>
                  </Col>
                  <Col offset={2} span={7}>
                    <Checkbox
                      disabled={preview}
                      value={statisticalDimension.sex.value}
                    >
                      {statisticalDimension.sex.label}
                    </Checkbox>
                    <br />
                    <br />
                    {/* <Checkbox disabled={preview} value={statisticalDimension.education.value}>{statisticalDimension.education.label}</Checkbox> */}
                    {/* <br /><br />
                                    <Checkbox value={statisticalDimension.ethnic.value}>{statisticalDimension.ethnic.label}</Checkbox> */}
                    <br />
                    <br />
                    {/* <Checkbox disabled={preview} value={statisticalDimension.other.value}>{statisticalDimension.other.label}</Checkbox> */}
                  </Col>
                </Row>
              </Checkbox.Group>
            )}
          </Col>
        </Row>
      </div>
    );
  }
}

ActivityStatistics.propTypes = {
  //预览模式
  preview: PropTypes.bool,

  //antd表单对象
  form: PropTypes.object.isRequired,

  //表单字段
  formFields: PropTypes.shape({
    //维度统计
    statisticalDimension: PropTypes.string.isRequired,
    //年龄维度以下
    dimensionAgeBegin: PropTypes.string.isRequired,
    //年龄维度以上
    dimensionAgeEnd: PropTypes.string.isRequired
  }).isRequired,

  //表单默认数据
  emuns: PropTypes.shape({
    statisticalDimension: PropTypes.shape({
      //年龄维度
      age: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired
      }),
      //性别维度
      sex: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired
      }),
      //文化程度维度
      education: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired
      }),
      //民族维度
      ethnic: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired
      }),
      //其他身份维度
      other: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired
      })
    }).isRequired
  }).isRequired,

  //表单model
  activityFormState: PropTypes.shape({
    //统计维度
    statisticalDimension: PropTypes.array.isRequired,
    //统计维度年龄配置
    statisticalDimensionAge: PropTypes.arrayOf(PropTypes.object).isRequired,
    //年龄已下
    statisticalDimensionAgeBegin: PropTypes.number.isRequired,
    //年龄以上
    statisticalDimensionAgeEnd: PropTypes.number.isRequired
  }).isRequired,

  //表单处理的事件合集
  handleEvents: PropTypes.shape({
    //新增年龄区间
    addAgeRange: PropTypes.func.isRequired,
    //删除年龄区间
    deleteAgeRange: PropTypes.func.isRequired
  }).isRequired
};

ActivityStatistics.defaultProps = {
  preview: false
};

export default ActivityStatistics;
