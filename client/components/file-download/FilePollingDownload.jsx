import React, { PureComponent } from 'react';
import PropTypes from "prop-types";
import { Button, message } from "antd";
import axios from "axios";
import { headers } from "client/tool/axios";
import { Gateway } from "apis/config";
import { getQuery } from "client/tool/util";
import { fileDownload } from "./index";

/**
 * 轮询下载
 */
export default class FilePollingDownload extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      uuid: "",
      percent: 0,
    };
    this.pollingTime = null;
    this.onDownloadFile = this.onDownloadFile.bind(this);
  }
  componentWillUnmount() {
    if (this.pollingTime) {
      clearTimeout(this.pollingTime);
    }
  }
  onDownloadFile(e) {
    e.preventDefault();
    const { filePath, params } = this.props;
    const customHeaders = {
      ...headers(),
      "Content-Type":"application/json"
    };
    axios.post(Gateway + filePath, params || getQuery(decodeURI(filePath)), {
      data: getQuery(filePath),
      headers: customHeaders,
    }).then(res => {
        // 后端返回json数据
        const { data, status, code, message: msg } = res.data;
        if (code === 0 && status === 200) {
          this.setState({uuid: data, loading: true}, () => {
            this.pollingDownload();
          });
        } else {
          message.error(msg || "网络异常，请稍后再试");
        }
    });
  }
  pollingDownload() {
    const { filePath, fileName } = this.props;
    const { uuid } = this.state;
    const customHeaders = {
      ...headers(),
      "Content-Type":"application/json"
    };
    const url =( Gateway + filePath).split("?")[0];
    axios.get(`${url}?uuid=${uuid}`, {
      headers: customHeaders,
    }).then(res => {
      const { status: httpStatus, data } = res;
      if (httpStatus === 200) {
        if (!(data.code === 0 && data.status === 200)) {
          this.setState({
            loading: false,
            percent: 0,
          });
          message.error(data.message || "服务器异常，请稍后再试！");
          return;
        }
        if (typeof(data.data) === "object" && data.data.file) {
          fileDownload(`/file/file/download/${data.data.file}`)
          .catch(() => {
            message.error("下载失败，请联系管理员");
          })
          .finally(() => {
            this.setState({
              loading: false,
              percent: 0,
            });
          });
        } else {
          this.setState({percent: data.data || 0}, () => {
            setTimeout(() => {
              this.pollingDownload();
            }, 1000);
          });
        }
      } else {
        this.setState({loading: false, percent: 0});
        message.error("网络异常，请稍后再试!");
      }
    });
  }
  render() {
    const { btnName, className, type } = this.props;
    const { loading, percent } = this.state;
    return (
      <Button
        className={`file-download-btn ${className}`}
        loading={this.state.loading}
        type={type}
        onClick={this.onDownloadFile}
      >
        { btnName } { loading ? `(${percent}%)` : "" }
      </Button>
    );
  }
}

FilePollingDownload.propTypes = {
  filePath: PropTypes.string.isRequired, // 下载地址（需自行添加网关）
  fileName: PropTypes.string, // 下载文件的文件名
  btnName: PropTypes.string, // 生成的按钮名
  type: PropTypes.string, // 同Antd button的type  默认primary
  className: PropTypes.string, // 按钮类名
};

FilePollingDownload.defaultProps = {
  type: "primary",
  btnName: "下载",
  className: "",
};