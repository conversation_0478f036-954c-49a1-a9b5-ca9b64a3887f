.import-page {
  width: 100%;
  height: 100%;
  .ip-content {
    .import-page-import {
      padding: 30px 50px;

      .index-block {
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #1fa1fd;
        color: #fff;
        font-size: 15px;
        line-height: 15px;
      }
      .time-item {
        .title {
          font-size: 15px;
          line-height: 32px;
          .text {
            margin: 0px 10px;
          }
        }
        .file {
          font-size: 15px;
          .text {
            margin: 0px 10px;
          }
        }
      }
      .btn-box {
        display: flex;
        gap: 10px 20px;
      }
    }
  }
  .m-top-10 {
    margin-top: 10px;
  }
  .result {
    .msg-box {
      margin: 10px 0px;
      padding: 10px 20px;
      background-color: rgba(239, 239, 239, 1);
      font-size: 20px;
    }
    .btn-box {
      padding: 10px;
    }
    .table-box {
      padding: 10px;
    }
  }
}
