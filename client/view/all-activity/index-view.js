/**
 * 全部互动
 * <AUTHOR>
 */
 import React, { Component, Fragment } from "react";
 import {
   Form,
   Input,
   Button,
   Select,
   Divider,
   Table,
   Popconfirm,
   DatePicker,
   Cascader,
   Icon,
   message,
 } from "antd";
 import SearchHeader from "components/search-header";
 import InputWrap from "components/layout/input-wrap";
 import { mobileHost } from "apis/config";
 // import locale from 'antd/lib/date-picker/locale/zh_CN';
 import DatePickerLocale from "config/DatePickerLocale.json";
 import { publicTypesArray } from "../activitys-matrix/dict";
 import Utils from "../activitys-matrix/utils";
 import config from "config/const-config";
 
 export default ({
   onBack,
   dispatch,
   props,
   typelist,
   listLoading,
   statelist,
   crownList,
   dataList,
   TypeChange,
   saveSelectActivity,
   stateChange,
   crownChange,
   searchSubmit,
   deleteActive,
   pageNew,
   changePage,
   moveActive,
   showNotice,
   showPushtoDialog,
   userInfo,
 }) => {
   const { history } = props;
 
   // 点击表格中的推送互动隐藏
   const activityPushHide = (row) => {
     dispatch({
       type: "allActivity/activityPushHide",
       payload: {
         activity_push_id: row.activity_push_id,
         // 隐藏功能
         visibility: 0,
         cid: row.column_id,
       },
     }).then((res) => {
       message.success("操作成功");
       // console.log(res);
       dispatch({
         type: "allActivity/loadAllActivity",
         payload: {},
       });
       // console.log("隐藏", row);
     });
   };
 
   //推送互动
   const pushActivity = (row) => {};
 
   // 点击表格中的互动情况
   const activityOverview = (row) => {
     // console.log('查看互动详情', row);
     let { type, activity_id } = row,
       // 定义路由目标
       routeTarget = "";
     // 根据type确定路由信息
     switch (type) {
       case 1:
         //投票详情
         routeTarget = "/vote-detail";
         break;
       case 2:
         //问卷调查
         routeTarget = "/questionnaire-survey";
         break;
       case 3:
         //有奖竞答
         routeTarget = "/competition";
         break;
       case 4:
         //线下互动详情，项目一期没有线下互动
         routeTarget = "/activity-details";
         break;
       case 5:
         //线下互动详情，项目一期没有线下互动
         routeTarget = "/contribute-detail";
         break;
       default:
         console.error("路由信息获取失败");
     }
     history.push(`${routeTarget}/${activity_id}`);
   };
 
   //点击互动列表编辑功能
   const editActivity = (row) => {
     let currentUserId = window
       ? parseInt(window.sessionStorage.getItem("_uid"))
       : "";
     let isPreview = 1,
       queryObject = { id: row.activity_id, action: "edit" };
     // isMyActivity(row.create_user) &&
     // (
     //     (row.type !== 4 && row.status !== 1 && row.status !== 4) ||
     //     (row.type === 4 && row.status !== 4)
     // ) ? '编辑' : '查看'
 
     if (
       currentUserId !== row.create_user ||
       row.pm === 2 || //不是创建者 不能修改
       (row.type !== 4 && [1, 4].indexOf(row.status) !== -1) || //互动中 已结束 不能修改
       (row.type === 4 && [4].indexOf(row.status) !== -1)
     ) {
       //线下互动 已结束 不能修改
 
       // message.error('当前互动不是你发起的，只能预览，无法编辑');
       queryObject.isPreview = isPreview;
       queryObject.action = "view";
       // return;
     }
 
     if (row.status === 6) {
       queryObject.action = "draft";
     }
 
     // console.log(queryObject);
     // return ;
     let pathname = "";
     if (row.type === 1) {
       pathname = "new-vote";
     } else if (row.type === 2) {
       pathname = "questionnaire";
     } else if (row.type === 3) {
       pathname = "contest";
     } else if (row.type === 4) {
       pathname = "physical";
     } else if (row.type === 5) {
       pathname = "contribute";
     }
     history.push({
       pathname,
       query: queryObject,
       state: queryObject,
     });
   };
 
   //判断互动是否为当前登录用户发布，若是，显示编辑；若不是，显示查看
   const isMyActivity = (user_id) => {
     let currentUserId = window
       ? parseInt(window.sessionStorage.getItem("_uid"))
       : "";
     return currentUserId === user_id;
   };
 
   const mobileOverview = (row) => {
     //跳转查看手机预览
     // console.log('预览', row);
     let url = mobileHost;
     const { type, activity_id, type_name } = row;
     const _tk = window.sessionStorage.getItem("_tk") || "",
       _uid = window.sessionStorage.getItem("_uid") || "",
       _un = window.sessionStorage.getItem("_un") || "",
       _type = window.sessionStorage.getItem("_type") || "",
       _oid = window.sessionStorage.getItem("_oid") || "",
       userInfo = window.sessionStorage.getItem("userInfo") || {},
       { phone } = JSON.parse(userInfo);
     const queryString = `?isview=1&_tk=${_tk}&_uid=${_uid}&_un=${_un}&_type=${_type}&_oid=${_oid}&phone=${
       phone || ""
     }`;
     url = `${mobileHost}/${activity_id}/${type}${queryString}`;
     dispatch({
       type: "allActivity/save",
       payload: {
         url,
         // url: 'http://************:7003',
         modalVisible: true,
       },
     });
     // console.log(url);
     // return;
     // window.open(
     //     url,
     //     '手机预览',
     //     'directories=no,height=575,width=337',
     //     false
     // );
   };
   const { getFieldDecorator } = props.form;
   const formItemLayout = {
       labelCol: { span: 8, offset: 1 },
       wrapperCol: { span: 10 },
     },
     Option = Select.Option,
     _oid = window.sessionStorage.getItem("_oid") || "",
     //查询结果的表格header
     resultColumns = [
       {
         title: "ID",
         dataIndex: "activity_id",
         key: "activity_id",
         width: 80,
       },
       {
         // title: '互动标题',
         title: "互动标题",
         dataIndex: "title",
         key: "title",
         render: (text, row) => (
           <span>
             <span>{text}</span>&nbsp;&nbsp;
             {/* <Divider type="vertical" />
                     <a href='#!'>网站预览</a> */}
           </span>
         ),
       },
       {
         title: "栏目",
         dataIndex: "column_name",
         key: "column_name",
         width: 120,
       },
       {
         title: "类型",
         dataIndex: "type",
         key: "type",
         width: 80,
         render: Utils.columns.type,
       },
       {
         title: "状态",
         dataIndex: "status",
         key: "status",
         width: 80,
         render: Utils.columns.status,
       },
       {
         title: "发布方式",
         dataIndex: "pm",
         key: "pm",
         width: 80,
         render: (text) => {
           return { 1: "原创", 2: "推送" }[text] || "未知";
         },
       },
       {
         title: "互动时间",
         dataIndex: "time",
         key: "time",
         width: 300,
         render: (text, record) => {
           let timeResult = "";
           //时间显示策略调整为:
           if (!record.start_time && !record.end_time) {
             //如果开始时间结束时间均未设置:
             timeResult = <div>永久</div>;
           } else {
             if (record.start_time && !record.end_time) {
               //如果开始时间设置，结束时间未设置:
               timeResult = <div>{record.start_time} 至 永久</div>;
             }
             if (!record.start_time && record.end_time) {
               //如果开始时间未设置,结束时间设置:
               //显示创建时间为开始时间
               timeResult = (
                 <div>
                   {record.create_time ? record.create_time : "-"} 至{" "}
                   {record.end_time}
                 </div>
               );
             }
             if (record.start_time && record.end_time) {
               timeResult = (
                 <div>
                   {record.start_time} 至 {record.end_time}
                 </div>
               );
             }
           }
           return timeResult;
         },
       },
       {
         title: "操作",
         width: 240,
         render: (text, row) => {
           const { org_id } = row;
           const { oid } = userInfo;
           // console.log(row, row.pm);
           return (
             /*
              *   "status": "int|互动状态（1：互动中；2：审批中；3：未通过；4：已结束；5：未开始；6:"草稿"）"
              *   "status_name": "string|互动状态中文表示"
              *   编辑和删除操作者（即当前登录用户）与发起互动者身份须一致
              *   否则列表页面提示：当前互动不是你发起的，无法编辑/删除
              *   互动管理列表:
              *   1.互动中删除，表示互动关闭；常规编辑update
              *   2.审批中删除，审批被撤回；撤回原有审批，重新发起一次（后台处理调用undo）
              *   3.未通过，同1；常规编辑update
              *   4.已结束，同1；常规编辑update
              *   5.未开始，同1；重新发起一次（后台处理undo）
              *
              * */
             <span>
               {org_id == oid && row.status != 6 && (
                 <Fragment>
                   <span className="link-el" onClick={() => mobileOverview(row)}>
                     手机预览
                   </span>
                   <Divider type="vertical" />
                 </Fragment>
               )}
               {/* {
                             isMyActivity(row.create_user) && '本人'
                         } */}
               <span className="link-el" onClick={() => editActivity(row)}>
                 {
                   //显示编辑功能，当前登录用户是互动发布人
                   //当不是线下互动时，不是互动中和已结束的状态，可以编辑
                   //当时线下互动，只要不是已结束状态，可以编辑
 
                   !isMyActivity(row.create_user) || //不是创建者 不能修改
                   row.pm == 2 || //是推送过来的不能修改的
                   (row.type !== 4 && [1, 4].indexOf(row.status) !== -1) || //互动中 已结束 不能修改
                   (row.type === 4 && [4].indexOf(row.status) != -1) //线下互动 已结束 不能修改
                     ? "查看"
                     : "编辑"
 
                   // isMyActivity(row.create_user) &&
                   //     (
                   //         (row.type !== 4 && row.status !== 1 && row.status !== 4) ||
                   //         (row.type === 4 && row.status !== 4)
                   //     ) ? '编辑' : '查看'
                 }
               </span>
               {row.pm !== 2 && <Divider type="vertical" />}
               {org_id === oid &&
               config.enableOrganizeLevelManageModule &&
               [1, 5].indexOf(row.status) != -1 &&
               row.pm !== 2 ? (
                 <Fragment>
                   <span className="link-el"
                     onClick={() => {
                       showPushtoDialog(row);
                     }}
                   >
                     推送
                   </span>
                   <Divider type="vertical" />
                 </Fragment>
               ) : (
                 ""
               )}
               {row.pm !== 2 && (
                 <Popconfirm
                   title="您确认删除吗?"
                   okText="确认"
                   cancelText="取消"
                   onConfirm={() => deleteActive(row)}
                   placement="bottomRight"
                 >
                   <span className="link-el">删除</span>
                 </Popconfirm>
               )}
               {[2, 3, 6].indexOf(row.status) == -1 && (
                 <span>
                   <Divider type="vertical" />
                   <span className="link-el"
                     onClick={() => {
                       return activityOverview(row);
                     }}
                   >
                     互动情况{" "}
                   </span>
                   <Divider type="vertical" />
                   {_oid == userInfo.root_oid ? (
                     org_id === oid ? (
                       <span className="link-el"
                         onClick={() => {
                           // console.log('发送通知');
                           showNotice(row);
                         }}
                       >
                         发送通知{" "}
                       </span>
                     ) : null
                   ) : null}
                 </span>
               )}
               {row.pm === 2 && (
                 <span>
                   <Divider type="vertical" />
                   <span className="link-el"
                     onClick={() => {
                       activityPushHide(row);
                     }}
                   >
                     隐藏
                   </span>
                 </span>
               )}
             </span>
           );
         },
       },
     ],
     rowSelection = {
       onChange: (selectedRowKeys, selectedRows) => {
         saveSelectActivity(selectedRows);
         // console.log(`选择的keys: ${selectedRowKeys}`, '选择的一行的信息: ', selectedRows);
       },
       /*getCheckboxProps: record => ({
                 disabled: record.name === 'Disabled User',
                 name: record.name
             })*/
     };
   if (!config.enableOrganizeLevelManageModule) {
     //NOTE：分级管理模块 根据配置是否显示此列
     let index = resultColumns.findIndex((item) => {
       return item.title === "发布方式";
     });
     resultColumns.splice(index, 1);
   }
 
   return (
     <div className="allactivity">
       <SearchHeader
         // title='互动管理'
         title="互动管理"
         onBack={onBack}
       />
       {/*查询信息按钮界面*/}
 
       <InputWrap>
         <Form layout="inline" onSubmit={searchSubmit} style={{ margin: 0 }}>
           <Form.Item label="类型" {...formItemLayout}>
             {getFieldDecorator("type")(
               <Select
                 onChange={TypeChange}
                 style={{ width: 100 }}
                 placeholder="全部"
               >
                 {typelist.map((val) => (
                   <Option key={val.key}>{val.title}</Option>
                 ))}
               </Select>
             )}
           </Form.Item>
           <Form.Item label="状态" {...formItemLayout}>
             {getFieldDecorator("status")(
               <Select
                 onChange={stateChange}
                 style={{ width: 100 }}
                 placeholder="全部"
               >
                 {statelist.map((val) => (
                   <Option key={val.key}>{val.title}</Option>
                 ))}
               </Select>
             )}
           </Form.Item>
           <Form.Item
             label="所在栏目"
             {...formItemLayout}
             labelCol={{ span: 11 }}
           >
             {getFieldDecorator("cid")(
               <Cascader
                 options={crownList}
                 onChange={crownChange}
                 style={{ width: 169 }}
                 placeholder="全部"
                 changeOnSelect
               />
             )}
           </Form.Item>
 
           <Form.Item label="关键词" {...formItemLayout}>
             {getFieldDecorator("keyword", {
               rules: [
                 {
                   required: false,
                   message: "请输入互动标题、ID",
                 },
                 {
                   max: 30,
                   message: "最多只能输入30个字",
                 },
               ],
             })(
               <Input
                 placeholder="互动标题、ID"
                 style={{ width: 200 }}
                 maxLength={30}
               />
             )}
           </Form.Item>
 
           <Form.Item {...formItemLayout} wrapperCol={{ offset: 6 }}>
             {getFieldDecorator("timepoint", {
               rules: [{ required: false, message: "" }],
             })(
               <DatePicker
                 showTime
                 format="YYYY-MM-DD HH:mm:ss"
                 placeholder="互动时间"
                 style={{ width: 169 }}
                 locale={DatePickerLocale}
               />
             )}
           </Form.Item>
 
           {/* NOTE：分级管理模块 根据配置是否显示*/}
           {config.enableOrganizeLevelManageModule ? (
             <React.Fragment>
               <div style={{ clear: "both" }}>&nbsp;</div>
 
               <Form.Item
                 label="发布方式"
                 {...formItemLayout}
                 labelCol={{ span: 11 }}
               >
                 {getFieldDecorator("pm", {
                   initialValue: "0",
                 })(
                   <Select style={{ width: 100 }} placeholder="全部">
                     {publicTypesArray.map((val) => (
                       <Option key={val.key}>{val.title}</Option>
                     ))}
                   </Select>
                 )}
               </Form.Item>
             </React.Fragment>
           ) : (
             ""
           )}
 
           <Form.Item {...formItemLayout} wrapperCol={{ offset: 16 }}>
             <Button htmlType="submit" type="primary">
               查询
             </Button>
             {/*<Button type="primary">高级查询<Icon type="right" /></Button>*/}
           </Form.Item>
         </Form>
       </InputWrap>
 
       {/*显示的查询结果*/}
       <Table
         rowKey={"activity_id"}
         dataSource={dataList}
         columns={resultColumns}
         pagination={{
           pageSize: pageNew.pageSize,
           total: pageNew.total,
           current: pageNew.pageNum,
         }}
         bordered
         rowSelection={rowSelection}
         loading={listLoading}
         onChange={changePage}
       />
       {Array.isArray(dataList) && dataList.length !== 0 && (
         <div className="allactivity-table-bottom">
           <Form layout="inline">
             <Form.Item
               label="所选互动移动到"
               labelCol={{ span: 12 }}
               wrapperCol={{ span: 10 }}
             >
               {getFieldDecorator("moveactive", {
                 rules: [{ required: false, message: "" }],
               })(
                 <Cascader
                   options={crownList}
                   style={{ width: 169 }}
                   placeholder="全部"
                   onChange={crownChange}
                   changeOnSelect
                 />
               )}
             </Form.Item>
             <Form.Item wrapperCol={{ offset: 12 }}>
               <Button type="primary" onClick={moveActive}>
                 确认移动
               </Button>
             </Form.Item>
           </Form>
         </div>
       )}
     </div>
   );
 };
 