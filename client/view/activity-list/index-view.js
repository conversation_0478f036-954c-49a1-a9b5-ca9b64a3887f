import React, { Component } from 'react';
import { Table, Divider, Popconfirm } from 'antd';

import SearchHeader from 'components/search-header';

export default ({
    workflowList,
    dataList,
    pageNum,
    pages,
    pageSize,
    total,
    props,
    onAppendChildren,
    onAppendRoot,
    onOverview,
    onEdit,
    onDelete,
    pageChangeHandler
}) => {
    const addChildNode = (row) => {
        return '';
        // return (
        //     <span>
        //         <a href="javascript:void(0)" onClick={() => onAppendChildren(row)}>
        //             添加子栏目
        //         </a>
        //         <Divider type="vertical"/>
        //     </span>
        // );
    };

    const fetchWorkflowName = (workflow_id, workflowList) => {
        // console.log(workflow_id, workflowList);
        let workflow_name = '';
        if (Array.isArray(workflowList) && workflowList.length !== 0) {
            for (let i = 0, len = workflowList.length; i < len; i++) {
                if (workflow_id === workflowList[i].workflowId) {
                    workflow_name = workflowList[i].name;
                    break;
                }
            }
        }
        return workflow_name || workflow_id;
    };

    const columns = [
        {
            // title: '活动栏目',
            title: '互动栏目',
            align: 'left',
            dataIndex: 'name',
            render(value, row, index) {
                // console.log(row)
                return (
                    <span>{row.parent_column_id === 0}{value}{(row.is_default ? '（默认栏目）' : '')}</span>
                )
            }
        },
        {
            title: '默认审批类型',
            align: 'center',
            dataIndex: 'workflow_name',
            render(value, row, index) {
                let render = value ? value : (fetchWorkflowName(row.workflow_id, workflowList, row.workflow_name));
                return (
                    <span>{render}</span>
                )
            }
        },
        {
            // title: '活动数',
            title: '互动数',
            align: 'center',
            dataIndex: 'count'
        },
        {
            title: '操作',
            align: 'center',
            render: (text, row, index) => {
                return (
                    <span>
                        {
                            addChildNode(row)
                        }
                        <a href="javascript:void(0)" onClick={() => onOverview(row, index)}>
                            查看互动
                        </a>
                        <Divider type="vertical" />
                        <a href="javascript:void(0)" onClick={() => onEdit(row)}>
                            编辑
                        </a>
                        <Divider type="vertical" />
                        <Popconfirm title={'确认删除活动分类？'} onConfirm={() => onDelete(row)} okText="删除" cancelText="取消">
                            {/*onClick={() => onDelete(row)}*/}
                            <a href="javascript:void(0)">
                                删除
                            </a>
                        </Popconfirm>
                    </span>);
            }
        }
    ];
    return (
        <div>
            <SearchHeader>
                <div className='header'>
                    {/* 活动栏目管理 */}
                    互动栏目管理
                </div>
            </SearchHeader>
            <section className="main">
                <Table
                    rowKey='activity_column_id'
                    columns={columns}
                    dataSource={dataList}
                    loading={false}
                    bordered
                    style={{ padding: 0 }}
                    title={() => {
                        return (
                            <a href="javascript:void (0)" onClick={() => onAppendRoot()}>添加栏目</a>
                        )
                    }}
                    pagination={{
                        pageSize: pageSize,//添加一行显示添加栏目按钮
                        current: pageNum,
                        total,
                        onChange: pageChangeHandler
                        // showTotal: (total, range) => {
                        //     return `数据总数：${total}条，当前显示${range.join('至')}条`;
                        // }
                    }}
                />
            </section>
        </div>
    )
}
