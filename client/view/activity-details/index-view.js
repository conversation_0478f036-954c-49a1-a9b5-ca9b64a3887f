import React, { Component } from 'react';
import { Table, Divider, Button, Tabs } from 'antd';
import moment from 'moment';
import HeaderCard from 'client/components/header-card';
import LoadingModal from 'client/components/loading-modal';
import { downloadResult } from '../../apis/activity';
import { fileDownload } from 'client/components/file-download';

export default ({
    userInfo,
    downloadUrl,
    //互动类型
    activityType,
    //线下互动基础信息
    baseInfo,
    //前往审核
    goAuditing,
    //互动概要
    summaryTableDataSource,
    //互动日程
    programmeTableDataSource,
    //颁奖明细
    prizeTableDataSource,
    //所有维度
    allCountTableDataSource,
    //年龄维度
    ageTableDataSource,
    //性别维度
    genderTableDataSource,
    //学历维度
    educationDegreeTableDataSource,
    //民族维度
    nationTableDataSource,
    //其他维度
    otherTableDataSource,
    //多维度表格Loading
    loadingVisible,
    tabChangeHandler,
    downloadHandler,
    peopleListOverview
}) => {
    const { TabPane } = Tabs,
        buttonStyle = {
            // position: 'absolute',
            // right: '-66px'
        },
        buttonWrapperStyle = {
            position: 'relative'
        },
        summaryColumns = [
            {
                title: '互动类型',
                align: 'center',
                dataIndex: 'rang_type',
                render(text, record, index) {
                    if (!text) {
                        return '-';
                    }
                    return (
                        // 1：开放互动
                        // 2：指定人员
                        // 3：指定组织
                        < span style={buttonWrapperStyle} >
                            {
                                text === 1 ? '开放互动' : text === 2 ? '封闭互动（指定人员）' : text === 3 ? '封闭互动（指定组织）' : '-'
                            }
                            {/* <a href="javascript:void(0)" onClick={peopleListOverview} style={buttonStyle}>查看名单</a> */}
                        </span >
                    )
                }
            },
            {
                title: '互动名额',
                align: 'center',
                dataIndex: 'ticket_num',
                render(text, record, index) {
                    if (!text || text == -1) {
                        return '不限制';
                    }
                    return text
                }
            },
            {
                title: '已抢票人数',
                align: 'center',
                dataIndex: 'scr_ticket_num',
                render(text, record, index) {
                    if (!text || text == -1) {
                        return '0';
                    }
                    return text
                }
            },
            {
                title: '待审核人数',
                align: 'center',
                dataIndex: 'to_check',
                render(text, record, index) {
                    const {org_id} = baseInfo;
                    const {oid} = userInfo;
                    if (!text) {
                        return '0';
                    }
                    return (
                        <span style={buttonWrapperStyle}>
                            {text}
                            &nbsp;&nbsp;
                            {
                                org_id === oid &&
                                <a href="javascript:void(0)" style={buttonStyle}
                                   onClick={() => goAuditing(record)}>前往审核</a>
                            }
                        </span>
                    )
                }
            },
            {
                title: '填写报名表人数',
                align: 'center',
                dataIndex: 'make_ticket_num',
                render(text, record, index) {
                    if (!text && text !== 0) {
                        return '0';
                    }
                    return text
                }
            },
            {
                title: '报名费收入',
                align: 'center',
                dataIndex: 'payoff',
                render(text, record, index) {
                    if (!text && text !== 0) {
                        return '0.00';
                    }
                    return typeof text === 'number' ? text.toFixed(2) : Number(text).toFixed(2);
                }
            }
        ],
        // 对于每一个日程，根据是否需要签（is_sign_in）到和是否可以晋级（is_level_up），可能存在以下状况
        // 1.需要签到，可以晋级
        // 实际签到人数名单中，包含晋级操作，颁奖操作
        // 2.不需要签到，可以晋级
        // 预计参加人员名单中，包含晋级操作，颁奖操作
        // 3.需要签到，不可以晋级
        // 实际签到人数中，包含颁奖操作
        // 4.不需要签到，不可以晋级
        // 预计参加人员名单中，包含颁奖操作
        programmeColumns = [
            {
                title: '日程名称',
                align: 'center',
                dataIndex: 'name'
            },
            {
                title: '互动场地',
                align: 'center',
                dataIndex: 'address'
            },
            {
                title: '预计参加人数',
                align: 'center',
                dataIndex: 'pro_people_team',
                render(text, record, index) {
                    return (
                        <span style={buttonWrapperStyle}>
                            {
                                `${text}${record.type === 1 ? '人' : record.type === 2 ? '队' : ''}`
                            }
                            &nbsp;&nbsp;
                            {
                                text !== 0 && (
                                    <a href="javascript:void(0)" onClick={() => peopleListOverview(2, record)} style={buttonStyle}>查看名单</a>
                                )
                            }

                        </span>
                    )
                }
            },
            {
                title: '参加人数',
                align: 'center',
                dataIndex: 'real_people_team',
                render(text, record, index) {
                    return (
                        <span style={buttonWrapperStyle}>
                            {
                                `${text}${record.type === 1 ? '人' : record.type === 2 ? '队' : ''}`
                            }
                            &nbsp;&nbsp;
                            {
                                text !== 0 && (
                                    <a href="javascript:void(0)" onClick={() => peopleListOverview(3, record)} style={buttonStyle}>查看名单</a>
                                )
                            }
                        </span>
                    )
                }
            },
            {
                title: '晋级人数',
                align: 'center',
                dataIndex: 'next_people_team',
                render(text, record, index) {
                    const {org_id} = baseInfo;
                    return (
                        <span style={buttonWrapperStyle}>
                            {
                                `${text}${record.type === 1 ? '人' : record.type === 2 ? '队' : ''}`
                            }
                            &nbsp;&nbsp;
                            {
                                (org_id === userInfo.root_oid &&  text !== 0) && (
                                    <a href="javascript:void(0)" style={{ ...buttonStyle, right: '-45px' }} onClick={() => peopleListOverview(4, record)}>颁奖</a>
                                )
                            }
                        </span >
                    )
                }
            }
        ],
        peopleColumns = [
            // {
            //     title: '项目',
            //     align: 'center',
            //     dataIndex: 'title',
            //     render(value, row, index) {
            //         return {
            //             children: <span>{value || '-'}</span>,
            //             props: {
            //                 rowSpan: row.rowSpan
            //             }
            //         }
            //     }
            // },
            {
                title: '人数',
                align: 'center',
                dataIndex: 'num',
                render(value, row, index) {
                    return (
                        <span>
                            {row.num_type ? (row.num_type + ': ') : ''}{value || '>'}
                        </span>
                    )
                }
            },
            {
                title: '占比',
                align: 'center',
                dataIndex: 'percent'
            }
        ];

    let prizeColumns = [
        {
            title: '奖项名',
            align: 'center',
            dataIndex: 'r_name',
            width: 160
        },
        {
            title: '奖项内容',
            align: 'center',
            dataIndex: 'content',
            render(text, record, index) {
                if (Array.isArray(text) && text.length !== 0) {
                    return (
                        <div>
                            {
                                text.map((item, index) => {
                                    return (
                                        <div>
                                            <span>{item.type === 2 ? item.prizeName : '积分'}：{item.num}</span>
                                        </div>
                                    )
                                })
                            }
                        </div>
                    )
                }
                return '-';
            }
        },
        {
            title: '姓名',
            align: 'center',
            dataIndex: 'u_name',
            width: 140
        },
        {
            title: '电话',
            align: 'center',
            dataIndex: 'phone',
            width: 160
        }
    ];
    //1.个人互动，2.团体互动
    // console.log('activityType', activityType);
    if (activityType === 2) {
        prizeColumns.push({
            title: '队伍',
            align: 'center',
            dataIndex: 'team',
            width: 180,
            render(text, record, index) {
                return (
                    <span>{text || '-'}</span>
                )
            }
        });
    }

    return (
        <div className={'activity-details-main'}>
            <header className={'header-wrapper'}>
                <HeaderCard style={{ width: '45%' }} title={'标题'}>
                    {baseInfo.title || ''}
                </HeaderCard>
                <HeaderCard style={{ width: '22.5%' }} title={'报名时间'}>
                    <div>
                        {
                            baseInfo.sign_up_time || ''
                        }
                    </div>
                    至
                    <div>
                        {
                            baseInfo.sign_up_end_time || ''
                        }
                    </div>
                </HeaderCard>
                <HeaderCard style={{ width: '22.5%' }} title={'互动时间'}>
                    <div>
                        {
                            baseInfo.start_time || ''
                        }
                    </div>
                    至
                    <div>
                        {
                            baseInfo.end_time || ''
                        }
                    </div>
                </HeaderCard>
            </header>
            <section className={'table-wrapper summary-table-wrapper'}>
                {/* {JSON.stringify(summaryColumns)}
                {JSON.stringify(summaryTableDataSource)} */}
                <Table
                    columns={summaryColumns}
                    dataSource={summaryTableDataSource}
                    bordered={true}
                    pagination={false}
                />
            </section>
            <section className={'table-wrapper programme-table-wrapper'}>
                <Divider>互动日程</Divider>
                {/* {JSON.stringify(programmeColumns)}
                {JSON.stringify(programmeTableDataSource)} */}
                <Table
                    columns={programmeColumns}
                    dataSource={programmeTableDataSource}
                    bordered={true}
                    pagination={false}
                />
            </section>
            {   baseInfo.org_id === userInfo.root_oid &&
                <section className={'table-wrapper price-table-wrapper'}>
                    <Divider>颁奖明细</Divider>
                    {/* {JSON.stringify(prizeColumns)}
                {JSON.stringify(prizeTableDataSource)} */}
                    <Table
                        columns={prizeColumns}
                        dataSource={prizeTableDataSource}
                        bordered={true}
                        pagination={false}
                    />
                </section>
            }
            <section className={'table-wrapper people-table-wrapper'}>
                <Divider>互动人员情况</Divider>
                {/*<header className={'people-table-header-wrapper'}>*/}
                {/*<div className="tab-wrapper">*/}
                {/**/}
                {/*</div>*/}
                {/*<div className="button-wrapper">*/}
                {/*<Button>下载结果</Button>*/}
                {/*</div>*/}
                {/*</header>*/}
                <div className={'tab-container'}>
                    {/* <Button type={'primary'} className={'download-button detail-download-btn'} onClick={downloadHandler}>
                        下载结果
                    </Button> */}
                    <a className={'download-button download-btn detail-download-btn'} 
                    onClick={()=>{
                        fileDownload(downloadUrl)
                      }}
                      href="javascript:void(0);"
                    >下载结果</a>
                    {/* //所有维度
                    allCountTableDataSource,
                    //年龄维度
                    ageTableDataSource,
                    //性别维度
                    genderTableDataSource,
                    //学历维度
                    educationDegreeTableDataSource,
                    //民族维度
                    nationTableDataSource,
                    //其他维度
                    otherTableDataSource, */}
                    <LoadingModal modalVisible={loadingVisible} tip={'加载中，请稍候...'} />
                    <Tabs defaultActiveKey="1" onChange={tabChangeHandler}>
                        <TabPane tab="全部" key="all">
                            <Table
                                columns={peopleColumns}
                                dataSource={allCountTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane>
                        <TabPane tab="年龄" key="age">
                            <Table
                                columns={peopleColumns}
                                dataSource={ageTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane>
                        <TabPane tab="性别" key="gender">
                            <Table
                                columns={peopleColumns}
                                dataSource={genderTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane>
                        {/* <TabPane tab="文化程度" key="4">
                            <Table
                                columns={peopleColumns}
                                dataSource={educationDegreeTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane>
                        <TabPane tab="民族" key="5">
                            <Table
                                columns={peopleColumns}
                                dataSource={nationTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane> */}
                        {/* <TabPane tab="其他身份" key="6">
                            <Table
                                columns={peopleColumns}
                                dataSource={otherTableDataSource}
                                bordered={true}
                                pagination={false} />
                        </TabPane> */}
                    </Tabs>
                </div>
            </section>
        </div>
    );
};