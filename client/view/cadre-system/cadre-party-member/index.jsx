import React, { useEffect, useState, useRef } from 'react'
import "./index.less"
import OrgTree from 'client/components/org-tree'
import { Table, Button } from 'antd';
import { getTeamMemberUserList } from "client/apis/cadre-portrait"
import CadreIndexImage from '../cadre-index-image'
import EvaIndex from '../cadre-index-image/EvaIndex'
import { uploadPhoto, downloadZip, loopDownloadZip, startZip, uploadCadreElement, downloadCadreElement, downloadCadreElementStatus, downloadCadreElementFile } from 'client/apis/cadre-portrait'; // 假设这是上传图片的API
import ImageQueue from './ImageQueue';
import { message, Spin } from 'antd';
import axios from 'axios'

function index() {

    const [data, setData] = useState([])
    const [page, setPage] = useState(1)
    const [notEmptyData, setNotEmptyData] = useState([])
    // 网络请求
    const axiosToken = useRef(axios.CancelToken.source())

    const [orgId, setOrgId] = useState(window.sessionStorage.getItem('_oid'))

    const [currentUser, setCurrentUser] = useState(null);
    const [currentUserEva, setCurrentUserEva] = useState(null);
    const updateCurrentUser = useRef(() => { });
    const updateCurrentUserEva = useRef(() => { });

    updateCurrentUser.current = setCurrentUser;
    updateCurrentUserEva.current = setCurrentUserEva;


    const [processing, setProcessing] = useState(false);
    const [progress, setProgress] = useState(0);
    const [total, setTotal] = useState(0);
    const [processed, setProcessed] = useState(0);

    const columns = [
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 100,
        },
        {
            title: '现任职务',
            dataIndex: 'current_job',
            key: 'current_job',
            width: 200,
        },
        {
            title: '出生年月',
            dataIndex: 'birthday',
            key: 'birthday',
            width: 120,
            render: (text) => text
        },
        {
            title: '任现职务时间',
            dataIndex: 'current_job_time',
            key: 'current_job_time',
            width: 120,
        },
        {
            title: '全日制学历',
            dataIndex: 'diploma',
            key: 'diploma',
            width: 120,
        },
        {
            title: '毕业院校及专业',
            dataIndex: 'school',
            key: 'school',
            width: 180,
        },
        {
            title: '干部指数',
            dataIndex: 'cadre_index',
            key: 'cadre_index',
            width: 100,
        },
        {
            title: '指数同序列排名',
            dataIndex: 'cadre_index_rank',
            key: 'cadre_index_rank',
            width: 140,
        }
    ];
    const loadData = async () => {
        if (loading) {
            axiosToken.current.cancel()
        }
        axiosToken.current = axios.CancelToken.source()

        setPage(1)
        setLoading(true)

        try {
            const { data: res } = await getTeamMemberUserList({
                org_id: orgId,
                cancelToken: axiosToken.current.token
            })
            if (res.code === 0) {
                setData(res.data)
                setNotEmptyData(res.data.filter(item => item.cadre_index !== '-'))
            }
        } catch (error) {
        } finally {
            setLoading(false)
        }

    }

    const onChange = ([org_id]) => {
        setOrgId(org_id)
    }
    const taskStack = useRef([]);
    const taskStackEva = useRef([]);

    const onDownload = async () => {
        if (!notEmptyData.length) {
            message.warning('没有可处理的数据');
            return;
        }

        if (taskStack.current.length) {
            return
        }

        setProcessing(true);
        setProgress(0);
        setProcessed(0);
        setTotal(notEmptyData.length);


        for (let i = 0; i < notEmptyData.length; i++) {
            taskStack.current.push({
                user_id: notEmptyData[i].user_id,
                task: () => {
                    updateCurrentUser.current(notEmptyData[i]);
                }
            })
        }

        runTask();
    };
    const onDownloadEva = async () => {
        if (!notEmptyData.length) {
            message.warning('没有可处理的数据');
            return;
        }

        if (taskStackEva.current.length) {
            return
        }

        setProcessing(true);
        setProgress(0);
        setProcessed(0);
        setTotal(notEmptyData.length);


        for (let i = 0; i < notEmptyData.length; i++) {
            taskStackEva.current.push({
                user_id: notEmptyData[i].user_id,
                task: () => {
                    updateCurrentUserEva.current(notEmptyData[i]);
                }
            })
        }

        runTaskEva();
    };

    const runTask = () => {
        if (taskStack.current.length) {
            const shift = taskStack.current.shift();
            shift.task();
        }
    }
    const runTaskEva = () => {
        if (taskStackEva.current.length) {
            const shift = taskStackEva.current.shift();
            shift.task();
        }
    }
    // 开始下载
    const startDownload = async () => {
        await startZip({ org_id: orgId })
    }
    // 先轮询查询生成进度，循环查询直到生成完成
    const loopDownload = async () => {
        return new Promise((resolve, reject) => {
            let timer = setInterval(async () => {
                const { data: res } = await loopDownloadZip({ org_id: orgId })
                // 0未完成 1完成
                if (res.code === 0) {

                    if (res.data == 0) {
                        return
                    }

                    clearInterval(timer)

                    await downloadZip({ org_id: orgId })

                    resolve()

                } else {
                    clearInterval(timer)

                    reject(res.message)
                }
            }, 1000)
        })
    }
    const loopDownloadEva = async () => {
        return new Promise((resolve, reject) => {
            let timer = setInterval(async () => {
                const { data: res } = await downloadCadreElementStatus({ org_id: orgId })
                // 0未完成 1完成
                if (res.code === 0) {
                    if (res.data == 0) {
                        return
                    }

                    clearInterval(timer)

                    await downloadCadreElementFile({ org_id: orgId })

                    resolve()

                } else {
                    clearInterval(timer)

                    reject(res.message)
                }
            }, 1000)
        })
    }



    const handleImageGenerated = async ({ file, user_id, name, org_id }) => {
        // document.querySelector('.imageContainer').src = URL.createObjectURL(imageData)
        // message.loading(`图片上传中${processed}/${total}`)
        console.log("反馈单运行")
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('org_id', org_id);
            formData.append('user_id', user_id);

            await uploadPhoto(formData);

            setProcessed(prev => {
                const newProgress = prev + 1;
                setProgress((newProgress / total) * 100);
                return newProgress;
            });

            if (taskStack.current.length === 0) {
                message.loading(`正在下载文件`)
                await startDownload()

                await loopDownload()

                message.destroy()

                message.success(`文件下载完成`)

                setProcessing(false)
            } else {
                runTask();
            }

        } catch (error) {
            console.log("🚀 ~ handleImageGenerated ~ error:", error)
            message.error(`用户 ${name} 的图片上传失败`);

            setProcessing(false)

            // 可以选择重试或继续处理下一个
        }
    };

    const startDownloadEva = async () => {
        await downloadCadreElement({ org_id: orgId })
    }
    const handleImageGeneratedEva = async ({ file, user_id, name, org_id }) => {
        // document.querySelector('.imageContainer').src = URL.createObjectURL(imageData)
        // message.loading(`图片上传中${processed}/${total}`)
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('org_id', org_id);
            formData.append('user_id', user_id);

            await uploadCadreElement(formData);

            setProcessed(prev => {
                const newProgress = prev + 1;
                setProgress((newProgress / total) * 100);
                return newProgress;
            });

            if (taskStackEva.current.length === 0) {
                message.loading(`正在下载文件`)
                await startDownloadEva()

                await loopDownloadEva()

                message.destroy()

                message.success(`文件下载完成`)

                setProcessing(false)
            } else {
                runTaskEva();
            }

        } catch (error) {
            console.log("🚀 ~ handleImageGenerated ~ error:", error)
            message.error(`用户 ${name} 的图片上传失败`);

            setProcessing(false)

            // 可以选择重试或继续处理下一个
        }
    };

    const [loading, setLoading] = useState(false);

    useEffect(() => {
        loadData()
    }, [orgId])
    return (
        <div className='party-member-page'>
            <div className="left-box">
                <OrgTree onChange={onChange} />
            </div>
            <div className="right-box">
                <div className="btn-box">
                    <Button type="primary" loading={processing} disabled={loading} onClick={onDownloadEva}>下载干部指数要素分析</Button>
                    <Button type="primary" loading={processing} disabled={loading} onClick={onDownload}>下载干部指数反馈单</Button>
                </div>
                <Table columns={columns} pagination={{
                    pageSize: 10,
                    current: page,
                    total: data.length,
                    onChange: (page, pageSize) => {
                        setPage(page)
                    }
                }} bordered dataSource={data} loading={loading} />
                {/* <img src="" className='imageContainer' alt="" /> */}
            </div>
            <CadreIndexImage user={currentUser} onImageGenerated={handleImageGenerated} />
            <EvaIndex user={currentUserEva} onImageGenerated={handleImageGeneratedEva} />
            {
                processing && <div className="loading-mask">
                    <Spin tip={`文件上传中，请不要做其他操作${processed}/${total}`} />
                </div>
            }
        </div>
    )
}

export default index