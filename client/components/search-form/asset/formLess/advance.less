.advanced-search-form {
  width: 100%;
  margin: 0 auto;

  h2, h3 {
    text-align: left;
    font-weight: 700;
  }

  section {
    margin-bottom: 20px;
  }


  button {
    display: block;
    width: 100px;
    margin: 20px auto;
  }
}

.ant-input-focused {
  border-color: #1890ff !important; /* 蓝色边框 */
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important; /* 蓝色阴影 */
}

.title-with-indicator {
  position: relative;
  padding: 6px 12px;
  background: #DAF0FE;
}

.title-with-indicator::before {
  content: ''; /* 必须设置 content 属性才能启用伪元素 */
  position: absolute;
  left: 0;
  top: 0;
  width: 3px; /* 蓝色矩形的宽度 */
  height: 100%; /* 蓝色矩形的高度，与标题高度一致 */
  background-color: #1890ff; /* 蓝色背景 */
  border-radius: 2px; /* 圆角 */
}

.button-box {
  box-sizing: content-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 50px;
  background: #ffffff;
  border-top: 12px solid #f5f5f5;
  gap: 15px;
  button {
    width: 95px;
    height: 36px;
    font-size: 14px;
    margin: unset;
  }
}

/* 时间日期样式 */
.date-flex {
  display: flex;
  gap: 10px;

  .custom-input {
    width: 100px; /* 根据需要调整宽度 */
  }
}

.form-common-style {
  display: flex;
  flex-wrap: wrap;
  .custom-select {
    width: 100px; /* 根据需要调整宽度 */
  }
}

.text-style {
  margin: 0 12px;
  white-space: nowrap;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  line-height: 32px; /* 与 Select 的高度一致 */
}

.text-rank {
  white-space: nowrap;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  line-height: 32px; /* 与 Select 的高度一致 */
}

.text-year {
  margin: 0 0 0 12px;
  white-space: nowrap;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  line-height: 32px; /* 与 Select 的高度一致 */
}

.line {
  display: inline-block;
  width: 1px;
  height: 32px;
  background-color: #d9d9d9;
  margin: 0 6px;
}

.basic-info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  align-items: center;  
  .ant-form-item {
    flex: 1;
    min-width: 10%; /* 确保每个表单项占据 20% 的宽度 */
    margin-right: 10px;
    margin-bottom: 15px;

    .ant-form-item-label {
      width: 100%;
      text-align: left;
      margin-bottom: 8px;
      padding: 0;
    }
  }
}
// 波浪线的样式
.line_wave {
  display: flex;
  align-items: center;
  margin: 10px 0;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    border-top: 1px dashed #d9d9d9; /* 虚线样式 */
    z-index: 1;
  }
}
.form_title {
  .ant-form-item-label {
    font-weight: bold;
    margin: 12px 0px;
    font-size: 16px;
    &::before {
			margin-right: 9px;
			content: '';
			display: inline-block;
			width: 9px;
			height: 9px;
			background: #008eff;
		}
  }
}