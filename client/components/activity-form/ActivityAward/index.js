/**
 * 活动奖励
 * <AUTHOR>
 * 2018/4/23
 */
import React from 'react'
import PropTypes from 'prop-types'
import {
    Row,
    Col,
    Icon,
    Radio,
    Checkbox,
    InputNumber,
    Input
} from 'antd'

import { CircleButton } from '../sub-components/CustomizeButton'
import InputNumberAttributes from '../sub-components/InputNumberAttributes'
import adaptiveLayout from '../gridLayout'

const ActivityAwardWrap = {
    left: { span: 4 },
    right: { span: 20 }
}

//奖励积分/奖励现金一期不做，暂时隐藏

class ActivityAward extends React.Component {
    render() {
        const {
            form,
            emuns,
            preview,
            formFields,
            handleEvents,
            activityFormState
        } = this.props
        const { getFieldDecorator, getFieldValue } = form

        const commodityDisabled = (!getFieldValue(formFields.rewardTypeCommodity) && !getFieldValue(formFields.rewardTypeIntegral))
        const rewardModelTypeDisabled = getFieldValue(formFields.rewardModelType) === emuns.rewardModelEnum.normal.value

        return <div>
            <Row className="ant-form-item">
                <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                    <label title="奖励方式">奖励方式</label>
                </Col>
                <Col {...adaptiveLayout.formWrapper.wrapperCol} className="custom-form-item-col">
                    <Row type="flex" align="middle">
                        <Col {...ActivityAwardWrap.left}>
                            {getFieldDecorator(formFields.rewardTypeIntegral, {
                                initialValue: activityFormState.rewardTypeIntegral.type,
                                valuePropName: 'checked'
                            })(
                                <Checkbox disabled={preview}>奖励积分</Checkbox>
                            )}
                        </Col>
                        <Col {...ActivityAwardWrap.right}>
                            {getFieldDecorator(formFields.rewardTypeIntegralSource, {
                                initialValue: activityFormState.rewardTypeIntegral.num
                            })(
                                <InputNumber
                                    placeholder="请输入"
                                    formatter={value => `${value}分/人/次`}
                                    parser={value => value.replace('分/人/次', '')}
                                    disabled={!getFieldValue(formFields.rewardTypeIntegral)}
                                    min={1}
                                    max={999999999}
                                    style={{ width: 120 }} />
                            )}
                            <span style={{ marginLeft: 40, marginRight: 10 }}>参加奖励上限(总额)</span>
                            {getFieldDecorator(formFields.rewardTypeIntegralTotal, {
                                initialValue: activityFormState.rewardTypeIntegral.total
                            })(
                                <InputNumber style={{ width: 100 }} disabled={preview || !getFieldValue(formFields.rewardTypeIntegral)}/>
                            )
                            }
                        </Col>
                    </Row>
                    <hr />
                    <div style={{ height: 5 }}></div>
                    <Row>
                        <Col {...ActivityAwardWrap.left}>
                            {getFieldDecorator(formFields.rewardTypeCommodity, {
                                initialValue: activityFormState.rewardTypeCommodity,
                                valuePropName: 'checked'
                            })(
                                <Checkbox disabled={preview} onChange={handleEvents.selectCommodity}>奖励物品</Checkbox>
                            )}
                        </Col>
                        <Col {...ActivityAwardWrap.right}>
                            <a href="javascript:void(0);" disabled={commodityDisabled} onClick={handleEvents.showCommodityModal}>选择物品</a>
                            <Row align="middle">
                                {
                                    activityFormState.rewardTypeCommodityData.map((item, index) => (
                                        <Col key={item.key}>
                                            <div style={{ margin: '10px 0' }}>
                                                数量：
                                                <InputNumberAttributes unit="个" style={{ marginRight: 15 }}>
                                                    <InputNumber
                                                        placeholder="请输入"
                                                        disabled={commodityDisabled || preview}
                                                        defaultValue={item.num}
                                                        min={0}
                                                        max={999999}
                                                        onChange={value => handleEvents.changeCommodityItem(value, index, item)}
                                                        style={{ width: 70 }} />
                                                </InputNumberAttributes>
                                                <span style={{ marginRight: 15 }}>已选中【{item.name}】</span>
                                                <CircleButton icon="gsg-shanchu" size="small" disabled={commodityDisabled || preview} onClick={handleEvents.deleteCommodityItem.bind(null, index, item.key)} />
                                            </div>
                                        </Col>
                                    ))}
                            </Row>
                        </Col>
                    </Row>
                    {/* <hr />
                    <Row type="flex" align="middle">
                        <Col {...ActivityAwardWrap.left}>
                            {getFieldDecorator(formFields.rewardTypeMoney, {
                                initialValue: activityFormState.rewardTypeMoney.type,
                                valuePropName: 'checked'
                            })(
                                <Checkbox>奖励现金</Checkbox>
                            )}
                        </Col>
                        <Col {...ActivityAwardWrap.right}>
                            <span style={{ marginRight: 10 }}>奖金区间：</span>
                            {getFieldDecorator(formFields.rewardTypeMoneyMin, {
                                initialValue: activityFormState.rewardTypeMoney.min
                            })(
                                <InputNumber
                                    style={{ width: 120, textAlign: 'center' }}
                                    min={0}
                                    max={99999999}
                                    disabled={moneyDisabled}
                                    placeholder="请输入"
                                    formatter={value => `${value}元`}
                                    parser={value => value.replace('元', '')} />
                            )}
                            <span style={{ margin: '0 10px' }}>至</span>
                            {getFieldDecorator(formFields.rewardTypeMoneyMax, {
                                initialValue: activityFormState.rewardTypeMoney.max
                            })(
                                <InputNumber
                                    style={{ width: 120, textAlign: 'center' }}
                                    min={0}
                                    max={99999999}
                                    disabled={moneyDisabled}
                                    formatter={value => `${value}元`}
                                    parser={value => value.replace('元', '')}
                                    placeholder="请输入" />
                            )}
                        </Col>
                        <Col {...ActivityAwardWrap.right} offset={ActivityAwardWrap.left.span}>
                            <div style={{ margin: '10px 0' }}>
                                <span style={{ marginRight: 10 }}>奖金总额：</span>
                                {getFieldDecorator(formFields.rewardTypeMoneySum, {
                                    initialValue: activityFormState.rewardTypeMoney.sum
                                })(
                                    <InputNumber
                                        style={{ width: 120, textAlign: 'center' }}
                                        min={0}
                                        max={99999999}
                                        placeholder="请输入"
                                        disabled={moneyDisabled}
                                        formatter={value => `${value}元`}
                                        parser={value => value.replace('元', '')} />
                                )}
                                <span style={{ color: '#FF7875', marginLeft: 33 }}>余额：{activityFormState.rewardTypeMoney.surplus}</span>
                            </div>
                            <p style={{ color: '#999999', marginBottom: 10 }}>奖励会通过普惠服务发放给活动参与人</p>
                        </Col>
                    </Row> */}
                </Col>
            </Row>
            <Row className="ant-form-item" type="flex" align="middle">
                <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                    <label title="奖励模式">奖励模式</label>
                </Col>
                <Col {...adaptiveLayout.formWrapper.wrapperCol} className="custom-form-item-col2">
                    {getFieldDecorator(formFields.rewardModelType, {
                        initialValue: activityFormState.rewardModel.type
                    })(
                        <Radio.Group>
                            <Radio
                                disabled={commodityDisabled || preview}
                                style={{ marginRight: 20 }}
                                value={emuns.rewardModelEnum.normal.value}>
                                {emuns.rewardModelEnum.normal.label}
                            </Radio>
                            <Radio
                                disabled={commodityDisabled || preview}
                                value={emuns.rewardModelEnum.random.value}>
                                {emuns.rewardModelEnum.random.label}
                                <InputNumberAttributes unit="%" disabled={rewardModelTypeDisabled || preview}>
                                    {getFieldDecorator(formFields.rewardModelProbabilityOfWinning, {
                                        initialValue: activityFormState.rewardModel.probabilityOfWinning
                                    })(
                                        <InputNumber
                                            disabled={commodityDisabled || preview}
                                            style={{ width: 70, marginLeft: 10 }}
                                            placeholder="请输入"
                                            min={0}
                                            max={100} />
                                    )}
                                </InputNumberAttributes>
                            </Radio>
                        </Radio.Group>
                    )}
                </Col>
            </Row>
        </div>
    }
}

ActivityAward.propTypes = {
    //预览模式
    preview: PropTypes.bool,

    //antd表单对象
    form: PropTypes.object.isRequired,

    //表单字段
    formFields: PropTypes.shape({
        // rewardTypeIntegral: PropTypes.string.isRequired,                        //是否奖励积分
        // rewardTypeIntegralSource: PropTypes.string.isRequired,                  //奖励积分数据集
        rewardTypeCommodity: PropTypes.string.isRequired,                       //是否奖励物品
        // rewardTypeMoney: PropTypes.string.isRequired,                           //是否奖励现金
        // rewardTypeMoneyMin: PropTypes.string.isRequired,                        //奖金最小值
        // rewardTypeMoneyMax: PropTypes.string.isRequired,                        //奖金最大值
        // rewardTypeMoneySum: PropTypes.string.isRequired,                        //奖金总额
        rewardModelType: PropTypes.string.isRequired,                           //奖励模式
        rewardModelProbabilityOfWinning: PropTypes.string.isRequired            //随机抽取奖励
    }).isRequired,

    //表单默认数据
    emuns: PropTypes.shape({
        //获奖模式
        rewardModelEnum: PropTypes.shape({
            //参与即可获得奖励
            normal: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired,
            //随机抽取奖励
            random: PropTypes.shape({
                label: PropTypes.string.isRequired,
                value: PropTypes.number.isRequired
            }).isRequired
        }).isRequired
    }).isRequired,

    //表单model
    activityFormState: PropTypes.shape({
        //奖励积分
        // rewardTypeIntegral: PropTypes.shape({
        //     //奖励积分是否启用
        //     type: PropTypes.bool.isRequired,
        //     //奖励积分数额
        //     num: PropTypes.number.isRequired
        // }).isRequired,
        //奖励物品 {num,name,key}
        rewardTypeCommodityData: PropTypes.arrayOf(PropTypes.object).isRequired,
        rewardTypeCommodity: PropTypes.bool.isRequired,
        //奖励现金
        // rewardTypeMoney: PropTypes.shape({
        //     type: PropTypes.bool.isRequired,                    //false:禁用  true:启用
        //     min: PropTypes.bool.isRequired,                     //最低奖金
        //     max: PropTypes.bool.isRequired,                     //最大奖金
        //     sum: PropTypes.bool.isRequired,                     //奖金总额
        //     surplus: PropTypes.bool.isRequired                  //余额
        // }).isRequired,
        //获奖模式
        rewardModel: PropTypes.shape({
            //获奖类型
            type: PropTypes.number.isRequired,
            //获奖概率
            probabilityOfWinning: PropTypes.number.isRequired
        }).isRequired
    }).isRequired,

    //表单处理的事件合集
    handleEvents: PropTypes.shape({
        //勾选选择奖励物品
        selectCommodity: PropTypes.func,
        //选择物品按钮事件
        showCommodityModal: PropTypes.func.isRequired,
        //删除选择的物品
        deleteCommodityItem: PropTypes.func.isRequired,
        //更改物品数量
        changeCommodityItem: PropTypes.func.isRequired
    }).isRequired
}

ActivityAward.defaultProps = {
    preview: false
}

export default ActivityAward