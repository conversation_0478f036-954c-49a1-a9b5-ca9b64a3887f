import React, {Component} from 'react';
import {Tree} from 'antd';
import './index.less';
import PropTypes from 'prop-types';

const PermissionTree = ({
                            dataTree,
                            nodeSelectHandler,
                            selectIds,
                            checkStrictly
                        }) => {
    const {TreeNode} = Tree;
    //无限级树，递归调用方法
    const recursion = (dataTree) => {
        return dataTree.map((node, index) => {
            if (node.children && node.children.length !== 0) {
                return (
                    <TreeNode selectable={false} key={node.menu_id} title={node.name}>
                        {
                            recursion(node.children)
                        }
                    </TreeNode>
                );
            } else {
                return (
                    <TreeNode selectable={false} key={node.menu_id} title={node.name}/>
                );
            }
        });
    };

    //选中树节点时的操作方法
    const checkHandler = (checkKeys, status) => {
        const {halfCheckedKeys} = status;
        //用于视图控制
        const selectIds = checkKeys;
        //用于数据提交
        const allSelectIds = checkKeys.concat(halfCheckedKeys);
        //调用外层传入的方法，将组建的数据传出
        nodeSelectHandler(selectIds, allSelectIds);
    };

    if (dataTree && dataTree.length !== 0) {
        return (
            <Tree checkable={true}
                  checkStrictly={checkStrictly}
                // onCheck={nodeSelectHandler}
                  onCheck={checkHandler}
                  checkedKeys={selectIds}
                  defaultExpandAll={true}>
                {recursion(dataTree)}
            </Tree>
        );
    } else {
        return null;
    }
};

PermissionTree.propTypes = {
    //渲染树的数据数组
    dataTree: PropTypes.array,
    //选择树的处理方法
    nodeSelectHandler: PropTypes.func,
    //选中树的节点+node.menu_id
    selectIds: PropTypes.array,
    //树的父子节点是否关联
    checkStrictly: PropTypes.bool
};


export default PermissionTree;