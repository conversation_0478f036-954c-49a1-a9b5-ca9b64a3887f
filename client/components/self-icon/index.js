import React from "react";
import { Icon } from "antd";

export default (props) => {
  const { type, style, className, onClick, oneself = false } = props;

  //如果传入type不是gsg-开头的自有图标，则返回antd Icon组件
  const reg = /^gsg-/;

  if ((reg.test(type) || oneself) && Icon.createFromIconfontCN) {
    const SelfIcon = Icon.createFromIconfontCN({
      scriptUrl: selfIconPath,
      extraCommonProps: {
        style,
        className,
        onClick
      }
    });
    return <SelfIcon type={`anticon-${type}`} />
  }
  return <Icon type={type} className={className} onClick={onClick} style={style} />
}
