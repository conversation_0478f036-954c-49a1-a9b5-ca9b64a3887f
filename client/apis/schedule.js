import { http } from 'client/tool/axios';
import { activityHost } from './config';

// 1061170401-新建日程
export const addSchedule = params => http.post(`${activityHost}/agenda/add`, params);

// 1061170402-日程列表
export const scheduleList = params => http.get(`${activityHost}/agenda/page`, params);

// 1061170403-日程详情
export const scheduleDetails = agenda_id => http.get(`${activityHost}/agenda/detail/${agenda_id}`);

// 1061170404-取消日程
export const deleteSchedule = agenda_id => http.delete(`${activityHost}/agenda/cancel/${agenda_id}`);

// 1061170405-参与人详情
export const scheduleJoinMemberDetails = agenda_id => http.get(`${activityHost}/agenda/participator-list/${agenda_id}`);

// 1061170406-参与人详情下载
export const scheduleJoinMemberExport = agenda_id => http.get(`${activityHost}/agenda/participator-list-export/${agenda_id}`);

// 1061170407-评论信息/操作记录查询
export const scheduleUserCommentLogList = agenda_id => http.get(`${activityHost}/agenda/comment/list/${agenda_id}`);

// 1061170408-评论
export const scheduleUserComment = params => http.post(`${activityHost}/agenda/comment/${params.agenda_id}`, {content: params.content});

// 1061170409-编辑日程
export const updateSchedule = params => http.post(`${activityHost}/agenda/update`, params);

// 1061170411-获取签到二维码
export const getScheduleSignQrCode = agenda_id => http.get(`${activityHost}/agenda/sign-in/qr-code/${agenda_id}`);

// 1061170411-获取签到人数
export const getAgendaSignInNum = agenda_id => http.get(`${activityHost}/agenda/sign-in-num/${agenda_id}`);

// 1061170412-是否使用动态二维码
export const useScheduleSignQrCode = params => http.post(`${activityHost}/agenda/sign-in/dynamic-qr-code/${params.agenda_id}`, {dynamic_qr_code: params.dynamic_qr_code});


