import { Button, Input, Table, Timeline, Upload, message } from "antd";
import { getUrlQuery } from "client/tool/util";
import Header from "components/search-header";
import { useRef, useState } from "react";
import "./index.less";

import {
  downloadImportFailData,
  downloadTemplate,
  exportFailComplaintReport,
  exportFailMattersCheck,
  exportFailPatrol,
  exportFailUserCert,
  exportFailUserMarriage,
  exportFailUserTrain,
  importAnnualEval,
  importAnnualReport,
  importAssignList,
  importChargeRange,
  importChargeRangeFail,
  importComplaintReport,
  importData,
  importJob,
  importMattersCheck,
  importMesosphereUserRoster,
  importPatrol,
  importPunishment,
  importPunishmentFail,
  importRelationshipGraph, // 新增导入社会关系的接口
  importReward,
  importRewardFail,
  importTakeAdvice,
  importTeamVisit,
  importUserCert,
  importUserMarriage,
  importUserTrain,
} from "client/apis/cadre-portrait";

function index({ history }) {
  // 文件上传
  const fileSource = useRef();
  // 文件信息
  const [fileInfo, setFileInfo] = useState({});
  // import / result
  const [pageType, setPageType] = useState("import");
  // 参数
  const [params, setParams] = useState({});
  // loading
  const [loading, setLoading] = useState({
    downloadLoading: false,
    importLoading: false,
  });
  // 数据源
  const [data, setData] = useState({
    fail_list: [],
    fail: 0,
    total: 0,
    success: 0,
    error_reasons: [],
  });
  // 定义页面类型,详细类型见下方Map
  const { type, org_name, org_id } = getUrlQuery();
  // 文件中所有key 都与下面的对应
  const titleMap = new Map([
    ["1", "导入重大现实表现"],
    ["2", "导入年度考核结果"],
    ["3", "导入干部任用记录"],
    ["4", "信访举报情况"],
    ["5", "巡视巡察评价"],
    ["6", "个人事项核查"],
    ["7", "婚姻状况"],
    ["8", "出国(境外)证件记录"],
    ["9", "培训情况"],
    ["10", "干部名册"],
    ["20", "导入社会关系"], // 新增
  ]);

  const paramsMap = new Map([
    ["1", "现实表现模板"],
    ["2", "年度考核模板"],
    ["3", "干部任用记录模板"],
    ["4", "信访举报情况"],
    ["5", "巡视巡察评价模版"],
    ["6", "个人事项核查模版"],
    ["7", "婚姻状况模版"],
    ["8", "出国证件记录模版"],
    ["9", "培训情况模版"],
    ["10", "干部名册"],
    ["20", "社会关系模板"], // 新增
  ]);
  // uploadParams
  const getUploadParams = (params) => {
    const formData = new FormData();

    Object.keys(params).forEach((key) => {
      formData.append(key, params[key]);
    });

    return formData;
  };

  // 类型
  const typeMap = {
    1: {
      title: "导入现实表现",
      params: "现实表现模板",
      upload: async () =>
        await importData(getUploadParams({ file: fileSource.current })),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "current_job",
        title: "时任职务",
      },
    },
    2: {
      title: "导入年度考核结果",
      params: "年度考核模板",
      upload: async () => {
        if (!params.year) {
          message.error("请设置考核年度");
          return false;
        }
        return await importAnnualEval(
          getUploadParams({ file: fileSource.current, year: params.year })
        );
      },
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "score",
        title: "综合得分",
      },
    },
    3: {
      title: "导入干部任用记录",
      params: "干部任用记录模板",
      upload: async () =>
        await importAssignList(getUploadParams({ file: fileSource.current })),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "assign_job",
        title: "拟任职务",
      },
    },
    4: {
      title: "信访举报情况",
      params: "信访举报情况",
      upload: async () =>
        await importComplaintReport(
          getUploadParams({ file: fileSource.current })
        ),
      downloadFail: exportFailComplaintReport,
      resultColumns: {
        key: "report_date",
        title: "信访日期",
      },
    },
    5: {
      title: "巡视巡察评价",
      params: "巡视巡察评价模版",
      upload: async () =>
        await importPatrol(getUploadParams({ file: fileSource.current })),
      downloadFail: exportFailPatrol,
      resultColumns: {
        key: "job",
        title: "时任职务",
      },
    },
    6: {
      title: "个人事项核查",
      params: "个人事项核查模版",
      upload: async () =>
        await importMattersCheck(getUploadParams({ file: fileSource.current })),
      downloadFail: exportFailMattersCheck,
      resultColumns: {
        key: "job",
        title: "时任职务",
      },
    },
    7: {
      title: "婚姻状况",
      params: "婚姻状况模版",
      upload: async () =>
        await importUserMarriage(getUploadParams({ file: fileSource.current })),
      downloadFail: exportFailUserMarriage,
      resultColumns: {
        key: "job",
        title: "现任职务",
      },
    },
    8: {
      title: "出国(境)证件记录",
      params: "出国证件记录模版",
      upload: async () =>
        await importUserCert(getUploadParams({ file: fileSource.current })),
      downloadFail: exportFailUserCert,
      resultColumns: {
        key: "job",
        title: "现任职务",
      },
    },
    9: {
      title: "培训情况",
      params: "培训情况模版",
      upload: async () =>
        await importUserTrain(getUploadParams({ file: fileSource.current })),
      downloadFail: exportFailUserTrain,
      resultColumns: {
        key: "job",
        title: "现任职务",
      },
    },
    10: {
      title: "导入干部名册",
      params: "干部名册",
      upload: async () =>
        await importMesosphereUserRoster(
          getUploadParams({ file: fileSource.current })
        ),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "current_job",
        title: "现任职务",
      },
    },
    11: {
      title: "导入职务信息",
      params: "职务",
      upload: async () => {
        const formData = new FormData();
        formData.append("file", fileSource.current);

        return await importJob(formData);
      },
      downloadFail: downloadImportFailData,
      columns: [
        {
          dataIndex: "职务简称",
          key: "name",
          title: "职务简称",
          width: 200,
          render: (text, record) => {
            return record.user_name || record.name;
          },
        },
        {
          dataIndex: "fail_result",
          key: "fail_result",
          title: "失败原因",
          render: (text, record) => {
            return record.fail_result || record.error_reason;
          },
        },
      ],
    },
    12: {
      title: "导入分管领域",
      params: "分管领域",
      upload: async () =>
        await importChargeRange(getUploadParams({ file: fileSource.current })),
      downloadFail: importChargeRangeFail,
      resultColumns: {
        key: "current_job",
        title: "现任职务",
      },
    },
    13: {
      title: "导入表彰表扬",
      params: "表彰表扬模板",
      upload: async () => {
        const formData = new FormData();
        formData.append("file", fileSource.current);

        return await importReward(formData);
      },
      downloadFail: importRewardFail,
      columns: [
        {
          dataIndex: "user_name",
          key: "user_name",
          title: "姓名",
          width: 200,
          render: (text, record) => {
            return record.user_name || record.name;
          },
        },
        {
          dataIndex: "birthday",
          key: "birthday",
          title: "出生年月",
          width: 200,
          render: (text, record) => {
            return record.birthday;
          },
        },
        {
          dataIndex: "fail_reason",
          key: "fail_reason",
          title: "失败原因",
          render: (text, record) => {
            return record.fail_reason || record.fail_result;
          },
        },
      ],
    },
    14: {
      title: "导入负面清单",
      params: "负面清单模板",
      upload: async () => {
        const formData = new FormData();
        formData.append("file", fileSource.current);

        return await importPunishment(formData);
      },
      downloadFail: importPunishmentFail,
      columns: [
        {
          dataIndex: "user_name",
          key: "user_name",
          title: "姓名",
          width: 200,
          render: (text, record) => {
            return record.user_name || record.name;
          },
        },
        {
          dataIndex: "birthday",
          key: "birthday",
          title: "出生年月",
          width: 200,
          render: (text, record) => {
            return record.birthday;
          },
        },
        {
          dataIndex: "fail_reason",
          key: "fail_reason",
          title: "失败原因",
          render: (text, record) => {
            return record.fail_reason || record.error_reason;
          },
        },
      ],
    },
    17: {
      title: "导入班子回访",
      params: "班子回访",
      upload: async () =>
        await importTeamVisit(getUploadParams({ file: fileSource.current })),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "current_job",
        title: "现任职务",
      },
    },
    18: {
      title: "导入年度述职",
      params: "年度述职",
      upload: async () =>
        await importAnnualReport(
          getUploadParams({ file: fileSource.current, type: 2 })
        ),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "current_job",
        title: "现任职务",
      },
    },
    19: {
      title: "导入征求意见",
      params: "征求意见",
      upload: async () =>
        await importTakeAdvice(getUploadParams({ file: fileSource.current })),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "current_job",
        title: "现任职务",
      },
    },
    20: {
      // 新增
      title: "导入社会关系",
      params: "社会关系模板",
      upload: async () =>
        await importRelationshipGraph(
          getUploadParams({ files: fileSource.current })
        ),
      downloadFail: downloadImportFailData,
      resultColumns: {
        key: "relation",
        title: "社会关系",
      },
    },
  };

  const [title, setTitile] = useState(() => {
    return typeMap[type].title;
  });

  // 模板
  const [keys, setKeys] = useState(() => {
    return typeMap[type].params;
  });
  //上传函数
  const uploadFunction = async () => {
    // 前置验证
    if (!fileSource.current) {
      return message.error("未选择文件");
    }

    // 获取上传函数
    const func = typeMap[type].upload;
    if (!func) {
      return message.error("未找到对应的上传函数");
    }
    // 设置加载状态
    setLoading((prev) => ({
      ...prev,
      importLoading: true,
    }));

    try {
      // 执行上传
      const res = await func();
      // 检查响应
      if (!res) {
        console.warn("⚠️ ~ 上传函数返回空结果");
        return;
      }

      if (res.data.code === 0) {
        setData(res.data.data);
        setPageType("result");
        message.success("导入完成");
      } else {
        message.error(res.data.message || "导入失败");
      }
    } catch (error) {
      message.error("导入过程中发生错误，请重试");
    } finally {
      // 无论成功失败都关闭加载状态
      setLoading((prev) => ({
        ...prev,
        importLoading: false,
      }));
    }
  };

  const exportFunctions = {
    3: downloadImportFailData,
    4: exportFailComplaintReport,
    5: exportFailPatrol,
    6: exportFailMattersCheck,
    7: exportFailUserMarriage,
    8: exportFailUserCert,
    9: exportFailUserTrain,
  };
  const downloadFail = async () => {
    const exportFunc = typeMap[type].downloadFail;
    if (exportFunc) {
      const res = await exportFunc({
        type: type,
        fail_list: data.fail_list,
        error_reasons: data.error_reasons,
      });

      if (res && res.data && res.data.code === 0) {
        message.success(res.data.message);
      }
    }
  };

  const onDownload = async () => {
    setLoading((pre) => {
      return {
        ...pre,
        downloadLoading: true,
      };
    });

    const res = await downloadTemplate({ file_name: keys });
    if (res && res.data) {
    }
    setTimeout(() => {
      setLoading((pre) => {
        return {
          ...pre,
          downloadLoading: false,
        };
      });
    }, 500);
  };
  const keyMap = {
    1: "score",
    2: "current_job",
    3: "assign_job",
  };
  const keyMap1 = {
    1: {
      key: "current_job",
      title: "时任职务",
    },
    2: {
      key: "score",
      title: "综合得分",
    },
    3: {
      key: "assign_job",
      title: "任用职务",
    },
    4: {
      key: "report_date",
      title: "信访日期",
    },
    5: {
      key: "job",
      title: "时任职务",
    },
    6: {
      key: "job",
      title: "时任职务",
    },
    7: {
      key: "job",
      title: "现任职务",
    },
    8: {
      key: "job",
      title: "现任职务",
    },
    9: {
      key: "job",
      title: "现任职务",
    },
  };
  const columns = typeMap[type].columns || [
    {
      dataIndex: "user_name",
      key: "user_name",
      title: "姓名",
      width: 200,
      render: (text, record) => {
        return record.user_name || record.name;
      },
    },
    {
      dataIndex: typeMap[type].resultColumns.key,
      key: typeMap[type].resultColumns.key,
      title: typeMap[type].resultColumns.title,
      width: 200,
    },
    {
      dataIndex: "fail_result",
      key: "fail_result",
      title: "失败原因",
      render: (text, record) => {
        return record.fail_result || record.error_reason;
      },
    },
  ];

  const beforeUpload = (file) => {
    fileSource.current = file;

    setFileInfo({
      name: file.name,
    });

    return false;
  };

  const onStartImport = async () => {
    await uploadFunction();
  };

  const Component = () => {
    const componentMap = {
      2: () => {
        return (
          <Timeline.Item dot={<span className="index-block">3</span>}>
            <div className="time-item">
              <div className="title">
                <span className="text"> 第三步：设置考核年度 </span>{" "}
              </div>
              <div className="title m-top-15">
                <Input
                  style={{ width: "200px" }}
                  placeholder="请输入"
                  value={params.year}
                  onChange={(e) => {
                    setParams({ year: e.target.value });
                  }}
                />
              </div>
            </div>
          </Timeline.Item>
        );
      },
    };
    return componentMap[type] ? componentMap[type]() : undefined;
  };
  return (
    <div className="import-page">
      <Header
        title={title}
        onBack={() => {
          pageType === "import" ? history.goBack() : setPageType("import");
        }}
      />
      <div className="ip-content">
        {pageType === "import" ? (
          <div className="import-page-import">
            <Timeline>
              <Timeline.Item dot={<span className="index-block">1</span>}>
                <div className="time-item">
                  <div className="title">
                    <span className="text"> 第一步：下载模板</span>{" "}
                    <Button
                      type="primary"
                      onClick={onDownload}
                      loading={loading.downloadLoading}
                    >
                      下载
                    </Button>
                  </div>
                  <div className="title m-top-10">
                    <span className="text">已选：{org_name}</span>
                  </div>
                </div>
              </Timeline.Item>
              <Timeline.Item dot={<span className="index-block">2</span>}>
                <div className="time-item">
                  <div className="title">
                    <span className="text"> 第二步：上传模板表 </span>{" "}
                    <Upload
                      onChange={() => {}}
                      beforeUpload={beforeUpload}
                      showUploadList={false}
                      accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    >
                      <Button type="primary">选择文件</Button>
                    </Upload>
                  </div>
                  <div className="file">
                    <span className="text">{fileInfo.name}</span>
                  </div>
                  <div className="title m-top-10">
                    <span className="text">
                      注意：请在7天内进行上传，请勿修改文件名！{" "}
                    </span>
                  </div>
                </div>
              </Timeline.Item>
              {Component()}
            </Timeline>

            <div className="btn-box">
              <Button
                type="primary"
                onClick={onStartImport}
                loading={loading.importLoading}
              >
                开始导入
              </Button>
              <Button
                onClick={() => {
                  history.goBack();
                }}
              >
                取消
              </Button>
            </div>
          </div>
        ) : (
          <div className="result">
            <div className="msg-box">
              导入完毕！本次共执行{data.total}
              条导入数据：成功{data.success}条，失败{data.fail}
              条，失败详情如下表。
            </div>
            <div className="btn-box">
              <Button
                type="primary"
                disabled={!data.fail}
                onClick={downloadFail}
              >
                下载失败结果
              </Button>
            </div>
            <div className="table-box">
              <Table
                columns={columns}
                dataSource={data.fail_list || data.error_reasons}
                bordered
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default index;
