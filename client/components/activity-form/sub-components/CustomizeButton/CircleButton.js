/**
 * 自定义按钮
 * <AUTHOR>
 * 2018/4/26
 */
import React from 'react'
import { Icon } from 'antd'
import PropTypes from 'prop-types'
import './style.less'

const CircleButton = ({ icon, size, ...props }) => <button type="button" className={'customize-button-circle-button ' + 'anticon-' + icon + ' ' + size} {...props}></button>

CircleButton.propTypes = {
    icon: PropTypes.string.isRequired,
    size: PropTypes.string
}

CircleButton.defaultProps = {
    size: '',       //small
    icon: null
}

export default CircleButton