.card-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  .header {
    flex-shrink: 0;
  }

  .content {
    padding: 20px;
    flex: 1 0;
    overflow: hidden;
  }
}

.sizeType {
}

.card-header {
  padding: 0 20px;
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  background-color: #daf4dd;

  .card-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 15px;
    width: 100%;
    background: url("../../images/card-icon.png") no-repeat left center / 8px
      24px;
    font-size: 24px;
    line-height: 24px;
    font-family: PingFang SC-Heavy, PingFang SC;
    font-weight: 800;
    color: #000000;

    .sub_title {
      font-size: 18px;
      color: #000000;
    }
  }
}
