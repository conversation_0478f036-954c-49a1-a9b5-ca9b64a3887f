import { useState, useEffect } from "react";
import { Radio, Input, Form, Divider, Button, message } from "antd";
import { SettleInProcess } from "apis/organize";
import SectionTitle from "components/activity-form/sub-components/SectionTitle";

const { TextArea } = Input;

const index = (props) => {
  const { form } = props;
  const { getFieldDecorator, validateFields } = form;
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 24 },
  };
  const [Radios, setRadios] = useState();
  // const [form] = Form.useForm();

  const onFinish = async (values) => {
    validateFields(async (error, values) => {
      console.log('-------', values);
      if (!error) {
        const params = {
          org_expand_id: props.state.org_id,
          audit_status: values.audit_status,
          say: values.say,
        }
        const { data } = await SettleInProcess(params);
        if (data.code == 0) {
          setTimeout(() => {
            window.history.go(-1)
          }, 1000);
          return message.success('提交成功');
        } else {
          return message.error('提交失败');
        }
      }
    });
  };

  return (
    <div className="CheckInAuditDetail-Audit">
      {/* <Divider orientation="left" orientationMargin="0" dashed>
        <Divider type="vertical" className="title-vertical" />
        审核意见
      </Divider> */}
      <SectionTitle title="审核意见" />
      <Form
        form={form}
        className={`CheckInAuditDetail-Audit-form`}
        onSubmit={e => { e.preventDefault(); onFinish() }}
      >
        <Form.Item>
          {getFieldDecorator('audit_status', {
            rules: [{ required: true, message: "请选择是否通过申请" }]
          })(
            <Radio.Group onChange={(i) => { setRadios(i.target.value) }}>
              <Radio value={2}>通过</Radio>
              <Radio value={3}>不通过</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item>
          {getFieldDecorator("say", {
            rules: [
              { max: 200, message: "最多输入200字" },
              { required: Radios == 2 ? false : true, message: "请填写不通过理由" }
            ]
          })(
            <TextArea rows={4} placeholder="请输入审核意见" />
          )}
        </Form.Item>
        <Form.Item>
          <Button type="primary" className="submit-btn" htmlType="submit">
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Form.create()(index);;