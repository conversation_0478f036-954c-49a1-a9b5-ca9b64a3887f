import React, { useState, useEffect } from "react";
import "./index.less";
import { message, Tabs, Input, Tree } from "antd";
import { getOrgTreeList } from "../../../../../apis/party-member-manage";

const { Search } = Input;
const { TabPane } = Tabs;
const { TreeNode } = Tree;

// 模拟数据
const imitationData = [
  {
    title: "0-0",
    key: "0-0",
    children: [
      {
        title: "0-0-0",
        key: "0-0-0",
      },
      {
        title: "0-0-1",
        key: "0-0-1",
        children: [
          {
            title: "0-0-1-0",
            key: "0-0-1-0",
          },
          {
            title: "0-0-1-1",
            key: "0-0-1-1",
          },
        ],
      },
    ],
  },
];

const getParentKey = (key, tree) => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

const TreeList = (props) => {
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
  const { name, oid: org_id } = userInfo;
  const [searchValue, setSearchValue] = useState(""); // 组织搜索框的value
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [treeData, setTreeData] = useState([]); // 接口接来的树数据
  const [dataList, setDataList] = useState([]); // 将多维数组展开成一维数组
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useEffect(() => {
    // 初始化
    setTreeData(imitationData);
    // getTreeListData();
    // console.log(dataList);
  }, []);

  const getTreeListData = async () => {

    const handleData = (data) => {
      for (const item of data) {
          
          if(item.has_child===1){
            handleData(item.child_org)
          }
      }
    };
    const { data: res } = await getOrgTreeList({ name, org_id });
    if (res.code !== 0) return message.error(res.message);

    // setTreeData(res.data);
  };

  // 节点展开时
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  // 搜索框的onChange
  const onChange = (e) => {
    // 把多维数组展开成一维数组
    const generateList = (data) => {
      const dataList_ = [];
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const { key } = node;
        dataList_.push({ key, title: key });
        if (node.children) {
          generateList(node.children);
        }
      }
      setDataList(dataList_);
    };
    const { value } = e.target;
    const expandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    setExpandedKeys(expandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  // 树节点
  const loop = (data) =>
    data.map((item) => {
      const index = item.title.indexOf(searchValue);
      const beforeStr = item.title.substr(0, index);
      const afterStr = item.title.substr(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: "#f50" }}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (item.children) {
        return (
          <TreeNode key={item.key} title={title}>
            {loop(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode key={item.key} title={title} />;
    });

  return (
    <Tabs onChange={() => {}} type="card" className="TreeList">
      <TabPane tab="党组织" key="1">
        <div className="TreeList_search">
          <Search
            placeholder="请输入组织名称"
            onChange={onChange}
            value={searchValue}
            className="ipt"
          />
        </div>
        <div className="TreeList_tree">
          <Tree
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onSelect={(rowKey, e) => {
              console.log("当前选择的节点", rowKey);
            }}
          >
            {loop(treeData)}
          </Tree>
        </div>
      </TabPane>
    </Tabs>
  );
};

export default TreeList;
