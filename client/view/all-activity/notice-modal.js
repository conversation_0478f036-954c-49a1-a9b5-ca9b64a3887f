import React from 'react';
import SiderContent from 'components/sider-content';
import { Form, Input, Button } from 'antd';

import InputTips from 'components/activity-form/sub-components/InputTips';

const NoticeModal = (props) => {
  // console.log(props);
  const FormItem = Form.Item;
  const { form,
    closeHandler,
    currentActivityInfo,
    collapsed,
    isHideWrap,
    submitButtonLoading,
    onSubmit } = props;
  // console.log(form);
  const { getFieldDecorator, resetFields, validateFields } = form;
  const { TextArea } = Input;
  const siderProps = {
    collapsed,
    isHideWrap,
    title: '发送通知',
    onClose() {
      // console.log('关闭侧边栏');
      closeHandler();
      //重置表单
      resetFields();
    }
  }
  //表格布局
  const formLayout = {
    labelCol: {
      span: 4
    },
    wrapperCol: {
      span: 14
    }
  }
  //时间补0
  const appendZero = (value) => {
    let result = value;
    if (value < 10) {
      result = `0${value}`;
    }
    return result;
  }
  //显示时间，抹除秒显示
  const formatTime = (timeString) => {
    if (!timeString) {
      return '从本通知发送之日起';
    }
    const date = new Date(timeString);
    const result = `${date.getFullYear()}年${appendZero(date.getMonth() + 1)}月${appendZero(date.getDate())}日 ${appendZero(date.getHours())}:${appendZero(date.getMinutes())}`
    return result;
  }
  //提交处理
  const submitHandler = (e) => {
    e.preventDefault();
    validateFields((error, values) => {
      if (!error) {
        if (!values.activity_id) {
          values.activity_id = currentActivityInfo.activity_id;
        }
        if (!values.type) {
          values.type = currentActivityInfo.type;
        }
        if (!values.time) {
          values.time = formatTime(currentActivityInfo.start_time);
        }
        if (!values.address) {
          // values.address = (currentActivityInfo.type === 1 ||
          //   currentActivityInfo.type === 2 ||
          //   currentActivityInfo.type === 3) ? '在线参与' : currentActivityInfo.type === 4 ? '在线报名' : '未知地点';
          values.address = currentActivityInfo.type === 4 ? '在线报名' : '在线参与';
        }
        // console.log('提交表单', values, currentActivityInfo);
        onSubmit(values);
      }
    });
  }
  return <SiderContent {...siderProps}>
    <div className={'notice-modal'} id={'notice-modal'}>
      {/* {
        JSON.stringify(currentActivityInfo)
      } */}
      <Form
        onSubmit={submitHandler}
        hideRequiredMark={true}>
        <FormItem {...formLayout} label={'通知内容'}>
          <InputTips
            max={120}
            container={() => document.getElementById('notice-modal')}
            text={form.getFieldValue('content')}>
            {
              getFieldDecorator('content', {
                rules: [
                  {
                    required: true,
                    message: '请输入通知内容'
                  },
                  {
                    max: 120,
                    message: '通知内容不超过120字'
                  }
                ],
                initialValue: `参加${currentActivityInfo.title}的通知`,
                // validateTrigger: 'onBlur'
              })(
                <TextArea rows={5} placeholder='请输入通知内容' />
              )
            }
          </InputTips>
          {/* {
            currentActivityInfo.title
          } */}
        </FormItem>
        <FormItem {...formLayout} className={'no-margin'} label={'互动时间'}>
          {/* 时间显示优先级，报名时间=>开始时间=>当前时间 */}
          {
            formatTime(currentActivityInfo.sign_start_time || currentActivityInfo.start_time || null)
          }
        </FormItem>
        <FormItem {...formLayout} className={'no-margin'} label={'互动地点'}>
          {
            currentActivityInfo.type === 4 ? '在线报名' : '在线参与'
            // (currentActivityInfo.type === 1 ||
            //   currentActivityInfo.type === 2 ||
            //   currentActivityInfo.type === 3) ? '在线参与' : currentActivityInfo.type === 4 ? '在线报名' : '未知地点'
          }
        </FormItem>
        <FormItem {...formLayout} label={'互动主题'}>
          <InputTips
            max={70}
            container={() => document.getElementById('notice-modal')}
            text={form.getFieldValue('theme')}>
            {
              getFieldDecorator('theme', {
                rules: [
                  {
                    required: true,
                    message: '请输入互动主题'
                  },
                  {
                    max: 70,
                    message: '互动主题不超过70字'
                  }
                ],
                initialValue: currentActivityInfo.title
              })(
                <TextArea rows={2} placeholder='请输入互动主题' />
              )
            }
          </InputTips>
          {/* {currentActivityInfo.title} */}
        </FormItem>
        <FormItem wrapperCol={{
          offset: 4,
          span: 14
        }}>
          <Button type={'primary'} htmlType={'submit'} loading={submitButtonLoading}>
            发送通知
          </Button>
        </FormItem>
      </Form>
    </div>
  </SiderContent >
}

export default Form.create()(NoticeModal); 