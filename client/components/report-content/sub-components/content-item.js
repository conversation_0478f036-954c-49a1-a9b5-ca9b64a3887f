import React, { Component } from "react";
import { Form, Input, Row, Col, Icon } from "antd";
import "./content-item.less";
import { removeFileItem } from "antd/lib/upload/utils";

import propTypes from "prop-types";

class ContentItem extends Component {
  constructor() {
    super();
    this.state = {
      selfIncreasing: 2,
      list: []
    }
    this.addItem = this.addItem.bind(this);
    this.moveItem = this.moveItem.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.exportData = this.exportData.bind(this);
  }
  componentDidMount() {
    const { opts } = this.props;
    if (opts && Array.isArray(opts) && opts.length !== 0) {
      let list = [], selfIncreasing = 0;
      opts.forEach((opt, index) => {
        list.push({
          key: String(selfIncreasing++)
        });
      });
      this.setState({
        list,
        selfIncreasing
      });
    }

  }
  // 添加一个选项
  addItem() {
    let { list, selfIncreasing } = this.state;
    list.push({
      key: String(selfIncreasing++)
    });
    // console.log(list, selfIncreasing);
    this.setState({
      list,
      selfIncreasing
    });
  }
  // 删除一个选项
  removeItem(index) {
    let { list } = this.state;
    list.splice(index, 1);
    this.setState({
      list
    });
  }
  // 移动一个选项(默认上移)
  moveItem(index, type = "up") {
    let { list } = this.state;
    const target = list[index];
    list.splice(index, 1);
    if (type === "up") {
      index--;
    } else if (type === "down") {
      index++;
    }
    list.splice(index, 0, target);
    this.setState({
      list
    });
  }

  // 暴露方法用于外侧获取选项表单数据
  exportData() {
    const { list } = this.state;
    const { form } = this.props;
    const { validateFields } = form;
    let opts = [];
    validateFields((error, values) => {
      if (!error) {
        try {
          Object.keys(list).forEach((order, index) => {
            opts.push({
              opts_name: values[list[order].key],
              seq: ++index
            })
          });
        } catch (e) {
          console.error(e);
        }
      }
    });
    // console.log(opts);
    return opts;
  }

  render() {
    const FormItem = Form.Item;
    const { list } = this.state;
    const { form, opts = [], isEdit } = this.props;
    // console.log(form);
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        span: 2
      },
      wrapperCol: {
        span: 22
      }
    };
    return (
      <div className="content-item">
        <a onClick={this.addItem}>增加选项</a>
        <Form hideRequiredMark={true}>
          {
            (Array.isArray(list) && list.length !== 0) &&
            list.map((item, index) => {
              // console.log(opts, item, index);
              return (
                <FormItem label={`选项${index + 1}`} {...formItemLayout} key={item.key}>
                  <Row type="flex" align="middle">
                    <Col span={20}>
                      {
                        getFieldDecorator(`${item.key}`, {
                          initialValue: (isEdit && opts[item.key]) ? opts[item.key].opts_name : "",
                          rules: [
                            { required: true, message: "请填写选项内容" }
                          ]
                        })(
                          <Input placeholder="请填写选项内容..." />
                        )
                      }
                    </Col>
                    <Col span={4}>
                      <div className="button-wrapper">
                        {
                          index !== 0 &&
                          <span className="handle-button" onClick={() => this.moveItem(index, "up")}>
                            <Icon type="arrow-up" />
                          </span>
                        }
                        {
                          index !== list.length - 1 &&
                          <span className="handle-button" onClick={() => this.moveItem(index, "down")} >
                            <Icon type="arrow-down" />
                          </span>
                        }
                        {
                          list.length > 2 &&
                          <span className="handle-button close-button" onClick={() => this.removeItem(index)} >
                            <Icon type="close" />
                          </span>
                        }
                      </div>
                    </Col>
                  </Row>
                </FormItem>
              )
            })
          }
        </Form>
      </div>
    )
  }
}

ContentItem.propTypes = {
  // 是否编辑模式
  isEdit: propTypes.bool,
  // 从父级传入的选项列表，主要为编辑时使用
  opts: propTypes.array,
}

ContentItem.defaultProps = {
  opts: [
    { opts_name: "", seq: 1 },
    { opts_name: "", seq: 2 }
  ],
}

export default Form.create()(ContentItem);

