.news-column {
  .index-view-container {
    .main-content-container {
      padding: 30px;
      .handler-wrapper {
        display: flex;
        a {
          margin: 0 15px 0 15px;
        }
      }
    }
  }
  .modal-view-container {
    .ant-input,
    .ant-select-selection {
      background-color: #F7F8F9;
      // border-color: #E5E5E5;
    }
    .ant-modal-body {
      padding-top: 50px;
      padding-bottom: 40px;
    }
    .only-text {
      .ant-form-item-control {
        margin-top: 0;
      }
    }
    .ant-btn {
      background-color: #F7F8F9;
      &.ant-btn-primary {
        background-color: #F46E65;
        border-color: #F46E65;
      }
    }
    .button-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      .ant-btn {
        width: 115px;
        height: 36px;
        margin: 25px;
      }
    }
  }





  .item-mt0 .ant-form-item-control{
    margin-top: 0px;
  }



  .mmk{

  }

  .clipping {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 99999;
    iframe{
      width: 100%;
      height: 100%;
    }
  }
}

.news-columns-siderbar{
  .item-mt0 .ant-form-item-control{
    margin-top: 0px;
  }

}
