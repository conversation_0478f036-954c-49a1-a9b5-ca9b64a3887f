/**
 * 106121005-组织奖惩信息
 * 106121006-党员奖惩信息
 */

import { http } from 'client/tool/axios';
import { mtHost, ucHost } from './config';

// 106121005001-添加组织奖励/惩罚
export const addOrgPenalize = params => http.post(`${mtHost}/org/commend/penalize/add`, params);

// 106121005003-查询组织奖惩信息列表
export const getOrgPenalizeList = params => http.post(`${mtHost}/org/commend/penalize/list`, params);

// 106121005004-导出组织奖惩信息列表
export const exportOrgPenalizeList = params => http.get(`${mtHost}/org/commend/penalize/excel/list`, params);

// 106121005005-查询组织奖惩详情
export const getOrgPenalizeDetails = params => http.post(`${mtHost}/org/commend/penalize/detail`, params);

// 106121005006-修改组织奖惩信息
export const updateOrgPenalize = params => http.post(`${mtHost}/org/commend/penalize/edit`, params);

// 106121005007-删除组织奖惩
export const deleteOrgPenalize = params => http.post(`${mtHost}/org/commend/penalize/delete`, params);

/**********-------------------------------**********/

// 106121006001-添加党员奖励
export const addPartyPenalize = params => http.post(`${mtHost}/user/commend/penalize/add`, params);

// 106121006002-添加党员惩罚
//--------

// 106121006003-查询党员奖惩列表
export const getPartyPenalizeList = params => http.post(`${mtHost}/user/commend/penalize/query`, params);

// 106121006004-导出党员奖惩列表
export const exportPartyPenalizeList = params => http.post(`${mtHost}/user/commend/penalize/export`, params);

// 106121006005-查询党员奖惩详情
export const exportPartyPenalizeDetails = params => http.get(`${mtHost}/user/commend/penalize/select`, params);

// 106121006006-修改党员奖惩信息
export const updatePartyPenalize = params => http.post(`${mtHost}/user/commend/penalize/update`, params);

// 106121006007-删除党员奖惩
export const deletePartyPenalize = params => http.get(`${mtHost}/user/commend/penalize/del`, params);

// ----
export const getReasonListOption = params => http.get(`${ucHost}/uc/op/list-child`, params);

