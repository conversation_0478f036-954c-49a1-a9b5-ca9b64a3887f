/**
 * 
 * 会议类型管理, 接口
 * 
 */

const { http } = require('client/tool/axios');
import { mtmHost } from './config';

// 新增会议类别
export const addMeetingCategory = (data) => {
  return http.post(`${mtmHost}/category/add`, data);
}

// 修改会议类别
export const updateMeetingCategory = (data) => {
  return http.post(`${mtmHost}/category/update`, data);
}

// 删除会议类别
export const deleteMeetingCategory = (data) => {
  return http.delete(`${mtmHost}/category/del/${data}`);
}

// 新增会议类型
export const addMeetingType = data => {
  return http.post(`${mtmHost}/type/add`, data);
}

// 修改会议类型
export const updateMeetingType = data => {
  return http.post(`${mtmHost}/type/update`, data);
}

// 删除会议类型
export const deleteMeetingType = data => {
  return http.delete(`${mtmHost}/type/del/${data}`);
}

// 查询会议类型列表, all
export const queryAllMeetingTypeList = (data) => {
  return http.get(`${mtmHost}/type/list-all`, data)
}

// 查询会议类型列表 , page
export const queryMeetingTypeList = (data) => {
  return http.get(`${mtmHost}/type/list`, data)
}

// 查询类别列表(下拉框)
export const queryCategoryList = (data) => {
  return http.get(`${mtmHost}/category/list-all/${data}`)
}
// 配置活动类型列表（下拉框）
export const queryCategoryListOrgType = (data) => {
  return http.get(`${mtmHost}/type/update-user-rule`, data)
}
// 查询会议类别列表
export const queryMeetingCategoryList = data => {
  return http.get(`${mtmHost}/category/list`, data);
}