.teacher-preview-modal {
  .ant-modal-body {
    padding: 24px 140px 48px;
  }

  .preview-modal-tab {
    margin-bottom: 36px;
    text-align: center;

    & > :first-child {
      margin-right: 60px;
    }

    &-item {
      display: inline-block;
      position: relative;
      font-size: 16px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #999999;
      cursor: pointer;
    }

    .active {
      font-weight: bold;
      color: #ff4d4f;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -6px;
        width: 100%;
        height: 3px;
        background: #ff4d4f;
        border-radius: 2px;
      }
    }
  }

  .preview-modal-content {
    &-pc {
      &-title {
        margin-bottom: 16px;
        text-align: center;
        font-size: 24px;
        font-family: PingFang SC-Heavy, PingFang SC;
        font-weight: 800;
        color: #333333;
      }

      &-info {
        padding-bottom: 30px;
        border-bottom: 1px solid #eeeeee;
        text-align: center;
        font-size: 16px;
        font-family: <PERSON><PERSON><PERSON> SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;

        &-v1,
        &-v2 {
          margin-right: 24px;
        }
      }

      &-abs {
        margin-top: 24px;
        padding: 24px 29px;
        background: #f1f1f1;
        border-radius: 10px;
        font-size: 18px;
        font-family: PingFang SC-Bold, PingFang SC;
        color: #666666;

        & > span:first-child {
          font-weight: bold;
        }
      }

      &-wrap {
        margin: 40px 0;
        font-size: 18px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }

      &-src {
        font-size: 18px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }

    &-mobile {
      // display: flex;
      // justify-content: center;
      // flex-direction: column;
      position: relative;
      width: 100%;
      height: 700px;
      background: url("../../../../image/rectangle.png") no-repeat
        center/contain;

      &-top {
        padding-left: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: absolute;
        width: 328px;
        height: 230px;
        top: 20%;
        left: 36.7%;
        transform: translateY(-50%);
        border-bottom: 1px solid #eeeeee;
        .top {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          width: 310px;
          .message {
            margin-top: -5px;
            display: flex;
            flex-direction: column;
            // width: 182px;
            font-size: 12px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(102, 102, 102, 0.8);
            line-height: 22px;
          }
          .head-portrait {
            margin-right: 13px;
            width: 113px;
            height: 136px;
          }
          .name {
            // margin-top: 6px;
            margin-bottom: 6px;
            font-size: 20px;
            font-family: PingFang SC-Heavy, PingFang SC;
            font-weight: 800;
            color: #333333;
            line-height: 24px;
          }
          .unit {
            height: 40px;
          }

          .rank {
            display: flex;
            margin-bottom: 2px;
            font-size: 10px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(102, 102, 102, 0.8);
            // line-height: 22px;
            div {
              margin-right: 20px;
            }
          }
        }
        .contact {
          display: flex;
          flex-direction: column;
          .phone {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 12px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: 500;
            color: rgba(102, 102, 102, 0.6);
            line-height: 16px;
            // .img {
            //   margin-right: 2px;
            //   width: 16px;
            //   height: 16px;
            // }
          }
          .tags {
            display: flex;
            align-items: center;
            font-size: 8px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 500;
            color: #999999;
            line-height: 10px;
            .tag {
              display: table;
              padding: 2px 6px;
              margin-right: 11px;
              background: #f2f2f2;
              border-radius: 7px;
              opacity: 1;
              // background-color: #f2f2f2;
            }
          }
        }
      }
      &-content {
        position: absolute;
        display: flex;
        justify-content: center;
        width: 100%;
        height: 600px;
        top: 75%;
        // left: -2%;
        transform: translateY(-50%);
        .bottom {
          display: flex;
          flex-direction: column;
          margin-top: 45px;
          height: 400px;
          overflow: hidden;
          overflow-y: auto;
          .content {
            margin-bottom: 40px;
            width: 308px;
            text-indent: 2em;
          }
          .provide {
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #999999;
            line-height: 20px;
          }
        }
        .bottom::-webkit-scrollbar {
          display: none;
        }
      }
    }
  }
}
