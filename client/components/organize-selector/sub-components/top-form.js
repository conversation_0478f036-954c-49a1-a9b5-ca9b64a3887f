import React, { Component } from "react";
import { Form, Button, Select, Input, message as Message } from "antd";
import { getcodeList } from "apis/users";
import { getOrgInfo } from "apis/organize";
import { getFindTreeTypeByOp } from "apis/organisition";
import "./top-form.less";

import propTypes from "prop-types";
import { fetchStorage } from "tool/util";

class TopForm extends Component {
  constructor() {
    super();
    this.state = {
      // 数据字典，查询组织类型根节点
      orgTypeRoot: 1028,
      orgType1: [],
      orgType2: [],
    };
    this.fetchOrgType = this.fetchOrgType.bind(this);
    this.fetchOrgInfo = this.fetchOrgInfo.bind(this);
    this.fetchOrgList = this.fetchOrgList.bind(this);
  }

  componentWillMount() {
    const { orgTypeRoot } = this.state;
    // 初始化的时候，渲染组织类型下拉框之前获取此接口内容
    this.fetchOrgInfo(orgTypeRoot);
  }

  async fetchOrgInfo(orgTypeRoot) {
    const {
      initOrgType,
      distinguishOwnerTree,
      updateState,
      isFfgr,
      rootOrgId,
      rootOrgType,
    } = this.props;
    if (!isFfgr) {
      // console.log(isFfgr, rootOrgId, rootOrgType);
      let tree_type = null;
      if (rootOrgType) {
        const response = await getFindTreeTypeByOp({ org_type: rootOrgType });
        // console.log(response);
        const { data: body } = response;
        const { code, data, message } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        tree_type = data;
      } else {
        try {
          const currentOrg =
            (typeof window !== "undefined" &&
              window.sessionStorage.getItem("current-org")) ||
            "{}";
          const { owner_tree } = JSON.parse(currentOrg);
          tree_type = owner_tree;
        } catch (error) {
          console.error(error);
        }
      }
      let org_id =
        (typeof window !== "undefined" &&
          window.sessionStorage.getItem("_oid")) ||
        0;
      const response = await getOrgInfo({
        org_id: rootOrgId || Number(org_id),
        tree_type: tree_type || owner_tree,
      });
      const { data: body } = response;
      const { data, code, message } = body;
      if (code !== 0) {
        Message.error(message);
        return;
      }
      // distinguishOwnerTree 是否区分父树还是子树
      if (tree_type === 1 && distinguishOwnerTree) {
        // console.log("查询父树");
        // 第一个下拉框为所有的组织类型
        updateState(
          {
            tree_type: 1,
          },
          () => {
            this.fetchOrgType(1, orgTypeRoot);
          }
        );
      } else {
        const orgType1 = [
          {
            code: orgTypeRoot,
            op_key: data.org_type,
            op_value: data.type_name,
          },
        ];
        this.setState({
          orgType1,
        });
        this.fetchOrgType(2, data.org_type);
        initOrgType(data.org_type, tree_type);
      }
    } else {
      console.log("分管组织走这里");
      this.fetchOrgType(1, orgTypeRoot);
    }
  }

  async fetchOrgType(type = 1, value) {
    let { orgTypeRoot } = this.state;
    const { form, topFormData } = this.props;
    const { getFieldValue, resetFields } = form;
    let typeCode = 0;
    if (type === 1) {
      typeCode = orgTypeRoot;
    } else if (type === 2) {
      // 如果一级组织类别发生变更，重置二级组织类别选择
      resetFields(["org_type_2"]);
      typeCode = value || getFieldValue("org_type_1");
      // console.log(typeCode);
    }
    const response = await getcodeList({
      code: typeCode,
    });
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (type === 1) {
      this.setState(
        {
          orgType1: data,
        },
        () => {
          // console.log(data[0]);
          if (data && Array.isArray(data) && data.length !== 0) {
            // this.fetchOrgType(2, data[0].op_key);
            this.fetchOrgList(true);
          }
        }
      );
    } else if (type === 2) {
      this.setState({
        orgType2: data,
      });
    }
  }

  fetchOrgList(isReset = false) {
    const { form, topFormData } = this.props;
    form.validateFields((error, values) => {
      if (!error) {
        values.org_type = values.org_type_1;
        if (values.org_type_2) {
          values.org_type = values.org_type_2;
        }
        delete values.org_type_1;
        delete values.org_type_2;
        // console.log("获取组织列表", values);
        values.isReset = isReset;
        this.getFindTreeTypeByOp(values, topFormData);
      }
    });
  }

  async getFindTreeTypeByOp(values = {}, callback) {
    console.log("🚀 ~ TopForm ~ getFindTreeTypeByOp ~ values:", values);
    const { org_type } = values;
    console.log("🚀 ~ TopForm ~ getFindTreeTypeByOp ~ org_type:", org_type);
    if (org_type) {
      const response = await getFindTreeTypeByOp({ org_type });
      // console.log(response);
      const { data: body } = response;
      const { code, message, data } = body;
      if (code !== 0) {
        Message.error(message);
        return;
      }
      values.tree_type = data;
    }
    // console.log(values);
    callback && callback(values);
  }

  render() {
    const FormItem = Form.Item;
    const Option = Select.Option;
    const { orgType1, orgType2 } = this.state;
    const {
      form,
      topFormData,
      disabledOrgType1,
      tree_type,
      updateState,
      onlyDirectSub = false,
    } = this.props;
    const { validateFields, getFieldDecorator, getFieldValue, resetFields } =
      form;

    const formProps = {
      hideRequiredMark: true,
      layout: "inline",
      onSubmit: (e) => {
        e.stopPropagation();
        e.preventDefault();
        updateState(
          {
            initLock: true,
          },
          () => {
            this.fetchOrgList();
          }
        );
      },
    };

    const selectProps = {
      style: {
        width: "150px",
      },
      // dropdownMatchSelectWidth: false
    };

    const resetHandler = (e) => {
      e.preventDefault();
      resetFields();
      if (!disabledOrgType1) {
        this.setState({
          orgType2: [],
        });
      }
      this.fetchOrgList(true);
    };

    const orgType1DefaultValue = () => {
      const { tree_type, isFfgr, rootOrgType } = this.props;
      // 父树查询
      if (tree_type === 1 && !rootOrgType) {
        return null;
      }
      return isFfgr ? null : orgType1[0] ? orgType1[0].op_key : null;
    };

    if (onlyDirectSub) {
      return null;
    }

    return (
      <div className="top-form">
        <Form {...formProps}>
          <FormItem label="组织类型" className="no-margin">
            {getFieldDecorator("org_type_1", {
              initialValue: orgType1DefaultValue(),
            })(
              <Select
                style={{
                  // position: "relative",
                  // top: 4,
                  width: 150,
                }}
                onChange={(value) => {
                  resetFields(["org_type_2"]);
                  if (!value) {
                    this.setState({
                      orgType2: [],
                    });
                  } else {
                    this.fetchOrgType(2, value);
                  }
                }}
                disabled={disabledOrgType1}
              >
                {
                  <Option key={-1} value={null}>
                    全部
                  </Option>
                }
                {orgType1 &&
                  Array.isArray(orgType1) &&
                  orgType1.length !== 0 &&
                  orgType1.map((org, index) => {
                    return (
                      <Option
                        key={org.op_key}
                        value={org.op_key}
                        title={org.op_value}
                      >
                        {org.op_value}
                      </Option>
                    );
                  })}
              </Select>
            )}
          </FormItem>
          {getFieldValue("org_type_1") !== null && (
            <FormItem>
              {getFieldDecorator("org_type_2", {
                initialValue: null,
              })(
                <Select {...selectProps}>
                  <Option key={-1} value={null}>
                    全部
                  </Option>
                  {orgType2 &&
                    Array.isArray(orgType2) &&
                    orgType2.length !== 0 &&
                    orgType2.map((org, index) => {
                      return (
                        <Option
                          key={org.op_key}
                          value={org.op_key}
                          title={org.op_value}
                        >
                          {org.op_value}
                        </Option>
                      );
                    })}
                </Select>
              )}
            </FormItem>
          )}
          <FormItem>
            {getFieldDecorator("org_name", {
              initialValue: null,
              rules: [{ max: 30, message: "查询关键字长度不超过30字" }],
            })(
              <Input
                placeholder="请输入关键字查询"
                onPressEnter={(e) => {
                  e.stopPropagation();
                  this.fetchOrgList();
                }}
              />
            )}
          </FormItem>
          <FormItem className="no-margin">
            <Button
              htmlType="submit"
              style={{ width: "90px", height: "36px", marginRight: "18px" }}
              type="primary"
            >
              查询
            </Button>
            <Button
              htmlType="reset"
              style={{ width: "90px", height: "36px" }}
              onClick={(e) => {
                resetHandler(e);
              }}
            >
              重置
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}

TopForm.propTypes = {
  topFormData: propTypes.func,
  initOrgType: propTypes.func,
  disabledOrgType1: propTypes.bool,
  onlyDirectSub: propTypes.bool,
  isFfgr: propTypes.bool,
};

TopForm.defaultProps = {
  disabledOrgType1: false,
  onlyDirectSub: false,
  isFfgr: false,
};

export default Form.create()(TopForm);
