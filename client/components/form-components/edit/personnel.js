import React, { Component } from 'react'
import Sider from 'components/sider-content';
import {
  Row,
  Col,
  Checkbox,
  Button,
  Divider,
  message
} from 'antd';
import { unique } from 'tool/util';
import { getDepAll, getcodeList } from "apis/users";
const CheckboxGroup = Checkbox.Group;
const params = {
  isCloseClickMask: false,
  width: 800,
  title: '选择人员限制条件',
  className: "personnel-container"
}
const formWrapper = {
  labelCol: {
    xs: { span: 3 },
    sm: { span: 3 },
    md: { span: 3 },
    lg: { span: 3 },
    xl: { span: 3 }
  },
}
const formWrapperCol = {
  xs: { span: 20 },
  sm: { span: 20 },
  md: { span: 20 },
  lg: { span: 20 },
  xl: { span: 20 }
}

const dataFilter = (dataSource, ) => {
  return dataSource.map((_data) => {
    return {
      label: _data.op_value,
      value: _data.op_key,
      checked: true
    }
  })
}

const codeEnum = {
  1002: 'sex',
  1010: 'cardType',
  1013: 'political',
  1009: 'skillLevel',
  1022: 'party',
  1023: 'group',
  1024: 'union',
  1025: 'women'
}
class Personnel extends Component {
  constructor() {
    super()
    this.state = {
      sex: [], // 性别
      cardType: [], // 证件类型
      political: [], // 政治面貌
      skillLevel: [], //技术等级
      party: [], // 党组织
      group: [], // 团组织
      union: [], // 工会组织
      women: [], // 妇女组织
    }
  }
  initChecked() {
    const { state } = this.props
    // 初始填充值
    Object.keys(codeEnum).forEach((code) => {
      this.setState({
        [codeEnum[code]]: state.editRule[code] ? unique(state.editRule[code].split(',').map(Number)) : []
      })
    })
  }

  async loadList(code, list) {
    // console.log(code, list);
    const { updateState } = this.props;
    const { data } = await getcodeList({ code });
    if (data.code !== 0) return message.error(data.message);
    updateState({
      [list]: data.data
    });
  }

  async componentDidMount() {
    // 1002: 'sex',
    // 1010: 'cardType',
    // 1013: 'political',
    // 1009: 'skillLevel',
    // 1022: 'party',
    // 1023: 'group',
    // 1024: 'union',
    // 1025: 'women'
    const Enums = [
      { code: 1002, list: "sexList" },
      { code: 1010, list: "cardTypeList" },
      { code: 1013, list: "politicalList" },
      { code: 1009, list: "skillList" },
      { code: 1022, list: "partyList" },
      { code: 1023, list: "groupList" },
      { code: 1024, list: "unionList" },
      { code: 1025, list: "womenList" },
    ];

    Enums.forEach((item) => {
      // console.log(item);
      this.loadList(item.code, item.list);
    });
  }
  componentWillReceiveProps(props) {
    if (props.visible) {
      this.initChecked()
    }
  }
  onChange(name) {
    return (val) => {
      this.setState({
        [name]: val
      })
    }
  }
  saveRule() {
    // 生成选择人员限制条件
    const { handleSave } = this.props
    let rule = {}
    Object.keys(codeEnum).forEach((code) => {
      rule[code] = this.state[codeEnum[code]].join(',')
      if (rule[code].length === 0) {
        delete rule[code]
      }
    })
    handleSave(rule)
  }
  render() {
    const { visible, handleCancel, state } = this.props
    return (
      <Sider
        collapsed={!visible}
        isHideWrap={visible}
        {...params}
      >
        <Row className="ant-form-item limit-range ">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">性别: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.sexList)} value={this.state.sex} onChange={this.onChange.bind(this)('sex')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range ">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">证件类型: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.cardTypeList)} value={this.state.cardType} onChange={this.onChange.bind(this)('cardType')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range ">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">政治面貌: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.politicalList)} value={this.state.political} onChange={this.onChange.bind(this)('political')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">技术等级: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.skillList)} value={this.state.skillLevel} onChange={this.onChange.bind(this)('skillLevel')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">党组织: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.partyList)} value={this.state.party} onChange={this.onChange.bind(this)('party')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">团组织: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.groupList)} value={this.state.group} onChange={this.onChange.bind(this)('group')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">工会组织: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.unionList)} value={this.state.union} onChange={this.onChange.bind(this)('union')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label">妇女组织: </Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <div className={'limit-range-box'}>
              <CheckboxGroup options={dataFilter(state.womenList)} value={this.state.women} onChange={this.onChange.bind(this)('women')} />
              <Divider />
            </div>
          </Col>
        </Row>
        <Row className="ant-form-item limit-range">
          <Col {...formWrapper.labelCol} className="ant-form-item-label"></Col>
          <Col {...formWrapperCol} className="custom-form-item-col pl15">
            <Button type="primary" className='btns' style={{ width: 115 }} onClick={this.saveRule.bind(this)}>确定</Button>
            <Button className='btns' style={{ width: 115, marginLeft: 45 }} onClick={handleCancel}>取消</Button>
          </Col>
        </Row>
      </Sider>
    )
  }
}

export default Personnel
