import { fetchNewsColumn, moveNewsColumn, addNewsColumn, deleteNewsColumn, editNewsColumn } from 'client/apis/news';
// import { uploadBase64File } from "apis/file";
// import { CDN } from "apis/config";
import { addSourceColumn, editSourceColumn, deleteSourceColumn, fetchSourceColumns, moveSourceColumn } from 'client/apis/waterControlBureau'

export default {
  namespace: 'WaterControlBureauColumnMgt',
  state: {
    dataSource: []
  },
  effects: {
    //加载新闻栏目数据
    *fetchColumn({ payload }, { put }) {
      // console.log('获取组织数据');
      const response = yield fetchSourceColumns();
      // console.log(response);
      const { data: body } = response;
      const { code, message, data } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      for(let i = 0, length = data.length; i < length; i ++){
        data[i].siblingNodes = [...data]
        const {data: res} = yield fetchSourceColumns({ top_column_id: data[i].column_id });
        const { data: childrenData, code: childrenCode } = res;
        if (childrenCode !== 0) {
          throw new Error(message);
        }
        if(childrenData.length > 0) {
          data[i].disabled = true
        }
        childrenData.forEach(item => {
          item.siblingNodes = [...childrenData]
        })
        if(childrenData.length > 0) {
          data[i].children = childrenData
        }
        if(i === length - 1) {
          yield put({
            type: 'save',
            payload: {
              dataSource: data
            }
          })
        }
      }
    },
    //移动新闻栏目
    *moveColumn({ payload }, { put }) {
      const { data } = payload;
      const response = yield moveSourceColumn(data);
      // console.log(response);
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
    },
    //添加新闻栏目
    *addColumn({ payload }, { put }) {
      const { data } = payload;
      // console.log('提交表单', data);
      const response = yield addSourceColumn(data);
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
    },
    //删除新闻栏目
    *deleteColumn({ payload }, { put }) {
      const response = yield deleteSourceColumn(payload);
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
    },
    //编辑新闻栏目
    *editColumn({ payload }, { put }) {
      const { data } = payload;
      const response = yield editSourceColumn(data);
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
    },
    // *uploadImage({ payload }, { put }) {
    //   // const { data } = payload;
    //   const response = yield uploadBase64File(payload);
    //   const { data: body } = response;
    //   const { code, message, data } = body;
    //
    //   if (code !== 0) {
    //     throw new Error(message);
    //   }
    //   const { path } = data;
    //
    //   return CDN+'/'+path;
    // },
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    }
  }
}
