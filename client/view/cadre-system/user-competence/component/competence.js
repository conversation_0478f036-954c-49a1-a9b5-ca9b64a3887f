import React, { Component } from "react";
import {
  Modal,
  Input,
  Table,
  Button,
  message,
  Popconfirm,
  Tooltip,
} from "antd";
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import {
  getUserAuthPage,
  addUserOrg,
  delUserAuth,
} from "client/apis/cadre-portrait";
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export default class Competence extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible,
      currentPage: 1,
      dataSource: [],
      selectRows: [],
      orgs: [],
      searchName: "",
      organizeVisible: false,
    };
    this.handleCancel = this.handleCancel.bind(this);
    this.getPower = this.getPower.bind(this);
    this.rowSelection = this.rowSelection.bind(this);
    this.hideOrganizeModal = this.hideOrganizeModal.bind(this);
    this.showOrganizeModal = this.showOrganizeModal.bind(this);
    this.addUserOrg = this.addUserOrg.bind(this);
    this.removeRoleUser = this.removeRoleUser.bind(this);
    this.searchNameOnChange = this.searchNameOnChange.bind(this);
    this.inquire = this.inquire.bind(this);
  }
  componentDidMount() {
    this.setState({
      visible: this.props.visible,
    });
    if (Object.keys(this.props.activeCompetence1).length > 0) {
      this.getPower(this.props.activeCompetence1.sys_user_id);
    }
  }
  componentWillReceiveProps(next) {
    if (!next.competenceVisible2) {
      this.inquire();
    }
  }
  searchNameOnChange(e) {
    this.setState({
      searchName: e.target.value,
    });
  }
  hideOrganizeModal() {
    this.setState({
      organizeVisible: false,
    });
  }
  showOrganizeModal() {
    this.setState({
      organizeVisible: true,
    });
  }
  rowSelection(selectKey, selectRows) {
    this.setState({
      selectRows,
    });
  }
  handleCancel() {
    this.setState({
      visible: false,
    });
    this.props.hideCompetence1();
  }
  inquire() {
    this.getPower(
      this.props.activeCompetence1.sys_user_id,
      this.state.searchName
    );
  }
  getPower(sys_user_id, name) {
    const { currentPage } = this.state;
    const postData = {
      sys_user_id,
      name,
      page: currentPage,
    };
    getUserAuthPage(postData).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(d.message);
      }
      this.setState({
        dataSource: _data.data.data,
        total: _data.data.total,
      });
    });
  }
  removeRoleUser(r) {
    const oids = this.state.selectRows.map((d) => d.org_id);
    let param = [];
    if (r && r.org_id) {
      param = [
        {
          sys_user_id: this.props.activeCompetence1.sys_user_id,
          org_id: r.org_id,
        },
      ];
    } else {
      param = oids.map((item) => {
        return {
          sys_user_id: this.props.activeCompetence1.sys_user_id,
          org_id: item,
        };
      });
    }

    // const postData = {
    //   sys_user_id: this.props.activeCompetence1.sys_user_id,
    //   org_ids: r && r.org_id ? [r.org_id] : oids,
    // };
    delUserAuth(param).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(d.message);
      } else {
        message.success("删除成功");
        this.getPower(this.props.activeCompetence1.sys_user_id);
      }
    });
  }
  addUserOrg(uid, orgs, cb) {
    const postData = {
      sys_user_id: uid,
      org_ids: orgs.map((d) => d.org_id),
    };
    addUserOrg(postData).then((d) => {
      const _data = d.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        cb();
        this.inquire();
      }
    });
  }

  render() {
    const { total } = this.state;
    const organizeModalProps = {
      radio: false,
      checkAll: true,
      visible: this.state.organizeVisible,
      dataSource: this.state.dataSource,
      onlyDirectSub: false,
      breadcrumbClick: true,
      hasRoot: true,
      hideModal: () => {
        this.hideOrganizeModal();
      },
      loadOrganizeData: (data) => {
        // dataSource = dataSource.concat(data);
        this.addUserOrg(this.props.activeCompetence1.sys_user_id, data, () => {
          this.setState({
            orgs: [],
            dataSource: data,
          });
        });
        this.hideOrganizeModal();
      },
      ref: (ref) => {
        this.organizeModal = ref;
      },
    };
    const columns = [
      {
        title: "序号",
        align: "center",
        width: "44",
        render: (t, row, i) => {
          return i + 1;
        },
      },
      {
        title: "组织名称",
        dataIndex: "org_name",
        key: "org_name",
        align: "center",
        width: "162px",
        render: (text) => (
          <Tooltip placement="top" title={text} arrowPointAtCenter>
            <span className="col-sql">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: "拥有角色",
        dataIndex: "role_name",
        key: "role_name",
        align: "center",
        width: "162px",
        render: (text) => (
          <Tooltip placement="top" title={text} arrowPointAtCenter>
            <span className="col-sql">{text}</span>
          </Tooltip>
        ),
      },
      // {
      //   title: "所属用户组",
      //   dataIndex: "group_names",
      //   key: "group_names",
      //   align: "center",
      //   width: "162px",
      //   render: (text) => (
      //     <Tooltip placement="top" title={text} arrowPointAtCenter>
      //       <span className="col-sql">{text}</span>
      //     </Tooltip>
      //   ),
      // },
      {
        title: "操作",
        dataIndex: "desc",
        key: "desc",
        align: "center",
        render: (t, row, i) => {
          return (
            <React.Fragment>
              <a
                href="javascript:;"
                onClick={() => {
                  this.props.showCompetence2({
                    user_id: this.props.activeCompetence1.sys_user_id,
                    org_id: row.org_id,
                    user_name: this.props.activeCompetence1.user_name,
                    org_name: row.org_name,
                  });
                }}
              >
                编辑权限
              </a>
              <Popconfirm
                placement="top"
                title={"确定你的操作"}
                onConfirm={() => {
                  this.removeRoleUser(row);
                }}
                okText="确定"
                cancelText="取消"
              >
                <a href="javascript:;" style={{ marginLeft: 10 }}>
                  移除
                </a>
              </Popconfirm>
            </React.Fragment>
          );
        },
      },
    ];
    const onPageChange = (page) => {
      this.setState(
        {
          currentPage: page,
        },
        () => {
          this.inquire();
        }
      );
    };
    return (
      <React.Fragment>
        <Modal
          title="编辑用户权限"
          visible={this.state.visible}
          onCancel={this.handleCancel}
          width={900}
          height={688}
          footer={null}
        >
          <div>用户姓名: {this.props.activeCompetence1.user_name}</div>
          <div className="clearfix" style={{ marginTop: 25 }}>
            <div style={{ float: "left" }}>
              <Input
                placeholder="请输入组织名称"
                style={{ width: 180, marginRight: 28 }}
                value={this.state.searchName}
                onChange={this.searchNameOnChange}
              />
              <Button type="primary" onClick={this.inquire}>
                查询
              </Button>
            </div>
            <div style={{ float: "right", marginBottom: 17 }}>
              <Button onClick={this.showOrganizeModal}>新增组织</Button>
              <Popconfirm
                placement="top"
                title={"确定你的操作"}
                onConfirm={this.removeRoleUser}
                okText="确定"
                cancelText="取消"
              >
                <Button style={{ marginLeft: 25 }}>批量移除</Button>
              </Popconfirm>
            </div>
          </div>
          <Table
            bordered
            rowKey="org_id"
            columns={columns}
            dataSource={this.state.dataSource}
            rowSelection={{ onChange: this.rowSelection }}
            pagination={{
              onChange: onPageChange,
              total,
            }}
          />
          <div style={{ textAlign: "center", marginTop: 20 }}>
            <Button
              style={{ width: 115, height: 36, marginLeft: 45 }}
              onClick={this.handleCancel}
            >
              关闭
            </Button>
          </div>
        </Modal>
        <OrganizeModal {...organizeModalProps} />
      </React.Fragment>
    );
  }
}
