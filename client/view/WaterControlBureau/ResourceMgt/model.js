import { message } from 'antd/lib/index';
import {
    loadnewsList, searchnewsList, fetchNewsColumn, deleteNews,
    setNewIndex, batchMoveNews, setPhotos, setRecommend, setVisibility
} from 'apis/news';

import { fetchSourceColumns, getSourceList, deleteSource, setSourceTop, setResourceIndex, batchMoveResource } from 'client/apis/waterControlBureau'

const treeTo = (tree) => {
    for (let i in tree) {
        tree[i].value = tree[i].news_column_id;
        tree[i].label = tree[i].name;
        if (tree[i].children != []) {
            treeTo(tree[i].children);
        }
    }
    return tree;
}
const getSelectIdsOfTree = (id) => {
    for (let i in crownList) {
        tree[i].value = tree[i].news_column_id;
        tree[i].label = tree[i].name;
        if (tree[i].children != []) {
            treeTo(tree[i].children);
        }
    }
    return tree;
}
export default {
    // 模型里面的子类   类似   {store : {root-manage:{}}}
    namespace: 'WaterControlBureauResourceMgt',
    state: {
        //新闻列表
        dataList: [],
        listLoading: false,
        //栏目列表
        crownList: [],
        columLoading: false,
        pageNew: {
            pageNum: '',
            pageSize: '',
            pages: '',
            total: ''
        },
        //预览模态框是否显示，默认为不显示
        modalVisible: false,
        // 是否展示编辑新闻序号的modal窗
        editIndexShow: false,
        // 编辑新闻序号时的当前编辑数据缓存，用于获取编辑当前行的数据
        editIndexData: {}, // 编辑序号的时候暂存
        editPhotosData: {}, // 编辑图片的时候暂存
        //通知侧边栏控制符，控制是否折叠
        isHideWrap: false,
        collapsed: true,
        //通知模态框暂存当前活动信息
        currentActivityInfo: {},
        //控制提交按钮
        submitButtonLoading: false,
        // 显示聚焦图片编辑器
        focusIsShow: false,
        focusDisplayImageUrl: '',
        showImageSubmitOpacity: false,
        focusIsEdit: undefined, // 表示是否是编辑聚焦图片 true编辑 false 新增聚焦
    },
    subscriptions: {
      setup({ history, dispatch }) {
        // 监听 history 变化，当进入 `/` 时触发 `load` action
        return history.listen(({ pathname }) => {

          // let match;
          // if( match = /(?:news-list\/)(\d+)/.exec(pathname) ){
          //   if(match[1]){
          //     const tabsKey = parseInt(match[1]) || 1;
          //     dispatch({type:'loadnewsList', payload:{
          //       status: tabsKey === 1 ? 5 : 6
          //     }})
          //     console.log('test');
          //   }
          // }
        });
      },
    },
    // 定义业务交互层
    effects: {

        //全部新闻的查询
        * loadnewsList({ payload }, { put, call }) {
            // console.error('loadnewsList')
            // yield put({ type: 'save', payload: { listLoading: true } });
            // let _newsList = yield searchnewsList({ page });
            // let { data: response } = _newsList;
            // yield put({ type: 'save', payload: { listLoading: false } });
            // if (response.code !== 0) {
            //     throw new Error(response.message);
            // }
            // if (response.code === 0 && response.message === 'success') {
            //     let { data } = response;
            //     data = data.map(d => {
            //         d.title = decodeURIComponent(d.title)
            //         d.source = decodeURIComponent(d.source)
            //         return d
            //     })
            //     yield put({ type: 'loadData', payload: { dataList: data } });
            //     yield put({
            //         type: 'setPage',
            //         payload: {
            //             pageNum: response.pageNum,
            //             pageSize: response.pageSize,
            //             pages: response.pages,
            //             total: response.total
            //         }
            //     });
            // }
            yield put( { type: 'searchnewsList', payload });
        },
        // 根据查询新闻
        *searchnewsList({ payload: { page = 1, keyword, column_id, start_time, end_time, top, focus, top_status, type, flag } }, { put }) {
            // 显示loading
            yield put({ type: 'save', payload: { listLoading: true } });
            // console.log({ page, keyword, cid, start_time, end_time, top, focus, status })
            // 加载列表数据
            const result = (yield getSourceList({ page, keyword, column_id, start_time, end_time, top, focus, type, top_status, flag })).data;
            yield put({ type: 'save', payload: { listLoading: false } }); // 停止加载项
            //
            // 如果请求错误
            if (result.code !== 0) {
                throw new Error(result.message);
            } else {
                let  resultData  = result.data && result.data.records && result.data.records.length > 0 ? result.data.records: []
                resultData.length > 0 && resultData.map(d => {
                    d.title = decodeURIComponent(d.title)
                    return d
                })
                yield put({ type: 'save', payload: { dataList: resultData } });
                yield put({
                    type: 'setPage',
                    payload: {
                        pageNum: result.data.current_page,
                        pageSize: result.data.page_size,
                        pages: result.data.total_page,
                        total: result.data.total_count
                    }
                });
            }
        },
        // 获取新闻栏目
        * getColumn({ }, { put }) {
            yield put({ type: 'save', payload: { columLoading: true } });

            let result = []
            const response = yield fetchSourceColumns();
            // console.log(response);
            const { data: body } = response;
            const { code, message, data } = body;
            if (code !== 0) {
              throw new Error(message);
            }
            for(let i = 0, length = data.length; i < length; i ++){
              const {data: res} = yield fetchSourceColumns({ top_column_id: data[i].column_id });
              const { data: childrenData, code: childrenCode } = res;
              if (childrenCode !== 0) {
                throw new Error(message);
              }
              data[i].children = childrenData
              if(i === length - 1) {
                result = data
              }
            }

            const columnList = treeTo(result);

            yield put({ type: 'save', payload: { columLoading: false } });
            yield put({ type: 'save', payload: { crownList: columnList, columLoading: false } });
        },
        // 删除新闻
        * deleteNews({ payload }, { put }) {
            const { id } = payload;
            yield put({ type: 'save', payload: { listLoading: true } });
            // 加载列表数据
            const result = (yield deleteSource({ resource_id: id })).data;
            // console.log(result);
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            }
            yield put({ type: 'save', payload: { listLoading: false } });
        },
        // 批量移动新闻
        * batchMoveNews({ payload: { ids, id  } }, { put }) {
          console.log(ids, id)
            yield put({ type: 'save', payload: { listLoading: true } });
            const result = (yield batchMoveResource({ id_list: ids, column_id: id })).data;
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            } else {
                yield put({ type: 'save', payload: { listLoading: false } });
            }
        },
        // 设置新闻序号
        * setResourceIndex({ payload: { nid, oid } }, { put }) {
            yield put({ type: 'save', payload: { listLoading: true } });
            const result = (yield setResourceIndex({ resource_id: nid, sort_num: oid })).data;
            yield put({ type: 'save', payload: { listLoading: false } });
            // 如果请求错误
            if (result.code !== 0) {
                throw new Error(result.message);
            }
        },
        // 设置聚焦图片
        * setPhotos({ payload: { id, url  } }, { put }) {
            yield put({ type: 'save', payload: { listLoading: true } });
            // 加载列表数据
            const result = (yield setPhotos({ id, url })).data;
            // console.log(result);
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            }
            yield put({ type: 'save', payload: { listLoading: false } });
        },
        // 设置置顶与聚焦
        * setRecommend({ payload: { id, type, focus_img  } }, { put }) {
            yield put({ type: 'save', payload: { listLoading: true } });
            // 加载列表数据
            const result = (yield setSourceTop({ resource_id: id, top_stutas: type })).data;
            // console.log(result);
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            }
            yield put({ type: 'save', payload: { listLoading: false } });
        },

        * setVisibility({ payload: { news_id, visibility  } }, { put }) {
            yield put({ type: 'save', payload: { listLoading: true } });

            const result = (yield setVisibility({ news_id, visibility })).data;

            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                return message.error(result.message);
            }
        }

    },
    reducers: {
        save(state, { payload }) {
            return { ...state, ...payload };
        },
        loadData(state, { payload }) {
            return { ...state, ...payload };
        },
        setPage(state, { payload }) {
            return { ...state, pageNew: { ...state.pageNew, ...payload } };
        },
        toggleCollapsed(state) {
            //如果当前侧边栏为显示状态，在收起时将表单状态重置
            return { ...state, collapsed: !state.collapsed };
        },
        toggleHideWrap(state) {
            return { ...state, isHideWrap: !state.isHideWrap };
        }
    }
}
