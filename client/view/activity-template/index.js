/**
 * @description 互动模板
 * <AUTHOR>
 * @createData 2019/09/17
 */

import React, {Component} from 'react';
import {connect} from "dva";
import SearchHeader from 'components/search-header';
import {
	Spin,
	Pagination,
	message
} from 'antd';
import ActivityFilter from './components/filter';
import ActivityTemplateAdd from './components/templateAdd';
import ActivityTemplateList from './components/templateList';
import ActivityTemplateItem from './components/templateItem';
import {getTemplateList, delTemplate } from 'client/apis/questionnaire';
import {settingData} from '../questionnaire-new/utils';
import './index.less';

class Index extends Component {
	constructor(props) {
		super(props);
		this.state = {
			title: '',
			total: 0,
			pages: 0,
			page_no: 1,
			page_size: 5,
			dataList: [],
			loading: false
		}
	}

	componentDidMount() {
		this.onLoadData();
	}

	/**
	 * @description 加载数据
	 * @return {Promise<void>}
	 */
	async onLoadData () {
		await this.setState({loading: true})
		const {
			title,
			page_no,
			page_size
		} = this.state;
		const {
			data: { code = 0, data = {}, message: msg }
		} = await getTemplateList({ title, page_no, page_size });
		const { list = [], total = 0, pages = 0 } = data
		if(code === 0){
			this.setState({
				total,
				pages,
				dataList: list,
				loading: false
			})
		}else{
			message.error(msg)
			this.setState({ loading: false })
		}
	}

	/**
	 * @description 新建模板
	 */
	newTemplate () {
		const {
			history: {push},
			dispatch
		} = this.props;
		dispatch({
      type: "questionnaireNew/save",
      payload: {
	      ...settingData,
	      activity_id: undefined,
				questionnaire: {
					title: "",
					logics: [],
					qs_opts: []
				},
				/* 逻辑设置 */
				edit_logics: [],
      }
    });
		push('/questionnaire-new')
	}

	/**
	 * @description 查询模板
	 * @param val
	 */
	onSearchTemplate (val = "") {
  	this.setState({
		  title: val
	  }, () => this.onLoadData())
	}

	/**
	 * 删除模板
	 */
	async delTemplate(id){
		const {data:{code = 0, message: msg}} = await delTemplate(id)
		if(code === 0) {
			this.onLoadData()
		}else{
			message.warn(msg)
		}
	}
	render() {
		const { history: {push}, dispatch } = this.props;
		const {
			dataList,
			page_no,
			page_size,
			total,
			pages,
			loading
		} = this.state;

		// 加载中
		const LoadingArea = () => (
			<div className="activity-spin">
				<Spin />
			</div>
		);

		// 结果显示
		const ResultArea = () => (
			<div>
				<ActivityTemplateList>
					<ActivityTemplateAdd
						onClick={() => this.newTemplate()}
					/>
					{dataList.map((item, index) => (
						<ActivityTemplateItem
							{...item}
							delTemplate = {(id) => this.delTemplate(id)}
							push={push}
							dispatch={dispatch}
							key={index}
						/>
					))}
				</ActivityTemplateList>
			</div>
		);

		// 加载判断
		const ActiveResult = loading
			? <LoadingArea />
			: <ResultArea />

		return (
			<div className="activity-template">
				<SearchHeader
          onBack={() => {
            history.back()
          }}
          title={'问卷模板'}
          style={{ marginBottom: 30 }}
        />
				<div className="template-wrap">
					<ActivityFilter onChange={({activityTitle}) => this.onSearchTemplate(activityTitle)}/>
					{ActiveResult}
					{
						dataList.length !== 0 && (
							<div className="activity-pages">
								<Pagination
									total={total}
									current={page_no}
									pageSize={page_size}
									onChange={value => this.setState({page_no: value}, () => this.onLoadData())}
								/>
								<span>共{pages}页</span>
							</div>
						)
					}
				</div>
			</div>
		);
	}
}

export default connect(({questionnaireNew}) => ({questionnaireNew}))(Index);
