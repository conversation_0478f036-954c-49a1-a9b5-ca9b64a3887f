import React, { Component } from "react";
import { message } from "antd";
import "./index.less";

import { uploadFile, uploadVideoFile, uploadVideoResult } from "apis/file";

import { headers } from "tool/axios";

import propTypes from "prop-types";
import LoadingModal from "components/loading-modal";
import { filter } from "jszip";

class FileUpload extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 用于暂存已经上传的文件序列
      list: [],
      finishList: [],
      loading: false,
    };
    this.uploadHandler = this.uploadHandler.bind(this);
    this.postFileUpload = this.postFileUpload.bind(this);
  }

  componentDidMount() {
    const { uploadedList } = this.props;
    if (uploadedList) {
      this.setState({
        list: uploadedList,
      });
    }
  }

  componentWillReceiveProps(props) {
    const { uploadedList } = props;
    if (uploadedList) {
      this.setState({
        list: uploadedList,
      });
    }
  }

  uploadHandler(e) {
    const { list } = this.state;
    const { max, limitSize, accept } = this.props;
    const target = e.target;
    const files = target.files;
    if (files.length === 0) {
      return;
    }
    const fileList = [];
    for (let key in files) {
      if (typeof files[key] === "object") {
        //根据传进来的文件名判断文件名后缀
        /* if (accept && !accept.includes(files[key].name.split(".")[files[key].name.split(".").length-1].toLowerCase())) {
          // console.log("查",files[key].name.split(".")[files[key].name.split(".").length-1].toLowerCase());
          message.warn(`${files[key].name}文件格式错误`);
          continue;
        } else  */
        if (limitSize && files[key].size / 1024 / 1024 > limitSize) {
          if (max && max === 1) {
            message.warn(`请上传小于${limitSize}M的文件`);
            continue;
          } else {
            message.warn(`${files[key].name} 大于${limitSize}M，请处理后上传`);
            continue;
          }
        }
        fileList.push(files[key]);
      }
    }
    if (max) {
      if (list.length + fileList.length > max) {
        message.warn(`限定最多上传${max}个文件`);
        return;
      }
    }
    this.setState({
      loading: true,
    });

    if (accept && accept.includes("video")) {
      if (fileList.length > 1) {
        message.warn("仅支持一次上传一个视频");
      } else {
        this.uploadVideoFile(fileList[0]);
      }
      return;
    }
    // 获取请求头
    const queryHeader = headers();
    let promiseList = [];
    fileList.forEach((file) => {
      promiseList.push(this.postFileUpload(file, queryHeader));
    });
    Promise.all(promiseList).then((responseList) => {
      const { onChange } = this.props;
      let finishResult = [];
      if (responseList && Array.isArray(responseList)) {
        let res;
        responseList.forEach((response) => {
          if (response) {
            if (response.body) {
              res = response.body.data;
              if (res.code !== 0) {
                message.error(
                  `${response.file.name}上传失败，原因：${
                    res.message || "未知原因"
                  }`
                );
                return;
              }
              // console.log(response.body,res.data);
              finishResult = finishResult.concat(res.data);
            }
          }
        });
      }
      const newList = list.concat(finishResult);
      this.setState({
        // finishList: finishResult,
        list: newList,
        loading: false,
      });
      // console.log(newList, finishResult,'新旧');
      onChange(newList, finishResult);
    });
  }

  async postFileUpload(file, queryHeader) {
    const formData = new FormData();
    const { upType = "file" } = this.props;
    // let { finishList } = this.state;
    const imageFileTypes = ["image/png", "image/jpeg", "image/jpg"];
    if (imageFileTypes.indexOf(file.type) > -1) {
      // 上传的是图片，图片质量保真处理，作为新闻类型图片来处理
      // 对符合条件的文件进行处理
      formData.append("upType", "no-compress-image");
      formData.append("is_water", 0)
      formData.append("is_val", 0)
    } else {
      formData.append("upType", upType);
    }
    let upName = file.name;
    // 去除文件名中的空格字符
    if (typeof upName === "string") {
      upName = upName.replace(/\s*/g, "");
    }
    if (upType === "eval-file") {
      if (typeof window !== "undefined") {
        let orgName = window.sessionStorage.getItem("_on");
        if (orgName) {
          orgName = unescape(orgName) || "";
          upName = upName.replace(/(\.[^\.]+)$/, "_" + orgName + "$1");
        }
      }
    }
    formData.append("upfile", file);
    formData.append("up_name", upName);
    return {
      body: await uploadFile(formData, queryHeader),
      file,
    };
  }
  
  async uploadVideoFile(file) {
    const { upType = "video" } = this.props;
    const formData = new FormData();
    formData.append("upType", upType);
    formData.append("upfile", file);
    formData.append("up_name", file.name);
    const task = (await uploadVideoFile(formData)).data;
    if (task.code == 0) {
      this.checkVideoUploadResult(task.data[0].task_id);
    }else{
      this.setState({loading: false });
    }
  }

  async checkVideoUploadResult(taskId) {
    const { code, data } = (await uploadVideoResult(taskId)).data;
    if (code == 0) {
      const id = data[0].id;
      if (id === -1) {
        setTimeout(async () => {
          this.checkVideoUploadResult(taskId);
        }, 1000);
      } else {
        const { list: files } = this.state;
        const list = files.concat(data);
        this.setState({ list, loading: false });
        this.props.onChange(list);
      }
    } else {
      this.setState({loading: false });
      // message.warn(res.message);
    }
  }

  render() {
    const { loading } = this.state;
    // accept 不能使用类似.jpg、.doc
    const { multiple, children, disabled, tipText = "", accept } = this.props;
    if (disabled) {
      return null;
    }
    return (
      <div className="file-upload-wrap">
        {!disabled && tipText && (
          <div className="tip-text-container">{tipText}</div>
        )}
        <div className="file-upload-container">
          {!disabled ? (
            <input
              type="file"
              className="file-hidden-trigger"
              multiple={multiple}
              value=""
              accept={accept}
              onChange={this.uploadHandler}
              // ondragover={(a) => {console.log(a,'--------------')}}
            />
          ) : (
            ""
          )}
          {children}
          <LoadingModal noTip={true} modalVisible={loading} />
        </div>
      </div>
    );
  }
}

FileUpload.propTypes = {
  tipText: propTypes.oneOfType([propTypes.string, propTypes.element]),
  // 限制文件最大可以上传数量
  max: propTypes.number,
  limitSize: propTypes.number,
  disabled: propTypes.bool,
  multiple: propTypes.bool,
  upType: propTypes.string,
  onChange: propTypes.func,
  uploadedList: propTypes.array,
  children: propTypes.oneOfType([propTypes.string, propTypes.element]),
  accept: propTypes.string,
};

FileUpload.defaultProps = {
  tipText: "",
  disabled: false,
  multiple: true,
  upType: "file",
  onChange: (data) => {
    console.log("上传完毕", data);
  },
  uploadedList: [],
  children: <span>上传附件</span>,
  accept: "",
};

export default FileUpload;
