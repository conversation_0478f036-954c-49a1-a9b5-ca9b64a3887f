import React, { Component } from "react";
import { connect } from "dva";
import { Form, Modal, Select, Input, Button, Table } from "antd";

const FormItem = Form.Item;
const Option = Select.Option;

const { Column, ColumnGroup } = Table;

class ActiveCon extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isClearAll: true,
    };

    this.onSearch = this.onSearch.bind(this);
    this.onSelect = this.onSelect.bind(this);
    this.onUnSelect = this.onUnSelect.bind(this);
    this.onCancel = this.onCancel.bind(this);
    this.pageChangeHandler = this.pageChangeHandler.bind(this);
  }

  async componentDidMount() {
    const { dispatch } = this.props;
    await dispatch({ type: "activeContent/getActiveList" });
  }

  componentWillReceiveProps(nextProps) {
    const { dispatch } = this.props;
    if (nextProps.isClearAll && this.state.isClearAll) {
      dispatch({ type: "activeContent/clearSelectedDataList" });

      this.setState({
        isClearAll: false,
      });
    } else if (!nextProps.isClearAll) {
      this.setState({
        isClearAll: true,
      });
    }
  }

  renderList(data) {
    return data.map((item) => {
      return (
        <Option key={item.value} value={item.value}>
          {item.label}
        </Option>
      );
    });
  }

  async onSearch() {
    const { dispatch, form } = this.props;

    const type = form.getFieldValue("type");
    const keyword = form.getFieldValue("keyword");
    const page = 1;
    let payload = { page };

    payload.type = type || "";
    payload.keyword = keyword || "";

    dispatch({
      type: "activeContent/updateState",
      payload,
    });

    await dispatch({ type: "activeContent/getActiveList", payload });
  }

  onSelect(record) {
    const { dispatch, onSelect, form } = this.props;
    onSelect && onSelect(record, form);
    // form.setFieldsValue({type: 0, keyword: ''});
    dispatch({
      type: "activeContent/updateDataList",
      record,
      isSelected: true,
    });
  }

  onUnSelect(record) {
    const { dispatch, onUnSelect, form } = this.props;
    onUnSelect && onUnSelect(record, form);
    dispatch({ type: "activeContent/updateDataList", record });
  }

  onCancel() {
    const { dispatch, onCancel, form } = this.props;
    onCancel && onCancel(form);
    // console.log(form);
    // 清空表单信息
    // form.resetFields();
    // dispatch({
    //     type: 'activeContent/updateState',
    //     payload: {
    //         pageNum: 1,
    //         type: '',
    //         keyword: ''
    //     }
    // });
    // dispatch({type: 'activeContent/getActiveList'});
  }

  pageChangeHandler(pageNum, type, keyword) {
    // console.log(pageNum, type, keyword);
    const { dispatch } = this.props;
    dispatch({
      type: "activeContent/getActiveList",
      payload: { page: pageNum, type, keyword },
    });
  }

  render() {
    const { title, visible, dispatch, form, activeContent, isSingle } =
      this.props;
    const {
      isListLoading,
      dataList,
      selectList,
      pageSize,
      pageNum,
      type,
      keyword,
      pageTotal,
    } = activeContent;
    const { getFieldDecorator } = form;
    const self = this;

    return (
      <Modal
        title={title || "相关互动"}
        visible={visible}
        footer={null}
        onCancel={this.onCancel}
        className="select-modal"
      >
        <Form layout={"inline"}>
          <FormItem>
            {getFieldDecorator("type", {
              initialValue: 0,
            })(
              <Select style={{ width: 100 }}>
                {this.renderList(selectList)}
              </Select>
            )}
          </FormItem>

          <FormItem>
            {getFieldDecorator("keyword", {
              initialValue: "",
            })(<Input placeholder="请输入关键词" style={{ width: 200 }} />)}
            <Button type="primary" onClick={this.onSearch}>
              查询
            </Button>
          </FormItem>
        </Form>

        <Table
          className="select-modal-table"
          style={{ marginTop: 10 }}
          dataSource={dataList}
          loading={isListLoading}
          pagination={{
            hideOnSinglePage: true,
            pageSize: pageSize,
            current: pageNum,
            total: pageTotal,
            onChange: (pageNum) => {
              return self.pageChangeHandler(pageNum, type, keyword);
            },
          }}
          bordered
        >
          <ColumnGroup title="查询结果">
            <Column
              title="标题"
              dataIndex="name"
              key="name"
              align="center"
              render={(text, record) => {
                return <span style={{ float: "left" }}>{text}</span>;
              }}
            />
            <Column
              title="操作"
              align="center"
              dataIndex="oper"
              width={100}
              key="oper"
              render={(text, record) => {
                return !record.isSelected ? (
                  <a
                    onClick={() => {
                      dispatch({ type: "activeContent/clearSelectedDataList" });
                      record.isSelected = true;
                      self.onSelect(record);
                    }}
                  >
                    选择
                  </a>
                ) : (
                  <a
                    onClick={() => {
                      record.isSelected = false;
                      self.onUnSelect(record);
                    }}
                  >
                    已选择
                  </a>
                );
              }}
            />
          </ColumnGroup>
        </Table>
      </Modal>
    );
  }
}

const mapStateToProps = ({ activeContent }) => ({ activeContent });
export default connect(mapStateToProps)(Form.create()(ActiveCon));
