import {message} from 'antd';
import {searchActivity} from 'apis/activity';

export default {
    // 模型里面的子类
    namespace: 'activeContent',
    state: {
        selectList: [{
            value: 0,
            label: '全部'
        }, {
            value: 1,
            label: '投票'
        }, {
            value: 2,
            label: '问卷调查'
        }, {
            value: 3,
            label: '有奖竞赛'
        }, {
            value: 4,
            // label: '线下活动'
            label: '线下互动'
        }],
        dataList: [],
        selectedData: [],
        isListLoading: false,
        pageNum: 1,
        pageSize: 10,
        pageTotal: 0,
        type: '',
        keyword: ''
    },
    // 定义业务交互层
    effects: {
        * getActiveList({payload = {}}, {put, select, call}) {

            const {type, keyword, page} = payload;
            yield put({type: 'updateState', payload: {isListLoading: true}})
            const selectedData = yield select(state => state.activeContent.selectedData);

            // 加载列表数据
            const result = (yield call(searchActivity, {type, keyword, page})).data;
            if (result.code != 0) {
                yield put({type: 'updateState', payload: {isListLoading: false}});
                return message.error(result.message);
            }

            let dataList = result.data.map((item) => {

                const inArray = selectedData.filter(value => {
                    return value.key == item.activity_id;
                });

                return {
                    key: item.activity_id,
                    name: item.title,
                    isSelected: !!inArray.length
                }
            });

            yield put({
                type: 'updateState', payload: {
                    dataList,
                    pageNum: page,
                    pageTotal: result.total,
                    isListLoading: false
                }
            });

        }
    },
    reducers: {
        updateState(state, {payload}) {
            return {...state, ...payload};
        },
        updateDataList(state, {record, isSelected}) {
            const index = state.dataList.indexOf(record);

            state.dataList[index] = record;

            if(isSelected) {
                const inArray = state.selectedData.filter(item => {
                    return item.key == record.key;
                });
                if (!inArray.length) {
                    state.selectedData = [...state.selectedData, record];
                }
            } else {
                const i = state.selectedData.indexOf(record);
                state.selectedData.splice(i, 1);
            }
            // console.log(state.selectedData);

            return {...state};
        },
        clearSelectedDataList(state) {
            const newData = state.dataList.filter(item => item.isSelected)[0];
            if (newData) {
                newData.isSelected = false;

                // console.log(state.selectedData);
                // console.log(newData);

                const i = state.selectedData.indexOf(newData);
                // console.log(i);
                state.selectedData.splice(i, 1);
                return {...state};
            }
            return {...state};
        }
    }
}