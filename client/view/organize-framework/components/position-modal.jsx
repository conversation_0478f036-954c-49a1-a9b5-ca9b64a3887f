import { DeleteOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Form,
  Input,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";
import {
  addPosition,
  deletePosition,
  getPositionList,
  queryByCode,
  updatePosition,
} from "apis/cadre-portrait";
import LoadingModal from "client/components/loading-modal";
import { useEffect, useRef, useState } from "react";

const { Option } = Select;
const FormItem = Form.Item;

const _optionArray = [
  {
    value: 1,
    label: "党组织书记",
  },
  {
    value: 2,
    label: "单位负责人",
  },
  {
    value: 3,
    label: "人大主席",
  },
  {
    value: 4,
    label: "其他",
  },
];

const PositionTable = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [selectList, setSelectList] = useState(_optionArray);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState("添加职务");
  const [currentPosition, setCurrentPosition] = useState(null);
  const [codeMap, setCodeMap] = useState({
    categoryOption: [],
  });
  const formRef = useRef(null);

  useEffect(() => {
    fetchData();
    initCodeMap(96160, "categoryOption");
  }, []);

  const initCodeMap = async (code, key) => {
    const res = await queryByCode({
      code,
    });
    if (res.data.code === 0) {
      setCodeMap({
        ...codeMap,
        [key]: res.data.data,
      });
    }
  };

  const fetchData = async () => {
    setLoading(true);
    const res = await getPositionList();
    if (res.data.code === 0) {
      setData(res.data.data);
    }
    setLoading(false);
  };

  const handleAdd = () => {
    setModalTitle("添加职务");
    setCurrentPosition(null);
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setModalTitle("编辑职务");
    setCurrentPosition(record);
    setModalVisible(true);
  };

  const handleDelete = async (record) => {
    const res = await deletePosition({
      pms_job_id: record.pms_job_id,
    });
    if (res.data.code === 0) {
      message.success("删除成功");
      fetchData();
    } else {
      message.error(res.data.message);
    }
  };

  const handleModalOk = async () => {
    formRef.current.validateFields(async (err, values) => {
      if (err) return;
      if (currentPosition) {
        const res = await updatePosition({
          ...values,
          pms_job_id: currentPosition.pms_job_id,
        });
        if (res.data.code === 0) {
          message.success("修改成功");
          fetchData();
          setModalVisible(false);
        } else {
          message.error(res.data.message);
        }
      } else {
        const res = await addPosition(values);
        if (res.data.code === 0) {
          message.success("添加成功");
          fetchData();
          setModalVisible(false);
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    formRef.current.resetFields();
  };

  const columns = [
    {
      title: "分类",
      dataIndex: "type",
      key: "type",
      render: (text) => {
        const option = _optionArray.find((option) => option.value === text);
        return option ? option.label : "";
      },
    },
    {
      title: "职务全称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "职务简称",
      dataIndex: "short_name",
      key: "short_name",
    },
    {
      title: "操作",
      key: "action",
      render: (text, record) => (
        <div>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除吗？"
            onConfirm={() => handleDelete(record)}
            okText="是"
            cancelText="否"
          >
            <Button type="link" icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div>
      <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
        添加职务
      </Button>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="pms_job_id"
        loading={loading}
      />
      <Modal
        title={modalTitle}
        visible={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
      >
        <Form
          ref={formRef}
          layout="vertical"
          initialValues={currentPosition || {}}
        >
          <FormItem name="type" label="分类">
            <Select>
              {selectList.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="name" label="职务全称">
            <Input placeholder="请输入职务全称" />
          </FormItem>
          <FormItem name="short_name" label="职务简称">
            <Input placeholder="请输入职务简称" />
          </FormItem>
          <FormItem name="part_job_name" label="兼职全称">
            <Input placeholder="请输入兼职全称" />
          </FormItem>
          <FormItem name="part_job_short_name" label="兼职简称">
            <Input placeholder="请输入兼职简称" />
          </FormItem>
          <FormItem name="title" label="发文抬头">
            <Input placeholder="请输入发文抬头" />
          </FormItem>
          <FormItem name="category" label="职务类别">
            <Select>
              {codeMap.categoryOption.map((item) => (
                <Option key={item.op_key} value={item.op_key}>
                  {item.op_value}
                </Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="num" label="职务数量">
            <InputNumber placeholder="请输入职务数量" />
          </FormItem>
        </Form>
      </Modal>
      <LoadingModal modalVisible={loading} tip="加载中..." />
    </div>
  );
};

export default PositionTable;
