import React, { Component } from 'react';
import './index.less';
import { mobileHost } from 'apis/config';
import { Icon } from 'antd';
import PropTypes from 'prop-types';

const OverviewModal = ({ modalVisible, closeHandler, url }) => {
    const stopPropagation = (e) => {
        e.stopPropagation();
    }
    if (modalVisible) {
        return (
            <div className={'overview-modal'} onClick={closeHandler}>
                <div className={'overview-content'} onClick={stopPropagation}>
                    {/* 新增模态框关闭按钮 */}
                    <Icon className={'overview-modal-close'} type="close-circle-o" onClick={closeHandler} />
                    {/* <iframe frameBorder='0' src={mobileHost+'/questionnaire-survey/1/0'}>
                        您的浏览器无法正常显示预览内容
                    </iframe> */}
                    <iframe className={'content-wrapper'} frameBorder='0' src={url || ''}>
                        您的浏览器无法正常显示预览内容
                    </iframe>
                </div>
            </div>
        )
    }
    return null;
}

OverviewModal.propTypes = {
    //是否显示加载层控制标识符
    modalVisible: PropTypes.bool,
    //关闭模态框的方法
    closeHandler: PropTypes.func,
    //地址
    url: PropTypes.string
};

export default OverviewModal;