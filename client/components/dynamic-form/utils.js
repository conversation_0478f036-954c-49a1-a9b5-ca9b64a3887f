import { deepClone } from "client/tool/deepClone";
import moment from "moment";

/**
 * 格式化单个默认值（对特殊的value进行格式化）
 * @param {Number} type 组件类型
 * @param {Object} attr 组件属性
 * @param {any} defaultlValue 组件默认value
 * @param {Boolean} initData 是否是初始值， 默认false则为初始默认值，true，则为初始后端返回值
 */
export const formatDefaultValue = (type, attr = {}, defaultlValue, initData = false) => {
  const tryArray = (defaultlValue) => {
    if (typeof (defaultlValue) === "string" && defaultlValue !== "") {
      return defaultlValue.split(",")
    } else if (Array.isArray(defaultlValue)) {
      return defaultlValue
    }
    return [];
  };
  switch (Number(type)) {
    case 14: {
      // 图片文件
      if (typeof (defaultlValue) === "string") {
        let res = [];
        try {
          res = JSON.parse(defaultlValue)
        } catch (error) {
          console.log(error);
        }
        return res;
      }
    }
    case 13:
      // 省市
      return tryArray(defaultlValue);
    case 12:
      // 数字输入框
      if (defaultlValue === undefined || defaultlValue === "" || defaultlValue === null) {
        return undefined;
      }
      return Number(defaultlValue);
    case 7:
      // 用户选择器
      if (Array.isArray(defaultlValue)) {
        return defaultlValue.join(",")
      }
      return defaultlValue
    case 8:
      // 组织选择器
      if (Array.isArray(defaultlValue)) {
        return defaultlValue.join(",")
      }
      return defaultlValue
    case 6:
      // 时间选择器
      const { now } = attr;
      if (now && !initData) {
        return moment(new Date)
      }
      return defaultlValue ? moment(defaultlValue) : undefined;
    case 5:
      // 级联选择器
      return tryArray(defaultlValue);
    case 4:
      // 复选框
      return tryArray(defaultlValue);
    case 3:
      // 单选框
      return Array.isArray(defaultlValue) ? defaultlValue.join(",") : (defaultlValue || undefined)
    case 2:
      // 下拉框（选择器）
      const { multiple } = attr;
      return multiple ? tryArray(defaultlValue) : (defaultlValue || undefined);
    default:
      return defaultlValue || undefined;
  }
};

/**
 * 对fieldList 中的每个项的defaultValue 进行格式化（调用formatDefaultValue子方法）
 * @param {*} fieldList 表单列表
 */
export const formatFieldDefaultValue = (fieldList) => {
  const list = deepClone(fieldList)
  list.map((fieldItem) => {
    const { type, attr, defaultValue } = fieldItem;
    fieldItem.defaultValue = formatDefaultValue(type, attr, defaultValue);
  })
  return list;
}

/**
 * 对详情返回的值进行格式化处理
 * @param {Array} fieldList 表单数据
 * @param {Object} initValue 初始化值
 */
export const formatInitValue = (fieldList, initValue) => {
  const res = {};
  // 将所有的number返回值转换为string类型
  for (const key in initValue) {
    if (Object.hasOwnProperty.call(initValue, key)) {
      let item = initValue[key];
      if (item === undefined || item === null) item === "";
      res[key] = typeof (item) === "number" ? String(item) : item;
    }
  }
  fieldList.map(({ type, key, attr = {} }) => {
    // 当为级联选择器时
    if ([5, 13].indexOf(type) !== -1) {
      // 1. 如果值为空，则采用匹配属性（match）中的值进行组合
      if (res[key] === undefined || res[key] === "") {
        const { match = [] } = attr;
        const arr = [];
        match.map(item => {
          const val = res[item];
          if (val !== undefined && val !== "" && val !== null) {
            arr.push(val);
          }
        });
        const str = arr.join(",");
        if (str !== "") {
          res[key] = str;
        }
      } else if (type === 5) {
        // 2. 如果不为空，则寻找出真正的值，因只传了最后一级给后端
        const { options = [] } = attr;
        res[key] = findCascaderValue(res[key], options).join(",");
      }
    }
    res[key] = formatDefaultValue(type, attr, res[key], true);
  });
  return res;
};

/**
 * 获取表单默认值
 * @param {Array} fieldList 表单数据
 */
export const getAllDefaultValue = (fieldList = []) => {
  const res = {};
  fieldList.map((item) => {
    const { defaultValue, key } = item;
    if (defaultValue !== undefined) {
      res[key] = defaultValue;
    }
  });
  return res;
};


/**
 * 对提交数据的值进行格式化处理
 * @param {Arrat} fieldList 表单数据
 * @param {Object} data 提交数据
 */
export const formatSubmitData = (fieldList, data) => {
  const res = {};
  for (const key in data) {
    if (Object.hasOwnProperty.call(data, key)) {
      const item = data[key];
      res[key] = (item === undefined || item === null) ? "" : item;
    }
  }
  fieldList.map(({ type, key, attr = {}, is_default }) => {
    // 图片上传
    if (data[key] && type === 14 && (data[key] instanceof Array)) {
      res[key] = JSON.stringify(res[key]);
    }
    // 日期类型处理
    if (data[key] && type === 6 && (data[key] instanceof moment)) {
      const formatStr = attr.mode === "datetime" ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD";
      res[key] = res[key].format(formatStr);
    }
    // 用户选择器、组织选择器、下拉选择框、单选框 全部转换成数组（非系统字段）
    if ((type === 7 || type === 8 || type === 2 || type === 3) && is_default === 2) {
      if (typeof (data[key]) === "string" && data[key].trim() !== "") {
        res[key] = res[key].split(",")
      } else if (!Array.isArray(res[key])) {
        res[key] = []
      }
    }
    // 复选框类型处理
    if (data[key] && (type === 4 || type === 2) && (data[key] instanceof Array) && is_default === 1) {
      // 系统字段转string
      res[key] = res[key].join(",");
    }


    // 级联选择器（包含省市区选择器）
    if (data[key] && [5, 13].indexOf(type) !== -1 && (data[key] instanceof Array)) {
      const { match } = attr;
      // 级联选择器只传 选中的最后一级！！！
      if (type === 5) {
        const resData = res[key]
        res[key] = String(resData[resData.length - 1] || "");
      } else {
        res[key] = res[key] = res[key].join(",");
      }
      if (match instanceof Array) {
        match.map((item, index) => {
          const cascaderValue = data[key];
          res[item] = cascaderValue[index] || "";
        });
        delete res[key];
      }
    }
    // 数字为空时处理为null
    if (type === 12 && res[key] === "") {
      res[key] = null;
    }
  });
  return res;
};


/**
 * 
 * @param {Object} rules 验证数组 例如： {
                                        "required": true, 
                                        "message": "请选择用户名",
                                        "pattern": /^[a-zA-Z0-9_-]{4,16}$/,
                                        "patternMessage": "请输入4-16位字符（字母，数字，下划线，减号）",
                                        "validator": "checkUserName"
                                        }
 * @param {Object} method 方法对象 例如: {fn1: () => {}, fn2: () => {}}
 * @param {Number} type 
 * @param {Boolean} realityShow 实际的显示状态
 */
export const formatRules = (rules = {}, method = {}, type, realityShow) => {
  const newRules = [];
  const { required, pattern, validator, max, min, message = "请输入", patternMessage = "格式验证错误" } = rules;
  // 当且仅当 1. 是必填。 2. 组件为显示状态
  const requiredItem = { required: realityShow && required, message };
  if ([1, 9, 10].indexOf(type) !== -1 && required) {
    requiredItem.whitespace = true;
  }
  newRules.push(requiredItem);
  try {
    if (pattern) {
      newRules.push({ pattern: new RegExp(pattern), message: patternMessage });
    }
  } catch (error) {
    console.error("正则表达式有误：", error);
  }
  if (validator) {
    newRules.push({ validator: method[validator] });
  }
  if (max) {
    newRules.push({ max, message: `内容最大长度超过${max}字符` });
  }
  if (min) {
    newRules.push({ min, message: `内容最小长度不少于${min}字符` });
  }
  return newRules;
};

/**
 * 寻找级联选择器的实际value
 * @param {string} lastValue 级联选择器中最后一个值
 * @param {array} options 选项
 * @param {array} returnValue 不用传，用于递归调用的标识
 * @returns 
 */
export const findCascaderValue = (lastValue, options, parent = []) => {
  for (let i = 0; i < options.length; i++) {
    const { value, children } = options[i];
    let res = [...parent];
    if (value === lastValue) {
      res.push(value);
      return res;
    }
    if (Array.isArray(children)) {
      res.push(value);
      const childRes = findCascaderValue(lastValue, children, res);
      if (childRes.length !== 0) {
        return childRes;
      }
    }
  }
  return [];
};