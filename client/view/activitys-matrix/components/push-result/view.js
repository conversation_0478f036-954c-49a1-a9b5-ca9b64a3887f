
import React, { Component, Fragment } from "react";
import { <PERSON><PERSON>, Table } from "antd";

import StatusIcon from "components/status-icon";

import PropTypes from "prop-types";

import "./view.less";

/**
 *
 */

 // const data = [{
 //   state: 0,
 //   name: '<PERSON>'
 // }, {
 //   state: 0,
 //   name: '<PERSON>'
 // }, {
 //   state: 1,
 //   name: '<PERSON>'
 // }];
// const data = []


const propTypes = {
  data: PropTypes.object,
  onChange: PropTypes.func,
  onShowSizeChange: PropTypes.func,
};

const defaultProps = {
  data:{data:{visibility:0, orgs:[]}, total:0},
  onChange: ()=>{}
};


export default Object.assign( class PushResultView extends Component {

  constructor(props) {
    super(props);
    this.state = {

    }

    // console.log(this.props)

  }



  onChange(pageObject){
    this.props.onChange(pageObject.current, pageObject.pageSize)
  }

  render() {
    const { props } = this;

    const { total, data, pageNum, pageSize } = props.data;

    const { orgs, actual, should, visibility,org } = data;
    // 所有新闻的组织 应推送组织数量 实推送组织数量 可见性
    // console.log('org---',org)
    // console.log('data---',data)
    const columns = [{
      title: '状态',
      dataIndex: 'visibility',
      render: visibility => <StatusIcon color={visibility ? '#4FC136' : '#999999'} text={visibility ? '展示中' : '未展示' }></StatusIcon>,
      align : 'center',
      width : '10%'
    }, {
      title: '组织名称',
      className: 'org_name',
      dataIndex: 'org_name',
      // width : '80%'
    }];
    return (<div className="box com-push-result">
        { !org ? (<div>
          推送情况： 推送到{props.title}矩阵<br />
          {props.title}矩阵可见：所有组织可见
        </div>)
          :
          (orgs && orgs.length > 0 &&
          <div className="box-hd">
            {should > 0 && (<Fragment>推送情况： 推送到指定组织<br /></Fragment>)}
            {/*共推送{should}个 组织， <span className="success">{actual}</span>  个组织已展示， <span>{should-actual}</span> 个组织未展示。 <br />*/}
           {
             visibility==2 ? `${props.title}矩阵可见：指定组织可见` :
              `${props.title}矩阵可见：所有组织可见`
           }
            
          </div>)
        }

        { !org ? '' :
        <div className="box-bd">
          <Table
            rowKey="org_id"
            columns={columns}
            dataSource={orgs}
            onChange={ this.onChange.bind(this)}
            onShowSizeChange={this.onChange.bind(this)}
            pagination={{
              size:"small",
              total:total,
              pageSize:pageSize,
              current:pageNum,
              showQuickJumper:true,
              showSizeChanger:true,
              showTotal:(total) => {
                return (
                    <div>共{total}条记录，每页显示{pageSize}条</div>
                );
              }
            }}
            bordered
          />
        </div> }

      </div>);

  }
},  { propTypes, defaultProps} )
