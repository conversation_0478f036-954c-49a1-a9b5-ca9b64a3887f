import { http } from 'tool/axios';
import { integrating } from 'apis/config';

//免登接口
export const getAvoidLogin = (data = {}) => {
  return http.post(`${integrating}/user/avoidLogin`, data);
} 
//渠道注册
export const getOpenChannel = (data = {}) => {
  console.log(data)
  return http.post(`${integrating}/mall/openChannel`, data);
} 
//10500401-获取当前组织是兑换渠道开通状态
export const getChannelStatus = (data = {}) => {
  return http.get(`${integrating}/mall/channel/status`, data);
} 
