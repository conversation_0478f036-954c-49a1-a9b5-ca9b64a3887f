import { http } from "client/tool/axios";
import { activityHost } from "./config";

// 400020312-我要出题-查看待我审批 http://wiki.aidangqun.com/project/7?p=885
const listApproveMy = (params = {}) => {
  return http.get(`${activityHost}/question/list-approve-my`, params);
};

// 400020311-我要出题-审批 http://wiki.aidangqun.com/project/7?p=884
const approveQuestion = (params = {}) => {
  return http.post(`${activityHost}/question/approve`, params);
};

// 400020303-我要出题保存/提交 http://wiki.aidangqun.com/project/7?p=877
const saveMyQuestion = (params = {}) => {
  return http.post(`${activityHost}/question/save-my`, params);
};

// 400020304-通过题目id获取出题内容（只能获取当前用户发起的） http://wiki.aidangqun.com/project/7?p=878
const getQuestionAny = (params = {}) => {
  return http.get(`${activityHost}/question/get-question-any`, params);
};

// 400020313-我要出题-删除 http://wiki.aidangqun.com/project/7?p=890
const delMy = (params = {}) => {
  return http.get(`${activityHost}/question/del-my`, params);
};

export {
  listApproveMy,
  approveQuestion,
  saveMyQuestion,
  getQuestionAny,
  delMy,
}