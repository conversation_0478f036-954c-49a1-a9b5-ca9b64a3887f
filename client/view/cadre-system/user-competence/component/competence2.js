import React, { Component } from "react";
import { Modal, Form, Table, Button, message } from "antd";
import { host } from "apis/config";
import { http, headers } from "client/tool/axios";
import {
  getUserOrgRole,
  addUserOrgRole,
  getRoleList,
} from "client/apis/cadre-portrait";
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export default class Competence2 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible,
      // 角色列表数据
      roleDataSource: [],
      // 分组列表数据
      groupDataSource: [],
      // 选中行
      selectedRows1: [],
      // 选中行的拷贝
      // selectedRows1Copy: [],
      // 选中行键值
      selectedRowKeys1: [],
      // 选中行键值拷贝
      // selectedRowKeys1Copy: [],

      selectedRows2: [],
      selectedRowKeys2: [],
    };
    this.handleCancel = this.handleCancel.bind(this);
    // this.getDetails = this.getDetails.bind(this);
    this.getGroupList = this.getGroupList.bind(this);
    this.getRoleList = this.getRoleList.bind(this);
    this.rowSelection1 = this.rowSelection1.bind(this);
    this.rowSelection2 = this.rowSelection2.bind(this);
    this.save = this.save.bind(this);
  }
  componentDidMount() {
    this.setState({
      visible: this.props.visible,
    });
    this.getRoleList();
    // this.getUserRole();
    // this.getGroupList();
  }

  handleCancel() {
    this.setState({
      visible: false,
    });
    this.props.hideCompetence2();
  }
  rowSelection1(selectedRowKeys, selectedRows) {
    // console.log("onchange", selectedRowKeys, selectedRows);
    this.setState({
      selectedRowKeys1: selectedRowKeys,
      selectedRows1: selectedRows,
    });
  }
  rowSelection2(selectedRowKeys, selectedRows) {
    this.setState({
      selectedRowKeys2: selectedRowKeys,
      selectedRows2: selectedRows,
    });
  }
  // getDetails(uid) {
  //   http.get(`${host}/uc/user/power-details`, { user_id: uid }).then(d => {
  //     const _data = d.data;
  //     if (_data.code != 0) {
  //       message.error(d.message);
  //     }
  //     this.setState({
  //       dataSource: _data.data
  //     });
  //   });
  // }
  getUserRole() {
    getUserOrgRole({
      sys_user_id: this.props.activeCompetence2.user_id,
      org_id: this.props.activeCompetence2.org_id,
    }).then((res) => {
      if (res.data.code === 0) {
        console.log(
          res.data.data.map((item) => {
            return item.user_role_id;
          })
        );
        this.setState({
          selectedRows1: res.data.data.map((item) => {
            return item.user_role_id;
          }),
        });
      }
    });
  }
  // 获取角色
  getRoleList() {
    const postData = {
      sys_user_id: this.props.activeCompetence2.user_id,
      org_id: this.props.activeCompetence2.org_id,
    };
    getRoleList(postData).then((data) => {
      const _data = data.data;
      if (_data.code !== 0) {
        message.error(_data.message);
        return;
      } else {
        let selectKeys = [],
          selectedRows = [];
        _data.data.forEach((d, i) => {
          if (d.isCheck == 1) {
            selectKeys.push(d.role_id);
            selectedRows.push(d);
          }
        });
        // console.log(selectKeys);
        this.setState({
          roleDataSource: _data.data,
          selectedRowKeys1: selectKeys,
          // selectedRowKeys1Copy: selectKeys,
          selectedRows1: selectedRows,
          // selectedRows1Copy: selectedRows
        });
      }
    });
  }
  // 获取用户组
  getGroupList() {
    const postData = {
      user_id: this.props.activeCompetence2.user_id,
      org_id: this.props.activeCompetence2.org_id,
    };
    http
      .get(`${host}/uc/role/select-user-group-by-org`, postData)
      .then((data) => {
        const _data = data.data;
        if (_data.code !== 0) {
          message.error(_data.message);
          return;
        } else {
          let selectKeys = [],
            selectedRows = [];
          _data.data.forEach((d, i) => {
            if (d.isCheck == 1) {
              selectKeys.push(d.group_Id);
              selectedRows.push(d);
            }
          });
          // console.log(selectKeys);
          this.setState({
            groupDataSource: _data.data,
            selectedRowKeys2: selectKeys,
            selectedRows2: selectedRows,
          });
        }
      });
  }
  save() {
    const postData = {
      sys_user_id: this.props.activeCompetence2.user_id,
      org_id: this.props.activeCompetence2.org_id,
      role_ids: this.state.selectedRows1.map((d) => d.role_id),
    };
    addUserOrgRole(postData).then((data) => {
      const _data = data.data;
      if (_data.code != 0) {
        message.error(_data.message);
      } else {
        message.success("保存成功");
        this.props.hideCompetence2();
      }
    });
  }

  render() {
    const columns1 = [
      {
        title: "序号",
        align: "center",
        width: "10%",
        render: (t, row, i) => {
          return i + 1;
        },
      },
      {
        title: "角色名称",
        dataIndex: "name",
        key: "name",
        align: "center",
      },
      {
        title: "角色描述",
        dataIndex: "remark",
        key: "remark",
        align: "center",
      },
    ];

    const columns2 = [
      {
        title: "序号",
        align: "center",
        width: "10%",
        render: (t, row, i) => {
          return i + 1;
        },
      },
      {
        title: "用户组名称",
        dataIndex: "name",
        key: "name",
        align: "center",
      },
      {
        title: "用户组描述",
        dataIndex: "remark",
        key: "remark",
        align: "center",
      },
    ];
    return (
      <React.Fragment>
        <Modal
          // maskClosable={false}
          title="编辑用户权限"
          visible={this.state.visible}
          onCancel={this.handleCancel}
          width={900}
          height={688}
          footer={null}
        >
          <Form>
            <Form.Item {...formItemLayout} label="用户姓名">
              {this.props.activeCompetence1.user_name}
            </Form.Item>
            <Form.Item {...formItemLayout} label="组织名称">
              {this.props.activeCompetence2.org_name}
            </Form.Item>
            <Form.Item {...formItemLayout} label="选择角色">
              <div style={{ height: 300, overflowY: "auto" }}>
                <Table
                  rowKey="role_id"
                  columns={columns1}
                  bordered
                  rowSelection={{
                    onSelect: (record, selected, selectedRows, nativeEvent) => {
                      const { selectedRows1 = [], selectedRowKeys1 = [] } =
                        this.state;
                      // console.log("onSelect", record, selected, selectedRows, nativeEvent);
                      if (selected) {
                        selectedRows1.push(record);
                        selectedRowKeys1.push(record.role_id);
                      } else {
                        if (selectedRows1 && Array.isArray(selectedRows1)) {
                          const index = selectedRows1.findIndex((item) => {
                            return record.role_id === item.role_id;
                          });
                          if (index !== -1) {
                            selectedRows1.splice(index, 1);
                          }
                        }
                        if (
                          selectedRowKeys1 &&
                          Array.isArray(selectedRowKeys1)
                        ) {
                          const index = selectedRowKeys1.indexOf(
                            record.role_id
                          );
                          if (index !== -1) {
                            selectedRowKeys1.splice(index, 1);
                          }
                        }
                      }
                      this.setState({
                        selectedRows1,
                        selectedRowKeys1,
                      });
                    },
                    onSelectAll: (selected, rows, changeRows = []) => {
                      // console.log(changeRows);
                      // 全选数据
                      const { selectedRowKeys1 = [], selectedRows1 = [] } =
                        this.state;
                      if (
                        selectedRows1 &&
                        Array.isArray(selectedRows1) &&
                        changeRows &&
                        Array.isArray(changeRows)
                      ) {
                        // 如果是全选本页
                        if (selected) {
                          let selectedRows = [],
                            selectedRowKeys = [];
                          const allRows = selectedRows1.concat(changeRows);
                          selectedRowKeys = allRows.map((item) => {
                            return item.role_id;
                          });
                          // console.log(selectedRowKeys);
                          selectedRowKeys = Array.from(
                            new Set(selectedRowKeys)
                          );
                          selectedRowKeys.forEach((item) => {
                            selectedRows.push(
                              allRows.find((row) => {
                                return row.role_id === item;
                              })
                            );
                          });
                          this.setState({
                            selectedRowKeys1: selectedRowKeys,
                            selectedRows1: selectedRows,
                          });
                        }
                        // 如果是反选本页
                        else {
                          let rowIndex = -1,
                            keyIndex = -1;
                          changeRows.forEach((changeItem) => {
                            rowIndex = selectedRows1.findIndex((item) => {
                              return changeItem.role_id === item.role_id;
                            });
                            keyIndex = selectedRowKeys1.findIndex((item) => {
                              return changeItem.role_id === item;
                            });
                            if (rowIndex !== -1) {
                              selectedRows1.splice(rowIndex, 1);
                            }
                            if (keyIndex !== -1) {
                              selectedRowKeys1.splice(keyIndex, 1);
                            }
                          });
                          this.setState({
                            selectedRowKeys1,
                            selectedRows1,
                          });
                        }
                      }
                    },
                    selectedRowKeys: this.state.selectedRowKeys1,
                  }}
                  pagination={false}
                  dataSource={this.state.roleDataSource}
                />
              </div>
            </Form.Item>
            {/* <Form.Item {...formItemLayout} label="选择用户组">
              <div style={{ height: 300, overflowY: "auto", border:"1px solid #eaeaea" }}>
                <Table
                  rowKey="group_Id"
                  columns={columns2}
                  bordered
                  rowSelection={{
                    onSelect: (record, selected, selectedRows, nativeEvent) => {
                      const { selectedRows2 = [], selectedRowKeys2 = [] } = this.state;
                      // console.log("onSelect", record, selected, selectedRows, nativeEvent);
                      if (selected) {
                        selectedRows2.push(record);
                        selectedRowKeys2.push(record.group_Id);
                      } else {
                        if (selectedRows2 && Array.isArray(selectedRows2)) {
                          const index = selectedRows2.findIndex((item) => {
                            return record.group_Id === item.group_Id;
                          });
                          if (index !== -1) {
                            selectedRows2.splice(index, 1);
                          }
                        }
                        if (selectedRowKeys2 && Array.isArray(selectedRowKeys2)) {
                          const index = selectedRowKeys2.indexOf(record.group_Id);
                          if (index !== -1) {
                            selectedRowKeys2.splice(index, 1);
                          }
                        }
                      }
                      // console.log(selectedRows2, selectedRowKeys2);
                      this.setState({
                        selectedRows2,
                        selectedRowKeys2
                      });
                    },
                    onSelectAll: (selected, rows, changeRows) => {
                      // console.log(selected, rows, changeRows);
                      const { selectedRowKeys2 = [], selectedRows2 = [] } = this.state;
                      if (selectedRows2 && Array.isArray(selectedRows2) && changeRows && Array.isArray(changeRows)) {
                        // 如果是全选本页
                        if (selected) {
                          let selectedRows = [], selectedRowKeys = [];
                          const allRows = selectedRows2.concat(changeRows);
                          selectedRowKeys = allRows.map((item) => {
                            return item.group_Id;
                          });
                          // console.log(selectedRowKeys);
                          selectedRowKeys = Array.from(new Set(selectedRowKeys));
                          selectedRowKeys.forEach((item) => {
                            selectedRows.push(allRows.find((row) => {
                              return row.group_Id === item;
                            }));
                          });
                          this.setState({
                            selectedRowKeys2: selectedRowKeys,
                            selectedRows2: selectedRows
                          });
                        }
                        // 如果是反选本页
                        else {
                          let rowIndex = -1, keyIndex = -1;
                          changeRows.forEach((changeItem) => {
                            rowIndex = selectedRows2.findIndex((item) => {
                              return changeItem.group_Id === item.group_Id;
                            });
                            keyIndex = selectedRowKeys2.findIndex((item) => {
                              return changeItem.group_Id === item;
                            });
                            if (rowIndex !== -1) {
                              selectedRows2.splice(rowIndex, 1);
                            }
                            if (keyIndex !== -1) {
                              selectedRowKeys2.splice(keyIndex, 1);
                            }
                          });
                          this.setState({
                            selectedRowKeys2,
                            selectedRows2
                          });
                        }
                      }
                    },
                    selectedRowKeys: this.state.selectedRowKeys2
                  }}
                  pagination={false}
                  dataSource={this.state.groupDataSource}
                />
              </div>
            </Form.Item> */}
          </Form>
          <div style={{ textAlign: "center", marginTop: 20 }}>
            <Button
              type="primary"
              style={{ width: 115, height: 36 }}
              onClick={this.save}
            >
              确定
            </Button>
            <Button
              style={{ width: 115, height: 36, marginLeft: 45 }}
              onClick={this.handleCancel}
            >
              取消
            </Button>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
