.CheckInAuditDetail {
  .contentContainer {
    padding: 8px 40% 32px 120px;

    .ant-divider {
      color: #F46E65 !important;

      .title-vertical {
        border-radius: '2px' !important;
        background: #F46E65;
      }
    }

    .submit-btn {
      margin-left: 30%;
      width: 300px;
      height: 40px;
      background: #FF4D4F;
      border-radius: 4px;
    }
  }

  &-Info {

    &-Item {
      width: auto;
      margin-bottom: 24px;
      font-weight: 400;
      margin-left: 2.5%;

      &-label {
        position: relative;
        margin-bottom: 12px;
        padding-left: 18px;
        height: 20px;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 20px;

        &::before {
          content: "";
          position: absolute;
          width: 8px;
          height: 8px;
          top: 25%;
          left: 0;
          z-index: 1;
          background: RGBA(255, 40, 39, 1);
        }

        &::after {
          content: "";
          position: absolute;
          width: 8px;
          height: 8px;
          top: 30%;
          left: 2px;
          z-index: 0;
          background: RGBA(255, 40, 39, 0.2);
        }
      }

      &-value {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 20px;

        &>img {
          width: 160px;
          height: 160px;
          border-radius: 8px;
          margin-right: 12px;
        }
      }

      &-info {
        margin-top: 19px;
        font-size: 14px;
        color: #666666;
        line-height: 16px;
      }
    }
  }

  &-Flow {

    &-Step {
      width: auto;
      margin-left: 2.5%;
      .step-item {
        position: relative;
        padding: 15px 0 15px 32px;

        &-icon {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          left: 0;

          &::before {
            content: "";
            position: absolute;
            top: -50px;
            width: 2px;
            height: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: #EEEEEE;
          }

          &::after {
            content: "";
            position: absolute;
            bottom: -50px;
            width: 2px;
            height: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: #EEEEEE;
          }
        }

        &-content {
          padding: 12px 32px;
          background: rgba(246, 246, 246, 0.88);
          border-radius: 8px;

          &-header {
            display: flex;
            align-items: center;

            &>span {
              display: inline-block;
              font-weight: 400;

              &:nth-of-type(1) {
                height: 22px;
                font-size: 16px;
                color: #202020;
                line-height: 19px;
              }

              &:nth-of-type(2) {
                margin-left: 12px;
                padding: 0 8px;
                height: 24px;
                border-radius: 26px;
                font-size: 14px;
                line-height: 24px;
              }

              &:nth-of-type(3) {
                margin-left: auto;
                font-size: 14px;
                color: #969696;
                line-height: 16px;
              }
            }

            .tipTime {
              margin-left: auto !important;
              font-size: 14px !important;
              color: #969696 !important;
              line-height: 16px !important;
              padding: 0 !important;
            }
          }

          &-opinion {
            margin-top: 8px;
            height: 20px;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            line-height: 16px;
          }
        }

        &:first-of-type {
          .step-item-icon {
            &::before {
              content: none;
            }
          }
        }

        &:last-of-type {
          .step-item-icon {
            &::after {
              content: none;
            }
          }
        }
      }

      .step-item-4 {
        .step-item-icon {
          background: url('./images/wait-icon.png') no-repeat center/contain;
        }

        .step-item-content {
          &-header {

            .tip {
              background: rgba(250, 149, 66, 0.2);
              color: #FA9542;
            }

          }
        }
      }

      .step-item-1 {
        .step-item-icon {
          background: url('./images/pass-icon.png') no-repeat center/contain;
        }

        .step-item-content {
          &-header {

            .tip {
              background: rgba(143, 195, 31, 0.2);
              color: #8FC31F;
            }

          }
        }
      }

      .step-item-5 {
        .step-item-icon {
          background: url('./images/withdraw-icon.png') no-repeat center/contain;
        }

        .step-item-content {
          &-header {

            .tip {
              background: rgba(153, 153, 153, 0.2);
              color: #999999;
            }

          }
        }
      }

      .step-item-2 {
        .step-item-icon {
          background: url('./images/fail-icon.png') no-repeat center/contain;
        }

        .step-item-content {
          &-header {

            .tip {
              background: rgba(244, 110, 101, 0.2);
              color: #F46E65;
            }

          }
        }
      }
    }
  }

  &-Audit {
    .ant-form{
      width: auto;
      margin-left: 2.5%;
    }
    .ant-radio-wrapper-checked {
      color: #8FC31F;

      .ant-radio-inner {
        border-color: #8FC31F;
        background-color: #8FC31F;
      }
    }
  }
}

.img-preview_wrap {

  .ant-modal-content,
  .ant-modal-body {
    background-color: transparent;
    box-shadow: none;
  }
}