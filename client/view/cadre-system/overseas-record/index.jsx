import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import { Form, Input, Popconfirm, Button, Table, Modal, message, Radio, DatePicker, Select } from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryUserCert,
  addOrUpdateUserCert,
  deleteUserCert,
  exportUserCert,
  queryMajorEventDetail,
} from "client/apis/cadre-portrait";
import moment from "moment";
const { Option } = Select;
function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields, setFieldsValue } = form;
  const { RangePicker } = DatePicker;
  const modalFormRef = useRef(null);

  const [treeData, setTreeData] = useState([]);

  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
 
  useEffect(() => {
    loadData();
  }, []);

  // 相同用户的记录合并处理
  const processData = (data) => {
    const processedData = [];

    data.forEach((record) => {
        const { name, cur_job, infos } = record;
        const rowSpan = infos.length; // 证件信息的数量决定了 rowSpan 的值

        infos.forEach((info, index) => {
            processedData.push({
                key: Math.random(), // 为每条记录添加唯一的 key
                name: index === 0 ? name : '', // 只在第一条记录显示姓名
                cur_job: index === 0 ? cur_job : '', // 只在第一条记录显示职务
                info, // 当前的证件信息
                rowSpan: index === 0 ? rowSpan : 0 // 只有第一条记录才设置 rowSpan
            });
        });
    });

    return processedData;
};
  // 加载数据
  const loadData = async ({page: _page, org_id} = {}) =>{
    const fields = getFieldsValue();
    const params = {
      // ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.user_name,
    };
    const res = await queryUserCert(params);
    if(res.data.code === 0) {
      const data = res.data.data.data;
      setDataSource(processData(data));
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.total,
        };
      });
    }

    setLoading(false);
  }

  const loadDataDesc = async (id) => {
    const res = await queryMajorEventDetail({
      performance_id: id,
    });

    if (res.data.code === 0) {
      modalFormRef.current.setFieldsValue(res.data.data);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportUserCert({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=8&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteUserCert({
      id: record.user_cert_id || record.info.user_cert_id,
    });
    if (res.data.code === 0) {
      loadData();
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");

    queueMicrotask(() => {
        const { info } = record; // 从 record 中取出当前要编辑的 info 对象
        console.log("record: ", record);
        // console.log("info: ", info);
        if (info) {
            const validityStart = info.validity_start ? moment(info.validity_start, "YYYY年MM月DD日") : undefined;
            const validityEnd = info.validity_end ? moment(info.validity_end, "YYYY年MM月DD日") : undefined;
            const validityRange = (validityStart && validityEnd) ? [validityStart, validityEnd] : undefined;

            const dataArray = [
                {
                    user_cert_id: record.user_cert_id,
                    type: info.type_str === "护照" ? 1 : (info.type_str === "港澳通行证" ? 2 : 3),
                    cert_number: info.cert_number,
                    validity_start: validityRange,
                    content: info.content,
                }
            ];

            // 填充表单数据
            modalFormRef.current.setFieldsValue({
                user_id: record.user_id || info.user_id,
                user_cert_id: record.user_cert_id || info.user_cert_id,
                user_name: record.name || info.name,
                cur_job: record.cur_job || info.cur_job,
                data: dataArray, // 把处理好的证件信息放到 data 字段
            });
        }
    });
};

  

  const columns = [
  {
      title: '姓名',
      dataIndex: 'name',
      align: "center",
      render: (text, record) => ({
          children: text,
          props: {
              rowSpan: record.rowSpan // 设置 rowSpan
          }
      })
  },
  // {
  //   title: '出生年月',
  //   dataIndex: 'birthday',
  //   align: "center",
  //   render: (text, record) => ({
  //       children: text,
  //       props: {
  //           rowSpan: record.rowSpan // 设置 rowSpan
  //       }
  //   })
  // },
  {
      title: '现任职务',
      dataIndex: 'cur_job',
      align: "center",
      render: (text, record) => ({
          children: text,
          props: {
              rowSpan: record.rowSpan // 设置 rowSpan
          }
      })
  },
  {
      title: '证件名称',
      dataIndex: 'info.type_str', 
      align: "center",
      render: text => text
  },
  {
      title: '证件号码',
      dataIndex: 'info.cert_number',
      align: "center",
      render: text => text
  },
  {
      title: '证件有效期',
      dataIndex: 'info.validity_text',
      align: "center",
      render: text => text
  },
  {
      title: '使用情况',
      dataIndex: 'info.content',
      align: "center",
      render: text => text
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
    align: 'center',
    render(_, record) {
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '0px 10px',
          }}
        >
          <Popconfirm
            title="确认删除?"
            onConfirm={() => onDel(record)}
            okText="确认"
            cancelText="取消"
          >
            <a>删除</a>
          </Popconfirm>
          <a onClick={() => onEditor(record)}>编辑</a>
        </div>
      );
    },
  },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData({ page: 1 });
  };
  
  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { data = [], user_name: _user_name, ...rest } = values;
        // 格式化 data 数据
        const formattedData = data.map((item, index) => {
          let validityStart = "";
          let validityEnd = "";
          if (Array.isArray(item.validity_start) && item.validity_start.length === 2) {
            const [validity_start, validity_end] = item.validity_start;
            validityStart = validity_start.format("YYYY-MM-DD");
            validityEnd = validity_end.format("YYYY-MM-DD");
            // console.log("有效期开始: ", validityStart);
            // console.log("有效期结束: ", validityEnd);
          }
          return {
            type: item.type,
            cert_number: item.cert_number,
            validity_start: validityStart,
            validity_end: validityEnd,
            content: item.content,
            user_cert_id: values.user_cert_id,
          };
      });

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        // console.log("values: ", values)
        const payload = {
          ...rest,
          user_name: values.user_name,
          user_id: values.user_id,
          data: formattedData,
        };

        const res = await addOrUpdateUserCert(payload);
        // console.log("payload: ", payload);

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };
  
  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };
    //管理表单项的状态
    const [data, setData] = useState([{}]);
    const addCertificate = () => {
      setData([...data, {}]);
    };
    const removeCertificate = (index) => {
      const updateddata = data.filter((_, i) => i !== index);
      setData(updateddata);
    };
 
    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("user_cert_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" labelCol={{span: 6}} wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月" labelCol={{span: 6}} wrapperCol={{ span: 18 }}>
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        {data.map((_, index) => (
        <div key={index} style={{ border: "1px dashed #ddd", padding: '16px', marginBottom: '8px' }}>
          <Form.Item label="证件名称" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            {getFieldDecorator(`data[${index}].type`, {
              initialValue: _.type,
              rules: [{ required: true, message: "请选择证件名称" }],
            })(
              <Select placeholder="请选择证件名称">
                <Option value={1}>护照</Option>
                <Option value={2}>港澳通行证</Option>
                <Option value={3}>台湾通行证</Option>
              </Select>
            )}
          </Form.Item>
          <Form.Item label="证件号码" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            {getFieldDecorator(`data[${index}].cert_number`, {
              initialValue: _.cert_number,
              rules: [{ required: true, message: "请输入证件编号" }],
            })(<Input placeholder="请输入证件编号" />)}
          </Form.Item>
          <Form.Item label="证件有效期" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            {getFieldDecorator(`data[${index}].validity_start`, {
              initialValue: _.validity_start,
              rules: [{ required: true, message: "请输入证件有效期" }],
            })(<RangePicker />)}
          </Form.Item>
          <Form.Item label="使用情况" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            {getFieldDecorator(`data[${index}].content`, {
              initialValue: _.content,
              rules: [{ required: false, message: "请输入使用情况" }],
            })(<Input placeholder="请输入使用情况" />)}
          </Form.Item>
          {index > 0 && (
            <Button
              type="danger"
              onClick={() => removeCertificate(index)}
            >
            删除
          </Button>)}
        </div>
      ))}
      <Form.Item>
        <Button
          type="dashed"
          onClick={addCertificate}
          disabled={modalType === 'edit' ? true: false}
        >
          添加证件
        </Button>
      </Form.Item>
        {/* <div style={{border: "1px dashed #ddd"}}>
        <Form.Item label="证件名称" labelCol={{span: 6}} style={{marginTop: "1rem"}}>
            {getFieldDecorator("type", {
              rules: [{ required: true, message: "请选择证件名称" }],
            })(
              <Select>
                <Option value={1}>护照</Option>
                <Option value={2}>港澳通行证</Option>
                <Option value={3}>台湾通行证</Option>
              </Select>
            )}
        </Form.Item>
        <Form.Item label="证件号码" labelCol={{span: 6}}>
          {getFieldDecorator("cert_number", {
            rules: [{ required: true, message: "请输入证件号码" }],
          })(
            <Input placeholder="请输入"/>
          )}
        </Form.Item>
       <Form.Item label="证件有效期" labelCol={{span: 6}}>
          {getFieldDecorator("validity_start", {
            rules: [{ required: true, message: "请输入证件有效期" }],
          })(<RangePicker />)}
        </Form.Item>
        <Form.Item label="使用情况" labelCol={{span: 6}}>
          {getFieldDecorator("content", {
            rules: [{ required: true, message: "请输入使用情况" }],
          })(<Input placeholder="请输入"/>)}
        </Form.Item>
        </div> */}
      </Form>
    );
  });
  return (
    <div className="petition-reporting">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            {/* <Form.Item label="事件">
              {getFieldDecorator("event")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item> */}
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "出国（境）证件记录"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
