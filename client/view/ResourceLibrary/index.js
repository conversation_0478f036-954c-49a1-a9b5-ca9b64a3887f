import React from "react";
import SearchHeader from "components/search-header";
import DocIcon from "./image/doc-icon.png";
import CaseIcon from "./image/case-icon.png";
import TechIcon from "./image/tech-icon.png";
import AddrIcon from "./image/addr-icon.png";
import "./index.less";

const menuList = [
  { name: "文件库", path: "/resource-library/document", icon: DocIcon },
  { name: "师资库", path: "/teacher-library", icon: TechIcon },
  { name: "案例库", path: "/resource-library/case", icon: CaseIcon },
  { name: "场地库", path: "/field-library/document", icon: AddrIcon },
];
const ResourceLibrary = (props) => {
  const { history } = props;

  return (
    <div className="resource-library">
      <SearchHeader
        onBack={false}
        title={"资源库"}
      />
      <div className="resource-library-content">
        {menuList.map((item, index) => {
          return (
            <div
              className="menu-item"
              key={index}
              onClick={() => history.push(item.path)}
            >
              <img src={item.icon} alt="" />
              <div>{item.name}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ResourceLibrary;
