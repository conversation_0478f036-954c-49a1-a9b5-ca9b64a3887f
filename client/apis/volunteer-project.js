const { http } = require('client/tool/axios');
import { vlHost,ucHost, host } from './config';
/**
 * 300003010201-志愿项目列表查询
 * @param {*} params 
 */
const getVolunteerProjectList = (params) => {
  return http.post(`${vlHost}/volunteer/project/list`, params);
};

/**
 * 300003010202-志愿项目详情查询
 * @param {*} project_id 
 */
const getVolunteerProjectDetails = (project_id) => {
  return http.get(`${vlHost}/volunteer/project/detail`, {
    project_id
  });
};

/**
 * 300003010205-保存草稿志愿项目    300003010204-修改志愿项目   300003010203-新增志愿项目
 * @param {*} type 1-新增，2-修改，3-草稿
 * @param {*} params 
 */
const editVolunteerProject = (type, params) => {
  switch (type) {
    // 新增
    case 1:
      return http.post(`${vlHost}/volunteer/project/add`, params);
    // 修改
    case 2:
      return http.post(`${vlHost}/volunteer/project/update`, params);
    // 草稿
    case 3:
      return http.post(`${vlHost}/volunteer/project/draft`, params);
    default:
      break;
  }
};

/**
 * 300003010206-志愿项目结项
 * @param {*} project_id 
 */
const finishVolunteerProject = (project_id) => {
  return http.get(`${vlHost}/volunteer/project/finish`, { project_id });
};

/**
 * 300003010207-项目作废
 * @param {*} project_id 
 */
export const invalidVolunteerProject = (project_id) => {
  return http.get(`${vlHost}/volunteer/project/invalid`, { project_id });
};

/**
 * 300003010208-项目延期
 * @param {*} project_id 
 */
export const delayVolunteerProject = (project_id, project_end_time) => {
  return http.get(`${vlHost}/volunteer/project/delay`, { project_id, project_end_time });
};

/**
 * 10630202010110-通过用户Id查询关联志愿者状态
 * @param {*} user_id 用户id
 */
const exchangeVolunteerIdByUserId = (user_id) => {
  return http.get(`${ucHost}/volunteer/user/check-volunteer-user`, { user_id });
};

/**
 * 300003010212-通过组织id 获取下级团体信息
 * @param {*} org_id 组织id
 */
export const findOrgLevel = (org_id) => {
  return http.get(`${host}/volunteer/user/find-org-level?`, { org_id });
};

//0503-根据pid获取父级的下级区域列表
const fetchAddressOption = (pid = '') => {
  return http.get(`${ucHost}/dict/area/list`, { pid });
};
//通复制从 10630202010110-通过用户Id查询关联志愿者状态(批量)
const getVolunteerUser = (data) => {
  return http.post(`${ucHost}/volunteer/user/check-volunteer-user`, data);
};
//300003010101-招募列表查询
const getVolunteerRecruitList = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/list/${data.type}`, data);
};
// 300003010103-剔除/退出
const deleteVolunteerRecruit = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/delete`, data);
};
// 300003010104 - 设置 / 取消管理员
const setOrCancelAdmin = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/setOrCancelAdmin`, data);
};
// 300003010105 - 录用 / 拒绝
const recruitOrRefuseVolunteer = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/recruitOrRefuse`, data);
};
//300003010106-邀请
const volunteerRecruitInvite = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/invite`, data);
};
//300003010109-取消邀请
const volunteerRecruitCancelInvite = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/cancelInvite`, data);
};
// 300003010107-申请
const volunteerRecruitApplyFor = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/applyFor`, data);
};
// 300003010108-待选拔人数
const getWaitChooseCount = (data) => {
  return http.post(`${vlHost}/volunteer/recruit/waitChooseCount`, data);
};
// 300003010401-今日未录入/今日已录入
const getVolunteerTimeEntryList = (data) => {
  return http.post(`${vlHost}/volunteer/time/entryList/${data.type}`, data);
};
// 300003010403-时长记录
const getVolunteerTimeRecordList = (data) => {
  return http.post(`${vlHost}/volunteer/time/recordList`, data);
};
// 300003010403-时长记录
const addVolunteerTime = (data) => {
  return http.post(`${vlHost}/volunteer/time/add`, data);
};
// 300003010404 - 时长记录删除
const deleteVolunteerTime = (data) => {
  return http.post(`${vlHost}/volunteer/time/delete`, data);
};
// 300003010301-已评价/未评价人员列表
const getAppraisedUserList = (data) => {
  return http.post(`${vlHost}/volunteer/appraised-user/list/${data.appraised_status}`, data);
};
// 300003010302-2批量评价志愿者
const volunteerAppraisedUserBatch = (data) => {
  return http.post(`${vlHost}/volunteer/appraised-user/batch`, data);
};
// 300003010304-标记未到岗
const volunteerReportedNoTag = (data) => {
  return http.post(`${vlHost}/volunteer/reported/no-tag/${data.recruit_id}`, data);
};
// 300003010305-取消未到岗标记
const deleteReportedNoTag = (data) => {
  return http.delete(`${vlHost}/volunteer/reported/no-tag/${data.recruit_id}`, data);
}
// 300003010303-评价信息
const getVolunteerAppraisedInfo = (data) => {
  return http.get(`${vlHost}/volunteer/appraised-user/by-recruit-id/${data.recruit_id}`, data);
}
// 300003010210-项目积分结算列表查询
const getVolunteerProjectScoreList = (data) => {
  return http.post(`${vlHost}/volunteer/project/score/list`, data);
};
//300003010211-项目积分结算列表总积分
const getVolunteerProjectScoreStatistics = (data) => {
  return http.post(`${vlHost}/volunteer/project/score/statistics`, data);
};
// 300003010209 - 项目结算
const getVolunteerProjectSettlement = (data) => {
  return http.get(`${vlHost}/volunteer/project/settlement`, data);
};

export {
  getVolunteerProjectList,
  getVolunteerProjectDetails,
  editVolunteerProject,
  finishVolunteerProject,
  exchangeVolunteerIdByUserId,
  fetchAddressOption,
  getVolunteerUser,
  getVolunteerRecruitList,
  deleteVolunteerRecruit,
  setOrCancelAdmin,
  recruitOrRefuseVolunteer,
  volunteerRecruitInvite,
  volunteerRecruitCancelInvite,
  volunteerRecruitApplyFor,
  getWaitChooseCount,
  getVolunteerTimeEntryList,
  getVolunteerTimeRecordList,
  addVolunteerTime,
  deleteVolunteerTime,
  getAppraisedUserList,
  volunteerAppraisedUserBatch,
  volunteerReportedNoTag,
  deleteReportedNoTag,
  getVolunteerAppraisedInfo,
  getVolunteerProjectScoreList,
  getVolunteerProjectScoreStatistics,
  getVolunteerProjectSettlement
};

