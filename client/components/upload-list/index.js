import React, { Component } from "react";
import qs from "querystring";
import path from "path";
import { Upload, message as Message } from "antd";
import { fileHost, uploadHost, CDN } from "apis/config";
import { postFilePreview } from "apis/file";
import { getIcon, converFileSize } from "tool/util";
import SelfIcon from "components/self-icon";
import LoadingModal from "components/loading-modal";
import FileUpload from "components/file-upload";
import "./index.less";
import propTypes from "prop-types";
import Preview from "components/preview";
import FileDownload from 'client/components/file-download';


// 等待上传
let
  // uploading = false,
  waitUploadList = [],
  waitUploadCount = 0;

class UploadList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      uploadFileUrl: `${uploadHost}/file/upload`,
      fileList: [],
      // // 导出接口地址
      // path: "/file/download/",
      // // 当前查询条件
      // queryParams: {},
      downloadUrl: "",
      uploading: false,
      previewTimer: null
    }
  }
  componentWillMount() {
    const { inputData } = this.props;
    if (inputData.length !== 0) {
      this.setState({
        fileList: inputData
      });
    }
  }
  componentWillReceiveProps(nextProps) {
    const { inputData } = nextProps;
    // if (inputData.length !== 0) {
    //   this.setState({
    //     fileList: inputData
    //   });
    // }
    this.setState({
      fileList: inputData
    });
  }

  async postFilePreview(params = {}) {
    this.setState({
      uploading: true
    });
    const response = await postFilePreview(params);
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      this.setState({
        uploading: false
      });
      Message.error(message);
      return;
    }
    // 生成成功
    if (data.status === 1 && data.target_url) {
      typeof window !== "undefined" && window.open(`https://preview.imm.aliyun.com/index.html?url=${CDN}/${data.target_url}`);
    }
    else if (data.status === 2 && data.request_id) {
      this.postFilePreview({ ...params, request_id: data.request_id });
    } else {
      Message.error("预览出现错误");
    }
    this.setState({
      uploading: false
    });
  }

  exportData() {
    const { fileList } = this.state;
    const output = JSON.parse(JSON.stringify(fileList));
    return output;
  }

  removeFile(index, file) {
    const { fileList } = this.state;
    const { onChange } = this.props;
    fileList.splice(index, 1);
    this.setState({
      fileList
    });
    onChange(fileList);
  }

  /* initDownloadUrl(file) {
    // console.log(file);
    // const { downloadBase } = this.state;
    // const target = "/file/download/";
    // const params = qs.stringify({ path: `${target}${file.name}` });
    // const result = `${downloadBase}?${params}`;
    const result = `/file/file/download/${file.id || file.name || file.file_name }`;
    return result;
  } */

  render() {
    const { uploadFileUrl, fileList, uploading } = this.state;
    // console.log(fileList);
    const { canEdit, onChange, maxAmount, canPreview, emptyText, upType, tipText, children, limitSize, accept } = this.props;
    // 计数上传文件数量
    let count = maxAmount;
    const uploadProps = {
      name: "upfile",
      action: uploadFileUrl,
      fileList,
      showUploadList: false,
      supportServerRender: true,
      multiple: true,
      headers: {
        "x-csrf-token": typeof window !== "undefined" ? window.sessionStorage.getItem("csrf") || "" : "",
        "Access-Control-Allow-Credentials": "true",
        "_tk": typeof window !== "undefined" ? window.sessionStorage.getItem("_tk") || "-1" : "-1",
        "_uid": typeof window !== "undefined" ? window.sessionStorage.getItem("_uid") || "1" : "1",
        "_un": typeof window !== "undefined" ? window.sessionStorage.getItem("_un") || "1" : "1",
        "_type": typeof window !== "undefined" ? window.sessionStorage.getItem("_type") || "2" : "2",
        "_oid": typeof window !== "undefined" ? window.sessionStorage.getItem("_type") || "1" : "1"
      },
      data: {
        upType: upType || "file"
      },
      beforeUpload: (file, list) => {
        // console.log("beforeUpload", list);
        // console.log(list, fileList);
        if ((list.length + fileList.length) > maxAmount) {
          count--;
          // console.log(count, maxAmount - list.length);
          if (count === (maxAmount - list.length)) {
            Message.warn(`限定上传${maxAmount}个文件`);
            count = maxAmount;
          }
          waitUploadList = [];
          waitUploadCount = 0;
          return false;
        } else {
          if (!uploading) {
            this.setState({
              uploading: true
            });
          }
          waitUploadList = list;
          waitUploadCount = list.length;
        }
      },
      onSuccess: (response, file) => {
        // console.log("onSucess", file);
        // console.log(response);
        let { code, data } = response;
        let id = -1;
        if (code !== 0) {
          Message.error(file.name + '上传失败!' + response.message);
          return;
        }
        //上传成功后，更新上传的列表
        fileList.push(data[0]);
        this.setState({
          fileList
        });
        onChange(fileList);
        if (waitUploadCount > 1 && waitUploadList.length !== 0) {
          waitUploadCount--;
        }
        else if (waitUploadCount === 1 && waitUploadList.length !== 0) {
          waitUploadCount = 0;
          waitUploadList = [];
          this.setState({
            uploading: false
          });
        }
        else {
          this.setState({
            uploading: false
          });
        }
      },
      onError: (e, info, file) => {
        //上传文件失败是，隐藏模态框
        Message.error(file.name + '上传失败!');
        this.setState({
          uploading: false
        });
      }
    }
    const loadingModalProps = {
      modalVisible: uploading,
      noTip: true
      // tip: "请稍候..."
    }

    let fileListHTML;
    if (fileList && fileList.length) {
      fileListHTML = fileList.map((file, index) => {
        let ext = path.extname(file.name || "").substr(1);
        if (!ext) {
          ext = path.extname(file.path || "").substr(1);
        }

        const icon = getIcon(ext)[0];
        const color = getIcon(ext)[1];
        return (
          <li className="upload-item-container" key={index}>
            <SelfIcon type={icon} className="file-type-icon" />
            {/* {JSON.stringify(file)} */}
            <div className="file-info-wrapper">
              <div className="file-name-wrapper">
                <span className="file-name" title={file.file_name}>{file.file_name || "未知文件"}</span>
                {/* 废弃 {
                  (canPreview && (file.id || file.path)) &&
                  <a style={{ marginRight: 10, cursor: "pointer" }} href="javascript:void(0)" onClick={() => {
                    // console.log("预览", file);
                    this.postFilePreview({
                      file_id: file.id || null,
                      file_path: file.path || null
                    });
                  }}>预览</a>
                } */}
                {
                  (canPreview && (file.id || file.path)) &&
                  <span style={{paddingRight: 15}}>
                    <Preview file={{
                      file_id: file.id || null,
                      file_path: file.path || null
                    }} text="预览" />
                  </span>
                }
                {
                  canEdit ?
                    <SelfIcon type="gsg-shanchu6"onClick={() => this.removeFile(index, file)} /> :
                    <FileDownload
                      filePath={`/file/file/download/${file.id || file.name}`}
                      fileName={file.file_name || file.name}
                      type="link"
                      btnName="下载附件"
                      style={{ paddingLeft: 0}}
                    />
                }
              </div>
              <div className="file-size">
                <span>{file.size ? converFileSize(file.size) : "未知大小"}</span>
                {/* <a
                    onClick={()=>{
                      // fileDownload(this.initDownloadUrl(file))
                    }}
                    href="javascript:void(0);"
                    >下载附件</a> */}
                {/* 暂留 <span>{file.size ? converFileSize(file.size) : "未知大小"}</span> */}
              </div>
            </div>
          </li>
        )
      });
    } else if (!canEdit && (!fileList || !Array.isArray(fileList) || fileList.length === 0)) {
      fileListHTML = emptyText;
    }

    return (
      <div className="upload-list">
        {
          (canEdit && uploading) &&
          <LoadingModal {...loadingModalProps} />
        }
        {
          canEdit &&
          <div className="upload-trigger">
            <FileUpload
              // max={9}
              tipText={tipText}
              children={children}
              limitSize={limitSize}
              upType={upType || "file"}
              uploadedList={fileList}
              accept={accept}
              onChange={(fileList) => {
                this.setState({
                  fileList
                });
                onChange(fileList);
              }} />
            {/* <Upload {...uploadProps}>
              <a onClick={(e) => {
                if (fileList.length >= maxAmount) {
                  e.stopPropagation();
                  Message.warn(`限定上传${maxAmount}个文件`);
                }
              }}>上传附件</a>
            </Upload> */}
            {/* <input type="file" multiple onChange={() => {
              console.log("onChange");
            }} /> */}
          </div>
        }
        <div className="upload-list-container">
          <ul className="upload-list-wrapper">
            {fileListHTML}
          </ul>
        </div>
      </div>
    )
  }
}

UploadList.propTypes = {
  // 提示性文本
  tipText: propTypes.oneOfType([propTypes.string, propTypes.element]),
  // 文件上传的最大个数
  maxAmount: propTypes.number,
  //文件大小单位 M
  limitSize: propTypes.number,
  exportData: propTypes.func,
  // 是否可以编辑上传文件列表
  canEdit: propTypes.bool,
  // 是否可以预览文件
  canPreview: propTypes.bool,
  // 是否用于移动端,如果是,则使用oss加速地址
  useInMobile: propTypes.bool,
  // 导入的初始数据
  inputData: propTypes.array,
  // 获取组件的值变化
  onChange: propTypes.func,
  // 设置无文件的信息
  emptyText: propTypes.string,
  // upType参数兼容
  upType: propTypes.string,
  // 接受类型
  accept: propTypes.string
}

UploadList.defaultProps = {
  tipText: "",
  // maxAmount: 9,
  exportData: (data) => { console.log("默认方法导出数据", data) },
  canEdit: true,
  canPreview: true,
  useInMobile: false,
  inputData: [],
  onChange: (data) => { console.log("上传文件组件值变化", data) },
  emptyText: null,
  upType: "file",
  accept: ''
}

export default UploadList;
