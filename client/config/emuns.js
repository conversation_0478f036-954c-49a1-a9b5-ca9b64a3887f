/**
 * 表单状态
 */

//活动规则select选项
const activityRulesRestrictionsEmuns = [
    {
        label: '只能参加一次',
        value: 1
    },
    {
        label: '不限（可以重复参加）',
        value: 0
    },
    {
        label: '30分钟内不能重复参加',
        value: 2
    },
    {
        label: '1小时不能重复参加',
        value: 3
    },
    {
        label: '6小时不能重复参加',
        value: 4
    },
    {
        label: '12小时不能重复参加',
        value: 5
    },
    {
        label: '24小时不能重复参加',
        value: 6
    },
    {
        label: '48小时不能重复参加',
        value: 7
    },
    {
        label: '每个自然日可参加1次',
        value: 8
    },
    {
        label: '每个自然日可参加2次',
        value: 9
    },
    {
        label: '每个自然日可参加3次',
        value: 10
    },
    {
        label: '每个自然日可参加4次',
        value: 11
    },
    {
        label: '每个自然日可参加5次',
        value: 12
    },
    {
        label: '每个自然日可参加6次',
        value: 13
    },
    {
        label: '每个自然日可参加7次',
        value: 14
    },
    {
        label: '每个自然日可参加8次',
        value: 15
    },
    {
        label: '每个自然日可参加9次',
        value: 16
    },
    {
        label: '每个自然日可参加10次',
        value: 17
    }
]

export default {
    voteOptionsButtons: [       //投票选项按钮
        {
            label: '单选投票',
            value: 2
        },
        {
            label: '多选投票',
            value: 1
        },
        {
            label: '一键投票',
            value: 3
        }
    ],
    rewardModelEnum: {          //获奖模式
        normal: {
            label: '参与即可获得奖励',
            value: 1
        },
        random: {
            label: '随机抽取奖励',
            value: 2
        }
    },
    voteResultPublic: [         //是否公布投票结果
        {
            label: '不公布投票结果',
            value: 0
        },
        {
            label: '公布投票结果',
            value: 1
        }
    ],
    voteSortingSetting: {       //排序设置
        normal: {
            label: '投票项排序（按照投票项的顺序排序）',
            value: 1
        },
        down: {
            label: '票数从大到小',
            value: 2
        },
        up: {
            label: '票数从小到大',
            value: 3
        },
        only: {
            label: '只显示票数前',
            value: 4
        }
    },     //排序设置只显示票数
    voteStatisticsShowSetting: [        //统计显示设置
        {
            label: '显示票数',
            value: 1
        },
        {
            label: '显示得票百分比',
            value: 2
        }
    ],
    voteSumPeople: [        //总投票人次设置
        {
            label: '显示',
            value: 1
        },
        {
            label: '不显示',
            value: 0
        }
    ],
    statisticalDimension: {     //统计维度
        age: {
            label: '年龄维度',
            value: 1
        },
        sex: {
            label: '性别维度',
            value: 2
        },
        education: {
            label: '文化程度维度',
            value: 3
        },
        ethnic: {
            label: '民族维度',
            value: 4
        },
        other: {
            label: '其他身份维度',
            value: 99
        }
    },
    joinModel: [        //参与模式
        {
            label: '记名',
            value: 1
        },
        {
            label: '不记名（会员无需登录即可参与）',
            value: 0
        }
    ],
    accountRestrictions: activityRulesRestrictionsEmuns,        //账户限制
    ipRestrictions: activityRulesRestrictionsEmuns,             //账户限制
    deviceRestrictions: activityRulesRestrictionsEmuns,         //设备限制
    approvalType: [      //审批类型
        {
            label: '网宣审批',
            value: 0
        }
    ],
    joinStaffRange: [
        {
            label: '开放活动',
            value: 0
        },
        {
            label: '指定参加人员名单',
            value: 1
        },
        {
            label: '指定参加组织人员',
            value: 2
        }
    ],
    joinStaffRestriction: [
        {
            label: '不限制',
            value: 0
        },
        {
            label: '有限制',
            value: 1
        }
    ],
    activityVisible: [
        {
            label: '所有人员可见',
            value: 0
        },
        {
            label: '参加人员可见',
            value: 1
        }
    ],
    customizeTag: {
        recommend: {
            label: '推荐',
            value: 1,
            previewUri: require('./images/recommend.png'),
            description: '列表置顶',
            note: '123'
        },
        focused: {
            label: '聚焦',
            value: 2,
            previewUri: require('./images/focused.png'),
            description: '活动页面顶部banner',
            note: ''
        }
    }
}