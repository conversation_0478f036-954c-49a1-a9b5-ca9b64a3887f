import React, { Component } from "react";
import { Form, Input, Select, TreeSelect, Button, Switch, Icon } from "antd";
import Editor from "client/components/RichTextEditor";
import SearchHeader from "components/search-header";
import InputTips from "components/activity-form/sub-components/InputTips";
import { List } from "immutable";

import Image from "./image";
import Preview from "./preview";
const { TextArea } = Input;
const FormItem = Form.Item;
const Option = Select.Option;
import { updateUserOperateState } from "tool/util";
import InvisibleSaveDraftBtn from "components/prompt/hide-btn";
import VideoUpload from "components/video-upload";
import VideoList from "./VideoList";
import ShowUploadFileType from "components/show-upload-file-type";
import { CDN } from "apis/config";

class View extends Component {
  componentDidMount() {}
  constructor(props) {
    super(props);
    // todo:state
    this.state = {
      files: [],
      uploading: false,
      fileNames: <span>视频上传</span>,
      content: "", // 资源内容
    };
  }

  // todo:updateState
  // updateState(payload, callback) {
  //   const { getFieldValue } = this.props.form;
  //   const { onCleanVideo } = this.props;
  //   this.setState(payload, () => {
  //     callback && callback();
  //     this.setState({
  //       fileNames: this.state.files.length ? (
  //         <span>重新上传</span>
  //       ) : (
  //         <span className="video-upload">视频上传</span>
  //       ),
  //     });
  //     if (!this.state.files.length) {
  //       if (typeof onCleanVideo === "function") onCleanVideo(true);
  //     }
  //   });
  // }

  getContent() {
    return this.state.content;
  }

  render() {
    const {
      handleSubmit,
      columnOnChange,
      editOnChange,
      sub_columnOnChange,
      clipEvent,
      show_preview_list,
      show_preview_article,
      closePreview,
    } = this.props.events;
    const {
      preUploadType,
      ApprovalProcess,
      approvalProcessProps,
      autoSave,
      setState,
      form,
      formItemLayout = {},
      container,
      param,
      newsColumn,
      column_id,
      sub_column,
      files_type2,
      files_type3,
      files_type4,
      preview_list,
      preview_article,
      preview_list_data,
      preview_article_data,
      hanlderSaveDraft,
      isEdit,
      isPreview,
      action,
      history,
      state: parentState,
      element,
      cleanVideo,
      onCleanVideo,
      setVideoFiles,
      that,
    } = this.props;
    // console.log(isEdit, action)
    const isVideo = element.filter((item) => {
      return item.element_id === "video_upload_button";
    });
    const { files } = this.state;
    // const type2 = files_type2[0];
    // const {isUploading} =parentState;
    const { getFieldValue, getFieldDecorator } = this.props.form;
    // if (param.content ) {
    //   this._edit.setContent(param.content);
    // }
    //

    // console.log(param);
    // console.log(approvalProcessProps);

    const imagesRender = (st) => {
      switch (st) {
        case 1:
          return null;
        case 2:
          return (
            <Image
              size={{ w: 322, h: 210 }}
              events={clipEvent(2)}
              src={files_type2[0]}
            />
          );
        case 3:
          return (
            <div>
              <Image
                size={{ w: 322, h: 210 }}
                events={clipEvent(3, 0)}
                src={files_type3[0]}
              />
              <Image
                size={{ w: 322, h: 210 }}
                events={clipEvent(3, 1)}
                src={files_type3[1]}
              />
              <Image
                size={{ w: 322, h: 210 }}
                events={clipEvent(3, 2)}
                src={files_type3[2]}
              />
            </div>
          );
        case 4:
          return (
            <Image
              size={{ w: 990, h: 558 }}
              events={clipEvent(4)}
              src={files_type4[0]}
            />
          );
        default:
          return null;
      }
    };

    const renderImg = (row, index) => {
      let imgSrc = "";
      if (row.url && !row.url.includes("http")) {
        imgSrc = `${CDN}/${row.url}`;
      } else {
        imgSrc = `${row.url}`;
      }
      return (
        <div className="upload-imgs">
          <img
            src={imgSrc}
            width="190"
            height="124"
            style={{ marginRight: "20px" }}
          />
          <div className="upload-imgs-modal">
            <Icon
              type="delete"
              onClick={() => {
                let files = files_type2;
                files.splice(index, 1);
                that.setState({ files_type2: files });
              }}
            />
          </div>
        </div>
      );
    };

    const reprint = getFieldValue("reprint");
    return (
      <div className="create-news" id={"create-news"}>
        <SearchHeader
          onBack={
            isEdit
              ? () => {
                  history.goBack();
                }
              : false
          }
          title={isPreview ? "查看资源" : isEdit ? "编辑资源" : "发布资源"}
          style={{ marginBottom: 30 }}
        />
        <Form
          className={autoSave ? "is-draft-autosaveing" : ""}
          onSubmit={handleSubmit}
        >
          <FormItem {...formItemLayout} label="资源标题">
            <InputTips
              max={70}
              container={container}
              text={getFieldValue("title")}
            >
              {getFieldDecorator("title", {
                initialValue: param.title,
                rules: [
                  {
                    max: 70,
                    message: "资源标题最长70个字",
                  },
                  {
                    required: true,
                    message: "请填写资源标题",
                  },
                ],
              })(
                <TextArea
                  disabled={isPreview}
                  placeholder="在资源列表、资源正文的标题处展示"
                  rows={2}
                  style={{ width: 700 }}
                />
              )}
            </InputTips>
          </FormItem>
          <FormItem {...formItemLayout} label="资源主栏目" required>
            <TreeSelect
              disabled={isPreview}
              treeData={newsColumn}
              style={{ width: 700 }}
              dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
              placeholder="资源将在指定栏目中展示"
              value={column_id || undefined}
              allowClear
              onChange={columnOnChange}
              // treeDefaultExpandAll
            />
          </FormItem>
          {!reprint && (
            <FormItem {...formItemLayout} label="资源副栏目">
              <TreeSelect
                disabled={isPreview}
                treeData={newsColumn}
                style={{ width: 700 }}
                value={sub_column}
                placeholder="资源将同时在指定副栏目中展示"
                treeCheckable={true}
                onChange={sub_columnOnChange}
                treeCheckStrictly={true}
              />
            </FormItem>
          )}

          <FormItem {...formItemLayout} label="展示方式">
            {getFieldDecorator("show_type", {
              initialValue: param.show_type,
              rules: [
                {
                  required: true,
                  message: "请选择展示方式",
                },
              ],
            })(
              <Select disabled={isPreview} style={{ width: 100 }}>
                <Option value={1}>文字</Option>
                <Option value={2}>图片</Option>
                <Option value={3}>图文</Option>
                <Option value={4}>视频</Option>
              </Select>
            )}
          </FormItem>
          {[2].includes(getFieldValue("show_type")) && (
            <FormItem {...formItemLayout} label=" " colon={false} required>
              {files_type2 && files_type2.length > 0
                ? files_type2.map((item, index) => {
                    return renderImg(item, index);
                  })
                : ""}
              <Image size={{ w: 1188, h: 674 }} events={clipEvent(2)} src={{}} />
            </FormItem>
          )}
          {[3].includes(getFieldValue("show_type")) && (
            <FormItem {...formItemLayout} label=" " colon={false} required>
              {files_type2 && files_type2.length > 0
                ? files_type2.map((item, index) => {
                    return renderImg(item, index);
                  })
                : ""}
              <Image size={{ w: 930, h: 523 }} events={clipEvent(2)} src={{}} />
            </FormItem>
          )}



          {[4].includes(getFieldValue("show_type")) && (
            <FormItem {...formItemLayout} label=" " colon={false} required>
              <div style={{ display: "flex", gap: "20px" }}>
                {getFieldDecorator("video_url", {
                  initialValue: param.video_url,
                })(
                  <VideoUpload
                    max={1}
                    cleanVideo={cleanVideo}
                    onCleanVideo={onCleanVideo}
                    video_title={getFieldValue("video_title")}
                    upType="no-compress-video"
                    children={this.state.fileNames}
                    uploadedList={this.state.files}
                    multiple={false}
                    // tipText="正在上传..."
                    onChange={(_, files) => {
                      if (files) {
                        console.log(files);
                        // this.updateState({ files: [{ ...files }] });
                        setVideoFiles([files]);
                      }
                    }}
                  >
                    <div className="ResourcePublish-upload-video-container">
                      <div className="ResourcePublish-upload-video">
                        <Icon
                          type="plus"
                          style={{
                            marginTop: "20px",
                            fontSize: "26px",
                            color: "#999",
                          }}
                        />
                        <div className="ResourcePublish-upload-video-mText">
                          将您的视频文件拖到此处
                        </div>
                        <div className="ResourcePublish-upload-video-sText">
                          视频大小：500M
                        </div>
                      </div>
                      <div className="ResourcePublish-upload-video-name">
                        视频文件
                      </div>
                    </div>
                  </VideoUpload>
                )}
                {/* {JSON.stringify(param.video_url)} */}
                <VideoList
                  dataSource={param.video_url || []}
                  onChange={(val) => {
                    // this.updateState({ files: val });
                    setVideoFiles(val);
                  }}
                />
              </div>
            </FormItem>
          )}

          {[1, 3].includes(getFieldValue("show_type")) && (
            <FormItem {...formItemLayout} label="文字内容" required>
              <div style={{ width: 700 }}>
                {getFieldDecorator(
                  "content",
                  {}
                )(<Input disabled={isPreview} style={{ display: "none" }} />)}
                <Editor
                  // width={1000}
                  // cdn
                  // upType={"news-image"}
                  // defaultValue={param.content || "<p></p>"}
                  extraToolbarConfig={["group-image"]}
                  onRef={(ref) => (that.editor = ref)}
                  onChange={(html) => this.setState({ content: html })}
                />
              </div>
            </FormItem>
          )}
          {/* <ShowUploadFileType
              updateState={this.updateState.bind(this)}
              data={
                // && preUploadType.file_name
                // (action === 'edit' || action === 'edit') &&
                !files.length && preUploadType ? preUploadType : files
              }
              loading={this.state.uploading}
              isDelete
              hiddenFileSize
            /> */}

          {/* <FormItem
            {...formItemLayout}
            label="是否转载"
            valuePropName="checked"
          >
            {getFieldDecorator("reprint", {
              initialValue: param.reprint || false,
              valuePropName: "checked",
            })(<Switch />)}
          </FormItem> */}
          {/* {reprint && (
            <FormItem {...formItemLayout} label="转载链接">
              {getFieldDecorator("link_url", {
                initialValue: param.link_url,
                rules: [],
              })(
                <Input
                  disabled={isPreview}
                  style={{ width: 700 }}
                  placeholder="转载链接 如https://www.aidangqun.com/"
                />
              )}
            </FormItem>
          )} */}
          {/* {!reprint && (
            <React.Fragment>
              <FormItem {...formItemLayout} label="文章短标题">
                <InputTips
                  max={25}
                  container={container}
                  text={getFieldValue("sub_title")}
                >
                  {getFieldDecorator("sub_title", {
                    initialValue: param.sub_title,
                    rules: [
                      {
                        max: 25,
                        message: "文章短标题最长25个字",
                      },
                      {
                        required: true,
                        message: "请填写文章短标题",
                      },
                    ],
                  })(
                    <Input
                      disabled={isPreview}
                      style={{ width: 700 }}
                      placeholder="在推荐资源、微信分享资源的标题处展示"
                    />
                  )}
                </InputTips>
              </FormItem>
              <FormItem {...formItemLayout} label="移动端呈现">
                {getFieldDecorator("show_type", {
                  initialValue: param.show_type,
                  rules: [
                    {
                      required: true,
                      message: "请选择移动端呈现方式",
                    },
                  ],
                })(
                  <Select disabled={isPreview} style={{ width: 100 }}>
                    <Option value={1}>文字</Option>
                    <Option value={2}>单图</Option>
                    <Option value={3}>三图</Option>
                    <Option value={4}>巨幅</Option>
                  </Select>
                )}
                <a
                  href="javascript:;"
                  style={{ marginLeft: 23 }}
                  onClick={show_preview_list}
                >
                  预览
                </a>
              </FormItem>
              <FormItem {...formItemLayout} label=" " colon={false}>
                {imagesRender(getFieldValue("show_type"))}
              </FormItem>
            </React.Fragment>
          )} */}

          {/* <FormItem {...formItemLayout} label="来源">
            {getFieldDecorator("source", {
              initialValue: param.source || "",
              rules: [
                {
                  max: 15,
                  message: "输入不能超过15个字",
                },
              ],
            })(
              <Input
                disabled={isPreview}
                style={{ width: 700 }}
                placeholder={"请输入"}
              />
            )}
          </FormItem> */}
          {/* {!reprint && (
            <React.Fragment>
              <FormItem {...formItemLayout} label="标签">
                {getFieldDecorator("keywords", {
                  initialValue: param.keywords,
                  rules: [
                    {
                      max: 32,
                      message: "输入不能超过32个字",
                    },
                  ],
                })(
                  <Input
                    disabled={isPreview}
                    style={{ width: 700 }}
                    placeholder="用于系统推荐联想，最多3个标签，请用逗号分开"
                  />
                )}
              </FormItem>

              <FormItem {...formItemLayout} label="内容摘要">
                <InputTips
                  max={100}
                  container={container}
                  text={getFieldValue("summary")}
                >
                  {getFieldDecorator("summary", {
                    initialValue: param.summary,
                  })(
                    <TextArea
                      disabled={isPreview}
                      placeholder="在微信分享资源时介绍中展示"
                      rows={4}
                      style={{ width: 700 }}
                    />
                  )}
                </InputTips>
              </FormItem>

               //todo:资源视频 element
              {isVideo.length > 0 && (
                <React.Fragment>
                  <FormItem {...formItemLayout} label="视频标题">
                    {getFieldDecorator("video_title", {
                      initialValue: param.video_title,
                      rules: [
                        {
                          max: 40,
                          message: "限制40个字",
                        },
                      ],
                    })(
                      <Input
                        placeholder="请输入视频标题(建议30字以内)"
                        style={{ width: 700 }}
                      />
                    )}
                  </FormItem>
                  <FormItem {...formItemLayout} label=" " colon={false}>
                    {getFieldDecorator("video_url", {
                      initialValue: param.video_url,
                    })(
                      <VideoUpload
                        max={1}
                        cleanVideo={cleanVideo}
                        onCleanVideo={onCleanVideo}
                        video_title={getFieldValue("video_title")}
                        upType="no-compress-video"
                        children={this.state.fileNames}
                        uploadedList={this.state.files}
                        multiple={false}
                        // tipText="正在上传..."
                        onChange={(_, files) => {
                          if (files) {
                            this.updateState({ files: [{ ...files }] });
                            setVideoFiles([{ ...files }]);
                          }
                        }}
                      />
                    )}
                    <ShowUploadFileType
                      updateState={this.updateState.bind(this)}
                      data={
                        // && preUploadType.file_name
                        // (action === 'edit' || action === 'edit') &&
                        !files.length && preUploadType ? preUploadType : files
                      }
                      loading={this.state.uploading}
                      isDelete
                      hiddenFileSize
                    />
                  </FormItem>
                </React.Fragment>
              )}
              <FormItem {...formItemLayout} label="审批类型">
                <div style={{ width: 700 }}>
                  <ApprovalProcess
                    preview={isPreview || [1, 2, 5].indexOf(param.status) != -1}
                    {...approvalProcessProps}
                  />
                </div>
              </FormItem>
            </React.Fragment>
          )} */}

          <FormItem {...formItemLayout} label=" " colon={false}>
            {!isPreview ? (
              <Button
                type="primary"
                htmlType="submit"
                loading={parentState.loading}
              >
                提交
              </Button>
            ) : (
              ""
            )}
            {(!isPreview && !isEdit) || action === "draft" ? (
              <Button
                onClick={hanlderSaveDraft}
                loading={parentState.loading}
                style={{ marginLeft: 25 }}
              >
                保存为草稿
              </Button>
            ) : (
              ""
            )}

            {/* <Button onClick={show_preview_article} style={{ marginLeft: 25 }}>
              手机预览
            </Button> */}

            <InvisibleSaveDraftBtn
              func={() => {
                hanlderSaveDraft(null, false, true);
              }}
            />
          </FormItem>
        </Form>

        {preview_list ? (
          <Preview
            src={"/ssr/preview-news-list"}
            data={preview_list_data}
            close={closePreview}
          />
        ) : null}
        {preview_article ? (
          <Preview
            src={"/ssr/news/ContentDetail/isview"}
            data={preview_article_data}
            close={closePreview}
          />
        ) : null}
        {/* <Preview src={"/ssr/preview-news-list"}/> */}
      </div>
    );
  }
}
export default View;
