import React, { Component } from 'react';
import { connect } from 'dva';
import './index.less';
import IndexView from './index-view';
import ModalView from './modal-view';
import { message } from 'antd';

class ColumnMgt extends Component {
  constructor(props) {
    super(props);
    this.state = {
      //表格loading状态
      tableLoading: false,
      //模态框可见状态
      modalVisible: false,
      //基于某个节点的新建子节点操作,暂存该节点的Id
      baseNode: null,
      //暂存当前栏目节点
      currentNode: null,
      //编辑某个节点时，暂存该节点的Name
      currentNodeName: null,

      img_url: '',//栏目图片

      is_default: false,//默认栏目

      //模态框按钮loading状态
      modalLoading: false,
      //是否为编辑模态框，默认为否（新增模态框）
      editModal: false
    }
  }
  componentDidMount() {
    // console.log(this.props);
    const _this = this;
    this.setState({
      tableLoading: true
    });
    const { dispatch } = this.props;
    dispatch({
      type: 'WaterControlBureauColumnMgt/fetchColumn',
      payload: {}
    }).then(
      () => {
        
      }
    ).catch(
      (error) => {
        console.error(error);
      }
    ).finally(
      () => {
        _this.setState({
          tableLoading: false
        });
      }
    );
  }
  render() {
    const _this = this;
    const { dispatch, WaterControlBureauColumnMgt, history } = this.props;
    const { dataSource } = WaterControlBureauColumnMgt;
    //移动资源栏目
    const moveColumn = (id, changeId) => {
      this.setState({
        tableLoading: true
      })
      dispatch({
        type: 'WaterControlBureauColumnMgt/moveColumn',
        payload: {
          data: {
            column_id: id,
            opposite_column_id: changeId
          }
        }
      }).then(
        () => {
            dispatch({
              type: 'WaterControlBureauColumnMgt/fetchColumn',
              payload: {}
            })
        }
      ).catch(
        (error) => {
          console.error(error);
        }
      ).finally(
        () => {
          _this.setState({
            tableLoading: false
          })
        }
      );
    }
    //新增资源栏目
    const addNewsColumn = (parentColumn) => {
      this.setState({
        modalVisible: true
      });
      if (parentColumn) {
        this.setState({
          baseNode: String(parentColumn.column_id) || '0'
        });
        // console.log('创建一个子节点', parentColumn);
      } else {
        this.setState({
          baseNode: null
        });
        // console.log('创建一个根节点');
      }
    }
    //删除资源栏目
    const deleteNewsColumn = (record) => {
      const id = record.column_id;
      if (id) {
        this.setState({
          tableLoading: true
        })
        // console.log('删除资源', id);
        dispatch({
          type: 'WaterControlBureauColumnMgt/deleteColumn',
          payload: {
            column_id: id
          }
        
        }).then(
          () => {
            dispatch({
              type: 'WaterControlBureauColumnMgt/fetchColumn',
              payload: {}
            })
          }
        ).catch(
          (error) => {
            console.error(error);
          }
        ).finally(
          () => {
            _this.setState({
              tableLoading: false
            })
          }
        )
      } else {
        message.error('操作失败');
      }
    }
    const editNewsColumn = (record) => {
      // console.log('编辑栏目', record);
      this.setState({
        modalVisible: true,
        editModal: true,
        // 编辑时，上级栏目默认为当前编辑节点的上级栏目
        baseNode: String(record.top_column_id || 0),
        currentNodeName: record.column_name || '',
        currentNode: record || null,
        is_default: !!record.is_default,
        img_url: record.background_url[0] || ''
      });
    }
    //跳转资源管理
    const managementNewsColumn = (record) => {
      //跳转至路由，并传递id栏目id参数
      history.push('/WaterControlBureau/ResourceMgt', { record });
    }
    const indexViewProps = {
      title: '资源栏目管理',
      dataSource,
      tableLoading: this.state.tableLoading,
      moveHandler(record, changeRecord) {
        moveColumn(record.column_id, changeRecord.column_id);
      },
      addHandler(parentColumn) {
        addNewsColumn(parentColumn);
      },
      editHandler(record) {
        editNewsColumn(record);
      },
      deleteHandler(record) {
        deleteNewsColumn(record);
      },
      managementHandler(record) {
        managementNewsColumn(record);
      }
    }
    const modalViewProps = {
      currentNode: this.state.currentNode,
      editModal: this.state.editModal,
      modalVisible: this.state.modalVisible,
      modalLoading: this.state.modalLoading,
      treeData: dataSource,
      baseNode: this.state.baseNode,
      currentNodeName: this.state.currentNodeName,
      img_url:this.state.img_url,
      is_default: this.state.is_default,
      closeHandler() {
        // console.log('关闭');
        _this.setState({
          editModal: false,
          modalVisible: false,
          baseNode: null,
          currentNodeName: null,
          modalLoading: false,
          img_url:'',
          is_default:false
        });
      },
      submitHandler(values, editModal, currentNode) {
        console.log(values)
        // console.log(values)
        _this.setState({
          modalLoading: true
        });
        values.column_level = values.top_column_id ? 2 : 1
        const column_status = Number(!!values.column_status);//false=>0 true=>1
        values.background_url =  values.background_url ? [values.background_url] : []
        // console.log("is_default", is_default)
        // dispatch({
        //   type: 'WaterControlBureauColumnMgt/uploadImage',
        //   payload: {
        //     upfile: values.img_url,
        //     upType: 'image'
        //   }
        // }).then(img_url=>{


        if (!editModal) {
          //新增提交
          dispatch({
            type: 'WaterControlBureauColumnMgt/addColumn',
            payload: {
              data: {...values, column_status  }
            }
          }).then(
            () => {
              message.success('操作成功');
              _this.setState({
                editModal: false,
                tableLoading: true,
                modalVisible: false,
                baseNode: null,
                img_url:'',
                is_default:false,
                currentNodeName: '',
                currentNode: null,
              });
              dispatch({
                type: 'WaterControlBureauColumnMgt/fetchColumn',
                payload: {}
              }).then().catch().finally()
            }
          ).catch().finally(
            () => {
              // console.log('这里最后被执行', _this);
              _this.setState({
                tableLoading: false,
                modalLoading: false
              })
            }
          )
        } else {
          if (!values.column_id && values.column_id !== 0) {
            values.column_id = currentNode.column_id;
          }
          //编辑提交
          dispatch({
            type: 'WaterControlBureauColumnMgt/editColumn',
            payload: {
              data: {...values, column_status }
            }
          }).then(
            () => {
              message.success('操作成功');
              _this.setState({
                editModal: false,
                tableLoading: true,
                modalVisible: false,
                baseNode: null,
                img_url:'',
                is_default:false,
                currentNodeName: '',
                currentNode: null,
              });
              dispatch({
                type: 'WaterControlBureauColumnMgt/fetchColumn',
                payload: {}
              }).then().catch().finally();
            }
          ).catch().finally(
            () => {
              // console.log('这里最后被执行', _this);
              _this.setState({
                tableLoading: false,
                modalLoading: false
              })
            }
          )
        }



      // }).catch(()=>{}).finally(()=>{});



      }
    };
    return (
      <div className="news-column">
        <IndexView {...indexViewProps} />
        <ModalView {...modalViewProps} />
      </div>
    )
  }
}

const mapStateToProps = ({ WaterControlBureauColumnMgt }) => ({ WaterControlBureauColumnMgt });

export default connect(mapStateToProps)(ColumnMgt);
