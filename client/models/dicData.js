import { getDataDictionary, getDataDictionarys } from 'client/apis/active-organization';

const getDefaultData = (init, num = 15) => {
  const data = {};
  const dataStatus = {};
  for (let i = 0; i < num; i++) {
    const item = init + i + 1;
    data[item] = [];
    dataStatus[item] = { loading: false, queue: [] };
  }
  return { data, dataStatus };
};

const defaultData = getDefaultData(1000, 15);

// 字典model
const dicDataModel = {
  namespace: 'dicData',
  state: {
    // 字典数据 {1011: [], 1012: [], 1013: []}
    data: defaultData.data,
    // 用于记录对应字典的请求状态, 请求状态及任务队列 {1011: {loading: false, queue: []}}
    dataStatus: defaultData.dataStatus,
  },
  reducers: {
    updateData(state, { payload }) {
      return { ...state, data: { ...payload } };
    },
    updateStatus(state, { payload }) {
      return { ...state, dataStatus: { ...payload } };
    },
  },
  effects: {
    // 根据code初始化字典数据
    *initDicDataByCode({ payload }, { put, call, select }) {
      // 参数 code: 字典code, callback回调函数, hasChild是否含有子集
      const { code, callback, hasChild = false } = payload;
      const { data: dataList, dataStatus } = yield select(
        (state) => state.dicData,
      );
      const data = dataList[code] || [];
      // 一、存在则直接返回对应数据
      if (data.length > 0) {
        if (callback) callback(data);
        return;
      }

      // 二、不存在数据则远程请求
      try {
        // 2.1 如果正在请求数据，且存在回调函数，则push任务队列中
        if (!dataStatus[code]) {
          dataStatus[code] = { loading: false, queue: [] };
        }
        const currentStatus = dataStatus[code];
        if (currentStatus.loading) {
          if (callback) currentStatus.queue.push(callback);
          return;
        }

        // 2.2 非loading状态，则更新状态
        const newDataStatus = { ...dataStatus };

        newDataStatus[code].loading = true;
        yield put({ type: 'updateStatus', payload: newDataStatus });

        const metheds = hasChild ? getDataDictionarys : getDataDictionary;
        // 2.3 请求数据，查询最新数据
        const list = (yield call(metheds, { code })).data.data;
        const { data: dataList1, dataStatus: dataStatus1 } = yield select(
          (state) => state.dicData,
        );

        const newDataList1 = { ...dataList1 };
        const newDataStatus1 = { ...dataStatus1 };
        newDataList1[code] = list;
        newDataStatus1[code].loading = false;
        newDataStatus1[code].queue.map((cb) => {
          cb(list);
          return "";
        });
        newDataStatus1[code].queue = [];

        if (callback) callback(list);
        yield put({ type: 'updateData', payload: newDataList1 });
        yield put({ type: 'updateStatus', payload: newDataStatus1 });
      } catch (error) {
        console.error('字典数据加载失败', error);
        if (callback) callback(false);
        // 三、请求失败后更新状态
        const { dataStatus: dataStatus1 } = yield select(
          (state) => state.dicData,
        );
        if (!dataStatus1[code]) {
          dataStatus1[code] = { loading: false, queue: [] };
        }
        const newDataStatus = { ...dataStatus1 };
        newDataStatus[code].loading = false;
        yield put({ type: 'updateStatus', payload: newDataStatus });
      }
    },
  },
};

export default dicDataModel;
