.case-library {
  &-content {
    margin-top: 24px;

    &-tabs {
      padding: 0 31px 27px;
      border-bottom: 1px solid #E4E4E4;

      &-item {
        margin-right: 60px;
        display: inline-block;
        position: relative;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #999999;
        cursor: pointer;
      }

      .active {
        font-weight: bold;
        color: #FF4D4F;

        &::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: -6px;
          width: 100%;
          height: 3px;
          background: #FF4D4F;
          border-radius: 2px;
        }
      }
    }

    &-search {
      padding: 31px;

      .ant-select-selection--multiple>ul>li,
      .ant-select-selection--multiple .ant-select-selection__rendered>ul>li {
        margin-top: 5px;
      }

      &-org {
        margin-bottom: 32px;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #4A4A4A;

        .ant-btn-link {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #359AF7;
        }
      }

      &-form {
        margin-bottom: 20px;

        .ant-select-selection {
          min-width: 200px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-select-selection__rendered {
          line-height: 36px;
        }

        .ant-input {
          width: 600px;
          height: 36px;
          border-radius: 4px;
          font-size: 16px;
        }

        .ant-btn-primary {
          height: 36px;
          background: #F46E65;
          border-radius: 4px;
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
        }
      }
    }

    &-list {
      padding: 0 31px;
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .case-item {
        flex: 0 1 18%;
        padding: 16px;
        background: #F6F8F9;
        border-radius: 8px;
        cursor: pointer;

        &-img {
          width: 268px;
          height: 156px;
          border-radius: 4px;
        }

        .case-img {
          object-fit: cover;
        }

        .case-default {
          padding: 43px 25px 57px;
          background: url("../../image/case-default.png") no-repeat center/contain;
          text-align: center;
          font-size: 18px;
          font-family: PingFang SC-Heavy, PingFang SC;
          font-weight: 800;
          color: #FFFFFF;
          word-break: break-all;
        }

        &-info {
          &-title {
            min-height: 56px;
            margin: 12px 0 6px;
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis; //文本溢出显示省略号
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            font-size: 18px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #000000;
          }

          &-tags {
            >span {
              padding: 5px;
              margin-right: 8px;
              background: #EEEEEE;
              border-radius: 10px;
              font-size: 12px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #666666;
            }
          }
        }
      }
    }

    .ant-pagination {
      margin: 21px 0;
      padding: 0 31px;
      text-align: right;

      .ant-select-selection {
        border-radius: 4px;
      }
    }
  }
}