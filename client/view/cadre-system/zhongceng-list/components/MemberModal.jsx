import React, { PureComponent } from 'react';
import { Modal, Row, Col, Form, Button, Checkbox, Select, Input, message } from 'antd';
import { getExtendInfo, commonJobUrl } from '../../../apis/party-organization';
import PersonModal from 'components/person-selector/sub-components/person-modal';

const { Option, OptGroup } = Select;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

/**
 * 成员选择弹窗
 */
class MemberModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      personVisible: false,
      partyPosts: [], // 党内职务
      memberData: {},
      grade_name: '',
      position_name: '',
      check: [],
    };
    this.personModal = null;
    this.onBtnClick = this.onBtnClick.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.onPersonChange = this.onPersonChange.bind(this);
    this.onSubmit = this.onSubmit.bind(this);
  }
  componentWillReceiveProps(nextProps) {
    const { defaultValue } = nextProps;
    if (defaultValue !== this.props.defaultValue) {
      const {
        user_id,
        user_name,
        grade_name,
        position_name,
        is_full_time_party_cadres,
        is_full_time_discipline_inspection_cadres,
      } = defaultValue;

      const check = [];
      if (Number(is_full_time_party_cadres) === 1) {
        check.push("is_full_time_party_cadres");
      }
      if (Number(is_full_time_discipline_inspection_cadres) === 1) {
        check.push("is_full_time_discipline_inspection_cadres");
      }

      this.setState({
        user_id,
        user_name,
        grade_name,
        position_name,
        check,
        type: 1,
      });
    }
  }
  componentDidMount() {
    this.getJobType();
  }
  async getJobType() {
    const { filter } = this.props;
    const { data, code, status } = (await commonJobUrl({ type: 1 }, filter)).data;
    if (code === 0 && status === 200) {
      this.setState({ partyPosts: data });
    } else {
      message.error('请求失败，请稍后再试！');
    }
  }
  onBtnClick() {
    this.setState({ personVisible: true });
  }
  hideModal() {
    this.setState({ personVisible: false });
  }
  async onPersonChange(data) {
    if (data && Array.isArray(data)) {
      data.map((item) => {
        if (!item.user_name) {
          item.user_name = item.name;
        }
      });
    }
    const { user_name, user_id } = data[0] || {};
    this.setState({ user_name, user_id });
  }
  onValueChange(value, props) {
    this.setState({ [props]: value });
  }
  setOptGroup(param) {
    const arr = [];
    for (const [key, value] of Object.entries(param)) {
      arr.push(
        <OptGroup label={key} key={key}>
          {value.map((item, i) => {
            return (
              <Option value={item} key={i}>
                {item}
              </Option>
            );
          })}
        </OptGroup>
      );
    }
    return arr;
  }
  // 提交数据
  onSubmit(e) {
    e.preventDefault();
    const { onSure } = this.props;
    const { user_name, user_id, position_name, grade_name, check, defaultValue } = this.state;
    if (!user_id) {
      message.error('请选择人员');
      return;
    }
    if (!position_name) {
      message.error('请选择党内职务');
      return;
    }

    const param = {
      user_id,
      user_name,
      grade_name,
      position_name,
      is_full_time_party_cadres: check.indexOf('is_full_time_party_cadres') === -1 ? 2 : 1,
      is_full_time_discipline_inspection_cadres:
      check.indexOf('is_full_time_discipline_inspection_cadres') === -1 ? 2 : 1,
      type: 1, // 暂不清楚作用
    };
    onSure && onSure(param);
  }
  render() {
    // operation_type   1 添加 2 编辑 3 详情
    const { visible, onCancel, operation_type, defaultValue, title } = this.props;
    const { personVisible, partyPosts, user_name, grade_name, position_name, check } = this.state;
    return (
      <React.Fragment>
        <Modal
          className='modal-orgCom'
          width={600}
          title={!defaultValue.user_id ? `添加${title}` :  `编辑${title}`}
          visible={visible}
          footer={null}
          onCancel={() => onCancel()}>
          <Form {...formItemLayout}>
            <Form.Item label='人员姓名'>
              <span>{user_name}</span>
              {
                !(operation_type === 2 && defaultValue.user_name) && (
                  <Button type='link' onClick={this.onBtnClick}>
                    选择人员
                  </Button>
                )
              }
              {/* user_name */}
            </Form.Item>
            <Form.Item label='行政职务'>
              <Input
                value={grade_name}
                onChange={(e) => this.onValueChange(e.target.value, 'grade_name')}
                placeholder='请输入行政职务'
                style={{ width: 300 }}
              />
            </Form.Item>
            <Form.Item label='党内职务' required>
              <Select
                value={position_name}
                style={{ width: 300 }}
                placeholder='请选择党内职务'
                onChange={(e) => this.onValueChange(e, 'position_name')}>
                {this.setOptGroup(partyPosts)}
              </Select>
            </Form.Item>
            {/* <Form.Item label=' ' colon={false}>
              <Checkbox.Group value={check} style={{ width: '100%' }} onChange={(e) => this.onValueChange(e, 'check')}>
                <Checkbox value='is_full_time_party_cadres'>专职党务干部</Checkbox>
                <Checkbox value='is_full_time_discipline_inspection_cadres'>专职纪检干部</Checkbox>
              </Checkbox.Group>
            </Form.Item> */}
            {operation_type !== 3 && (
              <Form.Item label=' ' colon={false} className='period-wrapper_btn'>
                <Button type='primary' onClick={this.onSubmit}>
                  提交
                </Button>
                <Button onClick={() => onCancel()} style={{ marginLeft: 40 }}>
                  取消
                </Button>
              </Form.Item>
            )}
          </Form>
        </Modal>
        <PersonModal
          title='选择人员'
          visible={personVisible}
          radio={true}
          ref={(ref) => (this.personModal = ref)}
          hideModal={this.hideModal}
          onChange={this.onPersonChange}
        />
      </React.Fragment>
    );
  }
}

export default Form.create()(MemberModal);
