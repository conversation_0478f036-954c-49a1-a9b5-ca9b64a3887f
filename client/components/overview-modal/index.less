.overview-modal {
  .overview-modal-close {
    color: #fff;
    position: absolute;
    right: -40px;
    top: -40px;
    z-index: 10;
    font-size: 40px;
    cursor: pointer;
  }
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, .5);
  z-index: 90000;
  -webkit-transition: all .15s;
  -moz-transition: all .15s;
  -ms-transition: all .15s;
  -o-transition: all .15s;
  transition: all .15s;
  display: flex;
  align-items: center;
  justify-content: center;
  .overview-content {
    box-sizing: content-box;
    width: 337px;
    height: 575px;
    background-color: #fff;
    border: 1px solid #000; // overflow: auto;
    position: relative;
    .content-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
    }
  }
}
