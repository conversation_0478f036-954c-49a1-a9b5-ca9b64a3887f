.video-tip-text-container{
  color: #666;
}
.video-upload-container {
  input {
    font-size: 0;
    cursor: pointer;
  }
  input::-webkit-file-upload-button {
    cursor: pointer;
  }

  &:hover {
    span{ 
      cursor: pointer;
      // color: #fff;
      // background: rgba(244, 110, 101, 1);
    }
    button {
      cursor: pointer;
      // color: #fff;
      // background: rgba(244, 110, 101, 1);
    }
  }
  display: inline-block;
  position: relative;
  overflow: hidden;
  // color: #359AF7;
  // background: rgba(244, 110, 101, 1);
  border-radius: 3px;
  // color: #fff;
  // width: 90px;
  // height: 36px;
  // line-height: 36px;
  text-align: center;
  font-size: 14px;
  .ant-btn-link{
    padding: 0;
  }
  .file-hidden-trigger {
    opacity: 0;
    position: absolute; // left: -100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    padding: 0;
    cursor: pointer;
  }
}