import { findByUser, initiateEvaluation } from "client/apis/cadre-portrait";
import SelectInput from "./SelectInput";

export default Ant.Form.create()(function ModalForm({ form }) {
  const [option, setOption] = React.useState([]);

  const { getFieldDecorator, setFieldsValue } = form;

  const findByUserInfo = (userName) => {
    findByUser({ user_name: userName }).then((res) => {
      if (res.data.code === 0) {
        setOption(res.data.data.slice(0, 10));
      } else {
        Ant.message.error(res.data.message);
      }
    });
  };

  const onSelect = ({ user_name, job, user_id }) => {
    setFieldsValue({
      job,
      user_id,
    });
  };

  return (
    <Ant.Form>
      <Ant.Form.Item label="姓名">
        {getFieldDecorator("user_name", {
          rules: [{ required: true, message: "请输入姓名" }],
        })(
          <SelectInput
            placeholder="请输入"
            onSearch={findByUserInfo}
            onSelect={onSelect}
            option={option}
          />
        )}
      </Ant.Form.Item>
      <Ant.Form.Item label="职务">
        {getFieldDecorator("job", {
          rules: [{ required: true, message: "请输入职务" }],
        })(<Ant.Input />)}
      </Ant.Form.Item>
      <div style={{ display: "none" }}>
        <Ant.Form.Item label="user_id">
          {getFieldDecorator("user_id", {
            rules: [{ required: true, message: "请输入" }],
          })(<Ant.Input />)}
        </Ant.Form.Item>
      </div>
      <Ant.Form.Item label="类别">
        {getFieldDecorator("user_type", {
          rules: [{ required: true, message: "请选择类别" }],
        })(
          <Ant.Select defaultValue="1">
            <Ant.Select.Option value="1">县管正职</Ant.Select.Option>
            <Ant.Select.Option value="2">县管副职</Ant.Select.Option>
            <Ant.Select.Option value="3">改非干部</Ant.Select.Option>
            <Ant.Select.Option value="4">新提拔中层干部</Ant.Select.Option>
          </Ant.Select>
        )}
      </Ant.Form.Item>
    </Ant.Form>
  );
});
