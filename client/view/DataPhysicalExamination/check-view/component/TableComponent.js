import React, { useState } from "react";
import { Table } from "antd";

export default function index({
  tableData,
  tabKey,
  tableLoading,
  pageNum,
  setPageNum,
  onChangeDrawerData,
}) {
  const { dataSource, total } = tableData;
  // 按组织统计
  const columns = [
    {
      title: "序号",
      key: "index",
      align: "center",
      render(_, __, index) {
        return index + 1;
      },
    },
    {
      title: "组织名称",
      dataIndex: "org_name",
    },
    {
      title: "组织书记",
      dataIndex: "org_secretary_name",
    },
    {
      title: "数量（项）",
      dataIndex: "num",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "address",
      align: "center",
      render(_, record) {
        record.abnormalType = 1; // 组织统计、党员统计
        return (
          <a
            onClick={() => {
              onChangeDrawerData(record);
            }}
          >
            详情
          </a>
        );
      },
    },
  ];
  // 按人员统计
  const columns1 = [
    {
      title: "序号",
      key: "index",
      align: "center",
      render(_, __, index) {
        return index + 1;
      },
    },
    {
      title: "党员姓名",
      dataIndex: "user_name",
    },
    {
      title: "手机号",
      dataIndex: "phone",
    },
    {
      title: "数量（项）",
      dataIndex: "num",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "address",
      align: "center",
      key: "address",
      render(_, record) {
        record.abnormalType = 1; // 组织统计、党员统计
        return (
          <a
            onClick={() => {
              onChangeDrawerData(record);
            }}
          >
            详情
          </a>
        );
      },
    },
  ];
  // 按异常统计
  const columns3 = [
    {
      title: "序号",
      key: "index",
      align: "center",
      render(_, __, index) {
        return index + 1;
      },
    },
    {
      title: "异常项",
      dataIndex: "examine_name",
    },
    {
      title: "异常类别",
      dataIndex: "examine_type_name",
    },
    {
      title: "数量（项）",
      align: "center",
      dataIndex: "num",
      render(value) {
        return <i style={{ color: "#FF4D4F", fontSize: "14px" }}>{value}</i>;
      },
    },
    {
      title: "操作",
      align: "center",
      dataIndex: "operation",
      key: "operation",
      render(text, record) {
        record.abnormalType = 2; // 异常项统计
        return (
          <a
            onClick={() => {
              onChangeDrawerData(record);
            }}
          >
            详情
          </a>
        );
      },
    },
  ];

  /* const dataSource = [
    {
      examine_id: 1,
      org_id: 1,
      user_id: 1,
      age: "12",
      type: "哈哈哈",
      number: 100,
    },
    {
      examine_id: 2,
      org_id: 2,
      user_id: 2,
      age: "12",
      type: "哈哈哈",
      number: 100,
    },
    {
      examine_id: 3,
      org_id: 3,
      user_id: 3,
      age: "12",
      type: "哈哈哈",
      number: 100,
    },
  ]; */
  /**
   * 分页数据修改
   *
   * @param {*} current
   * @param {*} page
   */
  const onChange = (page) => {
    setPageNum(page);
  };
  const _columns = tabKey === 3 ? columns3 : tabKey === 0 ? columns : columns1;
  const tableProps = {
    rowKey: tabKey === 3 ? "examine_id" : tabKey === 0 ? "org_id" : "user_id",
    columns: _columns,
    dataSource,
    loading: tableLoading,
    hideOnSinglePage: true,
    pagination: {
      total,
      pageSize: 10,
      showTotal: (total, range) =>
        `共${total}条记录，${Math.ceil(total / 10)}页`,
      showQuickJumper: true,
      onChange,
    },
  };
  return (
    <div className="abnormal-statistics">
      <Table {...tableProps}></Table>
    </div>
  );
}
