.table-module {
  .table-module-wrapper {
    display: flex;
    .type-wrapper {
      background-color: #fafafa;
      width: 108px;
      flex-grow: 0;
      flex-shrink: 0;
      border: 1px solid #E4E4E4;
      border-right: none;
      position: relative;
      & > div {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
      }
    }
    .table-wrapper {
      //min-height: 300px;
      flex-grow: 1;
    }
  }
  margin-bottom: 30px;
  &:last-child {
    margin-bottom: 0;
  }
}