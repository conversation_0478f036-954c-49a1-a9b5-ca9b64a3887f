.code-item {
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    line-height: 21px;
    &::before {
      content: "";
      margin-right: 8px;
      display: inline-block;
      width: 6px;
      height: 16px;
      background: #f46e65;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
    }
  }
  .code-container {
    padding-top: 8px;
    width: 100%;
    display: flex;
    .table-box {
      margin: 0px 20px 40px 0;
      flex: 1;
      .ant-switch-checked {
        background-color: #1890ff;
      }
    }
    .code-box {
      display: flex;
      flex-direction: column;
      width: 350px;
      height: 421px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      opacity: 1;
      border: 1px solid #f46e65;
      .img-box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        img {
          width: 310px;
          height: 310px;
        }
      }
      .code-name {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 350px;
        height: 54px;
        background: #f46e65;
        border-radius: 0px 0px 4px 4px;
        opacity: 1;
        border: 1px solid #f46e65;
        .name {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          color: #ffffff;
        }
        img {
          margin: 0px 6px 0px 24px;
          width: 18px;
          height: 18px;
        }
        .download-label {
          display: flex;
          align-items: center;
          font-size: 14px;
          line-height: 18px;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          color: #ffffff;
          cursor: pointer;
          &::before {
            content: "";
            display: inline-block;
            width: 18px;
            height: 18px;
            background: url("../images/download.png") no-repeat center center /
              cover;
            margin-right: 6px;
            margin-left: 24px;
          }
        }
      }
    }
  }
}
