import {
    testActivity,
    fetchColumn,
    fetchWorkflowList,
    fetchWorkflowItemInfo,
    insertActivity,
    editActivity,
    fetchEditColumn,
    deleteActivity,
    workflowAdd
} from '../../apis/activity';

//默认侧边栏表单数据
const defaultFormData = {
    'name': '',
    'hid': 0,//0不隐藏/1隐藏
    'jurisdiction': 1,//浏览权限，1：开放，2：平台用户，3：仅自己...
    'home_page': 1,//栏目首页，1：频道页，2：列表页'
    'title': '',
    'keyword': '',
    'description': '',
    'parent_column_id': 0,
    'workflow_id': -999,//'int|可选|审批类型id',默认选择不需要审批是-999
    'workflow_name': '自定义',
    'template': {
        'page_template': -999,//'long|必填|频道页模板id'
        'list_template': -999,//'long|必填|列表模板id'
        'doc_template': -999,//'long|必填|文章模板id'
        'page_template_app': -999,//'long|必填|频道页模板id（app）'
        'list_template_app': -999,//'long|必填|列表模板id（app）'
        'doc_template_app': -999//'long|必填|文章模板id（app）'
    },
    must_approve: 1 //标记活动发布是否需要审批：1为选中，0为反选
};

export default {
    namespace: 'activityList',

    state: {
        //当前添加子节点时的父节点id
        // parent_column_id: 0,
        //部门信息应该是从登陆时获取，并保存在本地，目前阶段暂时写死
        department_name: 'root',
        department_id: -999,//开发部

        // title: '活动栏目管理',
        title: '互动栏目管理',
        //页面主体表格Mock数据
        dataList: [],
        //当编辑时获取当前栏目信息
        editFormData: {},
        //页面侧边栏浮层表单数据
        formData: JSON.parse(JSON.stringify(defaultFormData)),
        //侧边栏控制符，控制是否折叠
        collapsed: true,
        isHideWrap: false,
        modalTitle: '',

        //工作流程列表，获取这个列表时，请求头中_type:2（组织），
        workflowList: [],
        //审批人列表
        approveList: [],
        //抄送人列表
        copyToList: [],
        //当前工作流程id
        workflow_id: -999,
        //当前工作流名称
        workflow_name: '自定义',

        //以下为分页表格所需要的参数
        //当前页，默认为1
        pageNum: 1,
        //每页数据条数，默认为10
        pageSize: 10,
        //总数据条数
        total: 0
    },

    effects: {
        //测试异步请求
        * testActivity({ payload }, { put }) {
            yield testActivity();
        },
        //获取加载编辑栏目的信息
        * fetchEditColumn({ payload }, { put }) {
            let { activityId } = payload;
            let response = yield fetchEditColumn(activityId);
            let { status, data: body } = response;
            if (status === 200) {
                let { code, message: messageInfo, status, data } = body;
                if (code === 0 && messageInfo === 'success' && status === 200) {
                    if (!data.template) {
                        data.template = {
                            page_template: -999,
                            list_template: -999,
                            doc_template: -999,
                            page_template_app: -999,
                            list_template_app: -999,
                            doc_template_app: -999
                        }
                    }
                    yield put({ type: 'save', payload: { formData: data } });
                    return data;
                }
            }
        },
        //获取工作流程
        * fetchWorkflowList({ payload }, { put }) {
            let response = yield fetchWorkflowList();
            let { status, data: body } = response;
            if (status === 200) {
                //请求成功取到了数据
                let { code, message, data, pageNum, pageSize, pages, total } = body;
                // console.log(data);
                if (code === 0 && message === 'success') {
                    yield put({ type: 'loadWorkflowList', payload: { workflowList: data } });
                    return data;
                }
            }
        },
        //基于已有流程建立新流程
        * workflowAdd({ payload }, { put }) {
            let { postObject } = payload;
            // let {users, cc} = postObject;
            let response = yield workflowAdd(postObject);
            let { status, data: body } = response;
            if (status === 200) {
                let { code, data, message } = body;
                if (code === 0) {
                    // let {name, workflow_id} = data;
                    return data;
                } else {
                    throw new Error( message ? message : '新建审批类型失败');
                }
            }
        },
        //根据workflowId,加载某审批类型下的用户列表和抄送列表信息
        * fetchWorkflowItemInfo({ payload }, { put }) {
            let { workflowId } = payload;
            if (workflowId && workflowId !== -999) {
                let response = yield fetchWorkflowItemInfo(workflowId);
                let { status, data: body } = response;
                if (status === 200) {
                    let { code, message, data } = body;
                    if (code === 0 && message === 'success') {
                        const { cc, users } = data,
                            copyToList = cc || [],
                            approveList = users || [];
                        // console.log('抄送列表', copyToList);
                        // console.log('用户列表', approveList);
                        yield put({ type: 'loadWorkflowItemInfo', payload: { copyToList, approveList } });
                        return data;
                    }
                }
            } else {
                yield put({ type: 'loadWorkflowItemInfo', payload: { copyToList: [], approveList: [] } });
            }
        },
        //编辑栏目信息
        * editActivity({ payload }, { put }) {
            let { postObject } = payload;
            let response = yield editActivity(postObject);
            let { status, data: body } = response;
            if (status === 200) {
                let { code, data, message } = body;
                if (code === 0) {
                    if (!data) {
                        throw new Error(message);
                    }
                } else {
                    throw new Error(message);
                }
            }
        },
        //新增栏目信息
        * insertActivity({ payload }, { put, call }) {
            let { postObject } = payload;
            let response = yield insertActivity(postObject);
            let { status, data: body } = response;
            if (status === 200) {
                let { code, data, message } = body;
                if (code === 0) {
                    if (!data) {
                        throw new Error('添加栏目失败');
                    }
                } else {
                    throw new Error(message);
                }
            }
        },
        * fetchColumn({ payload }, { put }) {
            let { pageNum, pageSize } = payload;
            let response = yield fetchColumn(pageNum, pageSize);
            let { status, data: body } = response;
            if (status === 200) {
                //请求成功获取到了数据
                let { code, message, data, pageNum, pageSize, pages, total } = body;
                if (code === 0 && message === 'success') {
                    yield put({ type: 'save', payload: { dataList: data, pageSize, total } });
                }
            }
        },
        //删除活动分类
        * deleteActivity({ payload }, { put }) {
            let { activityId } = payload;
            let response = yield deleteActivity(activityId);
            let { status, data: body } = response;
            if (status === 200) {
                let { code, data, message } = body;
                if (code === 0) {
                    if (!data) {
                        throw new Error('删除栏目失败');
                    }
                } else {
                    throw new Error(message);
                }
                //axios 设置了统一的错误信息提示
                // else if (code === 1321) {
                //     throw new Error('该栏目下有活动，不能删除');
                // }
                // else {
                //     throw new Error('删除栏目失败');
                // }
            }
        }
    },

    reducers: {
        //编辑完成之后将默认表单数据清空
        resetFormData(state, { payload }) {
            return { ...state, formData: defaultFormData, approveList: [], copyToList: [] };
        },
        setParentColumnId(state, { payload }) {
            return { ...state, ...payload };
        },
        toggleAsideViewType(state, { payload }) {
            return { ...state, ...payload };
        },
        loadWorkflowItemInfo(state, { payload }) {
            return { ...state, ...payload };
        },
        //加载审批流程列表
        loadWorkflowList(state, { payload }) {
            return { ...state, ...payload };
        },
        //切换页码改变页
        pageChange(state, { payload }) {
            return { ...state, ...payload };
        },
        toggleCollapsed(state) {
            //如果当前侧边栏为显示状态，在收起时将表单状态重置
            return { ...state, collapsed: !state.collapsed };
        },
        toggleHideWrap(state) {
            return { ...state, isHideWrap: !state.isHideWrap };
        },
        //改变侧边栏标题文字
        toggleModalTitle(state, params) {
            let { title } = params;
            return { ...state, modalTitle: title || '' };
        },
        save(state, { payload }) {
            return { ...state, ...payload };
        }
    }
}
