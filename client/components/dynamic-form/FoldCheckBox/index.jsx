import React, { PureComponent } from 'react';
import { Button, Checkbox, Icon } from "antd";
import "./styles.less";

/**
 * 支持折叠的checkbox
 */
export default class FoldCheckBox extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      unfold: true, // 折叠状态
    };
    this.handleToggle = this.handleToggle.bind(this);
    this.unfoldStyle = {
      maxHeight: 96,
      overflow: "hidden",
    };
  }
  handleToggle() {
    this.setState(({ unfold }) => ({ unfold: !unfold}));
  }
  render() {
    const { placeholder, options, attr = {} } = this.props;
    const { unfold } = this.state;
    const { fold, ...otherAttr } = attr;
    if (fold) {
      return (
        <div className={`fold-check-box ${unfold ? "unfold-check-box" : ""}`}>
          <Checkbox.Group placeholder={placeholder} options={options} {...otherAttr} />
          <Button size="small" onClick={this.handleToggle} className="fold-check-box-btn">
            { unfold ? "更多" : "收起"}
            <Icon type={unfold ? "down" : "up"} />
          </Button>
        </div>
      );
    }
    return (
      <Checkbox.Group placeholder={placeholder} options={options} {...otherAttr} />
    );
  }
}
