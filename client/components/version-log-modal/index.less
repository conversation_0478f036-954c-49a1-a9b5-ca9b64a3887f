.version-log-modal {
  .version-log-modal-main{
    overflow-y:auto;
    overflow-x:hidden;
    max-height:600px;
  }
  .more{
    text-align:center;margin:10px 0;
    a{
      i{
        display:block;
      }
    }
  }
  .end {
    @color: #999;
    position: relative;
    margin: 10px 0;
    text-align:center;
    &::before {
      content: attr(data-title);
      display: inline-block;
      color: @color;
      font-size: 14px;
      line-height: 14px;
      padding: 0 10px;
      background-color: #FFF;
      position: relative;
      z-index: 1;
    }
    &:after {
      content: '.';
      display: block;
      transform: translateY(-10px);
      color: transparent;
      width: 100%;
      height: 0;
      border-bottom: 1px solid #ddd;
    }
  }
}

.version-log-record-panel{
  .version-log-record-list {

    .ant-timeline-item-head-custom {
      background:none;
      .icon {
        width: 10px;
        height: 10px;
        background: #B1BCD2;
        border-radius: 5px;
        &.first-icon {
          background: #FF4D4F;
        }
      }
    }


    .top {
      line-height:24px;margin-bottom:5px;
      color: #666;
      .date{
        color:#333;
        margin-right:10px;
      }
      .v{
        margin-left:10px;
      }
    }
  }
}