import React, { Component } from "react";
import { Form, Card, Input, Button, message } from "antd";
import VerifyCodeImg from "client/components/verifycode-img";
import PropTypes from "prop-types";
import { checkUniqueCode } from "client/apis/active-organization";

const FormItem = Form.Item;

class OrganizationCode extends Component {
  constructor(props) {
    super(props);
  }

  handleSubmit(e) {
    e.preventDefault();
    const props = this.props;
    props.form.validateFieldsAndScroll((errs, values) => {
      if (!errs) {
        const payload = { ...values };
        payload.uuid = props.state.uuid;

        // props.changeState({ step: 1 })
        checkUniqueCode(payload)
          .then(res => {
            const { data } = res;

            if (data.code === 0) {
              props.changeState({
                organizationInfo: data.data,
                step: 1 // 跳转下一步
              });
            } else {
              return Promise.reject(data);
            }
          })
          .catch(err => {
            message.error(err.message);
          });
      }
    });
  }


  render() {
    const { getFieldDecorator } = this.props.form;

    return (
      <Card className="organization-code">
        <Form onSubmit={this.handleSubmit.bind(this)}>
          <FormItem>
            {getFieldDecorator("orgUniqueCode", {
              rules: [{ required: true, message: "请输入组织标识码" }]
            })(<Input placeholder="请输入组织标识码" />)}
          </FormItem>
          <FormItem>
            {getFieldDecorator("captcha", {
              rules: [{ required: true, message: "请输入验证码" }]
            })(<VerifyCodeImg changeState={this.props.changeState} />)}
          </FormItem>
          <FormItem style={{ marginTop: 50 }}>
            <Button type="primary" htmlType="submit" style={{ width: 140 }}>
              提交
            </Button>
            <Button
              onClick={()=>this.props.history.push('/login')}
              style={{ marginLeft: 30, width: 140 }}
            >
              返回登录
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}

const OrganizationCodeWrapper = Form.create()(OrganizationCode);

OrganizationCodeWrapper.propTypes = {
  changeState: PropTypes.func, // 改变父级state
  state: PropTypes.object
};

export default OrganizationCodeWrapper;
