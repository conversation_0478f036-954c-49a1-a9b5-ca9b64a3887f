.ta-left-sider-container {
  .top {
    width: 100%;
    background: #ffffff;
    padding: 20px;
    text h2 {
      height: 50px;
      line-height: 50px;
      background: #fff;
      width: 100px;
      text-align: center;
    }
    .ta-auto-complete {
      // width: 360px;
      width: 100%;
      background: #f7f8f9;
    }
    .ant-tabs {
      height: 40px;
    }

    .ant-tabs-tab {
      margin: 0;
      height: 40px;
      line-height: 38px;
      padding: 0 16px;
      border: 1px solid #e8e8e8;
      border-radius: 4px 4px 0 0;
      margin-right: 2px;
    }

    .ant-tabs-tab-active {
      background: #fff;
    }

    .ant-tabs-ink-bar {
      display: none !important;
    }
  }

  .orgContainer {
    position: relative;
    overflow: auto;
    background: #f7f8f9;
    min-height: 400px;
    max-height: 600px;
  }
  .ant-layout-sider-zero-width-trigger {
    top: 40%;
    z-index: 9;
    right: -10px;
    width: 20px;
    height: 35px;
    font-size: 16px;
    line-height: 35px;
    background-color: #e8e8e8;
    color: #95a2ba;
    box-shadow: 0 0 2px #ccc;
  }

  .current-item {
    background-color: #fff3f0;
  }
}
