import React, {Component} from 'react';
import moment from 'moment';
import {
	Form,
	Table,
	Modal,
	Alert,
	Button,
	Checkbox,
	DatePicker,
	message as Message
} from 'antd';
import {
	columnsA,
	columnsB
} from './columns';
import MobilePreview from 'client/components/overview-modal';
import PersonModal from 'client/components/person-selector/sub-components/person-modal';

export default class Class extends Component {
	constructor (props) {
		super(props);
		this.state = {
			type: '',
			endTime: '',
			startTime: '',
			activeTime: {},
			personData: [],
			checkboxList: [],
			activity_id: '',
			modalVisible: false,
			disableEndTime: false,
			disableStartTime: false,
			personModalVisible: false,
			setTimeModalVisible: false
		}
	}

	/**
	 * @description 取消
	 */
	onCancel () {
		this.setState({
			type: '',
			activeTime: {},
			activity_id: '',
			checkboxList: [],
			endTime: undefined,
			startTime: undefined,
			disableEndTime: false,
			disableStartTime: false,
			personModalVisible: false,
			setTimeModalVisible: false
		}, () => this.personModal.init([]))
	}

	/**
	 * @description 确认
	 */
	onConfirm () {
		const {
			type,
			endTime,
			startTime,
			personData,
			activity_id,
			disableEndTime,
			disableStartTime
		} = this.state;
		const {onOutput} = this.props;
		(!disableStartTime && !startTime) || (!disableEndTime && !endTime)
			? Message.error('请选择时间')
			: onOutput && onOutput({
			activity_id,
			end_time: !disableEndTime ? endTime : '',
			start_time: !disableStartTime ? startTime : '',
			user_list: JSON.stringify(personData.map(({phone, org_id, user_id}) => ({type, phone, user_id, organization_id: org_id})))
		});
		this.onCancel();
	}

	/**
	 * @description 上一步
	 */
	onPrevStep () {
		const {personData} = this.state;
		this.setState({setTimeModalVisible: false}, (
		) => this.setState({personModalVisible: true}), (
		) => this.personModal.init(personData))
	}

	/**
	 * @description 开始时间
	 * @param current
	 * @return {boolean}
	 */
	disabledStartDate (current) {
		const {activeTime: {start, end}, endTime} = this.state;
		return  current < moment(start) || current > moment(endTime ? endTime : end);
	}

	/**
	 * @description 结束时间
	 * @param current
	 * @return {boolean}
	 */
	disabledEndDate (current) {
		const {activeTime: {start, end}, startTime} = this.state;
		return  current < moment(startTime ? startTime : start) || current > moment(end);
	}

	render() {
		const {
			total,
      org_id,
      root_oid,
			history,
			loading,
			onDelete,
			dataList,
			onChange,
			params: {page, pagesize},
		} = this.props;
		const {
			url,
			endTime,
			startTime,
			checkboxList,
			modalVisible,
			disableEndTime,
			disableStartTime,
			personModalVisible,
			setTimeModalVisible,
			activeTime: {start, end},
		} = this.state;

		/**
		 * @description 列表Props
		 */
		const columnsProps = {
			history,
			onStart: ({type, activity_id, start_time, end_time}) => {
				const end = end_time ? end_time : undefined;
				const start = start_time ? start_time : undefined;
				this.setState({
					type,
					activity_id,
					endTime: end,
					startTime: start,
					personModalVisible: true,
					activeTime: {end, start}
				})
			},
			onDelete: data => onDelete && onDelete(data),
			onPreview: url => this.setState({modalVisible: true, url})
		};

		/**
		 * @description 成员选择Props
		 */
		const personModalProps = {
			title: '选择成员',
			visible: personModalVisible,
			ref: ref => this.personModal = ref,
			onCancel: () => {this.onCancel()},
			onOk: (data) => data.length
				? this.setState({
					personData: data,
					personModalVisible: false
				}, () => this.setState({setTimeModalVisible: true}))
				: Message.error('请选择成员！')
		};

		return (
			<div>
				<Table
					bordered
					loading={loading}
					dataSource={dataList.map((item, index) => ({...item, index}))}
					rowKey={({index}) => index}
					pagination={{
						total,
						size: 'small',
						current: page,
						pageSize: pagesize,
						showQuickJumper: true,
						onChange: e => onChange && onChange(e),
						onShowSizeChange: e => onChange && onChange(e)
					}}
					columns={org_id === root_oid ? columnsA(columnsProps) : columnsB(columnsProps)}
				/>
				<Modal
					title="开展时间"
					width={558}
					footer={false}
					visible={setTimeModalVisible}
					onCancel={() => this.onCancel()}
				>
					<Alert
						type="warning"
						message={<span style={{color: '#FF4D4F'}}>在活动时间：{start} 至 {end} 范围内</span>}
					/>
					<div className="assessment-form">
						<Form {...{
							labelCol: {sm: {span: 4}},
							wrapperCol: {sm: {span: 20}}
						}}>
							<Form.Item
								required
								label="开展时间"
							>
								<DatePicker
									showTime
									allowClear={false}
									disabled={disableStartTime}
									format='YYYY-MM-DD hh:mm:ss'
									value={startTime ? moment(startTime) : undefined}
									disabledDate={val => this.disabledStartDate(val)}
									onChange={val => this.setState({startTime: val && val.format('YYYY-MM-DD hh:mm:ss')})}
								/>
								<span style={{padding: '0 10px'}}>至</span>
								<DatePicker
									showTime
									allowClear={false}
									disabled={disableEndTime}
									format='YYYY-MM-DD hh:mm:ss'
									value={endTime ? moment(endTime) : undefined}
									disabledDate={val => this.disabledEndDate(val)}
									onChange={val => this.setState({endTime: val && val.format('YYYY-MM-DD hh:mm:ss')})}
								/>
							</Form.Item>
							<Form.Item wrapperCol={{sm: {offset: 4}}}>
								<Checkbox.Group value={checkboxList} onChange={val => this.setState({
									checkboxList: val,
									disableEndTime: val.includes(1),
									disableStartTime: val.includes(0)
								})}>
									<Checkbox value={0}>不设置开始时间</Checkbox>
									<Checkbox value={1}>不设置结束时间</Checkbox>
								</Checkbox.Group>
							</Form.Item>
						</Form>
					</div>
					<div className="button-list">
						<Button type="primary" onClick={() => this.onConfirm()}>确定</Button>
						<Button type="default" onClick={() => this.onPrevStep()}>上一步</Button>
						<Button type="default" onClick={() => this.onCancel()}>取消</Button>
					</div>
				</Modal>
				<MobilePreview
					url={url}
					modalVisible={modalVisible}
					closeHandler={() => this.setState({modalVisible: false})}
				/>
				<PersonModal {...personModalProps}/>
			</div>
		)
	}
}
