import React from "react";
import { Table, Form, Modal, Carousel, message } from "antd";
import StepTitle from "./components/Steps";
import Step1 from "./components/Step1";
import Step2 from "./components/Step2";
import Step3 from "./components/Step3";
import "./index.less";
import {
  queryMeetingTypeList,
  queryAllMeetingTypeList
} from "apis/meeting-type-manage";

import Steps from "components/steps";


/**
 * @param getMeetingType 
 * 一个方法, 点击提交 && 确定的时候导出表格数据, 是一个对象 { meeting_types: data<Array> }
 * 
 * @param data Array
 * 查看详情信息回显的数据
 */

class SelectMeetType extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false, // 选择会议类型 modal 是否显示
      setMeetVisible: false, // 设置会议要求 modal 是否显示
      setMeetItem: 0, // 设置会议要求, 第几项
      current: 0, // 当前步骤
      meeting_types: [], // 外层表格
      // 第一步
      dataSource: [], // 第一步 表格
      selected: [], // 第一步表格选中项
      step1Total: 0,
      // step1PageIndex: 1,
      step1PageSize: 10,
      step1TableLoading: false,
      type_id: null,
      category_id: null,
      meetingCategoryList: [], // 所属类别, 下拉选择框
      meetingTypeList: [], // 所属类型, 下拉选择框
      // 第三步
      deduct: 0, // 未执行扣分
      meeting_num: 1, // 召开次数
      is_sign_in: 0, // 是否需要签到
      is_w_resolution: 0 // 是否需要填写决议
    }
    this.carouselRef = React.createRef();
    this.updateState = this.updateState.bind(this);
    this.nextStep = this.nextStep.bind(this);
    this.handleCancel = this.handleCancel.bind(this);
    this.delMeetType = this.delMeetType.bind(this);
    this.openSetMeetModal = this.openSetMeetModal.bind(this);
    this.formatMeetingType = this.formatMeetingType.bind(this);
  }

  componentDidMount() {
    const { data } = this.props;
    if (data && data.length > 0) {
      this.setState({
        meeting_types: data
      });
    }
    // 初始化 step1 表格数据
    this.getStep1TableData({ page_size: 9999 });
    //
    queryAllMeetingTypeList({ category_id: null, type: null }).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        this.setState({
          meetingCategoryList: data.data
        });
        this.formatMeetingType(data.data);
      } else {
        message.error(data.message);
      }
    })
  }

  // 获取第一部表格数据
  getStep1TableData(_data) {
    this.setState({
      step1TableLoading: true
    })
    queryMeetingTypeList(_data).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        this.setState({
          dataSource: data.data,
          // step1PageIndex: data.pageNum,
          // step1PageSize: data.pageSize,
          step1Total: data.total,
          step1TableLoading: false
        });
      } else {
        this.setState({
          step1TableLoading: false
        })
      }
    })
  }

  // 设置组件 state
  updateState(payload, callback) {
    this.setState(payload, () => callback && callback());
  }

  // 下一步
  nextStep() {
    const { form } = this.props;
    const { selected } = this.state;
    if (selected.length > 0) {
      this.setState({
        current: this.state.current + 1,
      }, () => {
        this.carouselRef.current.slick.slickGoTo(this.state.current)
        form.resetFields();
      })
    } else {
      message.error("请选择所属类别与活动类型")
    }

  }

  // 上一步
  prevStep() {
    const { form } = this.props;
    this.setState({
      current: this.state.current - 1,
    }, () => {
      this.carouselRef.current.slick.slickGoTo(this.state.current)
      form.resetFields();
    })
  }

  // 关闭选择会议 modal
  handleCancel() {
    const { form } = this.props;
    this.setState({
      visible: false,
      current: 0
    }, () => {
      form.resetFields();
      this.getStep1TableData({ page_size: 9999 });
      this.carouselRef.current.slick.slickGoTo(this.state.current);
    });
  }

  // 删除会议类型
  delMeetType(_index) {
    const { getMeetingType } = this.props;
    const { meeting_types } = this.state;
    let temp = [...meeting_types];
    temp.splice(_index, 1);
    this.setState({
      meeting_types: []
    }, () => {
      this.updateState({ meeting_types: temp });
      getMeetingType && getMeetingType({ meeting_types: temp })
    });
  }

  // 打开设置会议要求 modal
  openSetMeetModal(record, index) {
    this.setState({
      setMeetVisible: true,
      setMeetItem: index,
      deduct: record.deduct, // 未执行扣分
      meeting_num: record.meeting_num, // 召开次数
      is_sign_in: record.is_sign_in, // 是否需要签到
      is_w_resolution: record.is_w_resolution // 是否需要填写决议
    })
  }

  // 格式化会议类型
  formatMeetingType(data) {
    const tempData = []
    if (data && data.length > 0) {
      data.map(item => {
        if (item.types && item.types.length > 0) {
          tempData.push(...item.types);
        }
      });
    };
    this.setState({
      meetingTypeList: tempData
    })
  }

  render() {
    const {
      visible,
      meeting_types,
      setMeetVisible,
      current
    } = this.state;
    const {
      form,
      getMeetingType
    } = this.props;
    const stepTitleProps = {
      state: this.state
    };
    // 第一步的属性
    const step1Props = {
      state: this.state,
      changeShowSize: (step1PageSize) => {
        this.setState({
          step1PageSize
        });
        // console.log("改变每页显示条数", step1PageSize);
      },
      form,
      nextStep: this.nextStep.bind(this),
      updateState: this.updateState.bind(this),
      getStep1TableData: this.getStep1TableData.bind(this),
      formatMeetingType: this.formatMeetingType.bind(this),
      handleCancel: this.handleCancel.bind(this)
    }
    // const step2Props = {
    //   state: this.state,
    //   form,
    //   nextStep: this.nextStep.bind(this),
    //   prevStep: this.prevStep.bind(this)
    // }
    // 第三步的属性
    const step3Props = {
      state: this.state,
      form,
      prevStep: this.prevStep.bind(this),
      updateState: this.updateState.bind(this),
      handleCancel: this.handleCancel.bind(this),
      getMeetingType
    }


    // 设置会议要求 modal 属性
    const setMeetModalProps = {
      state: this.state,
      form,
      hasSet: true,
      updateState: this.updateState.bind(this)
    }
    const columns = [
      {
        title: "序号",
        dataIndex: "index",
        align: "center",
        render: (val, record, index) => index + 1
      },
      {
        title: "所属类别",
        dataIndex: "category",
        align: "center",
      },
      {
        // title: "会议类型",
        title: "活动类型",
        dataIndex: "type",
        align: "center",
      },
      {
        // title: "要求召开次数",
        // title: "要求举办次数",
        title: "组织频次",
        dataIndex: "meeting_num",
        align: "center",
      },
      {
        title: "未执行扣分",
        dataIndex: "deduct",
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "null",
        align: "center",
        render: (val, record, index) => {
          return (
            <div className="operator">
              <a onClick={() => this.openSetMeetModal(record, index)}>设置活动要求</a>
              <a onClick={() => this.delMeetType(index)}>删除</a>
            </div>
          )
        }
      },
    ];
    const stepsProps = {
      stepList: [
        // { title: "选择会议类型" },
        { title: "选择活动类型" },
        // { title: "设置会议要求" }
        { title: "设置活动要求" }
      ],
      style: {
        maxWidth: "80%"
      },
      current
    }

    // console.log(current);
    return (
      <div className="SelectMeetType">
        <p>
          <a
            className="openModal"
            onClick={() => this.updateState({ visible: true })}
          >
            {/* 选择会议类型 */}
            选择活动类型
          </a>
        </p>
        <Table
          style={{
            display: `${meeting_types.length > 0 ? "block" : "none"}`
          }}
          bordered
          columns={columns}
          dataSource={meeting_types}
          rowKey="type_id"
          pagination={false}
        />
        <Modal
          title={
            <Steps {...stepsProps} />
            // <StepTitle {...stepTitleProps} />
          }
          visible={visible}
          width={current === 0 ? 800 : 600}
          footer={null}
          className="SelectMeetType modalStyle"
          onCancel={() => {
            this.handleCancel();
          }}
          destroyOnClose={true}
        // style={{ height: "600px" }}
        >
          <Carousel
            className="carouselStyle"
            ref={this.carouselRef}
            dots={null}
          >
            <div className="carouselChild">
              <Step1 {...step1Props} />
            </div>
            <div className="carouselChild">
              <Step3 {...step3Props} />
            </div>
          </Carousel>
        </Modal>
        <Modal
          title="设置活动要求"
          visible={setMeetVisible}
          onCancel={() => this.updateState({ setMeetVisible: false }, form.resetFields)}
          footer={null}
          width={800}
        >
          <Step3 {...setMeetModalProps} />
        </Modal>
      </div>
    )
  }
}

export default Form.create()(SelectMeetType);