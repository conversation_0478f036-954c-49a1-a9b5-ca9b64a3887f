/**
 * 活动介绍
 * <AUTHOR>
 * 2018/4/23
 */
import React from "react";
import PropTypes from "prop-types";
import { Row, Col, Radio, Form } from "antd";

import adaptiveLayout from "../gridLayout";
import { uploadFile } from "apis/file";
import { fileHost } from "apis/config";
import "./index.less";
import Editor from "client/components/RichTextEditor";

class ActivityIntroduce extends React.Component {
  uploadFun(param) {
    const fd = new FormData();
    fd.append("upfile", param.file);
    fd.append("upType", "image");
    uploadFile(fd)
      .then((result) => {
        const uri = `${fileHost}/file/download/${result.data.data[0]}`;
        param.success({
          url: uri,
        });
      })
      .catch((err) => {
        param.error({
          msg: "上传失败",
        });
      });
  }

  render() {
    const { that, form, preview, formFields, handleEvents, activityFormState } =
      this.props;
    const { getFieldDecorator, getFieldValue } = form;
    //编辑器配置
    const editorProps = {
      disabled: preview,
      height: 460,
      contentFormat: "html",
      placeholder: "请输入详情",
      media: {
        video: false,
        audio: false,
        uploadFn: this.uploadFun.bind(this),
        validateFn: (file) => {
          return file.size < 1024 * 1000;
        },
      },
    };

    const onHTMLChange = (content) => {
      handleEvents.commonIntroductionReceiver(content);
    };

    return (
      <div>
        <Row
          className="ant-form-item"
          style={{ visibility: "hidden", height: 0 }}
        >
          <Col {...adaptiveLayout.offsetWrapper.wrapperCol}>
            {getFieldDecorator(formFields.activityContentWebAndApp, {
              initialValue: activityFormState.activityContentWebAndApp,
            })(
              <Radio.Group name={formFields.activityContentWebAndApp}>
                <Radio value={true}>网站和移动端使用相同内容</Radio>
                <Radio value={false}>网站和移动端使用不同内容</Radio>
              </Radio.Group>
            )}
          </Col>
        </Row>

        {activityFormState.activityContentWebAndApp ? (
          <Row className="ant-form-item">
            <Col
              {...adaptiveLayout.formWrapper.labelCol}
              className="ant-form-item-label"
            >
              <label title="通用介绍">通用介绍</label>
            </Col>
            <Col {...adaptiveLayout.formWrapperCol}>
              <div id="editorCommon" className="editor-wrap">
                {getFieldDecorator(formFields.commonIntroduction, {
                  initialValue: activityFormState.commonIntroduction,
                  rules: [
                    {
                      required: true,
                      // message: "请输入活动网站版介绍"
                      message: "请输入通用介绍",
                    },
                  ],
                })}

                {/* <Editor
                   ref={e => (this._edit = e)}
                   width={1000}
                   cdn
                   upType={"image"}
                   onChange={onHTMLChange}
                   defaultValue={
                     activityFormState.commonIntroduction || " "
                   }
                 /> */}
                <Editor
                  value={getFieldValue(activityFormState.commonIntroduction || " ")}
                  onChange={(html) => onHTMLChange(html)}
                  //  disabled={preview}
                  //  initialValue={activityFormState.commonIntroduction || " "}
                  //  onRef={(ref) => ref && (that.editor = ref)}
                  //  onChange={(html) => onHTMLChange(html)}
                />
              </div>
            </Col>
          </Row>
        ) : (
          <div>
            <Row className="ant-form-item">
              <Col
                {...adaptiveLayout.formWrapper.labelCol}
                className="ant-form-item-label"
              >
                <label title="网站版">网站版</label>
              </Col>
              <Col {...adaptiveLayout.formWrapperCol}>
                {/* {EditorLoading ? (
                   <div id="editorWeb" className="editor-wrap">
                     {getFieldDecorator(formFields.webIntroduction, {
                       initialValue: activityFormState.webIntroduction,
                       rules: [
                         {
                           required: true,
                           message: "请输入活动网站版介绍"
                         }
                       ]
                     })(
                       <BraftEditor
                         {...editorProps}
                         viewWrapper="#editorWeb"
                         contentId={activityFormState.activityId}
                         initialContent={activityFormState.webIntroduction}
                         onHTMLChange={handleEvents.webIntroductionReceiver}
                       />
                     )}
                   </div>
                 ) : null} */}
              </Col>
            </Row>
            <Row className="ant-form-item">
              <Col
                {...adaptiveLayout.formWrapper.labelCol}
                className="ant-form-item-label"
              >
                <label title="移动端">移动端</label>
              </Col>
              <Col {...adaptiveLayout.formWrapperCol}>
                {/* {EditorLoading ? (
                   <div id="editorApp" className="editor-wrap">
                     {getFieldDecorator(formFields.appIntroduction, {
                       initialValue: activityFormState.appIntroduction,
                       rules: [
                         {
                           required: true,
                           message: "请输入活动APP版介绍"
                         }
                       ]
                     })(
                       <BraftEditor
                         {...editorProps}
                         viewWrapper="#editorApp"
                         contentId={activityFormState.activityId}
                         initialContent={activityFormState.appIntroduction}
                         onHTMLChange={handleEvents.appIntroductionReceiver}
                       />
                     )}
                   </div>
                 ) : null} */}
              </Col>
            </Row>
          </div>
        )}
      </div>
    );
  }
}

ActivityIntroduce.propTypes = {
  //预览模式
  preview: PropTypes.bool,

  //antd表单对象
  form: PropTypes.object.isRequired,

  //编辑器
  Editor: PropTypes.any,

  //编辑器加载，服务端渲染完毕后，改变状态，渲染编辑器
  EditorLoading: PropTypes.bool.isRequired,

  //表单字段
  formFields: PropTypes.shape({
    //使用通用介绍还是web/app分开介绍
    activityContentWebAndApp: PropTypes.string.isRequired,
    //通用详细介绍
    commonIntroduction: PropTypes.string.isRequired,
    //web详细介绍
    webIntroduction: PropTypes.string.isRequired,
    //app详细介绍
    appIntroduction: PropTypes.string.isRequired,
  }).isRequired,

  //表单处理的事件合集
  handleEvents: PropTypes.shape({
    //通用介绍内容onChange
    commonIntroductionReceiver: PropTypes.func.isRequired,
    //web介绍内容onChange
    webIntroductionReceiver: PropTypes.func.isRequired,
    //app介绍内容onChange
    appIntroductionReceiver: PropTypes.func.isRequired,
  }).isRequired,

  //表单model
  activityFormState: PropTypes.shape({
    activityId: PropTypes.number.isRequired, //活动id用于更新编辑器内容
    activityContentWebAndApp: PropTypes.bool, //使用通用介绍还是web/app分开介绍
    commonIntroduction: PropTypes.string, //通用详细介绍
    webIntroduction: PropTypes.string, //web详细介绍
    appIntroduction: PropTypes.string, //app详细介绍
  }).isRequired,
};

ActivityIntroduce.defaultProps = {
  preview: false,
  Editor: null,
  EditorLoading: false,
};

export default ActivityIntroduce;
