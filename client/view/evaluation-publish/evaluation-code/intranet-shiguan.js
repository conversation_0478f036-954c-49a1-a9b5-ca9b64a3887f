import SearchHeader from "client/components/search-header";
import { getInternalQrCodesDownload } from "apis/evaluation-publish";
import { cadreMobile } from "client/apis/config";

export default function IntranetShiguan(props) {
  const { history, location, evaluationId, orgId } = props;

  const MobileUrl = `${cadreMobile}/shiguan/`;

  const openDetail = (row) => {
    const params = new URLSearchParams({
      evaluation_id: evaluationId,
      city_leader_id: row.leader_id,
      leader_name: row.leader_name,
    });

    history.push(`/evaluation-publish/evaluation-detail?${params.toString()}`);
  };

  const [loading, setLoading] = React.useState(false);
  const [dss, setdss] = React.useState([]); // ✅ 正确的数组解构

  const reqList = () => {
    setLoading(true);
    getInternalQrCodesDownload({ evaluation_id: evaluationId }).then((res) => {
      setdss(res.data.data);
      setLoading(false);
    });
  };

  React.useEffect(() => {
    reqList();
  }, [evaluationId]);

  const handlePreview = (row, showType) => {
    console.log("preview-row", row);
    const url = `${MobileUrl}?evaluation_id=${evaluationId}&org_id=${orgId}&eval_partyment_id=${19}&qrcode_type=${
      row.qr_code_type
    }&preview_type=${showType}`;
    window.open(url);
  };

  const columns = [
    { title: "领导姓名", dataIndex: "leader_name", key: "user_name" },
    { title: "职务", dataIndex: "job", key: "job_name" },
    { title: "测评班子数", dataIndex: "team_num", key: "banziNum" },
    { title: "测评干部数", dataIndex: "user_num", key: "ganbuNum" },
    {
      title: "操作",
      dataIndex: "handler",
      key: "handler",
      width: 160,
      render: (_, r) => {
        return (
          <div>
            <a onClick={() => openDetail(r)}>详情</a>
            <Ant.Dropdown
              style={{ marginLeft: "auto" }}
              overlay={
                <Ant.Menu>
                  <Ant.Menu.Item>
                    <a onClick={() => handlePreview(r, "1")}>模拟测评</a>
                  </Ant.Menu.Item>
                  <Ant.Menu.Item>
                    <a onClick={() => handlePreview(r, "2")}>可跳过答题</a>
                  </Ant.Menu.Item>
                </Ant.Menu>
              }
            >
              <Ant.Button type="link">
                <Ant.Icon type="eye" />
                预览 <Ant.Icon type="down" />
              </Ant.Button>
            </Ant.Dropdown>
          </div>
        );
      },
    },
  ].map((i) => ({ align: "center", ...i }));

  return (
    <div>
      <SearchHeader
        title="下载测评码"
        onBack={() => {
          history.goBack();
        }}
      />
      <Ant.Table
        loading={loading}
        bordered
        dataSource={dss}
        columns={columns}
        pagination={false}
      />
    </div>
  );
}
