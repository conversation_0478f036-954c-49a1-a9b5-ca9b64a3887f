/**
 * 
 * 会议类型组合
 * 
 */

const { http } = require('client/tool/axios');
import { mtmHost } from './config';

// 新增会议类型组合
export const addMeetTypeGroup = data => {
  return http.post(`${mtmHost}/type-group/add`, data);
}

// 查询会议类型组合列表 all
export const queryAllMeetTypeGroup = data => {
  return http.get(`${mtmHost}/type-group/list-all`, data);
}

export const queryMeetingTypeGroup = data => {
  return http.get(`${mtmHost}/type-group/list`, data);
}

// 删除会议类型组合
export const deleteMeetTypeGroup = data => {
  return http.delete(`${mtmHost}/type-group/del/${data}`);
}