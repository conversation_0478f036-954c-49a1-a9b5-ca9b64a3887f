import React, { useState, useEffect } from "react";
import SearchHeader from "components/search-header";
import "./index.less";
import {
  Button,
  Form,
  Select,
  Input,
  Icon,
  Empty,
  Pagination,
  message,
  TreeSelect,
} from "antd";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import {
  findByWhere,
  findAllSources,
  findCategoryList,
} from "apis/resource-library";
import { CDN } from "apis/config";

// 格式化分类数据，选择框只展示一二三级
const formatData = (value) => {
  value.title = value.resource_category_name;
  value.value = value.resource_category_id;
  value.key = value.resource_category_id;
  if (value.children) {
    value.children.forEach(formatData);
  }
};

/**
 * 场地库
 */
const tabName = [
  { title: "系统公开", key: 1 },
  { title: "本组织", key: 2 },
];
const initQuery = {
  page: 1,
  page_size: 10,
};

const { root_oid, oid } = JSON.parse(sessionStorage.getItem("userInfo") || {});
const tagsList = ["市内", "市外"];
const { Item } = Form;
const { Option } = Select;
const CaseLibrary = (props) => {
  const {
    history,
    form: { getFieldDecorator, getFieldsValue },
  } = props;

  const [total, setTotal] = useState(0);
  const [tabKey, setTabKey] = useState(1);
  const [selOrg, setSelOrg] = useState([]);
  const [catagoryId, setCategoryId] = useState(null);
  const [keyWord, setKeyWord] = useState("");
  const [categoryList, setCategoryList] = useState([]);
  // const [sourcesList, setSourcesList] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [searchQuery, setSearchQuery] = useState({});
  const [query, setQuery] = useState(initQuery);
  const [orgModalVisible, setOrgModalVisible] = useState(false);

  useEffect(() => {
    _findCategoryList();
  }, []);

  useEffect(() => {
    // _findAllSources();
  }, [tabKey, selOrg]);

  useEffect(() => {
    initData();
  }, [tabKey, query, searchQuery, selOrg]);

  // 获取来源
  // const _findAllSources = () => {
  //   findAllSources({
  //     resource_type: 3,
  //     view_type: tabKey,
  //     provider_org_id: selOrg.length ? selOrg[0].org_id : null,
  //   }).then(({ data: res }) => {
  //     if (res.code !== 0) {
  //       message.error(res.message);
  //       return;
  //     }
  //     setSourcesList(res.data);
  //   });
  // };

  // 获取分类
  const _findCategoryList = () => {
    findCategoryList({ category_type: 4 }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      res.data.forEach(formatData);
      setCategoryList(res.data);
    });
  };

  const initData = () => {
    setKeyWord("")
    const params = {
      ...searchQuery,
      ...query,
      provider_org_id: selOrg.length ? selOrg[0].org_id : null,
      resource_type: 4,
      view_type: tabKey,
    };
    if (params.key_word) {
      setKeyWord(params.key_word);
    }
    findByWhere(params).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      setTotal(res.total);
      setFileList(res.data);
    });
  };

  const onHandleSubmit = () => {
    setSearchQuery({ ...searchQuery, ...getFieldsValue() });
  };

  const resetSearch = () => {
    setQuery(initQuery);
    // setSearchQuery({});
    // setCategoryId(null);
  };

  const caseItemRender = (item) => {
    return (
      <div
        key={item.resource_library_id}
        className="case-item"
        onClick={() =>
          history.push("/field-library/document/detail", {
            ...item,
            keyWord,
            resource_type: 4,
          })
        }
      >
        {item.cover_url ? (
          <img
            className="case-item-img case-img"
            src={`${CDN}/${item.cover_url}`}
            alt=""
          />
        ) : (
          <div className="case-item-img case-default">
            {item.name.substring(0, 24)}
          </div>
        )}
        <div className="case-item-info">
          <div
            className="case-item-info-title"
            dangerouslySetInnerHTML={{
              __html: item.name.replace(
                keyWord,
                `<span style="color: red;">${keyWord}</span>`
              ),
            }}
          />
          <div className="case-item-info-tags">
            {item.tags &&
              item.tags.split(",").map((el) => {
                return <span>{el}</span>;
              })}
          </div>
        </div>
      </div>
    );
  };

  const orgModalProps = {
    visible: orgModalVisible,
    dataSource: selOrg,
    hasRoot: false,
    onlyDirectSub: true,
    // rootOrgId: 3,
    hideModal: () => setOrgModalVisible(false),
    radio: true,
    loadOrganizeData: (data) => {
      setOrgModalVisible(false);
      setSelOrg(data);
    },
  };

  const paginationProps = {
    total,
    current: query.page,
    pageSize: query.page_size,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100", "200"],
    showTotal: ($total, range) =>
      `总计${$total}条，当前展示第${range[0]}至${range[range.length - 1]}条`,
    onChange: (page) => setQuery({ ...query, page }),
    onShowSizeChange: (_, page_size) => setQuery({ ...query, page_size }),
  };

  return (
    <div className="case-library">
      <SearchHeader onBack={() => history.goBack()} title={"场地库"} />
      <div className="case-library-content">
        <div className="case-library-content-tabs">
          {tabName.map((item) => {
            return (
              <div
                key={item.key}
                className={`case-library-content-tabs-item ${tabKey === item.key ? "active" : "inactive"
                  }`}
                onClick={() => {
                  if (tabKey !== item.key) {
                    setTabKey(item.key);
                    setSelOrg([]);
                    resetSearch();
                  }
                }}
              >
                {item.title}
              </div>
            );
          })}
        </div>
        <div className="case-library-content-search">
          {(root_oid === oid || tabKey === 1) && (
            <div className="case-library-content-search-org">
              信息提供：{selOrg[0] ? selOrg[0].org_name : ""}
              <Button type="link" onClick={() => setOrgModalVisible(true)}>
                选择组织
              </Button>
              <OrganizeModal {...orgModalProps} />
            </div>
          )}
          <Form layout="inline" className="case-library-content-search-form">
            <Item label="" colon={false}>
              {getFieldDecorator("resource_category_id", {
                initialValue: catagoryId,
              })(
                <TreeSelect
                  allowClear
                  style={{ width: "100%" }}
                  dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                  treeData={categoryList}
                  placeholder="请选择场地分类"
                  treeDefaultExpandAll
                  onChange={(val) => setCategoryId(val)}
                />
              )}
            </Item>
            <Item label="" colon={false}>
              {getFieldDecorator("tags", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择服务" mode="multiple" allowClear>
                  {tagsList.map((item) => {
                    return <Option value={item}>{item}</Option>;
                  })}
                </Select>
              )}
            </Item>
            <Item label="" colon={false}>
              {getFieldDecorator("contact_details", {
                initialValue: undefined,
              })(
                  <Input
                    allowClear
                    placeholder="请输入地址关键词"
                  />
              )}
            </Item>
            <Item label="" colon={false}>
              {getFieldDecorator("key_word", {
                initialValue: null,
              })(
                <Input
                  allowClear
                  placeholder="请输入场地名称关键词"
                  maxLength={150}
                  prefix={
                    <Icon
                      type="search"
                      style={{ color: "rgba(213, 213, 213, 1)" }}
                    />
                  }
                />
              )}
            </Item>
            <Button type="primary" onClick={() => onHandleSubmit()}>
              搜索
            </Button>
          </Form>
        </div>
        {fileList.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <div className="case-library-content-list">
            {fileList.map((item) => {
              return caseItemRender(item);
            })}
          </div>
        )}
        <Pagination {...paginationProps} />
      </div>
    </div>
  );
};

export default Form.create()(CaseLibrary);
