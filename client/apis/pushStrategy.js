import { strategy } from './config';
import { http } from 'client/tool/axios';

// 106120006-推送策略-管理列表
const getStrategyList = (params = {}) => {
  return http.get(`${strategy}/strategy/list`, params);
}
//106120003-推送策略-启用停用
const updateStatus = (params = {}) => {
  return http.post(`${strategy}/strategy/update-status/${params.strategy_id}/${params.status}`);
}
//106120002-推送策略-新增/修改
const addOrUpdateStrategy = (params = {}) => {
  return http.post(`${strategy}/strategy/add-update`, params);
}
// 106120004-推送策略-删除
const deleteStrategy = (params = {}) => {
  return http.post(`${strategy}/strategy/delete/${params}`);
}
//106120007-推送策略-修改详细数据
const getStrategyDetail = (params = {}) => {
  return http.get(`${strategy}/strategy/detail/${params}`);
}
//106120001-推送字典列表
const getDictCode = (params = {}) => {
  return http.get(`${strategy}/dict/getDictCode`);
}
//特殊字段
const getWrapperDictCode = (params = {}) => {
  return http.get(`${strategy}/dict/wrapperDictCode/${params}`);
}


export {
  getStrategyList, 
  updateStatus, 
  addOrUpdateStrategy, 
  getStrategyDetail,
  deleteStrategy,
  getDictCode,
  getWrapperDictCode
}