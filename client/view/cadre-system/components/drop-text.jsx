import React from "react";
import { Dropdown, Menu, Icon } from "antd";
import dropImage from "../images/drop.png";
import "./drop-text.less";
function DropText({
  value,
  onChange,
  onSelect,
  option,
  disabled,
  disableList = [],
}) {
  const menu = (
    <Menu>
      {option.map((item) => (
        <Menu.Item
          disabled={disabled || disableList.includes(item.key)}
          key={item.key}
          onClick={() => {
            if (disableList.includes(item.key)) return;
            onChange && onChange(item.key);

            onSelect && onSelect(item.key);
          }}
        >
          {item.label}
        </Menu.Item>
      ))}
    </Menu>
  );
  const getValue = (value) => {
    const item = option.find((item) => Number(item.key) === Number(value));
    return item ? item.label : "";
  };
  return (
    <Dropdown overlay={menu} trigger={"click"} disabled={disabled}>
      <div className="drop-text">
        <span>{getValue(value)}</span>
        {!disabled && <img src={dropImage} className="drop-icon" />}
      </div>
    </Dropdown>
  );
}

export default DropText;
