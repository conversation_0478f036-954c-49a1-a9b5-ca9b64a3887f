// 预览组件
import React from "react";
import PropTypes from "prop-types";
import { message as Message } from "antd";
import { postFilePreview } from "apis/file";
import { CDN } from "apis/config";
import path from "path";

const Preview = (props) => {
  const {
    file,
    text,
    previewBegin,
    previewDone
  } = props;

  // 文件对象传入参数：
  // {
  //   file_id: 6733
  //   file_path: "doc/c5f4f507ec2984013fd2f12a5adcb59c.pptx"
  // }

  // 支持预览的格式
  const canPreviewTypesMap = {
    // office文件类型
    "doc": ["doc", "docx", "xls", "xlsx", "ppt", "pptx"],
    "other": ["pdf"],
    "img": [
      "jpg", "jpeg", "png", "bmp",
      // "gif"
    ]
  };
  const { file_path = "" } = file || {};
  const formatReg = /^(\w+)\/\w*\.(\w+)$/g;
  const result = formatReg.exec(file_path);
  const type = result[1], ext = result[2];
  let canPreview = false;
  if (canPreviewTypesMap[type]) {
    canPreview = canPreviewTypesMap[type].indexOf(ext) !== -1;
  }
  // 如果文件类型不支持预览，则不反回组件
  if (!canPreview) {
    return null;
  }
  const filePreview = async (file = {}) => {
    // 如果是图形文件，预览时直接拼接url并打开
    if (type === "img") {
      // console.log("预览图片", path.join(CDN, file_path));
      Message.destroy();
      typeof window !== "undefined" && window.open(`${CDN}/${file_path}`);
      return;
    }
    // console.log("预览文件", file);
    if (!file.file_id && !file.file_path) {
      Message.error("文件找不到");
      return;
    }
    const response = await postFilePreview(file);
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message, () => {
        Message.destroy();
      });
      previewDone && previewDone();
      return;
    }
    // 生成成功
    if (data.status === 1 && data.target_url) {
      typeof window !== "undefined" && window.open(`https://preview.imm.aliyun.com/index.html?url=${CDN}/${data.target_url}`);
    }
    else if (data.status === 2 && data.request_id) {
      filePreview({ ...file, request_id: data.request_id });
    } else {
      Message.error("预览出现错误");
    }
    Message.destroy();
    previewDone && previewDone();
  }
  return (
    <a href="javascript:void(0);"
      onClick={() => {
        Message.loading("正在生成预览，请稍候...", 0);
        previewBegin && previewBegin();
        filePreview(file);
      }}
      style={{ cursor: "pointer" }}>
      {text}
    </a>
  )
}

Preview.propTypes = {
  // 按钮显示文字
  text: PropTypes.string,
  // 文件传入数据，必须文件id和文件路径path
  file: PropTypes.shape({
    file_id: PropTypes.number,
    file_path: PropTypes.string
  }),
  // 预留更改外部状态方法
  updateState: PropTypes.func,
  // 开始预览
  previewBegin: PropTypes.func,
  // 结束预览
  previewDone: PropTypes.func
};

Preview.defaultProps = {
  text: "预览",
  updateState: () => { },
  previewBegin: () => { },
  previewDone: () => { }
};

export default Preview;