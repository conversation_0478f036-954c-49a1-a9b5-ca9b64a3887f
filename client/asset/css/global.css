@import "./normalize.css";
/* @import "./bootstrap.css"; */

@import "./app.css";
html,
body {
  height: 100%;
  /* overflow: hidden; */
}
.redux-nav {
  margin-left: 40px;
}

.redux-nav li {
  float: left;
  background-color: #1cb94a;
  color: white;
}

.redux-nav li a {
  color: white;
}

.redux-nav-item {
  padding-left: 40px;
  padding-bottom: 40px;
}

.redux-btn-add {
  background-color: #1cb94a;
  color: white;
  text-align: center;
  line-height: 40px;
  height: 40px;
  vertical-align: middle;
}

.redux-btn-del {
  background-color: #cd2a19;
  color: white;
  display: block;
  width: 60px;
  line-height: 30px;
  height: 30px;
  vertical-align: middle;
  text-align: center;
}

.spa-title {
  margin-top: 40px;
  text-align: center;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  vertical-align: middle;
}

/* 修正tab页签高度异常 */

.ant-tabs-nav-container .ant-tabs-nav .ant-tabs-tab {
  height: auto;
}

a.download-btn:active,
a.download-btn.active {
  color: #fff;
  background-color: #d9363e;
  border-color: #d9363e;
}

a.download-btn:hover {
  color: #fff;
  background-color: #ff7875;
  border-color: #ff7875;
}

a.download-btn:focus {
  text-decoration: none;
}

a.download-btn {
  display: inline-block;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  width: 115px;
  height: 36px;
  background: rgba(255, 241, 240, 1);
  border-radius: 5px;
  color: #f46e65;
  border: 1px solid #ffccc7;
  text-align: center;
  line-height: 36px;
  transition: all linear 0.15s;
}

/* 全局样式覆盖 */

.ant-layout-content {
  display: flex;
}

.ant-layout-content > div,
.ant-layout-content > section {
  flex: auto;
}

.BraftEditor-container {
  height: auto !important;
}

.no-margin-form-item .ant-form-item-control {
  margin-top: 0;
}

/* 重写ANTD样式 */

/* 表格页码组件样式，修复上下翻页箭头未居中 */

.ant-pagination-jump-next.ant-pagination-jump-next-custom-icon
  .ant-pagination-item-link {
  width: 100%;
  height: 100%;
}

.ant-pagination-jump-prev.ant-pagination-jump-prev-custom-icon
  .ant-pagination-item-container,
.ant-pagination-jump-next.ant-pagination-jump-next-custom-icon
  .ant-pagination-item-container {
  height: 100%;
}

.ant-pagination-jump-prev.ant-pagination-jump-prev-custom-icon
  .ant-pagination-item-container
  .anticon.anticon-double-left.ant-pagination-item-link-icon,
.ant-pagination-jump-next.ant-pagination-jump-next-custom-icon
  .ant-pagination-item-container
  .anticon.anticon-double-right.ant-pagination-item-link-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ant-pagination-item-ellipsis {
  position: absolute;
  top: 0;
  left: 0;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-disabled.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link,
.ant-pagination-disabled.ant-pagination-next .ant-pagination-item-link {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

/* 下拉选择树样式，修复左侧箭头未居中 */

/* 级联选择列表，修复下一级按钮图标未居中  */

.ant-select-tree-treenode-switcher-close,
.ant-cascader-menu-item.ant-cascader-menu-item-expand {
  display: flex;
  align-items: center;
}

/* 模态框关闭按钮样式未居中 */

.ant-modal-close .ant-modal-close-x,
.ant-select-tree-treenode-switcher-close
  .ant-select-tree-switcher.ant-select-tree-switcher_close {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 表格loading图标未居中 */

.ant-spin-nested-loading {
}

.ant-pagination-options-size-changer.ant-select {
  width: auto;
}

.ant-layout.ant-layout-has-sider {
  min-height: 100%;
}

.col-sql {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 168px;
}

.ant-table-fixed-header .ant-table-content .ant-table-scroll .ant-table-header {
  margin-bottom: -12px;
  padding-bottom: 0;
}

.ant-table-bordered.ant-table-fixed-header .ant-table-placeholder {
  border: 1px solid #e8e8e8 !important;
  border-top: 0 !important;
}

/* .ant-table td {
  white-space: nowrap;
} */
/** 改造后的checkbox  添加类名check-group-box**/
.check-group-box .ant-checkbox-group-item {
  margin-bottom: 6px;
}
.check-group-box .ant-checkbox-group-item .ant-checkbox {
  display: none;
}
.check-group-box .ant-checkbox-group-item span:nth-child(2) {
  display: inline-block;
  border: 1px solid #eee;
  padding: 0 20px;
  line-height: 32px;
  height: 32px;
  text-align: center;
}
.check-group-box .ant-checkbox-wrapper-checked span:nth-child(2) {
  color: #fff;
  /* background-color: #8fc31f; */
}
