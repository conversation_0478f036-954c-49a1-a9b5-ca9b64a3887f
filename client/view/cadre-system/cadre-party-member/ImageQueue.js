class ImageQueue {
    constructor() {
        this.queue = [];
        this.processing = false;
    }

    add(task) {
        this.queue.push(task);
        if (!this.processing) {
            this.process();
        }
    }

    async process() {
        if (this.queue.length === 0) {
            this.processing = false;
            return;
        }

        this.processing = true;
        const task = this.queue.shift();

        try {
            await task();
        } catch (error) {
            console.error('Task failed:', error);
        }

        this.process();
    }
}
export default new ImageQueue();