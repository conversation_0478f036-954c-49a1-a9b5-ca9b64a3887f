import { http } from "client/tool/axios";
import { sas1Host } from "./config";

// 4010202201-3-开始检测  http://wiki.aidangqun.com/project/3?p=757
const getExamineStart = (params = {}) => {
  return http.get(`${sas1Host}/examine/start`, params);
};

// 4010202201-5-按异常统计 (人员范围/组织范围) http://wiki.aidangqun.com/project/3?p=759
const getAbnormalCheck = (params = {}) => {
  return http.get(`${sas1Host}/examine/find-examine`, params);
};

// 4010202201-4-按组织统计 / 按党员统计 http://wiki.aidangqun.com/project/3?p=758
const getExamineUserOrOrg = (params = {}) => {
  return http.get(`${sas1Host}/examine/find-user-org`, params);
};

// 4010202201-7-按异常统计 组织 / 人员 (点击详情) http://wiki.aidangqun.com/project/3?p=761
const getExamineDetail = (params = {}) => {
  return http.get(`${sas1Host}/examine/find-detail`, params);
};

//  4010202201-6-按组织统计 / 按人员统计 (点击详情) http://wiki.aidangqun.com/project/3?p=760
const getOrgExamineDetail = (params = {}) => {
  return http.get(`${sas1Host}/examine/find-org-detail`, params);
};

// 4010202201-8-备注 http://wiki.aidangqun.com/project/3?p=765
const setExamineRemark = (params = {}) => {
  return http.get(`${sas1Host}/examine/remark`, params);
};

export {
  getExamineStart,
  getAbnormalCheck,
  getExamineUserOrOrg,
  getExamineDetail,
  getOrgExamineDetail,
  setExamineRemark
};
