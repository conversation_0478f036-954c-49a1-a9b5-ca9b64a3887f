const { http } = require('client/tool/axios');
import { vlHost, host } from './config';

// 通过code获取数据字典的相应数据
const getCodeList = ({ code = '' }) => {
  return http.get(`${host}/uc/op/list`, { code });
};
/**
 * 根据组织ID查询团体信息
 * @param {*} org_id 组织id
 */
const getTeamByOrg = (org_id) => {
  return http.get(`${host}/volunteer/team/find-team-by-org-id`, { org_id })
}
//http://************:8090/pages/viewpage.action?pageId=42795683
const getVolunteerHelpDetail = (data) => {
  return http.get(`${vlHost}/volunteer/help/detail/${data}`);
};
//http://************:8090/pages/viewpage.action?pageId=42795760
const getVolunteerHelpList = (data) => {
  return http.post(`${vlHost}/volunteer/help/list`, data);
};
// 3000040205-求助接单
const setVolunteerHelpAccept = (data) => {
  return http.post(`${vlHost}/volunteer/help/accept`, data);
};
// 3000040214-查询可关联项目
const findVolunteerProject = (data) => {
  return http.get(`${vlHost}/volunteer/help/find-project`, data);
};
// 3000040218-关联志愿项目到求助
const setVolunteerHelpRelation = (data) => {
  return http.post(`${vlHost}/volunteer/help/relation`, data);
};
// 3000040220-求助项目完成
const setVolunteerHelpFinish = (data) => {
  return http.get(`${vlHost}/volunteer/help/finish`, data);
};
// 3000040206-求助派单
const setVolunteerHelpAssigned = (data) => {
  return http.post(`${vlHost}/volunteer/help/assigned`, data);
};
// 3000040215-拒绝求助派单
const setVolunteerHelpReject = (data) => {
  return http.post(`${vlHost}/volunteer/help/reject`, data);
};

export {
  getCodeList,
  getTeamByOrg,
  getVolunteerHelpDetail,
  getVolunteerHelpList,
  setVolunteerHelpAccept,
  findVolunteerProject,
  setVolunteerHelpRelation,
  setVolunteerHelpFinish,
  setVolunteerHelpAssigned,
  setVolunteerHelpReject
}