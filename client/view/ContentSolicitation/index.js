import React, { useEffect, useState, useMemo } from "react";
import { Button, Tabs, message, Table } from "antd";
import SearchHeader from "components/search-header";
import Search from "./components/Search";
import { listApproveMy, delMy } from "apis/content-solicitation";
import moment from "moment";
import "./index.less";

const { TabPane } = Tabs;
const contentSolicitation = (props) => {
  const {
    match: {
      params: {
        id, // 1 初审， 2 终审
      },
    },
    history,
  } = props;

  const [tabkey, setTabkey] = useState("1");
  const [searchQuery, setSearchQuery] = useState({});
  const [loading, setLoading] = useState(false);
  const [contentId, setContentId] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [pages, setPages] = useState(0);
  const [query, setQuery] = useState({
    page: 1,
    page_size: 10,
  });

  useEffect(() => {
    initData(1);
  }, [tabkey, searchQuery]);

  const initData = (page) => {
    setLoading(true);
    const params = {
      ...searchQuery,
      ...query,
      page,
      action: tabkey,
      approve_type: id,
    };
    listApproveMy(params)
      .then(({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        setDataSource(res.data);
        setTotal(res.total);
        setPages(res.pages);
        setQuery({
          ...query,
          page,
        });
      })
      .finally(() => setLoading(false));
  };

  const jumpTo = (isEdit, record) => {
    history.push("/audit-content/detail", {
      isEdit,
      record,
      isApprove: tabkey === "1",
      approve_type: id,
    });
  };

  const delQuestion = (record) => {
    delMy({ content_personal_id: record.content_personal_id }).then(
      ({ data: res }) => {
        if (res.code !== 0) {
          message.error(res.message);
          return;
        }
        message.success("删除成功！");
        initData(1);
      }
    );
  };

  const searchProps = {
    id,
    tabkey,
    contentId,
    initData,
    resetContentId: () => setContentId([]),
    changeSearch: (values) => setSearchQuery(values),
    clearSearch: () => setSearchQuery({}),
  };

  const columns = [
    {
      title: "序号",
      align: "center",
      width: "5%",
      render: (t, r, i) => {
        return i + 1;
      },
    },
    {
      title: "知识标题",
      align: "left",
      dataIndex: "content_title",
      ellipsis: true,
    },
    {
      title: "提交人",
      align: "center",
      dataIndex: "name",
      width: "10%",
    },
    {
      title: "提交时间",
      align: "center",
      dataIndex: "create_time",
      width: "10%",
      render: (text) => {
        return moment(text).format("YYYY-MM-DD");
      },
    },
    {
      title: id === "1" ? "初审状态" : "终审状态",
      align: "center",
      dataIndex: "status",
      width: "10%",
      render: (text) => {
        if (text === 3 || text === 4) {
          return <span style={{ color: "#13A662" }}>审核通过</span>;
        } else if (text === 5 || text === 6) {
          return <span style={{ color: "#FF1C1C" }}>审核不通过</span>;
        } else {
          return "--";
        }
      },
    },
    {
      title: "审核时间",
      align: "center",
      dataIndex: "update_time",
      width: "10%",
      render: (text) => {
        return moment(text).format("YYYY-MM-DD");
      },
    },
    {
      title: "操作",
      align: "center",
      dataIndex: "operation",
      render: (text, record) => {
        const approveDetail = (
          <Button type="link" onClick={() => jumpTo(false, record)}>
            {tabkey === "1" ? "审批" : "详情"}
          </Button>
        );
        const editor = (
          <Button type="link" onClick={() => jumpTo(true, record)}>
            编辑
          </Button>
        );
        const del = (
          <Button type="link" onClick={() => delQuestion(record)}>
            删除
          </Button>
        );
        const operation = {
          1: [approveDetail, editor],
          2: [approveDetail, editor, del],
          3: [approveDetail, del],
        };
        if (id === "1") {
          return approveDetail;
        } else if (record.status === 6) {
          return operation[3];
        } else {
          return operation[tabkey];
        }
      },
    },
  ];

  const pagination = {
    total,
    current: query.page,
    pageSize: query.page_size,
    showQuickJumper: true,
    showTotal: ($total) => `共 ${$total} 条记录，共 ${pages} 页`,
    onChange: (page) => {
      initData(page);
    },
  };

  const rowSelection = {
    onChange: (selectedRowKeys) => {
      setQuestionId([...questionId, selectedRowKeys]);
    },
  };

  const indexTable = useMemo(() => {
    const tableProps = {
      rowSelection: id === "2" && tabkey === "2" ? rowSelection : null,
      loading,
      dataSource,
      columns:
        tabkey === "2"
          ? columns
          : columns.filter((item) => {
              return (
                item.dataIndex !== "status" && item.dataIndex !== "update_time"
              );
            }),
      pagination,
      bordered: true,
      rowKey: "content_personal_id",
    };
    return <Table {...tableProps} />;
  }, [dataSource, loading]);

  return (
    <div className="content-solicitation">
      <SearchHeader title={id === "1" ? "内容初审" : "内容终审"} />
      <Tabs
        defaultActiveKey="1"
        onChange={(key) => {
          setSearchQuery({});
          setTabkey(key);
        }}
      >
        <TabPane tab="待审核" key="1">
          <Search {...searchProps} />
          {indexTable}
        </TabPane>
        <TabPane tab="已审核" key="2">
          <Search {...searchProps} />
          {indexTable}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default contentSolicitation;
