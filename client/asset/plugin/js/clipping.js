var $body = document.body;
var $tool = document.getElementById("tool");
var body_height = $body.clientHeight;
var body_width = $body.clientWidth;
var box_height, box_width;
var fileType = ["image/jpeg", "image/png"];
// 内容区域
var rectTop, rectLeft, rectName, rectZoom;
var canvas;

rectName = "react_content_box";

function init(opt) {
  box_height = opt.height;
  box_width = opt.width;
  rectTop = (body_height - opt.height) / 2;
  rectLeft = (body_width - opt.width) / 2;
  var textDesc = opt.width+'x'+opt.height
  domEventHandler.onload(textDesc,opt.title);
  canvas = new fabric.Canvas("c", {
    width: body_width,
    height: body_height
  });
  var clipingRect = new fabric.Rect({
    top: rectTop,
    left: rectLeft,
    height: opt.height,
    width: opt.width,
    fill: "white",
    name: rectName,
    selectable: false
  });
  // 40自身高度, 95是距离
  $tool.style.top = (body_height - opt.height) / 2 - 40 - 95;
  canvas.backgroundColor = "rgba(0,0,0,.6)";
  canvas.setBackgroundImage(clipingRect);
  canvas.renderAll();
  fabricEvent();
  holdControls();
  if (opt.img) {
    var imgObj = new Image();
    imgObj.crossOrigin = "Anonymous";
    imgObj.src = opt.img;
    imgObj.onload = function () {
      cleanObject();
      var size = util.resizeScale({
        width: imgObj.width,
        height: imgObj.height
      });

      var image = new fabric.Image(imgObj);
      image
        .setControlVisible("ml", false)
        .setControlVisible("mt", false)
        .setControlVisible("mr", false)
        .setControlVisible("mb", false);
      image.set({
        angle: 0,
        padding: 10,
        cornersize: 10,
        scaleY: size.h,
        scaleX: size.w,
        hasRotatingPoint: false,
        globalCompositeOperation: "source-atop"
      });
      canvas.centerObject(image);
      canvas.add(image);
      canvas.setActiveObject(image);
    };
  }
}

function fabricEvent() {
  // 选中置顶
  canvas.on("object:selected", function (obj) {
    var activeObj = canvas.getActiveObject();
    canvas.bringToFront(activeObj).renderAll();
  });
  // 对选中的图片进行放大
  canvas.on("mouse:wheel", function (opt) {
    var delta = opt.e.deltaY;
    var activeObj = canvas.getActiveObject();
    if (activeObj) {
      var zx = activeObj.get("scaleX") + delta / 200;
      var zy = activeObj.get("scaleY") + delta / 200;
      var x = zx > 20 ? 20 : zx < 0.01 ? 0.01 : zx;
      var y = zy > 20 ? 20 : zy < 0.01 ? 0.01 : zy;
      activeObj.set({
        scaleY: y,
        scaleX: x
      });
      canvas.renderAll();
    }
    opt.e.preventDefault();
    opt.e.stopPropagation();
  });
}

var util = {
  resizeScale: function (opt) {
    var w, h;
    var s = opt.width / opt.height;
    if (opt.width > opt.height) {
      h = box_height;
      w = h * s;
    } else {
      w = box_width;
      h = w / s;
    }
    // 进行转换后判断时候宽度是否版本
    if (w < box_width) {
      w = box_width;
      h = w / s;
    }
    return { w: w / opt.width + 0.005, h: h / opt.height + 0.005 };
  }
};
var domEventHandler = {
  upload: function (e) {
    document.getElementById("file").onclick = function () {
      this.value = null;
    };
    document.getElementById("file").onchange = function (e) {
      var reader = new FileReader();
      reader.onload = function (event) {
        var imgObj = new Image();
        imgObj.src = event.target.result;
        imgObj.crossOrigin = "Anonymous";
        imgObj.onload = function () {
          cleanObject();
          var size = util.resizeScale({
            width: imgObj.width,
            height: imgObj.height
          });

          var image = new fabric.Image(imgObj);
          image
            .setControlVisible("ml", false)
            .setControlVisible("mt", false)
            .setControlVisible("mr", false)
            .setControlVisible("mb", false);
          image.set({
            angle: 0,
            padding: 10,
            cornersize: 10,
            scaleY: 1, // || size.h,
            scaleX: 1, // || size.w,
            hasRotatingPoint: false,
            globalCompositeOperation: "source-atop"
          });
          canvas.centerObject(image);
          canvas.add(image);
          canvas.setActiveObject(image);
        };
      };
      if (fileType.indexOf(e.target.files[0].type) != -1) {
        reader.readAsDataURL(e.target.files[0]);
      } else {
        alert("文件格式不符合要求,请上传jpg或者png图片");
      }
    };
  },
  submit: function (e) {
    var $loading = document.getElementById("loading");
    document.getElementById("submit").onclick = function () {
      if (canvas._objects.length === 0) {
        alert("请先上传图片");
        return;
      }
      $loading.style.display = "block";
      window.parent
        .submit(
          canvas.toDataURL({
            left: rectLeft + 1,
            top: rectTop + 1,
            width: box_width - 1,
            height: box_height - 1,
            format: "png"
          })
        )
        .catch(function(e){
          // alert(e);
          console.error(e);
          $loading.style.display = "none";
        });
    };
  },
  remove: function (e) {
    document.getElementById("remove").onclick = function () {
      var activeObj = canvas.getActiveObject();
      if (activeObj) {
        canvas.remove(activeObj);
      }
    };
  },
  cancel: function (e) {
    document.getElementById("cancel").onclick = function () {
      window.parent.cancel();
    };
  },
  onload: function (text,title) {
    document.getElementById("size").innerText = text;
    document.getElementById("title").innerText = title || "";
  },
  zoom: function (e) {
    document.getElementById("zoom").onchange = function (e) {
      rectZoom = (this.value / 100).toFixed(2);
      canvas.setBackgroundImage(
        new fabric.Rect({
          top: (rectTop * rectZoom) / 2 + rectTop,
          left: (rectLeft * rectZoom) / 2 + rectLeft,
          height: box_height,
          width: box_width,
          fill: "white",
          name: rectName,
          scaleY: rectZoom,
          scaleX: rectZoom,
          selectable: false
        })
      );
      canvas.renderAll();
    };
  },

  register: function () {
    domEventHandler.upload();
    domEventHandler.submit();
    domEventHandler.remove();
    domEventHandler.cancel();
    // domEventHandler.zoom();
  }
};

domEventHandler.register();

function holdControls() {
  canvas.on("mouse:down", function (e) {
    canvas._objects.forEach(function (o) {
      canvas.setActiveObject(o);
    });
  });
}

function cleanObject() {
  canvas._objects.forEach(function (o) {
    canvas.remove(o);
  });
}
