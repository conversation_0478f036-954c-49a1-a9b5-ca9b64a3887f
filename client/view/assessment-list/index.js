import React, {Component} from 'react';
import {
	Form,
	Input,
	Select,
	Button,
	message as Message,
	DatePicker
} from 'antd';
import moment from 'moment';
import TableView from './table';
import {
	getConditionList,
	activityPushStart
} from 'client/apis/questionnaire';
import {
	deleteDraft,
	deleteActive,
} from 'client/apis/activity';
import './index.less';

const {Option} = Select;
const statusOptions = [
	'全部',
	'活动中',
	'审批中',
	'未通过',
	'已结束',
	'未开始',
	'草稿'
];

class Class extends Component {
	constructor(props) {
    super(props);
    const org_id = parseInt(typeof window !== 'undefined' && window.sessionStorage.getItem('_oid') || 0);
    const root_oid = parseInt(typeof window !== 'undefined' && window.sessionStorage.getItem('_root_oid') || 0);
		this.state = {
			total: 0,
			pages: 0,
			dataList: [],
      loading: false,
      org_id,
      root_oid,
			params: {
				type: 6,
				page: 1,
				status: '',
				pagesize: 5,
				keyword: '',
				timepoint: '',
				search_flag: true
			}
		}
	}

	componentDidMount () {
		this.onLoadData().catch(e => e);
	}

	/**
	 * @description 搜索
	 */
	onHandleSubmit () {
		const {form: {validateFields}} = this.props;
		const {params} = this.state;
		validateFields((err, {
			status,
			keyword,
			timepoint = ''
		}) => {
			if (!err) {
				this.setState({
					params: {
						...params,
						page: 1,
						status: status ? status : '',
						keyword,
						timepoint: timepoint
							? moment(timepoint).format('YYYY-MM-DD HH:mm:ss')
							: ''
					},
					loading: true
				}, () => this.onLoadData().catch(e => e))
			}
		});
	}

	/**
	 * @description 加载数据
	 * @return {Promise<void>}
	 */
	async onLoadData () {
		const {params} = this.state;
		const {
			data: {
				code = 0,
				total = 0,
				pages = 0,
				data = []
			}
		} = await getConditionList({...params});
		code === 0 && this.setState({
			total,
			pages,
			loading: false,
			dataList: data
		});
	}

	/**
	 * @description 删除草稿
	 * @param param
	 * @return {Promise<void>}
	 */
	async onDeleteDraft (param) {
		const {data: {code, message}} = await deleteDraft(param);
		await this.setState({
			loading: false
		}, () => {
			code === 0 ? Message.success('删除成功') : Message.error('删除失败');
			this.onLoadData().catch(e => e);
		})
	}

	/**
	 * @description 删除活动
	 * @param param
	 * @return {Promise<void>}
	 */
	async onDeleteActive (param) {
		const {data: {code, message}} = await deleteActive(param);
		await this.setState({
			loading: false
		}, () => {
			code === 0 ? Message.success('删除成功') : Message.error(message);
			this.onLoadData().catch(e => e);
		})
	}

	/**
	 * @description 启动考核
	 * @param param
	 * @return {Promise<void>}
	 */
	async onStart (param) {
		const {data: {code, message}} = await activityPushStart(param);
		await this.setState({
			loading: false
		}, () => {
			code === 0 ? Message.success('启动成功') : Message.error('启动失败');
			this.onLoadData().catch(e => e);
		})
	}

	/**
	 * @description 删除活动
	 * @param create_user
	 * @param status
	 * @param activity_id
	 * @param type
	 */
	onDelete ({status, activity_id, type}) {
		this.setState({
			loading: true
		}, () => {
			if (status === 6) {
				this.onDeleteDraft({draft_id: activity_id}).catch(e => e)
			} else {
				this.onDeleteActive({
					type,
					id: activity_id
				}).catch(e => e)
			}
		})
	}

	render () {
		const {history, form: {getFieldDecorator}} = this.props;
		const {params, org_id, root_oid} = this.state;
		return (
			<div className="assessment-list">
				<div className="header"><span>考核评价</span></div>
				<div className="content">
					{org_id === root_oid && (
						<div className="add">
							<Button
								type="primary"
								size="large"
								icon="plus"
								onClick={() => history.push('/activity-template')}
							>新增考核评价</Button>
						</div>
					)}
					<div className="filter">
						<Form
							layout='inline'
							onSubmit={e => {
								e.preventDefault();
								this.onHandleSubmit()
							}}
						>
							<Form.Item label="状态">
								{getFieldDecorator('status', {initialValue: 0})(
									<Select style={{width: 150}}>
										{statusOptions.map((item, index) => <Option key={index} value={index}>{item}</Option>)}
									</Select>
								)}
							</Form.Item>
							<Form.Item label="关键词">
								{getFieldDecorator('keyword')(<Input type="text" placeholder="互动标题" style={{width: 150}}/>)}
							</Form.Item>
							<Form.Item>
								{getFieldDecorator('timepoint')(<DatePicker showTime placeholder="互动时间" style={{width: 150}}/>)}
							</Form.Item>
							<Form.Item>
								<Button type="primary" htmlType="submit">查询</Button>
							</Form.Item>
						</Form>
					</div>
					<div className="table">
						<TableView
							{...this.state}
							history={history}
							onChange={val => this.setState({
								loading: true,
								params: {...params, page: val}
							}, (
								) => this.onLoadData().catch((e
								) => e))}
							onDelete={data => this.onDelete(data)}
							onOutput={(data
								) => this.setState({loading: true}, (
								) => this.onStart(data).catch((e
								) => e))
							}
						/>
					</div>
				</div>
			</div>
		)
	}
}

export default Form.create()(Class)
