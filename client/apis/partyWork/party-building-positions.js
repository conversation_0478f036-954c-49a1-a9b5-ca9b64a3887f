const { http, filterParamsValue } = require("client/tool/axios");
import { ucHost } from "../config";

/**
 * 400020306001-新建党建阵地
 * @param {Object} data
 * @returns
 */
//400020306001-新建党建阵地
const addPartyPositions = (data) => {
  return http.post(`${ucHost}/partyPositions/add`, filterParamsValue(data));
};
// 400020306005-党建阵地列表
const getPartyPositionsList = (org_id) => {
  return http.get(`${ucHost}/partyPositions/list?org_id=${org_id}`);
};
/**
 * 400020306004-删除党建阵地
 * @param {string} org_id 阵地所属组织编号
 * @param {string} party_position_id 阵地编号
 * @returns
 */
// 400020306004-删除党建阵地
const delPartyPosition = (org_id, party_position_id) => {
  return http.get(
    `${ucHost}/partyPositions/del?org_id=${org_id}&party_position_id=${party_position_id}`
  );
};
// 400020306002-编辑党建阵地
const editPartyPositions = (data) => {
  return http.post(`${ucHost}/partyPositions/edit`, filterParamsValue(data));
};
// 400020306005-党建阵地列表第一条
const getPartyPositionsOne = (data) => {
  return http.get(`${ucHost}/partyPositions/findOne?org_id=${data}`);
};

// 400020308-确认推优阵地 http://wiki.aidangqun.com/project/4?p=815
const setPartyPositionsRecommend = (params) => {
  return http.get(`${ucHost}/partyPositions/recommend`, params);
};

// 400020309-取消推优阵地 http://wiki.aidangqun.com/project/4?p=816
const setQuitRecommend = (params) => {
  return http.get(`${ucHost}/partyPositions/quit-recommend`, params);
};

// 4010202020801-查看推优标准 http://wiki.aidangqun.com/project/4?p=810
const searchStandar = (params) => {
  return http.get(`${ucHost}/push-excellent/list`, params);
};

export {
  addPartyPositions,
  editPartyPositions,
  getPartyPositionsList,
  delPartyPosition,
  getPartyPositionsOne,
  setPartyPositionsRecommend,
  setQuitRecommend,
  searchStandar
};
