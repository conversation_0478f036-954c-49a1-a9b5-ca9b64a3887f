import React, {Component} from 'react';
import {
	Form,
	Modal,
	Alert,
	Button,
	DatePicker
} from 'antd';
import moment from 'moment';

class Class extends Component {
	constructor(props) {
		super(props);
		this.state = {
			startValue: '',
			endValue: '',
			endOpen: false,
		}
	}

	disabledStartDate (startValue) {
		const endValue = this.state.endValue;
		if (!startValue || !endValue) {
			return false;
		}
		return startValue.valueOf() > endValue.valueOf();
	}

	disabledEndDate (endValue) {
		const startValue = this.state.startValue;
		if (!endValue || !startValue) {
			return false;
		}
		return endValue.valueOf() <= startValue.valueOf();
	};

	render() {
		const {form: {getFieldDecorator}} = this.props;
		const {
			visible,
			onCancel,
			activeTime: {start = '', end = ''}
		} = this.props;
		const { startValue, endValue, endOpen } = this.state;
		return (
			<Modal
				title="开展时间"
				visible={visible}
				footer={false}
				onCancel={() => onCancel && onCancel()}
			>
				<Alert
					message={
						<span style={{color: '#FF4D4F'}}>在活动时间：{start} 至 {end} 范围内</span>
					}
					type="warning"
				/>
				<div>
					<Form>
						<Form.Item
							label="开展时间"
						>
							<span>
								<DatePicker
									disabledDate={val => this.disabledStartDate(val)}
									showTime
									format="YYYY-MM-DD HH:mm:ss"
									placeholder="Start"
									value={startValue}
								/>
				        <DatePicker
					        disabledDate={val => this.disabledEndDate(val)}
					        showTime
					        format="YYYY-MM-DD HH:mm:ss"
					        value={endValue}
					        placeholder="End"
				        />
							</span>
						</Form.Item>
					</Form>
				</div>
				<div>
					<Button type="primary">确定</Button>
					<Button type="default">上一步</Button>
					<Button type="default">取 消</Button>
				</div>
			</Modal>
		)
	}
}

export default Form.create()(Class);
