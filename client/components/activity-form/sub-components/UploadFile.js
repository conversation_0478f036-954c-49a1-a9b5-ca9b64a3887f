/**
 * 上传文件
 * <AUTHOR>
 * 2018/4/25
 */
import React from 'react'
import { Upload, message } from 'antd'
import PropTypes from 'prop-types'

import { fileHost, uploadHost } from 'apis/config'
import { headers, formHeaders } from 'client/tool/axios'

class UploadFile extends React.Component {
    render() {
        const { onUploading, onSuccess, disabled, onError, upType, ...props } = this.props
        const uploadConfig = {
            disabled,
            supportServerRender: true,
            name: 'upfile',
            accept: 'image/*',
            action: `${uploadHost}/file/upload`,
            headers: formHeaders(),
            onChange(info) {
                if (info.file.status !== 'uploading') {
                    onUploading()
                }
                if (info.file.status === 'done') {
                    if (+info.file.response.code !== 0) {
                        message.error(`上传失败：${info.file.response.message}`)
                    }
                    onSuccess(info)
                } else if (info.file.status === 'error') {
                    message.error('网络错误，上传失败')
                    onError(info)
                }
            }
        }
        return <Upload {...uploadConfig} {...props} style={{ width: '100%' }} />
    }
}

UploadFile.propTypes = {
    data: PropTypes.object,
    onUploading: PropTypes.func,
    onSuccess: PropTypes.func,
    onError: PropTypes.func,
    beforeUpload: PropTypes.func
}

UploadFile.defaultProps = {
    data: { upType: 'image' },
    onUploading: () => { },
    onSuccess: () => { },
    onError: () => { }
}

export default UploadFile
