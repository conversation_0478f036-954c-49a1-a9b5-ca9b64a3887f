/**
 * 标题字数显示提示
 * <AUTHOR>
 * 2018/4/27
 */
import React from 'react'
import { Tooltip } from 'antd'
import './style.less'
export default ({ overlayClassName, placement, text, max, container, children, ...props }) => { 
    const wordCount = parseInt(max) - (text || '').length;
    const isWordFull = wordCount >= 0;
    const wordColor = isWordFull ? '#8FC31F' : '#f5222d';
    return <Tooltip
        title={<span>{isWordFull ? '还可以输入' : '已超出'}<span style={{color: wordColor}}>{Math.abs(wordCount)}</span>个字</span>}
        placement={placement || 'topRight'}
        getPopupContainer={container}
        overlayClassName={overlayClassName || "customize-title-tips"}
        trigger={'focus'}
        {...props}>
        {children}
    </Tooltip>
}