import React from "react";
import PropTypes from "prop-types";
import { Modal, Icon, Button } from "antd";

import "./index.less";

/**
 * @description 弹框表格组件
 */
class WinTableModal extends React.Component {

  /**
   * @description 关闭弹框
   * @param type
   */
  hideModal (type) {
    this.props.hideModal(type)
  }

  render () {
    const {
      title = "",
      visible = false,
      children = [],
      showCancel = false,
      showConfirm = false,
    } = this.props;
    const modalWidth = 800;
    const buttonConfirm = <Button type="primary" onClick={ this.hideModal.bind(this, 'confirm') }>确定</Button>;
    const buttonCancel = <Button type="primary" onClick={ this.hideModal.bind(this, 'cancel') }>关闭</Button>;

    return (
      <div>
        <Modal
          align={ null }
          width={ modalWidth }
          footer={ null }
          visible={ visible }
          centered={ true }
          closable={ false }
          bodyStyle={{ padding: 0 }}
          maskClosable={ false }
          onOk={ this.hideModal.bind(this, 'confirm') }
          onCancel={ this.hideModal.bind(this, 'cancel') }
        >
          <div className="w-header">
            <span>
              { title }
            </span>
            <Icon
              type="close"
              onClick={ this.hideModal.bind(this, 'cancel') }
            />
          </div>
          <div className="w-content">
            { children }
          </div>
          <div className="w-footer">
            { showConfirm && buttonConfirm }
            { showCancel && buttonCancel }
          </div>
        </Modal>
      </div>
    )
  }
}

WinTableModal.propTypes = {
  title: PropTypes.string,
  visible: PropTypes.bool,
  hideModal: PropTypes.func,
  showCancel: PropTypes.bool,
  showConfirm: PropTypes.bool
};


export default WinTableModal;
