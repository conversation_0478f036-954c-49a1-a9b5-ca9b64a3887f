import CryptoJS from 'crypto-js';

//本文件主要用于请求参数加密处理
// Create Base64 Object
export const Base64 = {
    _keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    encode: function (e) {
        let t = '';
        let n, r, i, s, o, u, a;
        let f = 0;
        e = Base64._utf8_encode(e);
        while (f < e.length) {
            n = e.charCodeAt(f++);
            r = e.charCodeAt(f++);
            i = e.charCodeAt(f++);
            s = n >> 2;
            o = (n & 3) << 4 | r >> 4;
            u = (r & 15) << 2 | i >> 6;
            a = i & 63;
            if (isNaN(r)) {
                u = a = 64
            } else if (isNaN(i)) {
                a = 64
            }
            t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
        }
        return t
    },
    decode: function (e) {
        let t = '';
        let n, r, i;
        let s, o, u, a;
        let f = 0;
        e = e.replace(/[^A-Za-z0-9\+\/\=]/g, '');
        while (f < e.length) {
            s = this._keyStr.indexOf(e.charAt(f++));
            o = this._keyStr.indexOf(e.charAt(f++));
            u = this._keyStr.indexOf(e.charAt(f++));
            a = this._keyStr.indexOf(e.charAt(f++));
            n = s << 2 | o >> 4;
            r = (o & 15) << 4 | u >> 2;
            i = (u & 3) << 6 | a;
            t = t + String.fromCharCode(n);
            if (u != 64) {
                t = t + String.fromCharCode(r)
            }
            if (a != 64) {
                t = t + String.fromCharCode(i)
            }
        }
        t = Base64._utf8_decode(t);
        return t
    },
    _utf8_encode: function (e) {
        e = e.replace(/\r\n/g, '\n');
        let t = '';
        for (let n = 0; n < e.length; n++) {
            let r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r)
            } else if (r > 127 && r < 2048) {
                t += String.fromCharCode(r >> 6 | 192);
                t += String.fromCharCode(r & 63 | 128)
            } else {
                t += String.fromCharCode(r >> 12 | 224);
                t += String.fromCharCode(r >> 6 & 63 | 128);
                t += String.fromCharCode(r & 63 | 128)
            }
        }
        return t
    },
    _utf8_decode: function (e) {
        let t = '';
        let n = 0;
        let r = c1 = c2 = 0;
        while (n < e.length) {
            r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r);
                n++
            } else if (r > 191 && r < 224) {
                c2 = e.charCodeAt(n + 1);
                t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                n += 2
            } else {
                c2 = e.charCodeAt(n + 1);
                c3 = e.charCodeAt(n + 2);
                t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                n += 3
            }
        }
        return t
    }
}
/**  * javascript sdbm hash算法  */
export const sdbm = (str) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        let cr = str.charCodeAt(i);
        hash = cr + (hash << 6) + (hash << 16) - hash;
        hash = hash & 0x7FFFFFFF;
        hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
}

/**
 * 通过hash 对  queryString 进行加密 ，并生成新的queryString
 * @param {string} keyHex
 * @param {string} queryString
 */
export const resetParms = (keyHex, queryString) => {
    let parameters = new URLSearchParams(queryString);
    //Iterate the search parameters.
    let parms = new URLSearchParams();
    for (let p of parameters) {
        parms.append(p[0], p[1] ? Base64.encode(des(keyHex, p[1])) : '');
    }
    for (let p of parms) {
        console.log(p[0], '=', Base64.decode(p[1]));
    }
    // console.log(parms);
    return parms.toString();
}
/**
 *
 * @param {string} url 请求的完整url(不包含域名/ip部分)，如 http://a.b.com/api/user/get/1/?c=1&c=2&pagesize=999&oid&xx=ABC&aaa=中文 中的 /api/user/get/1/?c=1&c=2&pagesize=999&oid&xx=ABC&aaa=中文
 * @param {string} body json body，如果有
 */
export const hash = (url, body) => {
    if (!url.startsWith('/')) {
        url = '/' + url;
    }
    let str = url + (body || '');
    // console.log(str);
    return sdbm(str);
}

/**
 * 通过 hashkey 算出8位的加密key
 * @param {any} key
 */
export const getKey = (key, isAES = false) => {
    const len = isAES ? 32 : 8;
    key = '' + key;
    if (key.length < len) {
        key = padNumber(key, len);
    } else if (key.length > len) {
        key = key.substring(0, len);
    }
    return key;
}
/**
 * 调用CryptoJS进行des
 * @param {string} key
 * @param {string} message
 */
export const des = (key, message) => {
    // console.log(key);
    let keyHex = CryptoJS.enc.Utf8.parse(key);
    let encrypted = CryptoJS.DES.encrypt(message, keyHex, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
}
/**
 * 调用 CryptoJS继续AES（AES/CBC/PKCS5Padding）
 * @param {string} key
 * @param {string} message
 * @param {string} iv 16位iv
 */
export const aes = (key, message, iv) => {
    let keyHex = CryptoJS.SHA256(key);
    //CryptoJS.enc.Utf8.parse(key);
    iv = CryptoJS.enc.Utf8.parse(iv);
    let encrypted = CryptoJS.AES.encrypt(message, keyHex, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    console.log("aes -> ", encrypted.toString());
    return encrypted.toString();
}
/**
 * 给数组字符串补零（不支持负数）
 * @param {any} num 需要补的值
 * @param {number} fill 多少位
 */
export const padNumber = (num, fill) => {
    //改自：http://blog.csdn.net/aimingoo/article/details/4492592
    let len = ('' + num).length;
    return (Array(
        fill > len ? fill - len + 1 || 0 : 0
    ).join(0) + num);
}
