import React from "react";
import { Form, Input, Button, message as Message, DatePicker, Radio } from "antd";

import ReportContent from "components/report-content";
import InputTips from "components/activity-form/sub-components/InputTips";
import ShowUploadFileType from "components/show-upload-file-type";
import FileUpload from "components/file-upload";
import OrganizeSelector from "components/organize-selector";

import moment from "moment";
import propTypes from "prop-types";

const FormItem = Form.Item;
const TextArea = Input.TextArea;
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker
class IndexView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  // componentDidMount() {
  //   console.log(this.props);
  // }

  render() {
    const {
      state,
      formItemLayout,
      updateState,
      addATopic,
      updateATopic,
      form,
      history
    } = this.props;
    const { getFieldDecorator, getFieldValue, setFields, validateFields, setFieldsValue } = form;

    const submitHandler = (e) => {
      e.preventDefault();
      validateFields((err, values) => {
        if (!err) {
          if (state.orgs.length <= 0 && state.status !== 2) {
            return Message.error("请选择接收组织");
          } else {
            // 目前默认一个内容项目为填写内容，名称为任务名称
            updateState({
              contents: [{
                type: 1,
                name: state.name,
              }]
            }, () => {
                const { state } = this.props;
                const time = values.time
              // 新增议题得参数
              const queryData = {
                name: state.name, // 议题名称
                description: state.description, // 议题描述
                files: state.files, // 附件
                contents: state.contents, // 内容
                start_time: time[0].format("YYYY-MM-DD"), // 开始时间
                end_time: time[1].format("YYYY-MM-DD"), // 结束时间
                orgs: state.orgs, // 组织 id 数组
                status: state.status, // 上级还是下级提交
                notice_type: values.notice_type
              }
              /* if (values.start_time) {
                queryData.start_time = values.start_time.format("YYYY-MM-DD");
              }
              if (values.end_time) {
                queryData.end_time = values.end_time.format("YYYY-MM-DD");
              } */
              if (values.tlt) {
                queryData.tlt = values.tlt;
              }
              if (state.isAdd) {
                // 新增议题
                addATopic(queryData);
              } else {
                // 修改原有议题
                updateATopic(Object.assign({}, queryData, { topic_id: state.topic_id }));
              }
            });
          }
        }
      });
    };

    const initialTime = (tlt, state) => {
      // console.log(tlt,  state);
      let result = null;
     /*  const allowTlt = {
        1: (state) => {
          return {
            disabled: true,
            start_time: moment(),
            end_time: moment().endOf("month"),
          }
        },
        2: (state) => {
          return {
            disabled: true,
            start_time: moment(),
            end_time: moment().endOf("quarter"),
          }
        },
        3: (state) => {
          return {
            disabled: true,
            start_time: moment(),
            end_time: moment().endOf("year"),
          }
        },
        4: (state) => {
          return {
            disabled: false,
            start_time: state.start_time ? moment(state.start_time, "YYYY-MM-DD") : null,
            end_time: state.end_time ? moment(state.end_time, "YYYY-MM-DD") : null,
          }
        },
      }; 
      result = allowTlt[tlt](state);*/
      switch (tlt) {
        case 1:
          result = [moment(), moment().endOf("month")] 
          break;
        case 2:
          result = [moment(), moment().endOf("quarter")] 
          break;
        case 3:
          result = [moment(), moment().endOf("year")] 
          break;
        case 4:
          result = [
            state.start_time ? moment(state.start_time, "YYYY-MM-DD") : null,
            state.end_time ? moment(state.end_time, "YYYY-MM-DD") : null
          ] 
          break;
      
        default:
          break;
      }
      return result;
    }
    // console.log(state);

    return (
      <div className="index-view-container">
        <Form
          hideRequiredMark={true}
        // onSubmit={submitHandler}
        >
          <FormItem
            // label="议题名称"
            label="标题"
            {...formItemLayout}
          >
            <InputTips
              max={state.topicNameSize}
              // container={container}
              text={getFieldValue("name")}
            >
              {getFieldDecorator("name", {
                initialValue: state.name,
                rules: [
                  { required: true, message: "请输入标题" },
                  {
                    max: state.topicNameSize,
                    message: `最多输入${state.topicNameSize}个汉字`
                  }
                ]
              })(<Input placeholder="请输入" rows={4} onBlur={(e) => updateState({ name: e.target.value })} />)}
            </InputTips>
          </FormItem>
          <FormItem
            label="接收组织"
            {...formItemLayout}
            className="no-margin"
          >
            {
              (state && state.status === 1) &&
              <OrganizeSelector
                inputData={state.orgs}
                canEdit={state && state.status === 1}
                getSelectedValues={(data) => updateState({ orgs: data })}
                ref={ref => this.child = ref}
              />
            }
            {
              (state && state.status === 2) &&
              <OrganizeSelector
                inputData={state.orgs}
                canEdit={false}
                getSelectedValues={(data) => updateState({ orgs: data })}
                ref={ref => this.loginOrg = ref}
              />
            }
          </FormItem>
          {/* <FormItem
            // label="议题内容"
            label="任务内容"
            {...formItemLayout}
            className="no-margin contents-wrapper"
          >
            {
              getFieldDecorator("contents", {})(
                <input type="hidden" />
              )
            }
            <ReportContent
              ref={(ref) => {
                this.reportContent = ref;
              }}
              inputData={state.contents}
              afterChange={() => {
                // 清除contents的
                setFields({ ["contents"]: { value: "", errors: [] } });
              }}
              onChange={(contents) => {
                // {type: 1, name: "任务名称"};
                // console.log(contents);
                updateState({
                  contents
                });
              }}
            />
          </FormItem> */}
          <FormItem
            // label="完成期限"
            // label="任务有效期"
            label="时间"
            style={{marginBottom: 0}}
            {...formItemLayout}>
            {
              getFieldDecorator("tlt", {
                initialValue: state.tlt || 1
              })(
                <RadioGroup
                  onChange={({target}) => {
                    const value = target.value;
                    const time = initialTime(value, state);
                    // console.log(value, result);
                    // setFieldsValue({ "start_time": result.start_time });
                    // setFieldsValue({ "end_time": result.end_time });
                    setFieldsValue({time});
                    updateState({
                      tlt: value,
                      start_time: time[0],
                      end_time: time[1]
                    });
                  }}
                >
                  <Radio value={1}>本月内</Radio>
                  <Radio value={2}>本季度内</Radio>
                  <Radio value={3}>本年内</Radio>
                  <Radio value={4}>自定义时间</Radio>
                </RadioGroup>
              )
            }
          </FormItem>
          <FormItem
            wrapperCol={{span: 15, offset: 2}}
          >
            {getFieldDecorator("time", {
              initialValue: initialTime(getFieldValue("tlt"), state),
              rules: [{ required: true, message: "请选择时间" }]
            })(
              <RangePicker disabled={state.tlt !== 4}/>
            )}
          </FormItem>
          {/* <FormItem
            label="开始时间"
            {...formItemLayout}
          >
            {getFieldDecorator("start_time", {
              initialValue: initialTime(getFieldValue("tlt"), state).start_time,
              // state.start_time === "" || state.start_time === undefined ? null : moment(state.start_time, "YYYY-MM-DD"),
              rules: [{ required: true, message: "请选择开始时间" }]
            })(
              <DatePicker
                disabled={initialTime(getFieldValue("tlt"), state).disabled}
                onChange={(date) => updateState({ start_time: moment(date).format("YYYY-MM-DD") })}
              />
            )}
          </FormItem>
          <FormItem
            label="截止时间"
            {...formItemLayout}
          >
            {getFieldDecorator("end_time", {
              initialValue: initialTime(getFieldValue("tlt"), state).end_time,
              // state.end_time === "" || state.end_time === undefined ? null : moment(state.end_time, "YYYY-MM-DD"),
              rules: [{ required: true, message: "请选择截止时间" }]
            })(
              <DatePicker
                disabled={initialTime(getFieldValue("tlt"), state).disabled}
                onChange={(date) => updateState({ end_time: moment(date).format("YYYY-MM-DD") })}
              />
            )}
          </FormItem> */}
          <FormItem
            label="通知"
            {...formItemLayout}>
            {
              getFieldDecorator("notice_type", {
                initialValue: state.notice_type || 2
              })(
                <RadioGroup disabled={!!state.topic_id}>
                  <Radio value={2}>微信通知</Radio>
                  <Radio value={1}>短信通知</Radio>
                </RadioGroup>
              )
            }
          </FormItem>
          <FormItem
            // label="关联附件"
            // label="相关资料"
            label="附件资料"
            {...formItemLayout}
            className="uploadWrap"
          >
            {getFieldDecorator("files", {})(
              <FileUpload
                // max={9}
                uploadedList={state.files}
                onChange={(files) => {
                  // console.log(files);
                  updateState({
                    files
                  });
                }} />
            )}
            {/* style={{ marginTop: 20 }} */}
            <div>
              <ShowUploadFileType data={state.files}
                updateState={(data) => updateState({ files: data })}
                loading={true}
                isDelete={true} />
            </div>

            {/* <div style={{ position: "relative" }}>
              {
                uploading &&
                <LoadingModal {...loadingModalProps} />
              }
              {getFieldDecorator("files", {})(
                <Upload className="upload" {...uploadProps}>
                  <a className="uploadAtarg" onClick={(e) => {
                    // console.log(state.files);
                    if (state.files && state.files.length >= maxAmount) {
                      e.stopPropagation();
                      Message.warn(`限定上传${maxAmount}个文件`);
                    }
                  }}>上传附件</a>
                </Upload>
              )}
              <div style={{ marginTop: 28 }}>
                <ShowUploadFileType data={state.files} updateState={(data) => updateState({ files: data })} loading={state.uploadLoading} isDelete={true} />
              </div>
            </div> */}

          </FormItem>
          <FormItem
            // label="议题描述"
            label="备注"
            {...formItemLayout}
          >
            <InputTips
              max={state.topicDescSize}
              // container={container}
              text={getFieldValue("description")}
            >
              {getFieldDecorator("description", {
                initialValue: state.description,
                rules: [
                  {
                    max: state.topicDescSize,
                    message: `最多输入${state.topicDescSize}个汉字`
                  }
                ]
              })(<TextArea placeholder="请输入" autoSize={{minRows: 3}} onBlur={(e) => updateState({ description: e.target.value })} />)}
            </InputTips>
          </FormItem>
          <FormItem wrapperCol={{ offset: 2 }}>
            <Button
              className="btnWrapper"
              // htmlType={"submit"}
              onClick={submitHandler}
              type="primary"
              loading={state.submitLoading}
            >
              提交
            </Button>
            <Button
              onClick={() => {
                history.goBack();
              }}
              loading={state.submitLoading}
              style={{ marginLeft: 28 }}
              className="btnWrapper">
              取消
            </Button>
          </FormItem>
        </Form>
      </div>
    )
  }
}

IndexView.propTypes = {
  state: propTypes.object,
  formItemLayout: propTypes.object,
  updateState: propTypes.func,
  form: propTypes.object,
  addATopic: propTypes.func,
  updateATopic: propTypes.func,
  history: propTypes.object
};

IndexView.defaultProps = {
  state: {},
  formItemLayout: {},
  updateState: () => { },
  form: {},
  addATopic: () => { },
  updateATopic: () => { },
  history: {}
};

export default IndexView;