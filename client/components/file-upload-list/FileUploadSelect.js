import React, { PureComponent } from 'react'
import { Button } from 'antd';
import SelfIcon from '../self-icon';

export class FileUploadSelect extends PureComponent {
  constructor(props) {
    super(props);
    this.onChange = this.onChange.bind(this)
  }
  onChange(e) {
    const { disabled, onInputChage } = this.props;
    if (disabled) return;
    onInputChage(e);
    e.target.value = '';
  };
  render() {
    const {
      showTip,
      maxSize,
      text,
      tip,
      listType,
      accept,
      multiple,
      children,
      disabled,
      limit,
      currentLength,
    } = this.props;
    let realityDisabled = disabled;
    if (limit) {
      realityDisabled = currentLength >= limit || disabled;
    }
    let realityTip = [];
    if (tip) realityTip.push(tip);
    if (maxSize) realityTip.push(`单个文件大小${maxSize}M以内`)
    if (limit) realityTip.push(`最多允许上传${limit}个文件`)
    realityTip = realityTip.join("，")
    return (
      <div className="file-upload-select">
        {listType === 'text' ? (
          <div className="file-upload-btn">
            <Button className="file-upload-btn" type="primary" disabled={realityDisabled}>
              {text}
              <input type="file" accept={accept} multiple={multiple} onChange={this.onChange} />
            </Button>
            {showTip && (
              <p className="tip-text">
                { realityTip}
              </p>
            )}
            {children}
          </div>
        ) : (
          <div>
            <div className="file-upload-card">
              {children}
              {!realityDisabled && (
                <div
                  className={`file-upload-card-btn file-upload-card-item ${realityDisabled ? 'file-upload-card-btn-disable' : ''
                    }`}
                >
                  <input
                    type="file"
                    accept={accept}
                    multiple={multiple}
                    onChange={this.onChange}
                    disabled={realityDisabled}
                  />
                  <SelfIcon className="file-upload-card-icon" type="gsg-tianjia" />
                  {text}
                </div>
              )}
            </div>
            {showTip && (
              <p className="tip-text">
                { realityTip}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
}

FileUploadSelect.defaultProps = {
  multiple: false,
};

export default FileUploadSelect
