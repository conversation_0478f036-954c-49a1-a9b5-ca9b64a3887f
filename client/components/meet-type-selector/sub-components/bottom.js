import React from "react";
import { Table, Checkbox } from "antd";

export default (props) => {
  const {
    dataSource,
    loading,
    checkedList,
    checkHandler
  } = props;
  const isChecked = (record) => {
    const result = false;
    if (checkedList && Array.isArray(checkedList)) {
      const index = checkedList.findIndex((item) => {
        return item.type_id === record.type_id;
      });
      return index !== -1;
    }
    return result;
  }
  const columns = [
    {
      align: "center",
      dataIndex: "checkbox",
      title: "选择",
      width: 60,
      render(text, record, index) {
        return (
          <Checkbox
            checked={isChecked(record)}
            onChange={(e) => {
              const target = e.target;
              const isCheck = target.checked;
              checkHandler && checkHandler(record, isCheck);
            }} />
        )
      }
    },
    {
      align: "center",
      dataIndex: "index",
      title: "序号",
      width: 80,
      render(text, record, index) {
        return index + 1;
      }
    },
    {
      align: "center",
      dataIndex: "type",
      title: "活动类型",
      width: 300
    },
    {
      align: "center",
      dataIndex: "category",
      title: "所属类别",
      width: 300
    }
  ];
  const tableProps = {
    rowKey: "type_id",
    bordered: true,
    loading,
    columns,
    dataSource,
    pagination: {
      pageSize: 5,
      showQuickJumper: true,
      size: "small"
    }
  };
  return (
    <div className="bottom-container">
      <Table {...tableProps} />
    </div>
  )
}
