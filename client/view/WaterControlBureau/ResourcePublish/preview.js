import React, { Component } from "react";
import { Mobile } from "apis/config";

export default props => {
  const {
    data,
    src,
    close
  } = props
  const bgimg = require('./iphone.png')
  return (
    <div className="mobile-preview">
      <div className="mark-layout" onClick={close}></div>
      <div className="box" style={{
        backgroundImage: `url(${bgimg})`
        }}>
        <iframe src={`${Mobile}${src}?params=${data}`} frameborder="0" />
      </div>
    </div>
  );
};
