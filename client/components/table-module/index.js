import React, {Component} from 'react';
import {Table} from 'antd';
import './index.less';
import PropTypes from 'prop-types';

const TableModule = ({
                         tableDataSource,
                         columns,
                         index
                     }) => {
    // console.log('表格组件收到了数据');
    //有下载操作的列

    if (!tableDataSource) {
        tableDataSource = [];
    }
    if (!columns) {
        columns = [];
    }
    if (!index) {
        index = 0;
    }

    //存在合并的属性有：subject,path2
    let subjectRowSpanStatus = {},
        path3RowSpanStatus = {};
    if (Array.isArray(tableDataSource) && tableDataSource.length !== 0) {
        tableDataSource.forEach((item, index) => {
            //只有选项栏目才存在行列合并
            if (item.rowType === 'option') {
                if (item.subject) {
                    if (!subjectRowSpanStatus[item.subject]) {
                        let subjectRowSpan;
                        if (item.level4RowSpan) {
                            subjectRowSpan = item.level2RowSpan;
                        } else if (item.level3RowSpan) {
                            subjectRowSpan = item.level2RowSpan;
                        } else if (item.level2RowSpan) {
                            subjectRowSpan = 1;
                        }
                        subjectRowSpanStatus[item.subject] = subjectRowSpan;
                        item.subjectRowSpan = subjectRowSpan;
                    } else {
                        item.subjectRowSpan = 0;
                    }
                }
                if (item.path4 && item.path4 !== '') {
                    let contentRowSpan;
                    contentRowSpan = item.level3RowSpan;
                    if (!path3RowSpanStatus[item.path2 + item.path3]) {
                        path3RowSpanStatus[item.path2 + item.path3] = contentRowSpan;
                        item.contentRowSpan = contentRowSpan;
                    } else {
                        item.contentRowSpan = 0;
                    }
                }
            }
        });
    }
    // console.log(subjectRowSpanStatus);

    return (
        <div className={'table-module'}>
            {/*{JSON.stringify(tableDataSource)}*/}
            <div className={'table-module-wrapper'}>
                <aside className={'type-wrapper'}>
                    <div>
                        <span>
                            {tableDataSource.length === 0 ? '-' : (index || index === 0) ? index + 1 + '.' : ''}
                            {tableDataSource.length !== 0 && (tableDataSource[0] ? tableDataSource[0].typeName || tableDataSource[0].activity_title : '-')}
                        </span>
                    </div>
                </aside>
                <section className={'table-wrapper'}>
                    <Table
                        rowKey="index"
                        columns={columns}
                        dataSource={tableDataSource}
                        pagination={false}
                        bordered
                    />
                </section>
            </div>
        </div>
    )
};

//参数校验
TableModule.propTypes = {
    tableDataSource: PropTypes.array,//表格组件使用的数据
    columns: PropTypes.array,//表格组件使用的列配置项
    index: PropTypes.number//外层如果是列表渲染，传入index值
};

export default TableModule;