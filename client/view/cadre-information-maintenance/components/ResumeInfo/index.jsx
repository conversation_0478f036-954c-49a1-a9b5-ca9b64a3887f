import { Button, Col, Form, Input, Modal, Popconfirm, Row, Select, Table, message } from "antd";
import { deleteWorkInfo, queryByCode } from "client/apis/cadre-portrait";
import { uuid } from "client/tool/uuid";
import DateSingle from "components/date-single/DateSingle";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from "react";
import "./index.less";
function index(props, ref) {
  const { getFieldDecorator, data, disabled } = props;
  const formRef = useRef({});
  const formRef1 = useRef({});

  const [workResume, setWorkResume] = useState([
    {
      key: uuid(),
    },
  ]);
  const [workResumeRemark, setWorkResumeRemark] = useState([
    {
      key: uuid(),
    },
  ]);

  const [codeMap, setCodeMap] = useState({
    jobLevelOption: [],
    jobCategoryOption: [],
    unitPropertyOption: [],
    //简历分类
    resumeCategoryOption: [
      {
        op_key: 0,
        op_value: "工作简历",
      },
      // {
      //   op_key: 1,
      //   op_value: "其他",
      // },
      {
        op_key: 2,
        op_value: "特殊标识",
      },
    ],
  });
  const modalFormRef = useRef(null);
  const [tbleData, setTbleData] = useState([])
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [modalType, setModalType] = useState("add");
  const [type, setType] = useState('')
  const Card = ({ title, children }) => {
    return (
      <div className="edu-card">
        <div className="gap">{children}</div>
      </div>
    );
  };

  useImperativeHandle(ref, () => ({
    getData: () => {
      let workResumedata = []
      let workResumeRemarkdata = []
      tbleData.forEach((item) => {
        if (item.remark == 1) {
          item.position = item.position_or_content
          item.pms_work_resume_id = item.id
          workResumedata.push(item)
        }
        if (item.remark == 2) {
          item.content = item.position_or_content
          item.pms_work_resume_remark_id = item.id
          workResumeRemarkdata.push(item)
        }
      })
      return {
        workResume: workResumedata,
        workResumeRemark: workResumeRemarkdata,
      };
    },
  }));

  useEffect(() => {
    if (data) {
      // const { work_resume, work_resume_remark } = data;

      // if (work_resume && work_resume.length) {
      //   const data = work_resume.map((item) => {
      //     const newItem = { ...item, key: uuid() };
      //     // 过滤不可渲染的数据类型
      //     for (const key in newItem) {
      //       if (typeof newItem[key] === 'function' || typeof newItem[key] === 'symbol') {
      //         delete newItem[key];
      //       }
      //     }
      //     return newItem;
      //   });

      // setWorkResume(data);
      setTbleData(data)
      // }

      // if (work_resume_remark && work_resume_remark.length) {
      //   const data = work_resume_remark.map((item) => {
      //     return { ...item, key: uuid() };
      //   });

      //   setWorkResumeRemark(data);
      // }
    }
  }, [data]);
  useEffect(() => {
    initCode(95103, "jobLevelOption");
    initCode(95104, "jobCategoryOption");
    initCode(95105, "unitPropertyOption");
  }, []);

  const initCode = async (code, key) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      setCodeMap((codeMap) => {
        codeMap[key] = res.data.data;

        return { ...codeMap };
      });
    }
  };
  const getData = () => {
    const _formRef = formRef.current;

    // const data = [];

    // Object.keys(_formRef).forEach((item) => {
    //   _formRef[item] &&
    //     _formRef[item].validateFields((err, values) => {
    //       data.push(values);
    //     });
    // });

    // return data;
  };

  const getData1 = () => {
    const _formRef = formRef1.current;

    const data1 = [];

    Object.keys(_formRef).forEach((item) => {
      _formRef[item] &&
        _formRef[item].validateFields((err, values) => {
          data1.push(values);
        });
    });

    return data1;
  };
  const onAddEdu = () => {
    setEducation([...getData, {}]);
  };
  const onAddEdu1 = () => {
    setFullTimeEdu([...fullTimeEdu, {}]);
  };

  const AddClose = ({ onAdd, onDelete }) => {
    return (
      <div className="operation">
        <svg
          t="1714460317123"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="9415"
          width="32"
          height="32"
          onClick={onAdd}
        >
          <path
            d="M542.72 732.16c0 20.48-15.36 35.84-35.84 35.84-20.48 0-35.84-15.36-35.84-35.84v-179.2h-179.2c-10.24 0-20.48-5.12-25.6-10.24-15.36-15.36-15.36-35.84 0-51.2 5.12-5.12 15.36-10.24 25.6-10.24h179.2v-179.2c0-20.48 15.36-35.84 35.84-35.84 20.48 0 35.84 15.36 35.84 35.84v179.2h179.2c20.48 0 35.84 15.36 40.96 30.72 0 20.48-15.36 35.84-30.72 40.96h-184.32v179.2h-5.12z m327.68-568.32c-194.56-194.56-512-194.56-706.56 0s-194.56 512 0 706.56c194.56 194.56 512 194.56 706.56 0 194.56-194.56 194.56-512 0-706.56z"
            fill="#17abe3"
            p-id="9416"
          ></path>
        </svg>
        <svg
          t="1714460360376"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="14688"
          width="32"
          height="32"
          onClick={onDelete}
        >
          <path
            d="M512 0C228.677818 0 0 228.677818 0 512s228.677818 512 512 512 512-228.677818 512-512S795.322182 0 512 0z"
            fill="#F5684C"
            p-id="14689"
          ></path>
          <path
            d="M281.832727 290.816a59.717818 59.717818 0 0 0 4.142546 78.010182L428.357818 512l-142.382545 143.173818a59.578182 59.578182 0 0 0 0 82.711273l4.654545 4.189091c22.667636 18.152727 57.111273 16.756364 77.963637-4.189091l142.149818-142.987636 142.196363 142.94109c20.852364 20.992 55.296 22.388364 77.963637 4.189091l9.262545-8.331636c21.410909-21.736727 19.781818-59.019636-6.516363-80.244364L593.128727 512l142.429091-143.173818a59.578182 59.578182 0 0 0 0-82.711273l-4.654545-4.189091a59.159273 59.159273 0 0 0-77.963637 4.189091L510.743273 429.149091 368.593455 286.161455a59.066182 59.066182 0 0 0-82.618182 0l-4.189091 4.654545z"
            fill="#FFFFFF"
            p-id="14690"
          ></path>
        </svg>
      </div>
    );
  };
  const FormRow = Form.create()(
    ({ form, data = {}, onBlur, onAdd, onDelete }) => {
      const { getFieldDecorator, setFieldsValue } = form;

      const _onBlur = () => {
        onBlur && onBlur();
      };
      return (
        <Row span={24} type="flex" align="middle">
          {getFieldDecorator("key", {
            initialValue: data.key,
          })(<input type="hidden" disabled={disabled} />)}
          {getFieldDecorator("pms_work_resume_id", {
            initialValue: data.pms_work_resume_id,
          })(<input type="hidden" disabled={disabled} />)}
          <Col span={3} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">开始时间</div>}
              <div className="form-content">
                {getFieldDecorator("start_time", {
                  initialValue: data.start_time || undefined,
                  onChange: (val) => {
                    setFieldsValue({ start_time: val });
                    _onBlur();
                  },
                })(
                  <DateSingle
                    type="month"
                    inputWidth="75px"
                    placeholder="请选择"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col className="margin-right-10">
            <div className="form-col">
              {!disabled && (
                <div className="form-label" style={{ opacity: 0 }}>
                  占
                </div>
              )}

              <div
                className="form-content"
                style={{
                  marginTop: disabled ? "0px" : undefined,
                }}
              >
                <div className="form-driver">-</div>
              </div>
            </div>
          </Col>
          <Col span={3} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">结束时间</div>}
              <div className="form-content">
                {getFieldDecorator("end_time", {
                  initialValue: data.end_time || undefined,
                  onChange: (val) => {
                    setFieldsValue({ end_time: val });
                    _onBlur();
                  },
                })(
                  <DateSingle
                    type="month"
                    inputWidth="75px"
                    onOk={_onBlur}
                    placeholder="请选择"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">职务</div>}
              <div className="form-content">
                {getFieldDecorator("position", {
                  initialValue: data.position,
                })(
                  <Input
                    onBlur={_onBlur}
                    placeholder="请输入"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">工作单位</div>}
              <div className="form-content">
                {getFieldDecorator("work_unit", {
                  initialValue: data.work_unit,
                })(
                  <Input
                    onBlur={_onBlur}
                    placeholder="请输入"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">职务层级</div>}
              <div className="form-content">
                {getFieldDecorator("job_level", {
                  initialValue: data.job_level,
                })(
                  <Select
                    placeholder="请选择"
                    disabled={disabled}
                    onBlur={_onBlur}
                  >
                    {codeMap.jobLevelOption.map((item) => {
                      return (
                        <Option value={Number(item.op_key)}>
                          {item.op_value}
                        </Option>
                      );
                    })}
                  </Select>
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">职务类别</div>}
              <div className="form-content">
                {getFieldDecorator("job_category", {
                  initialValue: data.job_category,
                })(
                  <Select
                    placeholder="请选择"
                    disabled={disabled}
                    onBlur={_onBlur}
                  >
                    {codeMap.jobCategoryOption.map((item) => {
                      return (
                        <Option value={Number(item.op_key)}>
                          {item.op_value}
                        </Option>
                      );
                    })}
                  </Select>
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">干部身份</div>}
              <div className="form-content">
                {getFieldDecorator("unit_property", {
                  initialValue: data.unit_property,
                })(
                  <Select
                    placeholder="请选择"
                    disabled={disabled}
                    onBlur={_onBlur}
                  >
                    {codeMap.unitPropertyOption.map((item) => {
                      return (
                        <Option value={Number(item.op_key)}>
                          {item.op_value}
                        </Option>
                      );
                    })}
                  </Select>
                )}
              </div>
            </div>
          </Col>
          <Col span={2} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">分管领域（工作）</div>}
              <div className="form-content">
                {getFieldDecorator("areas_responsibility", {
                  initialValue: data.areas_responsibility,
                })(
                  <Input
                    onBlur={_onBlur}
                    placeholder="请输入"
                    initialValue={data.areas_responsibility}
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col span={3} className="margin-right-10">
            <div className="form-col">
              {!disabled && <div className="form-label">备注说明</div>}
              <div className="form-content">
                {getFieldDecorator("remarks", {
                  initialValue: data.remarks,
                })(<Input disabled={disabled} onBlur={_onBlur} />)}
              </div>
            </div>
          </Col>

          <Col span={1} className="margin-right-10">
            {!disabled && (
              <div class="button-box">
                <svg
                  onClick={onAdd}
                  t="1715392676445"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="4273"
                  width="16"
                  height="16"
                >
                  <path
                    d="M928.78 339.61A449.35 449.35 0 1 0 964 514a446.24 446.24 0 0 0-35.22-174.39zM713 544H546v167a30 30 0 0 1-60 0V544H319a30 30 0 0 1 0-60h167V317a30 30 0 0 1 60 0v167h167a30 30 0 0 1 0 60z"
                    fill="#0368CD"
                    p-id="4274"
                  ></path>
                </svg>
                <svg
                  t="1715392815192"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="9085"
                  width="16"
                  height="16"
                  onClick={() => {
                    onDelete(data.key);
                  }}
                >
                  <path
                    d="M782.246469 240.527099c36.608779 36.608779 64.905242 79.291904 84.184331 126.794801 18.651802 45.9004 28.084639 94.451162 28.010961 144.263661 0.069585 49.808406-9.359158 98.363262-28.015054 144.260591-19.279089 47.50392-47.569412 90.189092-84.175121 126.793778-36.606733 36.606733-79.294974 64.900126-126.797871 84.179214-45.896306 18.654872-94.451162 28.084639-144.259568 28.015054-49.808406 0.069585-98.365308-9.358135-144.259568-28.015054-47.50392-19.277042-90.192162-47.569412-126.800941-84.179214-36.604686-36.603663-64.895009-79.289857-84.173075-126.793778-18.656919-45.895283-28.084639-94.452185-28.015054-144.260591-0.072655-49.812499 9.359158-98.363262 28.010961-144.263661 19.280112-47.501874 47.575552-90.186022 84.183308-126.793778 36.604686-36.604686 79.286788-64.899102 126.789685-84.178191 45.9004-18.651802 94.451162-28.084639 144.263661-28.011984 49.813522-0.072655 98.363262 9.360182 144.264685 28.011984C702.959681 175.626973 745.642806 203.922413 782.246469 240.527099M827.505255 195.268312C652.829957 20.593014 369.558335 20.593014 194.883037 195.269335 20.202623 369.94975 20.201599 653.220349 194.876897 827.895647c174.681438 174.681438 457.952037 174.679391 632.632451 0C1002.18567 653.220349 1002.18567 369.94975 827.505255 195.268312L827.505255 195.268312 827.505255 195.268312zM557.129338 513.486864 716.737476 353.87975l-45.222971-45.223994L511.906367 468.263893 352.762811 309.12136l-45.344744 45.344744 159.142533 159.143557L309.697993 670.472268l45.201481 45.201481 156.86363-156.86363 157.358911 157.356864 45.333488-45.333488L557.129338 513.486864zM714.465736 670.824285"
                    fill="#d81e06"
                    p-id="9086"
                  ></path>
                </svg>
              </div>
            )}
          </Col>
        </Row>
      );
    }
  );

  const FormRow1 = Form.create()(
    ({ data = {}, onAdd, onBlur, onDelete, form }) => {
      const { getFieldDecorator, setFieldsValue } = form;
      const _onBlur = () => {
        onBlur && onBlur();
      };
      return (
        <Row span={24} type="flex" align="middle">
          {getFieldDecorator("key", {
            initialValue: data.key,
          })(<input type="hidden" />)}
          {getFieldDecorator("pms_work_resume_remark_id", {
            initialValue: data.pms_work_resume_remark_id,
          })(<input type="hidden" />)}
          {getFieldDecorator("user_id", {
            initialValue: data.user_id,
          })(<input type="hidden" disabled={disabled} />)}
          {getFieldDecorator("pms_work_resume_id", {
            initialValue: data.pms_work_resume_id,
          })(<input type="hidden" disabled={disabled} />)}
          <Col span={3} className="margin-right-10">
            <div className="form-col">
              <div className="form-label">开始时间</div>
              <div className="form-content">
                {getFieldDecorator("start_time", {
                  initialValue: data.start_time || undefined,
                  onChange: (val) => {
                    setFieldsValue({ start_time: val });
                    _onBlur();
                  },
                })(
                  <DateSingle
                    type="month"
                    inputWidth="75px"
                    onOk={_onBlur}
                    placeholder="请选择"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col className="margin-right-10">
            <div className="form-col">
              {!disabled && (
                <div className="form-label" style={{ opacity: 0 }}>
                  占
                </div>
              )}

              <div
                className="form-content"
                style={{
                  marginTop: disabled ? "0px" : undefined,
                }}
              >
                <div className="form-driver">-</div>
              </div>
            </div>
          </Col>
          <Col span={3} className="margin-right-10">
            <div className="form-col">
              <div className="form-label">结束时间</div>
              <div className="form-content">
                {getFieldDecorator("end_time", {
                  initialValue: data.end_time || undefined,
                  onChange: (val) => {
                    setFieldsValue({ end_time: val });
                    _onBlur();
                  },
                })(
                  <DateSingle
                    type="month"
                    inputWidth="75px"
                    onOk={_onBlur}
                    placeholder="请选择"
                    className="form-input"
                    disabled={disabled}
                  />
                )}
              </div>
            </div>
          </Col>
          <Col span={13}>
            <div className="form-col">
              <div className="form-label">详情内容</div>
              <div className="form-content">
                {getFieldDecorator("content", {
                  initialValue: data.content,
                })(<Input placeholder="请输入" disabled={disabled} onBlur={_onBlur} />)}
              </div>
            </div>
          </Col>
          {!disabled && (
            <Col span={3} className="margin-right-10">
              <div className="button-box">
                <svg
                  onClick={onAdd}
                  t="1715392676445"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="4273"
                  width="16"
                  height="16"
                >
                  <path
                    d="M928.78 339.61A449.35 449.35 0 1 0 964 514a446.24 446.24 0 0 0-35.22-174.39zM713 544H546v167a30 30 0 0 1-60 0V544H319a30 30 0 0 1 0-60h167V317a30 30 0 0 1 60 0v167h167a30 30 0 0 1 0 60z"
                    fill="#0368CD"
                    p-id="4274"
                  ></path>
                </svg>
                <svg
                  t="1715392815192"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="9085"
                  width="16"
                  height="16"
                  onClick={() => {
                    onDelete(data.key);
                  }}
                >
                  <path
                    d="M782.246469 240.527099c36.608779 36.608779 64.905242 79.291904 84.184331 126.794801 18.651802 45.9004 28.084639 94.451162 28.010961 144.263661 0.069585 49.808406-9.359158 98.363262-28.015054 144.260591-19.279089 47.50392-47.569412 90.189092-84.175121 126.793778-36.606733 36.606733-79.294974 64.900126-126.797871 84.179214-45.896306 18.654872-94.451162 28.084639-144.259568 28.015054-49.808406 0.069585-98.365308-9.358135-144.259568-28.015054-47.50392-19.277042-90.192162-47.569412-126.800941-84.179214-36.604686-36.603663-64.895009-79.289857-84.173075-126.793778-18.656919-45.895283-28.084639-94.452185-28.015054-144.260591-0.072655-49.812499 9.359158-98.363262 28.010961-144.263661 19.280112-47.501874 47.575552-90.186022 84.183308-126.793778 36.604686-36.604686 79.286788-64.899102 126.789685-84.178191 45.9004-18.651802 94.451162-28.084639 144.263661-28.011984 49.813522-0.072655 98.363262 9.360182 144.264685 28.011984C702.959681 175.626973 745.642806 203.922413 782.246469 240.527099M827.505255 195.268312C652.829957 20.593014 369.558335 20.593014 194.883037 195.269335 20.202623 369.94975 20.201599 653.220349 194.876897 827.895647c174.681438 174.681438 457.952037 174.679391 632.632451 0C1002.18567 653.220349 1002.18567 369.94975 827.505255 195.268312L827.505255 195.268312 827.505255 195.268312zM557.129338 513.486864 716.737476 353.87975l-45.222971-45.223994L511.906367 468.263893 352.762811 309.12136l-45.344744 45.344744 159.142533 159.143557L309.697993 670.472268l45.201481 45.201481 156.86363-156.86363 157.358911 157.356864 45.333488-45.333488L557.129338 513.486864zM714.465736 670.824285"
                    fill="#d81e06"
                    p-id="9086"
                  ></path>
                </svg>
              </div>
            </Col>
          )}
        </Row>
      );
    }
  );

  const onAdd = (type) => {
    // setWorkResume([
    //   ...getData(),
    //   {
    //     key: uuid(),
    //   },
    // ]);
    setType(type);
    setVisible(true);
    setModalType("add");
  };

  const onDelete = async (item, index) => {

    let newdata = [...tbleData]

    if (item.id) {
      // 有 charge_range_id 调用接口删除
      try {
        const res = await deleteWorkInfo({
          [item.remark == 1 ? "work_resume_id" : "work_resume_remark_id"]: item.id,
        });

        if (res.data.code === 0) {
          message.success("删除成功！");
          if (index >= 0 && index < newdata.length) {
            newdata.splice(index, 1);
          }
          setTbleData(newdata);
        } else {
          message.error(res.data.message);
        }
      } catch (error) {
        message.error("删除失败，请稍后重试！");
      }
    } else {
      // 没有 charge_range_id 直接删除
      if (index >= 0 && index < newdata.length) {
        newdata.splice(index, 1);
      }
      setTbleData(newdata);
    }

  };

  const onBlur = () => {
    console.log(...getData());
    setWorkResume([...getData()]);
  };
  const onBlur1 = () => {
    setWorkResumeRemark([...getData1()]);
  };

  const onDeleteWorkInfo = async ({ type, id, key }) => {
    const res = await deleteWorkInfo({
      [type === 1 ? "work_resume_id" : "work_resume_remark_id"]: id,
    });

    if (res.data.code === 0 || !id) {
      if (type === 1) {
        if (workResume.length <= 1) {
          return setWorkResume([
            {
              key: uuid(),
            },
          ]);
        }
        // 防止刷新
        const data = getData();

        const index = data.findIndex((item) => item.key === key);

        data.splice(index, 1);

        setWorkResume([...data]);
      } else {
        if (workResumeRemark.length <= 1) {
          return setWorkResumeRemark([
            {
              key: uuid(),
            },
          ]);
        }

        // 防止刷新
        const data = getData1();
        const index = data.findIndex((item) => item.key === key);

        data.splice(index, 1);

        setWorkResumeRemark([...data]);
      }
    }
  };
  // 修改 onEditor 方法
  const onEditor = (record) => {
    // 设置模态框类型为编辑模式
    setModalType("edit");
    // 打开模态框
    setVisible(true);
    setType(record.remark)
    // 通过 ref 获取模态框表单实例并设置数据
    queueMicrotask(() => {
      modalFormRef.current.setFieldsValue(record);
    });
  };
  const modalHandleCancel = () => {
    setVisible(false);
  };
  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        let newTbleData = [...tbleData];
        if (values.id) {
          // 修改操作
          const index = newTbleData.findIndex(item => item.id == values.id);
          if (index !== -1) {
            newTbleData[index] = {
              ...newTbleData[index],
              ...values
            };
          }
        } else {
          if (values.ids) {
            const index = newTbleData.findIndex(item => item.ids == values.ids);
            if (index !== -1) {
              newTbleData[index] = {
                ...newTbleData[index],
                ...values
              };
            }
          } else {
            newTbleData.push({
              ...values,
              remark: type,
              ids: uuid()
            });
          }

        }
        setTbleData(newTbleData);
        setVisible(false);
      }
    });
  }
  const columns = [
    {
      title: "开始时间",
      dataIndex: "start_time",
      key: "start_time",
      width: 200,
      align: "center",

    },
    {
      title: "结束时间",
      dataIndex: "end_time",
      key: "end_time",
      width: 200,
      align: "center",
    },

    {
      title: "职务",
      dataIndex: "position_or_content",
      key: "position_or_content",
      // width: 200,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "工作单位",
      dataIndex: "work_unit",
      key: "work_unit",
      // width: 200,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "职务层级",
      dataIndex: "job_level",
      key: "job_level",
      // width: 200,
      align: "center",
      render: (text) => {
        const item = codeMap.jobLevelOption.find((option) => Number(option.op_key) == text);
        return <div style={{ textAlign: "left" }}>{item ? item.op_value : text}</div>;
      },
    },
    {
      title: "职务类别",
      dataIndex: "job_category",
      key: "job_category",
      // width: 200,
      align: "center",
      render: (text) => {
        const item = codeMap.jobCategoryOption.find((option) => Number(option.op_key) == text);
        return <div style={{ textAlign: "left" }}>{item ? item.op_value : text}</div>;
      },
    },
    {
      title: "干部身份",
      dataIndex: "unit_property",
      key: "unit_property",
      // width: 200,
      align: "center",
      render: (text) => {
        const item = codeMap.unitPropertyOption.find((option) => Number(option.op_key) == text);
        return <div style={{ textAlign: "left" }}>{item ? item.op_value : text}</div>;
      },
    },
    {
      title: "简历分类",
      dataIndex: "type",
      key: "type",
      // width: 200,
      align: "center",
      render: (text) => {
        const item = codeMap.resumeCategoryOption.find((option) => Number(option.op_key) == text);
        return <div style={{ textAlign: "left" }}>{item ? item.op_value : text}</div>;
      },
    },
    // {
    //   title: "分管领域（工作）",
    //   dataIndex: "areas_responsibility",
    //   key: "areas_responsibility",
    //   // width: 200,
    //   align: "center",
    //   render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    // },
    {
      title: "备注说明",
      dataIndex: "remarks",
      key: "remarks",
      // width: 200,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record, index) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDelete(record, index)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];
  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, getFieldValue, setFieldsValue, setFields } = form;
    const formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("pms_work_resume_id")(<input type="hidden" />)}
        {getFieldDecorator("id")(<input type="hidden" />)}
        {getFieldDecorator("ids")(<input type="hidden" />)}
        <Form.Item label="开始时间">
          {getFieldDecorator("start_time", {
            rules: [{ required: true, message: "请选择开始时间" }],
          })(<DateSingle disabled={disabled} placeholder="请选择" type="month" isWrap={false} />)}
        </Form.Item>
        <Form.Item label="结束时间">
          {getFieldDecorator("end_time", {
            rules: [{ required: false, message: "请选择开始时间" }],
          })(<DateSingle disabled={disabled} placeholder="请选择" type="month" isWrap={false} />)}
        </Form.Item>
        {
          type == 1 ?
            <span>
              <Form.Item label="职务">
                {getFieldDecorator("position_or_content", {
                  rules: [{ required: true, message: "请输入职务" }],
                })(<Input disabled={disabled} placeholder="请输入" allowClear />)}
              </Form.Item>
              <Form.Item label="工作单位">
                {getFieldDecorator("work_unit", {
                  rules: [{ required: true, message: "请输入工作单位" }],
                })(<Input disabled={disabled} placeholder="请输入" allowClear />)}
              </Form.Item>
              <Form.Item label="职务层级">
                {getFieldDecorator("job_level", {
                  rules: [{ required: false, message: "请选择职务层级" }],
                })(<Select
                  placeholder="请选择"
                  disabled={disabled}
                  allowClear
                >
                  {codeMap.jobLevelOption.map((item) => {
                    return (
                      <Option value={Number(item.op_key)}>
                        {item.op_value}
                      </Option>
                    );
                  })}
                </Select>)}
              </Form.Item>
              <Form.Item label="职务类别">
                {getFieldDecorator("job_category", {
                  rules: [{ required: false, message: "请选择职务类别" }],
                })(<Select
                  placeholder="请选择"
                  disabled={disabled}
                  allowClear
                >
                  {codeMap.jobCategoryOption.map((item) => {
                    return (
                      <Option value={Number(item.op_key)}>
                        {item.op_value}
                      </Option>
                    );
                  })}
                </Select>)}
              </Form.Item>
              <Form.Item label="干部身份">
                {getFieldDecorator("unit_property", {
                  rules: [{ required: false, message: "请选择干部身份" }],
                })(<Select
                  placeholder="请选择"
                  disabled={disabled}
                  allowClear
                >
                  {codeMap.unitPropertyOption.map((item) => {
                    return (
                      <Option value={Number(item.op_key)}>
                        {item.op_value}
                      </Option>
                    );
                  })}
                </Select>)}
              </Form.Item>
              <Form.Item label="简历分类">
                {getFieldDecorator("type", {
                  rules: [{ required: true, message: "请选择简历分类" }],
                })(<Select
                  placeholder="请选择"
                  disabled={disabled}
                  allowClear
                >
                  {codeMap.resumeCategoryOption.map((item) => {
                    return (
                      <Option value={Number(item.op_key)}>
                        {item.op_value}
                      </Option>
                    );
                  })}
                </Select>)}
              </Form.Item>
              <Form.Item label="备注说明">
                {getFieldDecorator("remarks", {
                  rules: [{ required: false, message: "请输入备注说明" }],
                })(<Input
                  placeholder="请输入"
                  disabled={disabled}
                  allowClear
                />)}
              </Form.Item>
             
            </span>
            :
            <Form.Item label="详情内容">
              {getFieldDecorator("position_or_content", {
                rules: [{ required: true, message: "请输入详情内容" }],
              })(<Input disabled={disabled} placeholder="请输入" allowClear />)}
            </Form.Item>
        }
      </Form>
    );
  });
  return (
    <div className="edu-info">
      {/* <Card>
        {workResume.map((item, index) => {
          return (
            <FormRow
              ref={(ref) => {
                formRef.current[item.key] = ref;
              }}
              key={item.key}
              data={item}
              onAdd={onAdd}
              onBlur={onBlur}
              onDelete={() => {
                onDeleteWorkInfo({
                  type: 1,
                  id: item.pms_work_resume_id,
                  key: item.key,
                });
              }}
            />
          );
        })}
      </Card>
      <div className="btn-box">
        <Button type="primary">其间</Button>
      </div>
      <Card>
        {workResumeRemark.map((item, index) => {
          const onChange = (key, value) => {
            workResumeRemark[index][key] = value;

            setWorkResumeRemark([...workResumeRemark]);
          };

          const onAdd = () => {
            setWorkResumeRemark([...getData1(), { key: uuid() }]);
          };
          const onDelete = (key) => { };
          return (
            <FormRow1
              data={item}
              ref={(ref) => {
                formRef1.current[item.key] = ref;
              }}
              onBlur={onBlur1}
              key={item.key}
              onAdd={onAdd}
              onDelete={() => {
                onDeleteWorkInfo({
                  type: 2,
                  id: item.pms_work_resume_remark_id,
                  key: item.key,
                });
              }}
            />
          );
        })}
      </Card> */}
      <Button onClick={() => onAdd(1)} type="primary" icon="plus" style={{ marginBottom: '10px' }}>
        简历
      </Button>
      <Button onClick={() => onAdd(2)} type="primary" icon="plus" style={{ marginBottom: '10px', marginLeft: "10px" }}>
        期间（特殊备注）
      </Button>
      <Table
        bordered
        loading={loading}
        columns={columns}
        dataSource={tbleData}
      //   pagination={paginationProps}
      />
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "简历信息"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default forwardRef(index);
