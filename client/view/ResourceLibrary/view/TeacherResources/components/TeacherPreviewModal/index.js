import React, { useState } from "react";
import { Modal } from "antd";
import moment from "moment";
import Particulars from "../Particulars";
import { CDN } from "apis/config";
import './index.less'

const tabName = [
  { label: "PC端", key: 1 },
  { label: "移动端", key: 2 },
];
const TeacherPreviewModal = (props) => {
  const {
    visible,
    onCancel,
    previewModalData: {
      name,
      sources,
      content,
      job,
      profession_level,
      belong_unit,
      resource_category_id,
      cover_url,
      contact_details,
      provider_org_id,
      provider_org_name,
      tags,
      resource_type,
    },
  } = props;

  const [tabKey, setTabKey] = useState(1);

  // console.log(props, tags);
  return (
    <Modal
      title="预览"
      className="teacher-preview-modal"
      width={1513}
      maskClosable={false}
      visible={visible}
      footer={false}
      onCancel={onCancel}
    >
      <div className="preview-modal-tab">
        {tabName.map((item) => {
          return (
            <div
              key={item.key}
              className={`preview-modal-tab-item ${tabKey === item.key ? "active" : "inactive"
                }`}
              onClick={() => {
                if (tabKey !== item.key) {
                  setTabKey(item.key);
                }
              }}
            >
              {item.label}
            </div>
          );
        })}
      </div>
      <div className="preview-modal-content">
        {tabKey === 1 ? (
          props.previewModalData && <Particulars previewModalData={props.previewModalData} table={1} />
        ) : (
          <div className="preview-modal-content-mobile">
            <div className="preview-modal-content-mobile-top">
              <div className="top">
                <img className="head-portrait" src={cover_url ? `${CDN}/${cover_url}` : require('../../../../image/ren.png')} />
                <div className="message">
                  <div className="name">{name}</div>
                  <div className="unit">{belong_unit}</div>
                  <div className="rank">
                    <div>职务：{job ? job : '-'}</div>
                  </div>
                  <div className="rank">
                    <div>职级：{profession_level ? profession_level : '-'}</div>
                  </div>
                </div>
              </div>
              <div className="contact">
                <div className="phone">
                  <div>联系方式：</div>
                  <div className="phone-num">
                    {contact_details}
                  </div>
                </div>
                <div className="tags">
                  <div>擅长领域：</div>
                  {tags && tags.split(',').map((item) => {
                    return (
                      <div className="tag">{item}</div>
                    )
                  })}
                </div>
              </div>
            </div>
            <div className="preview-modal-content-mobile-content">
              <div className="bottom">
                <div className="content">
                  {content}
                </div>
                <div className="provide">
                  信息提供：{provider_org_name}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default TeacherPreviewModal;
