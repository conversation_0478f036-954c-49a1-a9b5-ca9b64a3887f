import { http } from 'tool/axios';
import { ucHost } from 'apis/config';
//批量导入使用接口

//下载导入模板接口
export const fetchDownloadTemplateUrl = () => {
  return http.get(`${ucHost}/org/user/excelTemplate`);
}

//获取导入解析数据
export const importInit = (fileId) => {
  return http.get(`${ucHost}/org/user/importInit`, { fileId });
}

//轮询接口，询问数据表解析是否已经完成
export const polling = (result_key) => {
  return http.get(`${ucHost}/org/user/getOperKey`, { result_key });
}

//开始导入
export const startImport = (operKey, targetOid, currentHandlePage, pageSize) => {
  return http.get(`${ucHost}/org/user/importOrgUser`, { operKey, orgId: targetOid, page: currentHandlePage, pagesize: pageSize });
}

//单条数据导入
export const importSingle = ({ orgId, name, phone, certNumber, certType, politicalType, jobGrade, position, communist, youthLeague, unionMember, womenLeague }) => {
  return http.get(`${ucHost}/org/user/submitData`, { orgId, name, phone, certNumber, certType, politicalType, jobGrade, position, communist, youthLeague, unionMember, womenLeague });
}

//获取解析出待上传数据分页
export const fetchImportDataByPage = ({ operKey, page, pagesize }) => {
  // console.log({ operKey, page, pagesize });
  return http.get(`${ucHost}/org/user/importData`, { operKey, page, pagesize });
}