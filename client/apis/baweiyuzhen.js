import { crHost,baweiyuzhenAccessKey } from "./config";
import { http, headers } from "client/tool/axios";

/** 微信页面部署查询 http://************:8090/pages/viewpage.action?pageId=34441025 */
export const getData  = (params = {}) => http.get(`${crHost}/poverty/wechat/deploy/query`, params,{headers: {...headers(),...baweiyuzhenAccessKey}});

/** 微信页面部署操作 http://************:8090/pages/viewpage.action?pageId=34441027 */
export const editData  = (data = {}) => http.post(`${crHost}/poverty/wechat/deploy/edit`, data,{headers: {...headers(),...baweiyuzhenAccessKey}});


/** ---------------------专题页设置-------------------- */
//1061070208-微信页面专题信息列表查询
export const topicList  = () => http.get(`${crHost}/poverty/wechat/topic/query/list`, {}, {headers: {...headers(),...baweiyuzhenAccessKey}});

//1061070209-微信页面专题信息详情查询（PC端）
export const topicDetail  = (params = {}) => http.get(`${crHost}/poverty/wechat/topic/query/detail`, params,{headers: {...headers(),...baweiyuzhenAccessKey}});

//1061070210-微信页面专题信息编辑
export const topicEdit  = (data = {}) => http.post(`${crHost}/poverty/wechat/topic/edit`, data,{headers: {...headers(),...baweiyuzhenAccessKey}});


