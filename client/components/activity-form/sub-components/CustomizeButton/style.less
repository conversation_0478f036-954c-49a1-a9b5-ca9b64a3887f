.customize-button-icon-button {
    background-color: #FFF1F0;
    color: #FF7875;
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid #FFCCC7;
    padding: 10px 20px;
    transition: 0.3s;
    &>i {
        margin-right: 12px;
        font-weight: 600;
    }
    &+& {
        margin-left: 42px;
    }
    &:active,
    &:visited,
    &:focus {
        outline: none;
    }
    &:hover {
        box-shadow: 0 1px 6px rgba(0, 0, 0, .1)
    }
    &[disabled] {
        color: rgba(0, 0, 0, .25);
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        &:hover {
            box-shadow: none;
            cursor: not-allowed;
        }
    }
}

@circleDefalut: 24px;
@circleSmall: 18px;
.customize-button-circle-button {
    border: none;
    color: #FFF;
    font-size: 20px;
    padding: 0;
    font-family: 'gsg';
    font-weight: 600;
    text-align: center;
    line-height: @circleDefalut;
    width: @circleDefalut;
    height: @circleDefalut;
    border-radius: 50%;
    background: rgba(244, 110, 101, 1);
    &:active,
    &:visited,
    &:focus {
        outline: none;
    }
    &::before {
        width: @circleDefalut;
        height: @circleDefalut;
        display: block;
    }
    &:hover {
        cursor: pointer;
        background-color: rgba(254, 120, 101, 1);
        box-shadow: 0 1px 6px rgba(0, 0, 0, .1)
    }
    &.small {
        font-size: 12px;
        line-height: @circleSmall;
        width: @circleSmall;
        height: @circleSmall;
        &::before {
            width: @circleSmall;
            height: @circleSmall;
        }
    }
    &[disabled] {
        background-color: #BBB;
        &:hover {
            box-shadow: none;
            cursor: not-allowed;
        }
    }
}