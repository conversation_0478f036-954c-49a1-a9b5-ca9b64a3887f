.data-check-view {
  width: 100%;
  min-width: 850px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-x: auto;
  .check-view {
    text-align: center;

    img {
      width: 201px;
      height: 201px;
    }

    .check-text {
      font-size: 20px;
      font-weight: 400;
      color: #020202;
      line-height: 23px;
    }

    .btn-box {
      margin-top: 40px;

      button {
        width: 140px;
      }
    }

    .checking-text {
      font-size: 20px;
    }
  }

  .abnormal-view {
    padding: 0 24px;
    width: 100%;
    height: 100%;

    .abv__header-box {
      display: flex;
      align-items: center;
      padding: 0 45px 0 60px;
      margin-top: 37px;
      width: 100%;

      img {
        width: 109px;
        height: 109px;
      }
    }

    .right__text-box {
      flex: 1;
      display: flex;
      margin-left: 40px;

      .text-box {
        flex: 1;

        .abnormal-count {
          font-size: 24px;
          font-weight: 500;
          color: #f24014;
          line-height: 28px;
        }

        .sub-label {
          font-size: 16px;
          font-weight: 500;
          color: #333333;

          i {
            color: #f24014;
          }
        }
      }

      .operation-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;

        button {
          width: 140px;
        }

        .label-box {
          margin-top: 24px;
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }
    }

    .tabs {
      margin-top: 49px;
      .ant-tabs-bar {
        border-bottom: none;
      }
      .ant-tabs-tab {
        height: 50px;
        font-size: 16px;
        font-weight: 500;
        color: #94a6b7;
        line-height: 19px;
      }
    }

    // .ant-tabs-ink-bar{
    //     width: 60px !important;
    // }
  }
}
