import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Row, Col, Icon, Button, Form, Select, Input, Table, Tabs, Modal, DatePicker
  , message, Checkbox, Popconfirm, AutoComplete, Radio
} from 'antd';
import {
  createCommitteePeriodUrl, getPeriodDetail, getPeriodMember, getOrgCommittee, getExtendInfo,
  addCommitteeMemberUrl, deleteCommitteeMemberUrl, commonJobUrl, changeCommitteeMemberUrl,
  changeCommitteePeriodUrl, getSecretaryList
} from "../../../apis/party-organization";;
import UploadList from "components/upload-list";
import SearchHeader from 'components/search-header';
import PropTypes from "prop-types";
import InputTips from 'components/activity-form/sub-components/InputTips';
import PersonModal from "components/person-selector/sub-components/person-modal";
import moment from 'moment';
import SecretaryFormItem from "./SecretaryFormItem";
// import './index.less';
// import View from './index-view';

const { TextArea } = Input;
const { Option, OptGroup } = Select;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 9 }
};

class AddPeriod extends Component {
  constructor(props) {
    super(props);
    this.state = {
      editIndex: -1,
      type_child_name: this.props.location.state.type_child_name || "党组织",
      operation_type: 1,// 1 添加 2 编辑 3 详情
      op_type: 0,//成员 1 添加 2 编辑
      addModalVisible: false,
      hostUserVisible: false,
      partyPosts: [],//党内职务
      inspectionPartyPosts: [], // 纪委党内职务
      // jobRank: [],//行政职务
      memberData: [],//选择人员
      dataSource: [],//已选人员
      secretaryList: [], // 书记列表
      deputySecretaryList: [], // 副书记列表
      tableData: [],
      param: {},
      is_visible: {},
      files: [],
      org_id: 0,
      type: "1",// 成员类型
      period_id: 0,// 组织换届信息表id
      isParty: false,
      isDiscipline: false,
      position_name: null,
      grade_name: null,
      startMoment: null,//dataPick回显的moment
      endMoment: null,
      pages: 0,
      total: 0,
      page_num: 1,
      loading: false
    };
    this.getSecretaryList = this.getSecretaryList.bind(this);
    this.onSecretartChange = this.onSecretartChange.bind(this);
    this.switchName = this.switchName.bind(this);
    // this.onChangeRadio = this.onChangeRadio.bind(this);
  }
  async componentWillMount() {
    const { match, location: { state } } = this.props;
    const { params } = match;
    if (params && params.id) {
      const type = params.id * 1;
      let param = {};
      if (type === 2 || type === 3) {
        this.getSecretaryList();
        const res = await getPeriodDetail(state.period_id);
        if (res.data.code === 0) {
          param = res.data.data.data;
        }
      }
      const { data: org } = await getOrgCommittee(state.org_id);
      const { data: partyPostsData } = await commonJobUrl({ type: 1 });
      const { data: inspectionPartyPosts } = await commonJobUrl({ type: 1 }, null);
      let source = {
        period_id: state.period_id || -1,
        operation_type: type,
        org_id: state.org_id,
      };
      if (org.code === 0) {
        source.is_visible = org.data;
      }
      if (partyPostsData.code == 0) {
        source.partyPosts = partyPostsData.data;
      }
      this.setState({
        param,
        inspectionPartyPosts: inspectionPartyPosts.data,
        files: param.files,
        ...source
      },() => {
        this.onChangeTableData();
      });

    } else {
      message.error('参数错误');
    }
  }

  getSecretaryList() {
    const { match, location: { state } } = this.props;
    getSecretaryList(state.period_id).then(res => {
      const { data, code, status, } = res.data;
      if (status === 200 && code === 0) {
        this.setState({
          secretaryList: data.boss1 || [],
          deputySecretaryList: data.boss2 || [],
        });
      } else {
        message.error(res.data.message || "请求失败，请联系管理员！");
      }
    });
  }

  // 书记列表改变
  onSecretartChange(list, props) {
    this.setState({[props]: list});
  }

  selectMember() {
    const { history } = this.props;
    history.push('/select-member');
  }

  onSubmit(e) {
    e.preventDefault();
    const { form: { validateFields } } = this.props;
    /* const filess = this.personSelector.exportData()//上传文件得入参
    this.setState({
      files: filess
    }, () => { */
    validateFields(async (errors, values) => {
      if (!errors) {
        if (values.start_time.valueOf() > values.end_time.valueOf()) {
          message.error('开始时间不能在结束时间之后！');
          return;
        }
        const {
          org_id, files, dataSource, operation_type, period_id,
          secretaryList, deputySecretaryList, type_child_name
        } = this.state;
        const start_time = values.start_time.format('YYYY-MM-DD');
        const end_time = values.end_time.format('YYYY-MM-DD');
        const discipline_inspection_committee = values.discipline_inspection_committee || 2;
        const party_affairs_office = values.party_affairs_office || 2;
        const param = {
          org_id,
          start_time,
          end_time,
          explain_str: values.explain_str,
          files,
          discipline_inspection_committee,
          party_affairs_office,
          has_tag: values.has_tag,
        };
        let res = {};
        if (operation_type === 1) {
          let tab1 = 0;
          let tab2 = 0;
          let tab3 = 0;
          for (const [key, item] of dataSource.entries()) {
            if (item.type == 1) {
              tab1 = tab1 + 1;
            }
            if (discipline_inspection_committee === 1 && item.type == 2) {
              tab2 = tab2 + 1;
            } else if (item.type == 2) {//纪律检查委员会选项为无时
              dataSource.splice(key, 1);
            }
            if (party_affairs_office === 1 && item.type == 3) {
              tab3 = tab3 + 1;
            } else if (item.type == 3) {//党务办公室选项为无时
              dataSource.splice(key, 1);
            }
          }
          // if (tab1 == 0) {
          //   message.warn(`'请添加${type_child_name}委员会成员！`);
          //   return;
          // }
          if (discipline_inspection_committee === 1 && tab2 == 0) {
            message.warn('请添加纪律检查委员会成员！');
            return;
          }
          if (party_affairs_office === 1 && tab3 == 0) {
            message.warn('请添加党务办公室成员！');
            return;
          }
          // if (secretaryList && secretaryList.length === 0) {
          //   message.warn(`请选择${type_child_name}书记1`);
          //   return;
          // }
          let list = dataSource.concat(secretaryList);
          list = list.concat(deputySecretaryList);
          param.org_period_member_add_form_list = list;
          res = await createCommitteePeriodUrl(param);
        } else {
          // if (secretaryList && secretaryList.length === 0) {
          //   message.error(`请选择${type_child_name}书记`);
          //   return;
          // }
          param.period_id = period_id;
          res = await changeCommitteePeriodUrl(param);
        }
        if (res.data.code == 0) {
          const { history } = this.props;
          history.replace('/party-organization', { org_id });
        } else {
          message.error(res.data.message);
        }
      } else {
        message.error("请检查必填内容是否完整！");
      }
    });
    // })
  }
  async onChangeMember() {
    const {
      operation_type, op_type, period_id, type, dataSource = [],
      memberData, grade_name, position_name, isParty, isDiscipline, editIndex
    } = this.state;
    if (!memberData.length) {
      message.warn('请选择人员');
      return;
    }
    if (!position_name && String(type) !== "3") {
      message.warn('请选择党内职务');
      return;
    }
    if (op_type === 1) {
      for (const member of memberData) {
        for (const item of dataSource) {
          if (member.user_id == item.user_id && item.type == type) {
            message.error('已添加该成员，不能重复添加！');
            return;
          }
        }
      }
    }
    if (operation_type == 1) {
      const param = {
        user_id: memberData[0].user_id,
        user_name: memberData[0].user_name,
        position_name: position_name || (type === "3" ? "无" : ""),
        grade_name,
        is_full_time_party_cadres: isParty ? 1 : 2,
        is_full_time_discipline_inspection_cadres: isDiscipline ? 1 : 2
      };
      if (op_type === 1) {
        param.key = dataSource.length;
        param.type = type * 1;
        dataSource.push(param);
      } else {
        // 编辑
        dataSource.map((item, index) => {
          if (item.type === type * 1 && item.user_id === memberData[0].user_id) {
            dataSource[index] = {
              type: type * 1,
              key: memberData[0].user_id,
              user_id: memberData[0].user_id,
              user_name: memberData[0].user_name,
              position_name: position_name || (type === "3" ? "无" : ""),
              grade_name,
              is_full_time_party_cadres: isParty ? 1 : 2,
              is_full_time_discipline_inspection_cadres: isDiscipline ? 1 : 2
            };
          }
        });
      }
      // //存在相同的人员则信息同步
      // for (const item of dataSource) {
      //   if (item.user_id === param.user_id) {
      //     item.user_id = param.user_id;
      //     item.user_name = param.user_name,
      //       item.position_name = param.position_name,
      //       item.grade_name = param.grade_name,
      //       item.is_full_time_party_cadres = param.is_full_time_party_cadres,
      //       item.is_full_time_discipline_inspection_cadres = param.is_full_time_discipline_inspection_cadres;
      //   }
      // }
      this.setState({
        dataSource: [...dataSource],
      }, () => {
        this.handleTableData();
        this.clearMenberData();
      });
    } else {
      const param = {
        period_id,
        user_id: memberData[0].user_id,
        user_name: memberData[0].user_name,
        position_name: position_name || (type === "3" ? "无" : ""),
        grade_name,
        type: type * 1,
        is_full_time_party_cadres: isParty ? 1 : 2,
        is_full_time_discipline_inspection_cadres: isDiscipline ? 1 : 2
      };
      let res = {};
      if (op_type == 1) {
        res = await addCommitteeMemberUrl(param);
      } else {
        param.period_member_id = memberData[0].period_member_id;
        res = await changeCommitteeMemberUrl(param);
      }
      if (res.data.code === 0) {
        this.handleTableData();
        this.clearMenberData();
      } else {
        message.error(res.data.message);
      }
    }
  }
  async deleteMember(record) {
    const { operation_type, type } = this.state;
    if (operation_type == 1) {
      const temp_data = this.state.dataSource.filter((item, index) => {
        return item.key != record.key;
      });
      for (const [key, value] of temp_data.entries()) {
        value.key = key;
      }
      this.setState({
        dataSource: temp_data
      }, () => {
        this.handleTableData();
      });
    } else {
      const res = await deleteCommitteeMemberUrl(record.period_member_id);
      if (res.data.code === 0) {
        this.handleTableData();
      } else {
        message.error(res.data.message);
      }
    }

  }
  clearMenberData() {
    this.setState({
      addModalVisible: false,
      memberData: [],
      grade_name: null,
      position_name: null,
      isParty: false,
      isDiscipline: false
    });
  }
  setOptGroup(param) {
    let arr = [];
    for (const [key, value] of Object.entries(param)) {
      arr.push(<OptGroup label={key} key={key}>
        {
          value.map((item, i) => {
            return <Option value={item} key={i}>{item}</Option>;
          })
        }
      </OptGroup>);
    }
    return arr;
  }
  onChangeAtrr(value, attr) {
    this.setState({
      [attr]: value
    });
  }
  setGradeName(value) {
    if (value.length > 50) {
      message.warn('行政职务不能大于50个字符！');
      return;
    }
    this.setState({
      grade_name: value
    });
  }
  editMemberData(record) {
    this.setState({
      memberData: [{ user_id: record.user_id, user_name: record.user_name, period_member_id: record.period_member_id }],
      grade_name: record.grade_name,
      position_name: record.position_name,
      addModalVisible: true,
      isParty: record.is_full_time_party_cadres === 1 ? true : false,
      isDiscipline: record.is_full_time_discipline_inspection_cadres === 1 ? true : false,
      op_type: 2
    });
  }
  async onChangeTableData(type = "1") {
    const { dataSource, period_id } = this.state;
    const data = [...dataSource] || [];
    let total = 0;
    let pages = 0;
    this.setState({
      loading: true
    }, async () => {
      if (period_id !== -1) {
        const res = await getPeriodMember({ type, period_id, page_num: 1 });
        if (res.data.code === 0) {
          total = res.data.total;
          pages = res.data.pages;
          for (const item of res.data.data) {
            item.type = type;
            data.push(item);
          }
        }
      }
      const tableData = [];
      for (const item of data) {
        if (item.type == type) {
          tableData.push(item);
        }
      }
      this.personModal.init([]);
      this.setState({
        tableData,
        total: total ? total : tableData.length,
        pages: pages ? pages : Math.ceil(tableData.length / 10),
        type,
        page_num: 1,
        loading: false
      });
    });
  }
  handleTableData() {
    const { dataSource, period_id, page_num, type } = this.state;
    const data = [...dataSource] || [];
    let total = 0;
    let pages = 0;
    this.setState({
      loading: true
    }, async () => {
      if (period_id !== -1) {
        const res = await getPeriodMember({ type, period_id, page_num });
        if (res.data.code === 0) {
          total = res.data.total;
          pages = res.data.pages;
          for (const item of res.data.data) {
            item.type = type;
            data.push(item);
          }
        }
      }
      const tableData = [];
      for (const item of data) {
        if (item.type == type) {
          tableData.push(item);
        }
      }
      this.setState({
        tableData,
        total: total ? total : tableData.length,
        pages: pages ? pages : Math.ceil(tableData.length / 10),
        type,
        loading: false
      });
    });
  }
  switchName() {
    const { type, type_child_name } = this.state;
    switch (type) {
      case "1":
        return `${type_child_name}委员会`;
      case "2":
        return "纪律检查委员会";
      case "3":
        return "党委办公室";
      default:
        break;
    }
  }
  render() {
    const columns = [{
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: '10%',
      render: (text, record, index) => <span >{index + 1}</span>,
    }, {
      title: '姓名',
      dataIndex: 'user_name',
      align: 'center',
      width: 120
    }];

    if (this.state.type !== "3") {
      columns.push( {
        title: '行政职务',
        dataIndex: 'grade_name',
        align: 'center',
      });
      columns.push({
        title: '党内职务',
        dataIndex: 'position_name',
        align: 'center',
      });
      
    }

    if (this.state.type === "2") {
      columns.push( {
        title: '专职纪检干部',
        dataIndex: 'is_full_time_discipline_inspection_cadres',
        align: 'center',
        render: (text, record, index) => (
          <span>{text == 1 ? "是" : "否"}</span>
        )
      });
    }
    if (this.state.type === "3") {
      columns.push({
        title: '专职党务干部',
        colSpan: this.state.type === "3" ? 1 : 0,
        dataIndex: 'is_full_time_party_cadres',
        align: 'center',
        render: (text, record, index) => (
          <span>{text == 1 ? "是" : "否"}</span>
        )
      });
    }
    columns.push({
      title: '操作',
      key: 'action',
      align: 'center',
      width: '100px',
      render: (text, record, index) => (
        <span>
          <a href="javascript:;" onClick={() => this.editMemberData(record, index)} style={{ marginRight: '15px' }}>编辑 </a>
          <Popconfirm title={operation_type == 2 ? "当前状态确认后无法撤销!" : "是否删除？"} onConfirm={() => this.deleteMember(record)}>
            <a href="javascript:;">删除</a>
          </Popconfirm>
        </span>
      ),
    });

    const {
      type,
      param,
      tableData,
      operation_type,
      addModalVisible,
      op_type,
      hostUserVisible,
      memberData,
      partyPosts,
      inspectionPartyPosts,
      position_name,
      grade_name,
      isParty,
      isDiscipline,
      is_visible,
      total,
      page_num,
      pages,
      loading,
      files,
      org_id,
      period_id,
      secretaryList,
      deputySecretaryList,
      type_child_name,
    } = this.state;
    const { history } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const hostUserModalProps = {
      visible: hostUserVisible,
      radio: true,
      ref: (ref) => {
        this.personModal = ref;
      },
      hideModal: () => {
        this.setState({
          hostUserVisible: false
        });
      },
      // dataSource: memberData,
      title: "选择人员",
      onChange: async (data) => {
        if (data && Array.isArray(data)) {
          data.map((item) => {
            if (!item.user_name) {
              item.user_name = item.name;
              // delete item.name;
            }
          });
        }
        const { data: extendInfo } = await getExtendInfo(data[0].user_id);
        let isDiscipline = false;
        let isParty = false;
        if (extendInfo.code === 0) {
          isDiscipline = extendInfo.data.is_full_time_discipline_inspection_cadres == 1 ? true : false;
          isParty = extendInfo.data.is_full_time_party_cadres == 1 ? true : false;
        }
        this.setState({
          memberData: data,
          isDiscipline,
          isParty
        });
      }
    };
    if (operation_type == 3) {
      columns.splice(columns.length - 1, 1);
    }
    return (
      <div className='period-group'>
        <SearchHeader
          title={`${operation_type === 2 ? "编辑" : ""}${type_child_name}委员会${operation_type === 1 ? '换届' : operation_type === 3 ? "详情" : ""}`}
          // onBack={() => history.goBack()}
          onBack={() => history.replace('/party-organization', { org_id })}
        />
        <div className='period-wrapper'>
          <Form style={{ marginTop: 30 }} onSubmit={(e) => this.onSubmit(e)}>
            <FormItem label="开始时间" {...formItemLayout}>
              {
                operation_type == 3 ?
                  param.start_time
                  :
                  getFieldDecorator('start_time', {
                    initialValue: param.start_time && moment(param.start_time),
                    rules: [
                      { required: true, message: '请选择开始时间' },
                    ],
                  })(
                    <DatePicker style={{ width: '100%' }} placeholder="请选择开始时间" />
                  )
              }
            </FormItem>
            <FormItem label="结束时间" {...formItemLayout}>
              {
                operation_type == 3 ?
                  param.end_time
                  :
                  getFieldDecorator('end_time', {
                    initialValue: param.end_time && moment(param.end_time),
                    rules: [
                      { required: true, message: '请选择结束时间' },
                    ],
                  })(
                    <DatePicker style={{ width: '100% ' }} placeholder="请选择结束时间" />
                  )
              }
            </FormItem>
            <FormItem label="特殊说明" labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              {/* <InputTips max={200} text={getFieldValue('explain_str')} > */}
              {
                operation_type == 3 ?
                  param.explain_str || "暂无"
                  :
                  getFieldDecorator('explain_str', {
                    initialValue: param.explain_str,
                    rules: [{
                      max: 200,
                      message: "内容不能超过200个字"
                    }]
                  })(
                    <TextArea rows={4} placeholder="请输入特殊说明" />
                  )
              }
              {/* </InputTips> */}
            </FormItem>
            <FormItem className="files-wrapper" label="换届材料" labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              {
                operation_type === 3 && files && files.length === 0 && "暂无"
              }
              <UploadList
                children={<span>点击上传</span>}
                tipText={
                  <span style={{ color: "#f46e65" }}>
                    请上传不大于5M的文件。
                  </span>
                }
                limitSize={5}
                // ref={(ref) => { this.personSelector = ref }} 
                canPreview={true}
                canEdit={operation_type == 3 ? false : true}
                inputData={files}
                onChange={(files) => {
                  //获取最新的一个文件
                  this.setState({
                    files: files.length ? [files[files.length - 1]] : files
                  });
                }}
              />
            </FormItem>
            <FormItem label={`${type_child_name}书记`} labelCol={{ span: 3 }} wrapperCol={{ span: 14 }} >
              <SecretaryFormItem
                operation_type={operation_type}
                filter={1}
                period_id={period_id}
                prop="secretaryList"
                value={secretaryList}
                onChange={this.onSecretartChange}
                onRefresh={this.getSecretaryList}
                title={`${type_child_name}书记`}
                required
              />
            </FormItem>
            <FormItem label={`${type_child_name}副书记`} labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              <SecretaryFormItem
                operation_type={operation_type}
                filter={2}
                period_id={period_id}
                prop="deputySecretaryList"
                value={deputySecretaryList}
                onChange={this.onSecretartChange}
                onRefresh={this.getSecretaryList}
                title={`${type_child_name}副书记`}
              />
            </FormItem>

            {is_visible.discipline_inspection_committee_is_visible && <FormItem label="纪律检查委员会" labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              {
                operation_type == 3 ?
                  param.discipline_inspection_committee == 1 ? "有" : "无"
                  :
                  getFieldDecorator('discipline_inspection_committee', {
                    initialValue: param.discipline_inspection_committee || 2,
                    rules: [{
                      validator: (_, value, callback) => {
                        type !== "1" && this.onChangeTableData();
                        callback();
                      }
                    }]
                  })(
                    <Radio.Group>
                      <Radio value={1}>有</Radio>
                      <Radio value={2}>无</Radio>
                    </Radio.Group>
                  )
              }
            </FormItem>}
            {is_visible.party_affairs_office_is_visible && <FormItem label="党委办公室" labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              {
                operation_type == 3 ?
                  param.party_affairs_office == 1 ? "有" : "无"
                  :
                  getFieldDecorator('party_affairs_office', {
                    initialValue: param.party_affairs_office || 2,
                    rules: [{
                      validator: (_, value, callback) => {
                        type !== "1" && this.onChangeTableData();
                        callback();
                      }
                    }]
                  })(
                    <Radio.Group>
                      <Radio value={1}>有</Radio>
                      <Radio value={2}>无</Radio>
                    </Radio.Group>
                  )
              }
            </FormItem>}
            <FormItem label={`${type_child_name}委员会`} labelCol={{ span: 3 }} wrapperCol={{ span: 14 }}>
              {
                getFieldDecorator('has_tag', {
                  initialValue: param.has_tag || 2,
                  rules: [{
                    validator: (_, value, callback) => {
                      type !== "1" && this.onChangeTableData();
                      callback();
                    }
                  }]
                })(
                  <Radio.Group>
                    <Radio value={1}>已设立</Radio>
                    <Radio value={2}>未设立</Radio>
                  </Radio.Group>
                )
              }
            </FormItem>
            <FormItem wrapperCol={{ span: 22, offset: 1 }}>
              <Tabs activeKey={type} onChange={(value) => this.onChangeTableData(value)}>
                <Tabs.TabPane tab={`${type_child_name}委员会`} key="1" />
                {
                  //operation_type 3 详情
                  (getFieldValue('discipline_inspection_committee') == 1 || (operation_type == 3 && param.discipline_inspection_committee == 1)) && is_visible.discipline_inspection_committee_is_visible &&
                  <Tabs.TabPane tab="纪律检查委员会" key="2" />
                }
                {
                  (getFieldValue('party_affairs_office') == 1 || (operation_type == 3 && param.party_affairs_office == 1)) && is_visible.party_affairs_office_is_visible &&
                  <Tabs.TabPane tab="党委办公室" key="3" />
                }
              </Tabs>
              {
                operation_type !== 3 &&
                <Button type='primary' className="add-member" onClick={() => { this.personModal.init([]); this.setState({ addModalVisible: true, op_type: 1 }); }}>添加成员</Button>
              }
              <Table
                bordered
                columns={columns}
                dataSource={tableData}
                loading={loading}
                pagination={{
                  total,
                  current: page_num,
                  pageSize: 10,
                  // showQuickJumper: true,
                  showTotal: total => `共${total}条记录, ${pages}页`,
                  onChange: (_page, _pageSize) => {
                    this.setState({
                      page_num: _page
                    }, () => {
                      this.handleTableData();
                    });
                  },
                }}
              />
            </FormItem>
            {
              operation_type !== 3 &&
              <FormItem className='period-wrapper_btn' wrapperCol={{ span: 20, offset: 3 }}>
                <Button type='primary' htmlType="submit">提交</Button>
                {/* <Button onClick={() => history.goBack()} style={{ marginLeft: 40 }}>取消</Button> */}
                <Button onClick={() => history.replace('/party-organization', { org_id })} style={{ marginLeft: 40 }}>取消</Button>
              </FormItem>
            }
          </Form>
        </div >
        <Modal
          className='modal-orgCom'
          width={600}
          title={op_type === 1 || operation_type === 1 ? `添加${this.switchName()}成员` : `编辑${this.switchName()}成员`}
          visible={addModalVisible}
          footer={null}
          onCancel={() => this.clearMenberData()}
        >
          <Row className="modal-item">
            <Col span={6} className="modal-label label_required">
              <span>人员姓名：</span>
            </Col>
            <Col span={14}>
              {memberData.length ? <span>{memberData[0].user_name}</span> : ""}
              {
                op_type === 1 &&
                <a className="modal-a" onClick={() => this.setState({ hostUserVisible: true })}>选择人员</a>
              }
            </Col>
          </Row>
          {
            type !== "3" && (
              <React.Fragment>
                <Row className="modal-item">
                  <Col span={6} className="modal-label vertical">
                    <span>行政职务：</span>
                  </Col>
                  <Col span={14}>
                    <Input style={{ width: 300 }} value={grade_name} onInput={({ target: { value } }) => this.setGradeName(value)} placeholder="请输入行政职务" />
                  </Col>
                </Row>
                <Row className="modal-item">
                  <Col span={6} className="modal-label label_required vertical">
                    <span>党内职务：</span>
                  </Col>
                  <Col span={14}>
                    <Select style={{ width: 300 }} value={position_name || undefined} onChange={(value) => this.onChangeAtrr(value, "position_name")} placeholder="请选择党内职务">
                      {this.setOptGroup(type == "2" ? inspectionPartyPosts :  partyPosts)}
                    </Select>
                  </Col>
                </Row>
              </React.Fragment>
            )
          }
          <Row className="modal-item">
            <Col span={6} className="modal-label">
            </Col>
            <Col span={14}>
              {
                type === "3" && <Checkbox checked={isParty} onChange={({ target: { checked } }) => this.onChangeAtrr(checked, "isParty")}>专职党务干部</Checkbox>
              }
              {
                type === "2" && <Checkbox checked={isDiscipline} onChange={({ target: { checked } }) => this.onChangeAtrr(checked, "isDiscipline")}>专职纪检干部</Checkbox>
              }
            </Col>
          </Row>
          <Row className="modal-item modal_btn">
            <Col span={6} className="modal-label" />
            <Col span={14}>
              <Button type="primary" onClick={() => this.onChangeMember()}>提交</Button>
              <Button onClick={() => this.clearMenberData()}>取消</Button>
            </Col>
          </Row>
        </Modal>
        <PersonModal {...hostUserModalProps} />
      </div >
    );
  }
}
export default Form.create({})(AddPeriod);
