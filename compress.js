import JSZip from 'jszip'
import fs from 'fs'
import path from 'path'

// 获取命令行参数
const args = process.argv.slice(2)
// 默认压缩目标
const targets = ['dist']

// 创建压缩包
async function createZip() {
	console.log(`开始压缩文件`)
	const zip = new JSZip()
	// 获取项目文件夹名称
	const projectName = path.basename(process.cwd())
	let zipFileName = `${projectName}.zip`
	// 获取压缩包名称
	if (args.length > 0) {
		// 如果提供了参数，使用第一个参数作为压缩包名称
		zipFileName = `${projectName}_${args[0].endsWith('.zip') ? args[0] : `${args[0]}.zip`}`
	}

	// 递归添加文件到压缩包
	async function addToZip(zip, filePath, zipPath = '') {
		const stats = fs.statSync(filePath)

		if (stats.isDirectory()) {
			const files = fs.readdirSync(filePath)
			for (const file of files) {
				const fullPath = path.join(filePath, file)
				const relativePath = path.join(zipPath, file)
				await addToZip(zip, fullPath, relativePath)
			}
		} else {
			const content = fs.readFileSync(filePath)
			zip.file(zipPath || path.basename(filePath), content)
		}
	}

	// 处理每个目标
	for (const target of targets) {
		if (fs.existsSync(target)) {
			await addToZip(zip, target)
		} else {
			console.log(`警告: ${target} 不存在，已跳过`)
		}
	}

	// 生成压缩文件
	const content = await zip.generateAsync({ type: 'nodebuffer' })
	fs.writeFileSync(zipFileName, content)
	console.log(`压缩完成！文件已保存在项目根目录: ${zipFileName}`)
}

// 执行压缩
createZip().catch((err) => {
	console.error('压缩过程中发生错误:', err)
})
