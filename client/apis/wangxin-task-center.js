import { mtHost } from "apis/config"
import { http } from "client/tool/axios";

// 新增/保存转办单
export const addTurn = (params) => {
    return http.post(`${mtHost}/sbw-task/addTurn`, params)
}

// 新增/保存工作单
export const addWord = (params) => {
    return http.post(`${mtHost}/sbw-task/addWord`, params)
}

// 所在组织被分配任务列表
export const orgTaskList = (params) => {
    return http.post(`${mtHost}/sbw/my/task`, params)
}

// 提交被分配任务操作
export const submitTaskSelf = (params) => {
    return http.post(`${mtHost}/sbw/my/submit`, params)
}
// 保存转办单任务处理草稿
export const saveTaskDraft = (params) => {
    return http.post(`${mtHost}/sbw/my/save`, params)
}
// 审核任务列表
export const examineTaskList = (params) => {
    return http.get(`${mtHost}/sbw/verify/task`, params)
}

// 任务下发组织列表
export const taskUnderOrgList = (task_id) => {
    return http.get(`${mtHost}/sbw/verify/org?task_id=${task_id}`)
}
// 保存任务审批草稿
export const saveExamineDraft = (params) => {
    return http.post(`${mtHost}/sbw/verify/save`, params)
}
// 提交任务审批
export const submitTaskExamine = (params) => {
    return http.post(`${mtHost}/sbw/verify/submit`, params)
}
// 查询任务信息
export const findTaskMessage = (task_id) => {
    return http.get(`${mtHost}/sbw-task/myCheck?task_id=${task_id}`)
}

// 转办单信息详情
export const turnMessage = (params) => {
    return http.get(`${mtHost}/sbw-task/orderFind`,params)
}
// 生成转办单
export const createTurnWord = (params) => {
    return http.get(`${mtHost}/sbw-task/print`,params )
}
// 发布任务首页展示
export const releaseATask = (params) => {
    return http.get(`${mtHost}/sbw-task/releaseFind`, params)
}
// 发布任务首页-删除
export const homePageDel = (params) => {
    return http.post(`${mtHost}/sbw-task/del`, params)
}
// 我的任务-列表
export const myTask = (params) => {
    return http.get(`${mtHost}/sbw/my/task`, params)
}
// 根据任务状态查组织列表
export const findOrg = (params) => {
    return http.get(`${mtHost}/sbw-task/find-org`,params)
}
// 获取舆情分类
export const typeList = (params) => {
    return http.get(`${mtHost}/sbw/type/list`, params)
}
// 待办任务列表
export const shiftList = (params) => {
    return http.get(`${mtHost}/sbw/shift/list`,params)
}
// 新建待办任务
export const shiftSubmit = (params) => {
    return http.post(`${mtHost}/sbw/shift/submit`,params)
}
// 待办任务保存
export const shiftSave = (params) => {
    return http.post(`${mtHost}/sbw/shift/save`,params)
}
// 待办任务回显
export const shiftDetail = (params) =>{
    return http.get(`${mtHost}/sbw/shift/details`,params)
} 
// 删除待办任务
export const shiftDelete = (params) =>{
    return http.post(`${mtHost}/sbw/shift/del`,params)
} 