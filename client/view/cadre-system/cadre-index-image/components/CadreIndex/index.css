.scale-0-9 {
  transform: scale(0.8);
}
.data-menu {
  display: flex;
  align-items: center;
}
.data-menu .menu-item {
  margin-left: 12px;
  font-size: 18px;
  font-weight: 500;
  color: #666666;
  cursor: pointer;
}
.data-menu .menu-item.menu-active {
  color: #333333;
  font-weight: bold;
  position: relative;
}
.data-menu .menu-item.menu-active::after {
  content: "";
  display: inline-block;
  width: 70%;
  height: 2px;
  background: #ff2121;
  border-radius: 2px;
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translate(-50%, 0);
}
.pyramid {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
  padding: 0px 29px 27px 10px;
}
.pyramid .pyramid-item {
  position: relative;
  transform: translate(-60px);
}
.pyramid .pyramid-item .pyramid-img {
  position: relative;
  margin: 0 auto;
}
.pyramid .pyramid-item .pyramid-img .pyramid-label {
  position: absolute;
  top: 40%;
  left: -30px;
  font-size: 22px;
  font-weight: 400;
  color: #666666;
  line-height: 26px;
}
.pyramid .pyramid-item .pyramid-img img {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}
.pyramid .pyramid-item .pyramid-img .select {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 100%;
  display: flex;
  align-items: center;
  width: 341px;
  height: 75.5px;
  clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
  background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
}
.pyramid .pyramid-item .pyramid-img .select .select-line {
  height: 1px;
  background: #278236;
  width: 191px;
}
.pyramid .pyramid-item .pyramid-img .select .text {
  margin-left: 10px;
  font-size: 22px;
  color: #278236;
  line-height: 26px;
}
.pyramid .pyramid-item .pyramid-img .split-line {
  position: absolute;
  inset: 0;
}
.pyramid .pyramid-1 .pyramid-img {
  width: 74px;
  height: 86px;
}
.pyramid .pyramid-1 .select {
  transform: translate(-40px);
}
.pyramid .pyramid-2 {
  margin-top: -10px;
}
.pyramid .pyramid-2 .pyramid-img {
  width: 162.23px;
  height: 93.9px;
}
.pyramid .pyramid-2 .pyramid-img .pyramid-label {
  left: -30px !important;
}
.pyramid .pyramid-2 .select {
  width: 271px;
  transform: translate(-40px);
}
.pyramid .pyramid-2 .select .select-line {
  width: 146px;
}
.pyramid .pyramid-3 {
  margin-top: -20px;
}
.pyramid .pyramid-3 .pyramid-img {
  width: 265.23px;
  height: 112px;
}
.pyramid .pyramid-3 .select {
  width: 271px;
  transform: translate(-45px, 10px);
}
.pyramid .pyramid-3 .select .select-line {
  width: 126px;
}
.pyramid .pyramid-4 {
  margin-top: -20px;
}
.pyramid .pyramid-4 .pyramid-img {
  width: 384.87px;
  height: 140.51px;
}
.pyramid .pyramid-4 .select {
  width: 271px;
  transform: translate(-50px, 20px);
}
.pyramid .pyramid-4 .select .select-line {
  width: 66px !important;
}
