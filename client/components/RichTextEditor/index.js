import React, {
  useState,
  useEffect,
  memo,
  useImperativeHandle,
  forwardRef,
} from "react";
import { uploadFile } from "apis/file";
import { CDN } from "apis/config";
import { headers } from "client/tool/axios";
import {
  Editor as Editorcontiner,
  Toolbar,
} from "@wangeditor/editor-for-react";
import "./index.less";
import "@wangeditor/editor/dist/css/style.css";

const { message } = Ant;
const maxFileSize = 5 * 1024 * 1024; // 100kb
/**
 *
 *  无回显页面请调用onChange方法获取内容
 *  需回显且编辑的页面请使用ref
 */
const Editor = (props) => {
  const {
    onChange,
    onRef,
    initialValue,
    mode = "simple",
    height = 500,
    disabled = false,
    maxLength = 0, // 可输入数量
    judge = 0,
    extraToolbarConfig = [] // 额外的工具栏设置
  } = props;
  const [editor, setEditor] = useState(null); // 存储 editor 实例
  const [content, setContent] = useState(); // 编辑器内容

  // 禁用
  disabled && editor && editor.disable();

  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor == null) return;
      editor.destroy();
      setEditor(null);
    };
  }, []);

  useEffect(() => {
    if (initialValue) {
      const val = initialValue.startsWith("<p>")
        ? initialValue
        : `<p>${initialValue}</p>`;
      setContent(val);
    }
  }, [initialValue]);

  // 暴露给需要回显页面的ref
  useImperativeHandle(onRef, () => ({
    getEditorContent: () => {
      setContent("");
      return content;
    },
    setEditorContent: (val) => {
      const str = val.startsWith("<p>") ? val : `<p>${val}</p>`;
      setContent(str);
    },
  }));

  const editorConfig = {
    placeholder: "请输入内容",
    autoFocus: false,
    MENU_CONF: {
      uploadImage: {
        onError(file, err, res) {
          if (file.size > maxFileSize) {
            message.error("请上传小于5M的图片");
          }
        },
        async customUpload(file, insertFn) {
          let formData = new FormData();
          formData.append("upfile", file);
          formData.append("upType", "news-image");
          formData.append(
            "user_id",
            window.sessionStorage.getItem("_uid") != null
              ? window.sessionStorage.getItem("_uid")
              : "-1"
          );
          formData.append(
            "user_name",
            window.sessionStorage.getItem("_un") != null
              ? window.sessionStorage.getItem("_un")
              : "1"
          );
          const { data } = await uploadFile(formData, headers());
          if (data.code !== 0) {
            message.error(data.message);
          } else {
            data.data.forEach((img) => {
              insertFn(`${CDN}/${img.path}`, "图片", "");
            });
          }
        },
      },
    },
    customAlert: (s, t) => {
      switch (t) {
        case "success":
          message.success(s);
          break;
        case "info":
          message.info(s);
          break;
        case "warning":
          message.warning(s);
          break;
        case "error":
          message.error(s);
          break;
        default:
          message.info(s);
          break;
      }
    },
    onChange: (editor) => {
      if (maxLength && editor.getText().length > maxLength) {
        return false;
      }
      // 当编辑器选区、内容变化时，即触发
      onChange && onChange(editor.getHtml(), editor.getText(), editor.children);
      setContent(editor.getHtml());
    },
  };
  const toolbarConfig = {
    excludeKeys: ["insertVideo", "insertTable", "codeBlock", "group-video", ...extraToolbarConfig ],
  };
  return (
    <div className="editor-wrap">
      {!judge && <Toolbar editor={editor} defaultConfig={toolbarConfig} mode={mode} />}
      <Editorcontiner
        defaultConfig={editorConfig}
        value={content}
        onCreated={setEditor}
        onChange={(editor) => {
          setContent(editor.getHtml());
        }}
        mode={mode}
        style={{ height }}
      />
    </div>
  );
};

export default memo(forwardRef(Editor));
