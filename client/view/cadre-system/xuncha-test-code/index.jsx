import React, { useEffect, useState } from "react";
import "./index.less";
import SearchHeader from "client/components/search-header";
import CodeItem from "./components/code-item";
import { getQrCodeInfoV3, setQrCodeInfo } from "client/apis/cadre-portrait";
import { getOpList } from "client/apis/cadre-portrait";
import data from "./data";
import { message } from "antd";

function index({ history, location }) {
  const query = new URLSearchParams(location.search);
  // 组织id
  const org_id = query.get("org_id");
  // 测评id
  const evaluation_id = query.get("evaluation_id");
  // 数据
  const [state, setState] = useState([]);
  // 一键下载函数栈
  const [downloadCallBack, setCallBack] = useState([]);
  // 打票类型
  const [ticketType, setTicketType] = useState([]);
  useEffect(() => {
    loadData();
    loadDictType();
  }, []);
  /**
   * @description: 出具初始化
   * @return {*}
   */
  const loadData = () => {
    getQrCodeInfoV3({ evaluation_id }).then((res) => {
      if (res.data.code === 0) {
        setState(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  // 获取打票类型字典
  const loadDictType = () => {
    getOpList({ code: 96180 }).then((res) => {
      if (res.data.code === 0) {
        // 转换为 {label: "", value: ""} 格式
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.op_value,
            key: item.op_key,
          };
        });
        setTicketType(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  const onUpdateEvaluation = (params) => {
    setQrCodeInfo(params).then((res) => {
      if (res.data.code === 0) {
        message.success("操作成功");
        loadData();
      } else {
        message.error(res.data.message);
      }
    });
  };
  /**
   * @description: 注册下载函数
   * @param {*} func
   * @return {*}
   */
  const registryDownLoadCb = (func) => {
    setCallBack((prev) => {
      return [...prev, func];
    });
  };
  // 调用下载函数
  const downloadAll = () => {
    downloadCallBack.forEach((func) => {
      func();
    });
  };
  return (
    <div className="test-code">
      <SearchHeader
        title="测评码"
        onBack={() => {
          history.goBack();
        }}
      />
      <div className="table-list">
        {state.map((item) => {
          return (
            <CodeItem
              onUpdateEvaluation={onUpdateEvaluation}
              dataSource={item.info_list}
              ticketType={ticketType}
              org_id={org_id}
              registryDownLoadCb={registryDownLoadCb}
              evaluation_id={evaluation_id}
              title={item.qr_code_type_name}
              qr_code_type={item.qr_code_type}
            />
          );
        })}
      </div>
    </div>
  );
}

export default index;
