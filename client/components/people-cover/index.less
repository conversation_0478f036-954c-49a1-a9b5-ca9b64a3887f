.people-cover,
.people-cover-add {
  position: relative;
  vertical-align: top;
  width: 55px;
  display: inline-block;
  text-align: center;
  .avator+.bottom-name {
    overflow: hidden;
    height: auto;
    font-size: 14px;
    width: 100%;
    display: block;
    line-height: 1.6;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .avator,
  .avator-add {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #27BFBA;
    text-align: center;
    position: relative;
    margin: 0 auto;
    .name {
      // line-height: 44px;
      font-size: 16px;
      color: #fff;
    }
    .close {
      position: absolute;
      font-size: 14px;
      color: #8B9FA8;
      top: -2px;
      right: -2px;
      width: 16px;
      height: 16px;
      background: #fff;
      text-align: center;
      border: 1px solid #e5e5e5;
      border-radius: 50%;
      cursor: pointer;
      opacity: 1;
      filter: alpha(opacity=100);
      i {
        display: block;
      }
    }
  }
  .avator-add {
    background: #EBF2F6;
    i {
      font-size: 30px;
      // line-height: 43px;
      color: #8B9FA8;
    }
    .anticon {
      svg {
        height: 100%;
      }
    }
  }
  .name {
    font-size: 13px;
    color: #333333;
  }
  &+.people-cover,
  &+.people-cover-add {
    margin-left: 15px;
    a {
      font-size: 14px;
    }
  }
}

.people-cover-add {
  cursor: pointer;
  a {
    line-height: 22px;
  }
}

.approval-process {
  .people-cover {
    margin-left: 40px;
    &::before {
      font-family: 'gsg';
      color: #DBDFE1;
      font-size: 23px;
      content: '\e67c';
      position: absolute;
      right: 100%;
      top: 6px;
      transform: translateX(-10px)
    }
    &:first-of-type {
      margin-left: 0;
      &::before {
        content: ''
      }
    }
  }
}