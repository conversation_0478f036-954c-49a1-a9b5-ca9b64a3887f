import React, { Component } from "react";
import { Form, Input } from "antd";

const FormItem = Form.Item;
const Search = Input.Search;
class PersonSearcher extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.resetHandler = this.resetHandler.bind(this);
  }

  resetHandler(onlyResetValue) {
    const { form, onSearchHandler } = this.props;
    const { resetFields, getFieldValue } = form;
    resetFields();
    if (!onlyResetValue) {
      const value = getFieldValue(keyword);
      onSearchHandler(value);
    }
  }

  searchHandler(value) {
    const { onSearchHandler } = this.props;
    onSearchHandler(value);
  }
  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const formProps = {
      hideRequiredMark: true,
      layout: "inline",
      onSubmit(e) {
        e.preventDefault();
      }
    }
    return (
      <div className="person-searcher-form">
        <Form {...formProps}>
          <FormItem className="no-margin">
            {
              getFieldDecorator("keyword", {
                initialValue: ""
              })(
                <Search style={{ height: "32px" }} placeholder="请输入姓名/电话" enterButton="查询" onSearch={(value) => this.searchHandler(value)} />
              )
            }
          </FormItem>
        </Form>
      </div>
    )
  }
}

export default Form.create()(PersonSearcher);