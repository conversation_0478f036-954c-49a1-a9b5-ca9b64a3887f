import SearchHeader from "client/components/search-header";
import DropText from "../create-evaluation/components/DropText";
import AddForm from "./components/AddForm";
import EditForm from "./components/EditForm";

import {
  getEvaluationQrcode,
  setEvaluationQrcode,
  addEvaluationQrcode,
  modifyEvaluationQrcodeName,
  getRelationshipsDict,
} from "client/apis/evaluation-publish";
import { getOpList } from "client/apis/cadre-portrait";
import { Anchor } from "antd";
import "./anchor.less";
import { cadreMobile } from "client/apis/config";

const { message } = Ant;

export default function IntranetXianguan(props) {
  const { history, evaluationId, type, orgId } = props;

  const MobileUrl =
    type === "8" ? `${cadreMobile}/banzi/` : `${cadreMobile}/zhongceng/`;

  const addForm = React.useRef(null);
  const editForm = React.useRef(null);

  const [loading, setLoading] = React.useState(true);
  const [state, setState] = React.useState([]);
  const [ticketType, setTicketType] = React.useState([]);
  const [addVisible, setAddVisible] = React.useState(false);
  const [editVisible, setEditVisible] = React.useState(false);

  const loadData = () => {
    setLoading(true);
    getEvaluationQrcode(evaluationId).then((res) => {
      if (res.data.code === 0) {
        setState(res.data.data);
        setLoading(false);
      } else {
        message.error(res.data.message);
      }
    });
  };

  // 获取打票类型字典
  const loadDictType = () => {
    getOpList({ code: 96180 }).then((res) => {
      if (res.data.code === 0) {
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.op_value,
            key: item.op_key,
          };
        });
        setTicketType(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };

  const loadDictTypeForRela = () => {
    getRelationshipsDict({ eval_type: type }).then((res) => {
      if (res.data.code === 0) {
        res.data.data = res.data.data.map((item) => {
          return {
            label: item.value,
            key: item.key,
          };
        });
        setTicketType(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  React.useEffect(() => {
    loadData();
    // loadDictType();
    loadDictTypeForRela();
  }, []);

  const handleAdd = () => {
    if (!addForm.current) return;
    addForm.current.validateFields((err, values) => {
      if (!err) {
        addEvaluationQrcode({ ...values, evaluation_id: evaluationId }).then(
          (res) => {
            if (res.data.code === 0) {
              setAddVisible(false);
              loadData();
            } else {
              message.error(res.data.message);
              setLoading(false);
            }
          }
        );
      }
    });
  };

  const handleEdit = () => {
    if (!editForm.current) return;
    editForm.current.validateFields((err, values) => {
      if (!err) {
        modifyEvaluationQrcodeName({
          ...values,
          evaluation_id: evaluationId,
        }).then((res) => {
          if (res.data.code === 0) {
            setEditVisible(false);
            loadData();
          } else {
            message.error(res.data.message);
            setLoading(false);
          }
        });
      }
    });
  };

  const onUpdateEvaluation = (params) => {
    setLoading(true);
    setEvaluationQrcode(params).then((res) => {
      if (res.data.code === 0) {
        loadData();
      } else {
        message.error(res.data.message);
        setLoading(false);
      }
    });
  };

  const handlePreview = (row, showType) => {
    console.log(row);
    const url = `${MobileUrl}?evaluation_id=${evaluationId}&org_id=${orgId}&eval_partyment_id=${19}&qrcode_type=${
      row.qr_code_type
    }&preview_type=${showType}`;
    window.open(url);
  };

  const option = [
    { label: "县管正职", key: 1 },
    { label: "县管副职", key: 2 },
    { label: "改非干部", key: 3 },
    { label: "新提拔中层干部", key: 4 },
    { label: "中层干部", key: 5 },
  ];
  const getValue = (value) => {
    const item = option.find((item) => Number(item.key) === Number(value));
    return item ? item.label : "";
  };

  const columns = [
    {
      title: "被测评人",
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "10%",
    },
    {
      title: "干部类别",
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      width: "10%",
      render: (t) => getValue(t),
    },
    {
      title: "职务",
      dataIndex: "job_name",
      key: "job_name",
      align: "left",
      editable: true,
      width: "25%",
    },
    {
      title: "打票类型",
      dataIndex: "relationship",
      key: "relationship",
      align: "center",
      width: "20%",
      render(value, { pms_evaluation_qrcode_id }) {
        return (
          <DropText
            value={value}
            onSelect={(value) => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                relationship: value,
              });
            }}
            option={ticketType}
          />
        );
      },
    },
    {
      title: "测评开关",
      dataIndex: "enable",
      key: "enable",
      align: "center",
      render(t, { pms_evaluation_qrcode_id }) {
        const enable = Number(t) === 1 ? true : false;
        return (
          <Ant.Switch
            defaultChecked
            size="small"
            checked={enable}
            onChange={(checked) => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                enable: checked ? 1 : 2,
              });
            }}
          />
        );
      },
    },
    {
      title: "特征标签",
      dataIndex: "tag",
      key: "tag",
      align: "center",
      render(t, { pms_evaluation_qrcode_id }) {
        const tag = Number(t) === 1 ? true : false;
        return (
          <Ant.Switch
            defaultChecked
            size="small"
            checked={tag}
            onChange={(checked) => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                tag: checked ? 1 : 2,
              });
            }}
          />
        );
      },
    },
    {
      title: "一言素描",
      dataIndex: "sketch",
      key: "sketch",
      align: "center",
      render(_, { pms_evaluation_qrcode_id }) {
        const sketch = Number(_) === 1 ? true : false;
        return (
          <Ant.Switch
            defaultChecked
            size="small"
            checked={sketch}
            onChange={(checked) => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                sketch: checked ? 1 : 2,
              });
            }}
          />
        );
      },
    },
  ];

  return (
    <div>
      <SearchHeader
        title="下载测评码"
        onBack={() => {
          history.goBack();
        }}
      />

      <div>
        {loading && (
          <Ant.Row type="flex" justify="center">
            <Ant.Spin tip="正在加载..." />
          </Ant.Row>
        )}

        <Anchor onClick={(e) => e.preventDefault()} targetOffset={200}>
          <Ant.Row
            type="flex"
            align="middle"
            justify="space-between"
            style={{ width: "100%" }}
          >
            <Ant.Col
              span={21}
              style={{
                display: "flex",
                flexWrap: "wrap",
                borderBottom: "1px solid #e8e8e8",
              }}
            >
              {state.map((item) => {
                return (
                  <Anchor.Link
                    href={"#acl" + item.pms_evaluation_qrcode_type_id}
                    title={item.qr_code_type_name}
                  />
                );
              })}
            </Ant.Col>
            <Ant.Col>
              <Ant.Button type="primary" onClick={() => setAddVisible(true)}>
                添加测评码
              </Ant.Button>
            </Ant.Col>
          </Ant.Row>
        </Anchor>

        {state.map((item) => {
          return (
            <div
              style={{ padding: 16 }}
              key={item.pms_evaluation_qrcode_type_id}
              id={"acl" + item.pms_evaluation_qrcode_type_id}
            >
              <Ant.Row type="flex" align="middle" style={{ marginBottom: 12 }}>
                <div
                  style={{
                    marginRight: 8,
                    width: 4,
                    height: 16,
                    background: "#1890FF",
                  }}
                />
                <div style={{ fontSize: 16, fontWeight: 800 }}>
                  {item.qr_code_type_name}
                </div>

                <Ant.Button
                  type="link"
                  style={{ marginLeft: 10 }}
                  onClick={() => {
                    editForm.current.setFieldsValue({
                      qr_code_type_name: item.qr_code_type_name,
                      pms_evaluation_qrcode_type_id:
                        item.pms_evaluation_qrcode_type_id,
                    });
                    setEditVisible(true);
                  }}
                >
                  <Ant.Icon type="edit" theme="filled" />
                  修改二维码名称
                </Ant.Button>

                <Ant.Dropdown
                  style={{ marginLeft: "auto" }}
                  overlay={
                    <Ant.Menu>
                      <Ant.Menu.Item>
                        <a onClick={() => handlePreview(item, "1")}>模拟测评</a>
                      </Ant.Menu.Item>
                      <Ant.Menu.Item>
                        <a onClick={() => handlePreview(item, "2")}>
                          可跳过答题
                        </a>
                      </Ant.Menu.Item>
                    </Ant.Menu>
                  }
                >
                  <Ant.Button type="link">
                    <Ant.Icon type="eye" />
                    预览 <Ant.Icon type="down" />
                  </Ant.Button>
                </Ant.Dropdown>
              </Ant.Row>

              <Ant.Table
                loading={loading}
                tableLayout="fixed"
                columns={columns}
                dataSource={item.info_list}
                bordered
                scroll={{ y: 400 }}
                pagination={false}
              />
            </div>
          );
        })}
      </div>

      <Ant.Modal
        visible={addVisible}
        onOk={handleAdd}
        onCancel={() => setAddVisible(false)}
      >
        <AddForm ref={addForm} />
      </Ant.Modal>

      <EditForm
        ref={editForm}
        visible={editVisible}
        onOk={handleEdit}
        onCancel={() => setEditVisible(false)}
      />
    </div>
  );
}
