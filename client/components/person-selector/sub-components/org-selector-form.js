import React, { Component } from "react";
import { Form, Button, Select, Input, message as Message } from "antd";
import { getcodeList } from "apis/users";
import { getOrgInfo } from "apis/organize";
import { getFindTreeTypeByOp } from "apis/organisition";
import { fetchStorage } from "tool/util";

import propTypes from "prop-types";

class OrgSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 数据字典，查询组织类型根节点
      orgTypeRoot: 1028,
      orgType1: [],
      orgType2: [],
      tree_type: 2
    }
    this.fetchOrgType = this.fetchOrgType.bind(this);
    this.fetchOrgInfo = this.fetchOrgInfo.bind(this);
    this.resetHandler = this.resetHandler.bind(this);
  }
  componentDidMount() {
    const { orgTypeRoot } = this.state;
    // 初始化的时候，渲染组织类型下拉框之前获取此接口内容
    this.fetchOrgInfo(orgTypeRoot);
  }

  async resetHandler(onlyResetValue) {
    const { form, orgChangeHandler } = this.props;
    const { resetFields, getFieldsValue } = form;
    resetFields();
    if (!onlyResetValue) {
      let values = getFieldsValue();
      if (values.org_type_1 !== -1) {
        if (values.org_type_2 === -1) {
          values.org_type = values.org_type_1;
        } else {
          values.org_type = values.org_type_2;
        }
      }
      delete values.org_type_1;
      delete values.org_type_2;
      if (!values.tree_type && values.org_type) {
        const response = await getFindTreeTypeByOp({ org_type: values.org_type });
        // console.log(response);
        const { data: body } = response;
        const { code, message, data } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        this.setState({
          tree_type: data
        });
        values.tree_type = data;
      }
      // console.log("重置", values);
      orgChangeHandler(values);
    }
  }

  async fetchOrgInfo(orgTypeRoot) {
    // console.log("只调用一次");
    // console.log(this.props);
    const { initOrgType, isLeaderGroup } = this.props;
    // let org_id = typeof window !== "undefined" && window.sessionStorage.getItem("_oid") || 0;
    // org_id = Number(org_id);
    // const response = await getOrgInfo({ org_id, tree_type: 2 });
    // const { data: body } = response;
    // const { data, code, message } = body;
    // if (code !== 0) {
    //   Message.error(message);
    //   return;
    // }
    // console.log(data);
    try {
      // 获取当前登录组织信息
      const userInfoString = fetchStorage(2, "userInfo");
      const userInfo = JSON.parse(userInfoString) || {};
      const { org_type, owner_tree, oid } = userInfo;
      this.setState({
        tree_type: owner_tree
      });
      if (isLeaderGroup) {
        this.fetchOrgType(1);
        // this.fetchOrgType(2, org_type);
        initOrgType({ org_id: 0, tree_type: 1 });
      }
      else {
        const response = await getOrgInfo({ org_id: oid, tree_type: owner_tree });
        const { data: body } = response;
        const { data, code, message } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        // console.log(data);
        const orgType1 = [{
          code: orgTypeRoot,
          op_key: data.org_type,
          op_value: data.type_name
        }];
        // console.log(orgType1);
        this.fetchOrgType(2, org_type);
        initOrgType({ org_type, tree_type: owner_tree });
        this.setState({
          orgType1
        });
      }
      // console.log(userInfo);
      // const orgType1 = [{
      //   code: orgTypeRoot,
      //   op_key: data.org_type,
      //   op_value: data.type_name
      // }];
      // initOrgType(data.org_type);
      // this.setState({
      //   orgType1
      // });

    }
    catch (error) {
      console.error(error);
    }

  }
  async fetchOrgType(type = 1, value) {
    let { orgTypeRoot } = this.state;
    const { form } = this.props;
    const { getFieldValue, resetFields } = form;
    let typeCode = 0;
    if (type === 1) {
      typeCode = orgTypeRoot;
    } else if (type === 2) {
      // 如果一级组织类别发生变更，重置二级组织类别选择
      resetFields(["org_type_2"]);
      typeCode = value || getFieldValue("org_type_1");
      // console.log(typeCode);
    }
    const response = await getcodeList({
      code: typeCode
    });
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (type === 1) {
      this.setState({
        orgType1: data
      });
    } else if (type === 2) {
      this.setState({
        orgType2: data
      });
    }
  }

  async changeHandler(level = 2, value = 1028) {
    const { form, orgChangeHandler } = this.props;
    const { getFieldsValue } = form;
    let { tree_type } = this.state;
    if (level === 1) {
      if (value !== -1) {
        // console.log(value);
        this.fetchOrgType(2, value);
        // 通过value(org_type)查找tree_type
        const response = await getFindTreeTypeByOp({ org_type: value });
        // console.log(response);
        const { data: body } = response;
        const { code, message, data } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        this.setState({
          tree_type: data
        });
        tree_type = data;
      }
      else {
        tree_type = 1;
      }
    }
    setTimeout(() => {
      let values = getFieldsValue();
      // console.log(values);
      if (values.org_type_1 !== -1) {
        if (values.org_type_2 === -1) {
          values.org_type = values.org_type_1;
        } else {
          values.org_type = values.org_type_2;
        }
      }
      delete values.org_type_1;
      delete values.org_type_2;
      values.tree_type = tree_type;
      orgChangeHandler(values);
    }, 0);
  }

  render() {
    const FormItem = Form.Item;
    const Option = Select.Option;
    const {
      orgType1,
      orgType2
    } = this.state;
    const {
      form,
      isPartyGroup,
      isLeaderGroup
    } = this.props;
    const {
      validateFields,
      getFieldDecorator,
      resetFields,
      getFieldsValue,
      getFieldValue
    } = form;
    const formProps = {
      hideRequiredMark: true,
      layout: "inline",
      onSubmit(e) {
        e.preventDefault();
      }
    }
    const selectProps = {
      style: {
        width: "150px"
      },
      // dropdownMatchSelectWidth: false
    }

    return (
      <div className="org-selector-form">
        <Form {...formProps}>
          <FormItem label="组织类型" className="no-margin">
            {
              getFieldDecorator("org_type_1", {
                initialValue: (!isLeaderGroup && orgType1[0]) ? orgType1[0].op_key : -1
              })(
                <Select
                  style={{
                    width: "150px",
                    // position: "relative",
                    // top: 4
                  }}
                  onChange={(value) => {
                    resetFields("org_type_2");
                    setTimeout(() => {
                      this.changeHandler(1, value);
                    }, 0);
                  }}
                  // disabled={!isLeaderGroup}
                  disabled
                >
                  <Option key={-1} value={-1}>全部</Option>
                  {
                    (orgType1 && Array.isArray(orgType1) && orgType1.length !== 0) &&
                    orgType1.map((org, index) => {
                      return (
                        <Option key={org.op_key} value={org.op_key} title={org.op_value}>{org.op_value}</Option>
                      )
                    })
                  }
                </Select>
              )
            }
          </FormItem>
          {
            getFieldValue("org_type_1") !== -1 &&
            <FormItem>
              {
                getFieldDecorator("org_type_2", {
                  initialValue: -1
                })(
                  <Select {...selectProps}
                    onChange={(value) => this.changeHandler(2, value)}
                  >
                    <Option key={-1} value={-1}>全部</Option>
                    {
                      (orgType2 && Array.isArray(orgType2) && orgType2.length !== 0) &&
                      orgType2.map((org, index) => {
                        return (
                          <Option key={org.op_key} value={org.op_key} title={org.op_value}>{org.op_value}</Option>
                        )
                      })
                    }
                  </Select>
                )
              }
            </FormItem>
          }
        </Form>
      </div >
    )
  }
}

OrgSelector.propTypes = {
  orgChangeHandler: propTypes.func,
  initOrgType: propTypes.func
}

OrgSelector.defaultProps = {
  orgChangeHandler: () => { },
  initOrgType: () => { }
}

export default Form.create()(OrgSelector);