export default Ant.Form.create()(function DelayForm(props) {
  const { visible, onCancel, record, onOk, form } = props;

  const { getFieldDecorator, validateFields } = form;

  const handleOk = () => {
    validateFields((error, values) => {
      if (!error) onOk(values, record);
    });
  };

  return (
    <Ant.Modal visible={visible} onCancel={onCancel} onOk={handleOk}>
      <Ant.Form>
        <Ant.Form.Item label="延期截止时间">
          {getFieldDecorator("end_time", {
            rules: [{ required: true, message: "请选择延期截止时间" }],
          })(<Ant.DatePicker style={{ width: "100%" }} />)}
        </Ant.Form.Item>
      </Ant.Form>
    </Ant.Modal>
  );
});
