import { useState, useEffect, useMemo } from "react";
import { DatePicker, Tag, Button } from "antd";
import "./index.less";

const DateMultiPicker = ({ onChange, value = [] }) => {
  const [pickerOpen, setPickerOpen] = useState(false);
  const [tagTimes, setTagTimes] = useState(() => value);

  const _value = useMemo(() => JSON.stringify(value), [value]);
  useEffect(() => {
    setTagTimes(value);
  }, [_value]);

  return (
    <div className="DateMultiPicker">
      <div onClick={() => setPickerOpen(true)}>
        {tagTimes.length ? (
          tagTimes.map((tag, index) => (
            <Tag
              closable
              key={tag}
              bordered={false}
              className={"DateMultiPicker-tag"}
              onClose={() => {
                tagTimes.splice(index, 1);
                setTagTimes([...tagTimes]);
                onChange(tagTimes);
              }}
            >
              {tag}
            </Tag>
          ))
        ) : (
          <span className={"tips"}>选择时间</span>
        )}
      </div>
      <DatePicker
        className="DateMultiPicker-wrap"
        showToday={false}
        open={pickerOpen}
        // format="YYYY/MM/DD"
        renderExtraFooter={() => (
          <div style={{ height: 30 }}>
            <Button
              type="link"
              className={"DateMultiPicker-btn"}
              onClick={(e) => {
                e.stopPropagation();
                setPickerOpen(false);
                onChange(tagTimes);
              }}
            >
              确定
            </Button>
          </div>
        )}
        onChange={(date, ds) => {
          const isSome = tagTimes.some((time) => time === ds);
          let _times = tagTimes;
          if (isSome) {
            _times = _times.filter((time) => time !== ds);
          } else {
            _times.push(ds);
          }
          setTagTimes([..._times]);
        }}
      />
    </div>
  );
};
export default DateMultiPicker;
