import React, { Component } from 'react';
import { Modal, Form, Input, Button, Radio, InputNumber } from 'antd';
import { connect } from 'dva';
import Image from './upload-image';
import './index.less';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 4 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 20 },
    sm: { span: 20 },
  },
};

class ModalCharitys extends Component {
  componentDidMount() {}
  componentDidUpdate(prevProps) {
    const { baweiyuzhen, form } = this.props;
    const { show, index } = baweiyuzhen.status.charitys;
    if (show && show !== prevProps.baweiyuzhen.status.charitys.show) {
      if (index !== undefined) {
        const data = baweiyuzhen.formData.charitys[index];
        form.setFieldsValue(data);
      }
    }
  }
  onSubmit() {
    const { form, baweiyuzhen, dispatch } = this.props;
    form.validateFields((err, value) => {
      if (err) return;
      dispatch({
        type: 'baweiyuzhen/submitModal',
        payload: { data: value, type: 'charitys' },
      });
      this.onCancel();
    });
  }
  onCancel() {
    const { dispatch, form } = this.props;
    form.resetFields();
    dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: { charitys: { show: false } },
    });
  }

  render() {
    const { baweiyuzhen, form } = this.props;
    if (!baweiyuzhen) {
      return null;
    }
    const { charitys } = baweiyuzhen.status;
    const { index, show } = charitys;
    const { getFieldValue, getFieldDecorator } = form;
    return (
      <div>
        <Modal
          title={`${index === undefined ? '新增' : '编辑'}公益宝贝商品`}
          visible={show}
          width={660}
          wrapClassName="modal-baweiyuzhen"
          bodyStyle={{ minHeight: 400, width: 660, margin: 'auto' }}
          destroyOnClose
          onCancel={this.onCancel.bind(this)}
          footer={null}>
          <Form>
            <FormItem {...formItemLayout} label="名称">
              {getFieldDecorator('name', {
                rules: [
                  {
                    required: true,
                    message: '请填写名称',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="图片">
              {getFieldDecorator('image_url', {
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<Image size={{ w: 320, h: 290 }} ratio={3.2} />)}
            </FormItem>
            <FormItem {...formItemLayout} label="所属分期">
              {getFieldDecorator('cycle', {
                rules: [
                  {
                    required: true,
                    message: '请填写所属分期',
                  },
                ],
              })(<InputNumber min={1} style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="类别ID">
              {getFieldDecorator('recommend_type', {
                rules: [
                  {
                    required: true,
                    message: '请填写ID',
                  },
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label={'类别链接'}>
              {getFieldDecorator('target_url', {
                rules: [
                  {
                    required: true,
                    message: '请填写链接地址',
                  },
                  { pattern: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/, message: "请输入正确的链接" }
                ],
              })(<Input style={{ width: 450 }} placeholder="" />)}
            </FormItem>
            <FormItem {...formItemLayout} label="状态">
              {getFieldDecorator('is_show', {
                rules: [
                  {
                    required: true,
                    message: '请选择显示状态',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={1}>显示</Radio>
                  <Radio value={0}>隐藏</Radio>
                </Radio.Group>
              )}
            </FormItem>
          </Form>
          <div className="buttons-wrapper">
            <Button
              onClick={() => {
                this.onCancel();
              }}>
              取消
            </Button>
            <Button type="primary" onClick={() => this.onSubmit()}>
              确定
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}
export default connect(({ baweiyuzhen }) => ({ baweiyuzhen }))(Form.create()(ModalCharitys));
