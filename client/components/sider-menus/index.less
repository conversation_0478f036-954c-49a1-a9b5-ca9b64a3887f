.sider-main {
  overflow: auto;
  .logo {
    height: 60px;
    display: flex;
    justify-content: space-between;
    line-height: 60px;
    padding: 0 15px;
    position: relative;
    overflow: hidden;
    .logo-name {
      width: 100%;
      text-align: center;
      color: #fff;
      font-size: 18px;
    }
    .icon,
    .unicon {
      line-height: 70px;
      margin: auto;
    }
    .unicon {
      margin: auto 0px auto auto;
    }
    .hr {
      position: absolute;
      bottom: 0;
      left: 50%;
      margin-left: -40%;
      width: 80%;
      height: 1px;
      background: #171B20;
      box-shadow: 0 1px 2px rgba(255, 255, 255, .15);
    }
  }
  .menu-title-wrapper {
    display: flex;
    align-items: center;
  }
  .ant-menu-submenu.ant-menu-submenu-inline,
  .ant-menu-item {
    color: #95A2BA;
  }
  .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open {
    color: #fff;
  }
  .ant-menu-dark .ant-menu-item,
  .ant-menu-dark .ant-menu-item-group-title,
  .ant-menu-dark .ant-menu-item>a {
    color: #95A2BA;
  }
  .ant-menu.ant-menu-dark .ant-menu-item-selected,
  .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected>a {
    color: #fff;
  }
  .ant-menu-submenu.ant-menu-submenu-inline.menuLevel1.ant-menu-submenu-open {
    .ant-menu.ant-menu-sub.ant-menu-inline {
      box-shadow: unset;
    }
  }
}