import { sasHost, party } from "./config";
import { http } from 'client/tool/axios';

/**
 * @description 查询党费缴纳完成情况
 * @param params
 * @returns {*}
 */
export const getPayFeeList  = (params = {}) => http.post(`${sasHost}/pay-fee/list`, params);

/**
 * @description 查询支部党费个人交纳情况
 * @param params
 * @returns {*}
 */
export const getBranchPayDetails  = (params = {}) => http.post(`${party}/ppmd/stats/branchpay/details/sas`, params);
