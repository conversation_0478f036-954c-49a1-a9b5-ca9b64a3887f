import { useState, useEffect, useMemo } from "react";
import { getResourceAppointmentAppointment } from "apis/resource-library";
import { Table, Button, message } from "antd";

const Record = ({ state = {} }) => {
  const { resource_library_id } = state;
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    getResourceAppointmentAppointmentFn();
  }, []);

  const getResourceAppointmentAppointmentFn = (
    page = 1,
    page_size = 10
  ) => {
    setDataSource([])
    getResourceAppointmentAppointment(resource_library_id, {
      page,
      page_size,
    }).then(({ data: res }) => {
      const { code, data, total } = res;
      if (code === 0) {
        setDataSource(data);
        setTotal(total);
      } else {
        message.error(res.message);
      }
    });
  };

  const columns = [
    {
      title: "预约人",
      dataIndex: "user_name",
      align: "center",
    },
    {
      title: "预约人电话",
      dataIndex: "user_phone",
      align: "center",
    },
    {
      title: "预约时间",
      dataIndex: "x",
      align: "center",
      render: (_, { start_time, end_time }) =>
        `${start_time} 至 ${end_time}`,
    },
  ];
  return (
    <Table
      rowKey="resource_appointment_id"
      bordered
      dataSource={dataSource}
      columns={columns}
      pagination={{
        total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条数据`,
        onChange: (page, page_size) =>
          getResourceAppointmentAppointmentFn(page, page_size),
      }}
      style={{ marginTop: 20 }}
    />
  );
};
export default Record;
