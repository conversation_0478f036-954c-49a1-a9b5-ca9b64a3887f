import moment from "moment";
import SearchHeader from "client/components/search-header";
import ShiguanOrgTable from "./ShiguanOrgTable";
import ShiguanUserTable from "./ShiguanUserTable";

import {
  getInitEvalData,
  publishEval,
  getEvalDetail,
} from "client/apis/evaluation-publish";

const { message } = Ant;

export default Ant.Form.create()(function CreateShiguan(props) {
  const { history, form, orgId, evaluationId } = props;

  const { getFieldDecorator, validateFields, setFieldsValue } = form;

  const [orgList, setOrgList] = React.useState([]);
  const [userList, setUserList] = React.useState([]);
  const [showFormItem, setShowFormItem] = React.useState([]);

  const loadDetail = () => {
    if (!evaluationId) {
      getInitEvalData().then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data;
          const { org_list, user_list } = data;
          setOrgList(org_list);
          setUserList(user_list);
        }
      });
    } else {
      getEvalDetail(evaluationId).then((res) => {
        if (res.data.code === 0) {
          const data = res.data.data;
          const { org_list, user_list, cadre_range } = data;
          if (cadre_range.includes(1)) {
            setShowFormItem(cadre_range);
          }
          setOrgList(org_list);
          setUserList(user_list);
          setFieldsValue({
            end_time: moment(data.end_time),
            eval_name: data.eval_name,
            team_range: data.team_range,
            cadre_range: data.cadre_range,
            vice_limit: data.vice_limit,
          });
        }
      });
    }
  };

  React.useEffect(() => {
    loadDetail();
  }, []);

  const handleOrgUpdate = (item, idx) => {
    const ori = [...orgList];
    ori[idx] = item;
    setOrgList(ori);
  };

  const handleUserUpdate = (item, idx) => {
    const user = [...userList];
    user[idx] = item;
    setUserList(user);
  };

  const handlePublish = () => {
    validateFields((err, values) => {
      if (!err) {
        const params = {
          ...values,
          end_time: moment(values.end_time).format("YYYY-MM-DD HH:mm:ss"),
          evaluation_id: evaluationId,
          org_id: orgId,
          user_list: userList,
          org_list: orgList,
        };
        publishEval(params).then((res) => {
          if (res.data.code === 0) {
            message.success("发布成功");
            history.goBack();
          } else {
            message.error(res.data.message);
          }
        });
      } else {
        message.error("您还有未填写的信息！");
      }
    });
  };

  return (
    <div>
      <SearchHeader
        title="市管领导 - 发起测评"
        onBack={() => {
          history.goBack();
        }}
      />

      <div style={{ padding: 24 }}>
        <Ant.Form
          layout="inline"
          onValuesChange={(val) => {
            console.log(val);
          }}
        >
          <Ant.Row type="flex">
            <Ant.Form.Item label="测评名称">
              {getFieldDecorator("eval_name", {
                rules: [{ required: true, message: "请输入测评名称" }],
              })(<Ant.Input placeholder="请输入" style={{ width: 400 }} />)}
            </Ant.Form.Item>
            <Ant.Form.Item label="测评截止日期">
              {getFieldDecorator("end_time", {
                rules: [{ required: true, message: "请选择测评截止日期" }],
              })(<Ant.DatePicker style={{ width: 400 }} />)}
            </Ant.Form.Item>
          </Ant.Row>
          <div
            style={{
              margin: "10px 0",
              height: 1,
              borderBottom: "1px dashed #E9E9E9",
            }}
          />
          <Ant.Row type="flex">
            <Ant.Form.Item label="需测评的班子">
              {getFieldDecorator("team_range", {
                rules: [{ required: true, message: "请选择需测评的班子" }],
              })(
                <Ant.Radio.Group>
                  <Ant.Radio value={0}>所有班子</Ant.Radio>
                  <Ant.Radio value={1}>分管的班子</Ant.Radio>
                </Ant.Radio.Group>
              )}
            </Ant.Form.Item>
            <Ant.Form.Item label="需测评的干部" style={{ marginLeft: 166 }}>
              {getFieldDecorator("cadre_range", {
                rules: [{ required: true, message: "请选择需测评的干部" }],
              })(
                <Ant.Checkbox.Group
                  onChange={setShowFormItem}
                  options={[
                    { label: "所有党政正职", value: 0 },
                    { label: "分管干部", value: 1 },
                  ]}
                />
              )}
            </Ant.Form.Item>
          </Ant.Row>
          <div style={{ marginBottom: 10, color: "#D80C0C" }}>
            注：县委书记、县长默认是对所有班子及党政正职进行测评。其他领导可以只评价分管的部门及干部。如果需要对所有党政正职评价后再对分管干部进行评价，需要2个都勾选。
          </div>
          <div
            style={{
              margin: "10px 0",
              height: 1,
              borderBottom: "1px dashed #E9E9E9",
            }}
          />
          {showFormItem.includes(1) ? (
            <React.Fragment>
              <Ant.Row type="flex">
                <Ant.Form.Item label="如果一名领导多个分管部门的，测评分管副职是否分单位限定好等次人数">
                  {getFieldDecorator("vice_limit", {
                    rules: [{ required: true, message: "请选择" }],
                  })(
                    <Ant.Radio.Group>
                      <Ant.Radio value={0}>是</Ant.Radio>
                      <Ant.Radio value={1}>否</Ant.Radio>
                    </Ant.Radio.Group>
                  )}
                </Ant.Form.Item>
              </Ant.Row>
              <div
                style={{
                  margin: "10px 0",
                  height: 1,
                  borderBottom: "1px dashed #E9E9E9",
                }}
              />
            </React.Fragment>
          ) : null}

          <div style={{ marginBottom: 10 }}>被测评对象：</div>
          <div style={{ marginBottom: 16, color: "#D80C0C" }}>
            注：以下确认的市全量的班子及党政正职。分管信息以分管设置中的信息为主。
          </div>
        </Ant.Form>

        <Ant.Tabs animated={false}>
          <Ant.Tabs.TabPane tab="班子" key="1">
            {orgList.map((item, index) => {
              return (
                <ShiguanOrgTable
                  key={item.sequence}
                  index={index}
                  record={item}
                  onUpdateData={handleOrgUpdate}
                />
              );
            })}
          </Ant.Tabs.TabPane>
          <Ant.Tabs.TabPane tab="干部" key="2">
            {userList.map((item, index) => {
              return (
                <ShiguanUserTable
                  key={item.sequence}
                  index={index}
                  record={item}
                  onUpdateData={handleUserUpdate}
                />
              );
            })}
          </Ant.Tabs.TabPane>
        </Ant.Tabs>

        <Ant.Row type="flex" justify="center" style={{ margin: "32px 0" }}>
          <Ant.Button
            type="primary"
            style={{ padding: "0 43px" }}
            onClick={handlePublish}
          >
            发布
          </Ant.Button>
        </Ant.Row>
      </div>
    </div>
  );
});
