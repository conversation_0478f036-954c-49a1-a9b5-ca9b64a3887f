.create-news {
  .ant-select-selection--multiple .ant-select-selection__choice {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    cursor: default;
    float: left;
    margin-right: 4px;
    max-width: 99%;
    position: relative;
    overflow: hidden;
    -webkit-transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    padding: 0 20px 0 10px;
  }
  .image {
    text-align: center;
    width: 190px;
    height: 124px;
    background: #f7f8f9;
    border: 1px solid #e5e5e5;
    float: left;
    margin-right: 28px;
    .view{
      width: 100%;
      height: 100%;
    }
    i {
      font-size: 32px;
      color: #ccc;
      vertical-align: middle;
      margin-right: 12px;
    }
    .text{
      padding-top: 33px;
    }
    p {
      margin-bottom: 0px;
    }
  }
  .clipping {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 99999;
    iframe{
      width: 100%;
      height: 100%;
    }
  }
  .mobile-preview {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.65);
    .mark-layout{
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
    }
    .box{
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: -433px;
      margin-top: -884px;
      width: 866px;
      height: 1769px;
      transform: scale(.42);
    }
    iframe {
      position: absolute;
      left: 60px;
      top: 218px;
      width: 750px;
      height: 1334px;
    }
  }
  .editor-container .tab-group .tab-nav .tab-item{
    height: 30px;
    line-height: 30px;
  }
  .editor-container .tab-group .tab-nav .tab-item.active .tab-text{
    padding: 0px 8px;
  }
  .editor-container .tab-group .tab-nav .tab-item .tab-text{
    padding: 0px 8px;
  }
  .video-upload-container span {
    color: #359AF7
  }
  
  .upload-imgs {
    position: relative;
    display: inline-block;
    margin-right: 28px;
    width: 190px;
    height: 124px;

    &-modal {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.65);
      color: #fff;
      font-size: 26px;
    }

    &:hover {
      .upload-imgs-modal {
        display: flex;
      }
    }
  }
}

.ResourcePublish-upload-video {
  display: block;
  width: 190px;
  height: 136px;
  border: 1px solid #d5d5d5;
  line-height: 0;
  overflow: hidden;

  &-container {
    width: 190px;
  }

  &-mText {
    margin-top: 30px;

  }

  &-sText {
    margin-top: 30px;
    color: #999;
  }

  &:hover {
    .ResourcePublish-upload-video-sText {
      color: #f46e65 !important;
    }
  }
}
