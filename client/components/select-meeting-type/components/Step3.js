import React from "react";
import { Form, Input, Radio, InputNumber } from "antd";
import ButtonGroup from "./ButtonGroup";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const Step3 = (props) => {
  const { form, state, updateState, handleCancel, hasSet, getMeetingType } = props;
  const { getFieldDecorator, validateFields } = form;
  const { setMeetItem, meeting_types } = state;
  // 验证的字段
  const fields = ["deduct", "meeting_num", "is_sign_in", "is_w_resolution"]
  // 选择会议类型
  const handleSubmit = () => {
    validateFields(fields, (err, values) => {
      let temp = meeting_types || [];
      if (!err) {
        state.selected.map((item, index) => {
          temp.push(Object.assign({}, item, values))
        });
        // 点击提交导出表格内容
        getMeetingType && getMeetingType({ meeting_types: temp });
        // 选择会议类型
        updateState({
          meeting_types: [],
          meeting_types: temp,
          visible: false,
          selected: []
        }, handleCancel);
      }
    })
  }
  // 设置会议类型
  const setMeetingType = () => {
    let tempMeeting_type = meeting_types;
    validateFields(fields, (err, values) => {
      if (!err) {
        // 设置会议要求
        let _meet = Object.assign({}, tempMeeting_type[setMeetItem], values);
        tempMeeting_type[setMeetItem] = _meet;
        // 点击提交导出表格内容
        getMeetingType && getMeetingType({ meeting_types: tempMeeting_type });
        updateState({
          meeting_types: tempMeeting_type,
          setMeetVisible: false
        }, form.resetFields)
      }
    })
  }
  // 选择会议的按钮
  const data = [
    {
      name: "上一步",
      onClick: props.prevStep,
      style: {
        width: "115px",
        height: "36px",
      }
    },
    {
      name: "提交",
      onClick: handleSubmit,
      style: {
        width: "115px",
        height: "36px",
        backgroundColor: "#F46E65",
        marginLeft: "48px",
        border: "none"
      }
    }
  ];
  // 设置会议要求的按钮
  const isSet = [
    {
      name: "确定",
      onClick: setMeetingType,
      style: {
        width: "115px",
        height: "36px",
        backgroundColor: "#F46E65",
        border: "none"
      }
    },
    {
      name: "取消",
      onClick: () => updateState({ setMeetVisible: false }, form.resetFields),
      style: {
        width: "115px",
        height: "36px",
        marginLeft: "48px"
      }
    }
  ]
  // 按钮组属性
  const buttonGroup = {
    data: hasSet ? isSet : data
  };
  // 
  const formItemLayout = {
    labelCol: {
      span: 7
    },
    wrapperCol: {
      span: 15
    }
  }
  // 是否需要 签到 && 决议
  const whetherSignin = [
    {
      value: 0,
      name: "否"
    },
    {
      value: 1,
      name: "是"
    }
  ];

  return (
    <div>
      <Form>
        <FormItem
          label="未执行扣分"
          {...formItemLayout}
        >
          {getFieldDecorator("deduct", {
            initialValue: state.deduct
          })(
            <InputNumber
              precision={2}
              placeholder="请输入"
              max={100}
              min={0} />
          )}
        </FormItem>
        <FormItem
          // label="要求召开次数"
          // label="要求举办次数"
          // label="组织频次"
          label="要求开展频次"
          {...formItemLayout}
        >
          {getFieldDecorator("meeting_num", {
            initialValue: state.meeting_num || 1,
            rules: [{ required: true, message: "请填写要求举办次数" }]
          })(
            <InputNumber
              precision={0}
              placeholder="请输入"
              max={999999}
              min={1}
            />
          )}
        </FormItem>
        <FormItem
          label="是否要求签到"
          style={{ display: "none" }}
          {...formItemLayout}
        >
          {getFieldDecorator("is_sign_in", {
            initialValue: state.is_sign_in,
            rules: [{ required: true, message: "请选择是否需要签到" }]
          })(
            <RadioGroup>
              {whetherSignin.map((item, index) => {
                return (<Radio key={index} value={item.value}>{item.name}</Radio>)
              })}
            </RadioGroup>
          )}
        </FormItem>
        {/* <FormItem
          label="是否要求填写决议"
          {...formItemLayout}
        >
          {getFieldDecorator("is_w_resolution", {
            // initialValue: state.is_w_resolution,
            initialValue: 0,
            rules: [{ required: true, message: "请选择是否需填写决议" }]
          })(
            <RadioGroup style={radioGroupStyle}>
              {whetherSignin.map((item, index) => {
                return (<Radio key={index} value={item.value}>{item.name}</Radio>)
              })}
            </RadioGroup>
          )}
        </FormItem> */}
      </Form>
      <ButtonGroup {...buttonGroup} />
    </div>
  )
}

export default Step3;