import React, { Component } from 'react';
import { Upload, Button, message } from 'antd';
import { uploadHost } from 'apis/config';
import { formHeaders } from 'client/tool/axios';
import { uploadFile } from 'apis/file';
import './index.less';

class UploadVedio extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.uploadConfig = {
      supportServerRender: true,
      showUploadList: false,
      name: 'upfile',
      data: (file) => {
        return {
          upType: 'video',
          up_name: file.name,
        };
      },
      accept: 'video/*',
      action: `${uploadHost}/file/upload`,
      headers: formHeaders(),
      onChange: (info) => {
        if (info.file.status === 'done') {
          if (+info.file.response.code !== 0) {
            message.error(`上传失败：${info.file.response.message}`);
          } else {
            const headers = {
              ...formHeaders(),
              'Content-Type': 'multipart/form-data',
            };
            const { task_id, path } = info.file.response.data[0];
            if (!path && task_id) {
              uploadFile({ upType: 'video', up_name: info.file.name, task_id }, headers).then((res) => {
                // this.props.onChange(info.file.response.code);
                console.log('😈😈😈😈😈: UploadVedio -> constructor -> value', res);
              });
            } else if (path) {
              console.log('😈😈😈😈😈: UploadVedio -> constructor -> value', path);
            }
          }
        } else if (info.file.status === 'error') {
          message.error('网络错误，上传失败');
        }
      },
    };
  }

  componentDidMount() {}

  render() {
    return <Upload {...this.uploadConfig}>{this.props.children}</Upload>;
  }
}

export default UploadVedio;
