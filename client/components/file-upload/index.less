.file-upload-wrap{
  display: inline-flex;
  .file-upload-container {
    input {
      font-size: 0;
      cursor: pointer;
    }
    input::-webkit-file-upload-button {
      cursor: pointer;
    }
    &:hover {
      span {
        cursor: pointer;
        color: #f46e65;
      }
    }
    display: inline-block;
    position: relative;
    overflow: hidden;
    color: #359AF7;
    .file-hidden-trigger {
      opacity: 0;
      position: absolute; // left: -100%;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      padding: 0;
      cursor: pointer;
    }
  }
}
