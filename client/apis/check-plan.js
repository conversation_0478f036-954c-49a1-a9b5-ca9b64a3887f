// 纪实结果检查相关接口调用
import { http } from "client/tool/axios";
import { mtHost } from './config';

export const getMeetingReview = (data = {}) => {
  return http.get(`${mtHost}/meeting/review`, data);
}

// /result/check/list 03-上级 - 查询纪实结果列表（分页）
export const getResultCheckList = (data = {}) => {
  return http.get(`${mtHost}/result/check/list`, data);
}

// /result/detail/ 10300010604 04- 查看纪实结果详情
export const getResultDetail = (id, is_edit = 0) => {
  return http.get(`${mtHost}/result/detail/${id}`, { is_edit });
}


// /meeting/detail/{id} 10300010604-查询会议详情
// 默认is_edit为0，不是编辑时查询详情
export const getMeetingDetail = (id, is_edit = 0) => {
  return http.get(`${mtHost}/meeting/detail/${id}`, { is_edit });
}

// /result/check/back/list?meeting_id=1231 10-上级 - 退回纪录列表
export const getResultCheckBackList = (meeting_id) => {
  return http.get(`${mtHost}/result/check/back/list`, { meeting_id });
}

// /result/user/count?meeting_id=123 12-上级-下级 - 纪实结果人员信息统计
export const getResultUserCount = (meeting_id) => {
  return http.get(`${mtHost}/result/user/count`, { meeting_id });
}

// /meeting/hs/{meeting_id} 10300010614-会议历史记录
export const getMeetingHs = (meeting_id) => {
  return http.get(`${mtHost}/meeting/hs/${meeting_id}`);
}

// /result/check 07-上级 - 检查通过 / 退回 检查状态，非修改状态
export const postResultCheck = (data = {}) => {
  return http.post(`${mtHost}/result/check`, data);
}

// /result/update/leader 06-修改纪实结果 - 上级
export const postResultSubmit = (data = {}) => {
  return http.post(`${mtHost}/result/update/leader`, data);
}

// /type/list-all 10300010107-查询会议类型列表（all,会议类型管理页面使用）
export const getAllCategoryList = (params = {}) => {
  return http.get(`${mtHost}/type/list-all`, params);
}