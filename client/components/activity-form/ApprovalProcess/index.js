/**
 * 审批流程
 * <AUTHOR>
 * 2018/4/24
 */
import React from 'react'
import PropTypes from 'prop-types'
import {
    Row,
    Col,
    Spin,
    Select,
    Checkbox
} from 'antd'
const Option = Select.Option

import PeopleCover from 'components/people-cover'
import adaptiveLayout from '../gridLayout'

class ApprovalProcess extends React.Component {

    constructor(props) {
        super(props);

        this.inited = false;
    }

    handleChange(approvalName) {
        const { formFields, form, activityFormState, handleEvents } = this.props
        const { setFieldsValue } = form

        const selectApproval = activityFormState.approvalTypeList.filter(i => +i.workflowId === +approvalName)[0] || {}
        setTimeout(() => { setFieldsValue({ [formFields.approvalType]: selectApproval.name || '' }) }, 0)
        console.log(selectApproval, 222222222);
        handleEvents.selectApprovalType(selectApproval)
    }

    componentDidUpdate(prevProps, prevState) {
        const {
            formFields,
            form,
            activityFormState,
        } = this.props;
        const prevActivityFormState = prevProps.activityFormState;

        const { setFieldsValue, getFieldValue } = form;
        //初始化时 比对更新表单isApproval标志位
        if (!this.inited && activityFormState.isApproval != prevActivityFormState.isApproval) {
            setFieldsValue({
                [formFields.isApproval]: activityFormState.isApproval
            });

            this.inited = true;
        }

    }

    componentDidMount() {
        // const {
        //     formFields,
        //     form,
        //     activityFormState,
        // } = this.props;
        //
        //
        // if(form){
        //   const { setFieldsValue, getFieldValue } = form;
        //
        //   console.log(activityFormState, form, setFieldsValue);
        //   console.log(getFieldValue(formFields.isApproval), activityFormState.isApproval)
        //
        //   setTimeout(()=>{
        //     console.log('set', activityFormState.isApproval)
        //     setFieldsValue({
        //       [formFields.isApproval] : activityFormState.isApproval
        //     });
        //   }, 1000)
        // }
    }

    render() {
        const {
            form,
            preview,
            formFields,
            handleEvents,
            activityFormState
        } = this.props;

        const { updateState, searchApprovalList } = handleEvents;

        const { getFieldDecorator, getFieldValue } = form;

        // console.log(activityFormState, getFieldValue(formFields.isApproval));
        //取消 activityFormState.isApproval || getFieldValue(formFields.isApproval) 写法
        // 当activityFormState.isApproval为false 时， 永远无法联动
        const approvalDisabled = getFieldValue(formFields.isApproval)  //'isApproval' in activityFormState ? activityFormState.isApproval : getFieldValue(formFields.isApproval);

        //默认审批类型
        const approvalTypeDefault =
            activityFormState.approvalType.name ||
            (activityFormState.approvalTypeList ? ((activityFormState.approvalTypeList[0] || {}).name || '') : "")

        //审批状态 1:正常|2:审批中|3:审批不通过
        //是否禁止修改审批流程
        let isDisabledEdit = false
        switch (activityFormState.approvalStatus) {
            case 1:
            case 2:
                isDisabledEdit = true
        }

        return <div style={{ position: 'relative' }}>
            <Row className="ant-form-item">
                <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                    <label title="审批类型">审批类型</label>
                </Col>
                <Col {...adaptiveLayout.formWrapper.wrapperCol} style={{ paddingTop: 5 }}>
                    {getFieldDecorator(formFields.approvalType, {
                        initialValue: approvalTypeDefault,
                        rules: [{
                            required: false, message: '审批类型的名称'
                        }]
                    })(
                        <Select
                            style={{ width: 200, marginRight: 30 }}
                            mode="combobox"
                            disabled={approvalDisabled || isDisabledEdit || preview}
                            allowClear
                            placeholder="搜索审批类型名字"
                            notFoundContent={activityFormState.approvalTypeLoading ? <Spin size="small" /> : '没有审批类型'}
                            filterOption={false}
                            onSearch={handleEvents.searchApprovalList}
                            onSelect={this.handleChange.bind(this)}>
                            {activityFormState.approvalTypeList.map((item, index) => {
                                return <Option key={item.workflowId}>{item.name}</Option>
                            })}
                        </Select>
                    )}
                    {getFieldDecorator(formFields.isApproval, {
                        initialValue: activityFormState.isApproval,
                        valuePropName: 'checked'
                    })(
                        <Checkbox onChange={(e) => {
                            const target = e.target;
                            // const checked = target.checked;
                            // if (!target.checked) {
                            //     searchApprovalList && searchApprovalList();
                            // }
                            updateState && updateState({
                                isApproval: target.checked
                            });
                        }} disabled={isDisabledEdit || preview}>不要审批</Checkbox>
                    )}
                </Col>
            </Row>
            {
                approvalDisabled ? null : <div>
                    {
                        (activityFormState.approvalStaff.length === 0 && preview) ? null : <Row className="ant-form-item">
                            <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                                <label title="审批人员">审批人员</label>
                            </Col>
                            <Col {...adaptiveLayout.formWrapper.wrapperCol} className="approval-process">
                                {
                                    activityFormState.approvalStaff.map((item, index) => (
                                        <PeopleCover
                                            key={index}
                                            name={item.name}
                                            disabled={isDisabledEdit || preview}
                                            onClose={handleEvents.approvalStaffDelete.bind(this, item, index)}
                                        />
                                    ))
                                }
                                <PeopleCover
                                    isAdd={true}
                                    disabled={isDisabledEdit || preview}
                                    onAdd={handleEvents.centerContentShow.bind(this, 1)} />
                            </Col>
                        </Row>
                    }
                    {
                        (activityFormState.CcStaff.length === 0 && preview) ? null : <Row className="ant-form-item">
                            <Col {...adaptiveLayout.formWrapper.labelCol} className="ant-form-item-label">
                                <label title="抄送人员">抄送人员</label>
                            </Col>
                            <Col {...adaptiveLayout.formWrapper.wrapperCol}>
                                {
                                    activityFormState.CcStaff.map((item, index) => (
                                        <PeopleCover
                                            key={index}
                                            name={item.name}
                                            disabled={isDisabledEdit || preview}
                                            onClose={handleEvents.CcStaffDelete.bind(this, item, index)}
                                        />
                                    ))
                                }
                                <PeopleCover
                                    isAdd={true}
                                    disabled={isDisabledEdit || preview}
                                    onAdd={handleEvents.centerContentShow.bind(this, 2)} />
                            </Col>
                        </Row>
                    }

                </div>
            }
        </div>
    }
}

ApprovalProcess.propTypes = {
    //预览模式
    preview: PropTypes.bool,

    //antd表单对象
    form: PropTypes.object.isRequired,

    //表单处理的事件合集
    handleEvents: PropTypes.shape({
        //审批人员删除
        approvalStaffDelete: PropTypes.func.isRequired,
        //打开选择人员
        centerContentShow: PropTypes.func.isRequired,
        //抄送人员删除
        CcStaffDelete: PropTypes.func.isRequired,
        //查询审批列表
        searchApprovalList: PropTypes.func.isRequired,
        //选择审批项(这个方法主要用来在选择时候保存选择的审批数据)
        selectApprovalType: PropTypes.func.isRequired
    }).isRequired,

    //表单字段
    formFields: PropTypes.shape({
        //是否需要审批
        isApproval: PropTypes.string.isRequired,
        //审批类型
        approvalType: PropTypes.string.isRequired
    }).isRequired,

    //表单model
    activityFormState: PropTypes.shape({
        //是否需要审批
        isApproval: PropTypes.bool.isRequired,
        //审批类型
        approvalType: PropTypes.shape({
            //审批类型名字
            name: PropTypes.string.isRequired,
            //审批类型id
            id: PropTypes.number.isRequired
        }).isRequired,
        //审批人员数据
        approvalStaff: PropTypes.array.isRequired,
        //抄送人员数据
        CcStaff: PropTypes.array.isRequired,
        //审批类型列表
        approvalTypeList: PropTypes.array.isRequired,
        //查询审批类型列表loading
        approvalTypeLoading: PropTypes.bool,
        //审批状态
        approvalStatus: PropTypes.number.isRequired
    }).isRequired
}

ApprovalProcess.defaultPorps = {
    preview: false,
    approvalTypeLoading: false
}

export default ApprovalProcess
