import IntranetShiguan from "./intranet-shiguan";
import IntranetXianguan from "./intranet-xianguan";
import SaasShiguan from "./saas-shiguan";
import SaasXianguan from "./saas-xianguan";
import { INTRANET_LIST } from "../constants";

export default function EvaluationCode(props) {
  const { history, location } = props;

  const query = new URLSearchParams(location.search);
  // 【临时】县管/市管类型 10 市管 8 县管 9 中层
  const type = query.get("type");
  const orgId = query.get("orgId");
  const evaluationId = query.get("evaluationId");
  const evaluationName = query.get("evaluation_name");

  const hostname = window.location.hostname;
  const isSaas = !INTRANET_LIST.some((item) => hostname.includes(item));

  console.log(
    "EvaluationCode",
    evaluationId,
    new Array("8", "9").indexOf(type)
  );

  return (
    <React.Fragment>
      {/* 内网 市管 */}
      {type === "10" && !isSaas && (
        <IntranetShiguan evaluationId={evaluationId} orgId={orgId} {...props} />
      )}
      {/* 内网 县管（班子、中层） */}
      {type !== "10" && !isSaas && (
        <IntranetXianguan
          evaluationId={evaluationId}
          type={type}
          orgId={orgId}
          {...props}
        />
      )}
      {/* Saas 市管 */}
      {type === "10" && isSaas && (
        <SaasShiguan
          evaluationId={evaluationId}
          orgId={orgId}
          evaluationName={evaluationName}
          {...props}
        />
      )}
      {/* Saas 县管（班子、中层） */}
      {type !== "10" && isSaas && (
        <SaasXianguan
          evaluationId={evaluationId}
          orgId={orgId}
          evaluationName={evaluationName}
          type={type}
          {...props}
        />
      )}
    </React.Fragment>
  );
}
