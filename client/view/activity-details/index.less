.activity-details {
    .ant-tabs-nav .ant-tabs-tab {
        height: auto;
    } //  所有的样式文件写在这里
    .activity-details-main {
        padding: 28px 30px;
        .table-wrapper {
            .ant-divider.ant-divider-horizontal.ant-divider-with-text {
                margin: 50px 0 28px 0;
            }
            &::before {
                content: '';
                display: table;
            }
            &.summary-table-wrapper {
                margin-top: 23px;
            }
            &.people-table-wrapper {
                .ant-divider.ant-divider-horizontal.ant-divider-with-text {
                    margin-bottom: 16px;
                }
                .tab-container {
                    position: relative;
                    .download-button {
                        position: absolute;
                        top: 0;
                        right: 0;
                        z-index: 9;
                    }
                    .ant-tabs-bar {
                        border-bottom: none;
                    }
                    // .ant-table-bordered .ant-table-tbody>tr>td {
                    //     vertical-align: middle;
                    // }
                }
            }
        }
        .header-wrapper {
            height: 148px;
            display: flex;
        }
    }
    .sider-main {
        padding: 28px;
        .ant-row.ant-form-item {
            margin-right: 15px;
            margin-bottom: 20px;
            .ant-input{
                width: 140px;
            }
        }
        .form-item-wrapper {
            display: flex;
            align-items: center;
            .form-item-label {
                text-align: right;
                font-size: 12px; //width: 60px;
                flex-shrink: 0;
                padding: 0 10px 0 20px;
            }
        }
        .sider-content-section {
            margin-top: 10px;
        }
        .btn-wrap{
            display: flex;
            flex-direction: row;
            height: 42px;
            align-items: center;
            .export{
                margin-left: 10px;
                width: 80px;
            }
        }
    }
}

.modal-main {
    .ant-modal-content {
        border-radius: 0;
        .ant-modal-body {
            padding: 33px;
        }
    }
    .modal-form-item {
        min-height: 32px;
        margin-bottom: 20px;
        &.before-modal-form-divider {
            margin-bottom: 0;
        }
    }
    .activity-list {
        .activity-list-item {
            line-height: 40px; // cursor: pointer;
            // &:hover {
            //     color: #F46E65;
            // }
        }
    }
}