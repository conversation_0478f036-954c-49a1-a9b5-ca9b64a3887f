import { But<PERSON>, Checkbox, Modal, Table } from "antd";
import {
  leader<PERSON><PERSON><PERSON>
} from "client/apis/cadre-portrait";
import AdvancedSearchForm from "components/search-form/advancedSearchForm";
import { useEffect, useState } from "react";
import "./index.less";
function PersonnelSearch(props) {
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pages, setPages] = useState(0);
  const [query, setQuery] = useState({
    // page: 1,
    // page_size: 10,
    cadre_type: "",
    sort_type: "",
    cadre_sort_type: "",
    name: "",
    gender: "",
    ethic: "",
    birthday_start: "",
    birthday_end: "",
    political: "",
    join_time_start: "",
    join_time_end: "",
    technical_position: "",
    profession_specialty: "",
    cadre_category: "",
    current_job: "",
    current_job_time_gte: "",
    current_job_time_lte: "",
    current_rank: "",
    current_rank_time_gte: "",
    current_rank_time_lte: "",
    identity: "",
    full_time_education: "",
    on_job_education: "",
    full_time_school: "",
    major: "",
    information: "",
    responsibilities: "",
    speciality: "",
    label: "",
    source: "",
    base_year_start: "",
    base_year_end: "",
    year_examine_start: "",
    year_examine_end: "",
    examine_count: "",
    examine_level: "",
    cadre_rank: "",
    cadre_rank_time_gte: "",
    cadre_rank_time_lte: "",
  });
  const [downloadColumns, setDownloadColumns] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [isDownloadModalVisible, setIsDownloadModalVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false); // 新增状态，用于控制查询区域的展开/收起

  useEffect(() => {
    loadData(1);
  }, []);

  // 获取干部列表数据源
  const loadData = async (page) => {
    setLoading(true);
    const res = await leaderQuery({
    
      ...query,
     
    });
    setLoading(false);
    if (res.data.code === 0) {
      const { data, total, pages } = res.data.data;
      setDataSource(data);
      setTotal(total);
      setPages(pages);
      setQuery({
        ...query,
        page,
      });
    }
  };

  const columns = [
    {
      title: "序号",
      dataIndex: "user_id",
      key: "user_id",
      align: "center",
      width: "200px",
    },
    {
      title: "姓名",
      dataIndex: "username",
      key: "username",
      align: "center",
    },
    {
      title: "现任职务",
      dataIndex: "current_job",
      key: "current_job",
      align: "center",
      width: "200px",
    },
    {
      title: "出生年月",
      dataIndex: "birthday",
      key: "birthday",
      align: "center",
      width: "200px",
    },
    {
      title: "全日制学历",
      dataIndex: "diploma",
      key: "diploma",
      align: "center",
    },
    {
      title: "全日制学历毕业学校及专业",
      dataIndex: "school",
      key: "school",
      align: "center",
      width: "200px",
    },
    {
      dataIndex: "cadre_index",
      key: "cadre_index",
      align: "center",
      width: "200px",
      title: "干部指数",
    },
    {
      dataIndex: "age",
      key: "age",
      align: "center",
      width: "200px",
      title: "年龄",
    },
    {
      dataIndex: "head_url",
      key: "head_url",
      align: "center",
      title: "头像",
    },
  ];

  const handleDownloadCustom = () => {
    setIsDownloadModalVisible(true);
  };

  const handleDownloadModalOk = () => {
    const selected = downloadColumns.filter((item) => item.checked).map((item) => item.key);
    setSelectedColumns(selected);
    setIsDownloadModalVisible(false);
  };

  const handleDownloadModalCancel = () => {
    setIsDownloadModalVisible(false);
  };

  const handleCheckboxChange = (e, key) => {
    const newDownloadColumns = downloadColumns.map((item) =>
      item.key === key ? { ...item, checked: e.target.checked } : item
    );
    setDownloadColumns(newDownloadColumns);
  };


  // 控制查询区域的展开和收起
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="personnel-search">
      <div className="top-box">
        <div className="sm-t-left">
          <Button
            type="default"
            onClick={toggleExpand}
            style={{ marginLeft: 10 }}
          >
            {isExpanded ? "收起" : "搜索"}
          </Button>
        </div>
        <div className="sm-r-right">
          <div className="button">
            <Button
              type="primary"
              onClick={handleDownloadCustom}
            >
              下载
            </Button>
          </div>
        </div>
      </div>
      <div className={`search-area ${isExpanded ? "expanded" : "collapsed"}`}>
        {/* 查询区域内容 */}
        <div className="search-content">
         <AdvancedSearchForm/>
        </div>
      </div>
      <div className="table-box">
        <Table
          dataSource={dataSource}
          columns={columns}
          bordered
          rowKey="code"
          loading={loading}
          pagination={{
            size: "small",
            showQuickJumper: true,
            pageSize: query.page_size,
            total,
            current: query.page,
            showTotal: ($total) => `共 ${$total} 条记录，共 ${pages} 页`,
            onChange: (page) => {
              loadData(page);
            },
          }}
        />
      </div>
      <Modal
        title="自定义下载"
        visible={isDownloadModalVisible}
        onOk={handleDownloadModalOk}
        onCancel={handleDownloadModalCancel}
      >
        {downloadColumns.map((item) => (
          <div key={item.key} style={{ marginBottom: 10 }}>
            <Checkbox
              checked={item.checked}
              onChange={(e) => handleCheckboxChange(e, item.key)}
            >
              {item.title}
            </Checkbox>
          </div>
        ))}
      </Modal>
    </div>
  );
}

export default PersonnelSearch;