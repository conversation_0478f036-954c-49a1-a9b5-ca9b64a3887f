import React, { Component } from 'react';
import { connect } from 'dva';
import Image from './upload-image';
import './index.less';
const {
  Modal, 
  Form, 
  Input, 
  Button, 
  Radio, 
  message,
  Select
} = Ant
const Option = Select.Option;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 4 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 16 },
    sm: { span: 16 },
  },
};
const imageSize = [
  { w: "auto", h: "auto" },
  { w: 690, h: 240 },
  { w: 690, h: 222 },
  { w: 335, h: 120 }
]
class ModalBulletin extends Component {
  constructor() {
    super();
    this.state = {
      image_size: 0,
      data: {}
    };
  }
  componentDidMount() {}
  componentDidUpdate(prevProps) {
    const { baweiyuzhen, form } = this.props;
    const { show, index } = baweiyuzhen.status.bulletin_board;
    if (show && show !== prevProps.baweiyuzhen.status.bulletin_board.show) {
      this.setState({
        image_size: 0
      })
      if (index !== undefined) {
        const data ={ ...baweiyuzhen.formData.bulletin_board[index]};
        if (data.target_url.includes('http')) {
          data.other_url = data.target_url
          data.target_url = -1
        }
        this.setState({data})
        form.setFieldsValue(data);
      }
    }
  }
  onSubmit() {
    const { form, baweiyuzhen, dispatch } = this.props;
    form.validateFields((err, value) => {
      if (err) return;
      if (value.other_url && value.target_url === -1) {
        value.target_url = value.other_url
        delete value.other_url
      }
       dispatch({
        type: 'baweiyuzhen/submitModal',
        payload: { data: { ...this.state.data, ...value }, type: 'bulletin_board' },
      });
      this.onCancel();
    });
  }
  onCancel() {
    const { dispatch, form } = this.props;
    form.resetFields();
    this.setState({
      files: [],
    });
    dispatch({
      type: 'baweiyuzhen/updateStatus',
      payload: { bulletin_board: { show: false } },
    });
  }

  render() {
    const { baweiyuzhen, form } = this.props;
    const { image_size, data } = this.state
    if (!baweiyuzhen) {
      return null;
    }
    const { bulletin_board } = baweiyuzhen.status;
    const { topic_list } = baweiyuzhen.formData;
    const { index, show } = bulletin_board;
    const { getFieldValue, getFieldDecorator } = form;
    const selectBefore = (
      <Select defaultValue="http://">
        <Option value="http://">http://</Option>
        <Option value="https://">https://</Option>
      </Select>
    );
    return (
      <div>
        <Modal
          title={`${index === undefined ? '新增' : '编辑'}公告`}
          visible={show}
          width={660}
          wrapClassName="modal-baweiyuzhen"
          bodyStyle={{ minHeight: 400, width: 660, margin: 'auto' }}
          destroyOnClose
          onCancel={this.onCancel.bind(this)}
          footer={null}>
          <Form {...formItemLayout}>
            <FormItem label="公告名称">
              {getFieldDecorator('name', {
                rules: [
                  {
                    required: true,
                    message: '请填写公告名称',
                  },
                ],
              })(<Input placeholder="" />)}
            </FormItem>
            <FormItem label="链接">
              {getFieldDecorator("target_url", {
                rules: [{ required: true, message: "请选择链接" }]
              })(
                <Select placeholder="请选择">
                  {
                    topic_list.length && topic_list.map((val,index) => {
                      return <Option key={index} value={val.target_url}>
                        {val.topic_name}
                      </Option>
                    })
                  }
                  <Option key={-1} value={-1}>其他 </Option>
                </Select>
              )}
            </FormItem>
            {
              getFieldValue("target_url") == -1 && 
              <FormItem label="其他链接">
                {getFieldDecorator("other_url", {
                  initialValue: data.other_url,
                  rules: [
                    { required: true, message: "请输入链接" },
                    { pattern: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/ , message: "请输入正确的链接"}
                  ]
                })(
                  <Input/>
                )}
              </FormItem>
            }
            <FormItem label="图片大小">
              <Radio.Group value={image_size} onChange={({ target: { value } }) => this.setState({ "image_size": value })}>
                <Radio value="1">690*240</Radio>
                <Radio value="2">690*222</Radio>
                <Radio value="3">335*120</Radio>
              </Radio.Group>
            </FormItem>
            <FormItem label="图片">
              {getFieldDecorator('image_url', {
                rules: [
                  {
                    required: true,
                    message: '请上传图片',
                  },
                ],
              })(<Image size={imageSize[image_size]}/>)}
            </FormItem>
            <FormItem label="状态">
              {getFieldDecorator('is_show', {
                rules: [
                  {
                    required: true,
                    message: '请选择显示状态',
                  },
                ],
              })(
                <Radio.Group>
                  <Radio value={1}>显示</Radio>
                  <Radio value={0}>隐藏</Radio>
                </Radio.Group>
              )}
            </FormItem>
          </Form>
          <div className="buttons-wrapper">
            <Button
              onClick={() => {
                this.onCancel();
              }}>
              取消
            </Button>
            <Button type="primary" onClick={() => this.onSubmit()}>
              确定
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}
export default connect(({ baweiyuzhen }) => ({ baweiyuzhen }))(Form.create()(ModalBulletin));
