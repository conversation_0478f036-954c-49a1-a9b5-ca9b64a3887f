import React, { Component } from "react";
import { Icon, message } from "antd";
import Clip from "components/clip/clipping";
import { uploadBase64File } from "apis/file";
import { CDN } from "apis/config";

class ImageClip extends Component {
  constructor() {
    super();
    this.state = {
      showClip: false,
    };
    this.onClick = this._onClick.bind(this);
    this.cancel = this._cancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
  }
  _onClick() {
    this.setState({
      showClip: true,
    });
  }
  // 提交图片
  _onSubmit(base64) {
    const { onChange } = this.props
    uploadBase64File({
      upfile: base64,
      upType: "image",
    }).then(({ data: res }) => {
      if (res.code != 0) {
        message.error(res.message);
      } else {
        this.setState({
          showClip: false,
        });
        // 上传的文件
        onChange(res.data)
      }
    })
  }
  // 关闭裁剪框
  _cancel() {
    this.setState({
      showClip: false,
    });
  }
  render() {
    const { size, src, title } = this.props;
    var imgSrc = "";
    if (src && src.url) {
      imgSrc = `${CDN}/${src.url}`;
    }
    return (
      <div className="imageClip" onClick={this.onClick}>
        {src && !src.url && (
          <div className="up-file">
            <Icon type="plus" />
            <span className="tips">点击上传场地照片</span>
          </div>
        )}
        {this.state.showClip ? (
          <Clip
            src={imgSrc}
            size={size}
            title={title}
            onCancel={this.cancel}
            onSubmit={this.onSubmit}
          />
        ) : null}
      </div>
    );
  }
}

export default ImageClip;
