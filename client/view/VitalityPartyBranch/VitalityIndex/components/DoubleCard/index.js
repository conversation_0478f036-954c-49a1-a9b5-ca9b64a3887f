import { useState, useEffect, useMemo } from "react";
import { Card, Select, Icon, Table, Radio, DatePicker } from "antd";
import ReactEcharts from "echarts-for-react";
import moment from "moment";
import { YearsData } from "client/tool/util";
import ItemWrap from "../ItemWrap";
import "./index.less";
import { getLineChartTop, getOrganizationRankingTop } from "apis/Vitality-index";

const { MonthPicker } = DatePicker;
const { Option } = Select;
const RadioGroup = Radio.Group;

const compParams = {
  "0-2": {
    showRadio: true,
    title: "村/社党组织概况",
    title1: "党组织活力指数",
    tilte2: "组织活力指数榜",
    cardTitle1: "组织均值",
  },
  "0-3": {
    showRadio: false,
  },
};

const DoubleCard = ({ type, cardType, org_id, setSelect, setSearchType, searchType, select, dateVal, setDateVal, res, datas, history }) => {
  // 获取页面中各模块的title等信息
  const titles = compParams[cardType] || {};
  const { data: yearList } = YearsData(2019, false);
  const [option, setOption] = useState({});
  const [cardData, setCardData] = useState([]);
  useEffect(() => {
    getDataSourceFun();
  }, [dateVal, type, res]);

  const getDataSourceFun = async (value) => {
    // const date = moment(dateVal).format(
    //   searchType === "year" ? "YYYY" : "YYYYMM"
    // );
    // console.log("🚀 ~ file: index.js:59 ~ date:", date);
    // 请求接口获取数据
    const avg = res.data.sta_result_year !== undefined ? res.data.sta_result_year.user_year_avg : ''
    const max = res.data.sta_result_year !== undefined ? res.data.sta_result_year.user_year_max : ''
    const min = res.data.sta_result_year !== undefined ? res.data.sta_result_year.user_year_min : ''
    const num1=avg!==''?avg:res.data.sta_result.均值[res.data.sta_result.均值.length - 1]==undefined?'-':`${res.data.sta_result.均值[res.data.sta_result.均值.length - 1]}`
    const num2=avg!==''?max:res.data.sta_result.最高[res.data.sta_result.最高.length - 1]==undefined?'-':`${res.data.sta_result.最高[res.data.sta_result.最高.length - 1]}`
    const num3=avg!==''?min:res.data.sta_result.最低[res.data.sta_result.最低.length - 1]==undefined?'-':`${res.data.sta_result.最低[res.data.sta_result.最低.length - 1]}`

    const cardData = [
      {
        backgroundColor: "#867EEC",
        type: 1,
        text: titles.cardTitle1 || "党员均值",
        num: num1=='-'?num1:titles.cardTitle1?num1:Math.round(num1),
      },
      {
        backgroundColor: "#E9F3FF",
        text: "最高",
        num: num2=='-'?num2:titles.cardTitle1?num2:Math.round(num2),
      },
      {
        backgroundColor: "#E7FAE5",
        text: "最低",
        num: num3=='-'?num3:titles.cardTitle1?num3:Math.round(num3),
      },
    ];

    setCardData(cardData);
    setOptionFn(res.data);
  };

  const setOptionFn = (data) => {
    const option = {
      grid: {
        left: 30,
        right: 30,
        top: 40,
        bottom: "1%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
        formatter: (params) => {
          return params[0].name + '<br/>' + params[0].seriesName + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[0].value}<right/>` + '<br/>' + params[1].seriesName + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[1].value}<right/>` + '<br/>' + params[2].seriesName + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[2].value}<right/>`
        },
      },
      legend: {
        data: ["组织均值", "最高", "最低"],
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: res.data.sta_months,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          color: '#867EEC',
          name: "组织均值",
          type: "line",
          // stack: "Total",
          data: res.data.sta_result.均值,
        },
        {
          color: '#60CDF4',
          name: "最高",
          type: "line",
          // stack: "Total",
          data: res.data.sta_result.最高,
        },
        {
          color: '#5DCD73',
          name: "最低",
          type: "line",
          // stack: "Total",
          data: res.data.sta_result.最低,
        },
      ],
    };
    setOption(option);
  };

  const columns = [
    {
      title: "排名",
      dataIndex: "rank_num",
      key: "rank_num",
      width: 50,
      // render: (text) => <a>{text}</a>,
    },
    {
      title: "组织名称",
      dataIndex: "org_name",
      key: "org_name",
    },
    {
      title: "组织类型",
      dataIndex: "org_type_name",
      key: "org_type_name",
      width: 80,
    },
    {
      title: "组织类别",
      key: "org_category",
      dataIndex: "org_category",
      width: 80,
    },
    {
      title: "活力指数",
      key: "vital_index",
      dataIndex: "vital_index",
      width: 80,
      align: 'center',
      render: (text) => <span style={{ color: '#FE3A3B' }}>{text}</span>,
    },
  ];
  const Usercolumns = [
    {
      title: "排名",
      dataIndex: "rank_num",
      key: "rank_num",
      width: 50,
      // render: (text) => <a>{text}</a>,
    },
    {
      title: "党员",
      dataIndex: "user_name",
      key: "user_name",
      width: 65,
    },
    {
      title: "所属支部",
      dataIndex: "org_name",
      key: "org_name",
    },
    {
      title: "活力指数",
      key: "vital_index",
      dataIndex: "vital_index",
      width: 80,
      align: 'center',
      render: (text) => <span style={{ color: '#FE3A3B' }}>{text}</span>,
    },
  ];
  //切换村/社党组织概况
  const onChange = (value) => {
    setSelect(value.target.value)
    getDataSourceFun(value.target.value)
  }

  return (
    <Card bordered={false} title={titles.title || "党员概况"}>
      {titles.title == '村/社党组织概况' ?
        <div className="selection">
          <Radio.Group onChange={onChange} value={select}>
            <Radio value={''}>全部</Radio>
            <Radio value={104801}>城市社区</Radio>
            <Radio value={104802}>涉农社区</Radio>
            <Radio value={104803}>村</Radio>
          </Radio.Group>
        </div>
        : ''}
      <div className="doubleCard">
        <Card bordered={false}>
          <div className="title-wrap">
            <span className="title">{titles.title1 || "党员活力指数"}</span>
          </div>
          <div className="doubleCard-item">
            {cardData.map((item, key) => (
              <ItemWrap {...item} key={key} />
            ))}
          </div>
          <ReactEcharts option={option} />
        </Card>
        <Card bordered={false}>
          <div className="title-wrap">
            <div className="title">{titles.tilte2 || "党员活力指数榜"}</div>
            <div>
              {titles.showRadio && (
                <RadioGroup
                  value={searchType}
                  buttonStyle="solid"
                  onChange={({ target: { value } }) => {
                    setDateVal(moment().subtract(1, 'months'));
                    setSearchType(value);
                  }}
                >
                  <Radio.Button value="month">月度</Radio.Button>
                  <Radio.Button value="year">年度</Radio.Button>
                </RadioGroup>
              )}
              {searchType === "year" ? (
                <Select
                  value={Number(dateVal.format("YYYY"))}
                  onChange={(val) => setDateVal(moment(String(val)))}
                >
                  {yearList.map((year) => (
                    <Option key={year} value={year}>
                      {year}
                    </Option>
                  ))}
                </Select>
              ) : (
                <MonthPicker
                  allowClear={false}
                  value={dateVal}
                  disabledDate={(current) => current > moment().subtract(1, 'months')}
                  onChange={(val) => setDateVal(val)}
                />
              )}
              {titles.tilte2 ?
                <Icon type="right" onClick={() => history.push({ pathname: '/organizational-vitality-ranking', state: { back: true } })} /> :
                <Icon type="right" onClick={() => history.push({ pathname: '/member-vitality-ranking', state: { back: true } })} />
              }

            </div>
          </div>
          <Table className="table" columns={titles.tilte2 !== undefined ? columns : Usercolumns} dataSource={datas} pagination={false} />
        </Card>
      </div>
    </Card>
  );
};
export default DoubleCard;
