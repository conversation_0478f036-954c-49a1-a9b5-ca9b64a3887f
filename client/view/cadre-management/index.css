.cadre-management {
  width: 100%;
  height: 100%;
  display: flex;
}
.cadre-management .left {
  display: flex;
  width: 410px;
  background: #fff;
  flex-direction: column;
}
.cadre-management .left .left-header {
  width: 100%;
  height: 50px;
  background: #F1F5F8;
}
.cadre-management .left .left-header text h2 {
  height: 50px;
  line-height: 50px;
  background: #fff;
  width: 100px;
  text-align: center;
}
.cadre-management .left .left-header .ant-tabs {
  height: 40px;
}
.cadre-management .left .left-header .ant-tabs-tab {
  margin: 0;
  height: 40px;
  line-height: 38px;
  padding: 0 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px 4px 0 0;
  margin-right: 2px;
}
.cadre-management .left .left-header .ant-tabs-tab-active {
  background: #fff;
}
.cadre-management .left .left-header .ant-tabs-ink-bar {
  display: none !important;
}
.cadre-management .left .searchOrg {
  margin: 20px;
  width: 360px;
  background: #F7F8F9;
}
.cadre-management .left .orgContainer {
  position: relative;
  overflow: auto;
  background: #F7F8F9;
  min-height: 400px;
  max-height: 600px;
}
.cadre-management .right {
  flex: 1;
  margin-left: 10px;
  background: #fff;
  padding-bottom: 20px;
  position: relative;
  padding: 20px;
}
.cadre-management .right .cadre-header .header-search {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.cadre-management .right .cadre-header .header-search div {
  display: flex;
  align-items: center;
  margin-right: 30px;
}
.cadre-management .right .cadre-header .header-search .search-span {
  height: 100%;
  padding: 0px 10px;
  background: #FFF;
  border: 1px solid #cdcdcd;
  margin-right: 5px;
  cursor: pointer;
}
.cadre-management .right .cadre-header .header-search .search-active {
  height: 100%;
  padding: 0px 10px;
  border: 1px solid #cdcdcd;
  margin-right: 5px;
  cursor: pointer;
  color: #FFF;
  background: #a4adb3;
}
.cadre-management .right .cadre-header .header-search .search-btn div {
  width: 80px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1fa1fd;
  color: #FFF;
  cursor: pointer;
}
.cadre-management .right .cadre-header .header-search .search-btn .reset {
  color: #828a96;
  background: #f7f8fa;
}
.cadre-management .right .cadre-header .header-btn {
  width: 80px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1fa1fd;
  color: #FFF;
  cursor: pointer;
}
.cadre-management .right .cadre-header .header-btn i {
  margin-right: 10px;
}
.cadre-management .right .cadre-content {
  margin-top: 10px;
}
.cadre-management .right .cadre-content .handle {
  display: flex;
  justify-content: space-evenly;
}
