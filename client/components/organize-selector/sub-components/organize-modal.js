import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, message as Message } from "antd";
import "./organize-modal.less";

import TopForm from "./top-form";
import OrganizeList from "./organize-list";

import propTypes from "prop-types";

import { getOrgInfo } from "apis/organize";
import { fetchStorage } from "tool/util";
import { getFindTreeTypeByOp } from "apis/organisition";

class OrganizeModal extends Component {
  constructor() {
    super();
    this.state = {
      // 暂存当前树的组织类型，值从topForm获取
      org_type: null,
      // 默认查询都是子树
      tree_type: 2,
      memoryOrgId: null
    };
    this.exportData = this.exportData.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.initData = this.initData.bind(this);
  }

  componentDidMount() {
    const { isFfgr } = this.props;
    if (!isFfgr) {
      try {
        // 获取当前登录组织信息
        const userInfoString = fetchStorage(2, "userInfo");
        const userInfo = JSON.parse(userInfoString) || {};
        // console.log(userInfo);
        const { owner_tree } = userInfo;
        if (owner_tree) {
          this.setState({
            tree_type: owner_tree
          });
        }
      }
      catch (error) {
        console.error(error);
      }
    }
    // this.getOrgInfo({ org_id }).then((res) => {
    //   // console.log(res, res.owner_tree);
    //   if (res.owner_tree) {
    //     this.setState({
    //       tree_type: res.owner_tree
    //     });
    //   }
    // });
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      memoryOrgId: nextProps.memoryOrgId
    })
    // console.log(nextProps.memoryOrgId,'[[[[[[[[[-------]]]]]]]]')
  }

  async getOrgInfo(queryparams = {}) {
    const response = await getOrgInfo(queryparams);
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    return data;
  }

  initData(dataSource) {
    // console.log(this);
    // console.log(this.organizeList);
    if (this.organizeList) {
      // 初始化数据
      this.organizeList.initData(dataSource);
    }
  }

  // 对外暴露数据接口
  exportData() {
    if (this.organizeList) {
      const { loadOrganizeData } = this.props;
      const organizeListData = this.organizeList.exportData();
      loadOrganizeData && loadOrganizeData(organizeListData);
    }
  }

  // 对外暴露移除一个组织的方法
  removeItem(index, org) {
    this.organizeList && this.organizeList.removeOrg(index, org);
    // this.organizeList.removeOrg(index, org);
  }

  render() {
    const {
      visible,
      hideModal,
      dataSource,
      radio,
      disabledOrgList,
      checkAll,
      disabledOrgType1,
      distinguishOwnerTree,
      onlyDirectSub,
      isLeaderGroup,
      hasRoot,
      rootOrgId,
      rootOrgType,
      isFfgr,
      partyMasses,
      breadcrumbClick
    } = this.props;
    // console.log(isLeaderGroup, rootOrgId);
    const { org_type, tree_type } = this.state;
    const modalProps = {
      footer: null,
      title: "选择组织",
      visible,
      wrapClassName: "organize-modal",
      width: 900,
      onCancel: () => {
        hideModal();
      },
      afterClose: () => {
        // console.log("resetOrgs");
        this.organizeList && this.organizeList.resetOrgs();
      }
    };
    // console.log(disabledOrgType1, distinguishOwnerTree, isFfgr);
    const topFormProps = {
      rootOrgType,
      rootOrgId,
      isFfgr,
      isLeaderGroup,
      hasRoot,
      onlyDirectSub,
      tree_type,
      distinguishOwnerTree,
      updateState: (payload = {}, callback = () => { }) => {
        this.setState(payload, () => {
          callback && callback();
        });
      },
      // 当不区分owner_tree时，默认查询子树，组织类型第一级下拉框禁用
      disabledOrgType1: isFfgr ? false : (disabledOrgType1 || !distinguishOwnerTree),
      ref: ref => {
        this.topForm = ref;
      },
      topFormData: values => {
        if (this.state.memoryOrgId) { //这个if是李科加的，用来解决单位领导班子选择组织，选了下级后就不能通过重置和查询按钮回到上级的问题
          // const org_id = rootOrgId ? rootOrgId : (fetchStorage(2, "_oid") || 0);
          const org_id = this.state.memoryOrgId;
          const treeType = isFfgr ? (values.tree_type || 1) : (values.tree_type || tree_type);
          const orgType = values.org_type || null;
          this.setState({
            tree_type: treeType,
            org_type: org_type
          });
          if (values.isReset) {
            this.organizeList.fetchOrganize({
              type: 1,
              org_id,
              tree_type: treeType,
              org_type: values.org_type,
              root: true
            });
          }
          else {
            this.organizeList.fetchOrganize({
              type: 2,
              org_id,
              tree_type: treeType,
              org_name: values.org_name,
              org_type: values.org_type,
              root: true
            });
          }
        } else {//这个else是原来的代码
          const org_id = rootOrgId ? rootOrgId : (fetchStorage(2, "_oid") || 0);
          // const org_id =this.state.memoryOrgId;
          const treeType = isFfgr ? (values.tree_type || 1) : (values.tree_type || tree_type);
          const orgType = values.org_type || null;
          this.setState({
            tree_type: treeType,
            org_type: orgType
          });
          // console.log("父组件收到顶部表单传递的", values);
          if (values.isReset) {
            this.organizeList.fetchOrganize({
              type: 1,
              org_id,
              tree_type: treeType,
              org_type: values.org_type,
              root: true
            });
          }
          else {
            // console.log("查询", values, org_id);
            this.organizeList.fetchOrganize({
              type: 2,
              org_id,
              tree_type: treeType,
              org_name: values.org_name,
              org_type: values.org_type,
              root: true
            });
          }
        }
        // if (values.org_name) {
        //   this.organizeList.fetchOrganize({
        //     type: 2,
        //     org_id,
        //     tree_type,
        //     org_name: values.org_name,
        //     org_type: values.org_type
        //   });
        // } else {
        //   this.organizeList.fetchOrganize({
        //     type: 1,
        //     org_id,
        //     tree_type,
        //     org_type: values.org_type
        //   });
        // }
        // this.setState({
        //   org_type: values.org_type || 0
        // });
        // console.log(this.organizeList);
      },
      initOrgType: (org_type, tree_type) => {
        const org_id = rootOrgId ? rootOrgId : (fetchStorage(2, "_oid") || 0);
        this.setState(
          {
            tree_type,
            org_type
          },
          () => {
            // 这里需要传递org_type
            this.organizeList.fetchOrganize({
              type: 1,
              org_id,
              org_type,
              tree_type,
              root: true
            });
          }
        );
      }
    };
    const organizeListProps = {
      isFfgr,
      isLeaderGroup,
      rootOrgId,
      rootOrgType,
      hasRoot,
      onlyDirectSub,
      tree_type,
      disabledOrgList,
      dataSource,
      org_type,
      radio,
      checkAll,
      partyMasses,
      breadcrumbClick,
      ref: ref => {
        this.organizeList = ref;
      }
    };
    return (
      <Modal {...modalProps}>
        <div className={onlyDirectSub ? "" : "top-form-wrapper"} style={onlyDirectSub ? { display: "none" } : {}}>
          <TopForm {...topFormProps} />
        </div>
        <div className="organize-list-wrapper">
          <OrganizeList {...organizeListProps} />
        </div>
        <div className="buttons-wrapper">
          <Button type="primary" onClick={this.exportData}>
            确定
          </Button>
          <Button onClick={() => {
            modalProps.onCancel();
          }}>取消</Button>
        </div>
      </Modal >
    );
  }
}

OrganizeModal.propTypes = {
  visible: propTypes.bool,
  dataSource: propTypes.array,
  hideModal: propTypes.func,
  loadOrganizeData: propTypes.func,
  radio: propTypes.bool,
  disabledOrgList: propTypes.array,
  checkAll: propTypes.bool,
  // 只能选择直接下级，筛选查询功能和列表点击下级按钮禁用
  onlyDirectSub: propTypes.bool,
  // 是否是领导班子
  isLeaderGroup: propTypes.bool,
  // 查询组织结果是否包含根节点，当前登录组织为根节点
  hasRoot: propTypes.bool,
  // 根节点组织ID
  rootOrgId: propTypes.number,
  // 根节点组织类型
  rootOrgType: propTypes.number,
  // 是否选择分管单位
  isFfgr: propTypes.bool,
  //like--用来解决单位领导班子选择组织，选了下级后就不能通过重置和查询按钮回到上级的问题
  memoryOrgId: propTypes.string,
};

OrganizeModal.defaultProps = {
  visible: false,
  dataSource: [],
  radio: false,
  disabledOrgList: [],
  checkAll: true,
  // 默认查询时区分父子树
  distinguishOwnerTree: false,
  onlyDirectSub: false,
  isLeaderGroup: false,
  hasRoot: false,
  rootOrgId: null,
  rootOrgType: null,
  isFfgr: false,
  memoryOrgId: null
};

export default OrganizeModal;
