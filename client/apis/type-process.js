import { workflowHost } from './config';
const { http } = require( 'client/tool/axios' );

// 查询所有标签/标签组列表
export const getTypeList = ( { name, page } ) => {
    return http.get( `${workflowHost}/type/list`, {
        name, page
    } );
};

export const getTypeListItem = (id) => {
    return http.get( `${workflowHost}/type/get/${id}` );
}
export const deleteTypeListItem = (id) => {
    return http.delete( `${workflowHost}/type/del/${id}` );
}

export const addTypeListItem = (data) => {
    return http.post( `${workflowHost}/type/add`, data);
}
