import React, { Component } from "react";
import { connect } from "dva";
import DynamicForm from "client/components/dynamic-form";
import { getBirthdayFromIdCard } from "client/tool/util";
import { form1, form2, form3 } from "./formFieldsData";
import IndexView from "./index-view";
import {
  getArea,
  getNativeArea,
  addOrgUserNew,
  updateOrgUserNew,
} from "client/apis/organize";
import moment from "moment";
import {
  getStatisticsNum,
  getOrgUserList,
  delOrgUser,
} from "apis/VitalityPartyBranch";
import "./index.less";

const { message, Drawer, Row, Col, Button } = Ant;
const querystring = require("querystring");

import {
  getOrgTree,
  locateOrgTree,
  getTreeList,
  findOrgByName,
  getUserTagList,
  insertUserTag,
  deleteUserTag,
} from "apis/organize";

class MemberManage extends Component {
  constructor(props) {
    super(props);

    this.treeItemPos = {};
    this.state = {
      isInitLoading: false,
      currentOrgId: null,
      selectedOrgInfo: {},
      selectedOrgId: null,
      orgTreeTabIndex: 0,
      orgTreeTabList: [],
      orgTreeTabSelectData: null,
      statisData: {
        organizeTotal: 0,
        basicOrgCount: 0,
        manageOrgCount: 0,
        orgUserTotal: 0,
        partyMemberCount: 0,
        manageUserCount: 0,
      },
      treeProps: {
        dataSource: [],
        keys: [],
        loadedKeys: [],
        currentKey: null,
        autoExpandParent: true,
      },
      tableProps: {
        isLoading: false,
        isSubmit: false,
        dataSource: [],
        selectedRowKeys: [],
        selectedData: [],
        pageSize: 10,
        pageNum: 1,
        pageTotal: 1,
        downloadURL: null,
      },
      queryProps: {
        isLoading: false,
        params: {},
      },
      searchSiderProps: {
        isLoading: false,
        isSearch: false,
      },
      memberSiderProps: {
        isEdit: false,
        title: null,
        isLoading: false,
        isSubmitLoading: false,
        memberPhone: null,
        memberParams: {},
        isClearPhoneShow: false,
        isPopupClickPhone: false,
        isPopupClickCard: false,
        memberType: true,
        isMemberDisabled: true,
        isCardDisabled: true,
      },
      labelManageProps: {
        isDelete: false,
        title: null,
        isVisible: false,
        isLoading: false,
        isSearchLoading: false,
        isSubmitLoading: false,
        dataSource: [],
        selectedRowKeys: [],
        selectedData: [],
        isDeleteVisible: false,
        orgIds: [],
        userIds: [],
      },
      autoCompleteProps: {
        dataSource: [],
      },
      formFields: {
        form1: JSON.parse(JSON.stringify(form1)),
        form2: JSON.parse(JSON.stringify(form2)),
        form3: JSON.parse(JSON.stringify(form3)),
      },
      isRootId: false,

      drawerFormList: [], // 高级查询表单列表
      tableHeadConfig: [], // 表头字段
      userDetailField: [], // 用户详情表单字段
      userDetailData: [], // 用户详情数据
      drawerVisible: false, // 高级查询抽屉
      isDetailVisible: false, // 人员详情抽屉
      drawerTitle: "",
      dynamicFormLoading: false,

      /** ---------用户中心4.0-动态表单相关------------------ **/
      drawerVisible: false,
      drawerTitle: "新建组织",
      drawerLoading: false,
      extraAttr: {},
      extraValue: {},
      fieldList: [], // 动态表单数据
      edit: false, // 是否编辑信息， false为非编辑（新增）, 编辑则为当前的org_id
      showSubmitBtn: true,
      showCancelBtn: true,
      areaOptions: [], // 省市区options数据
      userDeatils: {}, // 编辑存放的用户详情
      /**** 高级搜索  ****/
      advanceVisible: false, // 高级搜索显示
      advanceFieldList: [], // 高级表单列表

      /****** 普通搜索  ******/
      searchHeaderForm: [], // 头部查询表单列表
      searchValue: {}, // 搜索值

      /******* 其他 ******/
      checkPhoneInfoData: {}, // 手机验证回调的详情信息
      /******* 其他 ******/
      batchVisiable: false,
      /** ---------用户中心4.0-动态表单相关------------------ **/
    };
    this.methods = (form) => {
      return {
        checkPositiveTime: (rule, value, callback) => {
          const politicalType = form.getFieldValue("political_type");
          if ((politicalType === "1" || politicalType === "17") && !value) {
            callback("中共党员、正式党员需填写转正时间");
            return;
          }
          callback();
        },
        // 身份证失去焦点事件
        onCertNumberBlur: (e) => {
          const str = getBirthdayFromIdCard(String(e.target.value));
          if (str && form) {
            form.setFieldsValue({ birthday: moment(str) });
          }
        },
        // 验证手机号回调事件
        onCheckUserPhone: (data) => {
          this.setState({
            checkPhoneInfoData: data,
          });
        },
      };
    };

    this.rootOrgId =
      typeof window !== "undefined" &&
      window.sessionStorage.getItem("_root_oid");
  }

  componentDidMount() {
    const { userInfo } = this.props;
    const orgId = userInfo.oid;
    const { orgTreeTabIndex } = this.state;
    // this.initArea();
    this.setState(
      {
        currentOrgId: orgId,
        selectedOrgId: orgId,
        selectedOrgInfo: {
          level: userInfo.level,
          id: userInfo.oid,
          name: userInfo.name,
        },
      },
      () => {
        this.getTreeList({ orgId, orgTreeTabIndex });
      }
    );
  }

  // 初始化籍贯选项
  initArea() {
    getArea().then((res) => {
      const { data, status } = res.data;
      if (status === 200) {
        Promise.all(
          data.map(async (item) => {
            const child = (await getNativeArea({ parentid: item.adcode })).data
              .data;
            return child.map(({ area_name, adcode }) => ({
              label: area_name,
              value: String(adcode),
            }));
          })
        ).then((child_list) => {
          const formatData = data.map(({ area_name, adcode }, index) => ({
            label: area_name,
            value: String(adcode),
            children: child_list[index],
          }));
          this.setState({
            areaOptions: formatData,
          });
        });
      }
    });
  }

  resetFormFields(formId = 1) {
    const { formFields } = this.state;
    let newFormFields = null;
    if (formId == 1) {
      newFormFields = {
        form1: JSON.parse(JSON.stringify(form1)),
      };
    } else if (formId == 2) {
      newFormFields = {
        form2: JSON.parse(JSON.stringify(form2)),
      };
    } else if (formId == 3) {
      newFormFields = {
        form3: JSON.parse(JSON.stringify(form3)),
      };
    } else {
      newFormFields = {
        form1: JSON.parse(JSON.stringify(form1)),
        form2: JSON.parse(JSON.stringify(form2)),
        form3: JSON.parse(JSON.stringify(form3)),
      };
    }

    if (newFormFields) {
      this.setState({
        formFields: Object.assign(formFields, newFormFields),
      });
    }
  }

  resetAllFields() {
    const { queryProps, searchSiderProps } = this.state;

    if (this.queryForm) {
      this.queryForm.resetFields();
    }
    if (this.searchForm) {
      this.searchForm.resetFields();
    }

    this.resetFormFields();
    this.resetFormFields(2);

    this.setState({
      queryProps: Object.assign(queryProps, {
        params: {},
      }),
      searchSiderProps: Object.assign(searchSiderProps, {
        isSearch: false,
      }),
    });
  }
  async getOrgUserList(payload = {}) {
    let page = 1;
    if (payload && payload.page) {
      page = payload.page;
    }

    let resultParams = {};

    if (payload.isInit) {
      resultParams = payload;
    }
    const {
      searchSiderProps,
      queryProps,
      selectedOrgId,
      tableProps,
      isInitLoading,
      searchValue,
    } = this.state;
    const orgId = selectedOrgId;
    if (!isInitLoading) {
      this.setState({
        tableProps: Object.assign(tableProps, {
          isLoading: true,
        }),
        queryProps: Object.assign(queryProps, {
          isLoading: true,
        }),
        searchSiderProps: Object.assign(searchSiderProps, {
          isLoading: true,
        }),
      });
    }
    const result = (
      await getOrgUserList({
        ...searchValue,
        org_id: selectedOrgId,
        page,
      })
    ).data;

    if (!isInitLoading) {
      this.setState({
        tableProps: Object.assign(tableProps, {
          isLoading: false,
        }),
        queryProps: Object.assign(queryProps, {
          isLoading: false,
        }),
        searchSiderProps: Object.assign(searchSiderProps, {
          isLoading: false,
        }),
      });
    }
    if (result.code != 0) {
      this.setState({
        queryProps: Object.assign(queryProps, {
          params: {},
        }),
        searchSiderProps: Object.assign(searchSiderProps, {
          isSearch: false,
        }),
      });
      if (isInitLoading) {
        this.setState({
          isInitLoading: false,
        });
      }

      return message.error(result.message);
    }

    const dataSource = result.data.map((item, index) => {
      return {
        ...item,
        key: (result.pageNum - 1) * result.pageSize + (index + 1),
      };
    });

    // params['path'] = `/user/org/user/downExcel`;
    // params = querystring.stringify({ orgId, ...params });

    this.setState({
      tableProps: Object.assign(tableProps, {
        selectedRowKeys: [],
        selectedData: [],
        dataSource,
        isLoading: false,
        isSubmit: false,
        pageNum: result.pageNum,
        pageTotal: result.total,
        // downloadURL: `/user/org/user/exportOrgUser?${params}`,
      }),
    });
  }

  getAutoComplete(value) {
    const { currentOrgId, autoCompleteProps, orgTreeTabSelectData } =
      this.state;

    if (this.autoCompleteTimeout) {
      clearTimeout(this.autoCompleteTimeout);
      this.autoCompleteTimeout = null;
    }

    value = value.trim();
    if (!value) {
      this.setState({
        autoCompleteProps: Object.assign(autoCompleteProps, {
          dataSource: [],
        }),
      });
    } else {
      this.autoCompleteTimeout = setTimeout(async () => {
        const result = (
          await findOrgByName({
            org_id: currentOrgId,
            org_name: value,
            tree_type: orgTreeTabSelectData.tree_type,
            only_active: 1,
          })
        ).data;

        if (result.code != 0) {
          return message.error(result.message);
        }

        const data = result.data;
        this.setState({
          autoCompleteProps: Object.assign(autoCompleteProps, {
            dataSource: data
              .slice(0, data.length > 10 ? 10 : data.length)
              .map((val) => {
                return {
                  value: val.org_id,
                  name: val.org_name,
                };
              }),
          }),
        });
      }, 300);
    }
  }

  async getTreeList(payload = {}) {
    const { orgId, orgTreeTabIndex, isOpt, isOptTree } = payload;
    const currentIndex = orgTreeTabIndex || 0;

    let data;
    if (!isOpt) {
      this.setState({
        isInitLoading: true,
      });

      const result = (await getTreeList(orgId)).data;

      if (result.code !== 0) {
        this.setState({
          isInitLoading: true,
        });
        return message.error(result.message);
      }
      data = result.data.sort((a, b) => {
        if (!a.tree_type || !b.tree_type) {
          return 0;
        }
        return a.tree_type - b.tree_type;
      });
    } else {
      data = this.state.orgTreeTabList;
    }

    const { treeProps } = this.state;
    const { keys } = treeProps;
    const curData = data[currentIndex];
    if (curData) {
      if (!isOptTree) {
        const params = {};
        if (curData.tree_type == 2) {
          params["orgType"] = curData.org_type;
        }

        this.loadOrgTree(
          Object.assign(
            {
              treeType: curData.tree_type,
            },
            params
          )
        );
      }

      this.treeItemPos = {};
      if (keys && keys.length) {
        this.setState({
          treeProps: Object.assign(treeProps, {
            keys: [],
            loadedKeys: [],
          }),
        });
      }
      // 组织树切换
      this.setState({
        orgTreeTabIndex: currentIndex,
        orgTreeTabList: data,
        orgTreeTabSelectData: curData,
      });
    }
  }

  async loadOrgTree(payload = {}) {
    const { treeType, orgType, isTreeDisabled, target } = payload;
    const { orgTreeTabSelectData, treeProps, isInitLoading, selectedOrgId } =
      this.state;
    const { userInfo } = this.props;
    const { oid } = userInfo;

    let ttype = treeType;
    let torgType = orgType;
    if (!treeType) {
      ttype = orgTreeTabSelectData.tree_type;
    }
    if (!orgType && orgTreeTabSelectData) {
      torgType = orgTreeTabSelectData.org_type;
    }

    let p = {
      ["load_root"]: 1,
    };
    if (ttype == 2) {
      p["org_type"] = torgType;
    }
    this.resetAllFields();
    if (this.advancedSearchDynamicForm)
      this.advancedSearchDynamicForm.resetFields();
    if (this.searchDynamicFormRef) this.searchDynamicFormRef.resetFields();
    this.setState(
      {
        searchValue: {},
      },
      async () => {
        await this.getOrgUserList();
      }
    );
    try {
      await this.loadOrgCount({
        orgId: selectedOrgId,
        treeType: ttype,
        orgType: torgType,
      });
      await this.getOrgUserList();
    } catch (e) {
      return;
    }

    if (isTreeDisabled) {
      this.setState({
        isInitLoading: false,
      });
      return;
    }

    let result;
    if (selectedOrgId === oid) {
      result = (
        await getOrgTree(
          Object.assign(
            {
              org_id: selectedOrgId,
              tree_type: ttype,
              only_active: 1,
            },
            p
          )
        )
      ).data;
    } else {
      result = (
        await locateOrgTree(
          Object.assign(
            {
              root_org_id: oid,
              org_id: selectedOrgId,
              tree_type: ttype,
              only_active: 1,
            },
            p
          )
        )
      ).data;
    }

    if (isInitLoading) {
      this.setState({
        isInitLoading: false,
      });
    }
    if (result.code != 0) {
      this.setState({
        statisData: {
          organizeTotal: 0,
          basicOrgCount: 0,
          manageOrgCount: 0,
          orgUserTotal: 0,
          partyMemberCount: 0,
          manageUserCount: 0,
        },
        treeProps: Object.assign(treeProps, {
          keys: [],
        }),
      });

      return message.error(result.message);
    }

    let data = result.data;
    let initValue = [];

    if (data && data[0]) {
      initValue.push(data[0].org_id + "");
    }

    const initDepartmentValue = treeProps.keys;
    if (selectedOrgId !== oid) {
      initValue.push(selectedOrgId + "");
    }
    if (initDepartmentValue && initDepartmentValue.length) {
      initValue = Array.from(new Set(initValue.concat(initDepartmentValue)));
    }

    let loadedValue = treeProps.loadedKeys;

    const renderData = (val) => {
      if (val && val.children) {
        loadedValue.push(val.org_id + "");
        val.children.forEach(renderData);
        return;
      }
      loadedValue.push(val.org_id + "");
    };

    data.forEach(renderData);

    const getOrgId = [selectedOrgId + ""];
    loadedValue = Array.from(new Set(loadedValue.concat(getOrgId)));
    this.setState(
      {
        treeProps: Object.assign(treeProps, {
          keys: initValue || [],
          loadedKeys: loadedValue || [],
          dataSource: data,
          autoExpandParent: true,
        }),
      },
      target
        ? () => {
            const currentKey = this.state.currentKey;
            const filterVal = initDepartmentValue.filter((val) => {
              return currentKey != val;
            });
            const initVal = Array.from(new Set(getOrgId.concat(filterVal)));

            this.setState({
              treeProps: Object.assign(treeProps, {
                currentKey: getOrgId[0],
                keys: initVal,
              }),
            });

            target();
          }
        : null
    );
  }

  async loadOrgCount(payload = {}) {
    const { orgId, treeType, orgType } = payload;
    const p = {};
    if (treeType == 2) {
      p["org_type"] = orgType;
    }

    const { data: result } = await getStatisticsNum({ org_id: orgId });
    if (result.code != 0 && !result.data) {
      this.setState({
        isInitLoading: false,
        statisData: {
          vigor_org_num: 0,
          vigor_user_num: 0,
        },
      });
      return message.error(result.message);
    }
    this.setState({
      isInitLoading: false,
      statisData: {
        ...result.data,
      },
    });
  }

  async getUserTagList(val) {
    console.log("🚀 ~ file: index.js ~ line 828 ~ val", val);
    const { labelManageProps } = this.state;
    const { orgIds } = labelManageProps;

    const params = {
      org_ids: orgIds,
    };
    this.setState({
      labelManageProps: Object.assign(labelManageProps, {
        isLoading: true,
      }),
    });

    if (val) {
      params.tag_name = val;
    }

    const result = (await getUserTagList(params)).data;

    this.setState({
      labelManageProps: Object.assign(labelManageProps, {
        isLoading: false,
        isSearchLoading: false,
      }),
    });

    if (result.code != 0) {
      return message.error(result.message);
    }
    this.setState({
      labelManageProps: Object.assign(labelManageProps, {
        dataSource: result.data,
      }),
    });
  }

  async updateUserLabelList(e) {
    const { tableProps, labelManageProps, selectedOrgId } = this.state;
    const { userIds, orgIds } = labelManageProps;

    this.setState({
      labelManageProps: Object.assign(labelManageProps, {
        isSubmitLoading: true,
      }),
    });

    const params = {
      org_ids: orgIds,
      users: userIds,
      tags: e.keys,
    };

    let result;
    if (e.isDelete) {
      result = (await deleteUserTag(params)).data;
    } else {
      result = (await insertUserTag(params)).data;
    }

    this.setState({
      labelManageProps: Object.assign(labelManageProps, {
        isSubmitLoading: false,
      }),
    });
    if (result.code != 0) {
      return message.error(result.message);
    }

    this.setState(
      {
        labelManageProps: Object.assign(labelManageProps, {
          isVisible: false,
          isLoading: false,
          isSubmitLoading: false,
          isSearchLoading: false,
          selectedRowKeys: [],
          selectedData: [],
          orgIds: [],
          userIds: [],
        }),
      },
      () => {
        this.getOrgUserList({
          page: tableProps.pageNum,
        });
      }
    );
  }

  _scrollPos(orgId) {
    const el = document.getElementById(orgId);
    const pE = document.getElementById("member-manager-tree-container");
    if (!el || !pE) {
      return;
    }
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      delete this.scrollTimeout;
    }

    this.scrollTimeout = setTimeout(() => {
      pE.scrollTop = el.offsetTop - pE.offsetTop - 10;
      delete this.scrollToOrgId;
    }, 300);
  }
  render() {
    const { userInfo } = this.props;
    const {
      searchValue,
      isInitLoading,
      selectedOrgId,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      autoCompleteProps,
      treeProps,
      tableProps,
      queryProps,
      searchSiderProps: ssp,
      memberSiderProps: msp,
      labelManageProps: lmp,
      statisData,
      searchHeaderForm,
      tableHeadConfig,
      isRootId,
      isShizhi,
      drawerFormList,
    } = this.state;

    const leftSiderProps = {
      _this: this,
      isInitLoading,
      orgTreeTabIndex,
      orgTreeTabList,
      orgTreeTabSelectData,
      onTabChange: (key) => {
        // tab切换
        this.getTreeList({
          orgId: userInfo.oid,
          orgTreeTabIndex: key,
          isOpt: true,
        });
      },
      autoCompleteProps: {
        dataSource: autoCompleteProps.dataSource,
        onSearch: (e) => {
          this.getAutoComplete(e);
        },
        onSelect: (e) => {
          if (e.orgId == selectedOrgId) {
            return;
          }
          e.target = () => {
            this.scrollToOrgId = e.orgId;
            this._scrollPos(e.orgId);
          };

          if (treeProps.loadedKeys.includes(e.orgId + "")) {
            this.setState(
              {
                selectedOrgId: e.orgId,
                isInitLoading: true,
                treeProps: Object.assign(treeProps, {
                  keys: Array.from(
                    new Set(treeProps.keys.concat([e.orgId + ""]))
                  ),
                  currentKey: e.orgId,
                  autoExpandParent: true,
                }),
              },
              async () => {
                e.target();
                this.resetAllFields();
                const ttype = orgTreeTabSelectData.tree_type;
                const torgType = orgTreeTabSelectData.org_type;

                await this.loadOrgCount({
                  orgId: e.orgId,
                  treeType: ttype,
                  orgType: torgType,
                });
                await this.getOrgUserList();

                this.setState({
                  isInitLoading: false,
                });
              }
            );
          } else {
            this.setState(
              {
                isInitLoading: true,
                selectedOrgId: e.orgId,
              },
              () => {
                this.loadOrgTree({ target: e.target });
              }
            );
          }
        },
      },
      treeProps: {
        dataSource: treeProps.dataSource,
        keys: treeProps.keys,
        loadedKeys: treeProps.loadedKeys,
        currentKey: Number(treeProps.currentKey),
        autoExpandParent: treeProps.autoExpandParent,
        onExpand: (e) => {
          const { treeProps } = this.state;
          this.setState({
            treeProps: Object.assign(treeProps, {
              keys: e,
              autoExpandParent: false,
            }),
          });
        },
        onSelect: (e) => {
          if (selectedOrgId == e.id) return;
          this.setState(
            {
              isInitLoading: true,
              selectedOrgInfo: e,
              selectedOrgId: e.id,
              treeProps: Object.assign(treeProps, {
                currentKey: e.id,
              }),
            },
            () => {
              this.loadOrgTree({
                isTreeDisabled: true,
              });
            }
          );
        },
        loadTreeData: (treeNode, f) => {
          return new Promise(async (resolve) => {
            if (
              (treeNode.props.children && treeNode.props.children.length > 0) ||
              !treeNode.props.dataRef.child_org_num
            ) {
              resolve();
              return;
            }
            const p = {};
            const { orgTreeTabSelectData, treeProps } = this.state;

            p["org_type"] = orgTreeTabSelectData.org_type;
            const result = (
              await getOrgTree(
                Object.assign(
                  {},
                  {
                    org_id: treeNode.props.dataRef.org_id,
                    tree_type: orgTreeTabSelectData.tree_type,
                    load_root: 0,
                    only_active: 1,
                  },
                  p
                )
              )
            ).data;

            if (result.code !== 0) {
              return message.error(result.message);
            }

            if (!result.data.length) {
              treeNode.props.dataRef.child_org_num = 0;
            } else {
              treeNode.props.dataRef.children = result.data.map((item) => {
                treeProps.loadedKeys.push(item.org_id + "");
                return { ...item, isLeaf: item.child_org_num === 0 };
              });
            }

            const payload = {
              treeProps: Object.assign(treeProps, {
                loadedKeys: Array.from(
                  new Set(
                    treeProps.loadedKeys.concat([
                      treeNode.props.dataRef.org_id + "",
                    ])
                  )
                ),
                dataSource: treeProps.dataSource,
              }),
            };
            this.setState(payload);

            resolve();
          });
        },
      },
    };

    const mainProps = {
      _this: this,
      searchHeaderForm,
      tableHeadConfig,
      statisData,
      isRootId,
      isShizhi,
      orgTreeTabSelectData: this.state.orgTreeTabSelectData,
      tableProps: {
        downloadParams: { ...searchValue, org_id: selectedOrgId },
        dataSource: tableProps.dataSource,
        isLoading: tableProps.isLoading,
        pageSize: tableProps.pageSize,
        pageNum: tableProps.pageNum,
        pageTotal: tableProps.pageTotal,
        rowSelection: {
          selectedRowKeys: tableProps.selectedRowKeys,
          selectedData: tableProps.selectedData,
          onSelect: (record, selected) => {
            const selectedRowKeys = tableProps.selectedRowKeys;
            const selectedData = tableProps.selectedData;

            if (selected) {
              selectedRowKeys.push(record.key);
              selectedData.push(record);
            } else {
              const index = selectedRowKeys.findIndex((value) => {
                return value == record.key;
              });
              selectedRowKeys.splice(index, 1);
              selectedData.splice(index, 1);
            }

            this.setState({
              tableProps: Object.assign(tableProps, {
                selectedRowKeys,
                selectedData,
              }),
            });
          },
        },
        onPageChange: (num) => {
          this.getOrgUserList({
            page: num,
          });
        },
        onDeleteMember: (user_vigor_org_id) => {
          delOrgUser({
            user_vigor_org_id,
          }).then((res) => {
            const { status, code } = res.data;
            if (code === 0 && status === 200) {
              message.success("操作成功！");
              this.getOrgUserList();
            }
          });
        },
      },
      // 搜索相关配置
      searchProps: {
        formFields: searchHeaderForm,
        isLoading: queryProps.isLoading,
        onSubmit: (values) => {
          this.setState(
            {
              searchValue: values,
            },
            () => {
              this.getOrgUserList();
            }
          );
        },
        // 搜索重置
        onReset: () => {
          if (this.searchDynamicFormRef) {
            this.searchDynamicFormRef.resetFields();
          }
          if (this.advancedSearchDynamicForm) {
            this.advancedSearchDynamicForm.resetFields();
          }
          this.setState(
            {
              searchValue: {},
            },
            () => {
              this.getOrgUserList();
            }
          );
        },
        getSearchFormRef: (ref) => {
          this.searchDynamicFormRef = ref;
        },
        /* onSearchAll: (form) => {
          this.setState({
            queryProps: Object.assign(queryProps, {
              params: {},
            }),
            searchSiderProps: Object.assign(ssp, {
              isSearch: false,
            }),
          });
          this.resetFormFields();
          form.resetFields();

          this.resetFormFields(2);
          // if (ssp.form) {
          //     ssp.form.resetFields();
          // }
          if (this.searchForm) {
            this.searchForm.resetFields();
          }

          this.getOrgUserList();
        },
        openSearch: () => {}, */
      },
    };
    return (
      <div className="manage-main-wrap">
        <IndexView leftSiderProps={leftSiderProps} mainProps={mainProps} />
      </div>
    );
  }
}

const mapStateToProps = ({ organizeData, userInfo }) => ({
  organizeData,
  userInfo,
});
export default connect(mapStateToProps)(MemberManage);
