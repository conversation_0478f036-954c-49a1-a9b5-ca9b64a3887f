import "./index.less"; // 假设样式文件

const DataInitialization = ({ history }) => {
  const items = [
    // { title: '组织架构', icon: 'icon-organization' },
    // { title: '干部任免审批表', icon: 'icon-approval' },
    { title: "干部名册", icon: "icon-roster", path: "/import-page?type=10" },
    {
      title: "干部历年年度考核",
      icon: "icon-evaluation",
      path: "/import-page?type=2",
    },
    { title: "职务信息表", icon: "icon-info", path: "/import-page?type=11" },
    {
      title: "干部社会关系",
      icon: "icon-relationship",
      path: "/import-page?type=20",
    },
    { title: "分管领域", icon: "icon-area", path: "/import-page?type=12" },
    { title: "班子回访", icon: "icon-bz", path: "/import-page?type=17" },
    { title: "年度述职", icon: "icon-nd", path: "/import-page?type=18" },
    { title: "征求意见", icon: "icon-zq", path: "/import-page?type=19" },
  ];

  return (
    <div className="data-initialization">
      <div className="grid-container">
        {items.map((item, index) => (
          <div
            key={index}
            className={`grid-item ${item.path ? "pointer" : ""}`}
            onClick={() => {
              if (item.path) {
                history.push(item.path);
              }
            }}
          >
            <div className="title">{item.title}</div>
            <div className={`icon ${item.icon}`}></div>
          </div>
        ))}
      </div>
      <div className="instructions">
        <div className="instructions-title">操作提示：</div>
        <div className="desc-list">
          <div>
            1.导入顺序：组织架构--&gt;干部任免审批表--&gt;干部名册，导入完成后再导入其他。
          </div>
          <div>
            2.每个导入文件请先下载模板，确认预设模板，如预设模板需调整，需要联系技术团队处理后再进行操作。
          </div>
          <div>
            3.导入的文件必须按照预设模板整理好数据，再上传导入，否则会导致操作失败。{" "}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataInitialization;
