/**
 * 
 * 会议管理
 * 
 */
const { http } = require('client/tool/axios');
import { mtmHost, mtHost } from "./config";

// 获取会议列表
export const queryMeetingList = data => {
  return http.get(`${mtmHost}/meeting/list`, { ...data, is_h5: 0 });
}

// 会议类型查询(下机组织查询)
export const queryMeetingType = data => {
  return http.get(`${mtmHost}/meeting/type/list`, data);
}

// 取消会议
export const cancelMeeting = data => {
  return http.delete(`${mtmHost}/meeting/cancel/${data}`);
}
// 撤回会议
export const revokeMeeting = data => {
  return http.delete(`${mtmHost}/meeting/revoke/${data}`);
}

// 会议历史记录(流程记录)
export const meetingHistoryLogging = data => {
  return http.get(`${mtmHost}/meeting/hs/${data}`);
}

// 发送会议通知
export const sendMeetingInform = data => {
  return http.post(`${mtmHost}/meeting/notice`, data);
}

// 删除会议
export const delMeeting = data => {
  return http.delete(`${mtmHost}/meeting/del/${data}`);
}

// 修改会议
export const updateMeeting = data => {
  return http.post(`${mtmHost}/meeting/update`, data);
}

// 组织查询，用于发起活动时选择任务来源
export const getMeetingOrgListAll = data => {
  return http.get(`${mtmHost}/meeting/org/list-all`, data);
}

// 查询所属组织生活
export const getPlanListAll = data => {
  const { tag = 1 } = data;
  delete data.tag;
  return http.get(`${mtmHost}/plan/list-all/${tag}`, data);
}

// 查询类别列表
export const getCategoryListAll = data => {
  const { tag = 1 } = data;
  delete data.tag;
  return http.get(`${mtmHost}/category/list-all/${tag}`, data);
}

// 查询类型列表
export const getTypeListAll = data => {
  const { tag = 1 } = data;
  delete data.tag;
  return http.get(`${mtmHost}/type/list-all/${tag}`, data);
}

// 查询已有任务列表(任务完成情况)
export const getTopicTaskListAll = data => {
  const { tag = 1 } = data;
  delete data.tag;
  return http.get(`${mtmHost}/topic-task/list-all/${tag}`, data);
}