.right-box {
    padding: 23px 32px;
    height: 100%;
    flex: 1;
    overflow: auto;
    background: #ffffff;
    .condition-box {
        min-height: calc(100% - 150px - 12px);
        .base-info{
            .base-title{
                background: #e7f5fd;
                padding-left: 14px;
                display: flex;
                align-items: center;
                width: 100%;
                height: 32px;
                border-radius: 6px 6px 0px 0px;
                font-size: 14px;
                line-height: 62px;
                font-family: Source <PERSON>, Source Han <PERSON>;
                font-weight: bold;
                color: rgba(0, 0, 0, 0.9);
            }
        }
        .title {
            padding-left: 14px;
            display: flex;
            align-items: center;
            width: 100%;
            height: 32px;
            background: #fff;
            border-radius: 6px 6px 0px 0px;
            font-size: 14px;
            line-height: 62px;
            font-family: Source <PERSON>, Source Han Sans CN;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.9);
        }

        .poc-input,
        :deep(.poc-input) {
            // width: 351px;
            height: 34px;
            font-size: 14px;
            background: #f7f8fa !important;
            input {
                background: transparent;
            }
        }
        :deep(.ant-input-number-input) {
            height: 34px;
            font-size: 14px;
            background: #f7f8fa;
        }
        .ant-picker {
            height: 34px;
            background: #f7f8fa;
            border: none;
        }
        .date-picker-width {
            .ant-picker {
                width: 168px;
            }
        }
      
    }
    .text-style {
        margin: 0px 12px;
        white-space: nowrap;
        font-size: 14px;
    }
    .form-common-style {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        justify-content: space-between;
        .ant-input-number {
            width: 138px;
        }
    }

    .no-wrap-box {
        display: flex;
        align-items: flex-end;
    }
    .cadre-index {
        .ant-select {
            width: 190px;
        }
        .text-style-percent {
            margin-left: 5px;
            font-size: 14px;
        }
        .line {
            margin: 0 10px;
        }
        .ant-input-number {
            width: 70px;
        }
    }
    .button-box {
        box-sizing: content-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 40px;
        background: #ffffff;
        border-top: 12px solid #f5f5f5;
        gap: 45px;
        button {
            width: 95px;
            height: 36px;
            font-size: 14px;
        }
    }
    .date-box {
        width: 100%;
        display: flex;
        align-items: center;
        white-space: nowrap;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
        .label-text {
            margin-right: 10px;
            font-size: 16px;
        }
    }
    .line {
        display: inline-block;
        flex-shrink: 0;
        width: 5px;
        height: 1px;
        background: rgba(0, 0, 0, 0.85);
        margin: 0 3px;
    }
}
.flex-avg {
    display: flex;
    & > div {
        flex: 1 0 0%;
    }
}
.border-bottom {
    border-bottom: 1px dashed #ececec;
}
.custom-select {
    width: 180px;
    :deep(.ant-select-selector) {
        width: 100%;
        height: 34px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 34px;
        background: #f7f8fa;
    }
    :deep(.ant-select-selection-placeholder) {
        line-height: 34px;
    }
    :deep(.ant-select-selection-item) {
        line-height: 34px;
    }
}