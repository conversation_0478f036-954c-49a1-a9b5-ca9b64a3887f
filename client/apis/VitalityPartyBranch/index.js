import { ucHost } from "../config";
import { http, filterParamsValue } from "client/tool/axios";

// 401020201005-报到党员审核列表 http://wiki.aidangqun.com/project/8?p=963
const findAuditByWhere = (params) => {
  return http.get(`${ucHost}/vigor/find-audit-by-where`, params);
};
// 401020201007-党员报到确认/退回 http://wiki.aidangqun.com/project/8?p=967
const confirmOrCancel = (params) => {
  return http.post(`${ucHost}/vigor/confirm-or-cancel`, params);
};
// 401020201014-统计报道人数 http://wiki.aidangqun.com/project/8?p=1297
const getStatisticsNum = (params) => {
  return http.get(`${ucHost}/vigor/statistics/num`, params);
};
// 401020201010-报到党员查询 http://wiki.aidangqun.com/project/8?p=970
const getOrgUserList = (params) => {
  return http.post(`${ucHost}/vigor/org/user/list`, filterParamsValue(params));
};
// 401020201011-删除报到党员 http://wiki.aidangqun.com/project/8?p=971
const delOrgUser = (params) => {
  return http.get(`${ucHost}/vigor/user/del`, filterParamsValue(params));
};
export { findAuditByWhere, confirmOrCancel, getStatisticsNum, getOrgUserList, delOrgUser };
