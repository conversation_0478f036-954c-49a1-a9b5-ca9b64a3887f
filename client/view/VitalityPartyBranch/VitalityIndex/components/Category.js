import { useState, useEffect, useMemo } from "react";
import ReactEcharts from "echarts-for-react";
import ItemWrap from "./ItemWrap";
import * as echarts from "echarts";
import { message } from "antd";
import { getHistogramTop, getVitalityIndexRanking, getBasicInformation, getSystemMean, getstreetRanking } from "apis/Vitality-index";
import moment from "moment";

/**
 *
 * @param {*} type  0县委  1街道  2村社  3支部
 * @returns
 */
const Category = ({ type, cardType, org_id }) => {
  const [option, setOption] = useState({});
  const [data, setData] = useState([]);
  const [staTime, setStaTime] = useState([]);//起始时间
  //支部
  const [VitalityIndex, setVitalityIndex] = useState([]);//活力指数
  const [VitalityIndexTime, setVitalityIndexTime] = useState([]);//活力指数时间
  //街道与村社
  const [VitalityIndexStreet, setVitalityIndexStreet] = useState([]);//活力指数
  const [VitalityIndexHighest, setVitalityIndexHighest] = useState([]);//最高
  const [VitalityIndexLowest, setVitalityIndexLowest] = useState([]);//最低
  const [VitalityIndexTimes, setVitalityIndexTimes] = useState([]);//折线图时间
  const [itemData, setItemData] = useState([]);

  useEffect(() => {
    //0县委 3支部 其余的为街道和村社
    type == 0 ?
      getTreeListData()
      : ""
    type == 3 ?
      getTreeListData2()
      : ""
    type !== 3 && type !== 0 ?
      getTreeListData3()
      : ''
    setTimeout(() => {
    type == 3 ?
      getTreeListData2()
      : ""
    type !== 3 && type !== 0 ?
      getTreeListData3()
      : ''
    }, 700);

  }, [org_id, type]);

  // useEffect(() => {
  //   type !== 0 ?
  //     setData([])
  //     : ''
  // }, [org_id, type]);

  const getTreeListData = async () => {
    console.log('出现在1', type);
    const params = {
      org_id: org_id,
      time: moment(moment()).format("YYYYMM"),
    }
    const { data: res } = await getHistogramTop(params);

    if (res.code !== 0) return message.error(res.message);
    setData(res.data)
    setItemData([
      {
        backgroundColor: "#867EEC",
        type: 1,
        text: "系统均值",
        num: `${res.data.sta_result.均值[res.data.sta_result.均值.length - 1]}` == undefined ? "0.00" : `${res.data.sta_result.均值[res.data.sta_result.均值.length - 1]}`,
      },
      {
        backgroundColor: "#E9F3FF",
        text: "城市社区",
        num: `${res.data.sta_result.城市社区[res.data.sta_result.城市社区.length - 1]}` == undefined ? "0.00" : `${res.data.sta_result.城市社区[res.data.sta_result.城市社区.length - 1]}`,
      },
      {
        backgroundColor: "#E7FAE5",
        text: "涉农社区",
        num: `${res.data.sta_result.涉农社区[res.data.sta_result.涉农社区.length - 1]}` == undefined ? "0.00" : `${res.data.sta_result.涉农社区[res.data.sta_result.涉农社区.length - 1]}`,
      },
      {
        backgroundColor: "#E6F6FA",
        text: "村",
        num: `${res.data.sta_result.村[res.data.sta_result.村.length - 1]}` == undefined ? "0.00" : `${res.data.sta_result.村[res.data.sta_result.村.length - 1]}`,
      }
    ])
    let arr = []
    for (var i = 0; i < res.data.sta_months.length; i++) {
      let NewArr = res.data.sta_months[i];
      arr.push(NewArr);
    }
    setStaTime(arr)
  };
  const getTreeListData2 = async () => {
    console.log('出现在2', type);
    const params = {
      org_id: org_id,
      time: moment(moment()).format("YYYYMM"),
    }
    const params2 = {
      org_id: org_id,
      time: moment(moment()).subtract(1, 'months').format("YYYYMM"),
    }
    const { data: res2 } = await getVitalityIndexRanking(params);
    const { data: res3 } = await getBasicInformation(params2);
    const { data: res4 } = await getSystemMean(params2);
    if (res2.code !== 0) return message.error(res2.message);
    if (res3.code !== 0) return message.error(res3.message);
    if (res4.code !== 0) return message.error(res4.message);
    console.log('非顶级', res2, res3, res4);
    setVitalityIndexTime(res2.data.sta_months)
    setVitalityIndex(res2.data.sta_result.活力指数)

    setItemData([
      {
        backgroundColor: "#867EEC",
        type: 1,
        text: '活力指数',
        num: res3.data.score == undefined ? '0.00' : `${res3.data.score}`,
      },
      {
        backgroundColor: "#E9F3FF",
        text: '系统排名',
        num: res3.data.rank == undefined ? '0.00' : `${res3.data.rank}`,
      },
      {
        backgroundColor: "#E7FAE5",
        text: '序列排名',
        num: res3.data.seq_rank == undefined ? '0.00' : `${res3.data.seq_rank}`,
      },
      {
        backgroundColor: "#E6F6FA",
        text: "系统均值",
        num: res4.data !== undefined ? `${res4.data.org_avg}` : '-',
        type: 2,
        max: res4.data !== undefined ? `${res4.data.org_max}` : '-',
        min: res4.data !== undefined ? `${res4.data.org_min}` : '-',
      }
    ])
    let arr = []
    for (var i = 0; i < res2.data.sta_months.length; i++) {
      let NewArr = res2.data.sta_months[i] * 0.01;
      arr.push(NewArr);
    }
    setStaTime(arr)
  };
  const getTreeListData3 = async () => {
    console.log('出现在3', type);
    const params = {
      org_id: org_id,
      time: moment(moment()).format("YYYYMM"),
    }
    const { data: res } = await getstreetRanking(params);
    if (res.code !== 0) return message.error(res.message);

    setVitalityIndexStreet(res.data.sta_result.均值)
    setVitalityIndexHighest(res.data.sta_result.最高)
    setVitalityIndexLowest(res.data.sta_result.最低)
    setVitalityIndexTimes(res.data.sta_months)
    setItemData([
      {
        backgroundColor: "#867EEC",
        type: 1,
        text: '活力指数',
        num: `${res.data.sta_result.均值[res.data.sta_result.均值.length - 1]}` || '-',
      },
      {
        backgroundColor: "#E9F3FF",
        text: '最高',
        num: `${res.data.sta_result.最高[res.data.sta_result.最高.length - 1]}` || '-',
      },
      {
        backgroundColor: "#E7FAE5",
        text: '最低',
        num: `${res.data.sta_result.最低[res.data.sta_result.最低.length - 1]}` || '-',
      },
      {
        backgroundColor: "#E6F6FA",
        text: "系统均值",
        num: `${res.data.sta_result.系统平均[res.data.sta_result.系统平均.length - 1]}`,
        // type: 2,
        // max: res4.org_max,
        // min: res4.org_min,
      }
    ])
    let arr = []
    for (var i = 0; i < res.data.sta_months.length; i++) {
      let NewArr = res.data.sta_months[i] * 0.01;
      arr.push(NewArr);
    }
    setStaTime(arr)
  };
  useEffect(() => {
    let option = {
      grid: {
        left: 30,
        right: 30,
        top: 40,
        bottom: "1%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
        formatter: (params) => {
          return params[0].name + '<br/>' + (params[0] !== undefined ? params[0].seriesName : '') + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[0] !== undefined ? params[0].value : ''}<right/>` + (params[1] !== undefined ? '<br/>' : '') + (params[1] !== undefined ? params[1].seriesName : '') + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[1] !== undefined ? params[1].value : ''}<right/>` + (params[2] !== undefined ? '<br/>' : '') + (params[2] !== undefined ? params[2].seriesName : '') + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[2] !== undefined ? params[2].value : ''}<right/>` + (params[3] !== undefined ? '<br/>' : '') + (params[3] !== undefined ? params[3].seriesName : '') + ' ' + `<right style={{font-weight:'bold',float:"right",display:'inline-block'}}>${params[3] !== undefined ? params[3].value : ''}<right/>`
        },
      },
    };
    switch (type) {
      case 0:
        option = {
          ...option,
          color: ["#1D7EFF", "#E9F3FF", "#E7FAE5", "#E6F6FA"],
          legend: {
            data: ["系统均值", "城市社区", "涉农社区", "村"],
          },
          xAxis: [
            {
              type: "category",
              axisTick: {
                alignWithLabel: true,
              },
              // prettier-ignore
              boundaryGap: true,
              data: staTime,
            },
          ],
          yAxis: [
            {
              type: "value",
              alignTicks: true,
            },
            {
              type: "value",
              position: "right",
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#1D7EFF",
                },
              },
            },
          ],
          series: [
            {
              color: '#867EEC',
              name: "系统均值",
              type: "line",
              alignTicks: true,
              yAxisIndex: 1,
              data: data.sta_result !== undefined ? data.sta_result.均值 : '',
            },
            {
              color: '#6297FF',
              name: "城市社区",
              type: "bar",
              data: data.sta_result !== undefined ? data.sta_result.城市社区 : '',
            },
            {
              color: '#2BC048',
              name: "涉农社区",
              type: "bar",
              data: data.sta_result !== undefined ? data.sta_result.涉农社区 : '',
            },
            {
              color: '#60CDF4',
              name: "村",
              type: "bar",
              data: data.sta_result !== undefined ? data.sta_result.村 : '',
            },
          ],
        };
        break;
      case 4:
      case 5:
        option = {
          ...option,
          legend: {
            data: ["均值", "最高", "最低"],
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: VitalityIndexTimes,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              color: '#867EEC',
              name: "均值",
              type: "line",
              // stack: "Total",
              data: VitalityIndexStreet,
            },
            {
              color: '#60CDF4',
              name: "最高",
              type: "line",
              // stack: "Total",
              data: VitalityIndexHighest,
            },
            {
              color: '#5DCD73',
              name: "最低",
              type: "line",
              // stack: "Total",
              data: VitalityIndexLowest,
            },
            {
              name: "",
              type: "line",
              // stack: "Total",
              data: [0],
            },
          ],
        };
        break;
      case 6:
      case 3:
        option = {
          ...option,
          xAxis: {
            type: "category",
            boundaryGap: true,
            data: VitalityIndexTime,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              data: VitalityIndex,
              type: "line",
              smooth: true,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#867EEC",
                  },
                  {
                    offset: 1,
                    color: "#fff",
                  },
                ]),
              },
            },
            {
              name: "",
              type: "line",
              // stack: "Total",
              data: [0],
            },
            {
              name: "",
              type: "line",
              // stack: "Total",
              data: [0],
            },
            {
              name: "",
              type: "line",
              // stack: "Total",
              data: [0],
            },
          ],
        };
        break;
      default:
        break;
    }
    setOption(option);
  }, [org_id, data, staTime, VitalityIndex, VitalityIndexTime]);

  return (
    <div className="category-wrap">
      <header className="category-header">
        {itemData.map((item, key) => (
          <ItemWrap {...item} key={key} />
        ))}
      </header>
      <ReactEcharts option={option} />
    </div>
  );
};
export default Category;
