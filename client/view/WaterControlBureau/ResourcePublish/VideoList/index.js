import React from "react";
import { Icon } from "antd";
import "./index.less";

const VideoList = ({ onChange, dataSource }) => {
  return (
    <div className="VideoList">
      {/* {JSON.stringify(dataSource)} */}
      {dataSource.length && dataSource.length > 0
        ? dataSource.map((item, index) => {
            return (
              <div className="VideoList-item">
                <div className="VideoList-item-file">
                  <Icon type="video-camera" />
                  <div className="VideoList-item-file-option">
                    <Icon type="delete" onClick={() => {
                      let videos = dataSource
                      videos.splice(index, 1)
                      onChange && onChange(videos)
                    }} />
                  </div>
                </div>
                <div className="VideoList-item-name" title={item.file_name || ""}>{item.file_name || ""}</div>
              </div>
            );
          })
        : ""}
    </div>
  );
};
export default VideoList;
