/**
 * 互动规则
 * <AUTHOR>
 * 2018/4/23
 */
import React from 'react'
import PropTypes from 'prop-types'
import {
    Form,
    Radio,
    Select
} from 'antd'

import adaptiveLayout from '../gridLayout'

const Option = Select.Option

class ActivityRules extends React.Component {
    render() {
        const {
            form,
            emuns,
            preview,
            formFields,
            activityFormState,
            isContribute = false
        } = this.props
        const { joinModel, accountRestrictions, ipRestrictions, deviceRestrictions } = emuns
        const { getFieldDecorator, getFieldValue } = form
        const commodityDisabled = getFieldValue(formFields.rewardTypeCommodity)
        return <div>
            <Form.Item {...adaptiveLayout.formWrapper} label='参与模式' className='custom-form-item-col3'>
                {getFieldDecorator(formFields.joinModel, {
                    initialValue: activityFormState.joinModel
                })(
                    <Radio.Group disabled={commodityDisabled || preview} >
                        {
                            (joinModel && Array.isArray(joinModel)) &&
                            joinModel.map((item, index) => {
                                return (
                                    <Radio
                                        key={index}
                                        disabled={isContribute && item.value === 0}
                                        value={item.value}>
                                        {item.label}
                                    </Radio>
                                )
                            })
                        }
                    </Radio.Group>
                    // <Radio.Group options={joinModel} disabled={commodityDisabled || preview} />
                )}
            </Form.Item>
            <Form.Item {...adaptiveLayout.formWrapper} label='账户限制'>
                {getFieldDecorator(formFields.accountRestrictions, {
                    initialValue: activityFormState.accountRestrictions
                })(
                    <Select disabled={preview} style={{ width: 200 }}>
                        {
                            accountRestrictions.map((item, index) => (
                                <Option key={index} value={item.value}>{item.label}</Option>
                            ))
                        }
                    </Select>
                )}
                <p style={{ color: '#999', fontSize: 12, lineHeight: 1.6 }}>限制单个账户参加互动的次数。</p>
            </Form.Item>
            <Form.Item {...adaptiveLayout.formWrapper} label='IP限制'>
                {getFieldDecorator(formFields.ipRestrictions, {
                    initialValue: activityFormState.ipRestrictions
                })(
                    <Select disabled={preview} style={{ width: 200 }}>
                        {
                            ipRestrictions.map((item, index) => (
                                <Option key={index} value={item.value}>{item.label}</Option>
                            ))
                        }
                    </Select>
                )}
                <p style={{ color: '#999', fontSize: 12, lineHeight: 1.6 }}>相同内网（wifi），或地理位置很接近的相同运营商的手机网络，IP有可能相同。IP限制是最基本和有效防刷票防攻击技术，如非特殊情况请务必开启！</p>
            </Form.Item>
            <Form.Item {...adaptiveLayout.formWrapper} label='设备限制'>
                {getFieldDecorator(formFields.deviceRestrictions, {
                    initialValue: activityFormState.deviceRestrictions
                })(
                    <Select disabled={preview} style={{ width: 200 }}>
                        {
                            deviceRestrictions.map((item, index) => (
                                <Option key={index} value={item.value}>{item.label}</Option>
                            ))
                        }
                    </Select>
                )}
                <p style={{ color: '#999', fontSize: 12, lineHeight: 1.6 }}>限制单台移动设备参加互动的次数。</p>
            </Form.Item>
        </div>
    }
}

ActivityRules.propTypes = {
    //预览模式
    preview: PropTypes.bool,

    //antd表单对象
    form: PropTypes.object.isRequired,

    //表单字段
    formFields: PropTypes.shape({
        //参与模式
        joinModel: PropTypes.string.isRequired,
        //账户限制
        accountRestrictions: PropTypes.string.isRequired,
        //IP限制
        ipRestrictions: PropTypes.string.isRequired,
        //设备限制
        deviceRestrictions: PropTypes.string.isRequired
    }).isRequired,

    //表单默认数据
    emuns: PropTypes.shape({
        joinModel: PropTypes.arrayOf(PropTypes.object).isRequired,
        accountRestrictions: PropTypes.arrayOf(PropTypes.object).isRequired,
        ipRestrictions: PropTypes.arrayOf(PropTypes.object).isRequired,
        deviceRestrictions: PropTypes.arrayOf(PropTypes.object).isRequired
    }).isRequired,

    //表单model
    activityFormState: PropTypes.shape({
        //参与模式
        joinModel: PropTypes.number.isRequired,
        //账户限制
        accountRestrictions: PropTypes.number.isRequired,
        //IP限制
        ipRestrictions: PropTypes.number.isRequired,
        //设备限制
        deviceRestrictions: PropTypes.number.isRequired
    }).isRequired
}

ActivityRules.defaultProps = {
    preview: false
}

export default ActivityRules
