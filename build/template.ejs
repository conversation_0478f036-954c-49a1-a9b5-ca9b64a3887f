<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- Ai党群 or 智慧党建服务平台 -->
    <title>加载中...</title>
    <meta charSet="utf-8" />
    <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <link rel="shortcut icon" href="/plugin/favicon.png" type="image/x-icon" />
    <link
      rel="preload"
      href="/plugin/js/font_1405225_2rhnjzz1bnw.js"
      as="script"
    />
    <link rel="preload" href="<%= selfIconPath %>" as="script" />
    <script
      src="/plugin/js/wpkReporter.js"
      crossorigin="true"
    ></script>
    <script>
      try {
        const config = {
          bid: "hyhzhdj_kmift2ec_orm4loju",
          signkey: "1234567890abcdef",
          gateway: "https://zd-wpkgate-emas.bigdatacq.com:32383",
        };
        const wpk = new wpkReporter(config);
        wpk.installAll();
        window._wpk = wpk;
      } catch (err) {
        console.error("WpkReporter init fail", err);
      }
    </script>
  </head>

  <body>
    <div id="app" style="height: 100%"></div>
    <script>
      (function () {
        window.onerror = function (message, url, line, column, stackError) {
          const body = {
            message: message || "",
            url: url || "",
            line: line || "",
            column: column || "",
            stackError: stackError || "",
          };
          body.stackError = stackError.stack;
        };
        function modernBrowserJudgment(userAgent) {
          const lowerUserAgent = userAgent.toLowerCase(userAgent);
          // chrome
          if (lowerUserAgent.indexOf("chrome") > -1) {
            return true;
          }
          // Safari
          if (lowerUserAgent.indexOf("safari") > -1) {
            return true;
          }
          // ie11 及以上
          if (
            // lowerUserAgent.indexOf("trident/7.0") > -1 ||edge
            lowerUserAgent.indexOf("edge") > -1 ||
            lowerUserAgent.indexOf("rv:11.0") > -1
          ) {
            return true;
          }
          // firefox
          if (lowerUserAgent.indexOf("firefox") > -1) {
            return true;
          }
          return false;
        }
        if (!modernBrowserJudgment(navigator.userAgent)) {
          location.href = "/browserSetup.html" + location.search;
        }
      })();
    </script>
  </body>
</html>
