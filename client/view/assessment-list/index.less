.assessment-list {
  .header {
    height: 60px;
    background: #F7F8F9;
    font-size: 18px;
    padding: 0 28px;
    line-height: 60px;
  }
  .content {
    padding: 0 28px;
  }
  .add {
    padding: 20px 0;
    border-bottom: 1px solid #eee;
  }
  .filter {
    background: #F3F5F8;
    padding: 20px 30px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.assessment-form{
  margin-top: 40px;
  .ant-form-item {
    margin-bottom: 0;
  }
  .ant-checkbox-wrapper {
    &:first-child {
      padding-right: 90px;
    }
  }
}
.button-list {
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
  .ant-btn {
    margin: 0 10px;
  }
}
