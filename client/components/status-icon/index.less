.status-icon {
  display: inline-flex;
  box-sizing: content-box; // border: 1px solid red;
  height: 24px;
  position: relative;
  padding: 0;
  margin: 0 24px;
  border-radius: 5px;
  color: #fff;
  font-size: 14px;
  background-color: #00B7EE;
  min-width: 64px;
  align-items: center;
  justify-content: center;
  &.default,
  &.info {
    background-color: #00B7EE;
    &.right {
      .r {
        border-left-color: #00B7EE;
      }
    }
    &.left {
      .r {
        border-right-color: #00B7EE;
      }
    }
  }
  &.success {
    background-color: #22AC38;
    &.right {
      .r {
        border-left-color: #22AC38;
      }
    }
    &.left {
      .r {
        border-right-color: #22AC38;
      }
    }
  }
  &.error {
    background-color: #F6473B;
    &.right {
      .r {
        border-left-color: #F6473B;
      }
    }
    &.left {
      .r {
        border-right-color: #F6473B;
      }
    }
  }
  &.warning {
    background-color: #F19149;
    &.right {
      .r {
        border-left-color: #F19149;
      }
    }
    &.left {
      .r {
        border-right-color: #F19149;
      }
    }
  }
  &.disable {
    background-color: #CCC;
    &.right {
      .r {
        border-left-color: #CCC;
      }
    }
    &.left {
      .r {
        border-right-color: #CCC;
      }
    }
  }
  &.right {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding-left: 5px;
    .r {
      content: "";
      display: inline-block;
      position: absolute;
      right: -24px;
      top: 0;
      bottom: 0;
      width: 0;
      height: 0;
      border-width: 12;
      border: 12px solid transparent;
    }
  }
  &.left {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding-right: 5px;
    .r {
      content: "";
      display: inline-block;
      position: absolute;
      left: -24px;
      top: 0;
      bottom: 0;
      width: 0;
      height: 0;
      border-width: 12;
      border: 12px solid transparent;
    }
  }
}
