import React, {
    Component
} from 'react';
import {
    Form,
    message
} from 'antd';
import './index.less';
import {
    connect
} from 'dva/index';
import {
    uploadHost
} from 'apis/config';
//引入模块头部组件
import SearchHeader from 'components/search-header';
//引入主视图区域模块
import IndexView from './index-view';
import { headers } from "tool/axios";


const fileUrl = `${uploadHost}/file/upload`;

class BatchAddition extends Component {
    constructor(props) {
        super(props);
        this.state = {
            //批量导入执行到步骤：0.操作前；1.数据解析完毕；2.数据解析完毕(收集错误报告和成功信息)
            addStep: 0
        }
    }

    // async componentDidMount() {
    //     const {
    //         dispatch
    //     } = this.props;
    //     await dispatch({
    //         type: 'departmentManage/getDep'
    //     });
    // }
    componentWillMount() {
        // console.log(this.props);
        const {
            dispatch,
            history
        } = this.props;
        if (history) {
            const { location } = history;
            if (location) {
                const { state } = location;
                if (state) {
                    dispatch({
                        type: 'batchAddition/save',
                        payload: {
                            targetOid: state.orgId || null,
                            targetName: state.orgName || ""
                        }
                    });
                }
            }
        }

        // const oid = typeof window !== 'undefined' ? window.sessionStorage.getItem('_oid') : '';
        // dispatch({
        //     type: 'batchAddition/fetchOrganisitionTree',
        //     payload: {
        //         oid
        //     }
        // });
        dispatch({
            type: 'batchAddition/fetchDownloadTemplateUrl',
            payload: {}
        });
        dispatch({
            type: 'batchAddition/save',
            payload: {
                tableList: [],
                failedList: [],
                current: 1,
                currentHandlePage: 1,
                total: 0,
                dataCount: 0,
                succeedCount: 0,
                failedCount: 0
            }
        });
    }

    render() {
        // console.log(this.props.departmentManage);
        const {
            departmentManage,
            batchAddition,
            history,
            dispatch
        } = this.props,
            {
                dataSource,
                initDepartmentValue
            } = departmentManage || {},
            {
                current,
                pageSize,
                total,

                currentHandlePage,

                dataCount,
                succeedCount,
                failedCount,
                templateUrl,
                title,
                orgTree,
                targetOid,
                targetName,
                operKey,
                tableList,
                tableLoading,
                loadingVisible,
                loadingTitle,
                batchEnable
            } = batchAddition || {};
        // console.log(batchEnable, batchAddition);
        //顶端状态条组件属性
        const searchHeaderProps = {
            title,
            onBack() {
                history.goBack();
            }
        };
        const _this = this;
        //主体视图组件属性
        const indexViewProps = {
            current,
            pageSize,
            total,
            targetName,

            pageChangeHandler(page, pagesize) {
                // console.log(`切换到第${page}页，每页${pagesize}条`);
                dispatch({
                    type: 'batchAddition/save',
                    payload: {
                        current: page,
                    }
                });
                dispatch({
                    type: 'batchAddition/fetchImportDataByPage',
                    payload: {
                        operKey,
                        page: page,
                        pagesize
                    }
                });
            },

            addStep: this.state.addStep,
            batchEnable,

            orgChangeHandler(value, label, extra) {
                // console.log('选择组织树', value, label, extra);
                // 如果在上传之后，错误列表位置，重新选择组织，
                // 则将所有错误结果清空，退回等待上传文件状态
                if (_this.state.addStep === 2) {
                    _this.setState({
                        addStep: 0
                    });
                    dispatch({
                        type: 'batchAddition/save',
                        payload: {
                            tableList: [],
                            failedList: [],
                            total: 0,
                            dataCount: 0,
                            operKey: null,
                            succeedCount: 0,
                            failedCount: 0,
                        }
                    });
                }
                dispatch({
                    type: 'batchAddition/save',
                    payload: {
                        targetOid: value || null
                    }
                });
            },
            dataCount,
            succeedCount,
            failedCount,
            templateUrl,
            orgTree,
            tableList,
            tableLoading,
            loadingVisible,
            loadingTitle,
            startImport() {
                //如果没有选择导入的目标，则不允许导入
                if (!targetOid) {
                    message.error('请选择导入目标组织');
                    return false;
                }
                if (!operKey) {
                    message.error('请先上传人员数据');
                    return false;
                }
                //开始导入
                dispatch({
                    type: 'batchAddition/startImport',
                    payload: {
                        operKey,
                        targetOid
                    }
                }).then(
                    (response) => {
                        // console.log('处理结果', response);
                        const { succeedCount, failedCount } = response;
                        message.info(`导入结果：成功${succeedCount}条，失败${failedCount}条。`);
                        dispatch({
                            type: 'batchAddition/save',
                            payload: {
                                loadingTitle: '',
                                loadingVisible: false,
                                //表单选择
                                current: 1,
                                total: 0
                            }
                        });
                        _this.setState({
                            addStep: 2
                        });
                    }
                ).catch(
                    (error) => {
                        // _this.setState({
                        //     addStep: 2
                        // });
                        console.log(error.message);
                        message.error('导入失败，请重新导入');
                        //重新发起请求
                        // dispatch({
                        //     type: 'batchAddition/retransmission',
                        //     payload: {
                        //         operKey,
                        //         targetOid
                        //     }
                        // });
                    }
                );
            },
            onDown() {
                console.log('下载导入用模板');
            },
            tableProps: {
                onEdit(data) {
                    dispatch({
                        type: 'batchAddition/toggleTableStatus',
                        data,
                        isEdit: true
                    });
                },
                onSave(record, index) {
                    const batchName = document.getElementById('batchName').value;
                    const batchPhone = document.getElementById('batchPhone').value;
                    const batchCard = document.getElementById('batchCard').value;

                    if (!batchName.trim()) {
                        return message.error('请输入姓名');
                    }
                    if (!batchPhone.trim()) {
                        return message.error('请输入电话');
                    }
                    if (!batchCard.trim()) {
                        return message.error('请输入证件号');
                    }

                    dispatch({
                        type: 'batchAddition/editTable',
                        payload: {
                            name: batchName,
                            phone: batchPhone,
                            certNumber: batchCard,
                            orgId: targetOid,
                            certType: record.cert_type,
                            politicalType: record.political_type,
                            jobGrade: record.job_grade,
                            position: record.position,
                            communist: record.communist,
                            youthLeague: record.youth_league,
                            unionMember: record.union_member,
                            womenLeague: record.women_league
                        }
                    }).then(
                        (payload) => {
                            message.success('操作成功');
                            tableList.splice(index, 1);
                            // let item = tableList[index];
                            // const { name, phone, certNumber } = payload;
                            // item.cert_number = certNumber;
                            // item.name = name;
                            // item.phone = phone;
                            // item.isEdit = false;
                            // item.state_txt = '导入成功';
                            let success = succeedCount + 1,
                                fail = failedCount - 1;
                            dispatch({
                                type: 'batchAddition/save',
                                payload: {
                                    tableList,
                                    succeedCount: success,
                                    failedCount: fail
                                }
                            });
                        }
                    );
                },
                onCancel(data) {
                    dispatch({
                        type: 'batchAddition/toggleTableStatus',
                        data
                    });
                }
            },
            uploadProps: {
                name: 'upfile',
                action: fileUrl,
                data: {
                    upType: 'file'
                },
                accept: '.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel',
                headers: headers(),
                multiple: false,
                showUploadList: false,
                onChange(info) {
                    if (info.file.status !== 'uploading') {
                        console.log(info.file, info.fileList);
                    }
                    if (info.file.status === 'done') {
                        if (info.file.response && info.file.response.success) {
                            if (!info.file.response.data.success) {
                                dispatch({
                                    type: 'batchAddition/updateState',
                                    payload: {
                                        tableLoading: false
                                    }
                                });
                                return message.error(info.file.response.message);
                            }
                            dispatch({
                                type: 'batchAddition/updateState',
                                payload: {
                                    tableList: info.file.response.data,
                                    tableLoading: false
                                }
                            })
                            message.success('上传成功')
                        } else {
                            message.error('上传失败')
                        }

                    } else if (info.file.status === 'error') {
                        message.error('上传失败')
                        dispatch({
                            type: 'batchAddition/updateState',
                            payload: {
                                tableLoading: false
                            }
                        })
                    }
                },
                beforeUpload: (file) => {
                    _this.setState({
                        addStep: 0
                    });
                    dispatch({
                        type: 'batchAddition/save',
                        payload: {
                            tableList: [],
                            failedList: [],
                            total: 0,
                            dataCount: 0,
                            operKey: null,
                            succeedCount: 0,
                            failedCount: 0,
                            current: 1,
                            currentHandlePage: 1
                        }
                    });
                    dispatch({
                        type: 'batchAddition/save',
                        payload: {
                            loadingVisible: true,
                            loadingTitle: '数据解析中，请耐心等待完成，请不要刷新页面...'
                        }
                    })
                },
                onStart() {
                    // console.log('开始解析');
                },
                onSuccess(response) {
                    // console.log('成功', response);
                    // console.log(_this.state.addStep);
                    // 从解析前状态变更为解析后，导入前状态
                    const { code, data } = response;
                    if (code !== 0) {
                        message.error(response.message);
                    }
                    //上传成功后，用上传成功的文件去请求oper_code
                    const file = data && data[0];
                    const fileId = file.name;
                    // console.log(fileId);

                    // console.log('解析完成', targetOid, fileId);

                    //获取解析出来的上传组织人员数据
                    dispatch({
                        type: 'batchAddition/importInit',
                        payload: {
                            fileId
                        }
                    }).then(
                        (result_key) => {
                            dispatch({
                                type: 'batchAddition/pollingOperKey',
                                payload: {
                                    result_key
                                }
                            }).then(
                                (data) => {
                                    // console.log(data);
                                    const { status } = data;
                                    //还在处理中
                                    if (status === 0) {
                                        // console.log('开始轮询');
                                        let timer = setInterval(() => {
                                            dispatch({
                                                type: 'batchAddition/pollingOperKey',
                                                payload: {
                                                    result_key
                                                }
                                            }).then(
                                                (data) => {
                                                    // console.log(data);
                                                    // console.log('轮询到解析完成，读取数据');
                                                    const { oper_key, status } = data;
                                                    if (status === 1) {
                                                        clearInterval(timer);
                                                        timer = null;
                                                        // console.log('轮询结束');
                                                        //根据operKey 去获取分页数据
                                                        dispatch({
                                                            type: 'batchAddition/fetchImportDataByPage',
                                                            payload: {
                                                                operKey: oper_key,
                                                                page: current,
                                                                pagesize: pageSize
                                                            }
                                                        }).then(
                                                            () => {
                                                                _this.setState({
                                                                    addStep: 1
                                                                });
                                                            }
                                                        );
                                                        if (!batchEnable) {
                                                            dispatch({
                                                                type: 'batchAddition/save',
                                                                payload: {
                                                                    batchEnable: true
                                                                }
                                                            })
                                                        }
                                                    }
                                                }
                                            ).catch(
                                                () => {
                                                    clearInterval(timer);
                                                    timer = null;
                                                    dispatch({
                                                        type: 'batchAddition/save',
                                                        payload: {
                                                            loadingVisible: false,
                                                            loadingTitle: ''
                                                        }
                                                    })
                                                }
                                            )
                                        }, 3000);
                                    } else if (status === 1) {
                                        console.log(data);
                                        //当请求立即被解析完成的时候
                                        const { oper_key } = data;
                                        if (!oper_key) {
                                            message.error('获取解析数据失败，请重试');
                                            return;
                                        }
                                        dispatch({
                                            type: 'batchAddition/fetchImportDataByPage',
                                            payload: {
                                                operKey: oper_key || '',
                                                page: current,
                                                pagesize: pageSize
                                            }
                                        }).then(
                                            () => {
                                                _this.setState({
                                                    addStep: 1
                                                });
                                            }
                                        );
                                        if (!batchEnable) {
                                            dispatch({
                                                type: 'batchAddition/save',
                                                payload: {
                                                    batchEnable: true
                                                }
                                            })
                                        }
                                    }
                                }
                            ).catch().finally(
                                // () => {
                                //     dispatch({
                                //         type: 'batchAddition/save',
                                //         payload: {
                                //             loadingVisible: false,
                                //             loadingTitle: ''
                                //         }
                                //     })
                                // }
                            )
                        }
                    );
                },
                onError() {
                    message.error('解析错误');
                    dispatch({
                        type: 'batchAddition/save',
                        payload: {
                            loadingVisible: false,
                            loadingTitle: ''
                        }
                    })
                    // console.log('解析错误');
                }
            }
        };
        return (
            <div className={'batch-addition'}>
                <SearchHeader {...searchHeaderProps} />
                {/* {title}*/}
                <IndexView {...indexViewProps} />
            </div>
        )
    }
}

const mapStateToProps = ({
    batchAddition,
    departmentManage
}) => ({
    batchAddition,
    departmentManage
});

export default connect(mapStateToProps)(Form.create()(BatchAddition));