import React, { PureComponent } from "react";
import OrgSelector from "./OrgSelector";
import UserSelector from "./UserSelector";
import CheckPhone from "./CheckPhone";
import FoldCheckBox from "./FoldCheckBox/index";
import FileUploadList from "client/components/file-upload-list";
import { guid } from "client/tool/util";
import moment from "moment";
import ProvincesSelect from "./ProvincesSelect";
import AreaSelect from "./AreaSelect";

const { Input, DatePicker, Select, Checkbox, Radio, Cascader, InputNumber } =
  Ant;
const { TextArea, Search } = Input;
const { Option } = Select;

class FieldItem extends PureComponent {
  constructor(props) {
    super(props);
  }
  renderCheckBox() {}
  render() {
    const {
      type,
      itemKey,
      methods,
      getRef,
      form,
      label,
      query_type,
      isShow,
      ...attr
    } = this.props;
    attr.ref = getRef;
    const { options, value } = attr;
    const uuid = guid();
    switch (Number(type)) {
      case 1: {
        // 文本输入框
        const val = typeof value === "string" ? value : undefined;
        if (typeof attr.onBlur === "string") delete attr.onBlur;
        return <Input placeholder={`请输入${label}`} {...attr} value={val} />;
      }
      case 2: {
        // 下拉框（选择器）
        const { multiple, ...otherAttr } = attr;
        const val =
          value instanceof Array || typeof value === "string"
            ? value
            : undefined;
        return (
          <Select
            showSearch
            allowClear
            className={`dynamic-form-select-${itemKey}-${uuid}`}
            mode={multiple ? "multiple" : undefined}
            placeholder={`请选择${label}`}
            {...otherAttr}
            value={val}
            filterOption={(input, option) =>
              option.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
            style={{ minWith: "170px", width: "100%" }}
            getPopupContainer={(trigger) =>
              (trigger = document.querySelector(
                `.dynamic-form-select-${itemKey}-${uuid}`
              ))
            }
          >
            {options &&
              options.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
          </Select>
        );
      }
      case 3: {
        // 单选
        const val = typeof value === "string" ? value : undefined;
        return (
          <Radio.Group
            placeholder={`请选择${label}`}
            options={options}
            {...attr}
            value={val}
          />
        );
      }
      case 4: {
        // 复选框
        const val = value instanceof Array ? value : undefined;
        attr.value = val;
        return (
          <FoldCheckBox
            placeholder={`请选择${label}`}
            options={options}
            attr={attr}
          />
        );
      }
      case 5: {
        // 级联选择器
        const val = value instanceof Array ? value : undefined;
        return (
          <Cascader
            className={`dynamic-form-cascader-${itemKey}-${uuid}`}
            placeholder={`请选择${label}`}
            options={options}
            {...attr}
            value={val}
            getPopupContainer={(trigger) =>
              (trigger = document.querySelector(
                `.dynamic-form-cascader-${itemKey}-${uuid}`
              ))
            }
          />
        );
      }
      case 6: {
        // 时间选择器
        const { mode, value, ...otherAttr } = attr;
        const val = value instanceof moment ? value : undefined;
        return (
          <DatePicker
            className={`dynamic-form-data-pick-${itemKey}-${uuid}`}
            showTime={mode === "datetime"}
            placeholder={`请选择${label}`}
            style={{ width: "100%", minWith: "170px" }}
            {...otherAttr}
            value={val}
            getCalendarContainer={(trigger) =>
              (trigger = document.querySelector(
                `.dynamic-form-data-pick-${itemKey}-${uuid}`
              ))
            }
          />
        );
      }
      case 7: {
        // 用户选择器
        const val = typeof value === "string" ? value : undefined;
        return (
          <UserSelector
            placeholder={`请选择${label}`}
            label={label}
            {...attr}
            value={val}
          />
        );
      }
      case 8: {
        // 组织选择器
        const val = typeof value === "string" ? value : undefined;
        return (
          <OrgSelector
            itemKey={itemKey}
            placeholder={`请选择${label}`}
            {...attr}
            value={val}
          />
        );
      }
      case 9: {
        // 文本域（多行文本）
        const val = typeof value === "string" ? value : undefined;
        return (
          <TextArea
            placeholder={`请输入${label}`}
            autoSize={{ minRows: 2, maxRows: 6 }}
            {...attr}
            value={val}
          />
        );
      }
      case 10: {
        // 手机号验证组件
        const val = typeof value === "string" ? value : undefined;
        const myattr = { ...attr, value: val };
        return (
          <CheckPhone
            placeholder={`请输入${label}`}
            methods={methods}
            form={form}
            attr={myattr}
          />
        );
      }
      case 12: {
        // 数字输入框
        if (typeof attr.onBlur === "string") delete attr.onBlur;
        const val = typeof value === "number" ? value : undefined;
        return (
          <InputNumber
            placeholder={`请输入${label}`}
            style={{ width: "100%", borderRadius: 0 }}
            {...attr}
            value={val}
          />
        );
      }
      case 13: {
        // 省市选择器
        const val = value instanceof Array ? value : undefined;
        const myattr = { ...attr, value: val };
        return (
          <ProvincesSelect attr={myattr} itemKey={itemKey} label={label} />
        );
      }
      case 14: {
        // 图片文件上传
        const val = value instanceof Array ? value : [];
        return <FileUploadList value={val} {...attr} />;
      }
      case 15: {
        // 省市区选择器
        const val = value instanceof Array ? value : undefined;
        const myattr = { ...attr, value: val };
        return (
          <AreaSelect attr={myattr} itemKey={itemKey} label={label} />
        );
      }
      default:
        return <Input {...attr} />;
    }
  }
}

export default FieldItem;
