const { http } = require('client/tool/axios');
import {host, orgHost, ucHost} from './config';


//104002003801-通用获取职务/职级数据字典
export const commonJobUrl = (data = {}) => {
    return http.get(`${host}/leader/option/${data.type}`);
};

//104002003802-模糊查询人员
export const vagueSearchMemberUrl = (data = {}) => {
    return http.get(`${host}/leader/search/${data.orgId}/${data.searchType}/${data.userName}`);
};

//104002003803-创建/修改 领导班子成员
export const addLeaderGroupUrl = (data = {}) => {
    return http.post(`${host}/leader/add`, data);
};

//104002003804-删除领导班子成员
export const deleteLeaderGroupUrl = (data = {}) => {
    return http.get(`${host}/leader/delete/${data.leader_id}`);
};

//104002003806-查询领导班子成员
export const searchLeaderGroupUrl = (data = {}) => {
    return http.get(`${host}/leader/find/${data.org_id}`);
};

/**
 * 30002020301001-活动录入需要 根据组织ID查询联系领导班子
 * @param {*} org_id 组织Id
 */
export const getLeaderSelectList = (org_id) => {
    return http.get(`${host}/leader/find-leader-contact-meeting`, { org_id })
}

//1040020037010-获取组织类别
export const getLeaderGroupTypeUrl = (data = {}) => {
    return http.post(`${host}/leader/find-org-type`, data);
};
// 1040020037010-获取组织与关联单位信息
export const getCompanyInfoUrl = (data = {}) => {
    return http.get(`${host}/leader/org-type/${data.org_id}`);
};

//模糊查询组织
export const findOrgByName = (data) => {
    return http.get(`${orgHost}/org/find-org-by-name`, data);
};

//组织列表信息查询
export const getTreeList = (data) => {
    return http.get(`${ucHost}/org/tree/list`, data);
};

// 分步获取组织树
export const getOrgTree = (data) => {
    return http.get(`${ucHost}/org/tree`, data);
};

// 查询获取整棵组织树
export const locateOrgTree = (data) => {
    return http.get(`${ucHost}/org/locate`, data);
};

// 查询关联组织单位
export const loadOrgUnit = (data) => {
    return http.get(`${ucHost}/leader/org-list`, data);
};

// 查询领导班子成员
export const findOrgMember = (data) => {
    return http.get(`${ucHost}/leader/find-now`, data);
};