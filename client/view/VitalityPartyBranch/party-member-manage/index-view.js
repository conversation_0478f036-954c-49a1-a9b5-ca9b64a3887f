import React from "react";
import LeftSider from "client/components/left-sider";
import MainContent from "./main-content";
import { Layout, Spin } from "antd";

const { Content } = Layout;
export default ({ leftSiderProps, mainProps }) => {
  const { orgTreeTabList, isInitLoading } = leftSiderProps;
  const isData = orgTreeTabList.length && orgTreeTabList[0].tree_id;
  let contentHTML = <center>暂无信息</center>;

  if (isData) {
    contentHTML = (
      <Layout className="member-manage-main">
        <LeftSider {...leftSiderProps} />
        <MainContent {...mainProps} />
      </Layout>
    );
  }

  return (
    <Spin size="large" spinning={isInitLoading}>
      {contentHTML}
    </Spin>
  );
};
