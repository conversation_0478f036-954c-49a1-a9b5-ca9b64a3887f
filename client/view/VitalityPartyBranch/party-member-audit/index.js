import React, { useState, useEffect } from "react";
import Header from "components/search-header";
import moment from "moment";
import { findAuditByWhere, confirmOrCancel } from "apis/VitalityPartyBranch";
import "./index.less";

const {
  Form,
  Table,
  Select,
  Input,
  Button,
  DatePicker,
  message,
  Popconfirm,
  Modal,
} = Ant;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

const PartyMemberAudit = ({ form }) => {
  const { getFieldDecorator, validateFields, resetFields } = form;

  const [selectKey, setselectKey] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [remarkModal, setRemarkModal] = useState({});
  const [remark, setRemark] = useState();

  useEffect(() => {
    findAuditByWhereData();
  }, [page]);

  const findAuditByWhereData = () => {
    validateFields((err, values) => {
      const params = {
        ...values,
      };
      if (values.time) {
        params.start_time = moment(values.time[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        params.end_time = moment(values.time[1]).format("YYYY-MM-DD HH:mm:ss");
      }
      params.page = page;
      delete params.time;
      setLoading(true);
      setDataSource([]);
      if (params.audit_status === -1) {
        delete params.audit_status;
      }
      findAuditByWhere(params).then(({ data: res }) => {
        setLoading(false);
        if (res.code !== 0) {
          message.error(res.message);
        } else {
          setDataSource(() => res.data || []);
          setTotal(res.total);
          setPage(params.page || 1);
        }
      });
    });
  };

  const confirmOrCancelData = (
    vigor_audit_id,
    audit_status,
    isBatch = false
  ) => {
    if (audit_status === 2) {
      setRemarkModal({ vigor_audit_id, audit_status, isBatch });
    } else {
      setBtnLoading(true)
      confirmOrCancel({ vigor_audit_id, audit_status }).then(
        ({ data: res }) => {
          if (res.code !== 0) {
            message.error(res.message);
          } else {
            message.success("确认成功");
            setselectKey([]);
            findAuditByWhereData();
          }
        }
      ).finally(() => setBtnLoading(false));
    }
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    if (page !== 1) {
      setPage(1);
    } else {
      findAuditByWhereData();
    }
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "user_name",
    },
    {
      title: "手机号",
      align: "center",
      dataIndex: "phone_secret",
    },
    {
      title: "所在组织",
      dataIndex: "affiliated_org_name",
      width: "20%",
    },
    {
      title: "报到组织",
      dataIndex: "vigor_org_name",
      width: "20%",
    },
    {
      title: "报到状态",
      dataIndex: "audit_status",
      render: (status) => {
        return status === 1 ? "审核通过" : status === 2 ? "已退回" : "待审核";
      },
    },
    {
      title: "报到时间",
      align: "center",
      dataIndex: "check_time",
      render: (time) => moment(time).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "审核备注",
      dataIndex: "remark",
      render: (text) => text || "无",
    },
    {
      title: "操作",
      align: "center",
      render(text, record, index) {
        return (
          <div>
            <Popconfirm
              disabled={record.audit_status !== 0}
              placement="topRight"
              title="确认该党员向组织报到吗？"
              onConfirm={() => confirmOrCancelData(record.vigor_audit_id, 1)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" disabled={record.audit_status !== 0}>
                确认
              </Button>
            </Popconfirm>
            <Button
              type="link"
              disabled={record.audit_status !== 0}
              className="return-btn"
              onClick={() => confirmOrCancelData(record.vigor_audit_id, 2)}
            >
              退回
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="PartyMemberAudit">
      <Header
        title="党员报到审核"
        renderRight={() => (
          <div>
            <Button
              loading={btnLoading}
              className={`batch-btn ${selectKey.length === 0 ? "" : "active"}`}
              disabled={selectKey.length === 0}
              onClick={() => confirmOrCancelData(selectKey.join(","), 1)}
            >
              批量确认
            </Button>
            <Button
              className={`batch-btn ${selectKey.length === 0 ? "" : "active"}`}
              disabled={selectKey.length === 0}
              onClick={() => confirmOrCancelData(selectKey.join(","), 2, true)}
            >
              批量退回
            </Button>
          </div>
        )}
      />
      <main className="PartyMemberAudit-main">
        <Form layout="inline" onSubmit={onSubmit}>
          <Form.Item label="姓名">
            {getFieldDecorator("user_name")(
              <Input placeholder="请输入" maxLength={8} />
            )}
          </Form.Item>
          <Form.Item label="审核状态">
            {getFieldDecorator("audit_status", {
              initialValue: -1,
            })(
              <Select style={{ width: 150 }}>
                <Option title="全部" value={-1}>
                  全部
                </Option>
                <Option title="待审核" value={0}>
                  待审核
                </Option>
                <Option title="审核通过" value={1}>
                  审核通过
                </Option>
                <Option title="已退回" value={2}>
                  已退回
                </Option>
              </Select>
            )}
          </Form.Item>
          <Form.Item label="报到时间">
            {getFieldDecorator("time")(
              <RangePicker
                format="YYYY-MM-DD HH:mm:ss"
                showTime={{
                  hideDisabledOptions: true,
                }}
              />
            )}
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" className="query-btn">
              查询
            </Button>
            <Button
              type="default"
              onClick={(e) => {
                resetFields();
                onSubmit(e);
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
        <Table
          columns={columns}
          rowKey="vigor_audit_id"
          bordered
          dataSource={dataSource}
          loading={loading}
          rowSelection={{
            selectedRowKeys: selectKey,
            getCheckboxProps: (record) => ({
              disabled: record.audit_status !== 0, // 待审核的可进行批量操作
            }),
            onChange: (selectedRowKeys) => {
              setselectKey(selectedRowKeys);
            },
          }}
          pagination={{
            showQuickJumper: true,
            pageSize: 10,
            total: total,
            current: page,
            showTotal: () => `共${total}条数据`,
            onChange: (page) => setPage(page),
          }}
        />
      </main>
      <Modal
        className="return-modal"
        title="审核提示"
        visible={!!remarkModal.vigor_audit_id}
        onOk={() => {
          if (!remark) {
            message.error("请输入退回原因！");
          } else {
            const { audit_status, vigor_audit_id } = remarkModal;
            confirmOrCancel({ audit_status, vigor_audit_id, remark }).then(
              ({ data: res }) => {
                if (res.code !== 0) {
                  message.error("退回失败");
                } else {
                  message.success("退回成功");
                  setselectKey([]);
                  findAuditByWhereData();
                }
              }
            );
            setRemarkModal({});
            setRemark();
          }
        }}
        onCancel={() => setRemarkModal({})}
      >
        <div className="return-reason-wrap">
          <div className="return-reason-title">
            是否退回{remarkModal.isBatch ? "所选择党员？" : "该党员报到？"}
          </div>
          <div className="return-reason">
            <span className="return-field">
              <i className="required-icon">*</i>
              <span>退回原因：</span>
            </span>
            <TextArea
              rows={3}
              value={remark}
              onChange={({ target: { value } }) => setRemark(value)}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Form.create()(PartyMemberAudit);
