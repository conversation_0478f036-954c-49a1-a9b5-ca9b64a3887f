import { fileHost, csHost } from "./config";
import { http } from "client/tool/axios";


// 40202020201-分类管理/下拉列表 http://wiki.aidangqun.com/project/3?p=1319
const findCategoryList = (params) => {
  return http.get(`${fileHost}/resource/find-category-list`, params);
};
// 40202020202-添加分类 http://wiki.aidangqun.com/project/3?p=1324
const createCategory = (params) => {
  return http.post(`${fileHost}/resource/create-category`, params);
};
// 40202020203-编辑分类 http://wiki.aidangqun.com/project/3?p=1325
const updateCategory = (params) => {
  return http.post(`${fileHost}/resource/update-category`, params);
};
// 40202020205-删除分类绑定 http://wiki.aidangqun.com/project/3?p=1328
const deleteCategoryBind = (params) => {
  return http.get(`${fileHost}/resource/delete-category-bind`, params);
};
// 40202020204-删除分类 http://wiki.aidangqun.com/project/3?p=1331
const deleteCategory = (params) => {
  return http.get(`${fileHost}/resource/delete-category`, params);
};
// 40202020207-新增资源 http://wiki.aidangqun.com/project/3?p=1332
const addResource = (params) => {
  return http.post(`${fileHost}/resource/add`, params);
};
// 40202020206-资源管理列表查询 http://wiki.aidangqun.com/project/3?p=1333
const findByWhereResource = (params) => {
  return http.get(`${fileHost}/resource/find-by-where`, params);
};
// 40202020208-分类列表上移下移 http://wiki.aidangqun.com/project/3?p=1341
const upDownCategory = (params) => {
  return http.post(`${fileHost}/resource/up-down-category`, params);
};
// 40202020209-编辑资源 http://wiki.aidangqun.com/project/3?p=1346
const editResource = (params) => {
  return http.post(`${fileHost}/resource/edit`, params);
};
// 40202020210-根据ID查询资源详情 http://wiki.aidangqun.com/project/3?p=1347
const findById = (params) => {
  return http.get(`${fileHost}/resource/find-by-id`, params);
};
// 40202020211-批量删除资源 http://wiki.aidangqun.com/project/3?p=1348
const removeResource = (params) => {
  return http.post(`${fileHost}/resource/remove`, params);
};
// 40202020212-公开/取消公开 http://wiki.aidangqun.com/project/3?p=1349
const publishResource = (params) => {
  return http.post(`${fileHost}/resource/publish`, params);
};
// 40202020213-查询所有来源 http://wiki.aidangqun.com/project/3?p=1350
const findAllSources = (params) => {
  return http.get(`${fileHost}/resource/find-all-sources`, params);
};
// 40202020214-资源展示列表查询 http://wiki.aidangqun.com/project/3?p=1351
const findByWhere = (params) => {
  return http.get(`${fileHost}/resource/view/find-by-where`, params);
};
// 40202020215-公开/取消公开显示状态 http://wiki.aidangqun.com/project/3?p=1355
const publishShowStatus = (params) => {
  return http.post(`${fileHost}/resource/publish-show-status`, params);
};
// 40002033301-点击事件埋点 http://wiki.aidangqun.com/project/4?p=936
const collectionEvent = (params) => {
  return http.post(`${csHost}/collection`, params);
};

// 4010202100301-获取场地库预约设置 http://wiki.aidangqun.com/project/8?p=1884
const getResourceAppointment = (params) => {
  return http.get(`${fileHost}/resource/appointment/get/${params}`);
};

// 4010202100302-场地库预约设置 http://wiki.aidangqun.com/project/8?p=1890
const setResourceAppointment = (params) => {
  return http.post(`${fileHost}/resource/appointment/set`, params);
};

// 4010202100307-根据场地库ID查询预约情况 http://wiki.aidangqun.com/project/8?p=1890
const getResourceAppointmentAppointment = (
  resourceLibraryId,
  params
) => {
  return http.get(
    `${fileHost}/resource/appointment/appointment/history/${resourceLibraryId}`,
    params
  );
};


export {
  getResourceAppointment,
  setResourceAppointment,
  getResourceAppointmentAppointment,
  findCategoryList,
  createCategory,
  updateCategory,
  deleteCategoryBind,
  deleteCategory,
  addResource,
  findByWhereResource,
  upDownCategory,
  editResource,
  findById,
  removeResource,
  publishResource,
  findAllSources,
  findByWhere,
  publishShowStatus,
  collectionEvent
};
