const { http } = require('client/tool/axios');
import { evalHost,host } from './config';
import axios from 'axios';
// const linshi ='http://*************:19802';

//104001001002-问题查处列表(分页)
export const searchUrl = (data = {}) => {
    return http.get(`${evalHost}/examine/list-page`, data);
}
//104001001003-查看问题查处详情
export const lookDetailUrl = (data = {}) => {
    const id =data.examine_id;
    return http.get(`${evalHost}/examine/detail/${id}`, {});
}
//104001001001-新增问题单
export const addQuestionUrl = (data = {}) => {
    return http.post(`${evalHost}/examine/add`,data);
}
//1040020032-模糊查询组织直接下级（新增查处单）
export const companySearchUrl = (data = {}) => {
    return http.get(`${host}/org/find-org-by-name`,data);
}
//104001000808-分值管理列表
export const tabs1ScoreTableUrl = (data = {}) => {
    return http.get(`${evalHost}/deduct-rule/list`, data);
}