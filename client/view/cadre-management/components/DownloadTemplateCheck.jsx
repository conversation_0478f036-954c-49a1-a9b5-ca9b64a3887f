import { Checkbox, Modal } from "antd";
import { queryUserRosterFieid } from "client/apis/cadre-portrait";
import { useEffect, useState } from "react";

const DownloadTemplateCheck = ({
  visible,
  selectedFields,
  onOk,
  onCancel,
  onSelectChange,
}) => {
  const [localSelectedFields, setLocalSelectedFields] = useState(
    selectedFields || []
  );
  const [downFields, setDownFields] = useState([]); // 添加状态来存储 downFields
  const [isMounted, setIsMounted] = useState(true);

  useEffect(() => {
    return () => {
      setIsMounted(false); // 组件卸载时设置为 false
    };
  }, []);

  const getDownFields = async () => {
    try {
      const { data } = await queryUserRosterFieid();
      if (isMounted) {
        setDownFields(data.data); // 更新 downFields
      }
    } catch (error) {
      console.error("Error fetching downFields:", error);
    }
  };

  // 在 visible 状态变化时调用 getDownFields
  useEffect(() => {
    if (visible) {
      getDownFields();
    }
  }, [visible]);

  const handleCheckChange = (checkedValues) => {
    if (isMounted) {
      setLocalSelectedFields(checkedValues);
      onSelectChange(checkedValues); // 将选择结果传递回父组件
    }
  };

  const extractColumnsData = (fields) => {
    return (
      fields &&
      fields.map((field) => ({
        label: field.name,
        value: field.code,
      }))
    );
  };

  // 根据 is_default_select 初始化选中状态
  useEffect(() => {
    if (downFields.length > 0) {
      const initialSelectedFields = downFields
        .filter((field) => field.is_default_select === 1)
        .map((field) => field.code);
      if (isMounted) {
        setLocalSelectedFields(initialSelectedFields);
        onSelectChange(initialSelectedFields); // 将初始选中状态传递回父组件
      }
    }
  }, [downFields]);

  return (
    <Modal
      title="自定义下载"
      visible={visible}
      onOk={() => {
        if (isMounted) {
          onOk(localSelectedFields);
        }
      }}
      onCancel={onCancel}
    >
      <Checkbox.Group
        style={{ width: "100%" }}
        options={downFields && extractColumnsData(downFields)}
        value={localSelectedFields}
        onChange={handleCheckChange}
      />
    </Modal>
  );
};

export default DownloadTemplateCheck;
