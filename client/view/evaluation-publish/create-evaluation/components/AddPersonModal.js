import SelectInput from "./SelectInput";
import { findByUser } from "client/apis/cadre-portrait";

const { Modal, Form, Select, Input, message } = Ant;

export default Ant.Form.create()(function AddPersonModal(props) {
  const { visible, dataSource, type, onOk, onCancel, form } = props;

  const { getFieldDecorator, setFieldsValue, validateFields } = form;

  const [option, setOption] = React.useState([]);

  const handelOk = () => {
    validateFields((err, values) => {
      if (!err) {
        const group = type === "8" ? (values.user_type === "1" ? 1 : 2) : 2;
        onOk({
          ...values,
          seq: dataSource.length + 1,
          group,
          enable: 1,
        });
      }
    });
  };

  const findByUserInfo = (userName) => {
    findByUser({ user_name: userName }).then((res) => {
      if (res.data.code === 0) {
        setOption(res.data.data.slice(0, 10));
      } else {
        message.error(res.data.message);
      }
    });
  };
  const onSelect = ({ user_name, job, user_id }) => {
    setFieldsValue({
      job,
      user_id,
    });
  };

  return (
    <Modal
      title="添加测评人员"
      visible={visible}
      onOk={handelOk}
      destroyOnClose
      onCancel={onCancel}
    >
      <Form>
        <Form.Item label="姓名">
          {getFieldDecorator("user_name", {
            rules: [{ required: true, message: "请输入姓名" }],
          })(
            <SelectInput
              placeholder="请输入"
              onSearch={findByUserInfo}
              onSelect={onSelect}
              option={option}
            />
          )}
        </Form.Item>
        <Form.Item label="职务">
          {getFieldDecorator("job", {
            rules: [{ required: true, message: "请输入职务" }],
          })(<Input />)}
        </Form.Item>
        <div style={{ display: "none" }}>
          <Form.Item label="user_id">
            {getFieldDecorator("user_id")(<Input />)}
          </Form.Item>
        </div>
        <Form.Item label="类别">
          {getFieldDecorator("user_type", {
            rules: [{ required: true, message: "请选择类别" }],
          })(
            <Select>
              <Select.Option hidden={type !== "8"} value="1">
                县管正职
              </Select.Option>
              <Select.Option hidden={type !== "8"} value="2">
                县管副职
              </Select.Option>
              <Select.Option hidden={type !== "8"} value="3">
                改非干部
              </Select.Option>
              <Select.Option hidden={type !== "8"} value="4">
                新提拔中层干部
              </Select.Option>
              <Select.Option hidden={type !== "9"} value="5">
                中层干部
              </Select.Option>
            </Select>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
});
