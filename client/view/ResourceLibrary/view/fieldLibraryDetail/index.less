.Particulars {
    &-information {
        margin-top: 66px;
        display: flex;
        // justify-content: center;
        margin: 30px 0 45px 195px;
    }

    &-content {
        margin: 30px 0 45px 195px;
        width: 80%;
        text-indent: 2em;
        font-family: PingFang SC-Medium, PingFang SC;
        font-size: 18px;
        color: #333333;
        // line-height: 40px;
    }

    &-information {
        &-Carousel {
            margin-right: 36px;
            width: 583px;
            height: 330px;

            .slick-dots-bottom {
                    button {
                        height: 5px !important;
                    }
                    .slick-active{
                        button{
                            width: 40px;
                        }
                    }

            }

            &-img {
                height: 100%;
            }
        }

        &-content {
            // display: flex;
            // flex-direction: column;
            margin-top: 20px;
            width: 45%;

            .name {
                margin-bottom: 16px;
                font-size: 36px;
                font-family: PingFang SC-Heavy, PingFang SC;
                font-weight: 800;
                color: #333333;
                line-height: 36px;
            }

            .label,
            .technical-post {
                display: flex;
                // width: 400px;
            }

            .label {
                width: 330px;
                color: #666666;
                // justify-content: space-between;
            }

            .technical-post {
                margin-bottom: 16px;
                justify-content: left;
                font-size: 18px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 24px;
            }

            .tag {
                margin-bottom: 16px;
                display: table;
                padding: 0 16px;
                background: #e9e9e9;
                border-radius: 4px;
                margin-right: 10px;
            }
        }
    }

    .lastMsg {
        font-size: 18px;
        color: #999;
        font-weight: 400;
        margin-top: 10px;
        text-indent: 0em;
    }

    .post2 {
        display: flex;
    }
}