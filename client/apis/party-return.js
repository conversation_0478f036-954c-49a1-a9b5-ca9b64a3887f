import { party } from "./config";
import { http } from "tool/axios";
export const returnAccountInsert = (payload)=>{
  const url = `${party}/returnAccount/insert`;
  return http.post(url,payload)
}

export const 	returnAccountUpdate = (payload)=>{;
  const url = `${party}/returnAccount/update`
  return http.post(url,payload)
}
export const returnAccountFindAccount=(office_id)=>{
  const url =`${party}/returnAccount/findAccount?office_id=${office_id}`;
  return http.get(url)
}