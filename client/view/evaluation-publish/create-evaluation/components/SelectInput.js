export default function SelectInput({
  value,
  onSearch,
  onSelect,
  onInput,
  onChange,
  option = [],
  ...props
}) {
  const menu = (
    <Ant.Menu>
      {option.map((item) => {
        return (
          <Ant.Menu.Item
            key={item.key}
            onClick={() => {
              onChange(item.user_name || item.org_name);
              onSelect(item);
            }}
          >
            {item.user_name || item.org_name} {item.job ? `-${item.job}` : ""}
          </Ant.Menu.Item>
        );
      })}
    </Ant.Menu>
  );
  return (
    <Ant.Dropdown overlay={menu}>
      <Ant.Input
        value={value}
        onChange={(e) => {
          const value = e.target.value;
          console.log(value);
          onInput && onInput(value);
          onChange && onChange(value);
          onSearch && onSearch(value);
        }}
        {...props}
      />
    </Ant.Dropdown>
  );
}
