import React, {
    useEffect
} from 'react';
import { useRef } from 'react';
import PropTypes from 'prop-types';
import './index.less';
import html2canvas from 'html2canvas'
const Card = ({
    header = false,
    sizeType = '1',
    title,
    subTitle,
    children,
    rightSlot = '',
    className = ''
}) => {
    const cardRef = useRef(null);

    return (
        <div className={`card-box size-${sizeType} ${className}`} ref={cardRef}>
            {header && (
                <div className="card-header header">
                    <div className="card-bg">
                        <div className="left">
                            <span className="title">{title}</span>
                            {subTitle && <span className="sub_title">{subTitle}</span>}
                        </div>
                        {rightSlot}
                    </div>
                </div>
            )}
            <div className="content">
                {children}
            </div>
        </div>
    );
};

// 可选：添加 PropTypes 验证
Card.propTypes = {
    header: PropTypes.bool,
    sizeType: PropTypes.oneOf(['1', '2', '3', '4', '5', '6']),
    title: PropTypes.string,
    subTitle: PropTypes.string,
    children: PropTypes.node,
    rightSlot: PropTypes.node
};

export default Card;