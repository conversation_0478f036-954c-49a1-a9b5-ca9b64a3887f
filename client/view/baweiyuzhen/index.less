.page-b<PERSON><PERSON><PERSON><PERSON> {
  background-color: #f1f5f8;
  .image-spin{
    display: inline-block;
  }
  .header {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 30px;
    padding-right: 40px;
    background-color: #fff;
    height: 60px;
    box-shadow: 0px 3px 10px 0px rgba(0, 18, 43, 0.06);
    border-radius: 5px 5px 0px 0px;
  }
  .header-left {
    font-size: 17px;
    font-weight: bold;
    color: rgba(0, 0, 1, 1);
  }
  .header-right {
    .ant-btn {
      margin-right: 20px;
      height: 34px;
      font-size: 15px;
      border-radius: 17px;
    }
  }
  .main-box {
    display: flex;
    .side {
      width: 220px;
      height: 100vh;
      background-color: #fff;
      box-shadow: 0px 3px 10px 0px rgba(0, 18, 43, 0.06);
      .ant-affix {
        height: 100vh !important;
        background-color: #fff;
      }
      .ant-anchor-wrapper {
        margin: 0;
        padding: 0;
      }
      .ant-anchor-ink {
        display: none;
      }
      .ant-anchor-link {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48px;
        margin: 0 22px;
        font-size: 17px;
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        text-align: center;
      }
    }
    .main {
      margin-left: 10px;
      .title {
        margin: 28px 0 16px 20px;
        font-weight: bold;
        color: rgba(102, 102, 102, 1);
        font-size: 17px;
        .tips {
          font-size: 14px;
          font-weight: bold;
          color: rgba(153, 153, 153, 1);
        }
      }
      .card + .card {
        margin-top: 20px;
      }
      .card {
        width: 840px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 3px 10px 0px rgba(0, 18, 43, 0.06);
        border-radius: 5px;
        overflow: auto;
        &.img-card {
          display: flex;
          .card-tittle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            background: rgba(247, 249, 251, 1);
            border-radius: 5px 0px 0px 5px;
            font-size: 16px;
            font-weight: bold;
            color: rgba(102, 102, 102, 1);
          }
          .ant-form {
            padding-top: 30px;
            flex-grow: 1;
          }
          // .required label::before {
          //   display: inline-block;
          //   margin-right: 4px;
          //   color: #f5222d;
          //   font-size: 14px;
          //   font-family: SimSun, sans-serif;
          //   line-height: 1;
          //   content: '*';
          // }

          .image {
            &.scale-no-img {
              padding: 10px 0;
            }
            &.scale-img{
              min-height: 30px;
            }
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            background: #f7f8f9;
            background: rgba(255, 255, 255, 1);
            border: 1px dashed rgba(230, 230, 230, 1);
            .view {
              width: 100%;
              height: 100%;
            }
            .text {
              cursor: pointer;
              font-weight: 400;
              color: rgba(200, 200, 200, 1);
              i {
                font-size: 40px;
                color: #ccc;
                vertical-align: middle;
                line-height: 1.4;
              }
              p {
                font-size: 13px;
                margin: 0;
                line-height: 1.4;
              }
            }
          }
          .clipping {
            position: fixed;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            z-index: 99999;
            iframe {
              width: 100%;
              height: 100%;
            }
          }
        }
        &.table-card {
          .is-hide {
            opacity: 0.3;
          }
          .ant-btn {
            padding: 0 6px;
          }
          td {
            padding: 6px 10px;
          }
          .btn-add {
            width: 100%;
            height: 60px;
            font-size: 15px;
            border-style: dashed;
            i {
              font-size: 16px;
              font-weight: bold;
            }
            // background: rgba(255, 255, 255, 1);
            // border: 1px dashed rgba(255, 107, 109, 1);
            // box-shadow: 0px 2px 8px 0px rgba(43, 48, 56, 0.06);
            // border-radius: 0px 0px 4px 4px;
            // font-size: 13px;
            // color: rgba(255, 107, 109, 1);
            // cursor: pointer;
          }
        }
      }
    }
  }
}
