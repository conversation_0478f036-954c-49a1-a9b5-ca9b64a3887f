import { getCountDeductLog, getCountDeductLogDetail } from "apis/assessment-statistical";
import { getViewDetail, getView, getUpdateScoreList } from "apis/examination";
import moment from "moment";

export default {
  namespace: "assessmentOrgDetail",
  state: {
    org: null,
    tableLoading: false,
    // pageLoading: false, // 页面全局, loading
    dataSource: [],

    queryParams: {
      year: moment().year(),
      time: -1,
      deduct_type: -1
    },

    // 查看扣分记录详情
    modalVisible: false,
    deductLogId: null,
    deductLogData: null,
    dataCollectData: null,
    updateScoreData: null,
    modalTitle: null
  },
  effects: {
    *getCountDeductLogDetail({ payload }, { put, call }) {
      const { eval_id, org_id, deduct_type, deduct_log_id, year, time } = payload;
      yield put({
        type: "save",
        payload: {
          tableLoading: true
        }
      });
      let response, collectBody, updateScoreList;
      if (deduct_type !== 4) {
        response = yield getCountDeductLogDetail({ deduct_log_id });
      }
      else {
        response = yield getView({ eval_id, org_id });
        const dataCollect = yield getViewDetail({ eval_id, org_id });
        // console.log(dataCollect);
        const { data: collect } = dataCollect;
        collectBody = collect;
        if(collectBody.code == 0 && collectBody.data.eval_cycle == 2){
          collectBody.data.list = collectBody.data.list.filter((item) => {
            return item.type_id == 6 || item.type_id == 7
          })
        }
        //查询考核修正分值记录
        const res = (yield getUpdateScoreList({ eval_id, org_id })).data;
        updateScoreList = res.data;
        if (res.code !== 0 && collect.code !== 0) {
          yield put({
            type: "save",
            payload: {
              tableLoading: false
            }
          });
          // throw new Error(collect.message);
        }
        yield put({
          type: "save",
          payload: {
            dataCollectData: collect.data || {},
            updateScoreData: updateScoreList || {}
          }
        });
      }
      const { data: body } = response,
        { data, code, message } = body;
        data.eval_id = eval_id;
        data.org_id = org_id
      if (code !== 0) {
        yield put({
          type: "save",
          payload: {
            tableLoading: false
          }
        });
        throw new Error(message);
      }
      // console.log(data);
      yield put({
        type: "save",
        payload: {
          deduct_log_id: data.deduct_log_id,
          deductLogData: data,
          tableLoading: false,
          modalVisible: true
        }
      });
      return {
        deductLogData: data || {},
        dataCollectData: collectBody || {},
        updateScoreData: updateScoreList || {}
      };
    },
    *getCountDeductLog({ payload }, { put, call }) {
      // console.log(payload);
      yield put({
        type: "save",
        payload: {
          tableLoading: true
        }
      });
      const response = yield getCountDeductLog(payload);
      const { data: body } = response,
        { data, code, message } = body;
      if (code !== 0) {
        yield put({
          type: "save",
          payload: {
            tableLoading: false
          }
        });
        throw new Error(message);
      }
      // console.log(data);
      yield put({
        type: "save",
        payload: {
          dataSource: data,
          tableLoading: false
        }
      });
    },
  },
  reducers: {
    reset(state) {
      return {
        ...state,
        modalVisible: false,
        deductLogId: null,
        deductLogData: null,
        dataCollectData: null,
        updateScoreData: null,
        modalTitle: null
      }
    },
    resetQueryParams(state) {
      return {
        ...state,
        queryParams: {
          year: moment().year(),
          time: -1,
          deduct_type: -1
        },
      }
    },
    queryDetail(state, payload) {
      // console.log("model-res-data", payload);
      return {
        ...state,
        ...payload
      };
    },
    updateOrgName(state, payload) {
      // console.log("state----", state);
      return { ...state, ...payload };
    },
    updateYear(state, payload) {
      // console.log("state----", state);
      return { ...state, ...payload };
    },
    updateQuarter(state, payload) {
      // console.log("state----", state);
      return { ...state, ...payload };
    },
    save(state, { payload }) {
      // console.log("state----", state);
      return { ...state, ...payload };
    },

    tableLoading(state, { payload }) {
      return { ...state, ...payload };
    }
  },
  subscriptions: {
    setup({ dispatch, history }) { }
  }
};
