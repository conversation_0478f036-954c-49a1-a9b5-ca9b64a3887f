import React, { Component } from "react";
import {
  Table,
  Button,
  message,
  Popconfirm,
  Modal,
  Input,
  DatePicker,
  Checkbox,
  Spin,
  Empty,
} from "antd";
import {
  getCsrfList,
  getCsrfById,
  addEvalList,
  cancelCsrf,
  addEval,
  getEvalUserList,
  getQrCodeInfo,
} from "client/apis/cadre-portrait";
import QRCode from "qrcode.react";
import SelfIcon from "components/self-icon";
import html2canvas from "html2canvas";
import moment from "moment";
import "./main-content.less";

export default class Class extends Component {
  constructor(props) {
    super(props);
    this.state = {
      oid: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      visible: false,
      qrVisible: false,
      evaluation_id: "", //测评id
      codeInfo: [], // 二维码信息
      downLoading: false,
      spinning: false,
      // 评测列表
      list: [],
      // 测评人员列表
      testUserList: [],
      eval_info: [],
      checkedData: [],
      eval_name: "",
      eval_date: "",
      // 发布测评请求参数
      // releaseTest: {
      //   eval_name: "",
      //   eval_info: [],
      //   org_id: undefined,
      // },
    };
    this.myRef = React.createRef();
    this.myRefTwo = React.createRef();
    this.myRefThree = React.createRef();
    this.myRefFour = React.createRef();
    this.calendar = React.createRef();
    this.getList = this.getList.bind(this);
    this.openVisible = this.openVisible.bind(this);
    this.onRelease = this.onRelease.bind(this);
    this.changeTime = this.changeTime.bind(this);
    this.DownQr = this.DownQr.bind(this);
    this.openQrVisible = this.openQrVisible.bind(this);
  }

  componentDidMount() {
    const { oid } = this.state;
    this.getList(oid);
  }

  static getDerivedStateFromProps(props, state) {
    if (props.selectedOrgId !== state.oid) {
      return {
        oid: props.selectedOrgId,
        pattern_sub: props.pattern_sub,
      };
    }
    return null;
  }

  componentDidUpdate(prevProps, prevState) {
    const { oid } = this.state;
    if (oid !== prevState.oid) {
      this.getList(oid);
    }
  }

  // 获取列表
  async getList(oid) {
    const params = {
      org_id: oid,
    };
    const { data } = await getCsrfList(params);
    if (data.code === 0) {
      this.setState({
        list: data.data,
      });
    }
  }
  // 删除
  async onQuash(evaluation_id) {
    const { oid } = this.state;

    const params = {
      evaluation_id,
    };
    const {
      data: { code, message: _msg },
    } = await cancelCsrf(params);
    if (code === 0) {
      message.success("撤销成功");

      this.getList(oid);
    } else {
      message.error(_msg);
    }
  }

  /**
   * @description: 根据组织id查询评测人员信息
   * @param {*} org_id
   * @return {*}
   */
  async getTestUserById(org_id) {
    this.setState({
      spinning: true,
    });
    const {
      data: { code, data, message },
    } = await getEvalUserList({ org_id });
    this.setState({
      spinning: false,
    });
    if (code === 0) {
      const checkedData = [];
      const testUserList = data.map(
        ({ position_list, principal_list, ...other }, index) => {
          index > 0 && checkedData.push(index); // 二级组织默认选中
          return {
            ...other,
            list: [
              {
                index, // 为0时 代表时一级组织 特殊处理
                name: "县管正职",
                principal_list,
              },
              {
                index,
                name: "县管副职",
                position_list,
              },
            ],
          };
        }
      );
      this.setState({ checkedData, testUserList });
    } else {
      message.error(message);
    }
  }

  /**
   * @description: 发布测评
   * @return {*}
   */
  async onRelease() {
    const {
      eval_name,
      oid,
      testUserList,
      eval_date,
      checkedData = [],
    } = this.state;
    if (!eval_name) {
      return message.error("请输入测评名称");
    }
    if (!eval_date) {
      return message.error("请输入测评时间");
    }
    /* if (!testUserList || !testUserList.length) {
      return message.error("未选择测评人员");
    } */
    const data_list = [];
    testUserList.forEach(({ list, ...other }, index) => {
      if (checkedData.includes(index) || index === 0) {
        data_list.push({
          ...other,
          principal_list: list[0].principal_list,
          position_list: list[1].position_list,
        });
      }
    });
    const params = {
      org_id: oid,
      eval_name,
      end_time: eval_date.format("YYYY-MM-DD HH:mm:ss"),
      data_list,
    };
    const res = await addEval(params);
    if (res.data.code === 0) {
      message.success("发布成功");

      this.getList(oid);

      this.changeTime(null);
      /* this.setState({
        visible: false,
        eval_name: "",
        eval_date: "",
      }); */
      this.onCancel();
    } else {
      message.error(res.data.message);
    }
  }

  async openVisible() {
    this.setState({
      visible: true,
    });
    // 获取测评人员信息
    this.getTestUserById(this.state.oid);
  }

  /**
   * @description: 根据index移除 评测人员列表中的一项数据
   * @return {*}
   */
  /* onRemoveItem(index, item) {
    const { testUserList, eval_info } = this.state;

    const filter_eval = eval_info.filter(
      (_item, index) => _item.user_id !== item.user_id
    );
    this.setState({
      eval_info: filter_eval,
      testUserList: testUserList.filter((item, _index) => _index !== index),
    });
  } */

  /**
   * @description: 移除某个评测人员
   * @param {*} upLeader
   * @param {*} testUser
   * @param {*} key
   * @return {*}
   */
  onRemoveTestUser(upLeader = {}, userId, key) {
    const { testUserList } = this.state;
    upLeader[key] = upLeader[key].filter((item) => {
      return item.user_id != userId;
    });
    this.setState({ testUserList });
  }

  onInput(value) {
    this.setState({
      eval_name: value,
    });
  }

  changeTime(item) {
    this.setState({
      eval_date: item,
    });
  }

  DownQr(type, title) {
    const img = document.getElementById(`qrCode${type}`);
    html2canvas(img).then((canvas) => {
      // 生成图片 URL
      const imageURL = canvas.toDataURL("image/png");
      // 生成一个临时 a 标签并点击触发下载
      const tempLink =
        document.getElementById("down-a") || document.createElement("a");
      tempLink.id = "down-a";
      tempLink.download = title;
      tempLink.href = imageURL;
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      this.setState({
        downLoading: false,
      });
    });

    /* switch (type) {
      case 1:
        html2canvas(this.myRef.current).then((canvas) => {
          // 生成图片 URL
          const imageURL = canvas.toDataURL("image/png");
          // 生成一个临时 a 标签并点击触发下载
          const tempLink = document.createElement("a");
          tempLink.download = "书记";
          tempLink.href = imageURL;
          tempLink.click();
        });
        break;
      case 2:
        html2canvas(this.myRefTwo.current).then((canvas) => {
          // 生成图片 URL
          const imageURL = canvas.toDataURL("image/png");
          // 生成一个临时 a 标签并点击触发下载
          const tempLink = document.createElement("a");
          tempLink.download = "行政正职";
          tempLink.href = imageURL;
          tempLink.click();
        });
        break;
      case 3:
        html2canvas(this.myRefThree.current).then((canvas) => {
          // 生成图片 URL
          const imageURL = canvas.toDataURL("image/png");
          // 生成一个临时 a 标签并点击触发下载
          const tempLink = document.createElement("a");
          tempLink.download = "县官副职";
          tempLink.href = imageURL;
          tempLink.click();
        });
        break;
      case 4:
        html2canvas(this.myRefFour.current).then((canvas) => {
          // 生成图片 URL
          const imageURL = canvas.toDataURL("image/png");
          // 生成一个临时 a 标签并点击触发下载
          const tempLink = document.createElement("a");
          tempLink.download = "其他干部";
          tempLink.href = imageURL;
          tempLink.click();
        });
        break;
    } */
  }

  downloadAll() {
    const { codeInfo } = this.state;
    this.setState({
      downLoading: true,
    });
    setTimeout(() => {
      codeInfo.map(({ title }, index) => this.DownQr(index, title));
    }, 10);
  }

  openQrVisible(evaluation_id) {
    getQrCodeInfo({ evaluation_id }).then(
      ({ data: { code, data: res, message: msg } }) => {
        this.setState({
          qrVisible: code === 0,
          evaluation_id,
        });
        if (code === 0) {
          this.setState({
            codeInfo: res,
          });
        } else {
          message.error(msg);
        }
      }
    );
  }

  onCancel() {
    this.setState({
      visible: false,
      testUserList: [],
      eval_name: "",
      eval_date: "",
    });
  }

  render() {
    const { onSaveTreeProps } = this.props;
    const {
      visible,
      list,
      testUserList,
      qrVisible,
      evaluation_id,
      codeInfo,
      oid,
      eval_date,
      eval_name,
      downLoading,
      checkedData,
      spinning,
    } = this.state;
    const columns = [
      {
        title: "测评名称",
        align: "center",
        dataIndex: "evaluation_name",
      },
      {
        title: "上传时间",
        align: "center",
        render: (time) => <span>{time}</span>,
        dataIndex: "release_time",
      },
      {
        title: "操作",
        align: "center",
        render: (_, { has_cancel, status, pms_evaluation_id }) => (
          <React.Fragment>
            {/* <a onClick={() => history.push('/organization-committee/3', {type_child_name, ...record})}>详情</a> */}
            {/* <Divider type="vertical" /> */}
            {status === 2 ? (
              "-"
            ) : (
              <div style={{ display: "flex", justifyContent: "space-evenly" }}>
                {has_cancel === 1 && (
                  <Popconfirm
                    title="是否撤销?"
                    onConfirm={() => this.onQuash(pms_evaluation_id)}
                  >
                    <div className="party-btn">
                      <a>撤销</a>
                    </div>
                  </Popconfirm>
                )}

                <a
                  onClick={() => {
                    // this.openQrVisible(pms_evaluation_id);
                    onSaveTreeProps();
                    this.props.history.push(
                      `/cadre-system/release-evaluation?org_id=${oid}&evaluation_id=${pms_evaluation_id}&pattern_sub=${this.props.pattern_sub}`
                    );
                  }}
                >
                  详情
                </a>
                <a
                  onClick={() => {
                    // this.openQrVisible(pms_evaluation_id);
                    this.props.history.push(
                      `/cadre-system/test-code?evaluation_id=${pms_evaluation_id}&org_id=${oid}`
                    );
                  }}
                >
                  下载测评码
                </a>
              </div>
            )}
            {/* <a onClick={() => history.push('/organization-committee/2', {type_child_name, ...record})}>编辑</a> */}
          </React.Fragment>
        ),
      },
    ];
    const column_appraisal = [
      {
        title: "被测评人",
        align: "center",
        dataIndex: "name",
        width: "30%",
        className: "table-item-name",
      },
      {
        title: "",
        align: "center",
        key: "k",
        width: "70%",
        // 上级同级下级
        render: (_, item, index) => {
          const { principal_list, position_list } = item;
          const data = principal_list || position_list;
          return (
            <div className="test-person" key={index}>
              <div className="text-person-item">
                <div className="person-box">
                  {data.map(({ user_name, user_id }, index) => (
                    <div className="personal" key={`${user_id}-${index}`}>
                      <span className="name">{user_name}</span>
                      <span
                        className={`delete-icon ${
                          item.index > 0 &&
                          !checkedData.includes(item.index) &&
                          "delete-icon-gray"
                        }`}
                        onClick={() => {
                          (item.index === 0 ||
                            checkedData.includes(item.index)) &&
                            this.onRemoveTestUser(
                              item,
                              user_id,
                              principal_list
                                ? "principal_list"
                                : "position_list"
                            );
                        }}
                      ></span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          );
        },
      },
    ];

    return (
      <div className="p-main-content">
        <div className="p-main-table">
          <div>
            <Button
              type="primary"
              size="large"
              style={{
                margin: "20px 0",
                padding: "0 36px",
              }}
              // onClick={() => this.openVisible()}
              onClick={() => {
                onSaveTreeProps();
                this.props.history.push(
                  `/cadre-system/release-evaluation?org_id=${oid}&evaluation_id=${evaluation_id}&pattern_sub=${this.props.pattern_sub}`
                );
              }}
            >
              发布测评
            </Button>
          </div>
          <div>
            <Table
              rowKey="pms_evaluation_id"
              bordered
              columns={columns}
              dataSource={list}
              pagination={false}
            />
          </div>
        </div>
        <Modal
          title="发起测评"
          visible={visible}
          onCancel={() => this.onCancel()}
          width="55%"
          className="org-modal"
          footer={[
            <div style={{ textAlign: "center", borderTop: "0px" }}>
              <Button
                disabled={spinning}
                key="back"
                type="primary"
                onClick={this.onRelease}
              >
                发布
              </Button>
            </div>,
          ]}
        >
          <div className="org-modal-content">
            <div className="input-box">
              <div className="label-name">
                <div className="label">测评名称：</div>
                <Input
                  onChange={(e) => {
                    this.onInput(e.target.value);
                  }}
                  value={eval_name}
                  placeholder="请输入"
                />
              </div>
              <div>
                <div className="label">测评截止时间：</div>
                <DatePicker
                  // disabledDate={this.disabledStartDate}
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  value={eval_date}
                  // placeholder="Start"
                  onChange={this.changeTime}
                  // onOpenChange={this.handleStartOpenChange}
                />
              </div>
            </div>
            <Spin size="large" spinning={spinning}>
              <div className="table-box">
                {testUserList.length ? (
                  testUserList.map(({ org_id, org_name, list }, index) => {
                    return (
                      <div key={`${org_id}-${index}`}>
                        <div className="org_name">
                          {index > 0 ? (
                            <Checkbox
                              checked={checkedData.includes(index)}
                              onChange={({ target: { checked } }) => {
                                if (checked) {
                                  checkedData.push(index);
                                } else {
                                  checkedData.splice(
                                    checkedData.indexOf(index),
                                    1
                                  );
                                }
                                this.setState({ checkedData });
                              }}
                            >
                              {org_name}
                            </Checkbox>
                          ) : (
                            <span>{org_name}</span>
                          )}
                        </div>
                        <Table
                          rowKey="name"
                          bordered
                          columns={column_appraisal}
                          dataSource={list}
                          pagination={false}
                        />
                      </div>
                    );
                  })
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </div>
            </Spin>
          </div>
        </Modal>
        <Modal
          title="下载测评码"
          visible={qrVisible}
          onCancel={() => this.setState({ qrVisible: false, codeInfo: [] })}
          width="60%"
          className="org-modal-qr"
          footer={[
            <Button
              type="primary"
              disabled={downLoading}
              loading={downLoading}
              onClick={() => this.downloadAll()}
            >
              批量下载
            </Button>,
          ]}
        >
          <div className="Qr-box">
            {codeInfo.map(({ url, title }, index) => {
              return (
                <div className="box" key={title}>
                  <div id={"qrCode" + index} key={index} className="qr-warp">
                    <QRCode
                      value={url}
                      size={240} // 二维码的大小
                      fgColor="#000000" // 二维码的颜色
                      style={{ margin: "auto" }}
                    />
                    <div className="qr-text">{title}请扫此码测评</div>
                  </div>
                  <div
                    className="down-text"
                    onClick={() => this.DownQr(index, title)}
                  >
                    <SelfIcon className="down-icon" type={"gsg-xiayi"} />
                    <div>下载二维码</div>
                  </div>
                </div>
              );
            })}
            {/* <div className="box" id="oneQrCode" ref={this.myRef}>
              <QRCode
                id="qrCode"
                value={`https://gbcp.cqfd.gov.cn/?org_id=${oid}&evaluation_id=${evaluation_id}&type=2&evaluation_type_id=1`}
                size={240} // 二维码的大小
                fgColor="#000000" // 二维码的颜色
                style={{ margin: "auto" }}
              />
              <div className="qr-text">
                <div className="text">书记请扫此码测评</div>
                <div className="down-text" onClick={() => this.DownQr(1)}>
                  <div
                    style={{
                      backgroundImage: `url(${DownImg})`,
                      backgroundSize: "100%,100%",
                      width: "18px",
                      height: "18px",
                      marginRight: "6px",
                    }}
                  ></div>
                  <div>下载二维码</div>
                </div>
              </div>
            </div>
            <div className="box" id="TwoQrCode" ref={this.myRefTwo}>
              <QRCode
                id="qrCode"
                value={`https://gbcp.cqfd.gov.cn/?org_id=${oid}&evaluation_id=${evaluation_id}&type=1&evaluation_type_id=1`}
                size={240} // 二维码的大小
                fgColor="#000000" // 二维码的颜色
                style={{ margin: "auto" }}
              />
              <div className="qr-text">
                <div className="text">行政正职请扫此码测评</div>
                <div className="down-text" onClick={() => this.DownQr(2)}>
                  <div
                    style={{
                      backgroundImage: `url(${DownImg})`,
                      backgroundSize: "100%,100%",
                      width: "18px",
                      height: "18px",
                      marginRight: "6px",
                    }}
                  ></div>
                  <div>下载二维码</div>
                </div>
              </div>
            </div>
            <div className="box" id="TwoQrCode" ref={this.myRefThree}>
              <QRCode
                id="qrCode"
                value={`https://gbcp.cqfd.gov.cn/?org_id=${oid}&evaluation_id=${evaluation_id}&type=3&evaluation_type_id=1`}
                size={240} // 二维码的大小
                fgColor="#000000" // 二维码的颜色
                style={{ margin: "auto" }}
              />
              <div className="qr-text">
                <div className="text">县管副职请扫此码测评</div>
                <div className="down-text" onClick={() => this.DownQr(3)}>
                  <div
                    style={{
                      backgroundImage: `url(${DownImg})`,
                      backgroundSize: "100%,100%",
                      width: "18px",
                      height: "18px",
                      marginRight: "6px",
                    }}
                  ></div>
                  <div>下载二维码</div>
                </div>
              </div>
            </div>
            <div className="box" id="TwoQrCode" ref={this.myRefFour}>
              <QRCode
                id="qrCode"
                value={`https://gbcp.cqfd.gov.cn/?org_id=${oid}&evaluation_id=${evaluation_id}&type=4&evaluation_type_id=1`}
                size={240} // 二维码的大小
                fgColor="#000000" // 二维码的颜色
                style={{ margin: "auto" }}
              />
              <div className="qr-text">
                <div className="text">其他干部请扫此码测评</div>
                <div className="down-text" onClick={() => this.DownQr(4)}>
                  <div
                    style={{
                      backgroundImage: `url(${DownImg})`,
                      backgroundSize: "100%,100%",
                      width: "18px",
                      height: "18px",
                      marginRight: "6px",
                    }}
                  ></div>
                  <div>下载二维码</div>
                </div>
              </div>
            </div> */}
          </div>
        </Modal>
      </div>
    );
  }
}
