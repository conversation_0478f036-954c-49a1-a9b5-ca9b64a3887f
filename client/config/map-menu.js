import { getParam, Base64 } from "client/tool/util";
import { headers } from "client/tool/axios";
/**
 * 菜单映射关系
 * <AUTHOR>
 * @description  根据原型和服务端提供的id对应关系创建
 */

module.exports = {
  menus: {
    pc01: {
      name: "党务工作",
      id: "pc01",
      icon: "gsg-qi<PERSON><PERSON><PERSON>",
      path: "",
    },
    pc02: {
      name: "党群服务",
      id: "pc02",
      icon: "gsg-dang<PERSON><PERSON><PERSON>wu",
      path: "",
    },
    pc03: {
      name: "工作协同",
      id: "pc03",
      icon: "gsg-gongzu<PERSON><PERSON>ong",
      path: "",
    },
    pc04: {
      name: "系统设置",
      id: "pc04",
      icon: "gsg-xitongshezhi1",
      path: "",
    },
    pc05: {
      name: "七一书院",
      id: "pc05",
      icon: "gsg-qi<PERSON><PERSON><PERSON>",
      path: "",
    },
    pc06: {
      name: "消息中心",
      id: "pc06",
      icon: "gsg-xiaoxizhongxin1",
      path: "",
    },
    pc07: {
      name: "数据分析",
      id: "pc07",
      icon: "gsg-shujufenxi",
      path: "",
    },
    pc11: {
      name: "帮助中心",
      id: "pc11",
      icon: "gsg-bangzhuzhongxin1",
      // link: 'http://h.goodsogood.com/'
      link: "http://h.aidangqun.com",
    },
    pc12: {
      name: "志愿服务",
      id: "pc12",
      icon: "gsg-zhiyuanzhe",
      path: "",
    },
    pc14: {
      name: "我要出题",
      id: "pc14",
      icon: "gsg-woyaochuti",
      path: "",
    },
    pc15: {
      name: "六点打卡",
      id: "pc15",
      icon: "gsg-woyaochuti",
      path: "",
    },
    pc16: {
      name: "水利局",
      id: "pc16",
      icon: "gsg-shuili",
      path: "",
    },
    pc17: {
      name: "支部之窗",
      id: "pc17",
      icon: "gsg-zhibuzhichuang",
      path: "",
    },
    "01": {
      name: "办公中心",
      id: "01",
      icon: "",
      path: "",
    },
    "0101": {
      name: "通知消息",
      id: "0101",
      icon: "",
      path: "/notification-message",
    },
    "0102": {
      name: "审批管理",
      // name: "协同管理",
      id: "0102",
      icon: "",
      path: "/secondaryDirectory/0102",
    },
    "010201": {
      name: "发起审批",
      id: "010201",
      icon: "gsg-faqishenpi",
      path: "/new-process",
    },
    "010202": {
      name: "我的审批",
      id: "010202",
      icon: "gsg-shenpiliebiao",
      path: "/my-process",
    },
    "010203": {
      name: "审批类型管理",
      id: "010203",
      icon: "gsg-shenpileixingguanli",
      path: "/type-process",
    },
    "0103": {
      name: "会议计划",
      id: "0103",
      icon: "",
      path: "",
    },
    "010301": {
      name: "新增会议计划",
      id: "010301",
      icon: "",
      path: "",
    },
    "010302": {
      name: "我的会议",
      id: "010302",
      icon: "",
      path: "",
    },
    "0104": {
      name: "日程表",
      id: "0104",
      icon: "",
      path: "",
    },
    "0105": {
      name: "人员管理",
      id: "0105",
      icon: "",
      path: "/secondaryDirectory/0105",
    },
    "010501": {
      name: "人员管理",
      id: "010501",
      icon: "gsg-guanliyuan",
      path: "/personnel-manage",
    },
    "010502": {
      name: "权限角色管理",
      id: "010502",
      icon: "gsg-quanxianjiaoseguanli",
      path: "/root-manage",
    },
    "0106": {
      name: "部门管理",
      id: "0106",
      icon: "",
      path: "/department-manage",
    },
    "0107": {
      name: "标签管理",
      id: "0107",
      icon: "",
      path: "/tag-manage",
    },
    "0108": {
      name: "系统管理员管理",
      id: "0108",
      icon: "",
      path: "/system-manage",
    },
    // 0109功能父级关联到07
    "0109": {
      name: "账户设置",
      id: "0109",
      icon: "",
      path: "/secondaryDirectory/0109",
    },
    "010901": {
      name: "密码修改",
      id: "010901",
      icon: "gsg-xiugaimima",
      path: "/user-info-password",
    },
    "010902": {
      name: "手机号修改",
      id: "010902",
      icon: "gsg-xiugaishoujihaoma",
      path: "/user-info-phone",
    },
    "0110": {
      // name: '单位信息',
      name: "组织信息",
      id: "0110",
      icon: "",
      // path: "/organization-information"
      path: "/unit-information",
    },
    "0111": {
      name: "财务",
      id: "0111",
      icon: "",
      path: "",
    },
    "011101": {
      name: "点券账户",
      id: "011101",
      icon: "",
      path: "",
    },
    "011102": {
      name: "点券流水",
      id: "011102",
      icon: "",
      path: "",
    },
    // 0102跳转已有OA系统
    "0112": {
      name: "办公OA",
      id: "0112",
      icon: "",
      path: "",
      link: "http://oa.cqjgdj.gov.cn",
      // path: "/office-oa"
    },
    "0113": {
      name: "新闻管理",
      id: "0113",
      icon: "",
      path: "/news-management",
    },
    // gsg-huodong
    "02": {
      // name: "活动中心",
      name: "互动中心",
      id: "02",
      icon: "",
      path: "/activity",
    },
    "0201": {
      // name: "活动发布",
      name: "互动发布",
      id: "0201",
      icon: "",
      path: "/secondaryDirectory/0201",
    },
    /* "0201": {
          // name: "活动发布",
          name: "互动发布",
          id: "0201",
          icon: "",
          path: "/activity-template"
        }, */
    "020101": {
      name: "投票",
      id: "020101",
      icon: "gsg-toupiao",
      path: "/new-vote",
    },
    "020102": {
      name: "问卷调查",
      id: "020102",
      icon: "gsg-wenjuantiaocha",
      path: "/questionnaire",
    },
    "020103": {
      name: "有奖竞答",
      id: "020103",
      icon: "gsg-youjiangjingda",
      path: "/contest",
    },
    "020104": {
      // name: "活动",
      name: "线下互动",
      id: "020104",
      icon: "gsg-huodong",
      path: "/physical",
    },
    "020105": {
      name: "积分捐赠",
      id: "020105",
      icon: "gsg-juanzeng",
      path: "/contribute",
    },
    "0202": {
      // name: "活动管理",
      name: "互动管理",
      id: "0202",
      icon: "",
      path: "/all-activity",
    },
    "0203": {
      // name: "活动分类",
      name: "互动分类",
      id: "0203",
      icon: "",
      path: "/activity-list",
    },
    "0204": {
      // name: "活动奖励",
      name: "互动奖励",
      id: "0204",
      icon: "",
      path: "/secondaryDirectory/0204",
    },
    "020401": {
      name: "奖品发布",
      id: "020401",
      icon: "gsg-wupinfabu",
      path: "/create-article",
    },
    "020402": {
      name: "奖品管理",
      id: "020402",
      icon: "gsg-wupinguanli",
      path: "/article-manage",
    },
    "020403": {
      name: "中奖记录",
      id: "020403",
      icon: "gsg-zhongjiangjilu",
      path: "/winning-history",
    },
    "020404": {
      name: "奖品发货",
      id: "020404",
      icon: "gsg-wupinfahuo",
      path: "/prize-delivery",
    },
    "0205": {
      name: "报名费",
      id: "0205",
      icon: "",
      path: "",
    },
    "020501": {
      name: "收取的报名费",
      id: "020501",
      icon: "",
      path: "",
    },
    "020502": {
      name: "报名费对账单",
      id: "020502",
      icon: "",
      path: "",
    },
    "020503": {
      name: "收款账户",
      id: "020503",
      icon: "",
      path: "",
    },
    "03": {
      name: "文件管理",
      id: "03",
      icon: "",
      path: "",
    },
    "0301": {
      name: "上传文件",
      id: "0301",
      icon: "",
      path: "",
    },
    "0302": {
      name: "文件列表",
      id: "0302",
      icon: "",
      path: "",
    },
    "0303": {
      name: "文件分类",
      id: "0302",
      icon: "",
      path: "",
    },
    "04": {
      name: "组织管理",
      id: "04",
      icon: "",
      path: "/organize",
    },
    //组织大数据菜单儿不可直接访问，必须通过上层组织结构，选取某个组织才能够使用
    // "0401": {
    //   name: "组织大数据",
    //   id: "0401",
    //   icon: "",
    //   path: "/organize-data"
    // },
    "0401": {
      name: "行政机构管理",
      id: "0401",
      icon: "",
      path: "/organize-framework",
    },
    PMS0214: {
      name: "班子成员",
      id: "PMS0214",
      icon: "",
      path: "/cadre-party-member",
    },
    PMS0215: {
      name: "机构树管理",
      id: "PMS0215",
      icon: "",
      path: "/organize-framework",
    },
      PMS0216: {
      name: "职务管理",
      id: "PMS0216",
      icon: "",
      path: "/position-management",
    },
    // 跳转12371
    "0402": {
      // name: "组织架构",
      name: "组织及党员管理",
      id: "0402",
      icon: "",
      path: "",
      link: "https://vpn.12371.gov.cn",
      // path: "/organize-framework"
    },
    46: {
      name: "党员管理",
      id: "46",
      icon: "",
      path: "",
    },
    // "0403": {
    //   name: "单位领导班子",
    //   id: "0403",
    //   icon: "",
    //   path: "/leader-group",
    // },
    PMS02: {
      name: "干部信息管理",
      id: "PMS02",
      icon: "",
      path: "/cadre-management",
    },
    /** 
     * PMS02 干部信息管理
PMS0201 干部管理
PMS0202 表扬表彰
PMS0203 负面清单
     * 
     * 
    */
    PMS0201: {
      name: "干部管理",
      id: "PMS0201",
      icon: "",
      path: "/cadre-management",
    },
    PMS0202: {
      name: "表扬表彰",
      id: "PMS0202",
      icon: "",
      path: "/praise-commendation",
    },
    PMS0203: {
      name: "负面清单",
      id: "PMS0203",
      icon: "",
      path: "/negative-list",
    },
    // "0404": {
    //   name: "党组织委员会",
    //   id: "0404",
    //   icon: "",
    //   path: "/party-organization",
    // },
    PMS03: {
      name: "干部测评",
      id: "PMS03",
      icon: "",
      path: "/cadre-system/evaluation-list",
    },
    PMS04: {
      name: "干部序列",
      id: "PMS04",
      icon: "",
      path: "/sequence-management",
    },
    PMS0401: {
      name: "机构序列",
      id: "PMS0401",
      icon: "",
      path: "/organizational-sequence-management",
    },
    PMS05: {
      name: "巡察测评",
      id: "PMS05",
      icon: "",
      path: "/cadre-system/xuncha-list",
    },
    PMS06: {
      name: "中层干部测评",
      id: "PMS06",
      icon: "",
      path: "/cadre-system/zhongceng-list",
    },
    PMS07: {
      name: "巡察评价",
      id: "PMS07",
      icon: "",
      // path: "/cadre-system/xuncha-list",
      link: `${window.location.origin}/inspection/#/data-screen/home`,
    },
    "0405": {
      name: "党小组",
      id: "0405",
      icon: "",
      path: "/party-group",
    },
    "05": {
      name: "网宣平台",
      id: "05",
      icon: "",
      path: "",
    },
    "06": {
      name: "上传资源",
      id: "06",
      icon: "gsg-shangchuanziliao",
      path: "/upload",
    },
    "0601": {
      name: "上传资源",
      id: "0601",
      icon: "",
      path: "/upload-resource",
    },
    72: {
      name: "社会组织",
      id: "72",
      // icon: "gsg-shehuizuzhi",
      // path: "/upload",
    },
    7203: {
      name: "入驻审核",
      id: "7203",
      icon: "",
      path: "/ResidentAudit",
    },
    // gsg-xitongshezhi
    "07": {
      name: "系统设置",
      id: "07",
      icon: "",
      path: "",
    },
    "0703": {
      name: "权限管理",
      id: "0703",
      path: "/authority-management",
    },
    PMS09: {
      name: "数据初始化",
      id: "PMS09",
      icon: "",
      path: "/data-initialization",
    },
    "0706": {
      name: "动态表单",
      id: "0706",
      path: "/dynamic-form-config",
    },
    // gsg-xiaoxizhongxin
    "08": {
      name: "消息中心",
      id: "08",
      icon: "",
      path: "",
    },
    "0801": {
      name: "通知消息",
      id: "0801",
      icon: "",
      path: "/notification-message",
    },
    "0802": {
      name: "消息管理",
      id: "0802",
      icon: "",
      path: "/message-management",
    },
    "0803": {
      name: "模板设置",
      id: "0803",
      icon: "",
      path: "/template-setting",
    },
    // gsg-gongxiangziyuan
    "09": {
      name: "共享资源",
      id: "09",
      icon: "",
      path: "",
    },
    "0901": {
      name: "发布资源",
      id: "0901",
      icon: "",
      path: "/release-resources",
    },
    "0902": {
      name: "管理资源",
      id: "0902",
      icon: "",
      path: "/resources-management",
    },
    // gsg-tongjizhongxin
    10: {
      name: "统计中心",
      id: "10",
      icon: "",
      path: "",
    },
    1001: {
      name: "统计看板",
      id: "1001",
      icon: "",
      path: "/secondaryDirectory/1001",
    },
    100101: {
      name: "积分兑换统计",
      id: "100101",
      icon: "gsg-jifenduihuantongji",
      path: "/integral-exchange-statisitics",
    },
    1002: {
      name: "智能查询",
      id: "1002",
      icon: "",
      path: "/secondaryDirectory/1002",
    },
    100201: {
      name: "积分兑换明细",
      id: "100201",
      icon: "gsg-jifen",
      path: "/integral-exchange",
    },
    100202: {
      name: "分级平台公众号列表",
      id: "100202",
      icon: "gsg-jifenduihuantongji",
      path: "/weixin-accounts",
    },
    100203: {
      name: "销售清单明细查询",
      id: "100203",
      icon: "gsg-xiaoshouqingdan",
      path: "/order-statistics",
    },
    2002: {
      name: "积分兑换设置",
      id: "2002",
      icon: "",
      path: "/integral-rule",
    },
    2603: {
      name: "扶贫主页",
      id: "2603",
      icon: "",
      path: "/baweiyuzhen",
    },
    2604: {
      name: "扶贫专题页",
      id: "2604",
      icon: "",
      path: "/poverty-alleviation-theme",
    },
    1003: {
      name: "统计设置",
      id: "1003",
      icon: "",
      path: "/statistical-setting",
    },
    // gsg-wangxuanpingtai
    11: {
      name: "新闻中心",
      id: "11",
      icon: "",
      path: "",
    },
    1101: {
      name: "新闻发布",
      id: "1101",
      icon: "",
      path: "/create-news/1",
    },
    1102: {
      name: "新闻管理",
      id: "1102",
      icon: "",
      path: "/news-list",
    },
    1103: {
      name: "新闻栏目",
      id: "1103",
      icon: "",
      path: "/news-column/1",
    },
    // gsg-jishiguanli
    12: {
      name: "纪实管理",
      id: "12",
      icon: "",
      path: "",
    },
    1201: {
      // name: "会议议题管理",
      // name: "工作任务发布",
      name: "派发任务",
      id: "1201",
      icon: "",
      path: "/meeting-topic-management",
    },
    1202: {
      // name: "考核计划管理",
      // name: "组织生活设置",
      name: "活动类型",
      id: "1202",
      icon: "",
      path: "/assess-plan-manage",
    },
    1203: {
      // name: "纪实结果检查",
      // name: "活动检查",
      name: "活动回顾",
      id: "1203",
      icon: "",
      path: "/check-plan",
    },
    // 1204功能父级关联到20
    1204: {
      name: "基础设置",
      // name: "组织生活设置",
      // name: "组织生活类型",
      id: "1204",
      icon: "",
      path: "/docu-manage-base-setting",
    },
    // 纪实执行合并到12中
    // "13": {
    //   name: "纪实执行",
    //   id: "13",
    //   icon: "gsg-jishizhihang",
    //   path: ""
    // },
    1301: {
      // name: "任务管理",
      name: "任务查看",
      id: "1301",
      icon: "",
      path: "/task-manage-base-setting",
    },
    1302: {
      // name: "会议管理",
      // name: "组织生活管理",
      // name: "活动组织",
      name: "活动录入",
      id: "1302",
      icon: "",
      path: "/meeting-manage",
    },
    // "1303": {
    //   // name: "填报纪实情况表",
    //   // name: "活动纪实（直报）",
    //   name: "活动补录",
    //   id: "1303",
    //   icon: "",
    //   path: "/fill-meeting-report-directly"
    // },
    // gsg-fenjiguanli
    14: {
      name: "分级平台",
      id: "14",
      icon: "",
      path: "",
    },
    1401: {
      name: "设置",
      id: "1401",
      icon: "",
      path: "/weixin-bind",
    },
    1402: {
      name: "新闻矩阵",
      id: "1402",
      icon: "",
      path: "/news-matrix",
    },
    1403: {
      name: "互动矩阵",
      id: "1403",
      icon: "",
      path: "/activitys-matrix",
    },
    // gsg-wangxuanpingtai
    15: {
      // name: "党费",
      name: "党费管理",
      id: "15",
      icon: "",
      path: "",
    },
    // 1051功能父级关联到20
    1501: {
      name: "党费设置",
      id: "1501",
      icon: "",
      path: "/party-setting",
    },
    1502: {
      name: "收交统计",
      id: "1502",
      icon: "",
      path: "/party-pay-statistics",
    },
    1503: {
      name: "党费标准",
      id: "1503",
      icon: "",
      path: "/party-normal",
    },
    // 工委的党费返还
    1505: {
      name: "党费返还",
      id: "1505",
      icon: "",
      path: "/party-return",
    },

    // 党委的党费
    1504: {
      name: "党费返还",
      id: "1504",
      icon: "",
      path: "/dues-return",
    },
    1506: {
      name: "党费返还记录",
      id: "1506",
      icon: "",
      path: "/party-history",
    },
    1507: {
      name: "党费对账报表",
      id: "1507",
      icon: "",
      path: "/party-fee-reconciliation",
    },
    1508: {
      name: "党费交纳流水",
      id: "1508",
      icon: "",
      path: "/party-pay-flow",
    },
    1511: {
      name: "	同步异常记录",
      id: "1511",
      icon: "",
      path: "/party-sync-abnormal",
    },
    // 16,17为微信菜单
    // 跳转七一书院后台
    18: {
      name: "七一书院",
      id: "18",
      icon: "",
      path: "/library-islogin",
      // link: "https://71sy.datajun.com/main/toLogin"
    },
    // 跳转每日一课后台
    19: {
      name: "每日一课",
      id: "19",
      icon: "",
      path: "",
      link: "https://mryk.cqjgdj.gov.cn/main/toLogin", //old https://mryk.datajun.com
    },
    20: {
      name: "参数设置",
      id: "20",
      icon: "",
      path: "",
    },
    2001: {
      name: "考核设置",
      id: "2001",
      icon: "",
      path: "/score-management",
    },
    2004: {
      name: "党费账户设置",
      id: "2004",
      icon: "",
      path: "/party-return-accounts",
    },
    200101: {
      name: "考核分值管理",
      id: "200101",
      icon: "",
      path: "",
    },
    200102: {
      name: "考核组管理",
      id: "200102",
      icon: "",
      path: "",
    },
    21: {
      name: "考核管理",
      id: "21",
      // gsg-kaoheguanli
      icon: "",
      path: "",
    },
    2101: {
      name: "考核任务管理",
      id: "2101",
      icon: "",
      path: "/examination-tasks-list",
    },
    2102: {
      name: "任务执行",
      id: "2102",
      icon: "",
      path: "/execute-task",
    },
    2103: {
      name: "任务审核",
      id: "2103",
      icon: "",
      path: "/audit-task",
    },
    2104: {
      name: "问题查处",
      id: "2104",
      icon: "",
      path: "/question-search",
    },
    2105: {
      name: "任务抽查",
      id: "2105",
      icon: "",
      path: "",
    },
    2106: {
      name: "考核统计",
      id: "2106",
      icon: "",
      path: "/evaluation-statistics",
    },
    2107: {
      name: "考核评价",
      id: "2107",
      icon: "",
      path: "/assessment-list",
    },

    36: {
      name: "述职评议",
      id: "36",
      icon: "",
      path: "",
    },
    3602: {
      name: "基层组织述职评议",
      id: "3602",
      icon: "",
      path: "/project-review",
    },
    3601: {
      name: "述职评议统计",
      id: "3601",
      icon: "",
      path: "/project-statistical",
    },
    35: {
      name: "民主评议",
      id: "35",
      icon: "",
      path: "",
    },
    3502: {
      name: "民主评议党员",
      id: "3502",
      icon: "",
      path: "/democracy-member",
    },
    3501: {
      name: "民主评议统计",
      id: "3501",
      icon: "",
      path: "/democracy-statistical",
    },
    "0704": {
      name: "升级日志管理",
      id: "0704",
      icon: "",
      path: "/version-management",
    },
    22: {
      name: "党务看板",
      id: "22",
      icon: "",
      path: "",
    },
    2201: {
      name: "党支部组织生活统计",
      id: "2201",
      icon: "",
      path: "/party-branch-report",
    },
    2202: {
      name: "领导干部双重组织生活统计",
      id: "2202",
      icon: "",
      path: "/leading-cadres-report",
    },
    2203: {
      name: "党费交纳完成情况统计",
      id: "2203",
      icon: "",
      path: "/payment-count",
    },
    2206: {
      name: "考核报表",
      id: "2206",
      icon: "",
      path: "/evaluation-report",
    },
    2204: {
      name: "党员组织生活统计",
      id: "2204",
      icon: "",
      path: "/party-member-org-life",
    },
    2205: {
      name: "组织生活一览表",
      id: "2205",
      icon: "",
      path: "/org-life-view",
    },
    2207: {
      name: "综合报表库",
      id: "2207",
      icon: "",
      path: "/comprehensive-report-library",
    },
    2208: {
      name: "三基建设看板",
      id: "2208",
      icon: "",
      path: "",
      // link: "http://bi.cqjgdj.gov.cn/webroot/decision/view/form?viewlet=decisionSystem.frm&ref_t=design&ref_c=fb053c16-1967-4d58-99a5-224104952aec",
      link: "https://owss.cqjgdj.gov.cn/owsz/rpt/webroot/decision/view/report?viewlet=decisionSystem.frm&ref_t=design",
    },
    2209: {
      name: "人员查询",
      id: "2209",
      icon: "",
      path: "/report-iframe/2209",
    },
    2210: {
      name: "组织基本信息",
      id: "2210",
      icon: "",
      path: "/report-iframe/2210",
    },
    2211: {
      name: "驾驶舱",
      id: "2211",
      icon: "",
      path: "/cockpit",
    },
    2212: {
      name: "市委办公厅大屏",
      id: "2212",
      icon: "",
      path: "/office-large-size",
    },
    47: {
      name: "考核结果统计分析",
      id: "47",
      icon: "",
      path: "/report-iframe/47",
    },
    2003: {
      name: "统计报表设置",
      id: "2003",
      icon: "",
      path: "/report-setting",
    },
    23: {
      name: "发送记录",
      id: "23",
      icon: "",
      path: "/send-message-record",
    },
    24: {
      name: "消息发送",
      id: "24",
      icon: "",
      path: "/send-message",
    },
    25: {
      name: "账户系统",
      id: "25",
      icon: "",
      path: "/accounts-stylem",
    },
    27: {
      name: "党员画像",
      id: "27",
      icon: "",
      path: "/member-photo",
    },
    28: {
      name: "组织画象",
      id: "28",
      icon: "",
      path: "/org-photo",
    },
    29: {
      name: "一元捐活动分析",
      id: "29",
      icon: "",
      path: "/member-oneMoney",
    },
    32: {
      name: "大数据",
      id: "32",
      icon: "",
      link: "/static/charts/",
    },
    "0406": {
      name: "成员管理",
      id: "0406",
      icon: "",
      path: "/member-management",
    },
    26: {
      name: "渠道管理",
      id: "26",
      icon: "",
      path: "",
    },
    2601: {
      name: "供应渠道管理",
      id: "2601",
      icon: "",
      path: "/channel-registration",
    },
    2602: {
      name: "商城链接工具	",
      id: "2602",
      icon: "",
      path: "/channel-generator-url",
    },
    // "0407": {
    //   name: "组织管理	",
    //   id: "0407",
    //   icon: "",
    //   path: "/org-management",
    // },
    PMS01: {
      name: "班子管理",
      id: "PMS01",
      icon: "",
      path: "/org-management",
    },

    34: {
      name: "主题推送策略	",
      id: "34",
      icon: "",
      path: "/push-strategy",
    },
    37: {
      name: "奖惩信息",
      id: "37",
      icon: "",
      path: "",
    },
    3701: {
      name: "组织奖惩",
      id: "3701",
      icon: "",
      path: "/org-report-comment",
    },
    3702: {
      name: "党员奖惩",
      id: "3702",
      icon: "",
      path: "/party-report-comment",
    },
    38: {
      name: "日程管理",
      id: "38",
      icon: "",
      path: "/schedule-management",
    },
    39: {
      name: "志愿团体",
      id: "39",
      icon: "",
      path: "/voluntary-group-management",
    },
    40: {
      name: "志愿者",
      id: "40",
      icon: "",
      path: "/volunteer",
    },
    41: {
      name: "志愿项目",
      id: "41",
      icon: "",
      path: "",
    },
    4101: {
      name: "本团体项目",
      id: "4101",
      icon: "",
      path: "/volunteer-project",
    },
    4102: {
      name: "下级团体项目",
      id: "4102",
      icon: "",
      path: "/subordinate-volunteer-project",
    },
    42: {
      name: "求助信息",
      id: "42",
      icon: "",
      path: "/volunteer-seek-help",
    },
    43: {
      name: "送服务",
      id: "43",
      icon: "",
      path: "/deliver-service",
    },
    "0705": {
      name: "解锁用户",
      id: "0705",
      icon: "",
      path: "/unlock-user",
    },
    "0707": {
      name: "菜单管理",
      id: "0707",
      icon: "",
      path: "/menu-config",
    },
    pc13: {
      name: "云区管理",
      id: "pc13",
      icon: "gsg-zhiyuanzhe",
      path: "",
    },
    48: {
      id: "48",
      name: "话题管理",
      icon: "",
      path: "/cloud-topic-management",
    },
    // "100":{
    //     name: "任务管理",
    //     id: "0707",
    //     icon: "",
    //     path: ""
    // },
    49: {
      name: "任务管理",
      id: "49",
      icon: "",
      path: "",
    },
    4901: {
      name: "发布任务",
      id: "4901",
      icon: "",
      path: "/wangxin-distributed-task",
    },
    4902: {
      name: "我的任务",
      id: "4902",
      icon: "",
      path: "/wangxin-my-task",
    },
    4903: {
      name: "任务审核",
      id: "4903",
      icon: "",
      path: "/wangxin-examine-list",
    },
    4904: {
      name: "待办任务",
      id: "4904",
      icon: "",
      path: "/wangxin-todo-list",
    },
    50: {
      name: "数据体检",
      id: "50",
      icon: "",
      path: "/data-physical-examination",
    },
    55: {
      name: "题目初审",
      id: "55",
      icon: "",
      path: "/audit-question-list/1",
    },
    56: {
      name: "题目终审",
      id: "56",
      icon: "",
      path: "/audit-question-list/2",
    },
    57: {
      name: "内容初审",
      id: "57",
      icon: "",
      path: "/audit-content-list/1",
    },
    58: {
      name: "内容终审",
      id: "58",
      icon: "",
      path: "/audit-content-list/2",
    },
    5901: {
      name: "党员报到管理",
      id: "5901",
      icon: "",
      path: "/party-member-manage",
    },
    5902: {
      name: "党员报到审核",
      id: "5902",
      icon: "",
      path: "/party-member-audit",
    },
    5903: {
      name: "活力指数概况",
      id: "5903",
      icon: "",
      path: "/vitality-index",
    },
    5904: {
      name: "组织活力指数榜",
      id: "5904",
      icon: "",
      path: "/organizational-vitality-ranking",
    },
    5905: {
      name: "党员活力指数榜",
      id: "5905",
      icon: "",
      path: "/member-vitality-ranking",
    },
    5906: {
      name: "个人活力指数",
      id: "5906",
      icon: "",
      path: "/individual-vitality-ranking",
    },
    59: {
      name: "活力型党组织",
      id: "59",
    },
    60: {
      name: "资源中心",
      id: "60",
    },
    6001: {
      name: "资源库",
      id: "6001",
      icon: "",
      path: "/resource-library",
    },
    6002: {
      name: "文件库管理",
      id: "6002",
      icon: "",
      path: "/document-manage",
    },
    6003: {
      name: "师资库管理",
      id: "6003",
      icon: "",
      path: "/teacher-resources",
    },
    6004: {
      name: "案例库管理",
      id: "6004",
      icon: "",
      path: "/case-manage",
    },
    6005: {
      name: "场地库管理",
      id: "6005",
      icon: "",
      path: "/field-manage",
    },
    6006: {
      name: "分类管理",
      id: "6006",
      icon: "",
      path: "/sort-manage",
    },
    61: {
      name: "智慧民情",
      id: "61",
    },
    6101: {
      name: "智慧大屏",
      id: "6101",
      icon: "",
      path: "/wzs-large-size",
    },
    6102: {
      name: "邢家桥",
      id: "6102",
      icon: "",
      path: "/xjq-large-size",
    },
    64: {
      name: "党建品牌管理",
      id: "64",
      path: "/party-brand",
    },
    65: {
      name: "党建阵地管理",
      id: "65",
      path: "/party-building-positions",
    },
    66: {
      name: "组织风采",
      id: "66",
      path: "/organization-style",
    },
    67: {
      name: "VR党建内容管理",
      id: "67",
      path: "/vr-Administration",
    },
    201: {
      name: "菜单管理",
      id: "201",
      icon: "",
      path: "/",
    },
    202: {
      name: "菜单管理",
      id: "202",
      icon: "",
      path: "/",
    },
    203: {
      name: "菜单管理",
      id: "203",
      icon: "",
      path: "/",
    },
    204: {
      name: "菜单管理",
      id: "204",
      icon: "",
      path: "/",
    },
    "0410": {
      name: "组织大数据",
      id: "0410",
      icon: "",
      path: "/wangxin-framework",
    },
    "0411": {
      name: "成员管理",
      id: "0411",
      icon: "",
      path: "/wangxin-member-management",
    },

    // 水利局
    68: {
      name: "大屏",
      id: "68",
      icon: "",
      link:
        process.env.NODE_ENV === "development"
          ? "http://localhost:8081"
          : "https://fd-datav917.aidangqun.com",
    },
    69: {
      name: "资源发布",
      id: "69",
      icon: "",
      path: "/WaterControlBureau/ResourcePublish",
    },
    70: {
      name: "资源管理",
      id: "70",
      icon: "",
      path: "/WaterControlBureau/ResourceMgt",
    },
    71: {
      name: "栏目管理",
      id: "71",
      icon: "",
      path: "/WaterControlBureau/ColumnMgt",
    },

    // 支部之窗
    17001: {
      name: "组织大数据",
      id: "17001",
      icon: "",
      path: "/organize-framework",
    },
    // 17002: {
    //   name: "党组织委员会",
    //   id: "17002",
    //   icon: "",
    //   path: "/party-organization",
    // },
    17003: {
      name: "党小组管理",
      id: "17003",
      icon: "",
      path: "/party-group",
    },
    17004: {
      name: "成员管理",
      id: "17004",
      icon: "",
      path: "/member-management",
    },
    17005: {
      name: "组织风采管理",
      id: "17005",
      icon: "",
      path: "/organization-style",
    },
    17006: {
      name: "组织历史管理",
      id: "17006",
      icon: "",
      path: "/History-Management",
    },
    17007: {
      name: "党建品牌管理",
      id: "17007",
      icon: "",
      path: "/party-brand",
    },
    17008: {
      name: "纪实管理",
      id: "17008",
      icon: "",
      path: "",
    },
    1700801: {
      name: "活动录入",
      id: "1700801",
      icon: "",
      path: "/meeting-manage",
    },
    1700802: {
      name: "活动回顾",
      id: "1700802",
      icon: "",
      path: "/check-plan",
    },
    17009: {
      name: "专题学习",
      id: "17009",
      icon: "",
      path: "",
    },
    1700901: {
      name: "专题资讯发布",
      id: "1700901",
      icon: "",
      path: "/create-news/2",
    },
    1700902: {
      name: "专题资讯管理",
      id: "1700902",
      icon: "",
      path: "/informations-list",
    },
    1700903: {
      name: "专题栏目管理",
      id: "1700903",
      icon: "",
      path: "/news-column/2",
    },
    77: {
      name: "党群服务中心",
      id: "77",
      icon: "",
      path: "/PartyServiceCenter",
    },
    PMS0204: {
      name: "现实表现",
      id: "PMS0204",
      icon: "",
      path: "/significant-performance",
    },
    PMS0205: {
      name: "年度考核",
      id: "PMS0205",
      icon: "",
      path: "/annual-assessment",
    },
    PMS0206: {
      name: "干部任用记录",
      id: "PMS0206",
      icon: "",
      path: "/appointment-records",
      parent_id: "PMS02",
    },
    PMS0207: {
      name: "社会关系",
      id: "PMS0207",
      icon: "",
      path: "/appointment-records",
      parent_id: "PMS02",
    },
    PMS0208: {
      name: "信访举报情况",
      id: "PMS0208",
      icon: "",
      path: "/petition-reporting",
      parent_id: "PMS02",
    },
    PMS0209: {
      name: "巡视巡察评价",
      id: "PMS0209",
      icon: "",
      path: "/patrol",
      parent_id: "PMS02",
    },
    PMS0210: {
      name: "个人事项核查",
      id: "PMS0210",
      icon: "",
      path: "/personal-matters",
      parent_id: "PMS02",
    },
    PMS0211: {
      name: "婚姻状况",
      id: "PMS0211",
      icon: "",
      path: "/marital-status",
      parent_id: "PMS02",
    },
    PMS0212: {
      name: "出国（境）证件记录",
      id: "PMS0212",
      icon: "",
      path: "/overseas-record",
      parent_id: "PMS02",
    },
    PMS0213: {
      name: "培训情况",
      id: "PMS0213",
      icon: "",
      path: "/training",
      parent_id: "PMS02",
    },
    PMS0216: {
      name: "班子回访",
      id: "PMS0216",
      icon: "",
      path: "/team-visit",
      parent_id: "PMS02",
    },
    PMS0217: {
      name: "年度述职",
      id: "PMS0217",
      icon: "",
      path: "/annual-report",
      parent_id: "PMS02",
    },
    PMS0218: {
      name: "征求意见",
      id: "PMS0218",
      icon: "",
      path: "/take-advice",
      parent_id: "PMS02",
    },
    PMS08: {
      name: '"下评上"测评',
      id: "PMS08",
      icon: "",
      path: "/cadre-system/mechanism-list",
    },
    PMS10: {
      name: "测评管理",
      id: "PMS10",
      icon: "",
      path: "",
    },
    PMS1003: {
      name: "测评配置",
      id: "PMS1003",
      icon: "",
      path: "/evaluation-management/evaluation-config",
    },
    PMS11: {
      name: "测评统计",
      id: "PMS11",
      icon: "",
      path: "",
    },
    PMS1101: {
      name: "班子测评统计",
      id: "PMS1101",
      icon: "",
      path: "/evaluation-stats/team-eval",
    },
    PMS1102: {
      name: "正职测评统计",
      id: "PMS1102",
      icon: "",
      path: "/evaluation-stats/primary-eval",
    },
    PMS1103: {
      name: "副职测评统计",
      id: "PMS1103",
      icon: "",
      path: "/evaluation-stats/deputy-eval",
    },
    PMS1104: {
      name: "副职测评统计",
      id: "PMS1104",
      icon: "",
      path: "/evaluation-stats/general-eval",
    },
    PMS1002: {
      name: "发布测评",
      id: "PMS1002",
      icon: "",
      path: "/evaluation-publish",
    },
    PMS12: {
      name: "干部选用",
      id: "PMS12",
      icon: "",
      path: "/motion-plan",
    },
  },
  pathMapName: {
    "/dictionary-config": ["字典表配置"],
    "/cockpit": ["驾驶舱"],
    "/smart-reports": ["数据体检表", "", "/comprehensive-report-library"],
    "/volunteer-seek-help": ["求助信息"],
    "/deliver-service": ["送服务"],
    "/volunteer": ["志愿者"],
    "/voluntary-group-management": ["志愿团体管理"],
    "/report-iframe/2210": ["组织基本信息"],
    "/schedule-management": ["日程管理"],
    "/report-iframe/2209": ["人员查询"],
    "/comprehensive-report-library": ["综合报表库"],
    "/push-strategy": ["主题推送策略管理"],
    "/party-fee-reconciliation": ["党费对账报表", ""],
    "/assessment-list": ["考核评价", ""],
    "/activity-template": ["问卷模板", "", "/assessment-list"],
    // "/activity-situation": ["互动情况","","/assessment-list"],
    "/questionnaire-new": ["问卷", "", "/assessment-list"],
    "/org-life-view": ["组织生活一览表", ""],
    "/party-member-org-life": ["党员组织生活统计", ""],
    "/channel-registration": ["渠道管理", ""],
    "/channel-generator-url": ["商城链接工具", ""],
    "/report-setting": ["统计报表设置", ""],
    "/party-branch-report": ["党支部组织生活统计", ""],
    "/evaluation-report": ["考核报表", ""],
    "/leading-cadres-report": ["领导干部双重组织生活统计", ""],
    "/payment-count": ["党费交纳完成情况统计", ""],
    "/examination-tasks-list": ["考核任务管理", ""],
    "/examination-task-view": ["考核任务详情", "", "/examination-tasks-list"],
    "/examination-task-add": ["新建考核任务", "", "/examination-tasks-list"],
    // 民主评议
    "/democracy-member": ["民主评议党员", ""],
    "/democracy-statistical": ["民主评议统计", ""],
    // 述职评议
    "/project-review": ["基层组织述职评议", ""],
    "/project-statistical": ["述职评议统计", ""],

    "/execute-task": ["任务执行", ""],
    "/execute-task-detail": ["任务详情", "", "/execute-task"],
    "/audit-task": ["任务审核", ""],
    "/audit-task-detail": ["任务审核详情", "", "/audit-task"],
    "/question-search": ["问题查处", ""],
    "/question-searchAdd": ["新增查处", "", "/question-search"],
    "/evaluation-statistics": ["考核统计", ""],
    "/assessment-org-detail": ["考核统计详情", "", "/evaluation-statistics"],
    "/score-management": ["考核设置", ""],

    "/contribute": ["积分捐赠", "", "/secondaryDirectory/0201"],
    // "/new-contribute": ["发起积分捐赠", "", "/secondaryDirectory/0201"],
    "/edit-contribute": ["编辑积分捐赠", "", "/secondaryDirectory/0201"],
    "/view-contribute": ["查看积分捐赠", "", "/secondaryDirectory/0201"],
    "/party-setting": ["党费设置", ""],
    "/party-pay-statistics": ["收交统计", ""],
    "/party-return": ["党费返还", ""],
    "/dues-return": ["党费返还", ""],
    "/party-history": ["党费返还记录", ""],
    "/party-pay-flow": ["党费交纳流水", ""],
    "/party-normal": ["党费标准", ""],
    "/party-normal/remove-and-repay": ["党费补交/删除设置", ""],
    "/party-normal-edit": ["批量导入交纳基数", "", "/party-normal"],
    // "/fill-meeting-report-condition": ["填报纪实情况表", ""],
    "/fill-meeting-report-condition": ["活动结果填报", ""],
    // "/view-meeting-report-condition": ["查看纪实情况表", ""],
    "/view-meeting-report-condition": ["查看活动结果", ""],
    // "/fill-meeting-report-directly": ["直接填报纪实情况表", ""],
    // "/fill-meeting-report-directly": ["直接填报", ""],
    // "/fill-meeting-report-directly": ["活动纪实（直报）", ""],
    // "/fill-meeting-report-directly": ["活动补录", ""],
    "/fill-meeting-report-directly": ["活动录入", ""],
    // "/meeting-topic-management": ["会议议题管理", ""],
    // "/meeting-topic-management": ["工作任务发布", ""],
    "/meeting-topic-management": ["派发任务", ""],
    // "/addMeetTopic": ["新增会议议题", ""],
    // "/addMeetTopic": ["新增工作任务", ""],
    "/addMeetTopic": ["新增任务", ""],
    // "/editMeetTopic": ["编辑工作任务", ""],
    "/editMeetTopic": ["编辑任务", ""],
    // "/assess-plan-manage": ["考核计划管理", ""],
    // "/assess-plan-manage": ["组织生活设置", ""],
    // "/assess-plan-manage": ["活动类型", ""],
    "/assess-plan-manage": ["活动类型设置", ""],
    // "/add-assess-plan": ["新增考核计划", "", "/assess-plan-manage"],
    // "/add-assess-plan": ["新增组织生活", "", "/assess-plan-manage"],
    // "/add-assess-plan": ["新增纪实管理", "", "/assess-plan-manage"],
    "/add-assess-plan": ["新增活动类型", "", "/assess-plan-manage"],
    "/docu-manage-base-setting": ["基础设置", ""],
    // "/docu-manage-base-setting": ["组织生活设置", ""],
    // "/docu-manage-base-setting": ["组织生活类别", ""],
    // "/topic-type-management": ["会议类型管理", "", "/docu-manage-base-setting"],
    "/topic-type-management": ["活动类型管理", "", "/docu-manage-base-setting"],
    // "/topic-type-group": ["会议类型组合", "", "/docu-manage-base-setting"],
    "/topic-type-group": ["活动类型组合", "", "/docu-manage-base-setting"],
    "/label-management": ["标签管理", "", "/docu-manage-base-setting"],
    "/member-management": ["成员管理", ""],
    // "/task-manage-base-setting": ["任务管理", ""],
    "/task-manage-base-setting": ["任务查看", ""],
    // "/meeting-performance": ["会议完成情况", "", "/task-manage-base-setting"],
    "/meeting-performance": [
      "组织生活完成情况",
      "",
      "/task-manage-base-setting",
    ],
    // "/topic-performance": ["议题完成情况", "", "/task-manage-base-setting"],
    // "/topic-performance": ["上级任务完成情况", "", "/task-manage-base-setting"],
    "/topic-performance": ["我的任务", "", "/task-manage-base-setting"],
    // "/meeting-manage": ["会议管理", ""],
    // "/meeting-manage": ["组织生活管理", ""],
    // "/meeting-manage": ["活动组织", ""],
    "/meeting-manage": ["活动录入", ""],
    "/meeting-create": ["发起活动", ""],
    "/check-plan/fill-meeting-info": ["填报会议纪实情况表", ""],
    // "/check-plan/check-plan-result": ["纪实结果检查", ""],
    // "/check-plan/check-plan-result": ["活动检查", ""],
    "/check-plan/check-plan-result": ["活动回顾详情", ""],
    // "/check-plan": ["纪实结果检查", ""],
    // "/check-plan": ["活动检查", ""],
    "/check-plan": ["活动回顾", ""],
    "/secondaryDirectory/1002": ["智能查询", ""],
    "/integral-exchange": ["积分兑换明细", "", "/secondaryDirectory/1002"],
    "/order-statistics": ["销售清单明细查询", "", "/secondaryDirectory/1002"],
    "/weixin-accounts": ["分级平台公众号列表", "", "/secondaryDirectory/1002"],
    "/secondaryDirectory/1001": ["统计看板", ""],
    "/integral-exchange-statisitics": [
      "积分兑换统计",
      "",
      "/secondaryDirectory/1001",
    ],
    "/news": ["新闻中心", ""],
    "/create-news/1": ["新闻发布", ""],
    "/create-news/2": ["专题资讯发布", ""],
    "/news-column/1": ["新闻栏目", ""],
    "/news-column/2": ["专题栏目管理", ""],
    "/news-matrix": ["新闻矩阵", ""],
    "/edit-news": ["新闻修改"],
    // "/news-management": ["新闻管理", ""],
    "/news-list": ["新闻管理", ""],
    "/informations-list": ["专题资讯管理", ""],
    "/upload": ["上传资源", ""],
    "/upload-resource": ["上传资源", ""],
    // "/unit-information": ["单位信息", ""],
    "/organization-information": ["组织信息", ""],
    // "/office-platform": ["办公平台", "gsg-bangongpingtai"],
    "/office-platform": ["办公中心", ""],
    "/secondaryDirectory/0102": ["审批管理", ""],
    // "/secondaryDirectory/0102": ["协同管理", ""],
    "/new-process": ["发起审批", "", "/secondaryDirectory/0102"],
    "/my-process": ["审批列表", "", "/secondaryDirectory/0102"],
    "/type-process": ["审批类型管理", "", "/secondaryDirectory/0102"],
    // "/new-process": [
    //   "发起审批",
    //   "team",
    //   "/secondaryDirectory/0102",
    //   "/my-process"
    // ],
    // "/my-process": ["审批列表", "team", "/secondaryDirectory/0102"],
    // "/type-process": ["审批类型管理", "team", "/secondaryDirectory/0102"],
    "/secondaryDirectory/0105": ["人员管理", ""],
    "/personnel-manage": ["人员管理", "gsg-guanliyuan"],
    "/root-manage": ["权限角色管理", "gsg-quanxianjiaoseguanli"],
    "/department-manage": ["部门管理", ""],
    "/tag-manage": ["标签管理", ""],
    "/system-manage": ["系统管理员管理", ""],
    "/secondaryDirectory/0109": ["账户设置", ""],
    "/user-info-password": ["密码修改", "", "/secondaryDirectory/0109"],
    // "/user-info-password": [
    //   "密码修改",
    //   "gsg-xiugaimima",
    //   "/secondaryDirectory/0109"
    // ],
    "/user-info-phone": ["手机号修改", "", "/secondaryDirectory/0109"],
    // "/user-info-phone": [
    //   "手机号修改",
    //   "gsg-xiugaishoujihaoma",
    //   "/secondaryDirectory/0109"
    // ],
    // "/activity": ["活动", "gsg-huodong"],
    "/activity": ["线下互动", "gsg-huodong"],
    // "/secondaryDirectory/0201": ["活动发布", ""],
    "/secondaryDirectory/0201": ["互动发布", ""],
    // "/new-vote": ["投票", "gsg-toupiao", "/secondaryDirectory/0201"],
    "/new-vote": ["投票", "", "/secondaryDirectory/0201"],
    "/questionnaire": ["新建问卷", ""],
    // "/questionnaire": [
    //   "问卷调查",
    //   "gsg-wenjuantiaocha",
    //   "/secondaryDirectory/0201"
    // ],
    // "/contest": ["有奖竞答", "gsg-youjiangjingda", "/secondaryDirectory/0201"],
    "/contest": ["有奖竞答", "", "/secondaryDirectory/0201"],
    // "/physical": ["线下活动", "gsg-xianxiahuodong", "/secondaryDirectory/0201"],
    "/physical": ["线下互动", "gsg-xianxiahuodong", "/secondaryDirectory/0201"],
    // "/secondaryDirectory/020104": ["活动", "gsg-huodong"],
    "/secondaryDirectory/020104": ["互动", "gsg-huodong"],
    // "/all-activity": ["活动管理", ""],
    "/all-activity": ["互动管理", ""],
    // "/activity-list": ["活动分类", ""],
    "/activity-list": ["互动分类", ""],
    // "/secondaryDirectory/0204": ["活动奖励", ""],
    "/secondaryDirectory/0204": ["互动奖励", ""],
    "/create-article": ["奖品发布", "", "/secondaryDirectory/0204"],
    // "/create-article": [
    //   "奖品发布",
    //   "gsg-wupinfabu",
    //   "/secondaryDirectory/0204"
    // ],
    // "/article-manage": [
    //   "奖品管理",
    //   "gsg-wupinguanli",
    //   "/secondaryDirectory/0204"
    // ],
    "/article-manage": ["奖品管理", "", "/secondaryDirectory/0204"],
    "/winning-history": ["中奖记录", "", "/secondaryDirectory/0204"],
    // "/winning-history": [
    //   "中奖记录",
    //   "gsg-zhongjiangjilu",
    //   "/secondaryDirectory/0204"
    // ],
    "/prize-delivery": ["奖品发布", "", "/secondaryDirectory/0204"],
    // "/prize-delivery": [
    //     "奖品发布",
    //     "gsg-wupinfahuo",
    //     "/secondaryDirectory/0204"
    // ],
    // "/organize": ["组织数据", ""],
    // "/organize": ["组织管理", ""],
    "/organize": ["组织及党员管理", ""],
    "/organize-data": ["组织人员", "", "/organize-framework"],
    "/organize-framework": ["组织架构", ""],
    "/organize-framework/batch-addition": ["批量新增", ""],
    "/integral-manage": ["积分管理", ""],
    "/office-oa": ["办公OA", ""],
    "/notification-message": ["通知消息", ""],
    "/message-management": ["消息管理", ""],
    "/template-setting": ["模板设置", ""],
    "/release-resources": ["发布资源", ""],
    "/resources-management": ["管理资源", ""],
    "/statistical-board": ["统计看板", ""],
    "/intelligence-inquire": ["智能查询", ""],
    "/statistical-setting": ["统计设置", ""],
    "/execute-task": ["任务执行", ""],

    "/execute-task-detail": ["任务详情", "", "/execute-task"],
    "/collect-data-detail": ["数据采集", "", "/execute-task"],
    "/audit-task": ["任务审核", ""],
    "/audit-task-detail": ["任务审核详情", "", "/audit-task"],
    "/question-search": ["问题查处", ""],
    "/question-searchAdd": ["新增查处单", ""],
    "/score-management": ["考核设置", ""],
    "/authority-management": ["权限管理", ""],
    "/user-group-competence": ["用户组管理", "", "/authority-management"],
    "/role-competence": ["角色管理", "", "/authority-management"],
    "/user-competence": ["用户权限管理", "", "/authority-management"],
    "/version-management": ["升级日志管理", ""],
    "/integral-rule": ["积分兑换设置", ""],
    "/poverty-alleviation-theme": ["扶贫专题页", ""],
    "/baweiyuzhen": ["扶贫主页", ""],
    // "/leader-group": ["领导班子", ""],
    "/leader-group": ["干部信息管理", ""],
    "/org-management": ["班子管理", ""],
    "/add-groupMember": ["添加领导班子成员", "", "/leader-group"],
    "/edit-groupMember": ["编辑领导班子成员", "", "/leader-group"],
    "/operate-groupMember": ["领导班子成员", "", "/leader-group"],
    // "/party-organization": ["党组织委员会", ""],
    "/cadre-system/evaluation-list": ["干部测评", ""],
    // "/add-period": ["添加届次", "", "/party-organization"],
    // "/edit-period": ["编辑届次", "", "/party-organization"],
    "/committee-member": ["委员会成员", "", "/party-organization"],
    "/party-group": ["党小组", ""],
    "/add-partyGroup": ["添加党小组", "", "/party-group"],
    "/edit-partyGroup": ["编辑党小组", "", "/party-group"],
    "/party-groupMember": ["党小组成员", "", "/party-group"],
    "/send-message-record": ["发送记录", ""],
    "/send-message": ["消息发送", ""],
    "/accounts-stylem": ["账户系统", ""],
    "/unlock-user": ["解锁用户", ""],
    "/wangxin-examine-list": ["任务审核", ""],
    "/wangxin-my-task": ["我的任务", ""],
    "/wangxin-distributed-task": ["发布任务", ""],
    "/wangxin-todo-list": ["待办任务", ""],
    "/cadre-information-maintenance": ["干部信息维护", ""],
    "/cadre-management": ["干部信息管理", ""],
    "/praise-commendation": ["表扬表彰", ""],
    "/negative-list": ["负面清单", ""],
    "/appointment-records": ["干部任用记录", ""],
    "/training": ["培训情况", ""],
    "/petition-reporting": ["信访举报情况", ""],
    "/patrol": ["巡视巡察评价", ""],
    "/personal-matters": ["个人事项核查", ""],
    "/marital-status": ["婚姻状况", ""],
    "/overseas-record": ["出国（境）证件记录", ""],
    "/team-visit": ["班子回访", ""],
    "/annual-report": ["年度述职", ""],
    "/take-advice": ["征求意见", ""],
    "/sequence-management": ["干部序列管理", ""],
    "/cadre-system/evaluation-list": ["干部测评", ""],
    "/annual-assessment": ["年度考核", ""],
    "/significant-performance": ["现实表现", ""],
    "/cadre-system/xuncha-list": ["巡察测评", ""],
    "/cadre-system/zhongceng-list": ["中层干部测评", ""],
    "/menu-config": ["菜单管理", ""],
    "/import-page": ["导入", ""],
    "/authority-management/user-competence": ["用户权限管理", ""],
    "/authority-management/role-competence": ["角色管理", ""],
    "/data-initialization": ["数据初始化", ""],
    "/evaluation-management/evaluation-config": ["测评配置", ""],
    "/evaluation-stats/team-eval": ["班子测评统计", ""],
    "/evaluation-stats/primary-eval": ["正职测评统计", ""],
    "/evaluation-stats/deputy-eval": ["副职测评统计", ""],
    "/evaluation-stats/general-eval": ["一般测评统计", ""],
  },
};
