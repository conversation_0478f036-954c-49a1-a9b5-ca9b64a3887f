import React from "react";

import propTypes from "prop-types";
import { Form } from "antd";
import { List } from "immutable";
import formFields from "config/formFields";
import {
  ApprovalProcess, //审批流程
} from "components/activity-form"; //活动表单
import {
  fetchWorkflowList,
  fetchWorkflowItemInfo,
} from "apis/activity";

import CenterContentModal from "components/center-content";

class Approval extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  //审批流程
  approvalProcessHandle() {
    const { state, updateState, form } = this.props;
    const { getFieldValue, setFieldsValue } = form;

    //打开选择人员modal
    const centerContentShow = (type = 0) => {
      updateState({
        centerContentVisble: true,
        centerContentModalType: type
      });
    };

    // 关闭选择人员modal
    const centerContentClose = () => {
      updateState({
        centerContentVisble: false,
        centerContentModalType: 0
      });
    };

    //添加审批/抄送人员
    const centerContentConfirm = result => {
      let staff = {};
      switch (state.centerContentModalType) {
        case 1:
          staff = { approvalStaff: [...state.approvalStaff, ...result] };
          break;
        case 2:
          staff = { CcStaff: [...state.CcStaff, ...result] };
          break;
      }
      let workflowName = "";
      if (state.approvalType.id <= 0) {
        workflowName =
          getFieldValue(formFields.approvalType) ||
          state.approvalType.name;
      }
      updateState({
        centerContentVisble: false,
        approvalType: {
          id: 0,
          name: ""
        },
        ...staff
      });

      //清除选中的审批类型，新增审批人/抄送人员将会新建审批类型
      setFieldsValue({
        [formFields.approvalType]: workflowName
      });
    };

    //删除审批人员
    const approvalStaffDelete = (staff, index) => {
      const approvalStaff = List(state.approvalStaff)
        .delete(index)
        .toJS();
      updateState({ approvalStaff });
    };

    //删除抄送人员
    const CcStaffDelete = (staff, index) => {
      const CcStaff = List(state.CcStaff)
        .delete(index)
        .toJS();
      updateState({ CcStaff });
    };

    //搜索审批类型
    const searchApprovalList = (name) => {
      updateState({
        approvalTypeList: [],
        approvalTypeLoading: true
      }, async () => {
        const page = 1;
        const { data } = await fetchWorkflowList(name, page, 10);
        updateState({
          approvalTypeLoading: false,
          approvalTypeList: data.data
        });
      })
    };

    //选择审批类型
    const selectApprovalType = ({ workflowId, name }) => {
      const approvalType = {
        id: workflowId,
        name
      };
      updateState({
        approvalType
      }, async () => {
        if (workflowId > 0) {
          //更新审批人
          const { data } = await fetchWorkflowItemInfo(workflowId);
          const result = data.data || {};
          const approvalStaff = [];
          (result.users || []).forEach(item => {
            approvalStaff.push({
              name: item.user_name[0],
              user_id: item.user_id[0]
            });
          });
          const CcStaff = [];
          (result.cc || []).forEach(item => {
            CcStaff.push({
              name: item.user_name,
              user_id: item.user_id
            });
          });
          updateState({
            approvalStaff,
            CcStaff
          });
        } else {
          updateState({
            approvalStaff: [],
            CcStaff: []
          });
        }
      });
    };

    return {
      updateState,
      centerContentShow,
      centerContentClose,
      centerContentConfirm,
      approvalStaffDelete,
      CcStaffDelete,
      searchApprovalList,
      selectApprovalType
    };
  }

  render() {
    const { form, isPreview, state } = this.props;
    return (
      <div className="approval-container">
        <ApprovalProcess
          form={form}
          preview={isPreview}
          formFields={formFields}
          activityFormState={state}
          handleEvents={{ ...this.approvalProcessHandle.bind(this)() }}
        />
        <CenterContentModal
          width={800}
          visible={state.centerContentVisble}
          handleOk={
            this.approvalProcessHandle.bind(this)().centerContentConfirm
          }
          handleCancel={
            this.approvalProcessHandle.bind(this)().centerContentClose
          }
          singleSlec={true}
        />
      </div>
    )
  }
}

Approval.propTypes = {
  updateState: propTypes.func,
  isPreview: propTypes.bool.isRequired,
  data: propTypes.object.isRequired,
  state: propTypes.object
};

Approval.defaultProps = {
  updateState: () => { },
  isPreview: false,
  data: {},
  state: {}
};

export default Form.create()(Approval);