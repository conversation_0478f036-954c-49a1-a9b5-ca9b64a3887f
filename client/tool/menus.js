module.exports = {
    /**
     * 处理服务端返回的按钮数据
     * <AUTHOR>
     * @param { Array }      menus        菜单主列表
     * @return [{
			'id': '01',
			'pid': '',
			'name': '角色管理',
			'isLeaf': '2',
			'chilren':[]
			'b_info': [
				{
					'id': '按钮编号',
					'name': '按钮名称',
					'url': 'url'
				}
			]
	 * }]
     */
    handleMenus({list, pid = 'pid', id = 'id', hasNotRelations, relationsKey}) {
        if (!list) return list;

        let _list = list;

        if (_list.length <= 0) return [];

        // 处理按钮
        for (let i = 0; i <= _list.length - 1; i++) {
            for (let j = 0; j <= _list.length - 1; j++) {
                if (hasNotRelations || (relationsKey && Number(_list[i][relationsKey]) === 2 && Number(_list[j][relationsKey]) === 1)) {
                    // 判断是不是当前子节点
                    if (_list[j] && _list[i] && _list[j][pid] === _list[i][id]) {
                        if (_list[i]['children']) {
                            _list[i]['children'].push(_list[j]);
                        } else {
                            _list[i]['children'] = [_list[j]];
                        }
                        _list.splice(j, 1);

                        if (j !== 0) j--;
                    }
                }
            }
        }

        return _list;
    }
}