import React, { PureComponent } from 'react';
import { Cascader } from 'antd';
import { connect } from 'dva';

class ProvincesSelect extends PureComponent {
  constructor(props) {
    super(props);
  }
  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: "provincesModel/initData"
    })
  }
  render() {
    const { attr = {}, label } = this.props;
    const { value } = attr;
    const val = value instanceof Array ? value : undefined;
    return (
      <Cascader
        placeholder={`请选择${label}`}
        {...attr}
        value={val}
        options={this.props.provincesModel.data}
      />
    );
  }
}

export default connect(({ provincesModel }) => ({ provincesModel }))(ProvincesSelect);
