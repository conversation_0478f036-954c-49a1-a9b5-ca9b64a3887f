import React, { useRef, useState, useEffect } from "react";
import "./index.less";
import {
  Form,
  Input,
  Popconfirm,
  Button,
  Table,
  Modal,
  message,
  DatePicker,
  Tooltip
} from "antd";
import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";
import {
  queryTakeAdvice,
  addOrUpdateTakeAdvice,
  deleteTakeAdvice,
  exportTakeAdvice,
  getSupervisionList,
  saveSupervision,
  deleteSupervision,
} from "client/apis/cadre-portrait";
import moment from "moment";
function index({ form, history }) {
  const { TextArea } = Input;
  const { RangePicker } = DatePicker;
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);
  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
  const [supervisionId, setSupervisionId] = useState("");
  useEffect(() => {
    loadData();
  }, []);

  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();

    const params = {
      // ...getFieldsValue(),
      // ...fields,
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.name,
      // start_time: fields.time && fields.time.length > 0 ? fields.time[0].format("YYYY-MM-DD") : "",
      // end_time: fields.time && fields.time.length > 0 ? fields.time[1].format("YYYY-MM-DD") : ""
    };
    if (fields.time && fields.time.length > 0) {
      params.start_time = fields.time[0].format("YYYY-MM-DD");
      params.end_time = fields.time[1].format("YYYY-MM-DD");
    }
    const res = await getSupervisionList(params);
    if (res.data.code === 0) {
      const data = res.data.data.content;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.totalElements,
        };
      });
    } else {
      message.error(res.data.mssage);
    }
    setLoading(false);
  };

  const onChange = (orgs, org) => {
    setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);
    const params = getFieldsValue();
    const res = await exportTakeAdvice({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);
    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=19&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteSupervision({
      pms_supervision_id: record.pms_supervision_id,
    });
    if (res.data.code === 0) {
      loadData();
    } else {
      message.error(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");
    setSupervisionId(record.pms_supervision_id);
    queueMicrotask(() => {
      const _ = { ...record };
      _.user_name = _.user_name ? _.user_name : undefined;
      if (_.supervision_date) {
        _.supervision_date = moment(_.supervision_date, "YYYY-MM-dd");
      } else {
        _.supervision_date = null;
      }
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: 100,
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 150,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "征求意见单位",
      dataIndex: "consultation_org",
      key: "consultation_org",
      width: 150,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "反馈意见情况",
      dataIndex: "condition",
      key: "condition",
      width: 200,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "年度",
      dataIndex: "year",
      key: "year",
      width: 80,
      align: "center",
    },
    {
      title: "征求意见时间",
      dataIndex: "supervision_date",
      key: "supervision_date",
      width: 100,
      align: "center",
    },
    {
      title: "征求科室",
      dataIndex: "section",
      key: "section",
      width: 200,
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据
    setPage(1);
    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name, start_time: _start_time } = values;
        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        if (supervisionId) {
          values.pms_supervision_id = supervisionId;
        }
        if (values.supervision_date) {
          values.supervision_date =
            values.supervision_date.format("YYYY-MM-DD");
        }
        const res = await saveSupervision({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);
          message.success("操作成功");
          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("marriage_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 18 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(<DateSingle placeholder="请选择" type="year" allowClear={false} isWrap={false} />)}
        </Form.Item> */}
        <Form.Item label="年度">
          {getFieldDecorator("year", {
            rules: [{ required: false, message: "请输入" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="征求意见时间">
          {getFieldDecorator("supervision_date", {
            rules: [{ required: true, message: "请选择征求意见时间" }],
          })(<DatePicker placeholder="请选择" type="date" />)}
        </Form.Item>
        <Form.Item label="类型">
          {getFieldDecorator("type", {
            rules: [{ required: false, message: "请输入类型" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="时任职务">
          {getFieldDecorator("job", {
            rules: [{ required: false, message: "请输入时任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="反馈意见情况">
          {getFieldDecorator("condition", {
            rules: [{ required: true, message: "请输入反馈意见情况" }],
          })(<TextArea placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="征求意见单位">
          {getFieldDecorator("consultation_org", {
            rules: [{ required: false, message: "请输入征求意见单位" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="征求科室">
          {getFieldDecorator("section", {
            rules: [{ required: false, message: "请输入征求科室" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="take-advice">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="征求意见时间">
              {getFieldDecorator("time")(<RangePicker />)}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "征求意见"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
