.dpe-tree-select {
  position: relative;
  padding: 3px;
  width: 400px;
  height: 100%;
  transition: all 0.3s;
  // max-height: 825px;
  .main-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    border: 1px solid #edeeee;
    background-color: #f7f8f9;
    overflow: hidden;

    .input-box {
      margin-top: 20px;
      padding: 0 12px;
    }

    .tree-box {
      position: relative;
      flex: 1;
      width: 100%;
      overflow: auto;

      .ant-tree-node-content-wrapper.ant-tree-node-selected {
        color: #fff;
        background-color: rgb(244, 110, 101);
      }
    }
  }
  .collapse-icon {
    position: absolute;
    right: 0;
    top: 45%;
    z-index: 9;
    padding: 10px 20px;
    font-size: 20px;
    color: #ccc;
    cursor: pointer;
    &:hover {
      color: #F46E65;
    }
  }
}
.dpe-tree-collapse {
  width: 0;
  .collapse-icon {
    right: -40px;
    transform: rotate(180deg);
  }
}
