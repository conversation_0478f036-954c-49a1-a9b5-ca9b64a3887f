import {
  fetchArticleList,
  deleteArticle,
  fetchArticleById,
  editArticle,
} from "apis/prize";
import { uploadHost } from "apis/config";
import { message } from "antd";

export default {
  namespace: "articleManage",

  state: {
    //上传文件列表
    fileList: [],
    uploadFileUrl: `${uploadHost}/file/upload`,
    formData: {
      name: "",
      description: "",
      description_app: "",
      first_img: "",
      img_two: "",
      img_three: "",
      img_four: "",
      img_five: "",
    },
    dataList: [],
    title: "物品管理",
    //侧边栏控制符，控制是否折叠
    collapsed: true,
    isHideWrap: false,
    modalTitle: "",
    //当前页，默认为1
    pageNum: 1,
    //每页数据条数，默认为10
    pageSize: 10,
    //总数据条数
    total: 0,
  },

  // 定义业务交互层
  effects: {
    //获取分页列表
    *fetchArticleList({ payload }, { put }) {
      let { pageNum, pageSize } = payload;
      let response = yield fetchArticleList(pageNum, pageSize);
      let { status, data: body } = response;
      if (status === 200) {
        let { code, message, pageNum, pageSize, total, data: dataList } = body;
        if (code === 0 && message === "success") {
          yield put({
            type: "save",
            payload: { pageNum, pageSize, total, dataList },
          });
        }
      }
    },
    //删除物品
    *deleteArticle({ payload }, { put }) {
      let { id } = payload;
      let response = yield deleteArticle(id);
      let { status, data: body } = response;
      if (status === 200) {
        let { code, data } = body;
        if (code === 0) {
          if (data) {
            message.success("删除物品成功");
          } else {
            throw new Error("删除物品失败");
          }
        } else {
          throw new Error(body.message);
        }
      }
    },
    //根据Id获取物品信息
    *fetchArticleById({ payload }, { put }) {
      let { id } = payload;
      let response = yield fetchArticleById(id);
      let { status, data: body } = response;
      if (status === 200) {
        let { code, data } = body;
        if (code === 0) {
          let fileList = [];
          // console.log('获取详情成功', data);
          //更新fileList
          let { first_img, img_two, img_three, img_four, img_five } = data;
          if (first_img && first_img !== "") {
            fileList.push({
              uid: -1,
              url: first_img,
            });
          }
          if (img_two && img_two !== "") {
            fileList.push({
              uid: -2,
              url: img_two,
            });
          }
          if (img_three && img_three !== "") {
            fileList.push({
              uid: -3,
              url: img_three,
            });
          }
          if (img_four && img_four !== "") {
            fileList.push({
              uid: -4,
              url: img_four,
            });
          }
          if (img_five && img_five !== "") {
            fileList.push({
              uid: -5,
              url: img_five,
            });
          }
          yield put({ type: "save", payload: { formData: data, fileList } });

          return data;
        }
      }
    },
    //编辑物品
    *editArticle({ payload }, { put }) {
      let { postObject } = payload;
      let { data } = yield editArticle(postObject);
      if (data.code !== 0) {
        message.error(data.message);
        return false;
      }
      return true;
    },
  },
  reducers: {
    toggleCollapsed(state) {
      //如果当前侧边栏为显示状态，在收起时将表单状态重置
      return { ...state, collapsed: !state.collapsed };
    },
    toggleHideWrap(state) {
      return { ...state, isHideWrap: !state.isHideWrap };
    },
    //改变侧边栏标题文字
    toggleModalTitle(state, params) {
      let { title } = params;
      return { ...state, modalTitle: title || "" };
    },
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};
