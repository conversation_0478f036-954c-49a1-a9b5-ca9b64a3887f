/*
 * @Author: l<PERSON><PERSON><PERSON> 
 * @Date: 2022-02-08 14:43:47 
 * @Last Modified by: ling<PERSON><PERSON>
 * @Last Modified time: 2022-02-16 17:10:50
 */
.data-physical-examination {
  display: flex;
  height: 0px;
  overflow-x: auto;

  .flex-width {
    flex: 1;
  }

  .spin-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(244, 244, 244, 0.7);
    z-index: 888;
  }

  .loading-hidden {
    opacity: 0;
    transition: all ease-in-out 0.3s;
    z-index: -1;
  }
}
