import React, { useRef, useState, useEffect } from "react";
import "./index.less";

import { Form, Input, Popconfirm, Button, Table, Modal, message } from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  queryMajorEventList,
  addOrUpdateMajorEvent,
  deleteMajorEvent,
  exportMajorEvent,
  queryMajorEventDetail,
} from "client/apis/cadre-portrait";
function index({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);

  const [treeData, setTreeData] = useState([]);

  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async ({ page: _page, org_id } = {}) => {
    const params = {
      ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
    };

    const res = await queryMajorEventList(params);

    if (res.data.code === 0) {
      const data = res.data.data.content;

      setDataSource(data);

      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.totalElements,
        };
      });
    } else {
      message.error(res.data.message);
    }

    setLoading(false);
  };

  const loadDataDesc = async (id) => {
    const res = await queryMajorEventDetail({
      performance_id: id,
    });

    if (res.data.code === 0) {
      modalFormRef.current.setFieldsValue(res.data.data);
    }else{
      message.error(res.data.message);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportMajorEvent({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=1&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteMajorEvent({
      performance_id: record.performance_id,
    });

    if (res.data.code === 0) {
      loadData();
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queueMicrotask(() => {
      loadDataDesc(record.performance_id);
      // modalFormRef.current.setFieldsValue(record);
    });
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: 100,
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "current_job",
      key: "current_job",
      width: 200,
      align: "center",
    },
    {
      title: "事件",
      dataIndex: "event",
      key: "event",
      width: 100,
      align: "center",
    },
    {
      title: "简要描述",
      dataIndex: "describe",
      key: "describe",
      align: "center",
      render(_) {
        return (
          <div
            style={{
              textAlign: "left",
            }}
          >
            {_}
          </div>
        );
      },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }

        const res = await addOrUpdateMajorEvent({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, setFieldsValue } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout}>
        {getFieldDecorator("performance_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 20 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="时任职务">
          {getFieldDecorator("current_job", {
            rules: [{ required: true, message: "请输入时任职务" }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="事件">
          {getFieldDecorator("event", {
            rules: [{ required: true, message: "请输入事件" }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="简要描述">
          {getFieldDecorator("describe", {
            rules: [{ required: true, message: "请输入简要描述" }],
          })(<Input />)}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="significant-performance">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "200px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="事件">
              {getFieldDecorator("event")(
                <Input style={{ width: "200px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "现实表现"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
