.select-table {
  margin-bottom: 42px;
  .ant-table-title {
    padding: 0px !important;
  }
  .org-name {
    display: flex;
    justify-content: space-between;
    .org-name-box {
      display: flex;
      align-items: center;
      .name {
        margin-left: 7px;
        font-size: 18px;
        font-family: Source <PERSON>, Source <PERSON>;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        line-height: 21px;
      }
    }
    .org-group-box {
      height: 40px;
    }
  }
  .table-box {
    margin-top: 12px;
  }
  .table-title {
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f3f3f3;
    .left-box {
      text-align: center;
      width: 20%;
      font-size: 16px;
      font-family: Source <PERSON>, Source <PERSON>;
      font-weight: 500;
      color: #000000;
    }
    .right-box {
      width: 20%;
      text-align: center;
      .add-btn {
        font-size: 16px;
        font-family: <PERSON> <PERSON>, Source <PERSON>;
        font-weight: 500;
        color: #f46e65;
        line-height: 24px;
      }
    }
  }
}
