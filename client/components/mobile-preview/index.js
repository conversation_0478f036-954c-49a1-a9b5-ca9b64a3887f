import React, { Component } from 'react';
import { Mobile } from 'apis/config';
import { getParam } from 'client/tool/util';
const bgimg = require('./iphone.png');

import './index.less';

class MobilePreview extends Component {
  render() {
    const { data, src, closePreview } = this.props;
    let strData = 'preview=1';
    try {
      const str = JSON.stringify(data);
      strData += `&previewData=${str}`;
    } catch (error) {
      console.log(error);
    }
    const redirect = `${src}?${strData}`;
    const { region_id } = getParam();
    return (
      <div className="mobile-preview">
        <div className="mark-layout" onClick={closePreview}></div>
        <div
          className="box"
          style={{
            backgroundImage: `url(${bgimg})`,
          }}>
          <iframe
            src={`${Mobile}/ssr/check-page/preview_id/111/${region_id}?org_id=3&redirect=${escape(redirect)}`}
            frameborder="0"
          />
        </div>
      </div>
    );
  }
}
export default MobilePreview;
