import React, { Component } from "react";
import { fileHost, CDN, uploadHost } from "apis/config";
import { UPLOAD_URL } from "../../apis/file";
import { message } from "antd";
import "./index.less";
import PropType from "prop-types";

class Editor extends Component {
  constructor() {
    super();
    this.state = {
      isEditorLoading: false,
      content: "",
    };
    this.handleChange = this._handleChange.bind(this);
    this.pasteinsert = this._pasteinsert.bind(this);
  }

  componentDidMount() {
    this.setState({
      isEditorLoading: true,
    });
    this.Editor = require("react-umeditor-good").default;
  }
  getContent() {
    return this.state.content;
  }
  setContent(text) {
    this.setState({
      content: text,
    });
  }
  focusEditor() {
    this.refs[this.props.refName].focusEditor();
  }
  _handleChange(content) {
    this.setState(
      {
        content: content,
      },
      () => {
        // console.log(content, this.state.content);
        // if (content === this.state.content) {
        //   return;
        // }
        // console.log("content will change");
        this.props.onChange(content);
      }
    );
  }
  _pasteinsert(text) {
    document.execCommand("inserthtml", false, text);
  }
  render() {
    const Editor = this.Editor || null;
    const self = this;
    return (
      <div className={"editor"}>
        {this.state.isEditorLoading ? (
          <Editor
            onPaste={(e) => {
              var _data = e.clipboardData.getData("text/plain") || "";
              var text = _data
                .split("\n")
                .map((v) => {
                  return `<p>${v}</p>`;
                })
                .join("");
              self.pasteinsert(text);
              e.preventDefault();
              return false;
            }}
            onRef={(ref) => {
              this.props.refName = ref;
            }}
            icons={[
              "paragraph",
              " | ",
              "justifyleft justifycenter justifyright  ",
              " image | link unlink",
            ]}
            value={this.state.content}
            defaultValue={this.props.defaultValue}
            onChange={this.handleChange}
            onInput={this.props.onInput}
            height={this.props.height}
            readOnly={this.props.readonly}
            plugins={{
              image: {
                uploader: {
                  name: "upfile",
                  url: UPLOAD_URL + "/file/upload",
                  data: {
                    upType: this.props.upType,
                    user_id:
                      window.sessionStorage.getItem("_uid") != null
                        ? window.sessionStorage.getItem("_uid")
                        : "-1",

                    user_name:
                      window.sessionStorage.getItem("_un") != null
                        ? window.sessionStorage.getItem("_un")
                        : "1",
                  },
                  onError: () => {
                    message.error("网络异常");
                  },
                  filter: (e) => {
                    if (e.data) {
                      if (this.props.cdn) {
                        return `${CDN}/${e.data[0].path}`;
                      } else {
                        return `${UPLOAD_URL}/file/download/${e.data[0].name}`;
                      }
                    }
                    message.error(e.message);
                    return null;
                  },
                },
              },
            }}
          />
        ) : null}
      </div>
    );
  }
}
Editor.PropType = {
  onChange: PropType.func,
  onFocus: PropType.func,
  onInput: PropType.func,
  defaultValue: PropType.string,
  ref: PropType.string,
  readonly: PropType.bool,
  height: PropType.number,
  // 暴露给外层的值
  getContent: PropType.func,
  focusEditor: PropType.func,
  upType: PropType.string,
  cdn: PropType.bool,
};
Editor.defaultProps = {
  onChange: () => {},
  onFocus: () => {},
  onInput: () => {},
  refName: "refName23",
  height: 200,
  upType: "image",
  cdn: false,
};
export default Editor;
