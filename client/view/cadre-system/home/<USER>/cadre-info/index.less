.cadre-info {
  display: flex;
  width: 100%;
  height: 100%;
  .left {
    width: 240px;
    height: 100%;
    background-color: #fff;
    overflow-y: auto;
  }
  .right {
    padding: 38px 50px;
    margin-left: 20px;
    flex: 1;
    background-color: #fff;
    overflow-y: auto;
    .user-box {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .card {
        display: flex;
        align-items: center;
        padding: 24px;
        margin-right: 20px;
        width: 306px;
        height: 166px;
        background: #f9f9f9;
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        .avatar {
          width: 89px;
          height: 118px;
          background: #c4c4c4;
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .position-box {
          margin-left: 32px;
          flex: 1;
          text-align: left;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .name {
            margin-bottom: 16px;
            font-size: 22px;
            font-family: PingFang SC-Heavy, PingFang SC;
            font-weight: 800;
            color: #000000;
            line-height: 26px;
          }
          .position-name {
            font-size: 16px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
          }
        }
      }
    }
  }
}
