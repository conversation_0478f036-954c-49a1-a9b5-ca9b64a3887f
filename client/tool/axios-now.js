import axios from "axios";
const querystring = require('querystring'),
    qs = require('qs'),
    { notification } = require('antd'),
    getCsrf = () => {
        return typeof window !== 'undefined' ? window.sessionStorage.getItem('csrf') : '';
    },
    getType = () => {
        if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem('_type') != null ? window.sessionStorage.getItem('_type') : '0';
        } else {
            return '0';
        }
    },
    getOid = () => {
        if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem('_oid') != null ? window.sessionStorage.getItem('_oid') : '1';
        } else {
            return '1';
        }
    },
    getToken = () => {
        if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem('_tk') != null ? window.sessionStorage.getItem('_tk') : '-1';
        } else {
            return '-1';
        }
    },
    getUserId = () => {
        if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem('_uid') != null ? window.sessionStorage.getItem('_uid') : '-1';
        } else {
            return '-1';
        }
    },
    getName = () => {
        if (typeof window !== 'undefined') {
            return window.sessionStorage.getItem('_un') != null ? window.sessionStorage.getItem('_un') : '-1';
        } else {
            return '-1';
        }
    };

const os = axios.create({
    baseURL: '/',
    // 请求之后处理的数据
    transformResponse: [function (data) {
        return data;
    }],
    // 响应数据格式
    responseType: 'json',
    // 超时处理  开发环境忽略超时
    timeout: process.env.IS_DEV ? 3000000 : 8000
});
const as = axios.create({
    // baseURL: 'http://127.0.0.1:7001/api/',
    // baseURL: '/',

    /*// 请求前处理数据
    transformRequest: [ function ( data, headers ) {
        return qs.stringify( data );
    } ],*/
    withCredentials: true,
    headers: {
        'x-csrf-token': getCsrf(),
        'Access-Control-Allow-Credentials': 'true',
        'Content-Type': 'application/json',
        '_tk': '-1',
        '_uid': '-1',
        '_un': '-1',
        '_type': '0',
        '_oid': '1'
    },

    // 请求之后处理的数据
    transformResponse: [function (data) {
        return data;
    }],

    // 响应数据格式
    responseType: 'json',

    // 超时处理  开发环境忽略超时
    timeout: process.env.IS_DEV ? 3000000 : 8000

}),
    handleRequest = ({ type = 'get', url, option = {}, data = {}, t }) => {
        try {
            if (process.env.IS_DEV) {
                console.group('------------API------------')
                console.log('method:', type);
                console.log('path:', url);
                console.log('body:', data);
                console.groupEnd();
            }

            // const headers = {
            //     'x-csrf-token': getCsrf(),
            //     'Access-Control-Allow-Credentials': 'true',
            //     'Content-Type': 'application/json',
            //     '_tk': getToken(),
            //     '_uid': getUserId(),
            //     '_un': getName(),
            //     '_type': getType(),
            //     '_oid':getOid()
            // }
            //
            // option.headers =headers

            switch (type) {
                case 'post':
                    if (t) {
                        console.log(url, data, option);
                        return os.post(url, data, option);
                    } else {
                        return as.post(url, data, option);
                    }
                case 'delete':
                    if (t) {
                        return os.delete(url, data, option)
                    } else {
                        return as.delete(url, data, option);
                    }
                case 'get':
                default:
                    if (t) {
                        return os.get(url, { params: option });
                    } else {
                        return as.get(url, { params: option });
                    }
            }

        } catch (err) {
            notification.error({
                message: '请求错误',
                description: err
            });
            console.error(err);
        }
    };


module.exports = {
    // node端的  请求
    n_http: {
        get(url, option = {}, t = false) {
            return handleRequest({ url, option, t });
        },
        post(url, data = {}, option = {}, t = false) {
            return handleRequest({
                url,
                option,
                type: 'post',
                data: querystring(data),
                t
            });
        }
    },
    // 浏览器端的请求
    http: {
        get(url, option = {}, t = false) {
            return handleRequest({ url, option, t });
        },
        post(url, data = {}, option = {}, t = false) {
            return handleRequest({
                url,
                option,
                data,
                type: 'post',
                t
            });
        },
        delete(url, data = {}, option = {}, t = false) {
            return handleRequest({
                url,
                option,
                data,
                type: 'delete',
                t
            });
        }
    },
    staticHttp: {
        post(url, data = {}, option = {}) {
            return axios.post(url, data, {
                headers: option
            })
        }
    }
}
