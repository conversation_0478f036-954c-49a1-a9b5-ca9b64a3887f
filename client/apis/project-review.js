/**
 * 述职评议接口
 */
import { http } from "client/tool/axios";
import { mtHost, fileHost } from './config';
/**
 * 获取基层组织述职评议列表
 */
export const getProjectReviewList = (data = {}) => {
  return http.post(`${mtHost}/org/debrief/review/list`, data);
}
/**
 * 录入述职评议
 */
export const entryProjectReview = (data = {}) => {
  return http.post(`${mtHost}/org/debrief/review/add`, data);
}
/**
 * 修改述职评议
 */
export const editProjectReview = (data = {}) => {
  return http.post(`${mtHost}/org/debrief/review/edit`, data);
}
/**
 * 查看述职评议结果
 */
export const viewProjectReview = (data = {}) => {
  return http.post(`${mtHost}/org/debrief/review/detail`, data);
}
/**
 * 删除述职评议结果
 */
export const delProjectReview = (data = {}) => {
  return http.post(`${mtHost}/org/debrief/review/delete`, data);
}