.question-info {

  .ant-row,
  .ant-col-10 {
    display: flex !important;
  }

  .ant-form-item-label {
    min-width: 102px !important;

    &>label {
      margin-right: 13px;
      font-size: 16px;
      font-family: Source <PERSON>;
      font-weight: 400;
      color: #515151;
    }
  }

  .ant-form-item-children {
    .value {
      font-size: 16px;
      font-family: Source <PERSON> Sans CN;
      font-weight: 500;
      color: #070707;
      width: 705px;
      word-break:break-all;
    }
  }

  .options {
    display: flex;
    align-items: center;

    .l-01 {
      margin-right: 34px;
      min-width: 12px;
      font-size: 16px;
      font-family: Source <PERSON> Sans CN;
      font-weight: 500;
      color: #070707;
    }

    .l_02 {
      width: 567px !important;
      border: 1px solid #DDDDDD;
      border-radius: 4px;
    }

    .on{
      color: #4A81FF !important;
    }

    .l_03{
      color: #BDBDBD;
      margin: 0 34px;
      font-size: 16px;
    }

    .l_04{
      color: #BDBDBD;
      font-size: 16px;
    }

    .l_05{
      color: #BDBDBD;
      font-size: 16px;
      margin-left: 34px;
    }
  }

  .ant-select-selection{
    width: 250px !important;
    border-radius: 4px;
  }

  .btn{
    margin-top: 106px;
  }

  .ant-input[disabled], .ant-select-disabled {
    border: none !important;
    padding: 0 !important;
    background-color: #FFF;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #070707;
    cursor: text;
    .ant-select-selection{
      border: none !important;
      padding: 0 !important;
      background-color: #FFF;

      .ant-select-selection__rendered{
        margin: 0 !important;
      }
    }
  }
}