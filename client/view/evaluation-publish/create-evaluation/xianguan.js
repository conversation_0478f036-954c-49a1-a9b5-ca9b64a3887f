import moment from "moment";

import SearchHeader from "client/components/search-header";
import XianguanTable from "./XianguanTable";
import AddPersonModal from "./components/AddPersonModal";
import OrganizeSelector from "./components/OrganizeSelector";

import {
  initiateEvaluation,
  getEvaluation,
} from "client/apis/evaluation-publish";

const { Form, message } = Ant;

export default Form.create()(function CreateXianguan(props) {
  const { history, form, type, orgId, evaluationId } = props;

  const { getFieldDecorator, validateFields, setFieldsValue } = form;

  const [addVisible, setAddVisible] = React.useState(false);
  const [dataSource, setDataSource] = React.useState([]);

  React.useEffect(() => {
    if (evaluationId) {
      getEvaluation(evaluationId).then((res) => {
        if (res.data.code === 0) {
          setFieldsValue({
            end_time: moment(res.data.data.end_time),
            eval_name: res.data.data.eval_name,
          });
          setDataSource(res.data.data.org_user);
        }
      });
    }
  }, [evaluationId]);

  const appendData = (list) => {
    const maxSeq = dataSource.reduce((acc, cur) => {
      return Math.max(acc, cur.seq || 1);
    }, 1);
    const source = list
      .filter(
        (item) => (type === "8" ? item.user_type != "5" : item.user_type == "5") // 筛选对应县管/中层
      )
      .filter((item) => dataSource.every((cur) => cur.user_id != item.user_id))
      .map((item, i) => ({
        ...item,
        group: type === "8" ? (item.user_type == "1" ? 1 : 2) : 2,
        enable: 1,
        seq: maxSeq + i,
      }));
    console.log("source", source);
    setDataSource([...dataSource, ...source]);
  };

  const handlePublish = async () => {
    validateFields((err, values) => {
      if (!err) {
        const params = {
          end_time: values.end_time.format("YYYY-MM-DD HH:mm:ss"),
          eval_name: values.eval_name,
          evaluation_id: evaluationId,
          org_id: Number(orgId),
          org_user: dataSource.map((item) => ({
            ...item,
            org_id: Number(orgId),
            evaluation_id: evaluationId,
          })),
          // pattern_sub: 0,
          // 县管8，中层9
          type,
        };
        initiateEvaluation(params).then((res) => {
          console.log(res);
          if (res.data.code === 0) {
            message.success(res.data.data);
            history.goBack();
          } else {
            message.error(res.data.message);
          }
        });
      } else {
        message.error("您还有未填写的信息！");
      }
    });
  };

  return (
    <div>
      <SearchHeader
        title={`${type === "8" ? "班子成员" : "一般干部"} - 发起测评`}
        onBack={() => {
          history.goBack();
        }}
      />

      <div style={{ padding: 24 }}>
        <Form layout="inline">
          <Form.Item label="测评名称">
            {getFieldDecorator("eval_name", {
              rules: [{ required: true, message: "请输入测评名称" }],
            })(<Ant.Input placeholder="请输入" style={{ width: 400 }} />)}
          </Form.Item>
          <Form.Item label="测评截止日期">
            {getFieldDecorator("end_time", {
              rules: [{ required: true, message: "请选择测评截止日期" }],
            })(<Ant.DatePicker style={{ width: 400 }} />)}
          </Form.Item>
        </Form>
        <Ant.Row
          gutter={8}
          type="flex"
          align="middle"
          style={{ margin: "16px 0" }}
        >
          <div style={{ fontSize: 18, fontWeight: 600, color: "#333" }}>
            测评对象（被测评人）
          </div>
          <OrganizeSelector onOk={appendData} />
          <Ant.Button
            style={{ borderColor: "#47b9ff", color: "#47b9ff" }}
            onClick={() => setAddVisible(true)}
          >
            <Ant.Icon type="user-add" />
            添加人员
          </Ant.Button>
        </Ant.Row>
        <div style={{ marginBottom: 20, color: "#D80C0C" }}>
          注：选择是选择机构后，自动添加该机构下的县管干部。添加人员，支持手动添加其他人员。
        </div>
        <XianguanTable
          type={type}
          dataSource={dataSource}
          onUpdateData={(data) => setDataSource(data)}
        />
        <Ant.Row type="flex" justify="center" style={{ margin: "32px 0" }}>
          <Ant.Button
            type="primary"
            style={{ padding: "0 43px" }}
            onClick={handlePublish}
          >
            发布
          </Ant.Button>
        </Ant.Row>
      </div>

      <AddPersonModal
        dataSource={dataSource}
        type={type}
        visible={addVisible}
        onOk={(val) => {
          if (dataSource.some((item) => item.user_id == val.user_id)) {
            message.info("该人员已存在");
            return;
          }
          setDataSource([...dataSource, val]);
          setAddVisible(false);
        }}
        onCancel={() => setAddVisible(false)}
      />
    </div>
  );
});
