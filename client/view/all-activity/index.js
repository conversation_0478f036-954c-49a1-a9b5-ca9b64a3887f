import React, { Component } from 'react';
import IndexView from './index-view';
import { connect } from 'dva';
import { Form, message } from 'antd/lib/index';
import OverviewModal from 'client/components/overview-modal';
import NoticeModal from './notice-modal';
import PushTo from '../activitys-matrix/components/push-to';
import './index.less';
import { activityPush } from "apis/activity";

//类型下拉列表
const typelist = [{
    key: -1,
    title: '全部',
    name: 'all'
}, {
    key: 1,
    title: '投票',
    name: 'vote'
}, {
    key: 2,
    title: '问卷调查',
    name: 'survey'
}, {
    key: 3,
    title: '有奖竞答',
    name: 'answer'
}, {
    key: 4,
    // title: '线下活动',
    title: '线下互动',
    name: 'active'
}, {
    key: 5,
    title: '积分捐赠',
    name: 'donate'
}
];
//状态下拉列表
const statelist = [{
    key: -1,
    title: '全部',
    name: 'all'
}, {
    key: 1,
    title: '活动中',
    name: 'pending'
}, {
    key: 2,
    title: '审批中',
    name: 'activing'
}, {
    key: 3,
    title: '未通过',
    name: 'nopass'
}, {
    key: 4,
    title: '已结束',
    name: 'end'
}, {
    key: 5,
    title: '未开始',
    name: 'beforestart'
}, {
    key: 6,
    title: '草稿',
    name: 'draft'
}];

let isSport = false;

// 切换边栏显示或者消失
const toggleSide = (dispatch, isShow = false) => {
    if (isShow) {
        // 切换外壳显示动画
        dispatch({ type: 'allActivity/toggleHideWrap' });
        setTimeout(() => {
            dispatch({ type: 'allActivity/toggleCollapsed' });
        }, 0);
    } else {
        // 关闭的时候 等待动画完毕的时候执行隐藏外壳
        dispatch({ type: 'allActivity/toggleCollapsed' });
        setTimeout(() => {
            isSport = false;
            dispatch({ type: 'allActivity/toggleHideWrap' });
        }, 300);
    }
};

class AllActivity extends Component {
    constructor(props) {
        super(props);
        this.state = {
            //分页判断是否查询了
            isOldSearch: null,
            //选择的活动
            selectActive: [],
            //默认没有返回按钮，如果从栏目跳转过来，则给与一个返回按钮
            onBack: undefined,
            disabledList:[],
            current: undefined,
            pushtoDialogVisible: false,//推送的modal
            pushing: false,
            pushtoDialogLoading: false,
            rangeType:1,//推送范围
            visibleType:1
        };
    }

    componentWillUnmount() {
        const { dispatch } = this.props;
        dispatch({
            type: "allActivity/resetFormParams"
        });
    }

    async componentDidMount() {
        const { dispatch, location, form } = this.props,
            { setFieldsValue } = form,
            { query } = location;
        await dispatch({ type: 'allActivity/getColumn' });
        let activity_column_id = '';
        if (query) {
            activity_column_id = query.activity_column_id;
            const { onBack } = query;
            setFieldsValue({
                cid: activity_column_id
            });
            this.setState({
                onBack: onBack
            })
        }

        if (!activity_column_id || activity_column_id.length === 0) {
            //没有活动栏目id时，加载所有活动
            await dispatch({ type: 'allActivity/loadAllActivity', payload: { page: 1 } });
        } else {
            this.setState({
                isOldSearch: {
                    cid: activity_column_id[activity_column_id.length - 1]
                }
            });
            await dispatch({
                type: 'allActivity/searchActivity',
                payload: {
                    page: 1,
                    cid: activity_column_id[activity_column_id.length - 1]
                }
            });
        }

    }

    TypeChange(value) {
        const { setFieldsValue } = this.props.form;
        setFieldsValue({
            typeselect: value
        });
    }

    //改变状态下拉框选择
    stateChange(value) {
        const { setFieldsValue } = this.props.form;
        setFieldsValue({
            stateselect: value
        });
    }

    //改变栏目下拉框选择
    crownChange(value) {
        // console.log(value);
        // const { setFieldsValue } = this.props.form;
        // setFieldsValue({'cid':value[value.length-1]});
    }

    async searchSubmit(e) {
        const { dispatch } = this.props;
        const { validateFields } = this.props.form;
        e.preventDefault();
        const _this = this;
        validateFields(async (err, values) => {
            if (!err) {

                values.cid ? values.cid = values.cid[values.cid.length - 1] : undefined;
                values.type == -1 ? values.type = undefined : '';
                values.status == -1 ? values.status = undefined : '';
                values.cid == -1 ? values.cid = undefined : '';
                values.keyword == '' ? values.keyword = undefined : '';
                values.pm == '0' ? values.pm = undefined : '';


                const rangeValue = values.timepoint ? values.timepoint.format('YYYY-MM-DD HH:mm:ss') : undefined;
                _this.setState({
                    isOldSearch: {
                        type: values.type,
                        status: values.status,
                        cid: values.cid,
                        keyword: values.keyword,
                        pm: values.pm,
                        timepoint: rangeValue
                    }
                });
                await dispatch({
                    type: 'allActivity/searchActivity',
                    payload: {
                        page: 1,
                        type: values.type,
                        status: values.status,
                        cid: values.cid,
                        keyword: values.keyword,
                        pm: values.pm,
                        timepoint: rangeValue
                    }
                });
            }
        })
    }

    //删除活动
    async deleteActive(row) {
        let currentUserId = window ? parseInt(window.sessionStorage.getItem('_uid')) : '';
        if (currentUserId !== row.create_user) {
            message.error('当前活动不是你发起的，无法删除');
            return;
        }
        const { dispatch, allActivity } = this.props,
            { pageNew } = allActivity;

        let type = 'allActivity/deleteActive';

        if (row.status === 6) {
            type = 'allActivity/deleteDraft';
        }
        await dispatch({
            type,
            payload: {
                id: row.activity_id,
                type: row.type
            }
        }).then(
            () => {
                message.success('删除成功');
                dispatch({ type: 'allActivity/loadAllActivity', payload: { page: pageNew.pageNum } });
            }
        ).catch(
            (error) => {
                console.error(error.message);
            }
        );
    }

    //改变页码
    async changePage(page) {
        const { dispatch, form } = this.props,
            { validateFields, getFieldValue } = form;
        await dispatch({
            type: 'allActivity/setPage',
            payload: { pageNum: page.current }
        });
        // console.log(page);
        if (this.state.isOldSearch) {
            // console.log('insert');
            const values = this.state.isOldSearch;
            await dispatch({
                type: 'allActivity/searchActivity',
                payload: {
                    page: page.current,
                    type: values.type,
                    status: values.status,
                    cid: values.cid,
                    keyword: values.keyword,
                    pm: values.pm,
                    timepoint: values.timepoint
                }
            });
        } else {
            await dispatch({
                type: 'allActivity/loadAllActivity',
                payload: { page: page.current }
            });
        }
    }

    //移动活动
    async moveActive() {
        const { dispatch, form } = this.props,
            { getFieldValue } = form;
        let cid = getFieldValue('moveactive');
        if (this.state.selectActive.length === 0) {
            message.error('请选择所移动的活动');
        } else if (getFieldValue('moveactive') == undefined) {
            message.error('请选择所要移动到的栏目');
        } else {
            cid ? cid = cid[cid.length - 1] : cid = '';
            await dispatch({
                type: 'allActivity/moveActive',
                payload: { aids: this.state.selectActive, cid: cid }
            });
        }


    }

    //选择activity
    saveSelectActivity(activeList) {
        const active = [];
        for (let val of activeList) {
            active.push(val.activity_id);
        }
        this.setState({
            selectActive: active
        })
    }

    //推送活动
    pushto(data) {
        const self = this;
        const { dispatch } = this.props;
        const { currentData, rangeType, visibleType, selectedData } = data;
        // console.log('data---',data)
        // console.log(currentData, rangeType, visibleType,  selectedData)
        this.setState({
            pushtoDialogLoading: true
        });
        this.setState({
            disabledList:[]
        })
        dispatch({
            type: "activitysMatrix/pushto",
            payload: {
                activity_id: currentData.activity_id,
                action: 1,  //1：发起/修改 2：撤回
                is_org: rangeType,
                orgs: selectedData, //selectedData.map(org=>org.org_id),
                visibility: visibleType //1：所有组织可见；2：接收组织可见
            }
        }).catch(e => {
            message.error(e);
        }).finally(() => {
            self.setState({
                pushtoDialogVisible: false,
                pushtoDialogLoading: false
            });
            message.success("推送成功");
        })

    }

    render() {
        const {pushtoDialogVisible} =this.state;
        const { allActivity, activitysMatrix, dispatch, userInfo } = this.props,
            { dataList, listLoading, crownList, pageNew, modalVisible, url,
                currentActivityInfo,
                isHideWrap,
                collapsed,
                submitButtonLoading } = allActivity,
            _app = {
                userInfo,
                onBack: this.state.onBack,
                dispatch,
                props: this.props,
                typelist,
                listLoading,
                statelist,
                crownList,
                dataList,
                pageNew,
                //改变类型下拉框选择
                TypeChange: this.TypeChange.bind(this),
                //改变状态下拉框选择
                stateChange: this.stateChange.bind(this),
                //改变栏目下拉框选择
                crownChange: this.crownChange.bind(this),
                searchSubmit: this.searchSubmit.bind(this),
                deleteActive: this.deleteActive.bind(this),
                changePage: this.changePage.bind(this),
                moveActive: this.moveActive.bind(this),
                saveSelectActivity: this.saveSelectActivity.bind(this),
                showNotice: (row) => {
                    toggleSide(dispatch, true);
                    dispatch({
                        type: 'allActivity/save',
                        payload: {
                            currentActivityInfo: row
                        }
                    });
                },
                // todo: 设置推送的modal的visible
                 showPushtoDialog: ((row) => {
                    activityPush(row.activity_id, { is_page: 0 })
                    .then(data => {
                        const _data = data.data
                        if (_data.code != 0) {
                            message.error(_data.message)
                        }
                        const { orgs: orgIds,visibility,org } = _data.data;
                        // console.log('orgIds---', orgIds)
                        // console.log('this--',this)
                        this.setState({
                            current: row,
                            disabledList: orgIds || [],
                            pushtoDialogVisible: true,
                            rangeType: org>>0,
                            visibleType:visibility
                            //推送配置信息保存到state，方便传入到pushTo组件
                            // rangeType: org>>0,  //推送范围 是否推送给指定组织 false=>0 true => 1
                            // visibleType: visibility //可见性 1：所有组织可见；2：接收组织可见
                        })

                    })
                    // this.setState({
                    //     current: row,
                    //     pushtoDialogVisible: true
                    // })
                }).bind(this)
            };


        const pushToProps = {
            title: "活动",
            currentData: this.state.current,
            visible: this.state.pushtoDialogVisible,
            isLoading: this.state.pushtoDialogLoading,
            onSave: this.pushto.bind(this),
            disabledList:this.state.disabledList,
            pushing: activitysMatrix.pushing,
            rangeType:this.state.rangeType,
            visibleType:this.state.visibleType,
            onCancel: () => this.setState({
                pushtoDialogLoading: false,
                pushtoDialogVisible: false
            })
        }

        const overviewModalProps = {
            currentActivityInfo,
            modalVisible,
            closeHandler(e) {
                // console.log('关闭预览框');
                dispatch({
                    type: 'allActivity/save',
                    payload: {
                        modalVisible: false
                    }
                })
            },
            url
        };
        const noticeModalProps = {
            currentActivityInfo,
            isHideWrap,
            collapsed,
            //控制提交按钮
            submitButtonLoading,
            closeHandler(e) {
                // console.log(e);
                if (isSport) return;
                isSport = true;
                toggleSide(dispatch, false);
                dispatch({
                    type: 'allActivity/save',
                    payload: {
                        submitButtonLoading: false,
                        currentActivityInfo: {}
                    }
                });
            },
            onSubmit(values) {
                // console.log(values);
                dispatch({
                    type: 'allActivity/save',
                    payload: {
                        submitButtonLoading: true
                    }
                });
                dispatch({
                    type: 'allActivity/sendNotice',
                    payload: {
                        postObject: values
                    }
                }).then(
                    (data) => {
                        message.success('发送成功');
                        noticeModalProps.closeHandler();
                    }
                ).catch(
                    (error) => {
                        console.error(error);
                    }
                );
            }
        }
        return (
            <div className={'all-activity'}>
                <IndexView {..._app} />
                {
                    pushtoDialogVisible ? <PushTo {...pushToProps} />:null
                }

                <OverviewModal {...overviewModalProps} />
                <NoticeModal {...noticeModalProps} />
            </div>
        )
    }
}

const mapStateToProps = state => ({
    allActivity: state.allActivity,
    activitysMatrix: state.activitysMatrix,
    userInfo: state.userInfo
});


export default connect(
    mapStateToProps
)(Form.create()(AllActivity));
