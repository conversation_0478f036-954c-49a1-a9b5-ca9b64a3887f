import React, { Component } from 'react';
import { Icon, Divider } from 'antd';
import SelfIcon from "components/self-icon";
import './index.less';

/**
 * 头部标题区外壳
 */
export default function ({ children, title = '首页', style = {}, onBack, renderRight }) {
	return (
		<div className="search-header-wrap" style={{ ...style, fontWeight: "bold" }}>
			{children ? children : (
				<div className="header">
					<div>
						{onBack && (
							<span>
								<a className="search-header-back" onClick={() => {
									onBack();
								}}>
									<SelfIcon type="gsg-fanhui" /> &nbsp;
									返回
								</a>
								<Divider type="vertical" />
								
							</span>
						)}
						<span className="title">{title}</span>
					</div>
					{
						renderRight && renderRight()
					}
				</div>
			)}
		</div>
	)
}
