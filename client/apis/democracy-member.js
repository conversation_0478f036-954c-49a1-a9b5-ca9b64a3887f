/**
 * 民主评议接口
 */
import { http } from "client/tool/axios";
import { mtHost } from './config';
/**
 * 获取民主评议党员列表
 */
export const getDemocracyMemberList = (data = {}) => {
  return http.post(`${mtHost}/comment/user/query`, data);
}
/**
 * 录入评议结果
 */
export const entryDemocracy = (data = {}) => {
  return http.post(`${mtHost}/comment/user/insert`, data);
}
/**
 * 修改评议结果
 */
export const editEntryDemocracy = (data = {}) => {
  return http.post(`${mtHost}/comment/user/update`, data);
}
/**
 * 评议评议详情
 */
export const viewEntryDemocracy = (data = {}) => {
  return http.get(`${mtHost}/comment/user/select`, data);
}
/**
 * 删除评议
 */
export const delEntryDemocracy = (data = {}) => {
  return http.get(`${mtHost}/comment/user/del`, data);
}
/**
 * 评议导出生成文件
 */
export const createFile = (data = {}) => {
  return http.post(`${mtHost}/comment/user/start_export`, data);
}
/**
 * 查询是否生成文件
 */
export const queryisCreateFile = (data = {}) => {
  return http.get(`${mtHost}/comment/user/is_export`, data);
}

/**
 * 评议导出
 */
export const exportFile = (data = {}) => {
  return http.get(`${mtHost}/comment/user/export`, data);
}




