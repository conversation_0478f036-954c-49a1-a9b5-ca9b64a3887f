import React, { Component } from 'react';
import './index.less';
import LoadingModal from '../loading-modal';
import { message, Icon } from 'antd';
import PropTypes from 'prop-types';

class LoadingImage extends Component {
    constructor(prop) {
        super(prop);
        this.state = {
            modalVisible: true
        };
        this.loadSuccess = this.loadSuccess.bind(this);
        this.loadError = this.loadError.bind(this);
    }

    //图片加载成功
    loadSuccess() {
        // message.success('图片加载成功');
        this.setState({
            modalVisible: false
        });
    }

    //图片加载失败
    loadError() {
        const { url } = this.props;
        message.error(url + '资源失效，加载失败');
        this.setState({
            modalVisible: false
        });
    }

    render() {
        // console.log(this.props);
        const { removeHandler, index } = this.props;
        return (
            <div className={'loading-image'}>
                <div className="image-wrapper">
                    {this.state.loadSuccess}
                    {
                        typeof removeHandler === 'function' && <div className={'remove-modal'}>
                            <Icon type="delete" className={'remove-button'} onClick={() => removeHandler(index)} />
                        </div>
                    }
                    {/*如果参数传入了removeHandler，则需要添加一个额外的模态框，用于显示删除按钮*/}
                    <img src={this.props.url} alt="" onLoad={this.loadSuccess} onError={this.loadError} />
                </div>
                <LoadingModal modalVisible={this.state.modalVisible} />
            </div>
        )
    }
}

LoadingImage.propTypes = {
    //图片url
    url: PropTypes.string,
    //删除图片处理方法
    removeHandler: PropTypes.func,
    //图片队列index
    index: PropTypes.number
};

export default LoadingImage;
