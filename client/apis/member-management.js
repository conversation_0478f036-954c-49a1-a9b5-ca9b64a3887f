import { ucHost } from "./config";
import { http, headers } from "client/tool/axios";
import axios from "axios";

// 400020216 - 下载用户导入模板
export const excelTemplate = (org_id, field_ids) => {
  return http.get(`${ucHost}/org/user/excelTemplate`, { params: {org_id, field_ids} });
}

// 400020214 - 人员上传进度查询
export const getImportRate = (uuid) => {
  return http.get(`${ucHost}/org/user/import-rate`,  { uuid })
}

// 400020215 - 下载失败列表
export const getImportFail = (uuid) => {
  return http.get(`${ucHost}/org/user/import-fail`,{ uuid})
}

// 400020214 - 人员上传进度查询
export const uploadUserFile = (formData, org_id) => {
  return http.post(`${ucHost}/org/user/import`, formData)
}