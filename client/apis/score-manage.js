const { http } = require('client/tool/axios');
import { evalHost } from './config';
import axios from 'axios';

//104001000801-添加分组

export const addGroupUrl = (data = {}) => {
    return http.post(`${evalHost}/group/add`, data);
}
//104001000809-停用/启用

export const offUrl = (data = {}) => {
    return http.post(`${evalHost}/deduct-rule/use`, data);
}
export const noUrl = (data = {}) => {
    return http.post(`${evalHost}/deduct-rule/use`, data);
}
//104001000809-修改分值
export const changedUrl = (data = {}) => {
    return http.post(`${evalHost}/deduct-rule/update`, data);
}
export const tabs2GroupListUrl = (data = {}) => {
    return http.get(`${evalHost}/group/list`, data);
}

//104001000808-分值管理列表
export const tabs1ScoreTableUrl = (data = {}) => {
    return http.get(`${evalHost}/deduct-rule/list`, data);
}
//104001000802-修改分组名称
export const changeGroupNameUrl = (data = {}) => {
    return http.post(`${evalHost}/group/update`, data);
}
//104001000803-删除某个分组
export const deletGroupUrl = (data = {}) => {
    return http.delete(`${evalHost}/group/del/${data.group_id}`, data);
}
//104001000805-关联组织
export const relevanceOrgUrl = (data = {}) => {
    return http.post(`${evalHost}/group/add-org`, data);
}
//104001000806-查询关联组织
export const pullCheckCompanyUrl = (data = {}) => {
    return http.get(`${evalHost}/group/orgs`, data);
}
//104001000807-删除单个关联组织
export const deleteOneUrl = (data = {}) => {
    return http.delete(`${evalHost}/group/del-org/${data.group_org_id}`);
}
// 1040010013002-梯度扣分-查询
export const ladderTbaleUrl = (data = {}) => {
    return http.get(`${evalHost}/gradient-deduction/list`);
}

// 1040010013001-梯度扣分-新增，修改，删除
export const ladderUpdataUrl = (data = {}) => {
    return http.post(`${evalHost}/gradient-deduction/update`, data);
}