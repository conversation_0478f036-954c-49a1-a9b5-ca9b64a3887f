/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @description:
 * @Date: 2022-02-09 14:19:16
 * @Last Modified by: ling<PERSON><PERSON>
 * @Last Modified time: 2022-02-16 17:59:47
 */
import React, { useEffect, useRef, useState, useMemo } from "react";
import "./index.less";
// antd组件
import { Tree, AutoComplete, message, Icon } from "antd";
const { Option } = AutoComplete;
const { TreeNode } = Tree;
// 组织查询
import { locateOrgTree, getOrgTree, findOrgByName } from "apis/organize";
// 用户信息
const { oid, org_type } = JSON.parse(
  sessionStorage.getItem("userInfo") || {}
);

/**
 * 防抖函数
 *
 * @param {*} callback
 * @param {*} time
 * @return {*}
 */
const debounce = (callback, time) => {
  let timer = null;
  return (...args) => {
    if (timer != null) {
      clearTimeout(timer);
      timer = null;
    }
    timer = setTimeout(() => {
      callback(...args);
      clearTimeout(timer);
      timer = null;
    }, time);
  };
};

export default function TreeSelect({ orgId, onTreeChange, onModifyLoading }) {
  // 搜索列表数据
  const [searchList, setSearchList] = useState([]);
  // 展开列表
  const [expandedKeys, setExpandedKeys] = useState();
  // 当前激活key
  const [currentActiveKey, setCurrentActiveKey] = useState([String(orgId)]);
  // 树数据
  const [treeSource, setTreeSource] = useState([]);
  // 左侧tree折叠展开
  const [collapse, setCollapse] = useState(false);
  // tree禁用
  // 初始化树形控件数据
  useEffect(() => {
    initTreeData(
      { load_root: 1, org_type, org_id: oid, tree_type: 2 },
      (data) => {
        setTreeSource(data);
        onModifyLoading();
      }
    );
  }, []);
  /**
   * 加载组织数据
   *
   * @param {*} params { org_id, org_type, tree_type, load_root }
   */
  const initTreeData = async (params, callback) => {
    const {
      data: { code, data, message: msg },
    } = await getOrgTree(params);
    if (code === 0) {
      callback(data);
    } else {
      message.error(msg);
    }
  };

  /**
   * 链查询
   *
   * @param {*} params
   */
  const findLocateOrgTree = async (params) => {
    onModifyLoading(true);
    const {
      data: { code, data, message: msg },
    } = await locateOrgTree(params);
    onModifyLoading(false);
    if (code === 0) {
      setTreeSource(data);
      const keys = getParentKey(params.org_id, data, [])
      setExpandedKeys(keys);
      setCurrentActiveKey([String(params.org_id)]);
      // scrollTree();
      scrollPos(params.org_id)
      onTreeChange && onTreeChange(params.org_id, params.org_name);
    } else {
      message.error(msg);
    }
  };

  /**
     * 滚动树组件
     *
     */
  const scrollPos = (oid) => {
    const el = document.getElementById(oid);
    const pE = document.getElementById("examination-tree-container");
    if (!el || !pE) {
      return;
    }
    setTimeout(() => {
      pE.scrollTop = el.offsetTop - pE.offsetTop - 10;
    }, 300);
  }

  /**
   * 异步加载数据🔎
   * @param {*} treeNode
   * @return {*}
   */
  const onLoadData = (treeNode) => {
    return new Promise((resolve) => {
      if (treeNode.props.children) {
        resolve();
        return;
      }
      const { org_type, org_id } = treeNode.props.dataRef;
      initTreeData({ load_root: 0, org_type, org_id, tree_type: 2 }, (data) => {
        treeNode.props.dataRef.children = data;
        setTreeSource([...treeSource]);
        resolve();
      });
    });
  };

  /**
   * 组织查询🔎
   *
   * @param {*} org_name
   */
  const inputDebounce = useMemo(() => {
    return debounce(async (org_name) => {
      if (!org_name) return;
      const {
        data: { code, data },
      } = await findOrgByName({ org_id: oid, org_name, tree_type: 2 });
      if (code === 0) {
        setSearchList(data);
      }
    }, 500);
  }, []);

  /**
   * 数据扁平化🛬(可优化查询已存在数据时)
   *
   * @param {*} list
   * @return {*}
   */
  const flatUtil = (list) => {
    let flatList = [];
    list.map(({ org_id, short_name, children }) => {
      flatList.push({ org_id, short_name });
      if (children) {
        flatList = flatList.concat(flatUtil(children));
      }
    });
    return flatList;
  };
  /**
   * 🎄节点点🐓
   *
   * @param {*} selectedKeys
   */
  const onSelectTree = (selectedKeys, { selectedNodes }) => {
    if (
      !selectedKeys.length ||
      String(selectedKeys) === String(currentActiveKey)
    ) {
      return;
    }
    setCurrentActiveKey(selectedKeys);
    const { props: { dataRef } } = selectedNodes[0]
    onTreeChange && onTreeChange(selectedKeys[0], dataRef.name, dataRef.short_name);
  };

  /**
   * 获取父节点id🤺🤺🤺
   * @param {*} key
   * @param {*} tree
   * @return {*}
   */
  const getParentKey = (key, tree, parentKey) => {
    tree.forEach((node) => {
      if (node.children.length) {
        parentKey.push(String(node.org_id))
        getParentKey(key, node.children, parentKey)
      }
    })
    /* let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => String(item.org_id) === String(key))) {
          parentKey = node.org_id;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    } */
    return parentKey
  };

  /**
   * 树递🐢渲染
   *
   * @param {*} data
   */
  const loop = (data) =>
    data.map((item) => {
      if (item.children && item.children.length) {
        return (
          <TreeNode
            key={String(item.org_id)}
            title={<div id={item.org_id} title={item.short_name || item.name}>{item.short_name || item.name}</div>}
            isLeaf={!item.child_org_num}
            dataRef={item}
          >
            {loop(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          key={String(item.org_id)}
          title={<div id={item.org_id} title={item.short_name || item.name}>{item.short_name || item.name}</div>}
          dataRef={item}
          isLeaf={!item.child_org_num}
        />
      );
    });

  /**
   * AutoComplete 搜索结果㊙️㊙️㊙️㊙️
   *
   * @param {*} data
   * @return {*}
   */
  const loopOption = (data) => {
    return data.map((item) => (
      <Option key={String(item.org_id)} dataRef={item}>
        {item.org_name}
      </Option>
    ));
  };
  // 自动补全prop㊙️㊙️㊙️
  const autoCompleteProps = {
    style: { width: 360 },
    placeholder: "请输入组织名称查询",
    onSelect: (_, dataRef) => {
      // setExpandedKeys([getParentKey(value, treeSource)]);
      findLocateOrgTree({
        org_id: dataRef.props.dataRef.org_id,
        org_name: dataRef.props.dataRef.org_name,
        org_type,
        load_root: 1,
        tree_type: 2,
        root_org_id: oid,
      });
    },
    onSearch: inputDebounce,
  };
  // 树组件prop
  const treeProps = {
    className: "draggable-tree",
    blockNode: true,
    expandedKeys: expandedKeys || [String(orgId)],
    selectedKeys: currentActiveKey,
    onSelect: onSelectTree,
    loadData: onLoadData,
    onExpand: (expandedKeys, _) => {
      setExpandedKeys(expandedKeys);
    },
  };
  return (
    <div className={`dpe-tree-select ${collapse && "dpe-tree-collapse"}`}>
      <div className="main-box">
        <div className="input-box">
          <AutoComplete {...autoCompleteProps}>
            {loopOption(searchList)}
          </AutoComplete>
        </div>
        <div className="tree-box" id="examination-tree-container">
          <Tree {...treeProps}>
            {loop(treeSource)}
          </Tree>
        </div>
      </div>
      <Icon className="collapse-icon" type="double-left" onClick={() => setCollapse(!collapse)} />
    </div>
  );
}
