import React from "react"
import {connect} from "dva"
import {Tabs} from "antd"
import LeaderConfig from './components/leader-config'
import ProportionConfig from './components/proportion-config'
import './index.less'

const {TabPane} = Tabs

const EvaluationConfig = ({userInfo}) => {
    return (
        <div className="EvaluationConfig-container">
            <Tabs defaultActiveKey="1" style={{height: "100%"}}>
                <TabPane style={{padding: "4px 24px"}} tab="市管领导分管设置" key="1">
                    <LeaderConfig userInfo={userInfo}></LeaderConfig>
                </TabPane>
                <TabPane style={{padding: "4px 24px"}} tab="好等次比例设置" key="2">
                    <ProportionConfig ></ProportionConfig>
                </TabPane>
            </Tabs>
        </div>
    )
}

export default connect(({organizeData, userInfo}) => ({
    organizeData,
    userInfo
}))(EvaluationConfig)
