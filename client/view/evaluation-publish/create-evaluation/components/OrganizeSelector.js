import { connect } from "dva";
import { getOrgTree, getTreeList } from "client/apis/organize";
import { findAppraisalMiddleUser } from "client/apis/evaluation-publish";

function OrganizeSelector(props) {
  const { userInfo, onOk } = props;

  const [visible, setVisible] = React.useState(false);
  const [currentInfo, setCurrentInfo] = React.useState({});
  const [tree, setTree] = React.useState([]);
  const [checkedKeys, setCheckedKeys] = React.useState([]);
  const [checkedItems, setCheckedItems] = React.useState([]);
  const [searchInput, setSearchInput] = React.useState("");

  const [prevData, setPrevData] = React.useState({});

  React.useEffect(() => {
    if (visible) {
      setPrevData({ checkedItems, checkedKeys });
    }
  }, [visible]);

  const getCurrentInfo = () => {
    if (!userInfo) return;
    getTreeList(userInfo.oid).then((res) => {
      if (res.data.code === 0 && Array.isArray(res.data.data)) {
        setCurrentInfo(res.data.data[0]);
      }
    });
  };

  const getOrgInfo = () => {
    if (!currentInfo) return;
    getOrgTree({
      org_id: userInfo.oid,
      tree_type: currentInfo.tree_type,
      load_root: 1,
      org_type: currentInfo.org_type,
    }).then((res) => {
      if (res.data.code === 0 && Array.isArray(res.data.data)) {
        setTree(res.data.data);
      }
    });
  };

  React.useEffect(() => {
    getCurrentInfo();
  }, []);
  React.useEffect(() => {
    getOrgInfo();
  }, [currentInfo]);

  const handleOk = async () => {
    if (!checkedKeys.length) return;
    const res = await Promise.all(
      checkedKeys.map((i) => findAppraisalMiddleUser(i))
    );
    if (res.every((item) => item.data.code === 0)) {
      const data = res.map((item) => item.data.data[0].user_list);
      onOk(data.flat());
      setVisible(false);
    }
  };

  const handleCheck = (checkedKeys, info) => {
    const items = checkedKeys.map((key) => {
      return findItemInTree(tree, (node) => {
        return node.org_id.toString() === key.toString();
      });
    });
    setCheckedItems(items);
    setCheckedKeys(checkedKeys);
  };

  const handleDelete = (key) => {
    setCheckedItems(checkedItems.filter((item) => item.org_id != key));
    setCheckedKeys(checkedKeys.filter((item) => item != key));
  };

  const handleClose = () => {
    setCheckedItems(prevData.checkedItems);
    setCheckedKeys(prevData.checkedKeys);
    setVisible(false);
  };

  const onLoadData = async (treeNode) => {
    if (!currentInfo) return;
    if (treeNode.props.children.length) return;

    const res = await getOrgTree({
      org_id: treeNode.props.dataRef.org_id,
      tree_type: currentInfo.tree_type,
      load_root: 0,
      org_type: currentInfo.org_type,
    });
    if (res.data.code === 0 && Array.isArray(res.data.data)) {
      treeNode.props.dataRef.children = res.data.data;
      setTree([...tree]);
    }
  };

  const renderTreeNodes = (data) =>
    data.map((item) => {
      const index = item.name.indexOf(searchInput);
      const beforeStr = item.name.substr(0, index);
      const afterStr = item.name.substr(index + searchInput.length);
      const name =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: "#f50" }}>{searchInput}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.name}</span>
        );
      if (item.children) {
        return (
          <Ant.Tree.TreeNode
            checkable={item.child_org_num === 0}
            isLeaf={item.child_org_num === 0}
            title={name}
            key={item.org_id}
            dataRef={item}
          >
            {renderTreeNodes(item.children)}
          </Ant.Tree.TreeNode>
        );
      }
      return (
        <Ant.Tree.TreeNode
          checkable={item.child_org_num === 0}
          isLeaf={item.child_org_num === 0}
          title={name}
          key={item.org_id}
          {...item}
          dataRef={item}
        />
      );
    });

  return (
    <div>
      <Ant.Button
        type="primary"
        style={{ margin: "0 10px" }}
        onClick={() => setVisible(true)}
      >
        <Ant.Icon type="plus" />
        选择
      </Ant.Button>

      <Ant.Modal
        title="选择分管部门"
        footer={null}
        visible={visible}
        width={900}
        onCancel={handleClose}
      >
        <Ant.Row type="flex" gutter={16} style={{ height: 640 }}>
          <Ant.Col span={10} style={{ height: "100%" }}>
            <div style={{ height: "100%", background: "#F7F8F9" }}>
              <div style={{ marginBottom: 8, padding: 16 }}>
                <Ant.Input.Search
                  value={searchInput}
                  placeholder="输入机构名称"
                  onChange={(e) => setSearchInput(e.target.value)}
                />
              </div>

              <div style={{ height: 550, overflow: "auto" }}>
                <Ant.Tree
                  checkable
                  checkedKeys={checkedKeys}
                  defaultExpandedKeys={[1]}
                  loadData={onLoadData}
                  onCheck={handleCheck}
                >
                  {renderTreeNodes(tree)}
                </Ant.Tree>
              </div>
            </div>
          </Ant.Col>
          <Ant.Col span={14} style={{ height: "100%" }}>
            <div
              style={{
                height: "100%",
                background: "#F7F8F9",
              }}
            >
              <Ant.Row
                type="flex"
                align="middle"
                style={{
                  paddingLeft: 16,
                  height: 64,
                  borderBottom: "1px solid #DBDBDB",
                  fontSize: 16,
                  fontWeight: 800,
                }}
              >
                <span>已选择</span>
                <span style={{ color: "#2ca6fc" }}>
                  ({checkedItems.length})
                </span>
              </Ant.Row>
              <div style={{ padding: 20, height: 550, overflow: "auto" }}>
                {checkedItems.map((item) => (
                  <Ant.Row
                    key={item.org_id}
                    type="flex"
                    justify="space-between"
                    align="middle"
                    style={{
                      padding: 20,
                      marginBottom: 16,
                      height: 60,
                      background: "#fff",
                    }}
                  >
                    <div style={{ fontWeight: 600 }}>{item.name}</div>
                    <Ant.Icon
                      type="close-circle"
                      style={{ color: "#fc352a", cursor: "pointer" }}
                      onClick={() => handleDelete(item.org_id)}
                    />
                  </Ant.Row>
                ))}
              </div>
            </div>
          </Ant.Col>
        </Ant.Row>

        <Ant.Row
          type="flex"
          justify="center"
          gutter={16}
          style={{ marginTop: 20 }}
        >
          <Ant.Col>
            <Ant.Button
              type="primary"
              style={{ padding: "0 40px" }}
              onClick={handleOk}
            >
              确定
            </Ant.Button>
          </Ant.Col>
          <Ant.Col>
            <Ant.Button style={{ padding: "0 40px" }} onClick={handleClose}>
              取消
            </Ant.Button>
          </Ant.Col>
        </Ant.Row>
      </Ant.Modal>
    </div>
  );
}

export default connect(({ userInfo }) => ({
  userInfo,
}))(OrganizeSelector);

function findItemInTree(tree, predicate) {
  function traverse(nodes) {
    for (let node of nodes) {
      if (predicate(node)) return node;
      if (node.children && node.children.length > 0) {
        const result = traverse(node.children);
        if (result) return result;
      }
    }
    return null;
  }

  return traverse(tree);
}
