import React, { Component } from 'react';
import { DatePicker } from 'antd';
import DatePickerLocale from 'config/DatePickerLocale.json';

const moment = require('moment');


class DateRangeComponent extends Component {
    constructor(props){
        super(props);
        this.state = {
            startValue: props.startValue ? moment(props.startValue) : null,
            endValue: props.endValue ? moment(props.endValue) : null,
            showTime: props.showTime || false,
            startPlaceHolder: props.startPlaceholder || '开始时间',
            endPlaceHolder: props.endPlaceholder || '结束时间',
            format: props.format || (props.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'),
            endOpen: false
        };
    }

    disabledStartDate(startValue){
        const endValue = this.state.endValue;
        if (!startValue || !endValue) {
            return false;
        }
        return startValue.valueOf() > endValue.valueOf();
    }
    disabledEndDate(endValue){
        const startValue = this.state.startValue;
        if (!endValue || !startValue) {
            return false;
        }
        return endValue.valueOf() <= startValue.valueOf();
    }
    onChange(e){
        this.props.onDateChange(e);
    }
    onStartChange(value){
        this.setState({startValue: value});
        this.onChange({status: 1, startValue: value ? value.format(this.state.format) : null});
    }
    onEndChange(value){
        this.setState({endValue: value });
        this.onChange({status: 2, endValue: value ? value.format(this.state.format) : null});
    }
    handleStartOpenChange(open){
        if (!open) {
            this.setState({ endOpen: true });
        }
    }
    handleEndOpenChange(open){
        this.setState({ endOpen: open });
    }
    componentWillReceiveProps(nextProps){
        this.setState({
            startValue: nextProps.startValue ? moment(nextProps.startValue) : null,
            endValue: nextProps.endValue ? moment(nextProps.endValue) : null
        });
    }
    render(){
        const { showTime, startValue, endValue, endOpen, startPlaceHolder, endPlaceHolder, format } = this.state;

        return (
            <div>
                <DatePicker
                    disabledDate={this.disabledStartDate.bind(this)}
                    showTime={showTime}
                    value={startValue}
                    format={format}
                    placeholder={startPlaceHolder}
                    onChange={this.onStartChange.bind(this)}
                    onOpenChange={this.handleStartOpenChange.bind(this)}
                    style={{ width: 190 }}
                    locale={DatePickerLocale}
                />
                &nbsp; 至 &nbsp;
                <DatePicker
                    disabledDate={this.disabledEndDate.bind(this)}
                    showTime={showTime}
                    value={endValue}
                    format={format}
                    placeholder={endPlaceHolder}
                    onChange={this.onEndChange.bind(this)}
                    open={endOpen}
                    onOpenChange={this.handleEndOpenChange.bind(this)}
                    style={{ width: 190 }}
                    locale={DatePickerLocale}
                />
            </div>
        )
    }
}

export default DateRangeComponent = DateRangeComponent;