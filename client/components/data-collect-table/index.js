// 考核二期，数据采集表格
import React from 'react';
import './index.less';
import { Tabs, Input, Button, message } from 'antd';
import propTypes from 'prop-types';
import { fileDownload } from 'client/components/file-download';

import Tab1 from './tab1';
import Tab2 from './tab2';
import Tab3 from './tab3';
import Tab4 from './tab4';
import Tab5 from './tab5';
import Tab6 from './tab6';
import Tab7 from './tab7';
import { reloadScore } from 'apis/examination';
const TabPane = Tabs.TabPane;
const Search = Input.Search;
class Class extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [],
      tableData: [],
      searchParam: '',
      eval_year: 0,
      eval_cycle: 0,
      org_id: 0,
      months: [],
      type_id: -1,
      tableLoading: false,
      curListObj: {},//当前type_id 对应的数据
    };
  }
  componentWillMount () {
    const { isFinish, dataSource = {}, orgId } = this.props;
    const list = dataSource.list;
    this.setState({
      list,
      tableData: list[0],
      type_id: list[0].type_id,
      eval_year: dataSource && dataSource.eval_year,
      eval_cycle: dataSource && dataSource.eval_cycle,
      months: dataSource && dataSource.months,
      isFinish,
      org_id: orgId
    })
  }

  componentWillReceiveProps (newProps) {
    const { isFinish, dataSource = {}, orgId, isSuccess } = newProps;
    if(isSuccess){
      this.reloadScore()
      return
    }
    if (orgId) {
      const { type_id, org_id, evalId, searchParam } = this.state;
      const list = dataSource.list;
      /* let tableData = list[0];
      console.log("TCL: Class ->", searchParam)
      if (org_id == orgId && type_id != -1) {
        for (let val of list) {
          if (val.type_id == type_id) {
            console.log("TCL: Class -> componentWillReceiveProps -> val", val)
            tableData = val
          }
        }
      } */
      this.setState({
        list,
        // tableData,
        type_id: org_id == orgId && type_id != -1 ? type_id : list[0].type_id,
        eval_year: dataSource && dataSource.eval_year,
        eval_cycle: dataSource && dataSource.eval_cycle,
        months: dataSource && dataSource.months,
        isFinish,
        org_id: org_id == orgId ? org_id : orgId
      },()=>{
        this.onSearchData(org_id == orgId ? searchParam : '')
      })
    }
    
  }
  onSearchData (value = '', typeId) {
    let { list, type_id } = this.state;
    let searchParam = typeId ? '' : value;
    typeId = typeId ? typeId : type_id;
    let tableData = [];
    for (const val of list) {
      if (val.type_id == typeId) {
        tableData = { ...val }
        if (!!value) {
          tableData.list_detail = tableData.list_detail.filter(org => {
            if (typeId == 1) {
              return org.user_name.includes(value)
            }
            return org.org_name.includes(value)
          })
        }
      }
    }
    this.setState({
      type_id: typeId,
      tableData,
      searchParam
    })
  }
  // todo: 刷新colletdataTable 的数据
  getPrams () {
    const { type_id } = this.state;
    const { evalId, orgId } = this.props;
    return Object.assign({}, { type_id, eval_id: evalId, org_id: orgId });
  }
  // 修改分值后刷新的接口
  async reloadScore () {
    const { type_id, list, searchParam } = this.state;
    this.setState({ tableLoading: true })
    // setTimeout(() => {
    const r = await reloadScore(this.getPrams());
    let that = this;
    if (r.data.code === 0 && r.data.status === 200) {
      let l = list
      for (let [key, val] of list.entries()) {
        if (val.type_id == type_id) {
          l[key] = r.data.data
        }
      }
      that.setState({
        list: l, 
        tableLoading: false 
      },() => {
        this.onSearchData(searchParam)
      })
    }
  }
  render () {
    const { list, isFinish, tableData, searchParam, eval_year, eval_cycle, months, type_id, tableLoading } = this.state;
    
    let { evalCycle, evalId, orgId, showModal, cRecoder, isCollect } = this.props;
    // 默认统计
    if (!evalCycle) {
      evalCycle = 1;
    }
    const commonProps = {
      eval_year,
      eval_cycle: eval_cycle || evalCycle,
      months,
      isFinish,
      cRecoder,
      loading: tableLoading,
      isCollect,
      eventsObj: {
        showModal,
        reload: this.reloadScore.bind(this)
      }
    }

    // 根据OptionId确认渲染的tab组件
    const renderTab = (tab = {}) => {
      const { type_id } = tab;
      switch (type_id) {
        case 1:
          return <Tab1 {...commonProps} data={tab} />;
        case 2:
          return <Tab2 {...commonProps} data={tab} />;
        case 3:
          return <Tab3 {...commonProps} data={tab} />;
        case 4:
          return <Tab4 {...commonProps} data={tab} />;
        case 5:
          return <Tab5 {...commonProps} data={tab} />;
        case 6:
          return <Tab6 {...commonProps} data={tab} />;
        case 7:
          return <Tab7 {...commonProps} data={tab} />;
        default:
          return null;
      }
    }

    return (
      <div className="data-collect-table-container">
        <Tabs activeKey={type_id + ""} onChange={type_id => this.onSearchData(null, type_id)}>
          {
            list && list.map((tab, index) => {
              return (
                <TabPane tab={tab.type_name} key={tab.type_id}>
                  {/* {renderTab(tab, months)} */}
                </TabPane>
              );
            })
          }
        </Tabs>
        {
          isCollect != 2 &&
          <div className="tab-search">
            <Search
              className="tab-search_el"
              placeholder={type_id == 1 ? "请输入领导姓名" : "请输入支部名称"}
              enterButton="搜索"
              value={searchParam}
              onChange={(e) => this.setState({ searchParam: e.target.value })}
              onSearch={value => this.onSearchData(value)}
            />
            <a
              onClick={()=>{
                fileDownload(`/eval/data-collect-deduction/view-detail-export?evalId=${evalId}&orgId=${orgId}&typeId=${type_id}&searchName=${searchParam}`)
              }}
              href="javascript:void(0);"
            >
              <Button >导出</Button>
            </a>
          </div>
        }
        {renderTab(tableData)}

      </div>

    )
  }
}

Class.propTypes = {
  // 标记位，从外部传入，标记是否统计完成，两种状态之间，表格格式，请求接口不相同
  isFinish: propTypes.bool,
  isSuccess: propTypes.bool, // 修正扣分值是否成功
  dataSource: propTypes.object,
  evalCycle: propTypes.number,
  isCollect: propTypes.number,
  evalId: propTypes.number,
  orgId: propTypes.number
}

Class.defaultProps = {
  isFinish: true,
  dataSource: {},
  evalCycle: 1
}

export default Class;
