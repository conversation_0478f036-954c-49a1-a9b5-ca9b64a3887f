//活动详情页面顶端卡片组件

/*
* 活动详情页顶端卡片
* <AUTHOR>
*
* 接收参数：
* style:样式控制，修改默认样式
* title:卡片标题
*
* */

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import './index.less';

class HeaderCard extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        let {style, title, children} = this.props;
        return (
            <div className={'header-card'} style={style}>
                <header className={'card-title'}>{title || ''}</header>
                <section>
                    {children || ''}
                </section>
            </div>
        )
    }
}

//传入参数检测
HeaderCard.propTypes = {
    //卡片样式
    style: PropTypes.object,
    //卡片标题
    title: PropTypes.string
};

export default HeaderCard;