import React, { useEffect, useState, useRef } from 'react'
import "./index.less"
import OrgTree from 'client/components/org-tree'
import { Table, Button } from 'antd';
import { getTeamMemberUserList } from "client/apis/cadre-portrait"
import CadreIndexImage from '../cadre-index-image'
import { uploadPhoto, downloadZip, loopDownloadZip, startZip } from 'client/apis/cadre-portrait'; // 假设这是上传图片的API
import ImageQueue from './ImageQueue';
import { message, Spin } from 'antd';

const asyncRunTask = (cb, timer) => {
    let timerId = setTimeout(() => {
        cb();

        clearTimeout(timerId)

    }, timer)
}

function index() {

    const [data, setData] = useState([])

    const [notEmptyData, setNotEmptyData] = useState([])

    const [orgId, setOrgId] = useState(window.sessionStorage.getItem('_oid'))

    const [currentUserStack, setCurrentUserStack] = useState([]);
    // 记录每一个上传状态
    const uploadUserStatus = useRef({});

    const [currentUser, setCurrentUser] = useState(null);

    const updateCurrentUser = useRef(() => { });

    updateCurrentUser.current = setCurrentUser;

    const [page, setPage] = useState(1);

    const [loading, setLoading] = useState(false);


    const [processing, setProcessing] = useState(false);
    const [progress, setProgress] = useState(0);
    const [total, setTotal] = useState(0);
    const [processed, setProcessed] = useState(0);

    const columns = [
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 100,
        },
        {
            title: '现任职务',
            dataIndex: 'current_job',
            key: 'current_job',
            width: 200,
        },
        {
            title: '出生年月',
            dataIndex: 'birthday',
            key: 'birthday',
            width: 120,
            render: (text) => text
        },
        {
            title: '任现职务时间',
            dataIndex: 'current_job_time',
            key: 'current_job_time',
            width: 120,
        },
        {
            title: '全日制学历',
            dataIndex: 'diploma',
            key: 'diploma',
            width: 120,
        },
        {
            title: '毕业院校及专业',
            dataIndex: 'school',
            key: 'school',
            width: 180,
        },
        {
            title: '干部指数',
            dataIndex: 'cadre_index',
            key: 'cadre_index',
            width: 100,
        },
        {
            title: '指数同序列排名',
            dataIndex: 'cadre_index_rank',
            key: 'cadre_index_rank',
            width: 140,
        }
    ];
    const loadData = async () => {
        setPage(1)
        setLoading(true)
        try {
            const { data: res } = await getTeamMemberUserList({
                org_id: orgId,
            })
            if (res.code === 0) {
                setData(res.data)
                setNotEmptyData(res.data.filter(item => item.cadre_index !== '-'))
            }
        } catch (error) {
        } finally {
            setLoading(false)
        }
    }

    // 先轮询查询生成进度，循环查询直到生成完成
    const loopDownload = async () => {
        return new Promise((resolve, reject) => {
            let timer = setInterval(async () => {
                const { data: res } = await loopDownloadZip({ org_id: orgId })
                // 0未完成 1完成
                if (res.code === 0) {

                    if (res.data == 0) {
                        return
                    }

                    clearInterval(timer)

                    await downloadZip({ org_id: orgId })



                    resolve()

                } else {
                    clearInterval(timer)

                    reject(res.message)
                }
            }, 1000)
        })
    }
    // 开始下载
    const startDownload = async () => {
        await startZip({ org_id: orgId })
    }

    const onChange = ([org_id]) => {
        setOrgId(org_id)
    }

    const taskStack = useRef([]);
    const STACK_LENGTH = 4

    const onDownload = async () => {
        if (!notEmptyData.length) {
            message.warning('没有可处理的数据');
            return;
        }

        if (taskStack.current.length) {
            return
        }
        // 下载状态重置
        downloading.current = false;
        setProcessing(true);
        setProgress(0);
        setProcessed(0);
        setTotal(notEmptyData.length);


        for (let i = 0; i < notEmptyData.length; i++) {
            taskStack.current.push({
                user_id: notEmptyData[i].user_id,
                task: () => {

                    uploadUserStatus.current[notEmptyData[i].user_id] = {
                        status: 'processing',
                        name: notEmptyData[i].name,
                        user_id: notEmptyData[i].user_id,
                        org_id: notEmptyData[i].org_id,
                    }

                    updateCurrentUser.current(notEmptyData[i]);

                    setCurrentUserStack((prev) => [...prev, notEmptyData[i]])

                }
            })
        }
        // 调用栈
        for (let i = 0; i < STACK_LENGTH; i++) {
            asyncRunTask(() => {
                runTask();
            }, 100 * i)
        }
    };

    // const runTask = () => {
    //     if (taskStack.current.length) {
    //         const shift = taskStack.current.shift();
    //         shift.task();
    //     }
    // }
    const runTask = () => {
        if (taskStack.current.length) {
            const shift = taskStack.current.shift();
            shift.task();
        }
    }
    const downloading = useRef(false);

    const handleImageGenerated = async ({ file, user_id, name, org_id }) => {
        // document.querySelector('.imageContainer').src = URL.createObjectURL(file)
        // message.loading(`图片上传中${processed}/${total}`)
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('org_id', org_id);
            formData.append('user_id', user_id);

            const { data: res } = await uploadPhoto(formData);
            if (res.code === 0) {

                setCurrentUserStack((prev) => prev.filter(item => item.user_id !== user_id))

                // 更新上传状态
                delete uploadUserStatus.current[user_id]

                uploadUserStatus.current[user_id] = {
                    status: 'success',
                }

                setProcessed(prev => {
                    const newProgress = prev + 1;
                    setProgress((newProgress / total) * 100);
                    return newProgress;
                });

                // 确认所有用户上传完成
                if (Object.values(uploadUserStatus.current).every(item => item.status === 'success')) {
                    downloading.current = true;
                    message.loading(`正在下载文件`, 999999)

                    // await downloadZip({ org_id: orgId })

                    await startDownload()

                    await loopDownload()

                    message.destroy()

                    message.success(`文件下载完成`)

                    setProcessing(false)
                    // 清空上传状态
                    uploadUserStatus.current = {};
                } else {

                    runTask();
                }
            } else {
                message.error(`用户 ${name} 的图片上传失败`);
                // 清空任务栈
                resetStatus()
            }

        } catch (error) {
            console.log("🚀 ~ handleImageGenerated ~ error:", error)

            message.error(`用户 ${name} 的图片上传失败`);
            // 下载状态重置
            downloading.current = false;
            setProcessing(false)

            resetStatus()

            message.destroy()

            // 可以选择重试或继续处理下一个
        }
    };
    const onError = ({ name }) => {
        message.error(`${name} 同志干部履职评价指数反馈单生成失败!`);
        // 清空任务栈
        resetStatus()
    }
    const resetStatus = () => {
        // 清空任务栈
        taskStack.current = [];
        // 下载状态重置
        downloading.current = false;
        // 处理状态重置
        setProcessed(0);
        setProgress(0);
        setTotal(0);
        setProcessing(false)
    }
    useEffect(() => {
        loadData()
    }, [orgId])
    return (
        <div className='party-member-page'>
            <div className="left-box">
                <OrgTree onChange={onChange} />
            </div>
            <div className="right-box">
                <div className="btn-box">
                    <Button type="primary" loading={processing} disabled={loading} onClick={onDownload}>下载干部指数反馈单</Button>
                </div>
                <Table columns={columns} loading={loading} pagination={data.length > 10 ? {
                    current: page,
                    onChange: (page) => {
                        console.log("🚀 ~ index ~ page:", page)
                        setPage(page)
                    },
                    pageSize: 10,
                    total: data.length,
                } : false} bordered dataSource={data} scroll={{ y: 500 }} />
                {/* <img src="" className='imageContainer' alt="" /> */}
            </div>
            {/* <CadreIndexImage user={currentUser} onImageGenerated={handleImageGenerated} /> */}
            {
                Array.from({ length: STACK_LENGTH }).map((item, index) => {
                    return <CadreIndexImage onError={onError} key={index} user={currentUserStack[index]} onImageGenerated={handleImageGenerated} />
                })
            }
            {
                processing && <div className="loading-mask">
                    <Spin tip={`文件上传中，请不要做其他操作${processed}/${total}`} />
                </div>
            }
        </div>
    )
}

export default index