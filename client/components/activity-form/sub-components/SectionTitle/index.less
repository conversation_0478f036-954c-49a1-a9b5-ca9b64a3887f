.sectionTitle {
    @color: #F46E65;
    position: relative;
    margin: 50px 0 30px;
    padding-left: 20px;
    &::before {
        content: attr(data-title);
        display: inline-block;
        color: @color;
        font-size: 14px;
        line-height: 14px;
        border-left: 2px solid @color;
        padding: 0 10px;
        background-color: #FFF;
        position: relative;
        z-index: 1;
    }
    &:after {
        content: '.';
        display: block;
        transform: translateY(-10px);
        color: transparent;
        width: 100%;
        height: 0;
        border-bottom: 1px dashed @color;
    }
}

@media (max-width: 768px) {
    .sectionTitle {
        padding-left: 0;
    }
}