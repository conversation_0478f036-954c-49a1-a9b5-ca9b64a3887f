/**
 * 互动规则
 * <AUTHOR>
 * 2018/4/23
 */
import React from "react";
import PropTypes from "prop-types";
import {
  Row,
  Col,
  Form,
  Input,
  Popover,
  Checkbox,
  Cascader,
  DatePicker,
  AutoComplete,
  Select
} from "antd";
import { getArea } from "client/apis/active-organization";
import moment from "moment";

import adaptiveLayout from "../gridLayout";
import DatePickerLocale from "config/DatePickerLocale.json";
import InputTips from "../sub-components/InputTips";
import CustomizeTagPreview from "../sub-components/CustomizeTagPreview";
import UploadImage from "./UploadImage";
import "./style.less";
import { CDN } from "apis/config";
const baseImageUri = `${CDN}/`;

const FormItem = Form.Item;
const { TextArea } = Input;
const { Option } = Select;

const CustomizeTagLayout = {
  align: "middle",
  type: "flex",
  justify: "space-between"
};

class ActivityBase extends React.Component {
  constructor() {
    super();
    this.state = {
      provinceData: [], // 区域数据
      cityData: [], // 城市
      districtData: [], // 行政区
      province: "",
      city: "",
      district: "",
      loadingDefaultCity: false, // 加载默认城市
      orgName:
        typeof window !== "undefined"
          ? unescape(window.sessionStorage.getItem("_on"))
          : ""
    };
    getArea()
      .then(res => {
        const { data } = res;
        if (data.code === 0) {
          this.setState({
            provinceData: data.data
          });
        }
      })
      .catch(err => { });
  }
  async componentDidMount() { }
  // 加载区域树
  loadOrgAreaData(val, name) {
    console.log("pid", val);
    getArea({ pid: val })
      .then(res => {
        const { data } = res;
        if (data.code === 0) {
          this.setState({
            [name]: data.data
          });
          // 加载城市数据
          if (name === "cityData") {
            this.setState({
              districtData: []
            });
          }
        }
      })
      .catch(err => { });
  }
  handleProvinceChange() {
    const { handleEvents } = this.props;
    const self = this;
    return value => {
      self.loadOrgAreaData(value, "cityData");
      let c = this.state.provinceData.find(p => p.adcode === value);
      if (c) {
        handleEvents.SavecityInfo({
          province_name: c.area_name,
          province: value,
          city_name: "",
          city: "",
          district_name: "",
          district: ""
        });
      }
    };
  }
  handleCityChange() {
    const { handleEvents } = this.props;
    const self = this;
    return value => {
      self.loadOrgAreaData(value, "districtData");
      let c = this.state.cityData.find(c => c.adcode === value);
      if (c) {
        handleEvents.SavecityInfo({
          city_name: c.area_name,
          city: value,
          district_name: "",
          district: ""
        });
      }
    };
  }
  handleDistrictChange() {
    const { handleEvents } = this.props;
    const self = this;
    return value => {
      let c = this.state.districtData.find(d => d.adcode === value);
      if (c) {
        handleEvents.SavecityInfo({
          district_name: c.area_name,
          district: value
        });
      }
    };
  }
  onFocusCity(e) {
    const { activityFormState } = this.props;
    this.loadOrgAreaData(activityFormState.offline.province, "cityData");
  }
  onFocusDistrict(e) {
    const { activityFormState } = this.props;
    this.loadOrgAreaData(activityFormState.offline.city, "districtData");
  }
  render() {
    const {
      form,
      emuns,
      preview,
      container,
      formFields,
      handleEvents,
      activityFormState,
      content
    } = this.props;
    const { customizeTag } = emuns;
    const { thumbnail } = activityFormState;
    const {
      onUploading,
      onUploadSuccess,
      onUploadError,
      onUploadDelete
    } = handleEvents;
    const { getFieldDecorator, getFieldValue } = form;
    const activityTimeStartDisabled = getFieldValue(
      formFields.activityTimeStartDisabled
    );
    const activityTimeEndDisabled = getFieldValue(
      formFields.activityTimeEndDisabled
    );

    const provinceOptions = this.state.provinceData.map(province => (
      <Option key={province.adcode}>{province.area_name}</Option>
    ));
    const cityOptions = this.state.cityData.map(city => (
      <Option key={city.adcode}>{city.area_name}</Option>
    ));
    const districtOptions = this.state.districtData.map(district => (
      <Option key={district.adcode}>{district.area_name}</Option>
    ));

    const replaceAdd = (val) => {
      return val;
      // return val.replace(/\+/g, ' ');
    }


    return (
      <div>
        <FormItem
          // label="互动长标题"
          label="长标题"
          {...adaptiveLayout.formWrapper}
          wrapperCol={{ md: { span: 18 }, lg: { span: 17 }, xl: { span: 13 } }}
        >
          <InputTips
            max={70}
            container={container}
            text={getFieldValue(formFields.longTitle)}
          >
            {getFieldDecorator(formFields.longTitle, {
              initialValue: activityFormState.longTitle,
              rules: [
                {
                  max: 70,
                  message: "最多输入70个汉字"
                },
                {
                  required: true,
                  message: "请输入互动长标题"
                }
              ]
            })(
              <TextArea
                placeholder="请输入标题"
                disabled={preview}
                autoSize={{ maxRows: 2 }}
              />
            )}
          </InputTips>
        </FormItem>

        <FormItem
          // label="互动短标题"
          label="短标题"
          {...adaptiveLayout.formWrapper}
          {...adaptiveLayout.wrapperColShort}
        >
          <InputTips
            max={20}
            container={container}
            text={getFieldValue(formFields.shortTitle)}
          >
            {getFieldDecorator(formFields.shortTitle, {
              initialValue: activityFormState.shortTitle,
              rules: [
                {
                  max: 20,
                  message: "最多输入20个汉字"
                },
                {
                  required: true,
                  message: "请输入互动短标题"
                }
              ]
            })(
              <Input
                disabled={preview}
                placeholder="请输入标题"
                style={{ maxWidth: 420 }}
              />
            )}
          </InputTips>
        </FormItem>

        <Row className="ant-form-item">
          <Col
            {...adaptiveLayout.formWrapper.labelCol}
            className="ant-form-item-label"
          >
            <label title="自定义标记">自定义标记</label>
          </Col>
          <Col {...adaptiveLayout.wrapperColShort.wrapperCol}>
            {getFieldDecorator(formFields.customizeTag, {
              initialValue: activityFormState.customizeTag
            })(
              <Checkbox.Group style={{ width: "100%" }}>
                {Object.keys(customizeTag).map((key, index) => (
                  <Popover
                    key={key}
                    placement={"rightTop"}
                    content={
                      <CustomizeTagPreview uri={customizeTag[key].previewUri} />
                    }
                  >
                    <Row {...CustomizeTagLayout} className="custom-tags">
                      <Col span={12}>
                        <Checkbox
                          disabled={preview}
                          value={customizeTag[key].value}
                        >
                          {customizeTag[key].label}
                        </Checkbox>
                        {/* <span className="note-description">{customizeTag[key].description}</span> */}
                      </Col>
                      <Col className="note-description">
                        {customizeTag[key].description}
                      </Col>
                    </Row>
                  </Popover>
                ))}
              </Checkbox.Group>
            )}
          </Col>
        </Row>

        <Row className="ant-form-item">
          <Col
            {...adaptiveLayout.formWrapper.labelCol}
            className="ant-form-item-label"
          >
            <label title="上传图片">上传图片</label>
          </Col>
          <Col {...adaptiveLayout.formWrapperCol}>
            <Row gutter={2} type="flex" justify="space-between">
              <Col {...adaptiveLayout.uploadImagesCol}>
                <UploadImage
                  disabled={preview}
                  typeName="app_theme_img"
                  title="移动端-互动主题图"
                  description="1080x432、小于800kb、jpg或png"
                  uri={
                    thumbnail.app_theme_img
                      ? `${baseImageUri}${thumbnail.app_theme_img}`
                      : ""
                  }
                  onUploading={onUploading}
                  onSuccess={onUploadSuccess}
                  onError={onUploadError}
                  onDelete={onUploadDelete}
                  limit={{ size: 800, width: 1080, height: 432 }}
                />
              </Col>
              <Col {...adaptiveLayout.uploadImagesCol}>
                <UploadImage
                  disabled={preview}
                  typeName="app_lists_img"
                  title="移动端-互动页列表图"
                  description="432x256px、小于250kb、jpg或png"
                  uri={
                    thumbnail.app_lists_img
                      ? `${baseImageUri}${thumbnail.app_lists_img}`
                      : ""
                  }
                  onUploading={onUploading}
                  onSuccess={onUploadSuccess}
                  onError={onUploadError}
                  onDelete={onUploadDelete}
                  data={{ upType: "list-image" }}
                  limit={{ size: 250, width: 432, height: 256 }}
                />
              </Col>
              <Col {...adaptiveLayout.uploadImagesCol}>
                <UploadImage
                  disabled={preview}
                  typeName="app_focus_img"
                  title="移动端-互动页聚焦图"
                  description="1080x432、小于800kb、jpg或png"
                  uri={
                    thumbnail.app_focus_img
                      ? `${baseImageUri}${thumbnail.app_focus_img}`
                      : ""
                  }
                  onUploading={onUploading}
                  onSuccess={onUploadSuccess}
                  onError={onUploadError}
                  onDelete={onUploadDelete}
                  limit={{ size: 800, width: 1080, height: 432 }}
                />
              </Col>
            </Row>
          </Col>
        </Row>
        {this.props.isPhysical ? (
          <FormItem
            // label="互动地区"
            label="举办地区"
            {...adaptiveLayout.formWrapper}>
            <Select
              style={{ width: 130, marginRight: 15 }}
              onChange={this.handleProvinceChange.bind(this)()}
              disabled={preview}
              placeholder="请选择"
              value={
                this.state.provinceData.length !== 0
                  ? content.offline.province
                  : content.offline.province_name
              }
            >
              {provinceOptions}
            </Select>
            <Select
              style={{ width: 130, marginRight: 15 }}
              onChange={this.handleCityChange.bind(this)()}
              disabled={preview}
              onFocus={this.onFocusCity.bind(this)}
              placeholder="请选择"
              value={
                this.state.cityData.length !== 0
                  ? content.offline.city
                  : content.offline.city_name
              }
            >
              {cityOptions}
            </Select>
            <Select
              style={{ width: 130, marginRight: 15 }}
              onChange={this.handleDistrictChange.bind(this)()}
              disabled={preview}
              onFocus={this.onFocusDistrict.bind(this)}
              placeholder="请选择"
              value={
                this.state.districtData.length !== 0
                  ? content.offline.district
                  : content.offline.district_name
              }
            >
              {districtOptions}
            </Select>
          </FormItem>
        ) : (
            ""
          )}

        <FormItem
          {...adaptiveLayout.formWrapper}
          // label="互动栏目"
          label="所属栏目"
        >
          {getFieldDecorator(formFields.activityColumn, {
            initialValue: activityFormState.activityColumn,
            rules: [
              {
                required: true,
                // message: "请选择互动栏目"
                message: "请选择所属栏目"
              }
            ]
          })(
            <Cascader
              changeOnSelect
              onChange={handleEvents.selectedWorkFlow}
              disabled={preview}
              // placeholder="请选择互动栏目"
              placeholder="请选择所属栏目"
              options={activityFormState.activityColumnList}
              style={{ maxWidth: 420 }}
            />
          )}
        </FormItem>
        <FormItem
          label="互动标签"
          {...adaptiveLayout.formWrapper}
          wrapperCol={{ md: { span: 18 }, lg: { span: 17 }, xl: { span: 13 } }}
        >
          <InputTips
            max={200}
            container={container}
            text={getFieldValue(formFields.keyword_tags)}
          >
            {getFieldDecorator(formFields.keyword_tags, {
              initialValue: activityFormState.keyword_tags,
              rules: [
                {
                  max: 200,
                  message: "最多输入200个汉字"
                },
                {
                  required: true,
                  message: "请输入互动标签"
                }
              ]
            })(
              <Input
                disabled={preview}
                placeholder="用于系统推荐联想，多个标签用逗号分隔"
                style={{ maxWidth: 420 }}
              />
            )}
          </InputTips>
        </FormItem>
        <FormItem
          {...adaptiveLayout.formWrapper}
          // label="互动主办单位"
          label="主办单位"
          className="custom-margin-top-none"
        >
          {/*unescape(this.state.orgName)*/}
          
          { activityFormState.eventOrganizationDepartment && activityFormState.eventOrganizationDepartment.id ?   unescape(activityFormState.eventOrganizationDepartment.name) : unescape(this.state.orgName)
          }

        </FormItem>
        {this.props.isPhysical
          ? ""
          : // <FormItem {...adaptiveLayout.formWrapper} label="互动组织部门">
          //   <InputTips
          //     max={30}
          //     container={container}
          //     text={getFieldValue(formFields.eventOrganizationDepartment)}>
          //     {getFieldDecorator(formFields.eventOrganizationDepartment, {
          //       initialValue: activityFormState.eventOrganizationDepartment.name,
          //       rules: [{
          //         max: 30,
          //         message: '最多输入30个汉字'
          //       }, {
          //         required: true, message: '请输入互动组织部门'
          //       }]
          //     })(
          //       <AutoComplete
          //         allowClear
          //         backfill={false}
          //         disabled={preview}
          //         placeholder="请输入互动组织部门"
          //         filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) >= 0}
          //         onChange={handleEvents.changeDepartment}>
          //         {
          //           activityFormState.departmentData.map((item => <AutoComplete.Option key={item.department_id}>{item.name}</AutoComplete.Option>))
          //         }
          //       </AutoComplete>
          //     )}
          //   </InputTips>
          // </FormItem>
          ""}
        {this.props.isPhysical ? (
          ""
        ) : (
            <Row className="ant-form-item">
              <Col
                {...adaptiveLayout.formWrapper.labelCol}
                className="ant-form-item-label"
              >
                {/* <label title="互动时间">互动时间</label> */}
                <label title="举办时间">举办时间</label>
              </Col>
              <Col {...adaptiveLayout.formWrapper.wrapperCol}>
                <table>
                  <tbody>
                    <tr>
                      <td>
                        {getFieldDecorator(formFields.activityTimeStartTime, {
                          initialValue:
                            (activityFormState.activityTime.start &&
                              moment(replaceAdd(activityFormState.activityTime.start))) ||
                            null,
                          rules: [
                            {
                              required: false,
                              // message: "请选择互动开始时间"
                              message: "请选择开始时间"
                            }
                          ]
                        })(
                          <DatePicker
                            showTime={{ defaultValue: moment("00:00:00", "HH:mm:ss") }}
                            disabled={activityTimeStartDisabled || preview}
                            locale={DatePickerLocale}
                            format="YYYY-MM-DD HH:mm:ss"
                            // placeholder="请选择互动开始时间"
                            placeholder="请选择开始时间"
                            disabledDate={handleEvents.disabledStartDate}
                            onOpenChange={handleEvents.handleStartOpenChange}
                          />
                        )}
                      </td>
                      <td width={40} align="center">
                        至
                    </td>
                      <td>
                        {getFieldDecorator(formFields.activityTimeEndTime, {
                          initialValue:
                            (activityFormState.activityTime.end &&
                              moment(replaceAdd(activityFormState.activityTime.end))) ||
                            null,
                          rules: [
                            {
                              required: false,
                              // message: "请选择互动结束时间"
                              message: "请选择结束时间"
                            }
                          ]
                        })(
                          <DatePicker
                            showTime={{ defaultValue: moment("23:59:59", "HH:mm:ss") }}
                            disabled={activityTimeEndDisabled || preview}
                            locale={DatePickerLocale}
                            open={activityFormState.activityTimeEndOpen}
                            format="YYYY-MM-DD HH:mm:ss"
                            // placeholder="请选择互动结束时间"
                            placeholder="请选择结束时间"
                            disabledDate={handleEvents.disabledEndDate}
                            onOpenChange={handleEvents.handleEndOpenChange}
                          />
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>
                        {getFieldDecorator(formFields.activityTimeStartDisabled, {
                          initialValue:
                            activityFormState.activityTime.startDisabled || false,
                          valuePropName: "checked"
                        })(
                          <Checkbox disabled={preview}>不设置开始时间</Checkbox>
                        )}
                      </td>
                      <td>&nbsp;</td>
                      <td>
                        {getFieldDecorator(formFields.activityTimeEndDisabled, {
                          initialValue:
                            activityFormState.activityTime.endDisabled || false,
                          valuePropName: "checked"
                        })(
                          <Checkbox disabled={preview}>不设置结束时间</Checkbox>
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </Col>
            </Row>
          )}
      </div>
    );
  }
}

ActivityBase.propTypes = {
  //预览模式
  preview: PropTypes.bool,

  //antd表单对象
  form: PropTypes.object.isRequired,

  //表单容器元素
  container: PropTypes.any.isRequired,

  //表单字段
  formFields: PropTypes.shape({
    //互动长标题
    longTitle: PropTypes.string.isRequired,
    //互动短标题
    shortTitle: PropTypes.string.isRequired,
    //自定义标记
    customizeTag: PropTypes.string.isRequired,
    //互动栏目
    activityColumn: PropTypes.string.isRequired,
    //互动组织部门
    eventOrganizationDepartment: PropTypes.string.isRequired,
    //互动开始时间
    activityTimeStartTime: PropTypes.string.isRequired,
    //互动结束时间
    activityTimeStartDisabled: PropTypes.string.isRequired,
    //不设置开始时间
    activityTimeEndTime: PropTypes.string.isRequired,
    //不设置结束时间
    activityTimeEndDisabled: PropTypes.string.isRequired
  }).isRequired,

  //表单默认数据
  emuns: PropTypes.shape({
    //自定义标记
    customizeTag: PropTypes.shape({
      recommend: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired,
        previewUri: PropTypes.any.isRequired,
        description: PropTypes.string.isRequired,
        note: PropTypes.string
      }).isRequired,
      focused: PropTypes.shape({
        label: PropTypes.string.isRequired,
        value: PropTypes.number.isRequired,
        previewUri: PropTypes.any.isRequired,
        description: PropTypes.string.isRequired,
        note: PropTypes.string
      }).isRequired
    }).isRequired
  }).isRequired,

  //表单model
  activityFormState: PropTypes.shape({
    //互动长标题
    longTitle: PropTypes.string,
    //互动短标题
    shortTitle: PropTypes.string,
    //选中的互动栏目列表
    activityColumn: PropTypes.arrayOf(PropTypes.number),
    //互动栏目列表
    activityColumnList: PropTypes.arrayOf(PropTypes.object).isRequired,
    //互动时间范围关联
    activityTimeEndOpen: PropTypes.bool.isRequired,
    //自定义标记
    customizeTag: PropTypes.arrayOf(PropTypes.number),
    //上传图片
    thumbnail: PropTypes.shape({
      app_lists_img: PropTypes.string, //app列表缩略图
      app_theme_img: PropTypes.string, //app主题图
      app_focus_img: PropTypes.string //app聚焦图
    }).isRequired,
    //互动组织部门
    eventOrganizationDepartment: PropTypes.shape({
      name: PropTypes.string
    }),
    departmentData: PropTypes.arrayOf(PropTypes.object).isRequired,
    activityTime: PropTypes.shape({
      start: PropTypes.any, //互动开始
      end: PropTypes.any, //互动结束
      startDisabled: PropTypes.bool, //不设置开始时间
      endDisabled: PropTypes.bool //不设置结束时间
    })
  }).isRequired,

  //表单处理的事件合集
  handleEvents: PropTypes.shape({
    // NOTE:查看链接自定义日期范围选择 https://ant.design/components/date-picker-cn/
    selectedWorkFlow: PropTypes.func.isRequired,
    disabledStartDate: PropTypes.func.isRequired,
    handleStartOpenChange: PropTypes.func.isRequired,
    disabledEndDate: PropTypes.func.isRequired,
    handleEndOpenChange: PropTypes.func.isRequired,

    onUploading: PropTypes.func.isRequired,
    onUploadSuccess: PropTypes.func.isRequired,
    onUploadError: PropTypes.func.isRequired,
    onUploadDelete: PropTypes.func.isRequired,
    //互动部门内容变化的时候
    changeDepartment: PropTypes.func.isRequired
  }).isRequired
};

ActivityBase.defaultProps = {
  preview: false,
  isPhysical: false
};

export default ActivityBase;
