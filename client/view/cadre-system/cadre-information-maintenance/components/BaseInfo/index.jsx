import React, {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useMemo,
  useImperativeHandle,
} from "react";
import {
  Form,
  Input,
  Row,
  Col,
  Select,
  DatePicker,
  Radio,
  Cascader,
  Upload,
  Modal,
  message,
  Checkbox,
} from "antd";
import "./index.less";
import avatarPng from "../../images/avatar.png";
import moment from "moment";
import { getQuery } from "client/tool/util";
const FormItem = Form.Item;
const { Option } = Select;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;
import CardBox from "../../../components/CardBox";
import NonPublicOwnership from "./NonPublicOwnership";
import { checkList } from "../../../config/index";
import Becatogery from "./Becatogery";
import {
  queryByCode,
  queryDicitemArea,
  queryJobInfo,
  uploadAvatar,
  checkPhone,
  checkCertNumber,
} from "client/apis/cadre-portrait";
import { uploadFile } from "client/apis/file";
import { getUrlQuery } from "client/tool/util";
const cdn = process.env.cdn;

const _consoleError = console.error;

function index(props, ref) {
  // 关闭 ant design 警告, 该组建销毁自动复原
  // console.error = (error) => {
  //   if (
  //     error ===
  //     "Warning: You cannot set a form field before rendering a field associated with the value."
  //   )
  //     return;

  //   _consoleError(error);
  // };

  const { form, data, disabled, history } = props;

  const { org_id, user_id: _user_id } = getUrlQuery();

  const { category = [], has_cadre } = JSON.parse(
    sessionStorage.getItem("cadre_management") || "{}"
  );

  const [categoryList, setCategoryList] = useState(category);

  const [categoryVisible, setCategoryVisible] = useState(false);

  const updateFunctionRef = useRef({
    categoryList,
    setCategoryList,
  });
  const noPublicOwnershipRef = useRef();

  const religion = category.includes(1);

  const democratic = category.includes(2);

  const nonPartisan = category.includes(3);

  const area = useRef();

  const {
    getFieldDecorator,
    validateFields,
    setFieldsValue,
    getFieldsValue,
    getFieldValue,
  } = form;

  const [positionList, setPositionList] = useState([]);

  const [codeMap, setCodeMap] = useState({
    cadreTypeOption: [],
    ethnicOption: [],
    politicalTypeOption: [],
    currentRankOption: [],
    sourceOption: [],
    religionBelief: [],
    religiousBelief: [],
    rendingOption: [],
    authenticationIdentityOption: [],
    marriageOption: [],
    healthOption: []
  });

  const [areaTree, setAreaTree] = useState([]);

  useImperativeHandle(ref, () => ({
    getParams: getBelongCategoryEntityList,
  }));

  const wrapperCol = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
  };
  const wrapperCol1 = {
    labelCol: { span: 12 },
    wrapperCol: { span: 12 },
  };

  useEffect(() => {
    if (!_user_id) {
      form.setFieldsValue({
        has_cadre: has_cadre,
        belong_category: category,
      });
    }
  }, [_user_id]);

  useEffect(() => {
    initData();
  }, [data]);

  const initData = async () => {
    if (!data) return;

    let {
      birthday,
      current_job_time,
      current_rank_time,
      joining_time,
      work_time,
      native,
      birthplace,
      religion_member = {},
      party_member = {},
      belong_category_entity_list,
      belong_category,
      gender,
    } = data;
    const timeTransform = (date) => {
      return date ? moment(date) : undefined;
    };
    const _gender = gender
      ? gender === "未知"
        ? undefined
        : gender === "男"
          ? "1"
          : "2"
      : undefined;
    // 所属类型字符串转Array
    belong_category = Array.isArray(belong_category)
      ? belong_category
      : JSON.parse(belong_category || "[]");

    let formFields = {
      ...data,
      birthday: moment(birthday),
      current_job_time: moment(current_job_time),
      current_rank_time: moment(current_rank_time),
      joining_time: joining_time ? moment(joining_time) : undefined,
      work_time: work_time ? moment(work_time) : undefined,
      native: native
        ? native.split("-").map((item) => Number(item))
        : undefined,
      birthplace: birthplace
        ? birthplace.split("-").map((item) => Number(item))
        : undefined,
      current_job_text: data.current_job,
      gender: _gender,
      belong_category,
    };

    await form.setFieldsValue({
      belong_category,
    });
    let fields10;
    belong_category_entity_list?.map(async (item) => {
      const { category } = item;

      let _json = item?.belong_category_json || [];

      const { categoryList, setCategoryList } = updateFunctionRef.current;

      if (!categoryList.includes(item?.belong_category)) {
        setCategoryList([...categoryList, item?.belong_category]);
      }
      delete _json.belong_category_id;

      getFieldDecorator(`category_${category}`, {
        initialValue: item.belong_category_id,
      });

      _json = convertTimeValues(_json);

      if (category === 4) {
        const { achievement_impact } = _json;

        _json.achievement_impact_4 = achievement_impact;
      }

      if (category === 9) {
        const { achievement_impact } = _json;

        _json.achievement_impact_9 = achievement_impact;
      }
      // 10的数据单独处理
      if (category === 10) {
        const { email } = _json;
        _json.email_10 = email;

        fields10 = {
          ..._json,
          [`category_${item.category}`]: item.belong_category_id,
        };

        return;
      }

      formFields = {
        ...formFields,
        ..._json,
        [`category_${item.category}`]: item.belong_category_id,
      };
    });

    // delete formFields?.belong_category_entity_list;

    await form.setFieldsValue(formFields);

    formFields &&
      (await noPublicOwnershipRef.current?.setFieldsValue?.(fields10));

    setCategoryList(belong_category);

    initAreaKey(native);

    formFields?.religion_belief &&
      initCode(formFields?.religion_belief, "religiousBelief");
    if (birthplace) {
      initAreaKey(birthplace);
    }
  };

  useEffect(() => {
    initCode(2001, "cadreTypeOption");
    initCode(1004, "ethnicOption");
    initCode(1013, "politicalTypeOption");
    initCode(2003, "currentRankOption");
    initCode(96140, "sourceOption");
    initCode(1014, "religionBelief");
    initCode(96302, "authenticationIdentityOption");
    initCode(96301, "marriageOption");
    initCode(95101, "healthOption");

    initAreaCode({
      type: 1,
    });

    return () => {
      console.error = _consoleError;
    };
  }, []);

  function isDateString(str) {
    const datePatterns = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
      /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
      /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
      /^\d{2}-\d{2}-\d{4}$/, // DD-MM-YYYY
      /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|([+-]\d{2}:\d{2}))?$/, // ISO 8601
      /^\d{13}$/, // Unix timestamp in milliseconds
      /^\d{10}$/, // Unix timestamp in seconds
    ];

    return datePatterns.some((pattern) => pattern.test(str));
  }
  const convertTimeValues = (obj) => {
    const timeFormats = ["YYYY-MM-DD", "YYYY.MM.DD", "YYYY/MM/DD"];

    const result = {};

    for (const [key, value] of Object.entries(obj)) {
      let isTimeFormat = false;

      if (typeof value === "string" || typeof value === "number") {
        if (isDateString(value)) {
          result[key] = moment(value);
          isTimeFormat = true;
          continue;
        }
      }

      if (!isTimeFormat) {
        result[key] = value;
      }
    }

    return result;
  };

  // 所属类别的字段所有字段
  const getBelongCategoryEntityList = () => {
    const keyMapFunc = {
      1: getBelongCategoryJSON_1,
      2: getBelongCategoryJSON_2,
      3: getBelongCategoryJSON_3,
      4: getBelongCategoryJSON_4,
      5: getBelongCategoryJSON_5,
      6: getBelongCategoryJSON_678,
      7: getBelongCategoryJSON_678,
      8: getBelongCategoryJSON_678,
      9: getBelongCategoryJSON_9,
      10: getBelongCategoryJSON_10,
    };

    const res = [];

    getFieldValue("belong_category").forEach((item) => {
      if (keyMapFunc[item]) {
        res.push(keyMapFunc[item]());
      }
    });

    return res;
  };
  const dealTime = (_obj, time) => {
    if (_obj[time]) {
      _obj[time] =
        _obj[time]?.format?.("YYYY-MM-DD") ||
        moment(_obj[time]).format("YYYY-MM-DD");
    }
  };
  // 宗教
  const getBelongCategoryJSON_1 = () => {
    const key = [
      "religion_belief",
      "religion_identity",
      "religion_time",
      "category_1",
    ];

    getBelongCategoryJSON_1.key = key;

    const belong_category_json = getFieldsValue(key);

    dealTime(belong_category_json, "religion_time");

    return {
      belong_category_id: belong_category_json.category_1,
      category: 1,
      belong_category_json,
    };
  };
  // 党派
  const getBelongCategoryJSON_2 = () => {
    const key = [
      "party_name",
      "join_time",
      "second_party_name",
      "second_join_time",
      "category_2",
    ];

    getBelongCategoryJSON_2.key = key;

    const belong_category_json = getFieldsValue(key);
    dealTime(belong_category_json, "join_time");
    dealTime(belong_category_json, "second_join_time");
    return {
      belong_category_id: belong_category_json.category_2,
      category: 2,
      belong_category_json,
    };
  };
  // 护照
  const getBelongCategoryJSON_3 = () => {
    const key = ["certification_unit", "certification_time", "category_3"];

    getBelongCategoryJSON_3.key = key;

    const belong_category_json = getFieldsValue(key);

    dealTime(belong_category_json, "certification_time");

    return {
      belong_category_id: belong_category_json.category_3,
      category: 3,
      belong_category_json,
    };
  };

  // 社会兼职
  const getBelongCategoryJSON_4 = () => {
    const key = [
      "sideline",
      "performance",
      "achievement_impact_4",
      "category_4",
    ];

    getBelongCategoryJSON_4.key = key;

    const belong_category_json = getFieldsValue(key);

    belong_category_json.achievement_impact =
      belong_category_json.achievement_impact_4;

    delete belong_category_json.achievement_impact_4;
    return {
      belong_category_id: belong_category_json.category_4,
      category: 4,
      belong_category_json,
    };
  };
  // 出国时间
  const getBelongCategoryJSON_5 = () => {
    const key = ["abroad_time", "repatriation_time"];
    getBelongCategoryJSON_5.key = key;
    const belong_category_json = getFieldsValue(key);

    dealTime(belong_category_json, "abroad_time");
    dealTime(belong_category_json, "repatriation_time");

    return {
      belong_category_id: belong_category_json.belong_category_id,
      category: 5,
      belong_category_json,
    };
  };

  // 港澳台通行证
  const getBelongCategoryJSON_678 = () => {
    const key = [
      "pass_check",
      "pass_check_type",
      "category_6",
      "category_7",
      "category_8",
    ];
    getBelongCategoryJSON_678.key = key;
    const belong_category_json = getFieldsValue(key);

    const { category_6, category_7, category_8 } = belong_category_json;
    const belong_category_id = category_6 || category_7 || category_8;
    return {
      belong_category_id,
      category: 6,
      belong_category_json,
    };
  };
  // 华侨、归侨及侨眷
  const getBelongCategoryJSON_9 = () => {
    const key = [
      "category_9",
      "bridge_not",
      "passport",
      "achievement_impact_9",
      "activity",
    ];
    getBelongCategoryJSON_9.key = key;

    const belong_category_json = getFieldsValue(key);

    belong_category_json.achievement_impact =
      belong_category_json.achievement_impact_9;

    delete belong_category_json.achievement_impact_9;

    return {
      belong_category_id: belong_category_json.category_9,
      category: 9,
      belong_category_json,
    };
  };

  function convertMomentToFormattedDate(obj) {
    const result = {};

    for (const [key, value] of Object.entries(obj)) {
      if (moment.isMoment(value)) {
        result[key] = value.format("YYYY-MM-DD");
      } else {
        result[key] = value;
      }
    }

    return result;
  }
  // 非公有制
  const getBelongCategoryJSON_10 = () => {
    const noPublicOwnership = noPublicOwnershipRef.current.getFieldsValue();
    const { email_10 } = noPublicOwnership;

    noPublicOwnership.email = email_10;

    return {
      category: 10,
      belong_category_id: noPublicOwnership.belong_category_id,
      belong_category_json: convertMomentToFormattedDate(noPublicOwnership),
    };
  };

  const initPosition = () => {
    queryJobInfo({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setPositionList(res.data.data);
      }
    });
  };
  const initAreaKey = async (native) => {
    await initAreaCode({
      type: 1,
    });
    if (!native) return;

    const nativeList = native.split("-");

    nativeList.forEach((key, index) => {
      loadData([
        {
          adcode: Number(key),
          type: index + 1,
        },
      ]);
    });
  };
  const initCode = async (code, key) => {
    const res = await queryByCode({ code });
    if (res.data.code === 0) {
      setCodeMap((codeMap) => {
        codeMap[key] = res.data.data;

        return { ...codeMap };
      });
    }
  };

  //
  const initAreaCode = async ({ type, adcode }) => {
    const res = await queryDicitemArea({ type, adcode });
    if (res.data.code === 0) {
      const data = res.data.data.map((item) => {
        return { ...item, isLeaf: false, type: 1 };
      });
      setAreaTree(data);

      area.current = data;
    }
  };

  const loadData = async (selectedOptions, type) => {
    const selectOption = selectedOptions[selectedOptions.length - 1];

    const res = await queryDicitemArea({
      type: selectOption.type + 1,
      adcode: selectOption.adcode,
    });

    if (res.data.code === 0) {
      // 根据adcode找寻下级

      if (!res.data.data.length) {
        delete selectOption.isLeaf;

        setAreaTree([...areaTree]);

        return;
      }

      const findByCode = (areaTree, children) => {
        areaTree.forEach((item) => {
          if (item.children) {
            findByCode(item.children, children);
          } else {
            if (item.adcode === selectOption.adcode) {
              item.children = children.map((item) => {
                return { ...item, isLeaf: false, type: selectOption.type + 1 };
              });
            }
          }
        });
      };
      findByCode(area.current, res.data.data);

      setAreaTree([...area.current]);
    }
  };

  const onChange = () => {
    console.log("Onchange");
  };

  const onUpload = async (file) => {
    const formdata = new FormData();

    formdata.append("avatar", file);

    formdata.append("head_url", data ? data.head_url : undefined);

    const res = await uploadAvatar(formdata);

    if (res.data.code === 0) {
      const data = res.data.data;
      setFieldsValue({
        head_url: data,
      });
    }

    return false;
  };

  const fieldNames = {
    label: "area_name",
    value: "adcode",
    children: "children",
  };

  const getAvatar = (head_url) => {
    return head_url ? `${cdn}/fr_img/${head_url}` : avatarPng;
  };
  const children = positionList.map((item) => {
    return (
      <Option
        onClick={() => {
          const params = getFieldsValue();
          setFieldsValue({
            has_divided: item.has_divided,
            current_job_text: item.job_name,
            job_id: item.job_id,
          });
        }}
        value={String(item.job_id)}
      >
        {item.job_name}
      </Option>
    );
  });

  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  const ConfirmModal = ({ title, children }) => {
    return (
      <Modal
        visible={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        onOk={() => setConfirmModalVisible(false)}
        title={title}
      >
        {children}
      </Modal>
    );
  };

  const onValidateFields = (fields) => {
    validateFields(fields, (err, values) => { });
  };

  const phoneValidate = (rule, value, callback) => {
    if (!value?.trim()) {
      return callback("请输入手机号");
    }
    if (!/^1[3456789]\d{9}$/.test(value)) {
      return callback("请输入正确的手机号");
    }

    // checkPhone(value).then(res => {
    //   if (res.data.code === 0) {
    //     return callback()
    //   } else {
    //     callback("手机号已存在!!")
    //   }
    // })
    callback();
  };
  const certNumberValidate = (rule, value, callback) => {
    // 身份证校验

    if (!value?.trim()) {
      return callback("请输入身份证号");
    }

    if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
      return callback("请输入正确的身份证号");
    }

    // checkCertNumber(value).then(res => {
    //   if (res.data.code === 0) {
    //     return callback()
    //   } else {
    //     callback("身份证号已存在!!")
    //   }
    // })
    callback();
  };

  const partyName = [
    "民革党员",
    "民盟盟员",
    "民建会员",
    "民进会员",
    "农工党党员",
    "致公党党员",
    "九三学社社员",
    "台盟盟员",
  ];

  const [zongjiao, setZongjiao] = useState([]);

  const [xinyang, setXinyang] = useState([]);

  const getTitle = () => {
    const category = category || [];
    return checkList
      .filter((_i) => {
        return category.includes(_i.key);
      })
      .map((item) => item.title)
      .join();
  };

  return (
    <div className="base-info-box">
      <div className="cim-content-base cim-content-common">
        <div className="content-card-title">基础信息</div>
        <div className="form-box">
          <Row span={24}>
            <Col span={8}>
              <FormItem label="是否干部" {...wrapperCol} required colon>
                {getFieldDecorator("has_cadre", {
                  rules: [{ required: true, message: "请输入选择" }],
                  onChange: (e) => {
                    console.log("🚀 ~ index ~ val:", e.target.value)
                    setFieldsValue({
                      has_cadre: e.target.value, // 传入onChange优先级大于getFieldDecorator onChange
                      'belong_category': []
                    })
                  }
                })(
                  <Radio.Group
                  // onChange={() => {
                  // 选择是否干部需要清空类别重新选择
                  // setFieldsValue({
                  //   belong_category: [],
                  // });
                  // }}
                  >
                    <Radio value={0}>是</Radio>
                    <Radio value={1}>否</Radio>
                  </Radio.Group>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="所属类别" {...wrapperCol} required colon>
                <div>
                  {getFieldDecorator(
                    "belong_category",
                    {}
                  )(<Becatogery getFieldsValue={getFieldsValue} />)}
                </div>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="层级" {...wrapperCol} required colon>
                {getFieldDecorator("area_level", {
                  rules: [{ required: true, message: "请输入选择" }],
                  initialValue: data ? data.area_level : 0,
                })(
                  <Radio.Group>
                    <Radio value={1}>市级</Radio>
                    <Radio value={2}>区县</Radio>
                  </Radio.Group>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row span={24}>
            {getFieldDecorator("user_id", {})(<Input type="hidden" />)}
            {getFieldDecorator("has_divided")(<Input type="hidden" />)}
            {getFieldDecorator("head_url")(<Input type="hidden" />)}
            <Col span={9}>
              <FormItem label="姓名" {...wrapperCol} required colon>
                {getFieldDecorator("name", {
                  rules: [{ required: true, message: "请输入姓名" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="手机号" {...wrapperCol} required colon>
                {getFieldDecorator("phone", {
                  rules: [
                    {
                      validator: phoneValidate,
                    },
                  ],
                })(<Input placeholder="请填写手机号" disabled={disabled} />)}
                {/* <span className="one-test" onClick={() => {
                  onValidateFields(['phone']);
                }}>一键检测</span> */}
              </FormItem>
              {/* <FormItem label="干部类别" {...wrapperCol} required colon>
            {getFieldDecorator("category", {
              rules: [{ required: true, message: "请选择干部类别" }],
            })(
              <Select
                style={{ width: 120 }}
                placeholder="请输入"
                disabled={disabled}
              >
                {codeMap.cadreTypeOption.map((item) => (
                  <Option value={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem> */}
              <FormItem label="籍贯" {...wrapperCol} required colon>
                {getFieldDecorator("native", {
                  rules: [{ required: true, message: "请选择籍贯" }],
                })(
                  <Cascader
                    disabled={disabled}
                    placeholder="请输入"
                    fieldNames={fieldNames}
                    options={areaTree}
                    loadData={(option) => {
                      loadData(option, 1);
                    }}
                    changeOnSelect
                  />
                )}
              </FormItem>
              <FormItem label="民族" {...wrapperCol} required colon>
                {getFieldDecorator("ethnic", {
                  rules: [{ required: true, message: "请选择民族" }],
                })(
                  <Select
                    style={{ width: 120 }}
                    placeholder="请输入"
                    disabled={disabled}
                  >
                    {codeMap.ethnicOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>

              <FormItem label="熟悉专业和特长" {...wrapperCol} colon>
                {getFieldDecorator("profession_specialty", {
                  rules: [{ required: false, message: "请输入熟悉专业和特长" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="专业技术职务" {...wrapperCol} colon>
                {getFieldDecorator("technical_position", {
                  rules: [{ required: false, message: "请输入专业技术职务" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="认定身份" {...wrapperCol} colon>
                {getFieldDecorator("authentication_identity", {
                  rules: [{ required: false, message: "请输入专业技术职务" }],
                })(
                  <Select placeholder="请输入" disabled={disabled}>
                    {codeMap.authenticationIdentityOption.map((item) => (
                      <Option value={Number(item.op_key)}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
              <FormItem label="健康状况" {...wrapperCol} colon>
                {getFieldDecorator("health", {
                  rules: [{ required: false, message: "请输入专业技术职务" }],
                })(
                  <Select placeholder="请输入" disabled={disabled}>
                    {codeMap.healthOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
              <FormItem label="通讯地址" {...wrapperCol} colon>
                {getFieldDecorator("family_address", {
                  rules: [{ required: false, message: "请输入通讯地址" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="座机" {...wrapperCol} colon>
                {getFieldDecorator("landline", {
                  rules: [{ required: false, message: "请输入座机" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>

              <FormItem label="享受国务院津贴" {...wrapperCol} colon>
                {getFieldDecorator("has_enjoy_allowance", {
                  rules: [{ required: false, message: "请选择享受国务院津贴" }],
                })(
                  <Radio.Group disabled={disabled}>
                    <Radio value={0}>是</Radio>
                    <Radio value={1}>否</Radio>
                  </Radio.Group>
                )}
              </FormItem>
              {/* <FormItem label="入党时间" {...wrapperCol} colon>
                {getFieldDecorator("joining_time", {
                  rules: [{ required: false, message: "请选择入党时间" }],
                })(<MonthPicker disabled={disabled} placeholder="请输入" />)}
              </FormItem> */}
              <FormItem label="人才类别" {...wrapperCol} colon>
                {getFieldDecorator("talent_category", {
                  rules: [{ required: false, message: "请选择参加工作时间" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="兴趣爱好" {...wrapperCol} colon>
                {getFieldDecorator("hobby", {
                  rules: [{ required: false, message: "请输入兴趣爱好" }],
                })(
                  <Input placeholder="多个之间用，分隔" disabled={disabled} />
                )}
              </FormItem>
              <FormItem label="派选区县" {...wrapperCol} colon>
                {getFieldDecorator("select_region", {
                  rules: [{ required: false, message: "请输入兴趣爱好" }],
                })(
                  <Input placeholder="多个之间用，分隔" disabled={disabled} />
                )}
              </FormItem>
              <FormItem label="曾用名" {...wrapperCol} colon>
                {getFieldDecorator("old_name", {
                  rules: [{ required: false, message: "请输入兴趣爱好" }],
                })(
                  <Input placeholder="请输入" disabled={disabled} />
                )}
              </FormItem>

            </Col>
            <Col span={9}>
              <FormItem label="政治面貌" {...wrapperCol} required colon>
                {getFieldDecorator("political_type", {
                  rules: [{ required: true, message: "请选择政治面貌" }],
                })(
                  <Select disabled={disabled} placeholder="请选择">
                    {codeMap.politicalTypeOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>

              <FormItem label="性别" {...wrapperCol} required colon>
                {getFieldDecorator("gender", {
                  rules: [{ required: true, message: "请选择性别" }],
                })(
                  <Radio.Group placeholder="请输入" disabled={disabled}>
                    <Radio value={"1"}>男</Radio>
                    <Radio value={"2"}>女</Radio>
                  </Radio.Group>
                )}
              </FormItem>
              <FormItem label="身份证号" {...wrapperCol} required colon>
                {getFieldDecorator("cert_number", {
                  rules: [{ validator: certNumberValidate }],
                })(<Input placeholder="请输入身份证号" disabled={disabled} />)}
                {/* <span className="one-test" onClick={() => {
                  onValidateFields(["cert_number"])
                }}>一键检测</span> */}
              </FormItem>

              <FormItem label="出生年月" {...wrapperCol} required colon>
                {getFieldDecorator("birthday", {
                  rules: [{ required: true, message: "请选择出生年月" }],
                })(<DatePicker placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="出生地" {...wrapperCol} colon>
                {getFieldDecorator(
                  "birthplace",
                  {}
                )(
                  <Cascader
                    disabled={disabled}
                    placeholder="请输入"
                    fieldNames={fieldNames}
                    options={areaTree}
                    loadData={(option) => {
                      loadData(option, 2);
                    }}
                    // onChange={onChange}
                    changeOnSelect
                  />
                )}
              </FormItem>

              <FormItem label="技能证书和称号" {...wrapperCol} colon>
                {getFieldDecorator("skill_info", {
                  rules: [{ required: false, message: "请输入技能证书和称号" }],
                })(
                  <Input
                    disabled={disabled}
                    placeholder="如xx领域国家专家库人才"
                  />
                )}
              </FormItem>
              <FormItem label="参加工作时间" {...wrapperCol} colon>
                {getFieldDecorator("work_time", {
                  rules: [{ required: false, message: "请选择参加工作时间" }],
                })(<DatePicker placeholder="请选择" disabled={disabled} />)}
              </FormItem>
              <FormItem label="婚姻状况" {...wrapperCol} colon>
                {getFieldDecorator("marriage", {
                  rules: [{ required: false, message: "请输入专业技术职务" }],
                })(
                  <Select placeholder="请输入" disabled={disabled}>
                    {codeMap.marriageOption.map((item) => (
                      <Option value={item.op_key}>{item.op_value}</Option>
                    ))}
                  </Select>
                )}
              </FormItem>
              <FormItem label="邮编" {...wrapperCol} colon>
                {getFieldDecorator("zip_code", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="传真" {...wrapperCol} colon>
                {getFieldDecorator("fax", {
                  rules: [{ required: false, message: "请选择参加工作时间" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="电子邮箱" {...wrapperCol} colon>
                {getFieldDecorator("email", {
                  rules: [{ required: false, message: "请选择参加工作时间" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="企业统一社会信用代码" {...wrapperCol} colon>
                {getFieldDecorator("credit_code", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
              <FormItem label="组别" {...wrapperCol} colon>
                {getFieldDecorator("user_group", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入组别" disabled={disabled} />)}
              </FormItem>
              <FormItem label="主要专长" {...wrapperCol} colon>
                {getFieldDecorator("main_expertise", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入组别" disabled={disabled} />)}
              </FormItem>
              <FormItem label="备注" {...wrapperCol} colon>
                {getFieldDecorator("notes", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入备注" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={6}>
              <div className="avatar-box">
                <div className="image">
                  <Upload
                    beforeUpload={onUpload}
                    disabled={disabled}
                    showUploadList={false}
                  >
                    <img src={getAvatar(getFieldValue("head_url"))} />
                    <div className="title">上传头像</div>
                  </Upload>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
      {getFieldValue("belong_category")?.includes?.(1) && (
        <div
          className="cim-content-base cim-content-common"
          key={getFieldValue("belong_category")}
        >
          <div className="content-card-title">宗教界人士</div>
          <div className="form-box">
            <Row gutter={24}>
              <Col span={8}>
                {getFieldDecorator("religious_id", {})(<Input hidden />)}
                <FormItem label="宗教信仰：" {...wrapperCol} colon>
                  {getFieldDecorator("religion_belief", {
                    rules: [{ required: false, message: "请输入宗教信仰" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {codeMap.religionBelief.map((item) => {
                        return (
                          <Select.Option
                            value={item.op_key}
                            onClick={() => {
                              initCode(item.op_key, "religiousBelief");
                            }}
                          >
                            {item.op_value}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="宗教身份：" {...wrapperCol} colon>
                  {getFieldDecorator("religion_identity", {
                    rules: [{ required: false, message: "请输入宗教身份" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {codeMap.religiousBelief.map((item) => {
                        return (
                          <Select.Option value={item.op_key}>
                            {item.op_value}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="入教时间：" {...wrapperCol} colon>
                  {getFieldDecorator("religion_time", {
                    rules: [{ required: false, message: "请选择入教时间	" }],
                  })(<DatePicker placeholder="请选择" disabled={disabled} />)}
                </FormItem>
              </Col>
            </Row>
          </div>
        </div>
      )}

      {getFieldValue("belong_category")?.includes?.(2) && (
        <div className="cim-content-base cim-content-common">
          <div className="content-card-title">民主党派成员</div>
          <div className="form-box">
            <Row gutter={24}>
              {getFieldDecorator("democratic_id", {})(<Input hidden />)}
              <Col span={6}>
                <FormItem label="党派" {...wrapperCol} colon>
                  {getFieldDecorator("party_name", {
                    rules: [{ required: false, message: "请选择党派" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {partyName.map((item) => (
                        <Select.Option value={item}>{item}</Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="党派加入时间" {...wrapperCol} colon>
                  {getFieldDecorator("join_time", {
                    rules: [{ required: false, message: "请选择党派加入时间" }],
                  })(<DatePicker placeholder="请选择" disabled={disabled} />)}
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="第二党派" {...wrapperCol} colon>
                  {getFieldDecorator("second_party_name", {
                    rules: [{ required: false, message: "请输入第二党派" }],
                  })(
                    <Select placeholder="请选择" disabled={disabled}>
                      {partyName.map((item) => (
                        <Select.Option value={item}>{item}</Select.Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="党派加入时间" {...wrapperCol} colon>
                  {getFieldDecorator("second_join_time", {
                    rules: [
                      { required: false, message: "请选择第二党派加入时间	" },
                    ],
                  })(<DatePicker placeholder="请选择" disabled={disabled} />)}
                </FormItem>
              </Col>
            </Row>
          </div>
        </div>
      )}
      {getFieldValue("belong_category")?.includes?.(3) && (
        <div className="cim-content-base cim-content-common">
          <div className="content-card-title">无党派人士</div>
          <div className="form-box">
            <Row gutter={24}>
              <Col span={8}>
                <FormItem label="认定时间" {...wrapperCol} colon>
                  {getFieldDecorator("certification_time", {
                    rules: [{ required: false, message: "请输入认定时间" }],
                  })(<DatePicker placeholder="请选择" disabled={disabled} />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="认定单位" {...wrapperCol} colon>
                  {getFieldDecorator("certification_unit", {
                    rules: [{ required: false, message: "请选择认定单位" }],
                  })(<Input placeholder="请选择" disabled={disabled} />)}
                </FormItem>
              </Col>
              {/* <Col span={8}>
                <FormItem label="双报到对象" {...wrapperCol} colon>
                  {getFieldDecorator("jion_party_time21", {
                    rules: [{ required: false, message: "请选择双报到对象" }],
                  })(
                    <Select>
                      <Select.Option value="是">是</Select.Option>
                      <Select.Option value="否">否</Select.Option>
                    </Select>
                  )}
                </FormItem>
              </Col> */}
            </Row>
          </div>
        </div>
      )}
      {getFieldValue("belong_category")?.includes?.(4) && (
        <CardBox title={"新的社会届次"}>
          <Row gutter={24}>
            <Col span={8}>
              <FormItem label="社会兼职" {...wrapperCol} colon>
                {getFieldDecorator("sideline", {
                  rules: [{ required: false, message: "请输入社会兼职" }],
                })(<Input placeholder="请选择" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="主要业绩" {...wrapperCol} colon>
                {getFieldDecorator("performance", {
                  rules: [{ required: false, message: "请选择主要业绩" }],
                })(<Input placeholder="请选择" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="主要社会成就及社会影响" {...wrapperCol} colon>
                {getFieldDecorator("achievement_impact_4", {
                  rules: [
                    {
                      required: false,
                      message: "请选择主要社会成就及社会影响",
                    },
                  ],
                })(<Input placeholder="请输入" />)}
              </FormItem>
            </Col>
          </Row>
        </CardBox>
      )}
      {getFieldValue("belong_category")?.includes?.(5) && (
        <CardBox title={"出国归国留学人员"}>
          <Row gutter={24}>
            <Col span={8}>
              <FormItem label="出国时间" {...wrapperCol} colon>
                {getFieldDecorator("abroad_time", {
                  rules: [{ required: false, message: "请选择出国时间" }],
                })(<DatePicker placeholder="请选择" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="归国时间" {...wrapperCol} colon>
                {getFieldDecorator("repatriation_time", {
                  rules: [{ required: false, message: "请输入认定单位" }],
                })(<DatePicker placeholder="请选择" disabled={disabled} />)}
              </FormItem>
            </Col>
          </Row>
        </CardBox>
      )}
      {getFieldValue("belong_category")?.find((item) => {
        if (item === 6 || item === 7 || item === 8) {
          getFieldDecorator("pass_check_type", { initialValue: item });
          return true;
        }
      }) && (
          <CardBox title={"香港同胞、澳门同胞"}>
            <Row gutter={24}>
              <Col span={8}>
                <FormItem label="港澳台通行证" {...wrapperCol} colon>
                  {getFieldDecorator("pass_check", {
                    rules: [{ required: false, message: "请输入" }],
                  })(<Input placeholder="请输入" disabled={disabled} />)}
                </FormItem>
              </Col>
            </Row>
          </CardBox>
        )}
      {getFieldValue("belong_category")?.includes?.(9) && (
        <CardBox title={"华侨、归侨及侨眷"}>
          <Row gutter={24}>
            <Col span={8}>
              <FormItem label="桥别" {...wrapperCol} colon>
                {getFieldDecorator("bridge_not", {
                  rules: [{ required: false, message: "请选择" }],
                })(<Input placeholder="请选择" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="护照" {...wrapperCol} colon>
                {getFieldDecorator("passport", {
                  rules: [{ required: false, message: "请输入护照" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="主要成就社会影响" {...wrapperCol} colon>
                {getFieldDecorator("achievement_impact_9", {
                  rules: [
                    { required: false, message: "请输入主要成就社会影响" },
                  ],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={8}>
              <Form.Item label="公益活动" {...wrapperCol}>
                {getFieldDecorator("activity", {
                  rules: [{ required: false, message: "请输入" }],
                })(<Input placeholder="请输入" disabled={disabled} />)}
              </Form.Item>
            </Col>
          </Row>
        </CardBox>
      )}
      {getFieldValue("belong_category")?.includes?.(10) && (
        <NonPublicOwnership ref={noPublicOwnershipRef} />
      )}

      <ConfirmModal title="提示">系统已有该人员，是否继续编辑！</ConfirmModal>
    </div>
  );
}

export default forwardRef(index);
