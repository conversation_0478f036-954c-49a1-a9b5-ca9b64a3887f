import React, {Component} from 'react';
import {connect} from 'dva';
import {Form, Modal, Button, TreeSelect, Spin } from 'antd';
import './index.less';

const FormItem = Form.Item;

class OrganizeCon extends Component {

    constructor(props) {
        super(props);
        this.state = {
            value: props.value || [],
            label: props.label || null,
            dataSource: props.dataSource || null,
            orgId: props.ordId || null
        };

        this.getTreeData = this.getTreeData.bind(this);
        this.onSelect = this.onSelect.bind(this);
        this.onSubmit = this.onSubmit.bind(this);
        this.onCancel = this.onCancel.bind(this);
    }
    async componentWillMount(){

        const { dispatch, organizeContent } = this.props;
        if(!this.state.dataSource && this.state.orgId){
            await dispatch({
                type: 'organizeContent/loadOrgTree',
                payload: {
                    orgId: this.state.orgId
                }
            });
            const { dataSource } =  organizeContent;
            this.setState({
                dataSource
            });
        }
    }
    componentWillReceiveProps(nextProps){
        if(this.props.label != nextProps.label){
            this.setState({
                label: nextProps.label
            })
        }
        if(this.props.value != nextProps.value) {
            this.setState({
                value: nextProps.value
            });
        }
        if(this.props.dataSource != nextProps.dataSource){
            this.setState({
                dataSource: nextProps.dataSource
            })
        }
    }

    // 递归生成树
    getTreeData(data){
        if(!data){
            return null;
        }
        return data.map(item => {
            if (item.children && item.children.length > 0) {
                return {
                    label: item.name,
                    value: item.organization_id + '',
                    key: item.organization_id + '',
                    children: this.getTreeData(item.children)
                }
            }

            return {
                label: item.name,
                value: item.organization_id + '',
                key: item.organization_id + ''
            }
        })
    }

    onSubmit() {
        const { onSubmit } = this.props;

        const label = this.state.label;
        const value = this.state.value;

        onSubmit && onSubmit({
            label, value
        });
    }

    onSelect(e, t) {

        const { onSelect } = this.props;
        const { props } = t;
        this.setState({
            value: props.value,
            label: props.title
        }, () => {
            onSelect && onSelect({
                value: props.value,
                label: props.title
            });
        });
    }

    onCancel() {
        const { onCancel } = this.props;

        onCancel && onCancel({
            value: this.state.value,
            label: this.state.label
        });
    }

    render() {

        const {
            title, visible, organizeContent
        } = this.props;
        const { isLoading } = organizeContent;

        return (
            <Modal title={title || '选择组织'}
                   visible={visible}
                   destroyOnClose={true}
                   footer={null}
                   onCancel={this.onCancel}
                   className="organize-select-modal"
            >
                <Spin spinning={isLoading}>
                    <Form layout={'inline'}>

                        <FormItem className="form-item-row" label='组织名称'>

                                <TreeSelect
                                    showSearch
                                    style={{ width: 200 }}
                                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                    treeData={this.getTreeData(this.state.dataSource)}
                                    treeDefaultExpandedKeys={[this.state.value + '']}
                                    treeNodeFilterProp="label"
                                    onSelect={this.onSelect}
                                    value={this.state.label || undefined}
                                    placeholder="请选择上级部门"
                                />

                        </FormItem>

                        <div className="bottom">

                            <Button type="primary" onClick={this.onSubmit}>确定</Button>
                            <Button type="button" className="cancelBtn" onClick={this.onCancel}>取消</Button>

                        </div>

                    </Form>
                </Spin>

            </Modal>
        )
    }
}

const mapStateToProps = ({organizeContent}) => ({organizeContent});
export default connect(
    mapStateToProps
)(Form.create()(OrganizeCon));