import React from 'react';
import {Table} from 'antd';
import EvalTopForm from "../components/eval-top-form";
import './index.less';

const TeamEval = () => {
    const columns = [
        {
            title: '所在单位',
            dataIndex: 'unitName',
            rowScope: 'row',
            width: 120,
        },
        {
            title: '干部姓名',
            dataIndex: 'teamName',
            rowScope: 'row',
            width: 120,
        },
        {
            title: '市管领导测评',
            children: [
                {title: '好', dataIndex: 'leaderGood'},
                {title: '较好', dataIndex: 'leaderBetter'},
                {title: '一般', dataIndex: 'leaderNormal'},
                {title: '差', dataIndex: 'leaderBad'},
                {title: '不了解', dataIndex: 'leaderUnknown'},
                {title: '总票数', dataIndex: 'leaderTotal'},
                {title: '得分', dataIndex: 'leaderScore'}
            ],
        },
        {
            title: '班子内部测评',
            children: [
                {
                    title: '学习能力',
                    children: [
                        {title: '好', dataIndex: 'unityGood'},
                        {title: '较好', dataIndex: 'unityBetter'},
                        {title: '一般', dataIndex: 'unityNormal'},
                        {title: '差', dataIndex: 'unityBad'},
                        {title: '不了解', dataIndex: 'unityUnknown'},
                        {title: '总票数', dataIndex: 'unityTotal'},
                        {title: '得分', dataIndex: 'unityScore'}
                    ]
                },
                {
                    title: '决策能力',
                    children: [
                        {title: '好', dataIndex: 'integrityGood'},
                        {title: '较好', dataIndex: 'integrityBetter'},
                        {title: '一般', dataIndex: 'integrityNormal'},
                        {title: '差', dataIndex: 'integrityBad'},
                        {title: '不了解', dataIndex: 'integrityUnknown'},
                    ]
                },
                {
                    title: '票数',
                    dataIndex: 'unityTotal2'
                },
                {
                    title: '得分',
                    dataIndex: 'unityScore2'
                }
            ]
        }
    ];
    const data = [
        {
            teamName: 'XX局',
            leaderGood: 10,
            leaderBetter: 5,
            // ...其他字段
            unityGood: 8,
            // ...团结度字段
            integrityGood: 7,
            unityTotal2: 20,
            unityScore2: 85,
        },
        {
            teamName: 'XX局2',
            leaderGood: 10,
            leaderBetter: 5,
            // ...其他字段
            unityGood: 8,
            // ...团结度字段
            integrityGood: 7,
            // ...廉洁度字段
        }
    ];
    const evalTopFormProps = {
        queryFields: [
            {
                label: '测评时间',
                field: 'time_range',
                type: 'RangePicker',
            },
            {
                label: '班子',
                field: 'team',
                type: 'Input',
            },
            {
                label: '干部',
                field: 'party_cadre',
                type: 'Input',
            }
        ],
        queryHandler: (values) => {},
        handleExport: () => {
        }
    }
    return (
        <div className="TeamEval-container">
            <EvalTopForm {...evalTopFormProps}></EvalTopForm>
            <Table
                columns={columns}
                dataSource={data}
                bordered
                size="middle"
                pagination={false}
                components={{
                    header: {
                        cell: ({children, ...props}) => (
                            <th {...props} style={{backgroundColor: '#fafafa', fontWeight: 600}}>
                                {children}
                            </th>
                        )
                    }
                }}
            />
        </div>
    )
}

export default TeamEval;