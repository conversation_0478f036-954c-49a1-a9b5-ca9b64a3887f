import React from "react";
import { Button, Form, Input, Select, Modal, message } from "antd";
import { delMy } from "apis/audit-question";

const { Item } = Form;
const { Option } = Select;
const search = (props) => {
  const {
    id,
    tabkey,
    initData,
    questionId,
    clearSearch,
    changeSearch,
    resetQuestionId,
    form: { getFieldDecorator, resetFields, getFieldsValue },
  } = props;

  const onHandleSubmit = () => {
    changeSearch({ ...getFieldsValue() });
  };

  const onHandleReset = () => {
    resetFields();
    clearSearch();
  };

  const onHandleDel = () => {
    if (questionId.length === 0) {
      message.warning("请选择题目！");
      return;
    }
    Modal.confirm({
      title: "确定删除所选项？",
      confirm: "",
      okText: "是",
      cancelText: "否",
      onOk() {
        delMy({ qp_id: questionId.join(",") }).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error(res.message);
            return;
          }
          message.success("删除成功！");
          initData(1);
          resetQuestionId();
        });
      },
    });
  };

  return (
    <Form className="search" layout="inline">
      <Item label="题目">
        {getFieldDecorator("knowledge_title", {
          initialValue: "",
        })(<Input placeholder="请输入" />)}
      </Item>
      <Item label="出题人">
        {getFieldDecorator("user_name", {
          initialValue: "",
        })(<Input placeholder="请输入" />)}
      </Item>
      <Item label="题型">
        {getFieldDecorator("types", {
          initialValue: undefined,
        })(
          <Select placeholder="请选择">
            <Option value={1}>单选题</Option>
            <Option value={2}>多选题</Option>
            <Option value={3}>判断题</Option>
          </Select>
        )}
      </Item>
      {tabkey === "2" && (
        <Item label={id === "1" ? "初审状态" : "终审状态"}>
          {getFieldDecorator("status", {
            initialValue: undefined,
          })(
            <Select placeholder="请选择">
              <Option value={id === "1" ? 3 : 4}>审核通过</Option>
              <Option value={id === "1" ? 5 : 6}>审核不通过</Option>
            </Select>
          )}
        </Item>
      )}
      <div className="btn">
        {id === "2" && tabkey === "2" && (
          <Button type="primary" onClick={() => onHandleDel()}>
            批量删除
          </Button>
        )}
        <Button type="primary" onClick={() => onHandleSubmit()}>
          查询
        </Button>
        <Button type="default" onClick={() => onHandleReset()}>
          重置
        </Button>
      </div>
    </Form>
  );
};

export default Form.create()(search);
