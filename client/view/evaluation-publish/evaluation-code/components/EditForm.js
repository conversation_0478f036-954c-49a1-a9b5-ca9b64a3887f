export default Ant.Form.create()(function EditForm(props) {
  const { form, visible, onOk, onCancel } = props;

  const { getFieldDecorator } = form;

  return (
    <Ant.Modal visible={visible} onOk={onOk} onCancel={onCancel}>
      <Ant.Form>
        <Ant.Form.Item label="二维码名称">
          {getFieldDecorator("qr_code_type_name", {
            rules: [{ required: true, message: "请输入二维码名称" }],
          })(<Ant.Input placeholder="请输入" />)}
        </Ant.Form.Item>
        <Ant.Form.Item label="" style={{ display: "none" }}>
          {getFieldDecorator("pms_evaluation_qrcode_type_id", {
            rules: [{ required: true }],
          })(<Ant.Input placeholder="请输入" />)}
        </Ant.Form.Item>
      </Ant.Form>
    </Ant.Modal>
  );
});
