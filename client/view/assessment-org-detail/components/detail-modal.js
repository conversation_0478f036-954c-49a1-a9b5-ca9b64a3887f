import React from "react";
import "./style.less";
import { Modal, Timeline } from "antd";
import PropTypes from "prop-types";
import EvalTaskViewBox from "client/view/examination-task-view/eval-task-viewbox";
import DataCollectTable from "components/data-collect-table";
import { getIcon, converFileSize } from 'client/tool/util';
import SelfIcon from "components/self-icon";
import Preview from "components/preview";
const path = require('path');

const DetailModal = (props) => {
  const renderTitle = (title = "查看", detailData) => {
    // console.log(title);
    let result = title;
    // 1 逾期扣分2 审核扣分 3 查处扣分 4 数据采集扣分
    if (detailData) {
      if (detailData.deduct_type === 1) {
        result = `${title}-逾期扣分`;
      }
      else if (detailData.deduct_type === 2) {
        result = `${title}-审核扣分`;
      }
      else if (detailData.deduct_type === 3) {
        result = `${title}-查处扣分`;
      }
      else if (detailData.deduct_type === 4) {
        result = `${title}-数据采集扣分`;
      }
    }

    return result;
  }
  const { title, visible, cancelHandler, detailData = {}, dataCollectData = {}, updateScoreData={} } = props,
    modalProps = {
      title: renderTitle(title, detailData),
      visible,
      detailData,
      onCancel: cancelHandler,
      footer: null,
      bodyStyle: {
        padding: 35
      },
      width: 800,
      wrapClassName: "detail-modal-container"
    };
  const { deduct_type, examine = {} } = detailData || {},
    // eval为关键字
    { task_detail = {}, execution_record_list = [], last_task_commit = {} } = (detailData && detailData.eval) ? detailData.eval : {},
    { summary = "" } = task_detail, { files, create_time, descriptive_opinions = "" } = last_task_commit;
  const renderEvalTime = (detailData = {}) => {
    // console.log(detailData);
    const { period, year, month, quarter, eval_year, eval_cycle, eval_date } = detailData;

    let result = '';
    if (period) {
      result = `${year}年`;
      if (period === 1 && quarter) {
        result += `第${quarter}季度`;
      }
      else if (period === 2 && month) {
        result += `第${month}月`;
      }
      else if (period === 3) {
        result += "全年";
      }
    }
    else if (eval_cycle) {
      result = `${eval_year}年`;
      if (eval_cycle === 1 && eval_date) {
        result += `第${eval_date}季度`;
      }
      else if (eval_cycle === 2 && eval_date) {
        result += `第${eval_date}月`;
      }
      else if (eval_cycle === 3) {
        result += "全年";
      }
    }

    return result;
  }
  const evalTaskViewBoxProps = {
    // TODO：
    // 执行描述没有对应字段
    summary: descriptive_opinions,
    files,
    executions: execution_record_list,
    // 传入外层创建时间create_time字段，用于资料列表表格，显示提交时间
    fliesCreateTime: create_time
  }
  
  return (
    <Modal  {...modalProps}>
      {/* {
        console.log(detailData)
      } */}
      <div className="base-info-container">
        {
          (deduct_type === 1 || deduct_type === 2 || (detailData && detailData.title)) &&
          <div className="item-container">
            <div className="item-label">
              任务标题
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {task_detail.title || detailData.title}
              </div>
            </div>
          </div>
        }

        {
          (deduct_type === 1 || deduct_type === 2 || (detailData && detailData.title)) &&
          <div className="item-container">
            <div className="item-label">
              任务内容
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {task_detail.content || detailData.content}
              </div>
            </div>
          </div>
        }
        {
          (deduct_type === 1 || deduct_type === 2 || (detailData && detailData.summary)) &&
          <div className="item-container">
            <div className="item-label">
              任务描述
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {task_detail.content || detailData.summary}
              </div>
            </div>
          </div>
        }
        {
          (deduct_type === 1 || deduct_type === 2 || deduct_type === 3 || detailData) &&
          <div className="item-container">
            <div className="item-label">
              考核时间
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {
                  renderEvalTime(detailData)
                }
              </div>
            </div>
          </div>
        }
        {
          (deduct_type === 1 || deduct_type === 2) &&
          <div className="item-container">
            <div className="item-label">
              考核结果
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {deduct_type === 1 ? "逾期" : deduct_type === 2 ? "不合格" : "其他"}
              </div>
            </div>
          </div>
        }
        {
          deduct_type === 3 &&
          <div className="item-container">
            <div className="item-label">
              考核方
          </div>
            <div className="item-content">
              <div className="content-wrapper">
                {examine.c_org_name}
              </div>
            </div>
          </div>
        }
        {
          deduct_type === 3 &&
          <div className="item-container">
            <div className="item-label">
              问题项
          </div>
            <div className="item-content">
              <div className="content-wrapper">
                {examine.question}
              </div>
            </div>
          </div>
        }
        {
          deduct_type === 3 &&
          <div className="item-container">
            <div className="item-label">
              处理意见
           </div>
            <div className="item-content">
              <div className="content-wrapper">
                {examine.suggestion}
              </div>
            </div>
          </div>
        }

        {
          (deduct_type === 1 || deduct_type === 2 || deduct_type === 3 || detailData) &&
          <div className="item-container">
            <div className="item-label">
              扣分值
            </div>
            <div className="item-content">
              <div className="content-wrapper">
                {detailData.score || detailData.deduct_points}
              </div>
            </div>
          </div>
        }
        {
          deduct_type === 2 &&
          <div className="eval-task-view-box-container">
            {/* 审核扣分挂载附件 */}
            <EvalTaskViewBox {...evalTaskViewBoxProps} />
          </div>
        }
        {
          (deduct_type === 4 && dataCollectData && dataCollectData.list && dataCollectData.list.length !== 0) &&
          <div style={{ paddingTop: 30, borderTop: '2px dotted #ddd' }}>
            <DataCollectTable
              isFinish={true}
              dataSource={dataCollectData}
              orgId={detailData.org_id}
              evalId={detailData.eval_id}
              isCollect={deduct_type}
            />
          </div>
        }
        {
          deduct_type === 4 &&
          <div className="item-timeline">
            <div className="timeline-title">修正分值记录</div>
            <div className="timeline-score">修正分值: <span className="score">{updateScoreData.score}</span></div>
            <Timeline>
              {updateScoreData.modify_record && updateScoreData.modify_record.map((item, index)=>{
                const ext = path.extname(item.file_name).substr(1);
                const icon = getIcon(ext)[0];
                const color = getIcon(ext)[1];
                return (
                  <Timeline.Item>
                    <div className="timeline-tiem">{item.create_time}<span className={item.type === 1 ? "plusScore" : "reduceScore"}><i>{item.type === 1 ? "+" : "-"}</i>{item.modify_score}</span></div>
                    <SelfIcon
                      className={'execute-task-detail-select-panel-avater'}
                      type={icon}
                      style={{ color: color }}
                    />
                    <div className="execute-task-detail-select-panel-info">
                      <div>{item.file_name} &nbsp;</div>
                      <div className="info-preview">
                        <Preview file={{
                          file_id: item.file_id || null,
                          file_path: item.file_url || null
                        }} text="查看图片" />
                      </div>
                    </div>
                  </Timeline.Item>
                )
              })}
            </Timeline>
          </div>
        }
      </div>
    </Modal>
  )
};

DetailModal.propTypes = {
  title: PropTypes.string || PropTypes.element,
  visible: PropTypes.bool,
  detailData: PropTypes.object,
  cancelHandler: PropTypes.func
};
DetailModal.defaultProps = {
  title: "查看",
  visible: false,
  detailData: {},
  cancelHandler: () => { }
};

export default DetailModal;

