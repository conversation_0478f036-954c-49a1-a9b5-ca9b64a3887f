import React from "react";
import { Table, Checkbox } from "antd";

const CheckboxGroup = Checkbox.Group;

export default (props) => {
  const {
    checkedList = [],
    dataSource = [],
    selectedType = {},
    tableLoading = false,
    onChange
  } = props;
  const isChecked = (record = {}) => {
    let result = false;
    if (record && checkedList && Array.isArray(checkedList)) {
      const index = checkedList.findIndex((data) => {
        return data.op_key === record.op_key;
      });
      result = index !== -1;
    }
    return result;
  }
  const columns = [
    {
      align: "center",
      dataIndex: "check",
      title: "选择",
      render: (text, record, index) => {
        return (
          <Checkbox checked={isChecked(record)} value={JSON.stringify(record)} onChange={(e) => {
            const target = e.target;
            const { checked, value } = target;
            try {
              const item = JSON.parse(value);
              onChange && onChange({ checked, item });
            }
            catch (error) {
              console.error(value);
            }
          }} />
        )
      }
    },
    {
      align: "center",
      dataIndex: "index",
      title: "序号",
      render: (text, record, index, a, b, c) => {
        return index + 1;
      }
    },
    {
      align: "center",
      dataIndex: "type",
      title: "组织类型",
      render: (text, record, index) => {
        return record.op_value || "-";
      }
    },
    {
      align: "center",
      dataIndex: "owner",
      title: "所属类别",
      render: (text, record, index) => {
        return selectedType.op_value || "-";
      }
    }
  ];

  const tableProps = {
    rowKey: "op_key",
    loading: tableLoading,
    columns,
    dataSource,
    bordered: true,
    locale: {
      emptyText: selectedType ? "暂无数据" : "请选择所属类别"
    },
    pagination: {
      size: "small",
      pageSize: 5,
      // pageSizeOptions: ["3", "5"],
      showQuickJumper: true,
      // showSizeChanger: true
    }
  }
  return (
    <div className="table-wrapper">
      <Table {...tableProps} />
    </div>
  )
}