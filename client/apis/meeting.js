// 会议接口
const { http } = require('client/tool/axios');
import { mtHost, host } from "./config";

// 填写纪实情况表（有会议流程）
export const postResultSubmit = (queryBody = {}) => {
  return http.post(`${mtHost}/result/submit`, queryBody);
}

// 06-修改纪实结果 - 下级
export const postResultUpdate = (queryBody = {}) => {
  return http.post(`${mtHost}/result/update`, queryBody);
}
// 10500102801-查询党小组信息
export const getOrgTypeList = (id) => {
  return http.get(`${host}/org-group/get-all/${id}`);
}
// 10500102802-查询支委会信息
export const getPeriodList = (id) => {
  return http.get(`${host}/period/get-all/${id}`);
}
// /period/get-all/{org_id}
// 直接填写纪实记录
export const postResultAdd = (queryBody = {}, user_id, org_id) => {
  return http.post(`${mtHost}/result/add?user_id=${user_id}&org_id=${org_id}`, queryBody);
}
// 105001031-根据组织ID查询用户列表
// /find-user-by-org-id
// 编辑会议（只修改人员也调用这个接口）
export const findUserByOrgId = (queryBody = {}) => {
  return http.post(`${host}/find-user-by-org-id`, queryBody);
}
export const postMeetingUpdate = (queryBody = {}) => {
  return http.post(`${mtHost}/meeting/update`, queryBody);
}

// 发起会议（10300010601-发起会议）
export const postMeetingAdd = (queryBody = {}) => {
  return http.post(`${mtHost}/meeting/add`, queryBody);
}
// 1030003034-根据组织ID查询顶级党委组织ID
export const getOrgFindPartyId = (org_id) => {
  return http.get(`${host}/org/find-party-id?org_id=${org_id}`);
}

// 30000712-获取草稿
export const getDraft = (org_id) => {
  return http.get(`${mtHost}/meeting/getDraft/${org_id}`);
};

// 30000711-保存草稿
export const saveDraft = (org_id, content) => {
  return http.post(`${mtHost}/meeting/saveDraft/${org_id}`, { content });
};

// 30000711-保存草稿
export const delDraft = (org_id) => {
  return http.post(`${mtHost}/meeting/deleteDraft/${org_id}`);
};

// 查询领导班子成员   http://wiki.aidangqun.com/project/3?p=1259
export const getMeetingLeader = ({params}) => {
  return http.get(`${mtHost}/meeting/query-leader`, params);
};
