import { ucHost, orgHost, Gateway } from "./config";
import { http } from "client/tool/axios";

export const findOrgByName = (data) => {
  //3
  return http.get(`${orgHost}/org/find-org-by-name`, data);
};
export const findByName = (data) => {
  return http.get(`${orgHost}/org/find-by-name`, data);
};
export const findByUser = (data) => {
  return http.get(`${orgHost}/org/find-by-user`, data);
};
export const getOrgTree = (data) => {
  return http.get(`${ucHost}/org/tree`, data);
};

export const getOrgCount = (data) => {
  return http.get(`${ucHost}/org/count`, data);
};

export const getOrgTotalCount = (data) => {
  return http.get(`${ucHost}/org/total-count`, data);
};

export const getOrgInfo = (data) => {
  return http.get(`${ucHost}/org/info`, data);
};

export const addOrg = (data) => {
  return http.post(`${ucHost}/org/add`, data);
};

export const updateOrg = (data) => {
  return http.post(`${ucHost}/org/update`, data);
};

export const deleteOrg = (data) => {
  return http.get(`${ucHost}/org/delete`, data);
};

export const sortOrg = (data) => {
  return http.post(`${ucHost}/org/sort`, data);
};
export const getOrgUserList = (data) => {
  return http.post(`${orgHost}/org/user/orgUserList`, data);
};
export const getUserList = (data) => {
  return http.post(`${orgHost}/org/user/getUserList`, data);
};
export const getOrgUserInfo = (data) => {
  return http.get(`${orgHost}/org/user/info`, data);
};
export const addOrgUser = (data) => {
  return http.post(`${orgHost}/org/user/orgUserAdd`, data);
};
export const updateOrgUser = (data) => {
  return http.post(`${orgHost}/org/user/orgUserUpdate`, data);
};
export const delOrgUser = (data) => {
  return http.get(`${orgHost}/org/user/del`, data);
};

//加载省市区地址
export const getArea = (data) => {
  return http.get(`${ucHost}/dict/area/list`, data);
};
export const getAllArea = (data) => {
  return http.get(`${ucHost}/dict/area/all`, data);
};
export const getNativeArea = (data) => {
  return http.get(`${orgHost}/org/user/nativeArea`, data);
};

//查询组织信息接口
export const fetchOrganizationInformation = (oid) => {
  return http.get(`${ucHost}/org/info`, { org_id: oid });
};
//查询手机号码是否重复
export const addCheckPhone = (data) => {
  return http.get(`${ucHost}/org/user/addCheckPhone`, data);
};
//查询身份证实名验证及重复检查
export const checkUserNameIdCard = (data) => {
  return http.get(`${ucHost}/org/user/checkNameIdCard`, data);
};

//改造组织架构
//查询组织树标签列表
export const getTreeList = (oid) => {
  //2
  const params = {};
  if (oid) {
    params["org_id"] = oid;
  }
  return http.get(`${ucHost}/org/tree/list`, params);
};
export const getTreeDetail = (id) => {
  return http.get(`${ucHost}/org/tree/get`, { tree_id: id });
};
export const addOrgTree = (data) => {
  return http.post(`${ucHost}/org/tree/add`, data);
};
export const updateOrgTree = (data) => {
  return http.post(`${ucHost}/org/tree/update`, data);
};
export const deleteOrgTree = (id) => {
  return http.delete(`${ucHost}/org/tree/delete`, { tree_id: id });
};
export const locateOrgTree = (data) => {
  //1
  return http.get(`${ucHost}/org/locate`, data);
};

export const getTagByType = (data) => {
  //return http.get(`http://*************:7456/uc/tag/getTagByType`, data);
  return http.get(`${ucHost}/uc/tag/getTagByType`, data);
};
export const updateUserOrgTag = (data) => {
  //return http.post(`http://*************:7456/uc/user/updateUserOrgTag`, data);
  return http.post(`${ucHost}/uc/user/updateUserOrgTag`, data);
};

//查询人员标签列表
export const getUserTagList = (data) => {
  return http.post(`${ucHost}/org/user/tag/getUserTagList`, data);
};
export const insertUserTag = (data) => {
  return http.post(`${ucHost}/org/user/tag/insert`, data);
};
export const deleteUserTag = (data) => {
  return http.post(`${ucHost}/org/user/tag/delete`, data);
};
//105001021-查询标签列表
export const getPublicTag = () => {
  return http.get(`${ucHost}/uc/tag/get-public-tag`);
};
//105001020-人员选择器-人员查询(含高级查询)
export const getUserByCondition = (data) => {
  return http.post(`${ucHost}/org/user/get-user-by-condition`, data);
};

export const getTagList = (data) =>
  http.get(`${ucHost}/uc/tag/getTagList`, data);

export const findOrgByWhere = (data) =>
  http.get(`${ucHost}/org/find-org-by-where`, data);

export const exportOrgInfo = (data) =>
  http.get(`${ucHost}/org/export-org-info`, data);
export const deleteOrgTags = (data) =>
  http.post(`${ucHost}/org/tag/delete`, data);
export const insertOrgTags = (data) =>
  http.post(`${ucHost}/org/tag/insert`, data);

export const addOrgUserNew = (data) => {
  return http.post(`${orgHost}/org/user/orgUserAdd`, data);
};

export const updateOrgUserNew = (data) => {
  return http.post(`${orgHost}/org/user/orgUserUpdate`, data);
};

export const getOrgUserListNew = (data) => {
  return http.post(`${ucHost}/org/user/getUserList`, data);
};

export const addOrgNew = (data) => {
  return http.post(`${ucHost}/org/add`, data);
};

export const updateOrgNew = (data) => {
  return http.post(`${ucHost}/org/update`, data);
};

// 1040020043-根据用户ID列表查询用户信息
export const findUserByList = (data) => {
  return http.post(`${ucHost}/find-user-org-by-batch`, data);
};

// 401020201001-获取生成党组织二维码信息 http://wiki.aidangqun.com/project/8?p=952
export const getQrCode = (data) => {
  return http.get(`${ucHost}/org/get-qr-code`, data);
};

// 得到地区信息 最高3的层
export const getAreaData = () => {
  return http.get(`${ucHost}/dict/area/get-all-by-type?type=3`);
};

// 4010202100115-获取组织审核列表 http://wiki.aidangqun.com/project/8?p=1810
export const SettleInList = (data) => {
  return http.get(`${ucHost}/org_examine/list`, data);
};
// 4010202100111-获取组织审核详情 http://wiki.aidangqun.com/project/8?p=1811
export const SettleInDetail = (data) => {
  return http.get(`${ucHost}/org_examine/getInfo`, data);
};
// 4010202100127-组织详情-审批列表 http://wiki.aidangqun.com/project/8?p=1837
export const SettleInFlowPath = (data) => {
  return http.get(`${ucHost}/org-expand/find-expand-task-list`, data);
};
// 4010202100112-组织审核 http://wiki.aidangqun.com/project/8?p=1812
export const SettleInProcess = (data) => {
  return http.get(`${ucHost}/org_examine/examine`, data);
};
// 4010202100105-更新家庭互助会组织 http://wiki.aidangqun.com/project/8?p=1801
export const UpdateOrganization = (data) => {
  return http.post(`${ucHost}/mma/org/update`, data);
};
// 4010202100101-新建家庭互助会组织（/mma/org/add）http://wiki.aidangqun.com/project/8?p=1789
export const addOrganization = (data) => {
  return http.post(`${ucHost}/mma/org/add`, data);
};
// 4010202100104-组织详情(/mma/org/info) http://wiki.aidangqun.com/project/8?p=1798
export const OrganizationalDetails = (data) => {
  return http.get(`${ucHost}/mma/org/info`, data);
};
//-查询党群服务中心列表
export const findServiceOrgList = (data) => {
  return http.get(`${ucHost}/mma/org/find-service-list`, data);
};
//-4010202100109-删除组织（/mma/org/remove）http://wiki.aidangqun.com/project/8?p=1809
export const removeMmaOrg = (data) => {
  return http.get(`${ucHost}/mma/org/remove`, data);
};
//4010202100130-党群服务中心列表导出http://wiki.aidangqun.com/project/8?p=1916
export const remexportServiceListoveMmaOrg = (data) => {
  return http.get(`${ucHost}/mma/org/export-service-list`, data);
};