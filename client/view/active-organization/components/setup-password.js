import React, { Component } from "react";
import { Form, Card, Input, Button } from "antd";
import PropTypes from "prop-types";
// import { dealPassword } from "client/apis/active-organization";
import md5 from "js-md5";

const FormItem = Form.Item;

class SetupPassword extends Component {
  constructor(props) {
    super(props);
  }

  handleSubmit(e) {
    e.preventDefault();
    const props = this.props;

    // props.changeState({ step: 3 });

    props.form.validateFieldsAndScroll((errs, values) => {
      if (!errs) {
        props.changeState({
          passwordInfo: {
            password: md5(values.password),
            repeat_password: md5(values.repeat_password)
          },
          step: 3
        });
      }
    });
  }

  passwordValidator(rule, value, callback) {
    const props = this.props;

    if (!value) {
      return callback("请重复输入密码");
    }

    const password = props.form.getFieldValue("password");

    if (password !== value) {
      return callback("两次输入的密码不一致");
    }

    callback();
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { passwordInfo } = this.props.state;

    return (
      <Card className="setup-password">
        <Form onSubmit={this.handleSubmit.bind(this)}>
          <FormItem>
            {getFieldDecorator("password", {
              initialValue: passwordInfo.password,
              rules: [{ required: true, message: "请输入密码" }]
            })(<Input type="password" placeholder="请输入密码" />)}
          </FormItem>

          <FormItem>
            {getFieldDecorator("repeat_password", {
              validateTrigger: "onBlur",
              initialValue: passwordInfo.repeat_password,
              rules: [{ validator: this.passwordValidator.bind(this) }]
            })(<Input type="password" placeholder="请重复输入密码" />)}
          </FormItem>

          <FormItem>
            <Button
              style={{ width: 140 }}
              onClick={() => this.props.changeState({ step: 1 })}
            >
              上一步
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: 140, marginLeft: 32 }}
            >
              下一步
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}

const SetupPasswordWrapper = Form.create()(SetupPassword);

SetupPasswordWrapper.propTypes = {
  changeState: PropTypes.func, // 改变父级state
  state: PropTypes.object
};

export default SetupPasswordWrapper;
