import React, { Component } from 'react';
import { Layout } from 'antd';
import './index.less';


const { Content } = Layout;
/**
 * 渲染菜单栏
 * <AUTHOR>
 */
export default function ( { leftComponent, rightComponent } ) {
	let isLeftObject = false;
	if(leftComponent && leftComponent.width != undefined){
        isLeftObject = true;
	}

	return (
		<Layout className="main-flex-wrap">
			{leftComponent && (
				<div style={{ background: '#fff', width: isLeftObject ? leftComponent.width || '250px' : '250px' }}>
					{isLeftObject ? leftComponent.component : leftComponent}
				</div>
			)}
			{rightComponent && (
				<Content style={{ background: '#fff', padding: '0 50px 0 25px' }}>
					{rightComponent}
				</Content>
			)}
		</Layout>
	);
}
