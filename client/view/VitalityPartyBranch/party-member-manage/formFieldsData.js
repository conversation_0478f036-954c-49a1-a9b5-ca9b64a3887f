export const form1 = {
    userName: ['userName', null],
    phone: ['phone', null],
    cardId: ['cardId', null],
    label: ['label', null],
    isOrg: ['isOrg', false]
};
export const form2 = {
    sex: ['sex', [null, false]],
    political: ['political', [null, false]],
    cardType: ['cardType', [null, false]],
    nation: ['nation', [null, false]],
    record: ['record', [null, false]],
    ethnic: ['ethnic', [null, false]],
    level: ['level', [null, false]],
    partyMembers: ['partyMembers', [null, false]],
    communistYouth: ['communistYouth', [null, false]],
    union: ['union', [null, false]],
    fede: ['fede', [null, false]],
    nativePlace: ['nativePlace', null],
    name: ['userName', null],
    tel: ['phone', null],
    cardId: ['cardId', null],
    job: ['job', null]
};
export const form3 = {
    name: ['name', null],
    phone: ['phone', null],
    phone2: ['phone2', null],
    cardType: ['cardType', 1],
    ethnic: ['ethnic', null],
    cardId: ['cardId', null],
    nativePlace: ['nativePlace', null],
    sex: ['sex', null],
    nation: ['nation', null],
    political: ['political', null],
    record: ['record', null],
    level: ['level', -1],
    job: ['job', null],
    positionCode: ['positionCode', -1],
    isLose: ['isLose', 2],
    partyMembers: ['partyMembers', -1],
    communistYouth: ['communistYouth', -1],
    union: ['union', -1],
    fede: ['fede', -1]
};

export const getFormParams = (queryParams) => {
    let params = {};

    if(queryParams) {
        if (queryParams['userId']){
            params['userId'] = queryParams.userId;
        }
        if (queryParams['orgId']) {
            params['orgId'] = queryParams.orgId;
        }
        if (queryParams['name']) {
            params['name'] = queryParams.name;
        }
        if (queryParams['phone']) {
            params['phone'] = queryParams.phone;
        }
        if (queryParams['cardId']) {
            params['certNumber'] = queryParams.cardId;
        }
        if (queryParams['label']) {
            params['tagName'] = queryParams.label;
        }
        if (queryParams['isOrg']) {
            params['onlyCurrentOrg'] = 1;
        }
        if (queryParams['job']) {
            params['position'] = queryParams.job;
        }
        if (queryParams['positionCode']) {
            params['position_code'] = queryParams.positionCode;
        }
        if (queryParams['isLose']) {
            params['is_lose'] = queryParams.isLose;
        }
        if (queryParams['sex']) {
            params['gender'] = Array.isArray(queryParams['sex']) ? queryParams['sex'].join(',') : queryParams.sex;
        }
        if (queryParams['cardType']) {
            params['certType'] = Array.isArray(queryParams['cardType']) ? queryParams['cardType'].join(',') : queryParams.cardType;
        }
        if (queryParams['nation']) {
            params['censusType'] = Array.isArray(queryParams['nation']) ? queryParams['nation'].join(',') : queryParams.nation;
        }
        if (queryParams['record']) {
            params['education'] = Array.isArray(queryParams['record']) ? queryParams['record'].join(',') : queryParams.record;
        }
        if (Array.isArray(queryParams['nativePlace'])) {
            if(queryParams.nativePlace[0]){
                params['nativeProvince'] = queryParams.nativePlace[0];
            }
            if(queryParams.nativePlace[1]){
                params['nativeCity'] = queryParams.nativePlace[1];
            }
        }
        if (queryParams['ethnic']) {
            params['ethnic'] = Array.isArray(queryParams['ethnic']) ? queryParams['ethnic'].join(',') : queryParams.ethnic;
        }
        if (queryParams['political']) {
            params['politicalType'] = Array.isArray(queryParams['political']) ? queryParams['political'].join(',') : queryParams.political;
        }
        if (queryParams['level']) {
            params['jobGrade'] = Array.isArray(queryParams['level']) ? queryParams['level'].join(',') : queryParams.level;
        }
        if (queryParams['partyMembers']) {
            params['communist'] = Array.isArray(queryParams['partyMembers']) ? queryParams['partyMembers'].join(',') : queryParams.partyMembers;
        }
        if (queryParams['communistYouth']) {
            params['youthLeague'] = Array.isArray(queryParams['communistYouth']) ? queryParams['communistYouth'].join(',') : queryParams.communistYouth;
        }
        if (queryParams['union']) {
            params['unionMember'] = Array.isArray(queryParams['union']) ? queryParams['union'].join(',') : queryParams.union;
        }
        if (queryParams['fede']) {
            params['womenLeague'] = Array.isArray(queryParams['fede']) ? queryParams['fede'].join(',') : queryParams.fede;
        }
    }
    return params;
}