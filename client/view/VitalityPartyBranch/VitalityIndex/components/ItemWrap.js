import { useState, useEffect, useMemo } from "react";

const ItemWrap = (props) => {
  const { backgroundColor, text, num, min, max, type = 0 } = props;
  return (
    <div className={`itemWrap itemWrap-${type}`}>
      <div className="item" style={{ backgroundColor }}>
        <div className="text">{text}</div>
        <div className="number-wrap">
          <div className="number">{num}</div>
          {type === 2 && (
            <div className="extra-wrap">
              <span className="icon1">最高: {max}</span>
              <span className="icon2">最低: {min}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default ItemWrap;
