.location-modal {
  .search-wrapper {
    margin-bottom: 26px;
    .ant-btn {
      width: 90px;
      height: 36px;
    }
  }
  .map-container {
    height: 380px;
    display: flex;
    border: 1px solid #E5E5E5;
    .map-wrapper {
      flex-grow: 1;
      border-right: 1px solid #E5E5E5;
    }
    .list-wrapper {
      padding: 20px;
      width: 40%;
      flex-shrink: 0;
      overflow: auto;
      display: flex;
      background-color: #F7F8F9;
      .no-data {
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
      }
      .result-item-wrapper {
        .ant-radio-wrapper {
          font-size: 14px;
          color: #333;
        }
        .item-detaile {
          margin-left: 24px;
          font-size: 12px;
          color: #999;
        }
        margin-bottom: 20px;
      }
    }
  }
  .buttons-wrapper {
    margin: 40px 0 20px 0;
    text-align: center;
    .ant-btn {
      width: 115px;
      height: 36px;
      margin: 0 24px 0 24px;
    }
  }
}