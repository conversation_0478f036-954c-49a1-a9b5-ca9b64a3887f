import React from "react";
import PropTypes from "prop-types";
import { Button } from "antd";

const ButtonGroup = (props) => {
  const { data } = props;
  // console.log(data);
  const wrapStyle = {
    width: `${(115 * data.length) + ((data.length - 1) * 48)}px`,
    height: "36px",
    display: "flex",
    flexDirection: "row",
    margin: "30px auto"
  }

  const renderView = (record, index) => {
    switch (record.name) {
      case "取消":
      case "上一步":
        return (
          <Button
            key={index}
            onClick={record.onClick}
            style={record.style}
          >
            {record.name}
          </Button>
        )
      default:
        return (
          <Button
            key={index}
            onClick={record.onClick}
            type="primary"
            style={record.style}
          >
            {record.name}
          </Button>
        )
    }
  }
  return (
    <div style={wrapStyle}>
      {data.map((item, index) => {
        return renderView(item, index)
      })}
    </div>
  )
}
ButtonGroup.propTypes = {
  // data: PropTypes.array,
  data: PropTypes.arrayOf(PropTypes.shape({
    onClick: PropTypes.func,
    style: PropTypes.object
  }))
}

export default ButtonGroup;