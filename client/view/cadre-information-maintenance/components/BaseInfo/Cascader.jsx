import { Cascader } from "antd";
import "./Cascader.less";

function CustomCascader({
  disabled,
  subText,
  fieldNames,
  areaTree,
  loadData,
  onChange,
  value,
  displayRender,
}) {
  return (
    <div className="custom-cascader">
      <Cascader
        onChange={onChange}
        value={value}
        disabled={disabled}
        placeholder="请输入"
        fieldNames={fieldNames}
        options={areaTree}
        loadData={(option) => {
          console.log(option, "option");
          loadData(option);
        }}
        changeOnSelect
        displayRender={displayRender}
      />
      {subText && <div className="sub-text">{subText}</div>}
    </div>
  );
}

export default CustomCascader;
