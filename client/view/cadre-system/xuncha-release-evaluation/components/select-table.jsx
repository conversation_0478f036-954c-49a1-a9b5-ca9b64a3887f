import React, { useState, useMemo, useRef } from "react";
import {
  <PERSON><PERSON>r,
  Button,
  Icon,
  Table,
  Modal,
  Select,
  Input,
  Form,
  Popconfirm,
  Checkbox,
  message,
} from "antd";
import DropText from "../../components/drop-text";
import { findByUser, initiateEvaluation } from "client/apis/cadre-portrait";
import NewUser from "./new-user";
import "./select-table.less";

const { Option } = Select;

const EditableContext = React.createContext();

const EditableRow = ({ form, index, ...props }) => (
  <EditableContext.Provider value={form}>
    <tr {...props} />
  </EditableContext.Provider>
);

const EditableFormRow = Form.create()(EditableRow);
class EditableCell extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      editing: false,
    };

    this.toggleEdit = this.toggleEdit.bind(this);
    this.save = this.save.bind(this);
    this.renderCell = this.renderCell.bind(this);
  }

  toggleEdit() {
    const editing = !this.state.editing;
    this.setState({ editing }, () => {
      if (editing) {
        this.input.focus();
      }
    });
  }

  save(e) {
    const { record, handleSave } = this.props;
    this.form.validateFields((error, values) => {
      if (error && error[e.currentTarget.id]) {
        return;
      }
      this.toggleEdit();
      handleSave({ ...record, ...values, o_seq: record.seq });
    });
  }
  renderCell(form) {
    this.form = form;
    const { children, dataIndex, record, title } = this.props;
    const { editing } = this.state;
    return editing ? (
      <Form.Item style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${title}为必填`,
            },
          ],
          initialValue: record[dataIndex],
        })(
          <Input
            ref={(node) => (this.input = node)}
            onPressEnter={this.save}
            onBlur={this.save}
          />
        )}
      </Form.Item>
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{ paddingRight: 24 }}
        onClick={this.toggleEdit}
      >
        {children}
      </div>
    );
  }
  render() {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      children,
      ...restProps
    } = this.props;
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    );
  }
}
export default function selectTable(props) {
  const {
    org_id,
    org_name,
    parent_id,
    pattern_sub,
    group,
    checked,
    dataSource,
    onUpdateData,
    onDelete,
    onCheckChange,
    onSelectChange,
  } = props;
  console.log("🚀 ~ selectTable ~ checked:", checked);
  const [visible, setVisible] = useState(false);

  const formRef = useRef(null);
  /**
   * @description: 二次确认
   * @param {*} e
   * @return {*}
   */
  const confirm = (user) => {
    console.log(user);
    const newData = dataSource.filter((item) => {
      if (user.user_id) {
        console.log("user_id");
        return item.user_id !== user.user_id;
      }
      if (user.evaluation_user_id) {
        console.log("evaluation_user_id");
        return item.evaluation_user_id !== user.evaluation_user_id;
      }

      if (user.seq) {
        return user.seq !== item.seq;
      }
    });

    console.log(newData);

    onUpdateData && onUpdateData({ org_id, user_list: newData });
    // 手动添加的用户不会被删除用户id列表
    if (user.type !== "new") {
      onDelete && onDelete(user.evaluation_user_id || user.user_id);
    }
  };

  /**
   * @description: 取消
   * @param {*} e
   * @return {*}
   */
  const cancel = (e) => {
    console.log(e);
  };

  /**
   * @description: 新增框确认
   * @return {*}
   */
  const handleOk = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        setVisible(false);
        // 找出最大数
        const maxSeq = dataSource.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 0);
        }, 0);

        onUpdateData({
          org_id,
          user_list: [
            ...dataSource,
            {
              ...formRef.current.getFieldsValue(),
              type: "add",
              seq: maxSeq + 1,
            },
          ],
        });
      }
    });
  };

  //   取消
  const handleCancel = () => {
    setVisible(false);
  };
  /**
   * @description: 复选框选中
   * @return {*}
   */
  const checkChange = ({ target: { checked } }) => {
    onCheckChange && onCheckChange({ org_id, checked });
  };
  const TableTitle = ({ }) => {
    return (
      <div className="table-title">
        <div className="left-box">参评人员</div>
        <div className="right-box">
          <a
            className="add-btn"
            onClick={() => {
              setVisible(true);
            }}
          >
            +添加
          </a>
        </div>
      </div>
    );
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "20%",
      title: "姓名",
    },
    {
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      title: "测评职务类别",
      render(_, record) {
        const option = [
          {
            label: "县管正职",
            key: 1,
          },
          {
            label: "县管副职",
            key: 2,
          },
          {
            label: "改非干部",
            key: 3,
          },
          {
            label: "新提拔中层干部",
            key: 4,
          },
        ];
        if (![3, 4].includes(Number(_))) {
          option.splice(2, 2);
        }
        return (
          <DropText
            value={_}
            disabled={[3, 4].includes(Number(_))}
            disableList={[3, 4]}
            option={option}
            onChange={(key) => {
              dataSource.forEach((item) => {
                if (item.user_id === record.user_id) {
                  item.user_type = key;
                }
              });

              onUpdateData && onUpdateData({ org_id, user_list: dataSource });
            }}
          />
        );
      },
    },
    {
      dataIndex: "job",
      key: "job",
      editable: true,
      title: "测评职务",
      // render(_) {
      //   return <div style={{ paddingLeft: "80px" }}>{_}</div>;
      // },
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (text, record) => (
        <Popconfirm
          title="确认删除?"
          onConfirm={() => {
            confirm(record);
          }}
          onCancel={cancel}
          okText="是"
          cancelText="否"
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      ),
    },
  ];

  const components = {
    body: {
      row: EditableFormRow,
      cell: EditableCell,
    },
  };

  const selectChange = (value) => {
    dataSource.forEach((item) => {
      item.group = value;
    });
    onSelectChange &&
      onSelectChange({ org_id, group: value, user_list: dataSource });
  };

  const handleSave = (row) => {
    if (!row.seq) {
      message.error("请输入测评序号");
      return;
    }
    if (!row.job) {
      message.error("请输入测评职务");
      return;
    }
    const newData = [...dataSource];
    let index = -1;
    // 没有user_id的是手动添加的
    if (!row.user_id) {
      index = newData.findIndex((item) => item.seq === row.o_seq);
    } else {
      index = newData.findIndex((item) => row.user_id === item.user_id);
    }
    // 有修改才更新
    if (row.seq != dataSource[index].seq || row.job !== dataSource[index].job) {
      delete row.o_seq;

      index !== -1 && newData.splice(index, 1, row);

      onUpdateData && onUpdateData({ org_id, user_list: newData });
    }
  };

  const newColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        handleSave: handleSave,
      }),
    };
  });
  return (
    <div className="select-table">
      <div className="org-name">
        <div className="org-name-box">
          <Checkbox onChange={checkChange} checked={checked} />
          <span className="name">{org_name}</span>
        </div>
        <div className="org-group-box">
          {checked && (
            <Select
              defaultValue={group ? String(group) : "1"}
              style={{ width: 120 }}
              onChange={selectChange}
            >
              <Option value="1">组别一</Option>
              <Option value="2">组别二</Option>
              <Option value="3">组别三</Option>
            </Select>
          )}
        </div>
      </div>
      <div className="table-box">
        <Table
          bordered
          components={components}
          columns={newColumns}
          dataSource={dataSource}
          title={TableTitle}
          pagination={false}
        />
      </div>
      <Modal
        title="添加测评人员"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
      >
        <NewUser ref={formRef} />
      </Modal>
    </div>
  );
}
