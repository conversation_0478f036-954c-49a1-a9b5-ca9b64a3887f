/**
 * input的右边属性字
 * <AUTHOR>
 * 2018/4/27
 */
import React from "react";
import { Icon, message, Spin } from "antd";
import PropTypes from "prop-types";
import UploadFile from "../sub-components/UploadFile";
import "./style.less";
import SelfIcon from "components/self-icon";

class UploadImage extends React.Component {
  constructor() {
    super();
    this.state = {
      loading: false,
    };
  }
  handleUploading(info) {
    const { typeName, onUploading } = this.props;
    onUploading(typeName, info);
  }

  handleSuccess(info) {
    const { typeName, onSuccess } = this.props;
    onSuccess(typeName, info.file.response);
    this.setState({
      loading: false,
    });
  }

  handleError(info) {
    const { typeName, onError } = this.props;
    const result = info.file.response;
    message.error("图片上传失败：" + result.message);
    this.setState({
      loading: false,
    });
    onError(typeName, result);
  }

  handleDelete() {
    const { typeName, onDelete } = this.props;
    onDelete(typeName);
  }

  beforeUpload(file, fileList) {
    const { limit, beforeUpload } = this.props;
    if (beforeUpload) beforeUpload();
    if (!limit) return true;
    this.setState({ loading: true });
    return new Promise((resolve, reject) => {
      if (file.size > limit.size * 1000) {
        message.error(`图片大小超过${limit.size}kb`);
        this.setState({ loading: false });
        return reject();
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target.result;
        const image = new Image();
        // image.onload = () => {
        //     if (image.width !== limit.width || image.height !== limit.height) {
        //         message.error(`请上传推荐的图片尺寸：${limit.width}x${limit.height}`)
        //         return reject()
        //     }
        //     resolve()
        // }
        resolve();
        image.src = data;
      };
      reader.readAsDataURL(file);
    });
  }

  render() {
    const { loading } = this.state;
    const {
      description,
      title,
      uri,
      typeName,
      onUploading,
      onSuccess,
      onError,
      beforeUpload,
      ...props
    } = this.props;

    const uploadConfig = {
      showUploadList: false,
      beforeUpload: this.beforeUpload.bind(this),
      onUploading: this.handleUploading.bind(this),
      onSuccess: this.handleSuccess.bind(this),
      onError: this.handleError.bind(this),
    };

    return (
      <div className="upload-image-container">
        {uri === "" ? (
          <UploadFile {...uploadConfig} {...props} disabled={loading}>
            <div className="upload-image-container-wrapper">
              <Spin spinning={loading}>
                <div className="upload-image-container-uploadbtn">
                  <SelfIcon type="gsg-shangchuantupian" />
                  点击上传
                </div>
              </Spin>
              <p>{description}</p>
            </div>
          </UploadFile>
        ) : (
          <div
            className="upload-done-container"
            data-disabled={props.disabled ? "disabled" : ""}
          >
            <Spin spinning={loading}>
              <img src={uri} className="upload-image-container" />
              <div className="upload-done-action-shadow">
                <span title="重新上传">
                  <UploadFile {...uploadConfig} {...props}>
                    <SelfIcon type="gsg-shangchuanziliao" />
                  </UploadFile>
                </span>
                <span title="删除图片" onClick={this.handleDelete.bind(this)}>
                  <SelfIcon type="gsg-shanchu4" />
                </span>
              </div>
            </Spin>
          </div>
        )}
        <p>{title}</p>
      </div>
    );
  }
}

UploadImage.propTypes = {
  disabled: PropTypes.bool,
  onUploading: PropTypes.func,
  onSuccess: PropTypes.func,
  onError: PropTypes.func,
  onDelete: PropTypes.func,
  title: PropTypes.string,
  uri: PropTypes.string,
  description: PropTypes.string,
  //当前上传图片的name
  typeName: PropTypes.string,
  //上传限制
  limit: PropTypes.object,
};

UploadImage.defaultProps = {
  onUploading: () => {},
  onSuccess: () => {},
  onError: () => {},
  onDelete: () => {},
  typeName: "upload",
  title: "上传图片",
  uri: "",
  description: "",
};

export default UploadImage;
