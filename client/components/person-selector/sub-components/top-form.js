import React, { Component } from "react";
import { Form, Button, Icon, Input, Select, Checkbox, message as Message } from "antd";
import { fetchStorage } from "tool/util";
import { getOrgInfo,getPublicTag } from "apis/organize";
import { getcodeList } from "apis/users";
import {connect} from 'dva';
import "./top-form.less";

import propTypes from "prop-types";

const FormItem = Form.Item;
const Option = Select.Option;
const Search = Input.Search;
class TopForm extends Component {
  constructor(props) {
    super(props);
    const org_type = typeof window !== "undefined" && window.sessionStorage.getItem("_org_type")  || -1
    this.state = {
      isAdvancedQuery: false,
      org_type,
      tagData: []
    };
    this.resetHandler = this.resetHandler.bind(this);
  }
  async componentWillMount(){
    this.fetchOrgInfo(this.props.initOrgType);
    const res = (await getPublicTag()).data
    if(res.code === 0){
      this.setState({
        tagData: res.data,
        checked_manager: false,
        checked_current: false
      })
    }
    const {dispatch} = this.props;
    await dispatch({ type: 'organizeData/loadculture' });
    await dispatch({ type: 'organizeData/loadSex' });
    await dispatch({ type: 'organizeData/loadDepment' });
    await dispatch({ type: 'organizeData/loadCensus' });
    await dispatch({ type: 'organizeData/loadpolitical' });
    await dispatch({ type: 'organizeData/loadType' });
    await dispatch({ type: 'organizeData/loadRecord' });
    await dispatch({ type: 'organizeData/loadEthnic' });
    await dispatch({ type: 'organizeData/loadLevel' });
    await dispatch({ type: 'organizeData/loadArea' });
    await dispatch({ type: 'organizeData/loadPartyMembers' });
    await dispatch({ type: 'organizeData/loadCommunistYouth' });
    await dispatch({ type: 'organizeData/loadUnion' });
    await dispatch({ type: 'organizeData/loadFede' });
    await dispatch({ type: 'organizeData/loadPositionCode' });
  }
  resetHandler(onlyResetValue) {
    const { form, onSearchHandler } = this.props;
    const { resetFields} = form;
    resetFields();
    onSearchHandler(null);
    this.setState({
      checked_manager: false,
      checked_current: false
    })
    /* if (!onlyResetValue) {
      const value = getFieldValue(keyword);
      onSearchHandler(value);
    } */
  }
  async fetchOrgInfo(orgTypeRoot) {
    // console.log("只调用一次");
    // console.log(this.props);
    const { initOrgType, isLeaderGroup } = this.props;
    // let org_id = typeof window !== "undefined" && window.sessionStorage.getItem("_oid") || 0;
    // org_id = Number(org_id);
    // const response = await getOrgInfo({ org_id, tree_type: 2 });
    // const { data: body } = response;
    // const { data, code, message } = body;
    // if (code !== 0) {
    //   Message.error(message);
    //   return;
    // }
    // console.log(data);
    try {
      // 获取当前登录组织信息
      const userInfoString = fetchStorage(2, "userInfo");
      const userInfo = JSON.parse(userInfoString) || {};
      const { org_type, owner_tree, oid } = userInfo;
      this.setState({
        tree_type: owner_tree
      });
      if (isLeaderGroup) {
        // this.fetchOrgType(1);
        // this.fetchOrgType(2, org_type);
        initOrgType({ org_id: 0, tree_type: 1 });
      }
      else {
        const response = await getOrgInfo({ org_id: oid, tree_type: owner_tree });
        const { data: body } = response;
        const { data, code, message } = body;
        if (code !== 0) {
          Message.error(message);
          return;
        }
        // console.log(data);
        const orgType1 = [{
          code: orgTypeRoot,
          op_key: data.org_type,
          op_value: data.type_name
        }];
        // console.log(orgType1);
        // this.fetchOrgType(2, org_type);
        initOrgType({ org_type, tree_type: owner_tree });
        this.setState({
          orgType1
        });
      }
      // console.log(userInfo); 
      // const orgType1 = [{
      //   code: orgTypeRoot,
      //   op_key: data.org_type,
      //   op_value: data.type_name
      // }];
      // initOrgType(data.org_type);
      // this.setState({
      //   orgType1
      // });

    }
    catch (error) {
      console.error(error);
    }

  }
  async fetchOrgType(type = 1, value) {
    let { orgTypeRoot } = this.state;
    const { form } = this.props;
    const { getFieldValue, resetFields } = form;
    let typeCode = 0;
    if (type === 1) {
      typeCode = orgTypeRoot;
    } else if (type === 2) {
      // 如果一级组织类别发生变更，重置二级组织类别选择
      resetFields(["org_type_2"]);
      typeCode = value || getFieldValue("org_type_1");
    }
    const response = await getcodeList({
      code: typeCode
    });
    const { data: body } = response;
    const { code, data, message } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (type === 1) {
      this.setState({
        orgType1: data
      });
    } else if (type === 2) {
      this.setState({
        orgType2: data
      });
    }
  }
  getParentNode(node,pname){
    let level = arguments[2] ? (arguments[2]-1) : 0;
    if(node.nodeName == 'BODY') return node;
    var tnode = node.parentNode;
    pname = pname.toUpperCase();
    while(tnode.className && tnode.className != pname)
    {
        tnode = tnode.parentNode;
    }
    if(level === 0)
    {
        return tnode;
    }
    else
    {
        return getParentNode(tnode,pname,level);
    }
  }
  onAdvancedQuery(e){
    const personModal = this.getParentNode(e.target, "person-modal")
    const {isAdvancedQuery} = this.state;
    this.setState({
      isAdvancedQuery: !isAdvancedQuery,
    })
    const tableBody = personModal.querySelectorAll("div.ant-table-body")
    const selectedList = personModal.querySelectorAll("div.selected-list")
    tableBody[0].style.maxHeight = !isAdvancedQuery ? "294px" : "459px";
    selectedList[0].style.height = !isAdvancedQuery ? "292px" : "457px";
  }
  //验证输入
  formItemValidator(r, value, callback, item){
    if(!value){
      callback()
      return
    }
    switch (r.field) {
      case "name":
          if (value.length > 30) {
            callback('不能超过30个字符');
          }
          break;
      case "keyword":
        if(value.length < 10){
          break;
        }
      case "cert_number":
        // var idreg = /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])(\d{4}|\d{3}[0-9Xx])$/;
        const idreg = /^[1-9]\d{16}[0-9|X|x]{1}$/;
        if (!idreg.test(value)) {
          callback('格式错误');
        }
        break;
      case "phone":
        if(!/^[1][3456789]\d{9}$/.test(value)){
          callback('请输入正确手机号')
        }
        break;
      case "age_one":
        if (!/^[1-9]\d{0,2}$/.test(value)) {
          callback('请填写正确的年龄')
        }
        break;
      case "age_two":
    　　if (!/^[1-9]\d{0,2}$/.test(value)) {
          callback('请填写正确的年龄')
        }
        break;
      default:
    }
    callback()
  }
  searchHandler(){
    const {form,onSearchHandler,isLeaderGroup, queryCriteria, userInfo} = this.props;
    form.validateFieldsAndScroll(async (err, values) => {
      if(!err){
        //领导班子查询
        if(isLeaderGroup){
          onSearchHandler(values.keyword)
          return
        }
        let param = {};
        for (const [key,val] of Object.entries(values)) {
          if(val){
            param[key] = key==="cert_type" ? val*1 : val
          }
        }
        //未填写输入条件
        if(Object.keys(param).length <= 1){
          Message.warn("请输入查询条件")
          return
        }
        //判断年龄
        if(param.age_one || param.age_two){
          const age_one = param.age_one ? param.age_one*1 : 1;
          const age_two = param.age_two ? param.age_two*1 : 999;
          if(age_one>age_two){
            Message.warn("请输入正确的年龄范围")
            return
          }
          param.age_one = age_one;
          param.age_two = age_two;
        }
        if(isLeaderGroup){
          let param = param.name? param.name : param.cert_number ? param.cert_number : "";
          if(param){
            param = {
              param,
            }
          }else{
            Message.warn("该页面只支持以姓名或身份证查询")
            return
          }
        }
        param.only_manager = param.only_manager ? 1 : 0;
        param.only_current_org = param.only_current_org ? 1 : 0;
        onSearchHandler(param)
      }
    })
  }
  onChangeChecke(val){
    this.setState({
      [val]: !this.state[val]
    })
  }
  render() {
    const {
      organizeData,
      orgChangeHandler,
      initOrgType,
      onSearchHandler,
      isPartyGroup,
      isLeaderGroup
    } = this.props;
    const { tagData, checked_manager, checked_current, org_type } = this.state;
    const {
      cardTypeList, recordList, ethnicList, sexList, politicalList, positionCodeList,
    } = organizeData;
    const {isAdvancedQuery} = this.state;
    const {form} = this.props;
    const { getFieldDecorator } = form;
    const selectEl = (val)=>{
      return <Select allowClear placeholder="请选择">
              {
               val.length && val.map((v, i)=>{
                  return <Option value={v.op_key} title={v.op_value} key={i}>{v.op_value}</Option>
                })
              }
            </Select>
    }
    // onSearch={(value) => this.searchHandler(value)} 
    const formProps = {
      layout: "inline",
      onSubmit(e) {
        e.preventDefault();
      },
    }
    return (
      <div className="top-form">
        <Form {...formProps}>
        {
          isLeaderGroup ? 
          <div className="item-container">
            <FormItem className="leader-form_item">
            {
              getFieldDecorator("keyword", {
                rules: [{
                  validator: this.formItemValidator
                }]
              })(
                <Search placeholder="请输入姓名或身份证号码" enterButton="查询" onSearch={()=>this.searchHandler()}/>
                )
              }
            </FormItem>
            <FormItem>
              <Button onClick={() => this.resetHandler()}  className="leader-form-btn">重置</Button>
            </FormItem>
          </div>
          :
          <div className="form-container">
            <div className="item-container">
              <FormItem  label="姓名"  className="item-container_first">
                {
                  getFieldDecorator("name", {
                    rules: [{
                      validator: this.formItemValidator
                    }]
                  })(
                    <Input placeholder="请输入用户名"/>
                  )
                }
              </FormItem>
              {
                org_type == 102811 && 
                <FormItem label="昵称">
                  {
                    getFieldDecorator("111", {
                      rules: [{
                        validator: this.formItemValidator
                      }]
                    })(
                      <Input placeholder="请输入昵称" />
                    )
                  }
                </FormItem>
              }
              <FormItem label="手机号">
                {
                  getFieldDecorator("phone", {
                    rules: [{
                      validator: this.formItemValidator
                    }]
                  })(
                    <Input placeholder="请输入手机号" />
                  )
                }
              </FormItem>
              <FormItem className="form-item_btn">
                <Button type="primary" className="form-btn" onClick={() => this.searchHandler()}> 查询 </Button>
                {org_type != 102811 && <Button onClick={(e) => this.onAdvancedQuery(e)}>高级查询 <Icon type={isAdvancedQuery ? "up":"down"}/></Button>}
                <Button onClick={() => this.resetHandler()}  className="form-btn">重置</Button>
              </FormItem>
            </div>
            <div style={{"display": isAdvancedQuery? "block": "none"}}>
            <div className="item-container">
              <FormItem label="证件类型">
                {
                  getFieldDecorator("cert_type", {
                    initialValue: cardTypeList.length && cardTypeList[0].op_key+""
                  })(
                    <Select>
                      {
                        cardTypeList.length && cardTypeList.map((v,i)=>{
                          return <Option value={v.op_key+""} title={v.op_value} key={i}>{v.op_value}</Option>
                        })
                      }
                    </Select>
                  )
                }
              </FormItem>
              <FormItem label="证件号">
                {
                  getFieldDecorator("cert_number", {
                    rules: [{
                      validator: this.formItemValidator
                    }]
                  })(
                    <Input placeholder="请输入"/>
                  )
                }
              </FormItem>
              <FormItem label="性别" className="form-input">
              {getFieldDecorator("gender", {
                })(
                  selectEl(sexList)
                )}
              </FormItem>
              <FormItem label="籍贯" className="form-input" >
                {getFieldDecorator("native_place", {
                    initialValue: ""
                })(
                  <Input placeholder="多个请以逗号隔开"/>
                )}
              </FormItem>
            </div>
            <div className="item-container">
              <FormItem label="工作岗位" className="item-container_first">
                {
                  getFieldDecorator("position", {
                  })(
                    selectEl(positionCodeList)
                  )
                }
              </FormItem>
              <FormItem label="学历" className="item-container_two">
                {
                  getFieldDecorator("education", {
                  })(
                    selectEl(recordList)
                    )
                  }
              </FormItem>
              <FormItem label="民族" className="form-input">
                {
                  getFieldDecorator("ethnic", {
                  })(
                    selectEl(ethnicList)
                    )
                  }
              </FormItem>
              <FormItem label="标签" className="form-input">
                {
                  getFieldDecorator("tag_id", {
                  })(
                    <Select allowClear placeholder="请选择">
                      {
                        tagData.length && tagData.map((v,i)=>{
                          return <Option title={v.name} value={v.tag_id} key={i}>{v.name}</Option>
                        })
                      }
                    </Select>
                  )
                }
              </FormItem>
            </div>
            <div className="item-container">
              <FormItem label="政治面貌" className="item-container_first">
                {
                  getFieldDecorator("political_type", {
                  })(
                    selectEl(politicalList)
                  )
                }
              </FormItem>
              <FormItem label="年龄" className="form-item_age age_one">
                {
                  getFieldDecorator("age_one", {
                    rules: [{
                      validator: this.formItemValidator
                    }]
                  })(
                    <Input addonAfter="岁"/>
                  )
                }
              </FormItem>
              {/* <span className="form-input_text">至</span> */}
              <FormItem label="至" colon={false} className="form-item_age">
                {
                  getFieldDecorator("age_two", {
                    rules: [{
                      validator: this.formItemValidator
                    }]
                  })(
                    <Input addonAfter="岁"/>
                  )
                }
              </FormItem>
              <FormItem className="item-only">
                {
                  getFieldDecorator("only_manager", {
                  })(
                    <Checkbox checked={checked_manager} onChange={()=> this.onChangeChecke("checked_manager")}>仅查询管理员</Checkbox>
                  )
                }
              </FormItem>
              <FormItem className="item-only">
                {
                  getFieldDecorator("only_current_org", {
                  })(
                    <Checkbox checked={checked_current} onChange={()=> this.onChangeChecke("checked_current")}>仅查询当前组织</Checkbox>
                  )
                }
              </FormItem>
            </div>
          </div>
          </div>
        }
        </Form>
      </div>
    )
  }
}

TopForm.propTypes = {
  orgChangeHandler: propTypes.func,
  initOrgType: propTypes.func,
  onSearchHandler: propTypes.func,
  setKeyword: propTypes.func,
  isPartyGroup: propTypes.bool,
  isLeaderGroup: propTypes.bool
}

TopForm.defaultProps = {
  orgChangeHandler: () => { },
  initOrgType: () => { },
  onSearchHandler: () => { },
  isPartyGroup: false,
  isLeaderGroup: false
}
const mapStateToProps = ({ organizeData, userInfo }) => ({ organizeData, userInfo});

export default connect(mapStateToProps)(Form.create({
  onValuesChange:(props, changedValues, allValues)=>{
    let prames = {}
    for (const [key,val] of Object.entries(allValues)) {
      if(val){
        prames[key] = key === "cert_type" ? val*1 : val
      }
    }
    //判断年龄
    if(prames.age_one || prames.age_two){
      prames.age_one = prames.age_one ? prames.age_one*1 : 1;
      prames.age_two = prames.age_two ? prames.age_two*1 : 999;
      if(prames.age_one>prames.age_two ){
        prames.age_two > 9 && Message.warn(('请输入正确的年龄范围'));
        delete prames.age_one
        delete prames.age_two
      }
    }
    props.setKeyword(prames)
  }
})(TopForm));