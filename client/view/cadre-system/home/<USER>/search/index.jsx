import React from "react";
import "./index.less";

import {
  Form,
  Row,
  Col,
  Checkbox,
  DatePicker,
  Input,
  Card,
  InputNumber,
  Button,
} from "antd";
import "moment/locale/zh-cn";

import locale from "antd/es/date-picker/locale/zh_CN";
import moment from "moment";
moment.locale("zh-cn");

const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;
const { RangePicker } = DatePicker;
function Index(props) {
  console.log("props", props);
  const { getFieldDecorator } = props.form;

  const plainOptions = ["男性", "女性"];

  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  const rangeConfig = {
    rules: [{ type: "array", required: true, message: "Please select time!" }],
  };
  return (
    <div className="cadre-search">
      <Form {...formItemLayout}>
        <Card>
          <div className="card-title">基本信息</div>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="性别">
                {getFieldDecorator("gender", {
                  initialValue: [],
                })(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="1">男</Checkbox>
                    <Checkbox value="2">女</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>

            <Col span={12}>
              <FormItem label="出生年月">
                {getFieldDecorator(
                  "range-picker",
                  rangeConfig
                )(<RangePicker locale={locale} />)}
              </FormItem>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="民族">
                {getFieldDecorator("ethic", {
                  initialValue: [],
                })(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="1">汉族</Checkbox>
                    <Checkbox value="2">少数民族</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>

            <Col span={12}>
              <FormItem label="政治面貌">
                {getFieldDecorator("political", {
                  initialValue: [],
                })(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="1">中共党员</Checkbox>
                    <Checkbox value="2">非党员</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="入党时间">
                {getFieldDecorator(
                  "join_time",
                  rangeConfig
                )(<RangePicker locale={locale} />)}
              </FormItem>
            </Col>

            <Col span={12}>
              <FormItem label="专业技术职务">
                {getFieldDecorator("technical_position")(<Input />)}
              </FormItem>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="熟悉专业和特长" wrapperCol={{ span: 9 }}>
                {getFieldDecorator("technical_position")(
                  <Input placeholder="请输入" />
                )}
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card>
          <div className="card-title">职务信息</div>
          <Row gutter={24}>
            <Col span={24}>
              <FormItem
                label="干部类别"
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 21 }}
              >
                {getFieldDecorator("cadre_category", {
                  initialValue: [],
                })(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="1">市管领导</Checkbox>
                    <Checkbox value="2">县管正职</Checkbox>
                    <Checkbox value="3">县管副职</Checkbox>
                    <Checkbox value="200108">乡镇正职</Checkbox>
                    <Checkbox value="200101">乡镇副职</Checkbox>
                    <Checkbox value="200102">部门正职</Checkbox>
                    <Checkbox value="200103">部门副职</Checkbox>
                    <Checkbox value="200104">企业正职</Checkbox>
                    <Checkbox value="200105">企业副职</Checkbox>
                    <Checkbox value="200106">街道正职</Checkbox>
                    <Checkbox value="200107">街道副职</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="现任职务" wrapperCol={{ span: 9 }}>
                {getFieldDecorator("current_job")(
                  <Input placeholder="请输入" />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label="任现职务时间"
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <Row style={{ margin: 0, display: "flex" }}>
                  <span>大于&nbsp;</span>
                  <FormItem
                    name="current_job_time_gte"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                  >
                    {getFieldDecorator("current_job_time_gte")(<InputNumber />)}
                  </FormItem>
                  <span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
                  <FormItem name="current_job_time_lte">
                    {getFieldDecorator("current_job_time_lte")(<InputNumber />)}
                  </FormItem>
                  <span>&nbsp;年</span>
                </Row>
              </FormItem>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem name="current_job_time_gte" label="干部职级">
                {getFieldDecorator("current_rank")(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="1">正处</Checkbox>
                    <Checkbox value="200301">副处</Checkbox>
                    <Checkbox value="200302">保留副处</Checkbox>
                    <Checkbox value="20030">正科</Checkbox>
                    <Checkbox value="5">保留正科</Checkbox>
                    <Checkbox value="6">副科</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="现职级任职时间" wrapperCol={{ span: 9 }}>
                {getFieldDecorator("current_job")(
                  <Input placeholder="请输入" />
                )}
              </FormItem>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="任现职务时间">
                <Row style={{ margin: 0, display: "flex" }}>
                  <span>大于&nbsp;</span>
                  <FormItem>
                    {getFieldDecorator("current_rank_time_gte")(
                      <InputNumber />
                    )}
                  </FormItem>
                  <span>&nbsp;年&nbsp;&nbsp;小于&nbsp;</span>
                  <FormItem name="current_rank_time_lte">
                    {getFieldDecorator("current_rank_time_lte")(
                      <InputNumber />
                    )}
                  </FormItem>
                  <span>&nbsp;年</span>
                </Row>
              </FormItem>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem name="current_job_time_gte" label="干部职级">
                {getFieldDecorator("identity")(
                  <Checkbox.Group style={{ width: "100%" }}>
                    <Checkbox value="200201">行政</Checkbox>
                    <Checkbox value="200202">事业</Checkbox>
                    <Checkbox value="200203">参公</Checkbox>
                    <Checkbox value="200204">国企</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card>
          <div className="card-title">教育信息</div>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem label="初始学历" name="fullTime_education">
                {getFieldDecorator("fullTime_education")(
                  <Checkbox.Group>
                    <Checkbox value="1">研究生</Checkbox>
                    <Checkbox value="2">大学本科</Checkbox>
                    <Checkbox value="3">大学专科</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="最高学历" name="onJob_education">
                {getFieldDecorator("fullTime_education")(
                  <Checkbox.Group value="onJob_education">
                    <Checkbox value="1">研究生</Checkbox>
                    <Checkbox value="2">大学本科</Checkbox>
                    <Checkbox value="4">大学专科</Checkbox>
                  </Checkbox.Group>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <FormItem
                label="全日制院校"
                name="fullTime_school"
                wrapperCol={{ span: 9 }}
              >
                {getFieldDecorator("fullTime_school")(<Input />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="专业" name="major" wrapperCol={{ span: 9 }}>
                {getFieldDecorator("major")(<Input />)}
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card>
          <div className="btn-box">
            <Button className="reset-btn">重置</Button>
            <Button type="primary">查询</Button>
          </div>
        </Card>
      </Form>
    </div>
  );
}

export default Form.create()(Index);
