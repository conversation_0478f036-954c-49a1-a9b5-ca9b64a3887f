/*
 * @Description:
 * @Author: b<PERSON><PERSON>
 * @Date: 2020-11-19 17:45:04
 * @LastEditTime: 2021-02-05 11:23:53
 * @LastEditors: baichao
 */
import React, { PureComponent } from "react";
import { Form, Input, Button, Checkbox } from "antd";
import DynamicForm from "client/components/dynamic-form";

export default class SearchHeader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
    this.renderFooter = this.renderFooter.bind(this);
  }

  renderFooter() {
    const { onReset, onQueryClick } = this.props;
    return (
      <div className="search-btn-group">
        <Button type="primary" htmlType="submit">
          查询
        </Button>
        <Button onClick={onReset}>重置</Button>
        {/* <Button onClick={onQueryClick}>高级查询</Button> */}
      </div>
    );
  }

  render() {
    const { formFields, getSearchFormRef, onSubmit } = this.props;
    return (
      <div
        style={{
          background: "#f0f3f6",
          padding: "15px",
        }}
      >
        <DynamicForm
          className="search-dynamic-form"
          ref={getSearchFormRef}
          fieldList={formFields}
          onSubmit={onSubmit}
          layout="inline"
          footer={this.renderFooter}
        />
      </div>
    );
  }
}
