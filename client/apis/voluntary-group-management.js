const { http } = require('client/tool/axios');
import { Yapi, host } from './config';

// 通过code获取数据字典的相应数据
export const getCodeList = ({ code = '' }) => {
  return http.get(`${host}/uc/op/list`, { code });
};
//105001018-定位组织树组织
export const getLocate = (data) => {
  return http.get(`${host}/org/locate`, data);
};
//0503-根据pid获取父级的下级区域列表
export const fetchAddressOption = (pid = '') => {
  return http.get(`${host}/dict/area/list`, { pid });
}
//0506-根据区域码获取各个级别的城市列表
export const getAddressOptionAll = (adcode = '') => {
  return http.get(`${host}/dict/area/all`, { adcode });
}
/* // 10630202010208-志愿者团体树
export const getVolunteerTeamTree = (data) => {
  return http.get(`${Yapi}/volunteer/team/tree`, data);
} */
export const getOrgTree = (data) => {
  return http.get(`${host}/org/tree`, data);
};

//10630202010203-查询志愿者团体/志愿者数量
export const findVolunteerCount = (data) => {
  return http.get(`${host}/volunteer/team/find-volunteer-count`, data);
}
// 10630202010209-志愿者团体详细信息
export const volunteerTeamInfo = (data) => {
  return http.get(`${host}/volunteer/team/info`, data);
}
// 10630202010204-根据团体名称查询
export const findVolunteerByWhere = (data) => {
  return http.get(`${host}/volunteer/team/find-volunteer-by-where`, data);
}
// 10630202010201-新建/注册志愿者团体
export const addVolunteerTeam = (data) => {
  return http.post(`${host}/volunteer/team/add`, data);
}
// 10630202010202-修改志愿者团体
export const updateVolunteerTeam = (data) => {
  return http.post(`${host}/volunteer/team/update`, data);
}
// 10630202010210-调整志愿团体顺序
export const volunteerTeamUpdateSeq = (data) => {
  return http.post(`${host}/volunteer/team/update-seq`, data);
}
// 10630202010205-启用/停用/删除团体
export const volunteerTeamUpdateStatus = (data) => {
  return http.post(`${host}/volunteer/team/update-status`, data);
}
// 10630202010213-根据团体ID查询团体管理员
export const findTeamMgrById = (data) => {
  return http.get(`${host}/volunteer/team/find-team-mgr-by-id`, data);
}
