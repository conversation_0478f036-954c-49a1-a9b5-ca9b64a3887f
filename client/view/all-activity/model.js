import { message } from 'antd/lib/index';

import {
    loadAllActivity,
    searchActivity,
    getColumn,
    deleteActive,
    deleteDraft,
    moveActive,
    sendNotice,
    pushto,
    pushshow
} from 'apis/activity';

const treeTo = (tree) => {
    for (let i in tree) {
        tree[i].value = tree[i].activity_column_id;
        tree[i].label = tree[i].name;
        if (tree[i].children != []) {
            treeTo(tree[i].children);
        }
    }
    return tree;
}
export default {
    // 模型里面的子类   类似   {store : {root-manage:{}}}
    namespace: 'allActivity',

    state: {
        //活动列表
        dataList: [],
        listLoading: false,
        //栏目列表
        crownList: [],
        columLoading: false,
        pageNew: {
            pageNum: '',
            pageSize: '',
            pages: '',
            total: ''
        },
        //预览模态框是否显示，默认为不显示
        modalVisible: false,
        //预览地址
        url: '',
        //通知侧边栏控制符，控制是否折叠
        isHideWrap: false,
        collapsed: true,
        //通知模态框暂存当前活动信息
        currentActivityInfo: {},
        //控制提交按钮
        submitButtonLoading: false,
        params: {
            status: "",
            type: "",
            cid: "",
            timepoint: "",
            keyword: ""
        }
    },

    // 定义业务交互层
    effects: {
        * activityPushHide({ payload }, { put, call, select }) {
            // console.log(payload);
            let response = yield pushshow(payload);
            // 重新加载数据
            // console.log(response);
            const { data: body } = response;
            const { code, message } = body;
            if (code !== 0) {
                throw new Error(message);
            } else {
                return message;
            }

            // console.log(response);
        },
        //全部活动的查询
        * loadAllActivity({ payload: { page = 1 } }, { put, select }) {
            const allActivity = yield select(state => state.allActivity);
            const { params, pageNew } = allActivity;
            // console.log(allActivity);
            // console.log(params);
            yield put({ type: 'save', payload: { listLoading: true } });
            let res = yield loadAllActivity({ page: pageNew.pageNum, ...params });
            let { data: response } = res;
            yield put({ type: 'save', payload: { listLoading: false } });
            if (response.code !== 0) {
                throw new Error(response.message);
            }
            if (response.code === 0 && response.message === 'success') {
                let { data } = response;
                yield put({ type: 'loadData', payload: { dataList: data } });
                yield put({
                    type: 'setPage',
                    payload: {
                        pageNum: response.pageNum,
                        pageSize: response.pageSize,
                        pages: response.pages,
                        total: response.total
                    }
                });
            }
        },
        //查询活动
        *searchActivity({ payload: { page = 1, status, type, cid, timepoint, pm, keyword } }, { put }) {
            // 显示loading
            yield put({
                type: 'save', payload: {
                    listLoading: true,
                    params: {
                        status,
                        type,
                        cid,
                        timepoint,
                        pm,
                        keyword
                    }
                }
            });
            // console.log(page, status, type, cid, timepoint, keyword)
            // 加载列表数据
            const result = (yield searchActivity({ page, status, type, cid, timepoint, pm, keyword })).data;
            yield put({ type: 'save', payload: { dataList: result.data, listLoading: false } });
            // 如果请求错误
            if (result.code !== 0) {
                // return message.error(result.message);
                throw new Error(result.message);
            } else {
                yield put({
                    type: 'setPage',
                    payload: {
                        pageNum: result.pageNum,
                        pageSize: result.pageSize,
                        pages: result.pages,
                        total: result.total
                    }
                });
            }

        },
        * getColumn({ }, { put }) {
            yield put({ type: 'save', payload: { columLoading: true } });

            const result = (yield getColumn()).data;
            const columList = treeTo(result.data);

            yield put({ type: 'save', payload: { columLoading: false } });

            yield put({ type: 'save', payload: { crownList: columList, columLoading: false } });
        },
        * deleteActive({ payload }, { put }) {
            // console.log(payload);
            const { id, type } = payload;
            // return
            yield put({ type: 'save', payload: { listLoading: true } });
            // 加载列表数据
            const result = (yield deleteActive({ id, type })).data;
            // console.log(result);
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            }
            yield put({ type: 'save', payload: { listLoading: false } });
        },
        * deleteDraft({ payload }, { put }) {
            // console.log(payload);
            const { id, type } = payload;
            // return
            yield put({ type: 'save', payload: { listLoading: true } });
            // 加载列表数据
            const result = (yield deleteDraft({ draft_id: id })).data;
            // console.log(result);
            // 如果请求错误
            if (result.code !== 0) {
                yield put({ type: 'save', payload: { listLoading: false } });
                throw new Error(result.message);
            }
            yield put({ type: 'save', payload: { listLoading: false } });
        },
        * moveActive({ payload: { aids, cid } }, { put }) {
            const result = (yield moveActive({ aids, cid })).data;
            // 如果请求错误
            if (result.code !== 0) {
                return message.error(result.message);
            } else {
                message.success('移动互动成功');
                yield put({ type: 'loadAllActivity', payload: { page: 1 } });
            }
        },
        *sendNotice({ payload: { postObject } }, { put }) {
            // console.log(postObject);
            const response = yield sendNotice(postObject);
            // console.log(response);
            const { data: body } = response;
            const { data, code, message } = body;
            if (code !== 0) {
                throw new Error(message);
            }
            return data;
        },
        //推送活动 功能相同 用activitysMatrix.pushto代替
        // *pushto({ payload }, { put }) {
        //   yield put({
        //       type: 'save',
        //       payload: {pushing: true}
        //   });
        //   ///http://10.10.100.12:8090/pages/viewpage.action?pageId=13009155
        //   const response = yield pushto(payload);
        //   // console.log(response);
        //   const { data: body } = response;
        //   const { data, code, message } = body;
        //   if (code !== 0) {
        //       throw new Error(message);
        //   }
        //   yield put({
        //       type: 'save',
        //       payload: {pushing: false}
        //   });
        //   return data;
        //
        // }
    },
    reducers: {
        save(state, { payload }) {
            return { ...state, ...payload };
        },
        loadData(state, { payload }) {
            return { ...state, ...payload };
        },
        setPage(state, { payload }) {
            return { ...state, pageNew: { ...state.pageNew, ...payload } };
        },
        toggleCollapsed(state) {
            //如果当前侧边栏为显示状态，在收起时将表单状态重置
            return { ...state, collapsed: !state.collapsed };
        },
        toggleHideWrap(state) {
            return { ...state, isHideWrap: !state.isHideWrap };
        },
        resetFormParams(state) {
            return {
                ...state,
                params: {
                    status: "",
                    type: "",
                    cid: "",
                    timepoint: "",
                    keyword: ""
                },
                pageNew: {
                    pageNum: '',
                    pageSize: '',
                    pages: '',
                    total: ''
                },
            };
        }
    }
}
