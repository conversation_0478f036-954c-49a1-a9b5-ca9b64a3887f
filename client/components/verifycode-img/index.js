import React, { Component } from "react";
import "./style.less";
import PropTypes from "prop-types";
import { randomString } from "client/tool/util";
import { showCaptcha } from "client/apis/active-organization";
import { Input } from "antd";

class VerifyCodeImg extends Component {
  constructor(props) {
    super(props);

    this.state = {
      value: this.props.value,
      captchaImgSrc: "",
    };
  }

  componentDidMount() {
    this.changeCaptcha();
  }

  changeCaptcha() {
    const uuid = randomString();
    const captchaImgSrc = showCaptcha(uuid);
    this.props.changeState && this.props.changeState({ uuid, captchaImgSrc });
    this.setState({ captchaImgSrc });
  }

  triggerChange(e) {
    this.setState({ value: e.target.value });
    this.props.onChange(e.target.value);
  }

  render() {
    return (
      <div className="verifycode-img">
        <Input
          value={this.state.value}
          placeholder="请输入右侧验证码"
          onChange={this.triggerChange.bind(this)}
          style={{ width: "72%", marginRight: "2%" }}
        />
        <img
          src={this.state.captchaImgSrc}
          onClick={this.changeCaptcha.bind(this)}
          style={{ verticalAlign: "top", width: "26%" }}
        />
      </div>
    );
  }
}

VerifyCodeImg.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  changeState: PropTypes.func,
};

export default VerifyCodeImg;
