/*
 * @Author: l<PERSON><PERSON><PERSON>
 * @description:  数据检查
 * @Date: 2022-02-10 13:59:18
 * @Last Modified by: l<PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-15 11:38:35
 */
import React, {
  Fragment,
  useMemo,
  useState,
  useEffect,
  lazy,
  Suspense,
} from "react";
import "./index.less";
import { Button, Tabs, message } from "antd";
import normalPng from "../img/1.png";
import checkingPng from "../img/checking.png";
import securityPng from "../img/security.png";
import abnormalPng from "../img/2.png";
import TableCollection from "./component/TableComponent";
import {
  getAbnormalCheck,
  getExamineUserOrOrg,
} from "apis/data-physical-examination";

const CollapseComponent = lazy(() => import("./component/CollapseComponent"));

const { TabPane } = Tabs;
const imgMap = {
  0: normalPng,
  2: checkingPng,
  3: securityPng,
};
export default function CheckCiew({
  history,
  checkStatus,
  checkResultData,
  onHandClick,
  className,
}) {
  // flag 	0:组织范围 1:党员范围
  const {
    org_id,
    org_name,
    flag,
    update_time,
    org_num,
    user_num,
    examine_num = 0,
    org_examine_num = 0,
    down_org_examine_num = 0,
    user_examine_num = 0,
  } = checkResultData;
  const [tabKey, setTabKey] = useState("3");
  const [pageNum, setPageNum] = useState(1);
  const [tableLoading, setTableLoading] = useState(false);
  const [tableData, setTableData] = useState({});
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerParams, setDrawerParams] = useState({});

  //切换组织时默认查询按异常项统计数据
  useEffect(() => {
    if (tabKey === "3" && flag !== undefined) {
        getTableData(org_id, flag);
    } else {
      setTabKey("3");
    }
  }, [org_id]);

  // 分页获取数据
  useEffect(() => {
    getTableData(org_id, flag, 1);
  }, [tabKey]);

  // 分页获取数据
  useEffect(() => {
    getTableData(org_id, flag, pageNum);
  }, [pageNum]);

  const getTableData = (org_id, flag, page = 1, page_size = 10) => {
    if (!org_id) {
      return false;
    }
    // 判断调用哪个接口 tabkey 3 异常项  其他调用getExamineUserOrOrg根据flag获取数据
    const apis = Number(tabKey) === 3 ? getAbnormalCheck : getExamineUserOrOrg;
    setTableLoading(true);
    apis({ org_id, flag, page, page_size }).then(({ data: res }) => {
      const { code, data, total } = res;
      setTableLoading(false);
      if (code !== 0) {
        message.error(res.message);
        return false;
      }
      setTableData({
        total,
        dataSource: data,
      });
    });
  };

  // tab切换获取数据
  const tabCallback = (key) => {
    // 表格数据加载时不允许切换
    if (tableLoading) {
      return false;
    }
    setPageNum(1);
    setTabKey(key);
    setTableData({});
  };

  const tableEl = useMemo(() => {
    // 异常表格prop
    const statisticsProps = {
      tableLoading,
      tableData,
      tabKey: Number(tabKey),
      pageNum,
      setPageNum,
      onChangeDrawerData: (record) => {
        setDrawerVisible(true);
        //flag 0:组织范围 1:党员范围
        record.flag = record.mark !== undefined ? record.mark : flag;
        record.selected_oid = org_id;
        record.selected_orgName = org_name;
        setDrawerParams(record);
      },
    };
    return <TableCollection {...statisticsProps} />;
  }, [tableLoading, tableData]);

  const collapseComponent = useMemo(() => {
    const drawerProsp = {
      history,
      setDrawerVisible,
      drawerParams,
      visible: drawerVisible,
    };
    return <CollapseComponent {...drawerProsp} />;
  }, [drawerVisible]);

  // console.log("重新检测", tableData, tabKey, org_id);
  return (
    <div className={`data-check-view ${className}`}>
      {checkStatus !== 1 ? (
        <div className="check-view">
          <img src={imgMap[checkStatus]} />
          {checkStatus !== 2 ? (
            <Fragment>
              <div className="check-text">
                {checkStatus === 0
                  ? "暂无检测数据，现在开始检测吧！"
                  : "太棒了！本次检测无异常"}
              </div>
              <div className="btn-box">
                <Button type="primary" onClick={() => onHandClick(org_id, 1)}>
                  {checkStatus === 0 ? "开始检测" : "重新检测"}
                </Button>
              </div>
            </Fragment>
          ) : (
            <div className="checking-text">检测中......</div>
          )}
        </div>
      ) : (
        <div className="abnormal-view">
          <div className="abv__header-box">
            <img src={abnormalPng} />
            <div className="right__text-box">
              <div className="text-box">
                <div className="abnormal-count">
                  本次检测共发现 {examine_num} 项异常
                </div>
                <div className="sub-label">
                  本组织有 <i>{org_examine_num}</i> 项异常
                </div>
                <div className="sub-label">
                  共 {org_num || user_num} 个{flag ? "党员" : "下属组织"}存在有{" "}
                  <i>{flag ? user_examine_num : down_org_examine_num}</i> 项异常
                </div>
              </div>
              <div className="operation-box">
                <Button type="primary" onClick={() => onHandClick(org_id, 1)}>
                  重新检测
                </Button>
                <div className="label-box">数据更新时间：{update_time}</div>
              </div>
            </div>
          </div>
          <Tabs
            activeKey={tabKey}
            className="tabs"
            size="small"
            tabBarGutter={60}
            onChange={tabCallback}
          >
            <TabPane tab="按异常项统计" key={"3"}>
              {Number(tabKey) === 3 && tableEl}
            </TabPane>
            <TabPane
              tab={flag ? "按党员统计" : "按组织统计"}
              key={String(flag)}
            >
              {Number(tabKey) !== 3 && tableEl}
            </TabPane>
          </Tabs>
        </div>
      )}
      <Suspense fallback={<div>Loading...</div>}>{collapseComponent}</Suspense>
    </div>
  );
}
