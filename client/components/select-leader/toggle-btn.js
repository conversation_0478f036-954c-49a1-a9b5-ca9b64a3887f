import React, { PureComponent } from 'react';
import SelfIcon from "../../components/self-icon";
import { Button } from 'antd';

/**
 * 展开收起
 */
export default class ToggleBtn extends PureComponent {
  render() {
    const { collapse, onChange } = this.props;
    return (
      <Button type="link" onClick={() => onChange(!collapse)}>
        <SelfIcon type={!collapse ? "plus-square" : "minus-square"} />
        {!collapse ? "展开" : "收起"}
      </Button>
    )
  }
}
