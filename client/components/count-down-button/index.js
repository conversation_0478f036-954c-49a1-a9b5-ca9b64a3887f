import React, { Component } from 'react';
import { Button } from 'antd';
import PropTypes from 'prop-types';
import './index.less';

class CountDownButton extends Component {
  constructor(props) {
    super(props);
    this.state = {
      time: 0,
      counting: false
    }
    this.startCounting.bind(this);
    this.stopCounting.bind(this);
  }

  componentWillReceiveProps(nextProps) {

  }

  //开始计时
  startCounting() {
    // console.log('开始计时', this.props);
    const { time } = this.props;
    let timer = null;
    this.setState({
      time: time || 59
    }, () => {
      timer = setInterval(() => {
        if (this.state.time > 0) {
          this.setState({
            time: --this.state.time
          })
        } else {
          this.stopCounting(timer);
        }
      }, 1000);
    });
  }

  //停止计时
  stopCounting(timer) {
    // console.log('结束计时', timer);
    clearInterval(timer);
    timer = null;
  }

  render() {
    const {
      ref,
      style,
      loading,
      type,
      children,
      wrapperClassName,
      buttonClassName,
      clickHandler
    } = this.props;

    const disabledStyle = {
      borderColor: '#E5E5E5',
      color: '#999',
      backgroundColor: '#f5f5f5'
    };
    return (
      <div className={wrapperClassName || 'count-down-wrapper'} ref={ref} >
        {
          children ||
          <Button
            loading={loading}
            type={type || ''}
            style={this.state.time > 0 ? Object.assign({}, style, disabledStyle) : Object.assign({}, style)}
            className={buttonClassName ? buttonClassName : ''}
            disabled={this.state.time > 0}
            onClick={() => {
              if (clickHandler) {
                clickHandler();
              } else {
                this.startCounting();
              }
            }}>
            <span style={{ color: 'inherit' }}>{this.state.time > 0 ? `重发${this.state.time ? `(${this.state.time}s)` : ''}` : '获取验证码'}</span>
          </Button>
        }
      </div>
    )
  }
}

//参数检测
CountDownButton.propTypes = {
  loading: PropTypes.bool,
  type: PropTypes.string,
  time: PropTypes.number,
  wrapperClassName: PropTypes.string,
  buttonClassName: PropTypes.string,
  clickHandler: PropTypes.func
}
//组件默认状态
CountDownButton.defaultProps = {
  // ref: 'count-down-button',
  loading: false,
  type: '',
  time: 0,
  // wrapperClassName: '',
  // buttonClassName: '',
  // clickHandler: () => { }
}

export default CountDownButton;