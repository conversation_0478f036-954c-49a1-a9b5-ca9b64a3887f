import React from "react";
import { Form, Input, Button, Upload, message as Message } from "antd";
import ReportContent from "components/report-content";
import InputTips from "components/activity-form/sub-components/InputTips";
import ShowUploadFileType from "components/show-upload-file-type";
import LoadingModal from "components/loading-modal";
import FileUpload from "components/file-upload";

const FormItem = Form.Item;
const TextArea = Input.TextArea;

// 等待上传
let uploading = false,
  waitUploadList = [],
  waitUploadCount = 0;

class Step1 extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      maxAmount: 9
    };
  }

  componentDidMount() {
    setTimeout(() => {
      const { contents } = this.props;
      this.reportContent.batchAddContent(contents);
    }, 0)
  }

  render() {
    const {
      formItemLayout,
      next,
      form,
      updateState,
      state,
      history
    } = this.props;
    const { maxAmount } = this.state;
    const { getFieldDecorator, getFieldValue, setFields } = form;
    // 最大限定文件上传个数
    let count = maxAmount;

    // 上传图片
    const uploadProps = {
      name: "upfile",
      action: state.uploadFileUrl,
      multiple: true,
      data: {
        upType: "file"
      },
      showUploadList: false,
      headers: {
        'x-csrf-token': typeof window !== 'undefined' ? window.sessionStorage.getItem('csrf') || '' : '',
        'Access-Control-Allow-Credentials': 'true',
        '_tk': typeof window !== 'undefined' ? window.sessionStorage.getItem('_tk') || '-1' : '-1',
        '_uid': typeof window !== 'undefined' ? window.sessionStorage.getItem('_uid') || '1' : '1',
        '_un': typeof window !== 'undefined' ? window.sessionStorage.getItem('_un') || '1' : '1',
        '_type': typeof window !== 'undefined' ? window.sessionStorage.getItem('_type') || '2' : '2',
        '_oid': typeof window !== 'undefined' ? window.sessionStorage.getItem('_type') || '1' : '1'
      },
      beforeUpload: (file, list) => {
        // console.log("beforeUpload", list);
        if ((list.length + state.files.length) > maxAmount) {
          count--;
          // console.log(count, maxAmount - list.length);
          if (count === (maxAmount - list.length)) {
            Message.warn(`限定上传${maxAmount}个文件`);
            count = maxAmount;
          }
          waitUploadList = [];
          waitUploadCount = 0;
          return false;
        } else {
          // console.log("期待执行这里", list);
          if (!uploading) {
            uploading = true;
          }
          waitUploadList = list;
          waitUploadCount = list.length;
          // console.log(waitUploadList, waitUploadCount);
        }
      },
      // onSuccess: (a, b, c) => {
      //   console.log("onSuccess");
      // },
      // onError: () => {

      // },
      onChange: ({ file, fileList, event }) => {
        // console.log("onChange", file.status);
        // updateState({
        //   uploadLoading: true
        // });
        // 上传的时候清除错误
        if (file.status === "done") {
          // console.log("onChange", fileList, waitUploadList, waitUploadCount);
          const { response } = file;
          const { code, message, data } = response;

          if (code === 0 && message === "success") {
            updateState({
              // uploadLoading: false,
              files: state.files.concat(data)
            });
            if (waitUploadCount > 1 && waitUploadList.length !== 0) {
              waitUploadCount--;
            }
            else if (waitUploadCount === 1 && waitUploadList.length !== 0) {
              waitUploadCount = 0;
              waitUploadList = [];
              uploading = false;
            }
            else {
              uploading = false;
            }
          } else {
            Message.error("上传失败");
            // updateState({
            //   uploadLoading: false
            // });
            uploading = false;
          }
        } else if (file.status !== "uploading") {
          // updateState({
          //   uploadLoading: false
          // });
          uploading = false;
        }
      }
    }

    // 校验设置第一步表单错误
    const setFormError = (_state) => {
      const { name, description, files, contents } = _state;
      if (name === "") {
        // setFields({ ["name"]: { value: "", errors: [new Error("议题名称必填")] } });
        setFields({ ["name"]: { value: "", errors: [new Error("任务名称必填")] } });
      }
      // if (description === "") {
      //   setFields({["description"]: { value: "", errors: [new Error("议题描述必填")] }})
      // }
      // if (files.length <= 0) {
      //   setFields({["files"]: { value: [], errors: [new Error("关联附件必传")] }})
      // }
      if (contents.length <= 0) {
        // setFields({ ["contents"]: { value: "", errors: [new Error("议题内容必填")] } });
        setFields({ ["contents"]: { value: "", errors: [new Error("任务内容必填")] } });
        // Message.error("议题内容必传");
      }
    }
    const nextStep = () => {
      if (this.reportContent) {
        const content = this.reportContent.exportData();
        updateState({
          contents: content
        }, () => next(setFormError))
      }
    }

    // console.log(uploading);
    const loadingModalProps = {
      modalVisible: uploading,
      tip: "上传中..."
    }

    return (
      <div>
        <Form>
          <FormItem
            // label="议题名称"
            label="任务名称"
            {...formItemLayout}
          >
            <InputTips
              max={state.topicNameSize}
              // container={container}
              text={getFieldValue("name")}
            >
              {getFieldDecorator("name", {
                initialValue: state.name,
                rules: [
                  {
                    max: state.topicNameSize,
                    message: `最多输入${state.topicNameSize}个汉字`
                  }
                ]
              })(<Input placeholder="请输入" rows={4} onBlur={(e) => updateState({ name: e.target.value })} />)}
            </InputTips>
          </FormItem>
          <FormItem
            // label="议题描述"
            label="备注"
            {...formItemLayout}
          >
            <InputTips
              max={state.topicDescSize}
              // container={container}
              text={getFieldValue("description")}
            >
              {getFieldDecorator("description", {
                initialValue: state.description,
                rules: [
                  {
                    max: state.topicDescSize,
                    message: `最多输入${state.topicDescSize}个汉字`
                  }
                ]
              })(<TextArea placeholder="请输入" rows={4} onBlur={(e) => updateState({ description: e.target.value })} />)}
            </InputTips>
          </FormItem>
          <FormItem
            // label="关联附件"
            label="相关资料"
            {...formItemLayout}
            className="uploadWrap"
          >
            {getFieldDecorator("files", {})(
              <FileUpload
                // max={9}
                uploadedList={state.files}
                onChange={(files) => {
                  // console.log(files);
                  updateState({
                    files
                  });
                }} />
            )}
            <div style={{ marginTop: 20 }}>
              <ShowUploadFileType data={state.files}
                updateState={(data) => updateState({ files: data })}
                loading={state.uploadLoading}
                isDelete={true} />
            </div>

            {/* <div style={{ position: "relative" }}>
              {
                uploading &&
                <LoadingModal {...loadingModalProps} />
              }
              {getFieldDecorator("files", {})(
                <Upload className="upload" {...uploadProps}>
                  <a className="uploadAtarg" onClick={(e) => {
                    // console.log(state.files);
                    if (state.files && state.files.length >= maxAmount) {
                      e.stopPropagation();
                      Message.warn(`限定上传${maxAmount}个文件`);
                    }
                  }}>上传附件</a>
                </Upload>
              )}
              <div style={{ marginTop: 28 }}>
                <ShowUploadFileType data={state.files} updateState={(data) => updateState({ files: data })} loading={state.uploadLoading} isDelete={true} />
              </div>
            </div> */}

          </FormItem>
          <FormItem
            // label="议题内容"
            label="任务内容"
            {...formItemLayout}
            className="no-margin"
          >
            {
              getFieldDecorator("contents", {})(
                <input type="hidden" />
              )
            }
            <ReportContent
              ref={(ref) => {
                this.reportContent = ref
              }}
              afterChange={() => {
                // 清除contents的
                setFields({ ["contents"]: { value: "", errors: [] } });
              }}
            />
          </FormItem>
          <FormItem
            wrapperCol={{ span: 20, offset: 2 }}
            style={{ marginTop: 50 }}
          >
            <Button
              type="primary"
              className="btnWrapper"
              onClick={nextStep}
            >
              下一步
            </Button>
            <Button
              className="btnWrapper"
              style={{ marginLeft: 28 }}
              onClick={() => history.goBack()}
            >
              取消
            </Button>
          </FormItem>
        </Form>
      </div>
    )
  }



}

export default Step1;