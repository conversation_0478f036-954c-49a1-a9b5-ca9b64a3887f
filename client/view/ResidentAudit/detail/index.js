import SearchHeader from 'components/search-header';
import Info from "./components/Info.jsx";
import Flow from "./components/flow.jsx";
import Audit from "./components/audit.jsx";
import "./index.less";

const index = (props) => {
  const { location, history } = props
  const { state } = location
  return (
    <div>
      <SearchHeader
        onBack={() => history.goBack()}
        title={state.isAudit ? `审核` : `详情`}
      />
      <Info {...state} />
      {state.state.workflow_task_id == undefined ? '' : <Flow {...state} />}
      {state.state.isAudit && <Audit {...state} />}
    </div>

  );
};

export default index;
