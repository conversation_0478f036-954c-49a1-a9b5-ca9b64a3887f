.person-selector-wrapper {
  padding-top: 15px;
  color: #333;
  .person-count {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .selector-label {
      // margin-right: 15px;
      &::after {
        content: ":";
        margin: 0 5px;
      }
    }
    .selected-count {
      margin: 0 5px 0 0;
    }
    .selector-trigger {
      margin-right: 10px;
    }
  }
  .person-table {
    .person-table-wrapper {
      padding-bottom: 15px;
      .sign-status {
        color: #999;
        &.sign-status-1 {
          color: #22AC38;
        }
        &.sign-status-3,
        &.sign-status-4 {
          color: #00A0E9;
        }
      }
      .handler-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-around;
      }
    }
    .ant-tabel-body{
      height: auto;
    }
  }
  .ie_scroll_table {
    .ant-table-body{
      height: 560px;
    }
  }
}

.absence-modal {
  .absence-container {
    padding: 26px 46px;
    .absence-wrapper {
      display: flex;
      margin-bottom: 30px;
      .label {
        text-align: right;
        width: 100px;
        flex-shrink: 0;
        margin-right: 5px;
        &::after {
          content: "：";
        }
      }
      .content {
        overflow: hidden;
        word-break: break-all;
        flex-grow: 1;
      }
    }
    .buttons-wrapper {
      padding-top: 20px;
      text-align: center;
      .ant-btn {
        width: 115px;
        height: 36px;
      }
    }
  }
}