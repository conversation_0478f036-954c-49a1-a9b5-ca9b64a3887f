import { List } from 'immutable'
import { requestPrizeList } from 'apis/activity'
export default {
    namespace: 'selectCommodity',
    state: {
        commodityData: [],
        pageSize: 5,
        currentPage: 1,
        pageSum: 1,
        total: 0,
        loading: false
    },
    effects: {
        *getPrizeList({ payload }, { call, put, select }) {
            yield put({ type: 'updateState', payload: { loading: true } })
            const { pageSize } = yield select(state => state.selectCommodity)
            const { page, pageSize: pSize } = payload
            const { data = {} } = yield requestPrizeList(page, pSize || pageSize)

            yield put({
                type: 'updateState',
                payload: {
                    commodityData: data.data,
                    pageSize: data.pageSize,
                    currentPage: data.pageNum,
                    pageSum: data.pages,
                    total: data.total,
                    loading: false
                }
            })
        }
    },
    reducers: {
        //更新state
        updateState(state, { payload }) {
            return {
                ...state,
                ...payload
            }
        }
    }
}