import React, { useState, useMemo, useRef, useEffect } from "react";
import SearchHeader from "components/search-header";
import {
    Button,
    Form,
    Input,
    Radio,
    TreeSelect,
    Checkbox,
    Select,
    message,
    InputNumber,
} from "antd";
import { uploadBase64File } from "apis/file";
// import Editor from "client/components/RichTextEditor";
import InputTips from "components/activity-form/sub-components/InputTips";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import Image from "../../../../components/image";
import { addResource, editResource, findById } from "apis/resource-library";
import TeacherPreviewModal from "../TeacherPreviewModal/index";
import './index.less'

const { Item } = Form;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 10 },
};
const { name, oid, root_oid } = JSON.parse(sessionStorage.getItem("userInfo") || {});
const seq_number = [...Array(100)].map((_, index) => index + 1);
const tagsList = [
    { label: "市内", value: "市内" },
    { label: "市外", value: "市外" },
];

const methods = {
    1: addResource,
    2: editResource,
};

const NewlyIncreased = (props) => {
    const {
        history,
        form: {
            getFieldDecorator,
            getFieldValue,
            setFieldsValue,
            validateFieldsAndScroll,
        },
        location: {
            state: {
                type, // 1 新增 2编辑
                categoryList,
                resource_library_id,
            },
        },
    } = props;
    // const editor = useRef(null);
    const [selOrg, setSelOrg] = useState(
        (type == 1 && oid === root_oid)
            ? []
            :
            [
                {
                    org_name: name,
                    org_id: oid,
                },
            ]);
    const [img, setImg] = useState([]); //照片
    const [dataSource, setDataSource] = useState({}); //数据
    const [content, setContent] = useState(null);
    const [catagoryId, setCategoryId] = useState(null);
    const [orgModalVisible, setOrgModalVisible] = useState(false);
    const [previewModalData, setPreviewModalData] = useState({});
    const [previewModalVisible, setPreviewModalVisible] = useState(false);
    // const [textNum, setTextNum] = useState(0);

    useEffect(() => {
        if (resource_library_id) {
            findById({ resourceLibraryId: resource_library_id }).then(
                ({ data: res }) => {
                    if (res.code !== 0) {
                        message.error(res.message);
                        return;
                    }
                    setCategoryId(res.data.resource_category_id);
                    setSelOrg([
                        {
                            org_id: res.data.provider_org_id,
                            org_name: res.data.provider_org_name,
                        },
                    ]);
                    setContent(res.data.content);
                    // editor.current.setEditorContent(res.data.content);
                    setImg({ path: res.data.cover_url });
                    setDataSource(res.data);
                }
            );
        }
    }, []);

    console.log(type);
    const onHandleSubmit = (isPreview = false) => {
        validateFieldsAndScroll((err, res) => {
            if (content === "<p><br></p>") {
                message.warn("请输入内容");
                return;
            }
            console.log(res);
            if (!err) {
                const { name, tags, sources, sort, seq, job, profession_level, content, contact_details, belong_unit } = res;
                let params = {
                    name,
                    sources,
                    content,
                    job,
                    profession_level,
                    contact_details,
                    belong_unit,
                    resource_category_id: catagoryId,
                    cover_url: img ? img.path : "",
                    provider_org_id: selOrg[0].org_id,
                    provider_org_name: selOrg[0].org_name,
                    tags: tags && tags.length ? tags.join(",") : null,
                    resource_type: 2,
                };
                if (sort === 2) {
                    params.seq = seq;
                }
                if (resource_library_id) {
                    params.resource_library_id = resource_library_id;
                }
                if (isPreview) {
                    setPreviewModalData(params);
                    setPreviewModalVisible(true);
                } else {
                    methods[type](params).then(({ data: res }) => {
                        if (res.code !== 0) {
                            message.error(res.message);
                            return;
                        }
                        message.success(`${type == 1 ? "新增" : "编辑"}成功`);
                        history.goBack();
                    });
                }
            }
        });
    };

    const clipEvent = () => {
        const upload = (base64) => {
            return new Promise((resolve, reject) => {
                uploadBase64File({
                    upfile: base64,
                    upType: "image",
                })
                    .then(({ data: res }) => {
                        if (res.code != 0) {
                            message.error(res.message);
                            reject("");
                            return;
                        } else {
                            setImg(res.data);
                            resolve();
                        }
                    })
                    .catch((e) => j(e));
            });
        };
        return { upload };
    };

    const checkSelect = (rule, value) => {
        return new Promise((resolve, reject) => {
            if (value && value.length > 3) {
                reject("标签最多选中3个");
            } else {
                resolve();
            }
        });
    };

    const orgModalProps = {
        visible: orgModalVisible,
        dataSource: selOrg,
        hideModal: () => setOrgModalVisible(false),
        radio: true,
        hasRoot: false,
        onlyDirectSub: true,
        rootOrgId: oid,
        loadOrganizeData: (data) => {
            setOrgModalVisible(false);
            setSelOrg(data);
            setFieldsValue({ provider_org_name: data && data.length > 0 ? data[0].org_name : '' });
            // setFieldsValue('provider_org_name', data[0].org_name)
            // console.log(data);
        },
    };

    const previewModalProps = {
        previewModalData,
        visible: previewModalVisible,
        onCancel: () => setPreviewModalVisible(false),
    };
    const PreviewModalMemo = useMemo(() => {
        return <TeacherPreviewModal {...previewModalProps} />;
    }, [previewModalProps]);

    console.log(selOrg);
    return (
        <div className="NewlyIncreased">
            <SearchHeader title={type == 2 ? '编辑资源' : '上传资源'} onBack={() => history.goBack()} />
            <Form className="NewlyIncreased-form" {...formItemLayout}>
                <Item label="师资分类" required>
                    {getFieldDecorator("resource_category_id", {
                        initialValue: catagoryId,
                        // initialValue: null,
                        rules: [
                            {
                                required: true,
                                message: "请选择师资分类",
                            },
                        ],
                    })(
                        <TreeSelect
                            style={{ width: "100%" }}
                            dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                            treeData={categoryList}
                            placeholder="请选择文件分类"
                            treeDefaultExpandAll
                            onChange={(val) => setCategoryId(val)}
                        />
                    )}
                </Item>
                <Item label="照片">
                    {getFieldDecorator(
                        "image_url",
                        {}
                    )(
                        <Image
                            wrapClass={{ width: "183px", height: "224px" }}
                            size={{ w: 183, h: 224 }}
                            events={clipEvent()}
                            delete={() => setImg({})}
                            src={{ url: img ? img.path : "" }}
                        />
                    )}
                </Item>
                <Item label="姓名" required>
                    {getFieldDecorator("name", {
                        initialValue: dataSource.name ? dataSource.name : "",
                        rules: [
                            {
                                required: true,
                                message: "请输入姓名",
                            },
                        ],
                    })(<Input allowClear placeholder="请输入姓名" maxLength={10} />)}
                </Item>
                <Item label="所在单位" required>
                    {getFieldDecorator("belong_unit", {
                        initialValue: dataSource.belong_unit ? dataSource.belong_unit : "",
                        rules: [
                            {
                                required: true,
                                message: "请输入所在单位",
                            },
                        ],
                    })(<Input allowClear placeholder="请输入所在单位" maxLength={20} />)}
                </Item>
                <Item label="联系方式" required>
                    {getFieldDecorator("contact_details", {
                        initialValue: dataSource.contact_details ? dataSource.contact_details : "",
                        rules: [
                            {
                                required: true,
                                message: "请输入联系方式",
                            },
                        ],
                    })(<Input placeholder="请输入联系方式" maxLength={20} allowClear />)}
                </Item>
                <Item label="信息提供" required className="select-org">
                    {getFieldDecorator("provider_org_name", {
                        initialValue: selOrg[0] ? selOrg[0].org_name : "",
                        rules: [
                            {
                                required: true,
                                message: "请选择信息提供组织",
                            },
                        ],
                    })(
                        <Input
                            disabled
                            placeholder="这里是组织名称不可编辑，选择组织后自动展示"
                        />
                    )}
                    <Button
                        type="link"
                        onClick={() => setOrgModalVisible(true)}
                        disabled={oid !== root_oid}>
                        选择组织
                    </Button>
                </Item>
                <Item label="擅长领域">
                    {getFieldDecorator("tags", {
                        initialValue: dataSource.tags ? dataSource.tags.split(",") : null,
                        rules: [
                            {
                                validator: checkSelect,
                            },
                        ],
                    })(<Checkbox.Group options={tagsList} />)}
                </Item>
                <Item label="职务">
                    {getFieldDecorator("job", {
                        initialValue: dataSource.job ? dataSource.job : "",
                        rules: [
                            {
                                max: 10,
                                message: "请输入职务",
                            },
                        ],
                    })(<Input placeholder="请输入职务" rows={10} maxLength={10} />)}
                </Item>
                <Item label="职级">

                    {getFieldDecorator("profession_level", {
                        initialValue: dataSource.profession_level ? dataSource.profession_level : "",
                        rules: [
                            {
                                max: 10,
                                message: "请输入职级",
                            },
                        ],
                    })(<Input allowClear placeholder="请输入职级" maxLength={10} />)}

                </Item>
                <Item label="排序" className="sort-line">
                    {getFieldDecorator("sort", {
                        initialValue: dataSource.seq ? 2 : 1,
                    })(
                        <Radio.Group>
                            <Radio value={1}>默认排序</Radio>
                            <Radio value={2}>TOP100</Radio>
                        </Radio.Group>
                    )}
                </Item>

                {getFieldValue("sort") === 2 && (
                    <Item
                        label=" "
                        colon={false}
                        wrapperCol={{ span: 6 }}
                        className="seq-line"
                    >
                        {getFieldDecorator("seq", {
                            initialValue: dataSource.seq ? dataSource.seq : 1,
                        })(
                            <Select showSearch>
                                {seq_number.map((item) => {
                                    return (
                                        <Select.Option value={item} key={item}>
                                            {item}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        )}
                    </Item>
                )}

                <Item label=" " colon={false} className="annotation">
                    排序规则：默认排序按照上传时间排序，Top100按照排序值正序排序，数字范围1-100
                </Item>

                <Item label="简介" required>
                    {/* <div> */}
                    <InputTips max={500} text={getFieldValue("content")} required>
                        {getFieldDecorator("content", {
                            initialValue: content,
                            rules: [
                                {
                                    required: true,
                                    message: "请输入简介",
                                },
                            ],
                        })
                            (
                                <TextArea rows={4} maxLength={500} />
                            )
                        }
                    </InputTips>
                </Item>

                <Item label=" " colon={false} className="btn">
                    <Button type="primary" onClick={() => onHandleSubmit()}>
                        发布
                    </Button>
                    <Button type="default" onClick={() => history.goBack()}>
                        返回
                    </Button>
                    <Button type="default" onClick={() => onHandleSubmit(true)}>
                        预览
                    </Button>
                </Item>
            </Form>
            <OrganizeModal {...orgModalProps} />
            {PreviewModalMemo}
        </div>
    )

}

export default Form.create()(NewlyIncreased) 