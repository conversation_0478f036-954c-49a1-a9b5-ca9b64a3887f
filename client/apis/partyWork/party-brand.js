const { http, filterParamsValue } = require("client/tool/axios");
import { ucHost } from "../config";

// 4000300704-查询党建品牌详情
const queryPartyBrand = (data) => {
  return http.get(`${ucHost}/partyBrand/selectSelf?org_id=${data}`);
};
//  4000300705-查询所有下级党建品牌列表
const querySubPartyBrand = (data) => {
  return http.get(`${ucHost}/partyBrand/selectSub?org_id=${data}`);
};
//4000300701-新建党建品牌
const addPartyBrand = (data) => {
  return http.post(`${ucHost}/partyBrand/insert`, filterParamsValue(data));
};
// 4000300703-修改党建品牌
const updatePartyBrand = (data) => {
  return http.post(`${ucHost}/partyBrand/update`, filterParamsValue(data));
};

const recommendPartyBrand = (data) => {
  return http.get(`${ucHost}/partyBrand/recommend`, filterParamsValue(data));
};

// 4010202020801-查看推优标准 http://wiki.aidangqun.com/project/4?p=810
const searchStandar = (params) => {
  return http.get(`${ucHost}/push-excellent/list`, params);
};

export {
  querySubPartyBrand,
  queryPartyBrand,
  addPartyBrand,
  updatePartyBrand,
  recommendPartyBrand,
  searchStandar
};
