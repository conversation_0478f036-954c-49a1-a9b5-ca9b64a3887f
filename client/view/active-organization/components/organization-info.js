import React, { Component } from "react";
import {
  Form,
  Card,
  Input,
  Button,
  Cascader,
  Select,
  TreeSelect,
  message,
  Col
} from "antd";
import PropTypes from "prop-types";
import { getParam } from 'tool/util';
import {
  getArea,
  getAreaAll,
  getDataDictionary,
  getOrganizationTree,
  activeOrganization,
  locateOrgTree
} from "client/apis/active-organization";

const SelectOption = Select.Option;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 }
};

// 递归生成树
const getTreeData = data => {
    if(!data) return null;
    return data.map(item => {
        if (item.children && item.children.length > 0) {
            return {
                label: item.name,
                title: item.name,
                value: item.org_id,
                key: item.org_id,
                dataRef: item,
                isLeaf: item.child_org_num === 0,
                children: getTreeData(item.children)
            }
        }

        return {
            label: item.name,
            title: item.name,
            value: item.org_id,
            key: item.org_id,
            dataRef: item,
            isLeaf: item.child_org_num === 0
        }
    })
}

class OrganizationInfo extends Component {
  constructor(props) {
    super(props);

    this.state = {
      orgTypeData: [], // 组织类型
      industryTypeData: [], // 行业类型
      areaData: [], // 区域数据
      orgTreeData: [], // 组织树
      ownerTreeData: [],

      defaultAreaData: [], // 默认区域数据
      defaultOrgTypeData: [], // 默认组织类型
      defaultOrgTreeData: [] // 默认组织树数据
    };
  }

  componentDidMount() {
    const dictionary = [
      {
        code: "1019",
        name: "行业类别",
        statePropName: "industryTypeData"
      }
    ];

    // 获取字典
    Promise.all(dictionary.map(item => getDataDictionary({ code: item.code })))
      .then(res => {
        dictionary.forEach((item, index) => {
          const { data } = res[index];
          this.setState({ [item.statePropName]: data.data });
        });
      })
      .catch(err => {
        message.error("获取数据字典出错");
      });

    // 初始化组织区域
    if (this.props.state.organizationInfo.org_area) {
      getAreaAll({ adcode: this.props.state.organizationInfo.org_area })
        .then(res => {
          const { data } = res;

          if (data.code === 0) {
            const allData = data.data;

            let firstId = "";
            let secondId = "";
            let threeId = "";

            let lastLevelParentId = allData.parentid;
            let lastLevelId = allData.adcode;

            let levelThree = [];
            let levelTwo = [];
            let levelOne = [];

            if (allData.level_three) {
              levelThree = allData.level_three.map(item => ({
                label: item.area_name,
                value: item.adcode,
                isLeaf: true
              }));

              threeId = lastLevelId;
              secondId = lastLevelParentId;
            } else {
              secondId = lastLevelId;
            }

            if (allData.level_two) {
              levelTwo = allData.level_two.map(item => {
                let formatedData = {
                  label: item.area_name,
                  value: item.adcode,
                  isLeaf: false
                };

                if (item.adcode == secondId) {
                  firstId = item.parentid;
                  if (allData.level_three) {
                    // 存在第三级，才赋值
                    formatedData.children = levelThree;
                  }
                }

                return formatedData;
              });
            }

            if (allData.level_one) {
              levelOne = allData.level_one.map(item => {
                let formatedData = {
                  label: item.area_name,
                  value: item.adcode,
                  isLeaf: false
                };

                if (item.adcode == firstId) {
                  formatedData.children = levelTwo;
                }

                return formatedData;
              });
            }

            this.setState({
              areaData: levelOne,
              defaultAreaData: [firstId, secondId, threeId]
            });
          } else {
            return Promise.reject(data);
          }
        })
        .catch(err => {
          message.error(err.message);
        });
    } else {
      getArea()
        .then(res => {
          const { data } = res;
          if (data.code === 0 && data.data) {
            const area = data.data.map(item => {
              return {
                label: item.area_name,
                value: item.adcode,
                isLeaf: false
              };
            });

            this.setState({ areaData: [...area] });
          }
        })
        .catch(err => {
          message.error(err.message);
        });
    }

    // 初始化组织类型
    const { organizationInfo } = this.props.state;
    if (organizationInfo.org_type_child) {
      Promise.all([
        getDataDictionary({ code: "1028" }),
        getDataDictionary({ code: organizationInfo.org_type })
      ])
        .then(res => {
          const firstData = res[0].data.data.map(item => {
            if (item.op_key == organizationInfo.org_type) {
              // 第二级数据
              const children = res[1].data.data.map(item => {
                return {
                  label: item.op_value,
                  value: item.op_key,
                  isLeaf: true
                };
              });

              return {
                children,
                label: item.op_value,
                value: item.op_key,
                isLeaf: false
              };
            }
            return {
              label: item.op_value,
              value: item.op_key,
              isLeaf: false
            };
          });

          this.setState({
            orgTypeData: firstData,
            defaultOrgTypeData: [
              organizationInfo.org_type,
              organizationInfo.org_type_child
            ]
          });
        })
        .catch(err => {
          message.error(err.message);
        });
    }

    // 获取组织树
    const p = {};
    const ownerTree = this.props.state.organizationInfo.owner_tree || 2;
    if(ownerTree == 2){
        p['org_type'] = this.props.state.organizationInfo.org_type;
    }

    locateOrgTree(Object.assign({
        root_org_id: this.props.state.organizationInfo.parent_id,
        org_id: this.props.state.organizationInfo.org_id,
        tree_type: ownerTree,
        load_root: 1
    }, p)).then(res => {
      const {data} = res;
      if (data.code === 0) {
          this.setState({orgTreeData: data.data});
      } else {
          return Promise.reject(data);
      }
    }).catch(err => {
      message.error(err.message);
    });

    // 获取所属组织
    if(this.props.state.organizationInfo.owner_tree == 2 && this.props.state.organizationInfo.owner_id) {
        // if (this.props.state.organizationInfo.owner_id != this.props.state.organizationInfo.parent_id) {
        //     locateOrgTree({
        //         root_org_id: this.props.state.organizationInfo.parent_id,
        //         org_id: this.props.state.organizationInfo.owner_id,
        //         tree_type: 1,
        //         load_root: 1
        //     }).then(res => {
        //         const {data} = res;
        //         if (data.code === 0) {
        //             this.setState({ownerTreeData: [data.data]});
        //         } else {
        //             return Promise.reject(data);
        //         }
        //     }).catch(err => {
        //         message.error(err.message);
        //     });
        // } else {
            getOrganizationTree({
                org_id: this.props.state.organizationInfo.owner_id,
                show_code: 0,
                tree_type: 1,
                load_root: 1
            }).then(res => {
                const {data} = res;
                if (data.code === 0) {
                    this.setState({ownerTreeData: data.data});
                } else {
                    return Promise.reject(data);
                }
            }).catch(err => {
                message.error(err.message);
            });
        // }
    }
  }

  // 提交
  handleSubmit(e) {
    e.preventDefault();
    const props = this.props;

    props.form.validateFieldsAndScroll((errs, values) => {
      if (!errs) {

        const data = {
          ...values,
          ...props.state.passwordInfo,
          ...props.state.registerUserInfo
        };

        if (props.state.userId) {
          data.user_id = props.state.userId;
        }

        // parent_id
        if (Array.isArray(values.parent_id)) {
          data.parent_id = parseInt(values.parent_id[values.parent_id.length - 1]);
        }


        // 组织类型
        data.org_type = values.orgTypeCascader[0] || "";
        data.org_type_child = values.orgTypeCascader[1] || "";
        delete data.orgTypeCascader;
        data.org_area = data.orgAreaCascader[data.orgAreaCascader.length - 1]; // 组织地址
        data.org_id = props.state.organizationInfo.org_id; // 组织id

        delete data.orgAreaCascader;
        activeOrganization(data)
          .then(res => {
            const { data } = res;
            if (data.code === 0) {
              message.success("激活成功");
              const { region_id } = getParam();
              setTimeout(() => {
                //props中无history
                window.location.href = `/login?region_id=${region_id}`
              }, 2000);
            } else {
              return Promise.reject(data);
            }
          })
          .catch(err => {
            message.error(err.message);
          });
      }
    });
  }

  // render select options
  renderOptions(data) {
    if (!data) return;

    let options = [
      <SelectOption key="" value="">
        请选择
      </SelectOption>
    ];
    data.forEach(item => {
      options.push(
        <SelectOption key={item.op_key} value={item.op_key}>
          {item.op_value}
        </SelectOption>
      );
    });

    return options;
  }

  // 加载区域树
  loadOrgAreaData(selectedOptions) {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    getArea({ pid: targetOption.value })
      .then(res => {
        targetOption.loading = false;

        const { data } = res;
        if (data.code === 0 && data.data) {
          const area = data.data.map(item => {
            return {
              label: item.area_name,
              value: item.adcode,
              isLeaf: selectedOptions.length > 1
            };
          });
          targetOption.children = area;

          this.setState({ areaData: [...this.state.areaData] });
        }
      })
      .catch(err => {
        targetOption.loading = false;
        message.error(err.message);
      });
  }

  // 加载组织类型
  loadOrgTypeData(selectedOptions) {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    getDataDictionary({ code: targetOption.value })
      .then(res => {
        targetOption.loading = false;
        const { data } = res;
        if (data.code === 0 && data.data) {
          const childData = data.data.map(item => {
            return {
              label: item.op_value,
              value: item.op_key,
              isLeaf: selectedOptions.length > 0
            };
          });
          targetOption.children = childData;

          this.setState({ orgTypeData: [...this.state.orgTypeData] });
        }
      })
      .catch(err => {
        targetOption.loading = false;
        message.error(err.message);
      });
  }

  onLoadOrganization(treeNode, treeType, f){
      return new Promise(async (resolve) => {
          if ((treeNode.props.children && treeNode.props.children.length > 0) || !treeNode.props.dataRef.child_org_num) {
              resolve();
              return;
          }

          // 获取组织树
          const p = {};
          if(treeType == 2){
              p['org_type'] = this.props.state.organizationInfo.org_type
          }

          const result = (await getOrganizationTree(Object.assign({
              org_id: treeNode.props.dataRef.org_id,
              show_code: 0,
              tree_type: treeType,
              load_root: 0
          }, p))).data;

          if(result.code !== 0){
              return message.error(result.message);
          }

          if(!result.data.length){
              treeNode.props.dataRef.child_org_num = 0;
          } else {
              treeNode.props.dataRef.children = result.data.map(item => {
                  return {...item, isLeaf: item.child_org_num === 0}
              });
          }

          if(f){
              this.setState({
                  ownerTreeData: [...this.state.ownerTreeData]
              });
          } else {
              this.setState({
                  orgTreeData: [...this.state.orgTreeData]
              });
          }
          resolve();
      });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { organizationInfo } = this.props.state;
    return (
      <Card className="organization-info">
        <Form onSubmit={this.handleSubmit.bind(this)}>
          <FormItem label="组织类型：" {...formItemLayout}>
            {getFieldDecorator("orgTypeCascader", {
              initialValue: this.state.defaultOrgTypeData,
              rules: [{ required: true, message: "请选择组织类型" }]
            })(
              <Cascader
                placeholder="请选择组织类型"
                options={this.state.orgTypeData}
                loadData={this.loadOrgTypeData.bind(this)}
                disabled={true}
              />
            )}
          </FormItem>

          <FormItem label="行业类别：" {...formItemLayout}>
            {getFieldDecorator("industry_type", {
              initialValue: organizationInfo.industry_type,
              rules: [{ required: true, message: "请选择行业类别" }]
            })(
              <Select placeholder="请选择" disabled={true}>
                {this.renderOptions(this.state.industryTypeData)}
              </Select>
            )}
          </FormItem>

          <FormItem label="上级组织：" {...formItemLayout}>
            {getFieldDecorator("parent_id", {
              initialValue: [organizationInfo.parent_id + ""],
              rules: [{ required: true, message: "请选择上级组织" }]
            })(
              <TreeSelect
                showSearch={true}
                placeholder="请选择上级组织"
                searchPlaceholder="请输入组织名称"
                treeDefaultExpandedKeys={[organizationInfo.parent_id + ""]}
                treeData={getTreeData(this.state.orgTreeData)}
                // treeData={this.state.orgTreeData}
                treeNodeFilterProp="label"
                dropdownStyle={{ maxHeight: 250, overflow: 'auto' }}
                loadData={(treeNode) => {
                   return this.onLoadOrganization(treeNode, organizationInfo.owner_tree);
                }}
                disabled={true}
                // disabled={!organizationInfo.owner_tree || organizationInfo.owner_tree == 2}
                // filterTreeNode={this.filterTreeNode.bind(this)}
              />
            )}
          </FormItem>

          {organizationInfo.owner_tree == 2 && (
          <FormItem>
              <Col span={6} style={{marginTop: -4}}>
                  <table style={{width:"100%",height:"40px",lineHeight:"normal"}}>
                    <tr><td style={{verticalAlign:"middle",textAlign:"right",paddingRight:13}}>
                    所属{organizationInfo.parent_tree_name || '组织'}
                    </td></tr>
                  </table>
              </Col>
              <Col span={18}>
                  {getFieldDecorator("owner_id", {
                      initialValue: organizationInfo.owner_id || null
                  })(
                  <TreeSelect
                      allowClear={true}
                      showSearch={true}
                      placeholder="请选择所属组织"
                      searchPlaceholder="请输入组织名称"
                      treeDefaultExpandedKeys={organizationInfo.owner_id ? [organizationInfo.owner_id + ""] : null}
                      treeData={getTreeData(this.state.ownerTreeData)}
                      // treeData={this.state.orgTreeData}
                      treeNodeFilterProp="label"
                      dropdownStyle={{ maxHeight: 250, overflow: 'auto' }}
                      loadData={(treeNode) => {
                        return this.onLoadOrganization(treeNode, 1, true);
                      }}
                      disabled={true}
                      // filterTreeNode={this.filterTreeNode.bind(this)}
                      />
                  )}
              </Col>
          </FormItem>
          )}

          <FormItem label="组织名称：" {...formItemLayout}>
            {getFieldDecorator("org_name", {
              initialValue: organizationInfo.name,
              rules: [{ required: true, message: "请输入组织名称" }]
            })(<Input placeholder="请输入组织名称" disabled={true}/>)}
          </FormItem>

          <FormItem label="组织代码：" {...formItemLayout}>
            {getFieldDecorator("org_code", {
              initialValue: organizationInfo.org_code
            })(<Input placeholder="请输入组织代码" disabled={true}/>)}
          </FormItem>

          <FormItem label="组织联系人" {...formItemLayout}>
            {getFieldDecorator("org_contacts", {
              initialValue: organizationInfo.org_contacts,
              rules: [{ required: true, message: "请输入组织联系人" }]
            })(<Input placeholder="请输入组织联系人" disabled={true}/>)}
          </FormItem>

          <FormItem label="法人(负责人)：" {...formItemLayout}>
            {getFieldDecorator("org_leader", {
              initialValue: organizationInfo.org_leader
            })(<Input placeholder="请输入法人(负责人)" disabled={true}/>)}
          </FormItem>

          <FormItem label="负责人电话：" {...formItemLayout}>
            {getFieldDecorator("org_leader_phone", {
              initialValue: organizationInfo.org_leader_phone
            })(<Input placeholder="请输入负责人电话" disabled={true}/>)}
          </FormItem>

          <FormItem label="所属行政区划：" {...formItemLayout}>
            {getFieldDecorator("orgAreaCascader", {
              initialValue: this.state.defaultAreaData,
              rules: [{ required: true, message: "请选择所属行政区划" }]
            })(
              <Cascader
                placeholder="请选择所属行政区划"
                options={this.state.areaData}
                loadData={this.loadOrgAreaData.bind(this)}
                disabled={true}
              />
            )}
          </FormItem>

          <FormItem label="组织联系电话：" {...formItemLayout}>
            {getFieldDecorator("org_phone", {
              initialValue: organizationInfo.org_phone,
              rules: [{ required: true, message: "请输入组织联系电话" }]
            })(<Input placeholder="请输入组织联系电话" disabled={true}/>)}
          </FormItem>

          <FormItem label="邮政编码：" {...formItemLayout}>
            {getFieldDecorator("postcode", {
              initialValue: organizationInfo.postcode,
              rules: [{ required: true, message: "请输入邮政编码" }]
            })(<Input placeholder="请输入邮政编码" disabled={true}/>)}
          </FormItem>

          <FormItem label="组织通讯地址：" {...formItemLayout}>
            {getFieldDecorator("org_address", {
              initialValue: organizationInfo.org_address,
              rules: [{ required: true, message: "请输入详细地址" }]
            })(<Input placeholder="请输入详细地址" disabled={true}/>)}
          </FormItem>

          <FormItem style={{ marginTop: 10 }}>
            <Button
              style={{ width: 140, marginLeft: 194 }}
              onClick={() => this.props.changeState({ step: 2 })}
            >
              上一步
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              style={{ width: 140, marginLeft: 20 }}
            >
              提交
            </Button>
          </FormItem>
        </Form>
      </Card>
    );
  }
}

const OrganizationInfoWrapper = Form.create()(OrganizationInfo);

OrganizationInfoWrapper.propTypes = {
  changeState: PropTypes.func, // 改变父级state
  state: PropTypes.object
};

export default OrganizationInfoWrapper;
