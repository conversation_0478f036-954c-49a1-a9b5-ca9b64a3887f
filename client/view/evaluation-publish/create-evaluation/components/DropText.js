export default function DropText({
  value,
  onChange,
  onSelect,
  option,
  disabled,
  disableList = [],
}) {
  const menu = (
    <Ant.Menu>
      {option.map((item) => (
        <Ant.Menu.Item
          disabled={disabled || disableList.includes(item.key)}
          key={item.key}
          onClick={() => {
            if (disableList.includes(item.key)) return;
            onChange && onChange(item.key);

            onSelect && onSelect(item.key);
          }}
        >
          {item.label}
        </Ant.Menu.Item>
      ))}
    </Ant.Menu>
  );

  const getValue = (value) => {
    const item = option.find((item) => Number(item.key) === Number(value));
    return item ? item.label : "";
  };

  return (
    <Ant.Dropdown overlay={menu} trigger={"click"} disabled={disabled}>
      <Ant.Row type="flex" align="middle" justify="space-between">
        <span>{getValue(value)}</span>
        {!disabled && <Ant.Icon type="down" />}
      </Ant.Row>
    </Ant.Dropdown>
  );
}
