import React from "react";
import './index.less';
import PropTypes from "prop-types";
import { Button, Table, message as Message, Form, Select, Modal } from "antd";
import { queryMeetingType, getMeetingOrgListAll, getPlanListAll, getCategoryListAll, getTypeListAll } from "apis/meeting-manage";

const FormItem = Form.Item;
const Option = Select.Option;
const OptGroup = Select.OptGroup;

class MeetingType extends React.Component {
  constructor(props) {
    super(props);
    // 活动类型选择
    this.state = {
      source: "",
      plan: "",
      category: "",
      type: "",
      selectedRowKeys: [],
      selectRowArray: [], // 还可点击的行

      task: '',
      // 用于比较task值是否发生了变化
      taskCurrent: '',
      taskList: [], // 任务来源(下拉)
      plan: '',
      planList: [], // 考核计划(下拉)
      category: '',
      categoryList: [], // 所属类别(下拉)
      type: '',
      typeList: [], // 会议类型(下拉)
      meetingTypeSourceData: [],
      meetingTypeGroups: [],
      meetingSelectedRow: [],
      meetingSelectedRowKeys: [],
      // 备份原选中数组
      backupSelectedRow: [],
      backupSelectedRowKeys: [],

      clearStatus: true,
      // 暂存可以选择的行，用于会议组合
      selectRowArray: []
    };
  }

  componentWillMount () {
    this.init();
  }
  // 22
  async  getNewMeetingType(timePoint){
    await  this.fetchMeetingType({ org_id: this.state.task, start_time: timePoint });
  }
  componentWillReceiveProps (props) {
    const { dataSource, timePoint } = props;
    if(this.props.timePoint !== timePoint && timePoint){this.getNewMeetingType(timePoint);}
    // && dataSource.length !== 0
    if (dataSource && Array.isArray(dataSource) ) {
      // 键值为：meeting_task_id
      const rowKeys = dataSource.map((row) => {
        return row.meeting_task_id;
      });
     
      this.setState({
        meetingSelectedRow: dataSource,
        meetingSelectedRowKeys: rowKeys,
        backupSelectedRow: dataSource,
        backupSelectedRowKeys: rowKeys,
        selectedRowKeys: rowKeys,
        selectRowArray: dataSource.map((item) => item.type_id),
      });
      
    }
  }

  init () {
    // 获取任务来源
    this.fetchMeetingOrgListAll();
    this.fetchPlanListAll();
    this.fetchCategoryListAll();
    this.fetchTypeListAll();
  }

  searchHandler (values = {}) {
    const { taskCurrent, task, plan, category, type } = this.state;
    // 任务来源变更
    if (taskCurrent !== task) {
      this.setState({
        taskCurrent: task,
        meetingSelectedRow: [],
        meetingSelectedRowKeys: [],
      });
    }
    const params = {
      plan_id: plan,
      org_id: task,
      category_id: category,
      type_id: type,
      ...values
    };
    if(this.props.timePoint){
      Object.assign(params,{start_time:this.props.timePoint})
    }
    this.fetchMeetingType(params);
  }

  async fetchTypeListAll (params = {}) {
    const response = await getTypeListAll({ ...params, tag: 1 });
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    this.setState({
      typeList: data
    });
  }

  async fetchCategoryListAll (params = {}) {
    const response = await getCategoryListAll({ ...params, tag: 1 });
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    this.setState({
      categoryList: data
    });
  }

  async fetchPlanListAll (params = {}) {
    const response = await getPlanListAll({ ...params, tag: 1 });
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    this.setState({
      planList: data
    });
  }

  async fetchMeetingType (params = {}) {
    // i:timePoint,postName,postParams,datasouce是回填的数据
    // p:postParams,update datasource,如果selectKeys !=[],需要遍历datasouce 根据selectKeys 里面的id重新选择selectRows,call onchange()
    //  :为[],显示数据。
    // o:types,selectKeys,
    const response =await queryMeetingType(params) 
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    const { groups = [], tasks = [] } = data;
    this.setState({
      meetingTypeSourceData: tasks,
      meetingTypeGroups: groups
    });
  }
  
  async fetchMeetingOrgListAll (params = {}) {

    const response = await getMeetingOrgListAll();
    const { data: body } = response;
    const { code, message, data } = body;


    if (code !== 0) {
      Message.error(message);
      return;
    }
    if (data && Array.isArray(data)) {
      // if (data.length === 0) {
      //   Message.error("暂无任务来源");
      //   return;
      // }
      const task = data[0] ? data[0].org_id : "";
      this.setState({
        taskList: data,
        task,
        taskCurrent: task
      });
      this.fetchMeetingType({
        org_id: task
      });
    }
  }

  render () {
    
    const {
      task,
      plan,
      category,
      type,
      meetingTypeSourceData,
      meetingSelectedRowKeys,
      selectedRowKeys
    } = this.state;
    // 修改活动录入文字
    const { fillDirectly = false }  = this.props
    const columns = [
      {
        title: "序号",
        align: "center",
        width: 65,
        render: (t, r, i) => {
          return i + 1;
        }
      },
      {
        // title: "会议类型",
        title: "活动类型",
        align: "center",
        dataIndex: "type"
      },
      // {
      //   // title: "所属计划",
      //   title: "所属组织生活",
      //   align: "center",
      //   dataIndex: "name"
      // },
      {
        title: "所属类别",
        align: "center",
        dataIndex: "category",
        width: 120
      },
      {
        title: "开始时间",
        align: "center",
        dataIndex: "start_time",
        width: 120,
      },
      {
        title: "截止时间",
        align: "center",
        dataIndex: "end_time",
        width: 120,
      },
      {
        title: fillDirectly ? "已开展" : "待完成任务",
        align: "center",
        dataIndex: "status",
        width: 120,
        render: (text, row) => {
          // 活动录入时修改文字其他不改
          if(fillDirectly) {
            return row.meeting_num > 0 ? `${row.meeting_num}次`: (<span><span style={{color: 'red'}}>{row.meeting_num}</span>次</span>)
          }else {
            if (row.status === 1) {
              return "是";
            } else if (row.status === 2) {
              return "否";
            }
            return "是";
          }
        }
      }
    ];
    const onSelectHandler = (record, selected, selectedRows, e) => {
      const {
        meetingSelectedRow,
        meetingTypeGroups
      } = this.state;
      const rows = JSON.parse(JSON.stringify(meetingSelectedRow));
      if (rows && Array.isArray(rows)) {
        const index = rows.findIndex((row) => {
          return row.meeting_task_id === record.meeting_task_id;
        });
        if (selected) {
          // 选中某个项目
          if (index === -1) {
            rows.push(record);
          }
        }
        else {
          // 反选某个项目
          if (index !== -1) {
            rows.splice(index, 1);
          }
        }
        let meetingSelectedRowKeys = [];
        rows.forEach((row) => {
          meetingSelectedRowKeys.push(row.meeting_task_id);
        });

        function fetchSelectRowArray (meetingTypeGroups = [], meetingSelectedRow = []) {
          // 记录能够选择的组合
          let result = [], match = false, typeIds = [];
          if (meetingTypeGroups && Array.isArray(meetingTypeGroups)) {
            meetingTypeGroups.forEach((group) => {
              if (group.type_ids && Array.isArray(group.type_ids)) {
                if (rows && Array.isArray(rows)) {
                  match = rows.every((row) => {
                    if (group.type_ids && Array.isArray(group.type_ids)) {
                      return group.type_ids.indexOf(row.type_id) !== -1;
                    }
                    else {
                      return false;
                    }
                  })
                }
              }
              if (match) {
                result = result.concat(group.type_ids);
                match = false;
              }
            })
          }

          //  去除重复项目
          result = Array.from(new Set(result));
          return result;
        }
        const selectRowArray = fetchSelectRowArray(meetingTypeGroups, rows);
        this.setState({
          meetingSelectedRow: rows,
          meetingSelectedRowKeys,
          selectedRowKeys: meetingSelectedRowKeys,
          // 计算取得的还可以点击的行
          selectRowArray
        });
      }
    }
    const isRowSelectionDisabled = (record) => {
      const { meetingSelectedRow, selectRowArray, meetingSelectedRowKeys } = this.state;
      let disabled = false;
      // 当有选中项目时，判断所有选项是否在可选项目中，如果不在，则禁用选择
      if (meetingSelectedRowKeys && Array.isArray(meetingSelectedRowKeys) && meetingSelectedRowKeys.length !== 0) {
        if (selectRowArray && Array.isArray(selectRowArray)) {
          if (selectRowArray.indexOf(record.type_id) === -1) {
            disabled = true;
          } else {
            // 当前参与组合的会议类型，所属计划meeting_plan_id，和所属类别category_id必须一致
            if (meetingSelectedRow[0]) {
              if (
                (record.category_id !== meetingSelectedRow[0].category_id)
                // || (record.meeting_plan_id !== meetingSelectedRow[0].meeting_plan_id)
              ) {
                disabled = true;
              }
            }
          }
        }
      }
      return disabled;
    }

    const renderSourceOption = () => {
      const { taskList } = this.state;
      if (!taskList) {
        return null;
      }
      return taskList.map(v => {
        return (
          <Option value={v.org_id} title={v.org_name} key={v}>
            {v.org_name}
          </Option>
        );
      });
    };
    const renderPlanOption = () => {
      const { planList } = this.state;
      if (!planList) {
        return null;
      }
      return planList.map(v => {
        return (
          <Option value={v.meeting_plan_id} title={v.name} key={v}>
            {v.name}
          </Option>
        );
      });
    };
    const renderCategoryOption = () => {
      const { categoryList } = this.state;
      if (!categoryList) {
        return null;
      }
      return categoryList.map(v => {
        return (
          <Option value={v.category_id} title={v.category} key={v}>
            {v.category}
          </Option>
        );
      });
    };
    const renderTypeOption = () => {
      const { typeList } = this.state;
      if (!typeList) {
        return null;
      }
      return typeList.map(v => {
        return (
          <Option value={v.type_id} title={v.type} key={v}>
            {v.type}
          </Option>
        );
      });
    };
    const selectOption = (name) => {
      return e => {
        this.setState({
          [name]: e
        });
      }
    }
    const { visible, updateState, form, onChange } = this.props;
    const { validateFields, getFieldDecorator } = form;
    const ModalProps = {
      title: "选择活动类型",
      width: 800,
      visible,
      onCancel: () => {
        updateState({
          meetingTypeVisible: false
        });
      },
      footer: null
    }
    const rowSelection = {
      columnTitle: "选择",
      selectedRowKeys: selectedRowKeys,
      // rowSelection的onChange,存在selectedRowKeys, selectedRows不一致的BUG
      // onChange: this.onSelectChange,
      onSelect: onSelectHandler,
      getCheckboxProps: record => {
        const result = isRowSelectionDisabled(record);
        return {
          disabled: result
        };
      }
    };
    return (
      <Modal {...ModalProps}>
        <div className="meeting-type-container" >
          <Form
            layout="inline"
            onSubmit={(e) => {
              e.preventDefault();
              validateFields((error, values) => {
                if (!error) {
                  this.searchHandler(values);
                }
              });
            }}
          >
            <FormItem label={"来源"}>
              {
                getFieldDecorator("org_id", {
                  initialValue: task
                })(
                  <Select
                    style={{ width: 130 }}
                    onChange={selectOption("task")}
                  // value={task}
                  >
                    {renderSourceOption()}
                  </Select>
                )
              }
            </FormItem>
            {/* <FormItem
              // label="所属计划"
              label="所属组织生活"
            >
              {
                getFieldDecorator("plan_id", {
                  initialValue: plan
                })(
                  <Select
                    style={{ width: 160 }}
                    onChange={selectOption("plan")}
                  // value={plan}
                  >
                    <Option value="">全部</Option>
                    {renderPlanOption()}
                  </Select>
                )
              }
            </FormItem> */}
            <FormItem label="所属类别">
              {
                getFieldDecorator("category_id", {
                  initialValue: category
                })(
                  <Select
                    style={{ width: 130 }}
                    onChange={selectOption("category")}
                  // value={category}
                  >
                    <Option value="">全部</Option>
                    {renderCategoryOption()}
                  </Select>
                )
              }

            </FormItem>
            <FormItem label="活动类型">
              {
                getFieldDecorator("type_id", {
                  initialValue: type
                })(
                  <Select
                    style={{ width: 130 }}
                    onChange={selectOption("type")}
                  // value={type}
                  >
                    <Option value="">全部</Option>
                    {renderTypeOption()}
                  </Select>
                )
              }
            </FormItem>
            <FormItem>
              <Button style={{ height: 36, width: 90 }} type={"primary"} htmlType="submit">
                查询
              </Button>
            </FormItem>
          </Form>
          <div className="table-wrapper">
            <Table
              rowKey={"meeting_task_id"}
              rowSelection={rowSelection}
              rowClassName={(record, index) => {
                const isRowDisable = isRowSelectionDisabled(record)
                return isRowDisable ? "disable-row-selection" : "";
              }}
              bordered
              pagination={false}
              dataSource={meetingTypeSourceData}
              columns={columns}
              scroll={{ y: 360 }}
            />
          </div>
          <div className="buttons-wrapper">
            <Button className={"buttons"} type="primary" onClick={() => {
              const { meetingSelectedRow = [], meetingSelectedRowKeys = [] } = this.state;
              const rows = JSON.parse(JSON.stringify(meetingSelectedRow)),
                rowKeys = JSON.parse(JSON.stringify(meetingSelectedRowKeys));
              this.setState({
                meetingSelectedRow: rows,
                meetingSelectedRowKeys: rowKeys,
                selectedRowKeys: rowKeys,
              }, () => {
                onChange(rows, rowKeys);
                ModalProps.onCancel();
              });
            }}>确定</Button>
            <Button className={"buttons"} onClick={() => {
              const { backupSelectedRow, backupSelectedRowKeys } = this.state;
              this.setState({
                meetingSelectedRow: backupSelectedRow,
                meetingSelectedRowKeys: backupSelectedRowKeys,
                selectedRowKeys: backupSelectedRowKeys,
              }, () => {
                ModalProps.onCancel();
              });
            }}>取消</Button>
          </div>
        </div>
      </Modal >
    )
  }
}

MeetingType.propTypes = {
  visible: PropTypes.bool,
  onCancel: PropTypes.func,
  onChange: PropTypes.func
}

MeetingType.defaultProps = {
  visible: false,
  onCancel: () => { },
  onChange: () => { }
}

export default Form.create()(MeetingType);