import React, { Component } from "react";
import { Table, message as Message, Checkbox, Radio } from "antd";

import {
  getUserListByOrgId,
  getOrgGroupMemberFindByUser,
  getFindAllUser
} from "apis/organisition";
import { findByUser,getUserByCondition } from "apis/organize";

import propTypes from "prop-types";

class PersonTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedRowKeys: [],
      selectedRows: [],
      allChecked: false,
      tableData: [],
      pageSize: 10,
      total: 0,
      current: 1,
      loading: false
    }
    this.fetchUserList = this.fetchUserList.bind(this);
    this.fetchUserListByKeyword = this.fetchUserListByKeyword.bind(this);
    this.exportData = this.exportData.bind(this);
    this.isAll = this.isAll.bind(this);
  }

  componentWillMount() {
    const { current, pageSize } = this.state;
    const { dataSource, isPartyGroup,org_id,selected_org_id, isEmployee } = this.props;
    this.init(dataSource);
    /* if (isPartyGroup) {
      const org_id = typeof window !== "undefined" && window.sessionStorage._oid;
      const org_type = typeof window !== "undefined" && window.sessionStorage._org_type;
      this.getOrgGroupMemberFindByUser({
        org_id,
        org_type,
        page: current,
        rows: pageSize
      });
    }else { */
      this.fetchUserList({ org_id: selected_org_id || org_id, page: current, pagesize: pageSize, is_employee: isEmployee });
    // }
  }

  componentWillReceiveProps(props) {
    // console.log(props);
    // const { dataSource } = props;
    // this.init(dataSource);
    // const { current, pageSize } = this.state;
    // this.fetchUserList({ page: current, pagesize: pageSize });

  }

  init(data = []) {
    const copyData = this.simpleCloneHandler(data);
    const {selectData} = this.props;
    if (copyData && Array.isArray(copyData) && copyData.length !== 0) {
      let rowkeys = [], rows = [];
      copyData.forEach((item) => {
        // console.log(item);
        if (!item.org_name) {
          item.org_name = "";
        }
        if (!item.org_id) {
          item.org_id = "";
        }
        if (item.phone_secret) {
          item.phone = item.phone_secret;
        }
        if (item.cert_number_secret) {
          item.cert_number = item.cert_number_secret;
        }
        item.key = `${item.user_id}-${item.org_id}`;
        rowkeys.push(item.key);
        rows.push(item);
      });
      // console.log("导入", rowkeys, rows);
      this.setState({
        allChecked: this.isAllChecked(rowkeys)
      },()=>{
        selectData(rowkeys, rows)
      });
      /* this.setState({
        selectedRowKeys: rowkeys,
        selectedRows: rows
      }, () => {
      }) */
    } else {
      selectData([], [])
      this.setState({
        // selectedRowKeys: [],
        // selectedRows: [],
        allChecked: false
      })
    }
  }

  // 向表格数据添加user_id-org_id作为key值
  addUserIdAsKeyOfTableDataSource(data = []) {
    // console.log(data);
    if (data && Array.isArray(data)) {
      data.map((item, index) => {
        // 新接口上后，用下面的值作为key，以确保key的唯一性
        item.key = `${item.user_id}-${item.org_id || index}`
        return item;
      })
    }
    return data;
  }

  exportData() {
    const { selectedRows } = this.state;
    // console.log(selectedRows)
    return JSON.parse(JSON.stringify(selectedRows));
  }

  async getFindAllUser(queryparmas = {}) {
    this.setState({
      loading: true
    });
    const response = await getFindAllUser(queryparmas);
    const { data: body } = response;
    const { code, data, message, total, pageNum } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    this.props.dataTotal(total)
    this.setState({
      tableData: this.addUserIdAsKeyOfTableDataSource(data),
      total,
      current: pageNum,
      loading: false
    });
  }

  // 新增，单选数据接口
  async getOrgGroupMemberFindByUser(queryparmas = {}) {
    this.setState({
      loading: true
    });
    const response = await getOrgGroupMemberFindByUser(queryparmas);
    const { data: body } = response;
    const { code, data, message, total, pageNum } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    data.map((item) => {
      if (!item.org_name) {
        item.org_name = "";
      }
      if (!item.org_id) {
        item.org_id = "";
      }
    });
    this.props.dataTotal(total)
    this.setState({
      tableData: this.addUserIdAsKeyOfTableDataSource(data),
      total,
      current: pageNum,
      loading: false
    }, () => {
      this.setState({
        allChecked: this.isAllChecked()
      });
    });
  }

  async fetchUserListByKeyword(queryParams = {}) {
    this.setState({
      loading: true
    });
    // 子树查询
    // const response = await findByUser(queryParams);
    const response = await getUserByCondition(queryParams);
    const { data: body } = response;
    const { code, data, message, total, pageNum } = body;
    if (code !== 0) {
      Message.error(message);
      this.setState({
        loading: false
      });
      return;
    }
    data.map((item) => {
      if (!item.org_name) {
        item.org_name = "";
      }
      if (!item.org_id) {
        item.org_id = "";
      }
    });
    this.props.dataTotal(total)
    this.setState({
      tableData: this.addUserIdAsKeyOfTableDataSource(data),
      total,
      current: pageNum,
      loading: false
    }, () => {
      this.setState({
        allChecked: this.isAllChecked()
      });
    });
  }

  async fetchUserList(queryParams) {
    this.setState({
      loading: true
    });
    // console.log(queryParams);
    const { org_id } = this.props;
    let orgId = -1;
    if (queryParams.org_id) {
      orgId = queryParams.org_id;
    } else if (org_id) {
      orgId = org_id;
    } else {
      orgId = typeof window !== "undefined" && window.sessionStorage.getItem("_oid") || 0;
      orgId = Number(orgId);
    }
    queryParams.org_id = orgId;
    this.props.queryCriteria(queryParams)
    // const response = await getUserListByOrgId(queryParams);
    const response = await getUserByCondition(queryParams);
    const { data: body } = response;
    const { code, data, message, total, pageNum } = body;
    if (code !== 0) {
      Message.error(message);
      return;
    }
    data.map((item) => {
      if (!item.org_name) {
        item.org_name = "";
      }
      if (!item.org_id) {
        item.org_id = "";
      }
    });
    this.props.dataTotal(total)
    this.setState({
      tableData: this.addUserIdAsKeyOfTableDataSource(data),
      total,
      current: pageNum,
      loading: false
    }, () => {
      this.setState({
        allChecked: this.isAllChecked()
      });
    });
  }

  simpleCloneHandler(input) {
    return JSON.parse(JSON.stringify(input));
  }

  // 检查当前列表是否被全选,checkedArr传入当前勾选的组织数组
  isAllChecked(checkedArr, allArr) {
    let result = true;
    if (!allArr) {
      const { tableData } = this.state;
      allArr = tableData;
    }
    if (!checkedArr) {
      const { selectedRowKeys } = this.props;
      checkedArr = selectedRowKeys;
    }
    if (Array.isArray(checkedArr) && Array.isArray(allArr) &&
      allArr.length !== 0 &&
      checkedArr.length >= allArr.length) {
      for (let i = 0; i < allArr.length; i++) {
        if (checkedArr.indexOf(allArr[i].key) === -1) {
          result = false;
          break;
        }
      }
    } else {
      result = false;
    }
    return result;
  }
  isAll(checkedArr){
    this.setState({
      allChecked: this.isAllChecked(checkedArr)
    })
  }
  // 全选数据处理函数
  selectAllHandler(allChecked) {
    const {  tableData } = this.state;
    const {selectedRowKeys, selectedRows, selectData} = this.props;
    const copySelectedRowKeys = this.simpleCloneHandler(selectedRowKeys);
    const copySelectedRows = this.simpleCloneHandler(selectedRows);
    let isAllChecked = allChecked;
    if (allChecked) {
      if (tableData && Array.isArray(tableData) && tableData.length !== 0) {
        let selected = [];
        tableData.forEach((item) => {
          const key = item.key;
          if (selectedRowKeys.indexOf(key) === -1) {
            let isSelect = false;
            //查看是否已选
            for (const iterator of copySelectedRowKeys) {
              if(iterator.startsWith(key.substring(0, key.indexOf("-")+1))){
                //已包含不加入数组
                selected.includes(item.name) ? "" : selected.push(item.name)
                isSelect = true;
                isAllChecked = false
              }
            }
            if(!isSelect){
              copySelectedRowKeys.push(key);
              item.is_add = 1;
              copySelectedRows.push(item);
            }
          }
        });
        if(selected.length){
          Message.warn("已经被选："+selected.join("、"));
        }
      }
    } else {
      if (copySelectedRowKeys && Array.isArray(copySelectedRowKeys) && copySelectedRowKeys.length !== 0) {
        for (let i = 0; i < copySelectedRowKeys.length; i++) {
          if (tableData && Array.isArray(tableData) && tableData.length !== 0) {
            for (let j = 0; j < tableData.length; j++) {
              // 默认选中的人无法取消
              if (copySelectedRowKeys[i] === tableData[j].key && copySelectedRows[i].is_add !== 0) {
                copySelectedRowKeys.splice(i, 1);
                copySelectedRows.splice(i, 1);
                i--;
                break;
              }
            }
          }
        }
      }
    }
    this.setState({
      allChecked: isAllChecked,
    });
    selectData(copySelectedRowKeys, copySelectedRows)
  }


  // 单选数据处理函数
  selectOneHandler(e, key, record) {
    const {allChecked } = this.state;
    const {selectedRowKeys, selectedRows, selectData} = this.props;
    const copySelectedRowKeys = this.simpleCloneHandler(selectedRowKeys);
    const copySelectedRows = this.simpleCloneHandler(selectedRows);
    const target = e.target;
    if (target.checked) {
      if (copySelectedRowKeys.indexOf(key) === -1) {
        let isSelect = false;
        for (const iterator of copySelectedRowKeys) {
          if(iterator.startsWith(key.substring(0, key.indexOf("-")+1))){
            Message.warn("已经被选："+record.name)
            isSelect = true
          }
        }
        if(!isSelect){
          // console.log("单选", key, record);
          copySelectedRowKeys.push(key);
          // 标记记录为从组件新添加
          record.is_add = 1;
          copySelectedRows.push(record);
        }
      }
      if (!allChecked) {
        this.setState({
          allChecked: this.isAllChecked(copySelectedRowKeys)
        });
      }
    } else {
      const index = copySelectedRowKeys.indexOf(key);
      if (index !== -1) {
        if (allChecked) {
          this.setState({
            allChecked: false
          });
        }
        copySelectedRows.splice(index, 1);
        copySelectedRowKeys.splice(index, 1);
      }
    }
    selectData(copySelectedRowKeys, copySelectedRows)
    /* this.setState({
      selectedRowKeys: copySelectedRowKeys,
      selectedRows: copySelectedRows
    }); */
  }

  // 处理单选选项
  selectRadioHandler(e, key, record) {
    const {selectData} = this.props;
    // console.log(e, key, record);
    selectData([key], [record])
   /*  this.setState({
      selectedRowKeys: [key],
      selectedRows: [record]
    }) */
  }
  render() {
    const This = this;
    const { isUpdateMeeting, radio, isPartyGroup, isLeaderGroup,selectedRowKeys, selectedRows, isSelectAll, selectAllNum } = this.props;
    const { allChecked, tableData, current, pageSize, total, loading } = this.state;
    const {} = this.props;
    const columns = [
      {
        // title设置全选按钮
        title: radio ? null : <Checkbox checked={ (selectAllNum > 0 && isSelectAll) || allChecked} 
        disabled={selectAllNum > 0}
        onChange={(e) => {
          const allChecked = e.target.checked;
          this.selectAllHandler(allChecked);
        }}></Checkbox>,
        align: "center",
        dataIndex: "key",
        width: 50,
        render(text, record) {
          const index = selectedRowKeys.indexOf(text);
          if (radio) {
            return <Radio
              disabled={selectAllNum > 0 || (!isUpdateMeeting && selectedRows[index] && selectedRows[index].is_add === 0)}
              checked={(selectAllNum > 0 && isSelectAll) || index !== -1}
              onChange={(e) => This.selectRadioHandler(e, text, record)}></Radio>
          }
          return (
            // 锁死读取时导入的is_add标志位为0的复选框
            <Checkbox
              disabled={selectAllNum > 0 || (!isUpdateMeeting && selectedRows[index] && selectedRows[index].is_add === 0)}
              checked={(selectAllNum > 0 && isSelectAll) || index !== -1}
              onChange={(e) => This.selectOneHandler(e, text, record)}></Checkbox>
          )
        }
      },
      {
        title: "姓名",
        align: "center",
        dataIndex: "name",
        width: 80
      },
      {
        title: "所属组织",
        align: "center",
        dataIndex: "org_name",
        ellipsis: true,
        // width: 220,
        // render: (text, record, index) => {
        //   return <span className="table-org_name" title={text}>{text}</span>
        // }
      },
      {
        title: "电话",
        align: "center",
        dataIndex: "phone_secret",
        width: 110,
        render: (text, record, index) => {
          if (isLeaderGroup) {
            // console.log(record);
            return text;
          }
          else if (isPartyGroup) {
            return record.phone_secret || text;
          }
          else {
            return text;
          }
        }
      }
    ];
    const tableProps = {
      // rowKey: "user_id",
      loading,
      scroll: { y: "459px" },
      bordered: true,
      columns,
      dataSource: tableData,
      pagination: {
        size: "small",
        total,
        current,
        pageSize,
        showTotal: (total) => {
          return `共${total}条记录，${Math.ceil(total/10)}页`
        },
        onChange: (page, pageSize) => {
          const {
            keyword,
            org_type,
            org_id,
            radio,
            isPartyGroup,
            isLeaderGroup,
            selected_org_id,
            selected_org_type,
            tree_type
          } = this.props;
          // if (isPartyGroup) {
          //   this.getOrgGroupMemberFindByUser({
          //     param: keyword,
          //     org_id: selected_org_id || org_id,
          //     org_type: selected_org_type || org_type,
          //     page,
          //     rows: pageSize
          //   });
          // }
          // else {
            if (keyword) {
              if (isLeaderGroup) {
                this.getFindAllUser({ param: keyword,org_type, page });
              } else {
                // this.fetchUserListByKeyword({ param: keyword, tree_type, org_type: selected_org_type || org_type, page, pagesize: pageSize })
                this.fetchUserListByKeyword({
                  ...keyword,
                  org_id: selected_org_id || org_id,
                  page,
                  pageSize,
                  is_employee: this.props.isEmployee
                })
              }
            } else {
              this.fetchUserList({ org_id: selected_org_id || org_id, page, pagesize: pageSize, is_employee: this.props.isEmployee });
            }
          // }
        }
      }
    };
    return (
      <div className="person-table">
        <Table {...tableProps} />
      </div>
    )
  }
}

PersonTable.propTypes = {
  dataSource: propTypes.array,
  org_id: propTypes.number,
  org_type: propTypes.number,
  keyword: propTypes.string,
  radio: propTypes.bool,
  isPartyGroup: propTypes.bool,
  isLeaderGroup: propTypes.bool,
  selectedRowKeys: propTypes.array,
  selectedRows: propTypes.array,
  selectData: propTypes.func
}

PersonTable.defaultProps = {
  dataSource: [],
  selectedRowKeys: [],
  selectedRows:[],
  org_id: typeof window !== "undefined" && window.sessionStorage.getItem("_oid") || 0,
  org_type: 0,
  keyword: "",
  radio: false,
  isPartyGroup: false,
  isLeaderGroup: false
}

export default PersonTable;
