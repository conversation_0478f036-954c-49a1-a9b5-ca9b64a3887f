import React, { Component } from 'react';
import { Popconfirm, Select, Upload, Row, Col, Table, Button, Input, TreeSelect, Icon } from 'antd';
import MainFlex from 'components/layout/main-flex';
import LeftMenu from 'components/left-menu-content';
import LoadingModal from 'components/loading-modal';
import SelfIcon from 'components/self-icon';
import { fileDownload } from 'client/components/file-download';


// 递归生成树
const getTreeData = data => {
    if (data) {
        return data.map(item => {
            if (item.children && item.children.length > 0) {
                return {
                    title: item.name,
                    value: item.org_id,
                    key: item.org_id,
                    children: getTreeData(item.children)
                }
            }

            return {
                title: item.name,
                value: item.org_id,
                key: item.org_id
            }
        })
    }
    return null;
}

export default ({
    targetName,
    current,
    pageSize,
    total,

    pageChangeHandler,

    batchEnable,
    addStep,

    dataCount,
    succeedCount,
    failedCount,
    loadingVisible,
    loadingTitle,
    orgTree,
    orgList = [], tableList = [],
    tableLoading, startImport, uploadProps = {}, onDown,
    tableProps = {
        onEdit() { },
        onSave() { },
        onCancel() { }
    },
    templateUrl,
    orgChangeHandler
}) => {
    // console.log('按钮状态传入', batchEnable);
    const { Option } = Select;
    const leftMenuProps = {
        menuList: [
            {
                key: 0,
                // icon: 'gsg-xiazaimoban',
                // name: '下载导入模板',
                // handleClick(){
                //     onDown();
                // }
                component: <a className="batch_addition_uploadPanel" 
                            onClick={()=>{
                                fileDownload(templateUrl)
                            }}
                            href="javascript:void(0);"
                            >
                    <div className='batch_addition_uploadBtn'>
                        <SelfIcon type='gsg-xiazaimoban' /><span>下载导入模板</span>
                    </div>
                </a>
            },
            {
                key: 1,
                component: <Upload {...uploadProps} className="batch_addition_uploadPanel">
                    <div className='batch_addition_uploadBtn'>
                        <SelfIcon type='gsg-shangchuanziliao' /><span>上传人员数据</span>
                    </div>
                </Upload>
            }
        ],
        listType: true
    };
    const Columns = [
        {
            title: '状态',
            dataIndex: 'state_txt',
            align: 'center',
            key: 'state_txt',
            width: 100,
            render(text, record) {
                return <span style={{ color: text === '导入失败' ? '#FCB12F' : '#000' }}>{text}</span>
            }
        },
        {
            title: '姓名',
            dataIndex: 'name',
            align: 'center',
            key: 'name',
            width: 100,
            render(text, record) {
                let html;
                if (record.isEdit) {
                    html = (
                        <Input id='batchName' defaultValue={text} placeholder='请输入姓名' />
                    );
                } else {
                    html = text;
                }
                return html;
            }
        },
        {
            title: '联系电话',
            dataIndex: 'phone',
            align: 'center',
            key: 'phone',
            width: 150,
            render(text, record) {
                let html;
                if (record.isEdit) {
                    html = (
                        <Input id='batchPhone' defaultValue={text} placeholder='请输入电话' />
                    );
                } else {
                    html = text;
                }
                return html;
            }
        },
        {
            title: '证件号',
            dataIndex: 'cert_number',
            align: 'center',
            key: 'cert_number',
            width: 200,
            render(text, record) {
                let html;
                if (record.isEdit) {
                    html = (
                        <Input id='batchCard' defaultValue={text} placeholder='请输入证件号' />
                    );
                } else {
                    html = text;
                }
                return html;
            }
        },
        {
            title: '失败原因',
            dataIndex: 'error_msg',
            align: 'center',
            key: 'error_msg',
            render(text, record) {
                return <span style={{ color: '#FF4D4F' }}>{text}</span>;
            }
        },
        {
            title: '操作',
            align: 'center',
            width: 100,
            render(text, record, index) {
                let html;
                if (record.state_txt === '准备导入') {
                    return null;
                }
                if (record.isEdit) {
                    html = (
                        <span>
                            <a onClick={() => {
                                tableProps.onSave(record, index);
                            }}>提交</a>
                            &nbsp;
                            <Popconfirm
                                title="确认是否取消?"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => tableProps.onCancel(record)}
                            >
                                <a>取消</a>
                            </Popconfirm>
                        </span>
                    );
                } else {
                    html = (
                        <a onClick={() => {
                            tableProps.onEdit(record);
                        }}>编辑</a>
                    );
                }
                return html;
            }
        }
    ];
    return (
        <div className={'index-view-container'}>
            <LoadingModal
                modalVisible={loadingVisible}
                tip={loadingTitle}
            />
            <MainFlex
                leftComponent={
                    <LeftMenu {...leftMenuProps} />
                }
                rightComponent={
                    <section className="right-content-wrapper">
                        <header>
                            <Row gutter={1} type={'flex'} align={'middle'} style={{ marginBottom: '30px' }}>
                                <Col>
                                    <span>
                                        将人员批量新增到
                                    </span>
                                </Col>
                                <Col style={{ margin: '0 25px 0 25px' }}>
                                    {/* <TreeSelect
                                        style={{ width: 220 }}
                                        dropdownMatchSelectWidth={false}
                                        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                        treeData={getTreeData(orgTree)}
                                        placeholder="请选择"
                                        showSearch={true}
                                        treeDefaultExpandedKeys={['0']}
                                        treeNodeFilterProp="title"
                                        onChange={orgChangeHandler}
                                    /> */}
                                    【{targetName}】
                                </Col>
                                <Col>
                                    <span>
                                        当前准备导入 {dataCount} 条,导入成功 {succeedCount} 条,导入失败 {failedCount} 条。
                                    </span>
                                </Col>
                            </Row>
                            <Table
                                // loading={tableLoading}
                                dataSource={tableList}
                                columns={Columns}
                                pagination={
                                    addStep === 1 ? {
                                        current,
                                        total,
                                        pageSize,
                                        onChange(page, pagesize) {
                                            pageChangeHandler(page, pagesize);
                                        },
                                        showTotal(total, range) {
                                            return tableList.length > 1 ? `当前显示${range.join('至')}条` : `当前显示第${total}条`;
                                        }
                                    } : false
                                }
                            />
                        </header>
                        <footer className='batch-footer'>
                            {
                                addStep === 1 &&
                                <Button type={'primary'}
                                    onClick={startImport}
                                // loading={loadingVisible}
                                >开始导入</Button>
                            }
                        </footer>
                    </section>
                }
            />
        </div>
    )
}