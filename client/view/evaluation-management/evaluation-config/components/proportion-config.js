import React, {useEffect} from 'react'
import {Spin, Form, Row, Col, Input, Button, message} from 'antd'
import {loadGoodCadre, editGoodCadre} from "client/apis/evaluation-management/evaluation-config"

const ProportionConfig = ({form}) => {
    const [loading, setLoading] = React.useState(false)
    const formItems = [
        {
            label: '班子测评-正职',
            field: 'team_eval_principal',
            placeholder: '不限制'
        },
        {
            label: '班子测评-副职',
            field: 'team_eval_deputy',
            placeholder: '不限制'
        },
        {
            label: '班子测评-中层',
            field: 'team_eval_middle',
            placeholder: '不限制'
        },
        {
            label: '市管领导测评-班子',
            field: 'city_leader_eval_charge_deputy',
            placeholder: '不限制'
        },
        {
            label: '市管领导测评-正职',
            field: 'city_leader_eval_principal',
            placeholder: '不限制'
        },
        {
            label: '市管领导测评-分管正职',
            field: 'city_leader_eval_charge_principal',
            placeholder: '不限制'
        },
        {
            label: '市管领导测评-分管副职',
            field: 'city_leader_eval_team',
            placeholder: '不限制'
        }
    ]
    const validateProportion = (rule, value, callback) => {
        if (value) {
            if (!/^0\.\d+$|^\.\d+$|^1\.0+$/.test(value)) {
                callback('请输入有效的小数格式（如0.5）')
                return
            }
        }
        callback()
    }
    const handleLoadGoodCadre = async () => {
        setLoading(true)
        const {data: {data, code}} = await loadGoodCadre()
        if(code === 0) {
            Object.keys(data).forEach(item => {
                form.setFieldsValue({
                    [item]: data[item] === -1 ? '' : data[item]
                })
            })
        }
        setLoading(false)
    }
    const handleSubmit = (e) => {
        e.preventDefault()
        form.validateFields(async (err, values) => {
            setLoading(true)
            const params = {}
            for(const k in values) {
                params[k] = values[k] || -1
            }
            const {data} = await editGoodCadre(params)
            setLoading(false)
            if (data.code === 0) {
                message.success('设置成功')
            } else {
                message.error(data.message)
            }
           await handleLoadGoodCadre()
        })
    }
    useEffect(() => {
        handleLoadGoodCadre()
    }, [])
    return (
        <Spin spinning={loading}>
            <div className="tip" style={{marginTop: 0}}>
                注：默认不填是不限制。限制是对同一组的被测评对象人数进行限制。
            </div>
            <Form>
                <Row gutter={[156, 40]}>
                    {
                        formItems.map(item => (
                            <Col span={8} key={item.field}>
                                <Form.Item label={item.label}>
                                    {form.getFieldDecorator(item.field, {
                                        rules: [
                                            { validator: validateProportion }
                                        ]
                                    })(
                                        <Input placeholder={item.placeholder} style={{width: '100%'}}/>
                                    )}
                                </Form.Item>
                            </Col>
                        ))
                    }
                </Row>
            </Form>
            <Button className="save-button" type="primary" onClick={handleSubmit}>保存</Button>
        </Spin>
    )
}

export default Form.create()(ProportionConfig)
