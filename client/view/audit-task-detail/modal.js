import React from 'react';
import { Modal } from 'antd';
import { Form, Button, Spin, Row, Col, Radio, Input, Tooltip } from 'antd';
import InputTips from "components/activity-form/sub-components/InputTips";
import EvalTaskViewBox from "client/view/examination-task-view/eval-task-viewbox"

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { TextArea } = Input;

const labelCol = {
    span: 3
}
const wrapperCol = {
    span: 21
}

const TaskModal = ({
    orgId,
    form, isVisible, onCancel, onInit, onSubmit, isSubmitLoading, isLoading, title, isEdit,
    dataSource = {}
}) => {
    const summary = dataSource && decodeURIComponent(dataSource.summary || '');
    const files = (dataSource && dataSource.files) || [];
    const executions = (dataSource && dataSource.recordList) || [];
    const create_time = (dataSource && dataSource.create_time) || "";
    const { getFieldDecorator, getFieldValue } = form;

    onInit && onInit(form);

    return (
        <Modal
            title={title}
            visible={isVisible}
            width={900}
            footer={null}
            onCancel={() => {
                onCancel(form);
            }}
            className="task-modal"
            destroyOnClose={true}
        >
            <Spin spinning={isLoading}>
            <EvalTaskViewBox
              summary={summary}
              files={files}
              executions={executions}
              create_time={create_time}
            />

            {isEdit && (
                <Form>
                    <Row className="task-modal-row">
                        <Col {...labelCol} className="label formItemLabel" style={{width: 100}}>审核结果：</Col>
                        <Col {...wrapperCol}>
                            <FormItem>
                            {getFieldDecorator( "operation_status" , {
                                initialValue: 6
                            })(
                                <RadioGroup>
                                    <Radio value={6}>合格</Radio>
                                    <Radio value={5}>不合格</Radio>
                                    <Radio value={10}>退回</Radio>
                                </RadioGroup>
                            )}
                            </FormItem>
                        </Col>
                    </Row>
                    <Row className="task-modal-row">
                        <Col {...labelCol} className="label" style={{marginTop: 5, width: 100}} >审核意见：</Col>
                        <Col {...wrapperCol}>
                            <FormItem>
                                <InputTips
                                    max={500}
                                    text={getFieldValue('descriptive_opinions')}
                                    placement="bottomRight"
                                    overlayClassName="eval-task-viewbox-titleTips"
                                >
                                    {getFieldDecorator( "descriptive_opinions" , {
                                        initialValue: null,
                                        rules: [{
                                            max: 500,
                                            message: '最多输入500个汉字'
                                        }]
                                    })(
                                        <TextArea
                                            rows={3}
                                            style={{width: 400}}
                                            placeholder='请输入审批意见'
                                        />
                                    )}
                                </InputTips>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row className="task-modal-row">
                        <Button
                            style={{ marginLeft: '107px' }}
                            type="primary"
                            htmlType="submit"
                            loading={isSubmitLoading}
                            onClick={() => {
                                onSubmit({
                                    orgId,
                                    form
                                });
                            }}
                        >提交</Button>
                    </Row>
                </Form>
            )}
            </Spin>

        </Modal>
    )
}

export default Form.create()(TaskModal);