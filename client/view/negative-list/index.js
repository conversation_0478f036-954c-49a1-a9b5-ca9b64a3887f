import {
  Button,
  DatePicker,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Table,
  Tree
} from "antd";
import {
  addOrUpdatePunishInfo,
  deletePunishInfo,
  exportPunishList,
  getOrgUserList,
  queryByCode,
  queryOrgType,
  queryPunishInfo,
  queryPunishInfoList,
} from "client/apis/cadre-portrait";
import { connect } from "dva";
import { Component } from "react";
import "./index.less";
const { TreeNode } = Tree;
const { Option } = Select;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;

import OrgTree from "client/components/org-tree";
import DateSingle from "components/date-single/DateSingle";
import PropTypes from "prop-types";
import PAutoComplete from "../praise-commendation/components/PAutoComplete";
class LeaderGroup extends Component {
  constructor(props) {
    super(props);
    const currentUserInfo =
      typeof window !== "undefined"
        ? JSON.parse(window.sessionStorage.getItem("userInfo"))
        : {};
    const currentOid =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";
    this.state = {
      visible: false,
      loading: false,
      org_unit_name: "",
      has_leader: "",
      page: 1,
      selectKey: currentOid,
      expandKeys: [],
      autoExpandParent: true,
      selectName: currentUserInfo.name || "",
      activeTabKey: 0,
      isTabSwitch: false,
      currentSelect: {},
      // 新增
      org_id: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      selectOrgId: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      user_id: undefined,
      user_name: "",
      level: "all",
      user_phone: "",
      user_label: "",
      page: 1,
      page_size: 10,
      pageNum: 0,
      total: 0,
      dataSource: [],
      link: null,
      options: [],
      orgType: 1, //1-班子，2-组织
    };
  }

  componentDidMount() {
    this.getOrgTypeList();
    this.getOrgList();
    this.getCode();
    this.queryList();
  }

  queryList() {
    this.setState({
      loading: true,
    });

    let { selectOrgId, user_name, level, page } = this.state;

    if (level === "all") level = undefined;

    queryPunishInfoList({
      org_id: selectOrgId,
      user_name,
      level,
      page,
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;

        this.setState({
          dataSource: data.content,
          pages: data.totalPages,
          total: data.totalElements,
          loading: false,
        });
      } else {
        message.error(res.data.message);
        this.setState({
          loading: false,
        });
      }
    });
  }
  queryOrgType(org_id) {
    queryOrgType({
      org_id,
    }).then((res) => {
      if (res.data.code === 0) {
        this.setState({
          orgType: res.data.data.org_type,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  onExport() {
    const { user_name, type, level, selectOrgId } = this.state;
    let obj = {
      user_name,
      type,
      level,
      org_id: selectOrgId,
    }
    if (level === "all") delete obj.level;
    if (!user_name) delete obj.user_name;
    exportPunishList(obj).then((res) => {
      if (res.data.code === 0) {
      }
    });
  }

  pageChange(page) {
    this.setState(
      {
        page: page,
      },
      () => {
        this.queryList();
      }
    );
  }
  onReset() {
    this.setState(
      {
        user_name: "", // 清空姓名查询条件
        level: "all", // 重置类别为默认值 "all"
        page: 1, // 将页码重置为第一页
      },
      () => {
        console.log("🚀 ~ user_name:", this.state)
        this.queryList(); // 重新加载数据
      }
    );
  }
  onSearch() {
    this.setState(
      {
        page: 1,
      },
      () => {
        this.queryList();
      }
    );
  }
  onDelete(record) {
    deletePunishInfo({ punish_id: record.pms_punishment_id }).then((res) => {
      if (res.data.code === 0) {
        message.success("删除成功");

        this.queryList();
      }
    });
  }
  onEditor(record) {
    const { setFieldsValue } = this.props.form;

    queryPunishInfo({
      punish_id: record.pms_punishment_id,
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        this.setState({
          visible: true,
          currentSelect: data,
        });

        // if (data.punishment_time) {
        //   data.punishment_time = moment(data.punishment_time);
        // }

        setFieldsValue(data);
      } else {
        message.error(res.data.message);
      }
    });
  }
  selectTree(org) {
    if (!org.length) return;

    this.setState(
      {
        page: 1,
        selectOrgId: org[0],
      },
      () => {
        this.queryList();
        this.queryOrgType(org[0]);
      }
    );
  }
  async getCode() {
    const [res1, res2, res3] = await Promise.all([
      queryByCode({
        code: 9511301,
      }),
      queryByCode({
        code: 9511302,
      }),
      queryByCode({
        code: 9511303,
      }),
    ]);
    if (!res1.data.code && !res2.data.code && !res3.data.code) {
      const option = [].concat(res1.data.data, res2.data.data, res3.data.data);

      this.setState({
        options: option,
      });
    }
  }
  async getOrgList() {
    const params = {
      name: this.state.user_name,
      phone: this.state.user_phone,
      user_tag: this.state.user_label,
      org_id: this.state.org_id,
      page: this.state.page,
      page_size: this.state.page_size,
    };
    const { data } = await getOrgUserList(params);
    console.log(data, "org_id");
    if (data.code == 0) {
      this.setState({
        pageNum: data.pageNum,
        total: data.total,
      });
    } else {
      message.error(data.message);
    }
  }
  // 组织类型获取
  getOrgTypeList(orgId) {
    const { dispatch } = this.props;
    const { selectKey } = this.state;
    dispatch({
      type: "leaderGroup/getTreeTypeList",
      payload: {
        org_id: orgId || selectKey,
      },
    }).then(() => {
      this.loadTreeList(orgId || selectKey);
    });
  }
  // 左边树 滚动到指定位置
  expandTree(keys, params) {
    const { expanded, node } = params;
    const {
      dataRef: { org_id },
    } = node.props;
    this.setState(
      {
        expandKeys: keys,
        autoExpandParent: false,
        isTabSwitch: false,
      },
      () => {
        expanded && this.loadTreeList(org_id);
      }
    );
  }

  tabChange(key) {
    this.setState(
      {
        activeTabKey: key,
        isTabSwitch: true,
        autoExpandParent: false,
        expandKeys: [],
        selectKey: this.state.selectKey,
        page: 1,
      },
      () => {
        this.loadTreeList(this.state.selectKey);
        this.loadOrgUnit();
      }
    );
  }
  //切换树
  loadOrgUnit() {
    const { selectKey } = this.state;
    this.setState(
      {
        org_id: selectKey,
      },
      () => {
        this.getOrgList();
      }
    );
  }
  //组织树
  loadTreeList(org_id) {
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey, isTabSwitch } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      tree_type,
      org_type,
      org_id,
      isTabSwitch,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    dispatch({
      type: "leaderGroup/getOrgTree",
      payload,
    });
  }
  // 模糊查询组织列表
  searchOrgList(value) {
    console.log(this.state.selectKey, "this.state.selectKey");
    const { dispatch } = this.props;
    if (!value) {
      this.setState(
        {
          selectKey: JSON.parse(sessionStorage.getItem("userInfo")).oid,
        },
        () => {
          dispatch({
            type: "leaderGroup/findOrgByName",
            payload: {
              org_id: this.state.selectKey,
              org_name: value,
              tree_type: 2,
            },
          });
        }
      );
    } else {
      dispatch({
        type: "leaderGroup/findOrgByName",
        payload: {
          org_id: this.state.selectKey,
          org_name: value,
          tree_type: 2,
        },
      });
    }
  }
  // 点击组织树
  selectTreeOrg(value, name) {
    this.setState(
      {
        selectKey: value,
        selectName: name,
        page: 1,
        org_id: value,
      },
      () => {
        this.getOrgList();
        this.loadOrgUnit();
      }
    );
  }
  // 左边树查询
  selectOrg(value, option) {
    console.log("1111", value, option, this.state.selectKey);
    const { key } = option;
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      root_org_id: this.state.selectKey,
      org_id: key,
      org_type,
      load_root: 1,
      tree_type,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    if (key == this.state.selectKey) {
      return;
    }
    dispatch({
      type: "leaderGroup/getAllOrgs",
      payload,
    }).then(() => {
      this.setState(
        {
          selectKey: key,
          expandKeys: [key],
          autoExpandParent: true,
          selectName: value,
          page: 1,
          org_id: key,
        },
        () => {
          this.getOrgList();
          this.loadOrgUnit();
        }
      );
      this._scrollPos(key);
    });
  }
  //树
  renderTreeNodes(data) {
    return data.map((item) => {
      const { child_org_num, org_id, name, short_name } = item;
      const { selectKey } = this.state;
      if (item.children) {
        return (
          <TreeNode
            isLeaf={child_org_num === 0}
            title={
              <div
                ref={org_id}
                title={name || short_name}
                className={
                  parseInt(selectKey) === parseInt(org_id) ? "active" : ""
                }
                onClick={this.selectTreeOrg.bind(this, org_id, name)}
              >
                {short_name || name}
              </div>
            }
            key={org_id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf={child_org_num === 0}
          title={
            <div
              ref={org_id}
              onClick={this.selectTreeOrg.bind(this, org_id, name)}
            >
              {short_name || name}
            </div>
          }
          key={org_id}
          dataRef={item}
        />
      );
    });
  }
  handleOk() {
    const { getFieldsValue, validateFields } = this.props.form;
    validateFields((error, values) => {
      if (!error) {
        const params = getFieldsValue();

        const { user_name, punishment_time } = params;

        if (toString.call(user_name) === "[object Object]") {
          delete params.user_name;

          params.user_name = user_name.user_name;
          params.user_id = user_name.user_id;
        }

        if (punishment_time) {
          params.punishment_time = punishment_time
        }
        addOrUpdatePunishInfo({
          ...this.state.currentSelect,
          ...params,
        }).then((res) => {
          if (res.data.code === 0) {
            this.setState({
              visible: !this.state.visible,
            });

            message.success("提交成功");

            this.queryList();
          } else {
            message.error(res.data.message);
          }
        });
      }
    });
  }
  handleCancel() {
    this.setState({
      visible: !this.state.visible,
    });
  }
  cancel(e) { }
  jumpImport() {
    this.props.history.push("/import-page?type=14");
  }
  confirm(record) {
    this.onDelete(record);
  }
  render() {
    const { tabs, visible, index, autoExpandParent, expandKeys, dataSource } =
      this.state;
    const { dispatch, form, leaderGroup } = this.props;
    const { getFieldDecorator } = form;
    const totalPage = Math.ceil(leaderGroup.totalCount / 10);
    const _expandKeys =
      leaderGroup &&
        leaderGroup.treeList &&
        leaderGroup.treeList.length &&
        leaderGroup.treeList[0].children.length
        ? leaderGroup.treeList[0].org_id
        : "";
    const columns = [
      {
        title: "姓名",
        dataIndex: "user_name",
        key: "user_name",
        width: 100,
        align: "center",
      },
      // {
      //   title: "出生年月",
      //   dataIndex: "birthday",
      //   key: "birthday",
      //   width: 100,
      //   align: "center",
      // },
      {
        title: "时任职务",
        dataIndex: "job",
        key: "job",
        width: 100,
        align: "center",
      },
      {
        title: "类型",
        dataIndex: "level",
        key: "level",
        width: 100,
        align: "center",
      },
      {
        title: "生效时间",
        dataIndex: "punishment_time",
        key: "punishment_time",
        width: 100,
        align: "center",
      },
      {
        title: "主要内容或处理情况",
        dataIndex: "reason",
        key: "reason",
        align: "center",
        render(_) {
          return <div style={{ width: "100%", textAlign: "left" }}>{_}</div>;
        },
        // render(_) {
        //   return (
        //     <div className="line-limit" title={_}>
        //       {_}
        //     </div>
        //   );
        // },
      },
      // {
      //   title: "文书文号",
      //   dataIndex: "attachment",
      //   key: "attachment",
      //   width: 100,
      //   align: "center",
      // },
      {
        title: "影响期",
        dataIndex: "impact_period",
        key: "impact_period",
        width: 100,
        align: "center",
      },
      {
        title: "备注",
        dataIndex: "remark",
        key: "remark",
        width: 100,
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "handle",
        key: "handle",
        align: "center",
        width: 100,
        render: (_, record) => {
          return (
            <div className="handle">
              <Popconfirm
                title="确定删除吗?"
                onConfirm={() => {
                  this.confirm(record);
                }}
                onCancel={this.cancel.bind(this)}
                okText="确定"
                cancelText="取消"
              >
                <a href="#">删除</a>
              </Popconfirm>
              <a
                onClick={() => {
                  this.onEditor(record);
                }}
              >
                编辑
              </a>
            </div>
          );
        },
      },
    ];
    const data = [
      {
        key: "1",
        name: "John Brown",
        post: "市长",
        unit: "市政府",
        sort: "市政府",
        category: "市政府",
        award: "市政府",
        gist: "市政府",
        remark: "市政府",
      },
    ];
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };

    const yearArray = () => {
      const arr = [];
      for (let i = 1960; i <= new Date().getFullYear(); i++) {
        arr.push(i);
      }
      return arr;
    };
    return (
      <div className="praise-commendation">
        <div className="left">
          {/* <AutoComplete
            className="searchOrg"
            placeholder="请输入组织名称"
            allowClear
            optionLabelProp="value"
            onSearch={this.searchOrgList.bind(this)}
            onSelect={this.selectOrg.bind(this)}
          >
            {leaderGroup &&
              leaderGroup.orgList &&
              leaderGroup.orgList.map((item) => {
                return (
                  <AutoComplete.Option
                    key={item.org_id}
                    value={item.org_name}
                    text={item.org_name}
                  >
                    {item.org_name}
                  </AutoComplete.Option>
                );
              })}
          </AutoComplete>
          <div className="orgContainer" ref="orgContainer">
            <Tree
              blockNode={true}
              autoExpandParent={autoExpandParent}
              onExpand={this.expandTree.bind(this)}
              expandedKeys={
                expandKeys.length ? expandKeys : [_expandKeys.toString()]
              }
              onSelect={this.selectTree.bind(this)}
            >
              {leaderGroup.treeList
                ? this.renderTreeNodes(leaderGroup.treeList)
                : null}
            </Tree>
          </div> */}
          <OrgTree onChange={this.selectTree.bind(this)} />
        </div>
        <div className="right">
          <div className="praise-header">
            <div className="header-search">
              <div>
                <span className="r-label">姓名：</span>
                <Input
                  placeholder="请输入"
                  style={{ width: "160px" }}
                  value={this.state.user_name}
                  onChange={(event) => {
                    this.setState({ user_name: event.target.value });
                  }}
                />
              </div>
              <div className="levelcss">
                <span className="r-label">类别：</span>
                <Select
                  // style={{ width: '300px' }}
                  className="custom-select"
                  onChange={(value) => {
                    this.setState({ level: value });
                  }}
                  // defaultValue={"all"}
                  value={this.state.level}
                  allowClear
                >
                  <Option value="all">全部</Option>
                  {this.state.options.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              </div>
              <div className="search-btn">
                <div
                  className="header-btn"
                  onClick={() => {
                    this.onSearch();
                  }}
                >
                  查询
                </div>
                <div className="reset" onClick={() => this.onReset()}>
                  重置
                </div>
                <div
                  className="reset"
                  onClick={() => {
                    this.onExport();
                  }}
                >
                  导出
                </div>
              </div>
            </div>

            <div className="btn-item">
              <div
                className="header-btn"
                onClick={() => this.setState({ visible: true })}
              >
                <Icon type="plus" />
                添加
              </div>
              <Button className="import-btn" onClick={() => this.jumpImport()}>
                导入
              </Button>
            </div>
          </div>
          <div className="praise-content">
            <Table
              bordered
              columns={columns}
              dataSource={dataSource}
              loading={this.state.loading}
              pagination={{
                pageSize: 10,
                total: this.state.total,
                current: this.state.page,
                onChange: (page) => {
                  this.setState(
                    {
                      page,
                    },
                    () => {
                      this.queryList();
                    }
                  );
                },
              }}
            />
          </div>
        </div>
        <Modal
          title={`${this.state.currentSelect.user_name ? "编辑" : "添加"
            }负面清单`}
          visible={visible}
          onOk={this.handleOk.bind(this)}
          onCancel={this.handleCancel.bind(this)}
          width={800}
          destroyOnClose
        >
          <Form {...formItemLayout}>
            {getFieldDecorator("user_id", {})(<Input type="hidden" />)}
            {getFieldDecorator(
              "pms_punishment_id",
              {}
            )(<Input type="hidden" />)}
            <Form.Item label="姓名" required>
              {getFieldDecorator("user_name", {
                rules: [{ required: true, message: "请输入姓名" }],
              })(
                <PAutoComplete
                  org_id={this.state.selectOrgId}
                  placeholder="请输入"
                  style={{ width: "300px" }}
                />
              )}
            </Form.Item>
            {/* <Form.Item label="出生年月">
              {getFieldDecorator("birthday", {
                rules: [{ required: true, message: "请选择出生年月" }],
              })(
                <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
              )}
            </Form.Item> */}
            {/* <Form.Item label="批准单位">
              {getFieldDecorator("authorized_unit", {
                rules: [{ required: false, message: "请输入批准单位" }],
              })(
                <Input
                  placeholder="请输入批准单位"
                  style={{ width: "300px" }}
                />
              )}
            </Form.Item> */}
            {/* <Form.Item label="授予单位">
              {getFieldDecorator("unit", {
                rules: [{ required: true, message: "请输入授予单位" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="授予单位类别">
              {getFieldDecorator("time", {
                rules: [{ required: true, message: "请选择" }],
                initialValue: "all",
              })(
                <Select
                  defaultValue="all"
                  style={{ width: 120 }}
                  onChange={null}
                >
                  <Option value="all">全部</Option>
                </Select>
              )}
            </Form.Item> */}
            <Form.Item label="类型">
              {getFieldDecorator("level", {
                rules: [{ required: true, message: "请选择" }],
              })(
                <Select
                  style={{ width: 120 }}
                  onChange={null}
                  placeholder="请选择"
                >
                  <Option value="all">全部</Option>
                  {this.state.options.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              )}
            </Form.Item>
            {/* <Form.Item label="奖励名称">
              {getFieldDecorator("unit", {
                rules: [{ required: true, message: "请输入奖励名称" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="文件依据">
              {getFieldDecorator("why", {
                rules: [{ required: true, message: "请输入文件依据" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item> */}
            <div style={{ display: "flex", width: "100%" }}>
              <Form.Item
                label="生效时间："
                style={{ width: "100%" }}
                labelCol={{ span: 5 }}
                wrapperCol={{ span: 19 }}
              >
                <div style={{ display: "flex", width: "100%" }}>
                  <div>
                    {getFieldDecorator("punishment_time", {
                      rules: [{ required: true, message: "请选择" }],
                    })(
                      <DateSingle
                        placeholder="请选择"
                        type="month"
                        allowClear={false}
                        isWrap={false}
                      />
                    )}
                    {/* <span style={{ margin: "0px 10px" }}></span> */}
                    {/* {getFieldDecorator("time", {
                      rules: [{ required: true, message: "请选择" }],
                      initialValue: "all",
                    })(
                      <Select
                        defaultValue="all"
                        style={{ width: 120 }}
                        onChange={null}
                      >
                        <Option value="all">1</Option>
                      </Select>
                    )}
                    <span style={{ margin: "0px 10px" }}>月</span> */}
                  </div>
                  <Form.Item
                    label="影响期"
                    style={{ marginBottom: 0, width: "50%" }}
                    labelCol={{ span: 6 }}
                    wrapperCol={{ span: 5 }}
                  >
                    <div style={{ whiteSpace: "nowrap" }}>
                      {getFieldDecorator("impact_period", {
                        rules: [{ required: false, message: "请输入影响期" }],
                      })(
                        <InputNumber
                          placeholder="请输入"
                          style={{ width: "100px" }}
                        />
                      )}
                      <span style={{ margin: "0px 10px" }}>月</span>
                    </div>
                  </Form.Item>
                </div>
              </Form.Item>
            </div>

            <Form.Item label="时任职务">
              {getFieldDecorator("job", {
                rules: [{ required: false, message: "请输入时任职务" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            {/* <Form.Item label="文书文号">
              {getFieldDecorator("attachment", {
                rules: [{ required: false, message: "请输入时任职务" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item> */}
            {/* <Form.Item label="文书文号">
              {getFieldDecorator("remark", {
                rules: [{ required: false, message: "请输入文书文号" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item> */}
            <Form.Item label="批准单位">
              {getFieldDecorator("authorized_unit", {
                rules: [{ required: true, message: "请输入批准单位" }],
              })(
                <Input
                  placeholder="请输入批准单位"
                  style={{ width: "300px" }}
                />
              )}
            </Form.Item>
            {/* <Form.Item label="备注">
              {getFieldDecorator("remark", {
                rules: [{ required: false, message: "请输入时任备注" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item> */}

            <Form.Item label="主要内容或处理情况">
              {getFieldDecorator("reason", {
                rules: [{ required: true, message: "请输入主要内容或处理情况" }],
              })(
                <Input.TextArea
                  type="textarea"
                  rows={4}
                  placeholder="请输入"
                  style={{ width: "500px" }}
                />
              )}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(Form.create()(LeaderGroup));
