import React, { Component } from "react";
import { message as Message, Checkbox, Radio } from "antd";
import "./organize-list.less";
import SelfIcon from "components/self-icon";
import { fetchOrganisitionTree } from "apis/organisition";
import { findByName } from "apis/organize";

import propTypes from "prop-types";
import Item from "antd/lib/list/Item";
import LoadingModal from "components/loading-modal";

const RadioGroup = Radio.Group;
class OrganizeList extends Component {
  constructor() {
    super();
    this.state = {
      // 选择器数据
      selectorData: [],
      // 选中的选项，带名称等参数，用于右侧列表的显示
      orgs: [],
      // 选中的列表，用于存储所有选中组织的org_id，用于控制复选框的状态
      checked: [],
      // 选中的列表，用于存储所有选中组织的org_id，用于控制单选框的状态
      radio: {},
      // 展开的树节点
      // expanded: [],
      // 选择器的类型，是组织树或者组织列表，默认为1，组织树，可以取2，组织列表
      selectorType: 1,
      // 记录组织树形选择时顶部面包屑
      breadcrumb: [],
      // 控制是否全选
      isCheckAll: false,
      // 禁用交互的组织
      disabledOrgList: [],
      // loading显示控制
      loadingModalVisible: false,
    };
    this.fetchOrganize = this.fetchOrganize.bind(this);
    this.removeOrg = this.removeOrg.bind(this);
    this.exportData = this.exportData.bind(this);
    this.listCheckHandler = this.listCheckHandler.bind(this);
    this.initData = this.initData.bind(this);
    this.renderCheckbox = this.renderCheckbox.bind(this);
    this.renderRadio = this.renderRadio.bind(this);
  }

  componentWillMount() {
    const { orgs } = this.state;
    const { dataSource, disabledOrgList } = this.props;
    if (disabledOrgList && Array.isArray(disabledOrgList)) {
      this.setState({
        disabledOrgList,
      });
    }
    this.initData(dataSource);
    // console.log(dataSource);
  }

  componentWillReceiveProps(props) {
    const { orgs } = this.state;
    const { dataSource, disabledOrgList } = props;
    if (disabledOrgList && Array.isArray(disabledOrgList)) {
      this.setState({
        disabledOrgList,
      });
    }
    // 如果已经存在操作的结果了，则不能执行初始化
    if (!orgs || !Array.isArray(orgs) || orgs.length === 0) {
      // console.log("搜索的时候不期待执行这里", dataSource);
      this.initData(dataSource);
    }
  }

  resetOrgs() {
    this.setState({
      orgs: [],
    });
  }

  initData(dataSource) {
    // console.log("初始化数据", dataSource);
    let orgs = [],
      checked = [];
    dataSource.forEach((item, index) => {
      // console.log(item);
      if (item) {
        orgs.push({
          org_name: item.org_name,
          org_id: item.org_id,
        });
        checked.push(item.org_id);
      }
    });
    this.setState({
      orgs,
      radio: orgs[0] ? orgs[0].organization_id || orgs[0].org_id : "",
      checked,
      isCheckAll: this.isAllChecked(checked),
    });
  }

  // 通过ref向外部输出组织选择结果数据
  exportData() {
    const { orgs } = this.state;
    return JSON.parse(JSON.stringify(orgs));
  }

  // 从右侧移除一个组织标签
  removeOrg(index, org = {}) {
    const { orgs, checked, isCheckAll, radio } = this.state;
    if (isCheckAll) {
      this.setState({
        isCheckAll: false,
      });
    }
    let copyOrgs = JSON.parse(JSON.stringify(orgs)),
      copyChecked = JSON.parse(JSON.stringify(checked));
    if (org.org_id === orgs[index].org_id) {
      copyOrgs.splice(index, 1);
      copyChecked.splice(index, 1);
    }
    this.setState({
      checked: copyChecked,
      orgs: copyOrgs,
    });

    // 如果是单选，则将
    if (radio) {
      this.setState({
        radio: {},
      });
    }
  }

  // 全选当前列表页的组织
  checkAllHandler(isCheckAll) {
    this.setState({
      isCheckAll,
    });
    const { orgs, checked, selectorData } = this.state;
    let copyOrgs = JSON.parse(JSON.stringify(orgs)),
      copyChecked = JSON.parse(JSON.stringify(checked));

    // 全选
    if (isCheckAll) {
      if (
        selectorData &&
        Array.isArray(selectorData) &&
        selectorData.length !== 0
      ) {
        selectorData.forEach((item) => {
          // console.log(item, checked);
          if (
            checked.indexOf(item.organization_id) === -1 &&
            checked.indexOf(item.org_id) === -1
          ) {
            copyChecked.push(item.organization_id || item.org_id);
            copyOrgs.push({
              org_name: item.name,
              org_id: item.organization_id || item.org_id,
            });
          }
        });
      }
    }
    // 取消全部
    else {
      if (
        copyChecked &&
        Array.isArray(copyChecked) &&
        copyChecked.length !== 0
      ) {
        for (let i = 0; i < copyChecked.length; i++) {
          // copyChecked[i]
          if (
            selectorData &&
            Array.isArray(selectorData) &&
            selectorData.length !== 0
          ) {
            for (let j = 0; j < selectorData.length; j++) {
              if (
                copyChecked[i] === selectorData[j].organization_id ||
                copyChecked[i] === selectorData[j].org_id
              ) {
                // console.log("删除", i);
                copyChecked.splice(i, 1);
                copyOrgs.splice(i, 1);
                i--;
                break;
              }
            }
          }
        }
      }
    }
    this.setState({
      checked: copyChecked,
      orgs: copyOrgs,
    });
  }

  // 检查当前列表是否被全选,checkedArr传入当前勾选的组织数组
  isAllChecked(checkedArr, allArr) {
    let result = true;
    if (!allArr) {
      const { selectorData } = this.state;
      allArr = selectorData;
    }
    if (!checkedArr) {
      const { checked } = this.state;
      checkedArr = checked;
    }
    if (
      checkedArr &&
      Array.isArray(checkedArr) &&
      allArr &&
      Array.isArray(allArr) &&
      checkedArr.length !== 0 &&
      checkedArr.length >= allArr.length
    ) {
      for (let i = 0; i < allArr.length; i++) {
        if (
          checkedArr.indexOf(allArr[i].organization_id) === -1 &&
          checkedArr.indexOf(allArr[i].org_id) === -1
        ) {
          result = false;
          break;
        }
      }
    } else {
      result = false;
    }
    return result;
  }

  // 列表勾选处理
  listCheckHandler(item, isCheck) {
    const { orgs, checked, isCheckAll, selectorData } = this.state;
    let copyOrgs = JSON.parse(JSON.stringify(orgs)),
      copyChecked = JSON.parse(JSON.stringify(checked));
    // console.log(checked, orgs);
    // console.log(isCheck, item);
    if (isCheck) {
      if (copyOrgs && Array.isArray(copyOrgs)) {
        copyOrgs.push({
          org_name: item.name,
          org_id: item.organization_id || item.org_id,
        });
        copyChecked.push(item.organization_id || item.org_id);
        // console.log(copyChecked, selectorData)
        // 如果为勾选,检查当前页是否被全选
        this.setState({
          isCheckAll: this.isAllChecked(copyChecked),
        });
      }
    } else {
      if (isCheckAll) {
        this.setState({
          isCheckAll: false,
        });
      }
      if (copyOrgs && Array.isArray(copyOrgs)) {
        for (let i = 0, len = copyOrgs.length; i < len; i++) {
          // console.log(item, copyOrgs[i]);
          if (
            copyOrgs[i].org_id === item.organization_id ||
            copyOrgs[i].org_id === item.org_id
          ) {
            copyOrgs.splice(i, 1);
            copyChecked.splice(i, 1);
            break;
          }
        }
      }
    }
    this.setState({
      checked: copyChecked,
      orgs: copyOrgs,
    });
  }

  // 获取某组织下级组织列表处理
  fetchNextLevelOrg(item) {
    let { breadcrumb } = this.state;
    const { org_type, tree_type } = this.props;
    breadcrumb.push({
      org_id: item.org_id,
      org_name: item.name,
    });
    this.fetchOrganize({
      type: 1,
      org_id: item.org_id,
      org_type,
      fetchNext: true,
      tree_type,
      root: false,
    });
  }

  // 面包屑导航跳转控制
  breadcrumbGo(item, index) {
    const { org_type, tree_type } = this.props;
    // console.log("面包屑导航跳转", item);
    // return;
    this.fetchOrganize({
      type: 1,
      org_id: item.org_id,
      org_type,
      index,
      tree_type,
      root: index === 0,
    });
  }

  // 默认查询所有组织org_id=0，type=1查询组织树，type=2查询组织列表
  async fetchOrganize({
    type = 1,
    org_id = 0,
    org_name = "",
    org_type = -1,
    fetchNext = false,
    index = 0,
    tree_type = 2,
    root,
  }) {
    this.setState({
      loadingModalVisible: true,
    });
    // console.log(tree_type);
    // 之前暂存的面包屑状态
    let { breadcrumb, selectorType } = this.state;
    const {
      updateState,
      hasRoot,
      isFfgr,
      rootOrgId,
      rootOrgType,
      isLeaderGroup,
      partyMasses,
    } = this.props;
    const orgId =
      JSON.parse(sessionStorage.getItem("userInfo")).root_oid || null; //只是挂靠党组织传顶级组织

    if (!fetchNext) {
      if (selectorType !== type) {
        breadcrumb = [];
      } else {
        breadcrumb = breadcrumb.slice(0, index === 0 ? index : index + 1);
      }
    }
    let response;
    // || !org_name
    let queryparams = {
      tree_type,
    };
    // console.log(type, queryparams, root);
    if (type === 1) {
      queryparams = {
        ...queryparams,
        org_type,
        org_id: partyMasses && root ? orgId : org_id,
      };
      if (root) {
        queryparams.load_root = 1;
      }
      response = await fetchOrganisitionTree(queryparams);
    } else {
      // 调用获取组织列表接口
      response = await findByName({
        org_id,
        tree_type,
        org_name,
        org_type: 0,
        // 分页，此处查询结果不分页
        is_page: 0,
      });
    }
    this.setState({
      loadingModalVisible: false,
    });
    const { data: body } = response;
    const { code, data, message } = body;
    // console.log(data);
    if (code !== 0) {
      Message.error(message);
      return;
    }
    // 如果是树形列表，将根节点信息放入面包屑导航，并且把数据的children作为list展示
    let result = [];
    // console.log("root关系到第一次加载的结果", root);
    if (root) {
      if (!hasRoot &&
        type === 1 &&
        queryparams.load_root === 1 &&
        data[0] &&
        (!rootOrgId || !rootOrgType || !isLeaderGroup)
      ) {
        breadcrumb.push({
          org_id: data[0].organization_id || data[0].org_id,
          org_name: data[0].name,
        });
        result = data[0].children || [];
      } else {
        result = data;
      }
    } else {
      result = data;
    }
    console.log(result);

    this.setState(
      {
        // expanded,
        breadcrumb,
        selectorData: result,
        selectorType: type,
      },
      () => {
        this.setState({
          isCheckAll: this.isAllChecked(),
        });
      }
    );
  }
  renderCheckbox(data, showNextLevel) {
    console.log(data)
    const { disabledOrgList, checked } = this.state;
    const { hasRoot, rootOrgId, onlyDirectSub } = this.props;
    return data.map((item, index) => {
      // console.log(hasRoot, rootOrgId, onlyDirectSub, item);
      // 当前是否选中需要
      return (
        <div className="org-item-wrapper" title={item.name} key={index}>
          <div
            className={
              showNextLevel && item.child_org_num !== 0
                ? "org-item-name"
                : "org-item-name indent-right"
            }
          >
            <div className="checkbox-wrapper">
              <Checkbox
                disabled={
                  disabledOrgList.findIndex((org) => {
                    return org.org_id === item.org_id;
                  }) !== -1
                }
                checked={
                  checked.indexOf(item.organization_id) !== -1 ||
                  checked.indexOf(item.org_id) !== -1
                }
                onChange={(e) => {
                  const isCheck = e.target.checked;
                  this.listCheckHandler(item, isCheck);
                }}
              >
                {item.name}
              </Checkbox>
            </div>
          </div>
          {((showNextLevel && item.child_org_num !== 0) ||
            (hasRoot && onlyDirectSub && rootOrgId == item.org_id)) && (
              <div
                className="org-item-next"
                onClick={() => this.fetchNextLevelOrg(item)}
              >
                <span>下级</span>
                <SelfIcon type="right" />
              </div>
            )}
        </div>
      );
    });
  }
  renderRadio(data, showNextLevel) {
    const { disabledOrgList } = this.state;
    const { hasRoot, rootOrgId, onlyDirectSub } = this.props;
    return (
      <RadioGroup
        onChange={(e) => {
          const { selectorData } = this.state;
          const _orgs = [];
          const val = e.target.value;
          const find = selectorData.find((d) => val === d.org_id);
          _orgs.push({
            org_name: find.name,
            org_id: find.organization_id || find.org_id,
          });
          this.setState({
            radio: e.target.value,
            orgs: _orgs,
          });
        }}
        value={this.state.radio}
      >
        {data.map((item, index) => {
          return (
            <div className="org-item-wrapper" title={item.name} key={index}>
              <div
                className={
                  showNextLevel && item.child_org_num !== 0
                    ? "org-item-name"
                    : "org-item-name indent-right"
                }
              >
                <div className="checkbox-wrapper">
                  <Radio
                    disabled={
                      disabledOrgList.findIndex((org) => {
                        return org.org_id === item.org_id;
                      }) !== -1
                    }
                    style={{ width: 300, whiteSpace: "pre-wrap" }}
                    value={item.organization_id || item.org_id}
                  >
                    {item.name}
                  </Radio>
                </div>
              </div>
              {((showNextLevel && item.child_org_num !== 0) ||
                (hasRoot && onlyDirectSub && rootOrgId == item.org_id)) && (
                  <div
                    className="org-item-next"
                    onClick={() => this.fetchNextLevelOrg(item)}
                  >
                    <span>下级</span>
                    <SelfIcon type="right" />
                  </div>
                )}
            </div>
          );
        })}
      </RadioGroup>
    );
  }
  render() {
    const {
      selectorData,
      selectorType,
      orgs,
      breadcrumb,
      isCheckAll,
      disabledOrgList,
      loadingModalVisible,
    } = this.state;
    const {
      radio,
      checkAll,
      onlyDirectSub = false,
      isLeaderGroup = false,
      hasRoot = false,
    } = this.props;
    console.log("🚀 ~ OrganizeList ~ render ~ checkAll:", checkAll);
    const renderList = (data, showNextLevel = true) => {
      if (data && Array.isArray(data) && data.length !== 0) {
        return (
          <div>
            {checkAll && !radio ? (
              <div className="org-item-wrapper" title={"全选"}>
                <div className="org-item-name">
                  <div className="checkbox-wrapper">
                    <Checkbox
                      checked={isCheckAll}
                      onChange={(e) => {
                        const isCheck = e.target.checked;
                        this.checkAllHandler(isCheck);
                      }}
                    >
                      全选
                    </Checkbox>
                  </div>
                </div>
              </div>
            ) : null}
            {!radio
              ? this.renderCheckbox(data, showNextLevel)
              : this.renderRadio(data, showNextLevel)}
          </div>
        );
      } else {
        return <div>暂无数据</div>;
      }
    };
    const renderBreadcrumb = (data) => {
      const { rootOrgId, hasRoot, onlyDirectSub, breadcrumbClick } = this.props;
      console.log(
        "----------------------",
        rootOrgId,
        hasRoot,
        onlyDirectSub,
        data
      );
      return (
        <div className="breadcrumb-wrapper">
          {data &&
            data.map((item, index) => {
              return (
                <div className="breadcrumb-item" key={index}>
                  {index > 0 && <SelfIcon type="right" />}
                  {(index < data.length - 1 ||
                    (breadcrumbClick || (hasRoot && onlyDirectSub && rootOrgId == item.org_id))) && (
                      <a
                        onClick={() => {
                          this.breadcrumbGo(item, index);
                        }}
                      >
                        {item.org_name}
                      </a>
                    )}
                  {!breadcrumbClick && (index >= data.length - 1 &&
                    !(hasRoot && onlyDirectSub && rootOrgId == item.org_id)) &&
                    item.org_name}
                </div>
              );
            })}
        </div>
      );
    };
    return (
      <div className="organize-list">
        <LoadingModal modalVisible={loadingModalVisible} />
        <div className="selector-wrapper">
          {/* 左侧选择器 */}
          {selectorType === 1 && <div>{renderBreadcrumb(breadcrumb)}</div>}
          <div className="selector-list">
            {selectorType === 1
              ? renderList(selectorData, !onlyDirectSub)
              : renderList(selectorData, false)}
          </div>
        </div>
        <div className="selected-list">
          <div className="count">已选 ({orgs.length})</div>
          {orgs &&
            Array.isArray(orgs) &&
            orgs.length !== 0 &&
            orgs.map((org, index) => {
              return (
                <div key={index} className="org-tag">
                  <div className="tag-text">{org.org_name}</div>
                  {disabledOrgList.findIndex((item) => {
                    return org.org_id === item.org_id;
                  }) === -1 && (
                      <div
                        className="tag-close"
                        onClick={() => {
                          this.removeOrg(index, org);
                        }}
                      >
                        <SelfIcon type="close" />
                      </div>
                    )}
                </div>
              );
            })}
        </div>
      </div>
    );
  }
}

OrganizeList.propTypes = {
  dataSource: propTypes.array,
  radio: propTypes.bool,
  disabledOrgList: propTypes.array,
  onlyDirectSub: propTypes.bool,
  // 是否是领导班子
  isLeaderGroup: propTypes.bool,
  // 查询组织结果是否包含根节点，当前登录组织为根节点
  hasRoot: propTypes.bool,
};

OrganizeList.defaultTypes = {
  dataSource: [],
  radio: false,
  disabledOrgList: [],
  onlyDirectSub: false,
  isLeaderGroup: false,
  hasRoot: false,
};

export default OrganizeList;
