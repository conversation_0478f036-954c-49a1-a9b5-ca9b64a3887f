.position-content {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 450px;
  .pc-job-name {
    width: 100%;
    margin: 7px 0px 10px;
    // height: 48px;
    font-size: 16px;
    font-family: Source <PERSON>, Source <PERSON>N;
    font-weight: 400;
    color: rgba(80, 80, 80, 0.85);
  }
  .check-box-job {
    display: flex;
    align-items: center;
    .remark {
      color: #0c0c0c;
    }
  }
  .pc-main-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f7f7f7;
    padding: 10px 10px 10px 10px;
    .select-box {
      flex: 1;
      overflow: auto;
    }
    .check-box {
      display: flex;
      padding: 0px 0px;
      gap: 12px 11px;
      display: flex;
      flex-wrap: wrap;
    }
    .tree-list {
      display: flex;
      flex-direction: column;
      width: 100%;
      .tree-item {
        width: 100%;
        .tree-header {
          width: 100%;
          display: flex;
          align-items: center;
          .left-box {
            display: flex;
            align-items: center;
            &:hover {
              .org-name {
                color: #008eff !important;
              }
            }
            .org-name {
              cursor: pointer;
              font-size: 16px;
              line-height: 16px;
              font-family: Source Han Sans CN, Source Han Sans CN;
              font-weight: 400;
              color: #000000;
              transition: all 0.1s ease-in-out;
            }
            .org-name-active {
              color: #008eff !important;
            }
          }
          .right-box {
            padding-right: 20px;
            flex: 1 0;
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
    .m-top-18 {
      margin-top: 18px;
    }
  }
}
.search-box-change {
  padding: 0px;
  background-color: #ccc;
  .ant-input-affix-wrapper {
    height: 48px;
  }
  .search-icon {
    width: 18px;
    height: 18px;
    background: url(../images/search.png) center / cover no-repeat;
  }
  .search-input {
    input {
      padding-left: 35px !important;
      border: none;
    }
  }
  input {
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .search-text {
    padding-left: 20px;
    font-size: 16px;
    line-height: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    color: #008eff;
    cursor: pointer;
    border-left: 1px solid #d9d9d9;
  }
}

.sub-org-icon {
  margin: 0px 5px;
  width: 25px;
  height: 23px;
}
.short-name {
  display: inline-block;
  width: 90%;
  font-size: 16px;

  span.name {
    display: inline-block;
    width: 80%;
  }
}
.sub-org-list {
  width: 100%;
  .sub-item {
    display: flex;
    padding: 0px 20px;
    width: 100%;
    display: flex;
    overflow: hidden;
    .position-box {
      flex: 1;
      display: flex;
      // cursor: pointer;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      overflow: hidden;
      .pos {
        padding-right: 20px;
        flex-grow: 0;
        width: 85%;
        text-overflow: ellipsis;
        overflow: hidden;
        line-clamp: 1;
        white-space: nowrap;
      }
      .name {
        white-space: nowrap;
      }
    }
    .position_box_active {
      .pos {
        color: #008eff !important;
      }
      .name {
        color: #008eff !important;
      }
    }
  }
}
