.edu-info {
  padding: 0px 20px;
  .edu-card {
    .title {
      display: flex;
      align-items: center;
      font-size: 20px;
      &::before {
        content: "";
        margin-right: 10px;
        display: inline-block;
        width: 6px;
        height: 20px;
        background: #f46e65;
      }
    }
  }
  .form-row {
    display: flex;
  }
  .gap {
    display: flex;
    flex-direction: column;
    gap: 10px 0px;
  }
  .form-col {
    flex-direction: column;
    display: flex;
    justify-content: flex-start;
  }
  .form-content {
    margin-top: 5px;
    height: 100%;
  }
  .form-driver {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 0;
  }
  .btn-box {
    margin: 10px 0px 10px;
    padding-left: 5px;
  }
  .margin-right-10 {
    margin-right: 10px;
  }
  .operation {
  }
  .button-box {
    display: flex;
    align-items: center;
    svg {
      margin: 25px 0px 0px 10px;
      cursor: pointer;
    }
  }
}
