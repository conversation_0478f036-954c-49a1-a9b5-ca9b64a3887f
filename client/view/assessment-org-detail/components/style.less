.header-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  font-size: 18px;
  font-weight: bold;
  background: rgba(247, 248, 249, 1);
  border-radius: 5px 5px 0px 0px;
  .btn-sty {
    border: 0px;
    font-size: 18px;
    padding: 0;
    margin: 0;
    position: relative;
    background: #f7f8f9;
  }
  .v-line {
    position: relative;
    top: -0.06em;
    display: inline-block;
    width: 1px;
    height: 1.5em;
    margin: 0 8px;
    vertical-align: middle;
    background: rgba(233, 233, 233, 1);
  }
  h3 {
    margin: 0;
    padding: 0;
  }
}

.top-form-container {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e4e4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .query-btn {
    width: 90px;
    height: 36px;
    line-height: 36px;
    margin-left: 10px;
  }
}

.detail-modal-container {
  .base-info-container {
    margin-bottom: 10px;
    .item-container {
      margin-bottom: 25px;
      display: flex;
    }
    .item-label {
      width: 80px;
      text-align: right;
      flex-shrink: 0;
      &::after {
        content: ":";
        margin: 0 5px;
      }
    }
    .item-content {
      flex-grow: 1; // border: 1px solid red;
      white-space: wrap;
      .content-wrapper {
        width: 100%;
      }
    }
    .item-timeline{
      margin-top: 20px;
      font-family: MicrosoftYaHei;
      .timeline-title{
        position: relative;
        padding-left: 15px;
        color: #F46E65;
        &::before{
          content: '';
          position: absolute;
          top:3px;
          left:0;
          width:3px;
          height:14px;
          background:rgba(244,110,101,1);
          border-radius:2px;
        }
        &::after{
          content: '';
          position: absolute;
          top: 10px;
          margin-left:20px; 
          width: 610px;
          border-bottom: 2px dotted rgb(221, 221, 221);
        }
      }
      .timeline-score{
        margin: 20px 0;
        width:500px;
        font-size:16px;
        font-weight:bold;
        color:rgba(51,51,51,1);
        .score{
          color: #F46E65;
          margin-left: 5px;
        }
      }
      .ant-timeline-item-head-blue {
        background: #999999;
        border-color: #999999;
      }
      .plusScore,.reduceScore{
        margin-left: 15px;
        font-size:16px;
        font-family:MicrosoftYaHei;
        font-weight:400;
      }
      .plusScore{
        color:rgba(143,195,31,1);
      }
      .reduceScore{
        color:#F46E65
      }
      .ant-timeline-item{
        height: 85px;
      }
      .execute-task-detail-select-panel-icon {
        color: #F46E65;
      }
      .execute-task-detail-select-panel-avater {
        float: left;
        font-size: 36px;
        width: 32px;
        height: 40px;
        margin: 5px 10px 0 0;
      }
      .execute-task-detail-select-panel-info {
        margin-left: 42px;
        .info {
          height: 30px;
          overflow: hidden;
          color: rgba(0, 0, 0, 0.65);
        }
        .size {
          line-height: 20px;
          height: 20px;
          overflow: hidden;
          color: #999;
        }
        .info-preview a{
          text-decoration:underline
        }
      }
    }
  }
  .eval-task-view-box-container {
    // padding-top: 30px;
    border-top: 2px dotted #ddd;
    .task-view-box {
      .record-timeline-panel .ant-timeline-item-tail {
        // left: 92px;
      }
      dt.label {
        width: 80px;
        flex: 0 0 80px;
      }
    }
  }
}