import React, { Component } from "react";
import { Form, Icon, Button, Tooltip, Input, Upload, Radio } from "antd";
import SiderContent from "components/sider-content";
import TextRemain from "components/text-remain";
import LoadingImage from "components/loading-image";
import { CDN } from "apis/config";
import Editor from "client/components/RichTextEditor";

export default ({
  collapsed,
  isHideWrap,
  onClose,
  modalTitle,
  submitHandler,
  uploadFileUrl,
  form,
  fileList,
  changeHandler,
  //移除已经上传的图片
  removeHandler,
  //手动上传文件
  // customRequest,
  //上传文件时的回调
  beforeUpload,
  onStart,
  onSuccess,
  onError,
  // BraftEditor,
  // BraftEditorLoading,
  _this,
  onEditorInput,
  //表单的初始值
  name,
  onChangeDescription,
}) => {
  const siderContentProps = {
      collapsed,
      onClose,
      title: modalTitle,
      isHideWrap,
    },
    formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
    };
  const editorProps = {
    height: 300,
    contentFormat: "html",
    placeholder: "请输入详情",
  };
  const FormItem = Form.Item,
    RadioGroup = Radio.Group;
  const { getFieldDecorator } = form,
    uploadButton = (
      <div>
        <Icon type="plus" />
        <div className="ant-upload-text">上传图片</div>
      </div>
    );
  return (
    <SiderContent {...siderContentProps}>
      <div style={{ paddingTop: "20px" }}>
        <Form className="form" onSubmit={submitHandler}>
          <FormItem {...formItemLayout} label="奖品名称">
            <Tooltip
              title={() => (
                <TextRemain maxSize={60} value={form.getFieldValue("name")} />
              )}
              trigger={"focus"}
              overlayClassName={"text-remain-tooltip"}
            >
              {getFieldDecorator("name", {
                initialValue: name,
                rules: [
                  { required: true, message: "输入奖品名称" },
                  { max: 60, message: "奖品名称长度超过60字" },
                ],
              })(
                <Input
                  style={{ width: "100%" }}
                  placeholder="奖品名称"
                  // suffix={<span style={{color: '#999'}}>还可以输入<span style={{color: '#8FC31F'}}>70</span>个汉字</span>}
                />
              )}
            </Tooltip>
          </FormItem>
          <FormItem {...formItemLayout} label="奖品组图">
            {getFieldDecorator("images", {
              initialValue: fileList,
              valuePropName: "filelist",
            })(
              <div>
                {/* {
                                    JSON.stringify(removeHandler)
                                } */}
                <div className={"upload-wrapper"}>
                  {fileList.map((file, index) => {
                    return (
                      <LoadingImage
                        key={file.uid}
                        url={`${CDN}/${file.url}` || ""}
                        index={index}
                        removeHandler={removeHandler}
                      />
                    );
                  })}
                  <Upload
                    name={"upfile"}
                    action={uploadFileUrl}
                    listType="picture-card"
                    accept={"image/*"}
                    defaultFileList={[]}
                    fileList={fileList}
                    supportServerRender={true}
                    showUploadList={false}
                    onChange={changeHandler}
                    // onRemove={removeHandler}
                    headers={{
                      "x-csrf-token":
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("csrf") || ""
                          : "",
                      "Access-Control-Allow-Credentials": "true",
                      _tk:
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("_tk") || "-1"
                          : "-1",
                      _uid:
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("_uid") || "1"
                          : "1",
                      _un:
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("_un") || "1"
                          : "1",
                      _type:
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("_type") || "2"
                          : "2",
                      _oid:
                        typeof window !== "undefined"
                          ? window.sessionStorage.getItem("_type") || "1"
                          : "1",
                    }}
                    // customRequest={customRequest}
                    beforeUpload={beforeUpload}
                    onStart={onStart}
                    onSuccess={onSuccess}
                    onError={onError}
                    // 可以多选上传
                    // multiple={true}
                    data={{
                      upType: "prize-image",
                    }}
                  >
                    {fileList.length >= 5 ? null : uploadButton}
                  </Upload>
                </div>
                <div>
                  最多上传5张图片，第一张图为奖品列表图（上传图片要求，1000x1000px、小于800kb、jpg或png）
                </div>
              </div>
            )}
          </FormItem>
          {/*<FormItem*/}
          {/*{...formItemLayout}*/}
          {/*label="奖品描述"*/}
          {/*>*/}
          {/*{getFieldDecorator('manageScope', {*/}
          {/*initialValue: 0*/}
          {/*})(*/}
          {/*<RadioGroup>*/}
          {/*<Radio value={0}>网站和移动端使用相同的内容</Radio>*/}
          {/*<Radio value={1}>网站和移动端使用不同的内容</Radio>*/}
          {/*</RadioGroup>*/}
          {/*)}*/}
          {/*</FormItem>*/}
          <FormItem
            {...formItemLayout}
            //隐藏网站和移动端使用不同功能的内容
            // wrapperCol={form.getFieldValue('manageScope') === 1 ? {span: 18} : {offset: 4, span: 18}}
            // label={form.getFieldValue('manageScope') === 1 ? '网站版' : ''}
            label={"奖品描述"}
            wrapperCol={{ span: 18 }}
          >
            <div id="description" className="editor-wrap">
              <Editor
                // 新增属性cdn,标记图片是否使用cdn地址，默认为false
                /* cdn
                defaultValue="" */
                onRef={(ref) => {
                  _this.description_editor = ref;
                }}
                height={460}
                mode={"simple"}
                // onInput={(e) => onEditorInput(e, _this.description_editor)}
              />
            </div>
            {/* {
                            BraftEditorLoading ? <div id="description" className="editor-wrap">
                                {getFieldDecorator('description', {
                                    initialValue: ''
                                })(
                                    <BraftEditor
                                        {...editorProps}
                                        ref={instance => _this.editorInstance = instance}
                                        viewWrapper="#description"
                                    />
                                )}
                            </div> : null
                        } */}
          </FormItem>
          <FormItem></FormItem>
          <FormItem
            style={
              form.getFieldValue("manageScope") === 1 ? {} : { display: "none" }
            }
            {...formItemLayout}
            wrapperCol={
              form.getFieldValue("manageScope") === 1
                ? { span: 18 }
                : { offset: 4, span: 18 }
            }
            label={form.getFieldValue("manageScope") === 1 ? "移动端" : ""}
          >
            <div id="description_app" className="editor-wrap">
              <Editor
                // 新增属性cdn,标记图片是否使用cdn地址，默认为false
                /* cdn
                defaultValue=""*/
                onRef={(ref) => {
                  _this.description_app_editor = ref;
                }}
                // value={descriptionAppContent}
                mode={"simple"}
                height={460}
                // onInput={(e) => onEditorInput(e, _this.description_app_editor)}
              />
            </div>
            {/* {
                            BraftEditorLoading ? <div id="description_app" className="editor-wrap">
                                {getFieldDecorator('description_app', {
                                    initialValue: ''
                                })(
                                    <BraftEditor
                                        {...editorProps}
                                        ref={instance => _this.editorInstance_app = instance}
                                        viewWrapper="#description_app"
                                    />
                                )}
                            </div> : null
                        } */}
          </FormItem>

          <FormItem {...formItemLayout} wrapperCol={{ offset: 4, span: 18 }}>
            <div className="buttons">
              <Button type="primary" size="large" htmlType="submit">
                提交
              </Button>
              <Button
                size="large"
                style={{ marginLeft: "20px" }}
                onClick={onClose}
              >
                取消
              </Button>
            </div>
          </FormItem>
        </Form>
      </div>
    </SiderContent>
  );
};
