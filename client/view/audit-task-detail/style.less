.audit-task-detail-main{
  .audit-task-detail-content{
    padding: 15px;
    .audit-task-detail-row{
      padding: 10px 0;
      .audit-task-detail-label{
        text-align:right;
        width: 100px;
        color: rgb(112, 112, 112);
        .label-validator:before {
          display: inline-block;
          margin-right: 4px;
          content: "*";
          font-family: SimSun;
          line-height: 1;
          font-size: 14px;
          color: #f5222d;
        }
      }
    }
    .audit-task-detail-list{
      padding: 0 15px;

      .audit-task-detail-file-item{
        height: 55px;
      }
    }

    .audit-task-detail-file-list:after {
      content: ".";
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }
    .audit-task-detail-file-list {
      display:inline-block;
    }
    .audit-task-detail-file-list .left {
      float: left;
      width: 40px;
    }
    .audit-task-detail-file-list .left .file-icon {
      font-size: 35px;
    }
    .audit-task-detail-file-list .right {
      float: right;
      display:inline-block;
      text-align: right;
    }
    .audit-task-detail-file-list .content {
      margin-left: 40px;
      margin-right: 65px;
    }
    .audit-task-detail-file-list .content p {
      margin: 0;
    }
    .audit-task-detail-file-list .content .size {
      color: #999;
    }
    .audit-task-detail_gray{
      margin-top: -10px;
      padding:10px;
      border-radius: 3px;
      background-color: rgb(247, 247, 247)
    }

  }
  .tab-search{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
    // width: 100%;
    .tab-search_el{
      margin-right: 30px;
      width: 250px;
    }
    .tab-select{
      position: absolute;
      left: 45px;
    }
  }
}

.task-modal{
  .task-view-box{padding-top:0;}
  .task-modal-row {
    .label {
      text-align: right;
      &.formItemLabel {
        margin-top: 9px;
      }

      &.label-validator:before {
        display: inline-block;
        margin-right: 4px;
        content: "*";
        font-family: SimSun;
        line-height: 1;
        font-size: 14px;
        color: #f5222d;
      }
    }
  }
}

.eval-task-viewbox-titleTips {
  .ant-tooltip-arrow {
    border-right-color: #FFF !important;
    border-bottom-color: #FFF !important;
    border-top-color: #FFF !important;
  }
  .ant-tooltip-inner {
    background-color: #FFF;
    span {
      color: #999;
      font-size: 13px;
      span {
        color: #8FC31F;
      }
    }
  }
}