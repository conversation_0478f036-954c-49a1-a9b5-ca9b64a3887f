import { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON>,
  But<PERSON>,
  Popconfirm,
  message,
  Form,
  Input,
  Select,
  Spin,
  Row,
  Col,
  Icon,
  Radio
} from "antd";
import moment from "moment";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import InputTips from "components/activity-form/sub-components/InputTips";
// import FileManagement from "components/File/FileManagement";
// import OrganizeSelector from "components/Selector/OrganizeSelector";
// import http from "tools/axios";
import ShowUploadFileType from "components/show-upload-file-type";
import FileUpload from "components/file-upload";
import { CDN } from "apis/config";
import {
  addOrganization,
  UpdateOrganization,
  OrganizationalDetails,
} from "apis/organize";
import { getcodeList } from "apis/users";
import { getArea, getNativeArea } from "client/apis/organize";

const { Option } = Select;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};
const layout2 = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};
let initKey = 1;

const drawerComn = (props) => {
  const {
    operationType,
    open,
    setOpen,
    parentId,
    findServiceOrgListFn,
    orgInfo = {},
    setOrgInfo,
    form,
  } = props;
  const {
    getFieldDecorator,
    getFieldValue,
    validateFieldsAndScroll,
    setFieldsValue,
  } = form;
  const [orgDetails, setOrgDetails] = useState({});
  const [loading, setLoading] = useState(false);
  const [nativePlace, setNativePlace] = useState([]);
  const [politicalOutlookList, setPoliticalOutlookList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [orgData, setOrgData] = useState({});
  const [validateStatus, setValidateStatus] = useState("validating");
  const [dataSource, setDataSource] = useState({});
  const [keys, setKeys] = useState([0]);
  const org_docs = getFieldValue("org_docs");

  useEffect(() => {
    getFieldDecorator("keys", { initialValue: [0] });
    getAreaFn();
    getcodeListFn();
  }, []);

  useEffect(() => {
    if (orgInfo.org_id && open) {
      setLoading(true);
      OrganizationalDetails({ org_id: orgInfo.org_id }).then(
        ({ data: res }) => {
          setLoading(false);
          if (res.code === 0) {
            const {
              data: {
                org_create_time,
                org_area: orgArea,
                name,
                affiliation_org_id,
                affiliation_org_name,
                org_docs = [],
                open_time,
                contacts,
                ...other
              },
            } = res;
            // console.log(orgArea, '打印1');
            const org_area = orgArea.split(",");
            let user_name = [];
            let phone = [];
            let seq = [];
            let org_contacts_id = [];
            if (contacts) {
              contacts.forEach((item) => {
                user_name.push(item.user_name);
                phone.push(item.phone);
                seq.push(item.seq);
                org_contacts_id.push(item.org_contacts_id);
              });
            }
            // console.log(org_docs, '打印2');
            const fieldData = {
              ...other,
              user_name,
              phone,
              seq,
              org_contacts_id,
              org_area,
              org_name: name,
              org_create_time: moment(org_create_time),
              open_time: open_time,
              // org_docs: org_docs.split(',').length > 0 ? JSON.parse(org_docs) : [],
              org_docs: org_docs.length > 0 && org_docs.split(',').length > 0 ? org_docs.split(',') : [],
              // org_docs: org_docs ? org_docs.split(',') : [],
            };
            setOrgData({
              org_id: affiliation_org_id,
              org_name: affiliation_org_name,
            });
            setNativePlaceData(org_area);
            form.setFieldsValue(fieldData);
            setDataSource(fieldData);
            setKeys(contacts.map((_, index) => index));
          }
        }
      );
    }
  }, [open]);

  const getcodeListFn = async () => {
    const {
      data: { code, data },
    } = await getcodeList({ code: 1013 });
    code === 0 && setPoliticalOutlookList(data);
  };

  const loadDataFn = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (targetOption.children && targetOption.children.length) {
      return;
    }
    targetOption.loading = true;
    const {
      data: { code, data },
    } = await getNativeArea({
      parentid: targetOption.value,
    });
    if (code === 0 && data) {
      targetOption.loading = false;
      targetOption.children = data.map((item) => {
        item.label = item.area_name;
        item.value = String(item.adcode);
        item.isLeaf = item.type < 4 ? false : true;
        return item;
      });
      setNativePlace([...nativePlace]);
    }
  };

  const getAreaFn = (pid = null) => {
    getArea({ pid })
      .then((res) => {
        const { data } = res;
        if (data.code === 0) {
          const area = data.data.map((item) => {
            return {
              label: item.area_name,
              value: item.adcode,
              isLeaf: false,
            };
          });
          setNativePlace(area);
        } else {
          return Promise.reject(data);
        }
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const setNativePlaceData = async (areaCodes) => {
    const navP = nativePlace;
    const getAreaData = async (key) => {
      const { data: res } = await getNativeArea({
        parentid: areaCodes[key],
      });
      if (res.code !== 0) {
        return;
      }
      let childrenData = res.data.map((item) => {
        item.label = item.area_name;
        item.value = String(item.adcode);
        item.isLeaf = item.type < 4 ? false : true;
        return item;
      });
      if (key < areaCodes.length - 1) {
        const index = key + 1;
        childrenData.forEach(async (item) => {
          if (String(item.value) === String(areaCodes[index])) {
            item.children = await getAreaData(index);
          }
        });
      }
      return childrenData;
    };
    navP.forEach(async (item) => {
      if (String(item.value) === String(areaCodes[0]) && !item.children) {
        item.children = await getAreaData(0);
      }
    });
    setTimeout(() => {
      setNativePlace(() => [...navP]);
    }, 500);
  };

  const onSubmitFn = async (e) => {
    e.preventDefault();
    validateFieldsAndScroll(async (err, values) => {
      console.log(err, values);
      setValidateStatus(!orgData.org_id ? "error" : "validating");
      const img = []
      if (values.org_docs) {
        values.org_docs.map((item) => {
          img.push(item.path)
        })
      }
      console.log(values.org_docs, img, '提交数据');

      if (err || !orgData.org_id) {
        return;
      }
      setLoading(true);
      const {
        phone,
        seq,
        user_name,
        org_contacts_id,
        org_docs,
        introduction,
        charge_political,
        charge_phone,
        charge,
        org_area,
        org_address,
        org_create_time,
        open_time,
        org_type,
        org_name,
        ...other
      } = values;
      const params = {
        ...other,
        has_approval: 0,
        organization_id: dataSource.org_id,
        parent_id: parentId,
        org_name: org_name || orgDetails.org_name,
        org_type: org_type || orgDetails.org_type,
        affiliation_org_id: orgData.org_id || orgDetails.affiliation_org_id,
        affiliation_org_name:
          orgData.org_name || orgDetails.affiliation_org_name,
        org_create_time: org_create_time
          ? moment(org_create_time).format("YYYY-MM-DD")
          : orgDetails.org_create_time,
        open_time: open_time || orgDetails.open_time,
        org_area: org_area ? org_area.join(",") : orgDetails.org_area,
        org_address: org_address || orgDetails.org_address,
        charge: charge || orgDetails.charge,
        charge_phone: charge_phone || orgDetails.charge_phone,
        charge_political: charge_political || orgDetails.charge_political,
        // introduction: introduction || orgDetails.introduction,
        introduction: introduction,
        contacts: user_name.map((user_name, key) => ({
          user_name,
          phone: phone[key],
          seq: seq[key],
          org_contacts_id: org_contacts_id[key],
        })),
        org_docs: img.join(','),
      };
      // setLoading(true);
      const reqApi = operationType === 2 ? UpdateOrganization : addOrganization;
      const { data: res } = await reqApi(params);
      if (res.code !== 0) {
        setLoading(false);
        return message.error(res.message);
      }

      message.success(operationType === 2 ? "编辑成功" : "新增成功");
      setLoading(false);
      setOpen(false);
      setOrgInfo({});
      setOrgData({});
      setKeys([0]);
      setDataSource({});
      findServiceOrgListFn();
    });
  };

  const organizeModalProps = {
    radio: true,
    visible,
    dataSource: [orgData],
    hideModal: () => {
      setVisible(false);
    },
    loadOrganizeData: (data) => {
      setOrgData(data[0]);
      setVisible(false);
      setValidateStatus("validating");
    },
    partyMasses: true//是否是党群服务中心，显示挂靠组织
    /*  ref: (ref) => {
       this.organizeModal = ref;
     } */
  };

  const add = () => {
    const nextKeys = keys.concat(initKey++);
    setKeys(nextKeys);
  };
  const remove = (k) => {
    setKeys(keys.filter((key) => key !== k));
  };

  const disabled = operationType === 3;
  const formItems = keys.map((k, index) => (
    <Row key={k}>
      <Col span={9} offset={2}>
        <Form.Item label="联系人" {...layout2}>
          {getFieldDecorator(
            `user_name[${k}]`,
            {
              initialValue: dataSource.user_name && dataSource.user_name[k],
              rules: [
                {
                  required: true,
                  message: "请填写联系人",
                },
              ],
            },
            {
              rules: [
                {
                  required: true,
                  message: "请填写联系人",
                },
              ],
            }
          )(<Input disabled={disabled} placeholder="请填写联系人" />)}
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="手机号" {...layout2}>
          {getFieldDecorator(
            `phone[${k}]`,
            {
              initialValue: dataSource.phone && dataSource.phone[k],
              rules: [
                {
                  required: true,
                  message: "请填写联系人",
                },
              ],
            },
            {
              rules: [
                {
                  required: true,
                  message: "请输入手机号",
                },
                {
                  validator: (rule, value, callback) => {
                    const phoneReg = /^[1][3-9][0-9]{9}$/;
                    if (value && !phoneReg.test(value)) {
                      callback("请输入正确的手机号码");
                    } else {
                      callback();
                    }
                  },
                },
              ],
            }
          )(<Input disabled={disabled} placeholder="请输入手机号" />)}
        </Form.Item>
      </Col>
      <Col span={1}>
        {index !== 0 && (
          <Form.Item>
            <Icon type="close-circle" onClick={() => remove(k)} />
          </Form.Item>
        )}
      </Col>
      <Col>
        <Form.Item hidden>
          {getFieldDecorator(`org_contacts_id[${k}]`, {
            initialValue:
              dataSource.org_contacts_id && dataSource.org_contacts_id[k],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="权重" hidden>
          {getFieldDecorator(`seq[${k}]`, {
            initialValue: dataSource.seq && dataSource.seq[k],
          })(<Input />)}
        </Form.Item>
      </Col>
    </Row>
  ));

  return (
    <Drawer
      title={`${operationType === 1
        ? "新增党群服务中心"
        : (orgInfo.name || orgInfo.org_name || "") + "的资料"
        }`}
      placement="right"
      width="660px"
      className="PartyServiceCenter-drawer"
      getContainer={false}
      onClose={() => {
        if (!loading) {
          form.resetFields();
          setOrgInfo({});
          setOrgData({});
          setKeys([0]);
          setDataSource({});
          setOpen(false);
        }
      }}
      visible={open}
    >
      <Spin spinning={loading} size="large">
        <Form {...layout} onSubmit={onSubmitFn}>
          <Form.Item label="组织名称">
            {getFieldDecorator("org_name", {
              rules: [
                {
                  required: true,
                  message: "请填写组织名称",
                },
              ],
            })(<Input disabled={disabled} placeholder="请输入组织名称" />)}
          </Form.Item>
          <Form.Item label="组织简称">
            {getFieldDecorator("short_name")(
              <Input disabled={disabled} placeholder="请输入组织简称" />
            )}
          </Form.Item>
          <Form.Item label="组织类型">
            {getFieldDecorator("org_type", {
              initialValue: 102817,
              rules: [
                {
                  required: true,
                  message: "请选择组织类型",
                },
              ],
            })(
              <Select disabled={disabled} placeholder="请选择组织类型">
                <Option value={102817}>党群服务中心</Option>
              </Select>
            )}
          </Form.Item>
          <Form.Item label="是否完成民政局登记" {...layout2}>
            {getFieldDecorator('has_register', {
              initialValue: orgDetails.has_register,
              rules: [
                {
                  required: true,
                  message: "请填写是否完成民政局登记",
                },
              ],
            })(
              <Radio.Group disabled={disabled} >
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item
            label="挂靠党组织"
            required
            validateStatus={validateStatus}
            help={validateStatus === "error" && "请选择挂靠党组织"}
          >
            <span>{orgData.org_name || orgData.name}</span>
            <Button
              type="link"
              disabled={disabled}
              onClick={() => setVisible(true)}
            >
              选择组织
            </Button>
          </Form.Item>

          <Form.Item label="组织成立日期">
            {getFieldDecorator("org_create_time")(
              <DatePicker disabled={disabled} format={"YYYY-MM-DD"} />
            )}
          </Form.Item>
          <Form.Item label="所在地区">
            {getFieldDecorator("org_area", {
              rules: [
                {
                  required: true,
                  message: "请选择所在地区",
                },
              ],
            })(
              <Cascader
                disabled={disabled}
                allowClear
                options={nativePlace}
                loadData={loadDataFn}
                placeholder="请选择"
              />
            )}
          </Form.Item>
          <Form.Item label="详细地址">
            {getFieldDecorator("org_address", {
              rules: [
                {
                  required: true,
                  message: "请输入详细地址",
                },
              ],
            })(<Input disabled={disabled} placeholder="请输入详细地址" />)}
          </Form.Item>
          <Form.Item label="开放时间">
            <InputTips max={60} text={orgDetails.open_time}>
              {getFieldDecorator("open_time", {
                // initialValue: dataSource.open_time ? dataSource.open_time : "",
                rules: [
                  {
                    required: true,
                    max: 60,
                    message: "摘要最长60个字",
                  },
                ],
              })(<Input.TextArea disabled={disabled} placeholder="请输入开放时间" rows={4} />)}
            </InputTips>
            {/* {getFieldDecorator("open_time", {
              rules: [
                {
                  required: true,
                  message: "请输入开放时间",
                },
              ],
            })(<Input.TextArea placeholder="请输入开放时间" rows={4} />)} */}
          </Form.Item>
          <Row>
            <Col span={9} offset={2}>
              <Form.Item label="负责人" {...layout2}>
                {getFieldDecorator("charge", {
                  rules: [
                    {
                      required: true,
                      message: "请填写负责人",
                    },
                  ],
                })(<Input disabled={disabled} placeholder="请填写负责人" />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="手机号" {...layout2}>
                {getFieldDecorator("charge_phone", {
                  rules: [
                    {
                      required: true,
                      message: "请输入手机号",
                    },
                  ],
                })(<Input disabled={disabled} placeholder="请输入手机号" />)}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="负责人政治面貌">
            {getFieldDecorator("charge_political", {
              rules: [
                {
                  required: true,
                  message: "请选择负责人政治面貌",
                },
              ],
            })(
              <Select disabled={disabled} placeholder="请选择">
                {politicalOutlookList.map(({ op_key, op_value }) => (
                  <Option key={op_key} value={op_key}>
                    {op_value}
                  </Option>
                ))}
              </Select>
            )}
          </Form.Item>

          {formItems}
          <Row style={{ marginBottom: 30 }}>
            <Col offset={5}>
              <Button
                disabled={disabled}
                type="dashed"
                onClick={() => add()}
                block
                icon="plus"
                style={{ width: "250px" }}
              >
                添加联系人
              </Button>
            </Col>
          </Row>
          <Form.Item label="组织简介">
            {getFieldDecorator("introduction", {
              /* rules: [
                {
                  required: true,
                  message: '请填写组织简介',
                },
              ], */
            })(
              <Input.TextArea
                disabled={disabled}
                placeholder="请填写组织简介"
              />
            )}
          </Form.Item>

          <Form.Item label="照片">
            {/* <div className="tips">
              材料包括：1.经县民政局登记的法人证书；2.负责人身份证（正反面）；3.乡镇（街道）备案手续；4.村（社区）党组织书记承诺书
            </div> */}
            {getFieldDecorator("org_docs", {
              // rules: [
              //   {
              //     // required: true,
              //     message: '请上传证明材料',
              //   },
              // ],
            })(
              <FileUpload
                max={9}
                accept="image/jpeg,image/png"
                uploadedList={org_docs}
                disabled={disabled}
              />
            )}
            <ShowUploadFileType
              data={org_docs}
              updateState={(org_docs) => {
                setFieldsValue({
                  org_docs,
                });
              }}
              loading={true}
              isDelete={operationType !== 3}
              hiddenFileSize={true}
            />
          </Form.Item>
          {!disabled && (
            <Form.Item>
              <div style={{ textAlign: "center" }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  disabled={loading}
                  loading={loading}
                >
                  保存
                </Button>
              </div>
            </Form.Item>
          )}
        </Form>
      </Spin>
      <ShowUploadFileType />
      <OrganizeModal {...organizeModalProps} />
    </Drawer>
  );
};
export default Form.create()(drawerComn);
