import React, { Component } from 'react';
import IndexView from './index-view';
import './index.less'
import { connect } from 'dva';
import { Form } from 'antd/lib/index';
import PropTypes from 'prop-types';

const formValueName = {
    department: 'depId',//部门
    name: 'name'//姓名
}

class CenterCon extends Component {
    constructor() {
        super();
        this.state = {
            isOldSearch: null,
            checkedRows: [],
            checkedRowKeys: []
        }
    }

    async componentDidMount() {
        const { dispatch } = this.props;
        await dispatch({ type: 'centerContent/load', payload: { name: '', page: 1, depId: '' } });
        // await dispatch({type: 'centerContent/loadDep'});
    };

    //查询
    searchSubmit(e) {
        const { dispatch, form } = this.props,
            { validateFields } = form;
        e.preventDefault();
        validateFields(async (err, values) => {
            if (!err) {
                values.depId == -1 ? values.depId = '' : values.depId;
                this.setState({
                    isOldSearch: {
                        name: values.name,
                        depId: values.depId
                    }
                })
                await dispatch({
                    type: 'centerContent/load',
                    payload: { page: 1, name: values.name, depId: values.depId }
                });
            }
        })
    };

    //多选时触发的方法
    selected(record) {
        const { checkedRows, checkedRowKeys } = this.state;
        // console.log(checkedRows, checkedRowKeys, record);
        const index = checkedRowKeys.indexOf(record.user_id);
        if (index === -1) {
            checkedRows.push(record);
            checkedRowKeys.push(record.user_id);
        }
        this.setState({
            checkedRows,
            checkedRowKeys
        });

        const { dispatch, centerContent } = this.props,
            { dataList } = centerContent;
        dispatch({
            type: 'centerContent/save',
            payload: {
                selectChange: true
            }
        });
        const newData = [...dataList];
        const target = newData.filter(item => record.user_id === item.user_id && record.department_id === item.department_id)[0];
        if (target) {
            target.editable = true;
            // _this.setState({selectList:newData});
            dispatch({ type: 'centerContent/save', payload: { dataList: newData } });
        }
    };

    //多选时触发的方法
    cancelSelect(record) {
        const { checkedRows, checkedRowKeys } = this.state;
        const index = checkedRowKeys.indexOf(record.user_id);
        if (index !== -1) {
            checkedRows.splice(index, 1);
            checkedRowKeys.splice(index, 1);
            this.setState({
                checkedRows,
                checkedRowKeys
            });
        }

        const { dispatch, centerContent } = this.props,
            { dataList } = centerContent;
        dispatch({
            type: 'centerContent/save',
            payload: {
                selectChange: true
            }
        });
        const newData = [...dataList];
        const target = newData.filter(item => record.user_id === item.user_id && record.department_id === item.department_id)[0];
        if (target) {
            //Object.assign(target, _this.state.cacheData.filter(item => key === item.key)[0]);
            delete target.editable;
            //_this.setState({ selectList: newData });
            dispatch({ type: 'centerContent/save', payload: { dataList: newData } });
        }
    };

    //单选时触发的方法
    selected1(record) {
        const { checkedRows, checkedRowKeys } = this.state;
        // console.log(checkedRows, checkedRowKeys, record);
        const index = checkedRowKeys.indexOf(record.user_id);
        if (index === -1) {
            this.setState({
                checkedRows: [record],
                checkedRowKeys: [record.user_id]
            });
        }


        const { dispatch, centerContent } = this.props,
            { dataList } = centerContent;
        dispatch({
            type: 'centerContent/save',
            payload: {
                selectChange: true
            }
        });
        const newData = [...dataList];
        const target = newData.filter(item => record.user_id === item.user_id && record.department_id === item.department_id)[0];
        for (let i in newData) {
            if (newData[i].editable) {
                delete newData[i].editable;
                dispatch({ type: 'centerContent/save', payload: { dataList: newData } });
            }
        }
        if (target) {
            target.editable = true;
            // this.setState({
            //     selectList:newData
            // });
            dispatch({ type: 'centerContent/save', payload: { dataList: newData } });
        }
    };

    async changePage(page) {
        const { dispatch, form } = this.props,
            { validateFields, getFieldValue } = form;
        await dispatch({
            type: 'centerContent/setPage',
            payload: { pageNum: page.current }
        });

        if (this.state.isOldSearch) {
            await dispatch({
                type: 'centerContent/load',
                payload: { page: page.current, name: this.state.isOldSearch.name, depId: this.state.depId }
            });

        } else {
            await dispatch({
                type: 'centerContent/load',
                payload: { page: page.current, name: '', depId: '' }
            });
        }
    }

    handleCancel() {
        // console.log('关闭人员选择模态框');
        const { centerContent, dispatch, form } = this.props,
            { dataList } = centerContent;
        this.props.handleCancel();
        // 重置查询关键字
        this.setState({
            isOldSearch: null
        });
        dispatch({ type: 'centerContent/save', payload: { selectChange: false } });
        /*清除前次的选择*/
        const newData = [...dataList];
        for (let i in newData) {
            if (newData[i].editable) {
                delete newData[i].editable;
            }
        }
        dispatch({ type: 'centerContent/load', payload: { name: '', page: 1, depId: '' } });
        form.resetFields();
    }

    submitData() {
        const { centerContent, dispatch, form } = this.props,
            { dataList, selectChange } = centerContent;
        let outlist = [];
        for (let i in dataList) {
            if (dataList[i].editable) {
                outlist.push(dataList[i]);
            }
        }
        // 人员组件交互选择，记录选择先后并保存所有选择人员
        const { checkedRows, checkedRowKeys } = this.state;
        // console.log(checkedRows);
        this.props.handleOk(outlist, selectChange);
        dispatch({ type: 'centerContent/save', payload: { selectChange: false } });
        // console.log(outlist);
        /*清除前次的选择*/
        const newData = [...dataList];
        for (let i in newData) {
            if (newData[i].editable) {
                delete newData[i].editable;
            }
        }
        //清空筛选条件，重置分页数据
        form.resetFields();
        this.setState({
            checkedRows: [],
            checkedRowKeys: [],
            isOldSearch: null
        });
        dispatch({ type: 'centerContent/load', payload: { name: '', page: 1, depId: '' } });
    }

    render() {
        const { centerContent, selectAdmin } = this.props,
            { dataList, listLoading, departmentlist, pageNew, selectChange } = centerContent,
            _app = {
                formValueName,
                props: this.props,
                listLoading,
                dataList,
                pageNew,
                departmentlist,
                searchSubmit: this.searchSubmit.bind(this),
                //多选时触发的方法
                selected: this.selected.bind(this),
                //多选时触发的方法
                cancelSelect: this.cancelSelect.bind(this),
                //单选时触发的方法
                selected1: this.selected1.bind(this),
                submitData: this.submitData.bind(this),
                handleCancel: this.handleCancel.bind(this),
                changePage: this.changePage.bind(this),
                selectChange,
                selectAdmin
            };


        return (
            <IndexView {..._app} />
        )
    }

};
CenterCon.propTypes = {
    // 控制是否收起 true 为收起
    visible: PropTypes.bool.isRequired,
    //提交确认，返回值
    handleOk: PropTypes.func.isRequired,
    //关闭显示，取消操作
    handleCancel: PropTypes.func.isRequired,
    //当前弹框的题目 选择人员
    title: PropTypes.string,
    //当前弹框的宽度  600
    width: PropTypes.number,
    //控制选择人员是单选还是多选  true是多选
    singleSlec: PropTypes.bool.isRequired
};

const mapStateToProps = ({ centerContent }) => ({ centerContent });
export default connect(
    mapStateToProps
)(Form.create()(CenterCon));