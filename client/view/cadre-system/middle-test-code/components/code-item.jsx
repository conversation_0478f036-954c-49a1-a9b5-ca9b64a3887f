import React, { useState, useRef, useEffect } from "react";
import "./code-item.less";
import DropText from "../../components/drop-text";
import CodeImage from "../images/code.png";
import DownloadImage from "../images/download.png";
import { Table, Switch, Button, Popconfirm } from "antd";
import html2canvas from "html2canvas";
import QRCode from "qrcode.react";
import { getQrCodeInfo } from "client/apis/cadre-portrait";
import { cadreMobile } from "client/apis/config"
// 市管干部
const MobileUrl = `${cadreMobile}/shiguan/`
/**
 * @description: 测评码
 * @return {*}
 */

function CodeItem(props) {
  const {
    title = "测评码",
    org_id,
    dataSource,
    evaluation_id,
    qr_code_type, // 二维码类型ID
    registryDownLoadCb,
    onDelete,
    ticketType = [],
    onUpdateEvaluation,
  } = props;
  const [code, setCode] = useState("");
  const [qrVisible, setQrVisible] = useState(false);
  const qrcodeRef = useRef();

  useEffect(() => {
    registryDownLoadCb(onDownload);
  }, []);
  /**
   * @description: 二次确认
   * @param {*} e
   * @return {*}
   */
  const confirm = (e) => {
    onDelete && onDelete(user.user_id);
  };
  /**
   * @description: 取消
   * @param {*} e
   * @return {*}
   */
  const cancel = (e) => {
    console.log(e);
    message.error("Click on No");
  };
  /**
   * @description: 下拉
   * @return {*}
   */
  const onChange = () => {
    console.log("select");
  };
  const option = [
    { label: "正职", key: "1" },
    { label: "副职", key: "2" },
    { label: "改非干部", key: "3" },
    { label: "新提拔中层干部", key: "4" },
  ];

  const getValue = (value) => {
    const item = option.find((item) => Number(item.key) === Number(value));
    return item ? item.label : "";
  };

  const columns = [
    {
      title: "分类",
      dataIndex: "sequence_type",
      key: "sequence_type",
      align: "center",
      width: "25%",
    },
    {
      title: "被测评人",
      dataIndex: "user_name",
      key: "user_name",
      align: "center",
      width: "10%",
    },
    {
      title: "干部类别",
      dataIndex: "user_type",
      key: "user_type",
      align: "center",
      render(_) {
        return getValue(_);
      },
      width: "10%",
    },
    {
      title: "职务",
      dataIndex: "job_name",
      key: "job_name",
      align: "left",
      editable: true,
      width: "25%",
      // onHeaderCell(column) {
      //   return {
      //     style: {
      //       paddingLeft: "20px",
      //     },
      //   };
      // },
      // render(_, { job }) {
      //   return <span style={{ paddingLeft: "20px" }}>{_}</span>;
      // },
    },
    {
      title: "打票类型",
      dataIndex: "relationship",
      key: "relationship",
      align: "center",
      width: "20%",
      render(value, { pms_evaluation_qrcode_id }) {
        const option = [
          { label: "其他", key: "-1" },
          { label: "上级", key: "1" },
          { label: "同级", key: "2" },
          { label: "下级", key: "3" },
          { label: "主管单位正职", key: "4" },
          { label: "本单位正职", key: "5" },
          { label: "书记", key: "6" },
          { label: "乡（镇）长", key: "7" },
        ];

        return (
          <DropText
            value={value}
            onSelect={(value) => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                relationship: value,
              });
            }}
            option={ticketType}
          />
        );
      },
    },
    // {
    //   title: "特征标签",
    //   dataIndex: "tag",
    //   key: "tag",
    //   align: "center",
    //   render(_, { pms_evaluation_qrcode_id }) {
    //     const status = Number(_) === 1 ? true : false;
    //     return (
    //       <Switch
    //         defaultChecked
    //         size="small"
    //         checked={status ? true : false}
    //         onChange={() => {
    //           onUpdateEvaluation({
    //             pms_evaluation_qrcode_id,
    //             tag: status ? 2 : 1,
    //           });
    //         }}
    //       />
    //     );
    //   },
    // },
    // {
    //   title: "一言素描",
    //   dataIndex: "sketch",
    //   key: "sketch",
    //   align: "center",
    //   render(_, { pms_evaluation_qrcode_id }) {
    //     const status = Number(_) === 1 ? true : false;
    //     return (
    //       <Switch
    //         defaultChecked
    //         size="small"
    //         checked={status ? true : false}
    //         onChange={() => {
    //           onUpdateEvaluation({
    //             pms_evaluation_qrcode_id,
    //             sketch: status ? 2 : 1,
    //           });
    //         }}
    //       />
    //     );
    //   },
    // },
    {
      title: "操作",
      dataIndex: "opretion",
      key: "opretion",
      align: "center",
      width: "10%",
      render(_, { pms_evaluation_qrcode_id }) {
        return (
          <Popconfirm
            title="确认删除?"
            onConfirm={() => {
              onUpdateEvaluation({
                pms_evaluation_qrcode_id,
                has_delete: 1,
              });
            }}
            onCancel={cancel}
            okText="是"
            cancelText="否"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        );
      },
    },
  ];

  const onDownload = () => {
    const el = qrcodeRef.current;
    if (!el) return;

    const canvas = html2canvas(el);
    canvas.then((canvas) => {
      const imageURL = canvas.toDataURL("image/png");
      // 生成一个临时 a 标签并点击触发下载
      const tempLink = document.createElement("a");
      tempLink.id = "down-a";
      tempLink.download = title;
      tempLink.href = imageURL;
      document.body.appendChild(tempLink);

      tempLink.click();

      document.body.removeChild(tempLink);
    });
  };

  // getQrCodeInfo({ evaluation_id }).then(
  //   ({ data: { code, data: res, message: msg } }) => {
  //     this.setState({
  //       qrVisible: code === 0,
  //       evaluation_id,
  //     });
  //     if (code === 0) {
  //       this.setState({
  //         codeInfo: res,
  //       });
  //     } else {
  //       message.error(msg);
  //     }
  //   }
  // );
  const url = `${MobileUrl}?evaluation_id=${evaluation_id}&org_id=${org_id}&eval_partyment_id=${19}&qrcode_type=${qr_code_type}`;
  return (
    <div className="code-item">
      <div className="header">{title}测评码</div>
      <div className="code-container">
        <div className="table-box">
          <Table
            tableLayout="fixed"
            columns={columns}
            dataSource={dataSource}
            bordered
            scroll={{
              y: 400,
            }}
            pagination={false}
          />
        </div>
        <div className="code-box">
          <div
            className="img-box"
            ref={qrcodeRef}
            onClick={() => {
              // window.open(url);
            }}
          >
            {/* <img alt="" src={CodeImage} /> */}
            <QRCode
              value={url}
              size={310} // 二维码的大小
              fgColor="#000000" // 二维码的颜色
              style={{ margin: "auto" }}
            />
          </div>
          <div className="code-name">
            <div className="name">{title}测评码</div>
            <div className="download-label" onClick={onDownload}>
              下载二维码
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CodeItem;
