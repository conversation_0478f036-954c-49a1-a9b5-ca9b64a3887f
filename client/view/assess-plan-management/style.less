.AssessPlanManage {
  .body {
    width: 100%;
    padding: 32px;

    .operatorHeader {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .btnBg {
        background: #F46E65;
        border: none;
        width: 160px;
        height: 40px;
        font-size: 16px;
      }

      .headerForm {
        .ant-form-item:last-child {
          margin-right: 0;
        }

        .queryKeyword {
          width: 200px;
        }

        .queryBtn {
          width: 90px;
          height: 36px; // background: #F46E65;
          // border: none;
        }
      }
    }

    .planList {
      margin-top: 14px;

      .operatorPlan {
        display: flex;
        flex-direction: row;
        justify-content: space-around;

        a {
          text-decoration: underline;
        }
      }

      .useStauts {
        display: inline-block;
        width: 59px;
        height: 24px;
        color: white;
        font-family: FZLTHK--GBK1-0;
        font-size: 14px;
        border-radius: 3px;
      }

      .stopUse {
        background: #ccc;
      }

      .openUse {
        background-color: #22AC38;
      }
    }
  }
}

.assess-plan-manage-detail-modal-wrapper {
  .limit-label {
    display: inline-block;
    width: 100px;

    // text-align: right;
    &::after {
      content: ":";
      margin: 0 5px;
    }
  }

  .limit-org-type-wrapper {
    &:not(:last-child) {
      &::after {
        content: "、";
      }
    }
  }

  .search-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid #e8e8e8;
    border-bottom: 0;
    padding: 15px 25px;

    label {
      white-space: nowrap;

      &::after {
        content: ":";
        margin: 0 5px;
      }
    }

    .input-wrapper {
      margin-right: 20px;
    }

    .buttons-wrapper {
      .ant-btn {
        width: 90px;
        height: 36px;

        &:first-child {
          margin-right: 30px;
        }
      }
    }
  }
}