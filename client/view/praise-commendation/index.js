import {
  Button,
  DatePicker,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Table,
  Tree,
} from "antd";
import {
  addOrUpdateCommendInfo,
  deleteCommendInfo,
  exportCommendList,
  getOrgUserList,
  queryByCode,
  queryCommendInfo,
  queryCommendInfoList,
  queryOrgType,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import DateSingle from "components/date-single/DateSingle";
import { connect } from "dva";
import { Component } from "react";
import PAutoComplete from "./components/PAutoComplete";
import "./index.less";

const { TreeNode } = Tree;
const { Option } = Select;
const { MonthPicker, RangePicker, WeekPicker } = DatePicker;

import PropTypes from "prop-types";

class LeaderGroup extends Component {
  constructor(props) {
    super(props);
    const currentUserInfo =
      typeof window !== "undefined"
        ? JSON.parse(window.sessionStorage.getItem("userInfo"))
        : {};
    const currentOid =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";
    this.state = {
      visible: false,
      org_unit_name: "",
      has_leader: "",
      page: 1,
      selectKey: currentOid,
      expandKeys: [],
      autoExpandParent: true,
      selectName: currentUserInfo.name || "",
      activeTabKey: 0,
      isTabSwitch: false,
      // 新增
      org_id: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      selectOrgId: undefined,
      user_id: undefined,
      user_name: "",
      user_phone: "",
      currentSelect: {},
      user_label: "",
      page: 1,
      total: 0,
      page_size: 10,
      pageNum: 0,
      total: 0,
      dataSource: [],
      link: null,
      unit_type: [],
      orgType: 1, //1-班子，2-组织
      level: "all",
      type: "all"
    };
  }

  componentDidMount() {
    this.getOrgTypeList();
    this.getOrgList();
    this.queryCommendInfoList();
    this.getCode();
  }
  /**
   *
   *  org_id	number		N	组织Id	
      user_name	string		N	姓名	
      type	number		N	类别	1.表扬 2.表彰
      level	string		N	授予单位类别	code-10080
   */
  queryCommendInfoList() {
    let { selectOrgId, user_name, type, level, page } = this.state;
    if (type === "all") type = undefined;

    if (level === "all") level = undefined;

    queryCommendInfoList({
      page,
      org_id: selectOrgId,
      user_name,
      type,
      level,
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;

        this.setState({
          dataSource: data.content,
          pages: data.totalPages,
          total: data.totalElements,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  onSearch() {
    this.setState(
      {
        page: 1,
      },
      () => {
        this.queryCommendInfoList();
      }
    );
  }
  onReset() {
    this.setState(
      {
        user_name: "", // 清空姓名查询条件
        type: "all", // 重置类别为默认值 "all"
        level: "all", // 重置授予单位类别为默认值 "all"
        page: 1, // 将页码重置为第一页
      },
      () => {
        this.queryCommendInfoList(); // 重新加载数据
      }
    );
  }
  onExport() {
    let { user_name, type, level, selectOrgId } = this.state;

    if (type === "all") type = undefined;

    if (level === "all") level = undefined;

    exportCommendList({
      user_name,
      type,
      level,
      org_id: selectOrgId,
    }).then((res) => {
      if (res.data.code === 0) {
      }
    });
  }
  onChange(page) {
    this.setState(
      {
        page,
      },
      () => {
        this.queryCommendInfoList();
      }
    );
  }
  onDelete(record) {
    deleteCommendInfo({ reward_id: record.pms_reward_id }).then((res) => {
      if (res.data.code === 0) {
        message.success("删除成功");
        this.queryCommendInfoList();
      } else {
        message.error(res.data.message);
      }
    });
  }
  onEditor(record) {
    const { setFieldsValue } = this.props.form;
    queryCommendInfo({
      reward_id: record.pms_reward_id,
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        this.setState({
          visible: true,
          currentSelect: data,
        });

        setFieldsValue(data);
      } else {
        message.error(res.data.message);
      }
    });
  }
  selectTree(org) {
    if (!org.length) return;

    this.setState(
      {
        page: 1,
        selectOrgId: org[0],
      },
      () => {
        this.queryCommendInfoList();

        this.queryOrgType(org[0]);
      }
    );
  }
  async getOrgList() {
    const params = {
      name: this.state.user_name,
      phone: this.state.user_phone,
      user_tag: this.state.user_label,
      org_id: this.state.org_id,
      page: this.state.page,
      page_size: this.state.page_size,
    };
    const { data } = await getOrgUserList(params);
    if (data.code == 0) {
      this.setState({
        pageNum: data.pageNum,
        total: data.total,
      });
    }
  }
  getCode() {
    queryByCode({
      code: 1008,
    }).then((res) => {
      if (res.data.code === 0) {
        this.setState({
          unit_type: res.data.data,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  queryOrgType(org_id) {
    queryOrgType({
      org_id,
    }).then((res) => {
      if (res.data.code == 0) {
        this.setState({
          orgType: res.data.data.org_type,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  // 组织类型获取
  getOrgTypeList(orgId) {
    const { dispatch } = this.props;
    const { selectKey } = this.state;
    dispatch({
      type: "leaderGroup/getTreeTypeList",
      payload: {
        org_id: orgId || selectKey,
      },
    }).then(() => {
      this.loadTreeList(orgId || selectKey);
    });
  }
  // 左边树 滚动到指定位置
  expandTree(keys, params) {
    const { expanded, node } = params;
    const {
      dataRef: { org_id },
    } = node.props;
    this.setState(
      {
        expandKeys: keys,
        autoExpandParent: false,
        isTabSwitch: false,
      },
      () => {
        expanded && this.loadTreeList(org_id);
      }
    );
  }

  tabChange(key) {
    this.setState(
      {
        activeTabKey: key,
        isTabSwitch: true,
        autoExpandParent: false,
        expandKeys: [],
        selectKey: this.state.selectKey,
        page: 1,
      },
      () => {
        this.loadTreeList(this.state.selectKey);
        this.loadOrgUnit();
      }
    );
  }
  //切换树
  loadOrgUnit() {
    const { selectKey } = this.state;
    this.setState(
      {
        org_id: selectKey,
      },
      () => {
        this.getOrgList();
      }
    );
  }
  //组织树
  loadTreeList(org_id) {
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey, isTabSwitch } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      tree_type,
      org_type,
      org_id,
      isTabSwitch,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    dispatch({
      type: "leaderGroup/getOrgTree",
      payload,
    });
  }
  // 模糊查询组织列表
  searchOrgList(value) {
    console.log(this.state.selectKey, "this.state.selectKey");
    const { dispatch } = this.props;
    if (!value) {
      this.setState(
        {
          selectKey: JSON.parse(sessionStorage.getItem("userInfo")).oid,
        },
        () => {
          dispatch({
            type: "leaderGroup/findOrgByName",
            payload: {
              org_id: this.state.selectKey,
              org_name: value,
              tree_type: 2,
            },
          });
        }
      );
    } else {
      dispatch({
        type: "leaderGroup/findOrgByName",
        payload: {
          org_id: this.state.selectKey,
          org_name: value,
          tree_type: 2,
        },
      });
    }
  }
  // 点击组织树
  selectTreeOrg(value, name) {
    this.setState(
      {
        selectKey: value,
        selectName: name,
        page: 1,
        org_id: value,
      },
      () => {
        this.getOrgList();
        this.loadOrgUnit();
      }
    );
  }
  // 左边树查询
  selectOrg(value, option) {
    console.log("1111", value, option, this.state.selectKey);
    const { key } = option;
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      root_org_id: this.state.selectKey,
      org_id: key,
      org_type,
      load_root: 1,
      tree_type,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    if (key == this.state.selectKey) {
      return;
    }
    dispatch({
      type: "leaderGroup/getAllOrgs",
      payload,
    }).then(() => {
      this.setState(
        {
          selectKey: key,
          expandKeys: [key],
          autoExpandParent: true,
          selectName: value,
          page: 1,
          org_id: key,
        },
        () => {
          this.getOrgList();
          this.loadOrgUnit();
        }
      );
      this._scrollPos(key);
    });
  }
  //树
  renderTreeNodes(data) {
    return data.map((item) => {
      const { child_org_num, org_id, name, short_name } = item;
      const { selectKey } = this.state;
      if (item.children) {
        return (
          <TreeNode
            isLeaf={child_org_num === 0}
            title={
              <div
                ref={org_id}
                title={name || short_name}
                className={
                  parseInt(selectKey) === parseInt(org_id) ? "active" : ""
                }
                onClick={this.selectTreeOrg.bind(this, org_id, name)}
              >
                {short_name || name}
              </div>
            }
            key={org_id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf={child_org_num === 0}
          title={
            <div
              ref={org_id}
              onClick={this.selectTreeOrg.bind(this, org_id, name)}
            >
              {short_name || name}
            </div>
          }
          key={org_id}
          dataRef={item}
        />
      );
    });
  }
  handleOk() {
    const { getFieldsValue, validateFields } = this.props.form;
    validateFields((err, values) => {
      if (!err) {
        const params = getFieldsValue();

        const { pms_reward_id } = this.state.currentSelect;

        const { user_name, reward_time } = params;

        if (toString.call(user_name) === "[object Object]") {
          delete params.user_name;

          params.user_name = user_name.user_name;
          params.user_id = user_name.user_id;
        }

        addOrUpdateCommendInfo({
          ...this.state.currentSelect,
          ...params,
        }).then((res) => {
          if (res.data.code === 0) {
            this.setState({
              visible: !this.state.visible,
            });
            message.success("提交成功");

            this.queryCommendInfoList();
          } else {
            message.error(res.data.message);
          }
        });
      }
    });
  }
  handleCancel() {
    const { resetFields } = this.props.form;

    this.setState({
      visible: !this.state.visible,
      currentSelect: {},
    });

    resetFields();
  }
  cancel(e) { }

  jumpImport() {
    this.props.history.push("/import-page?type=13");
  }

  render() {
    const { tabs, visible, index, autoExpandParent, expandKeys, dataSource } =
      this.state;
    const { dispatch, form, leaderGroup } = this.props;
    const { getFieldDecorator } = form;
    const totalPage = Math.ceil(leaderGroup.totalCount / 10);
    const _expandKeys =
      leaderGroup &&
        leaderGroup.treeList &&
        leaderGroup.treeList.length &&
        leaderGroup.treeList[0].children.length
        ? leaderGroup.treeList[0].org_id
        : "";
    const columns = [
      {
        title: "姓名",
        dataIndex: "user_name",
        key: "user_name",
        width: "5%",
        align: "center",
      },
      // {
      //   title: "出生年月",
      //   dataIndex: "birthday",
      //   key: "birthday",
      //   width: 100,
      //   align: "center",
      // },
      {
        title: "时任职务",
        dataIndex: "current_job",
        key: "current_job",
        width: "10%",
        align: "center",
      },
      {
        title: "授予单位",
        dataIndex: "authorized_unit",
        key: "authorized_unit",
        width: "15%",
        align: "center",
      },
      {
        title: "授予单位类别",
        dataIndex: "level",
        key: "level",
        width: "10%",
        align: "center",
      },
      {
        title: "类别",
        dataIndex: "type",
        key: "type",
        width: "5%",
        align: "center",
      },
      {
        title: "奖励内容",
        dataIndex: "name",
        key: "name",
        align: "center",
        render(_) {
          return <div style={{ width: "100%", textAlign: "left" }}>{_}</div>;
        },
      },
      {
        title: "文件依据",
        dataIndex: "doc_basis",
        key: "doc_basis",
        width: 100,
        align: "center",
      },
      {
        title: "发文时间",
        dataIndex: "reward_time",
        key: "reward_time",
        width: 100,
        align: "center",
      },
      {
        title: "备注",
        dataIndex: "remark",
        key: "remark",
        width: 100,
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "handle",
        key: "handle",
        align: "center",
        width: "8%",
        render: (_, record) => {
          return (
            <div className="handle">
              <Popconfirm
                title="确定删除吗?"
                onConfirm={() => {
                  this.onDelete(record);
                }}
                onCancel={this.cancel.bind(this)}
                okText="确定"
                cancelText="取消"
              >
                <a href="#">删除</a>
              </Popconfirm>
              <a
                onClick={() => {
                  this.onEditor(record);
                }}
              >
                编辑
              </a>
            </div>
          );
        },
      },
    ];
    const data = [
      {
        key: "1",
        name: "John Brown",
        post: "市长",
        unit: "市政府",
        sort: "市政府",
        category: "市政府",
        award: "市政府",
        gist: "市政府",
        time: "市政府",
        remark: "市政府",
      },
    ];
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    return (
      <div className="praise-commendation">
        <div className="left">
          {/* <AutoComplete
            className="searchOrg"
            placeholder="请输入组织名称"
            allowClear
            optionLabelProp="value"
            onSearch={this.searchOrgList.bind(this)}
            onSelect={this.selectOrg.bind(this)}
          >
            {leaderGroup &&
              leaderGroup.orgList &&
              leaderGroup.orgList.map((item) => {
                return (
                  <AutoComplete.Option
                    key={item.org_id}
                    value={item.org_name}
                    text={item.org_name}
                  >
                    {item.org_name}
                  </AutoComplete.Option>
                );
              })}
          </AutoComplete>
          <div className="orgContainer" ref="orgContainer">
            <Tree
              blockNode={true}
              autoExpandParent={autoExpandParent}
              onExpand={this.expandTree.bind(this)}
              expandedKeys={
                expandKeys.length ? expandKeys : [_expandKeys.toString()]
              }
              onSelect={this.selectTree.bind(this)}
            >
              {leaderGroup.treeList
                ? this.renderTreeNodes(leaderGroup.treeList)
                : null}
            </Tree>
          </div> */}
          <OrgTree onChange={this.selectTree.bind(this)} />
        </div>
        <div className="right">
          <div className="praise-header">
            <div className="header-search">
              <div>
                <span className="r-label"> 姓名：</span>
                <Input
                  value={this.state.user_name}
                  placeholder="请输入"
                  style={{ width: "150px" }}
                  onChange={(e) => {
                    this.setState({
                      user_name: e.target.value,
                    });
                  }}
                />
              </div>
              <div>
                <span className="r-label">类别：</span>
                <Select
                  // defaultValue="all"
                  value={this.state.type}
                  onChange={(value) => {
                    this.setState({ type: value });
                  }}
                  allowClear
                >
                  <Option value="all">全部</Option>
                  <Option value="1">表扬</Option>
                  <Option value="2">表彰</Option>
                </Select>
              </div>
              <div>
                <span className="r-label">授予单位类别：</span>
                <Select
                  // defaultValue="all"
                  onChange={(value) => {
                    this.setState({ level: value });
                  }}
                  value={this.state.level}
                  allowClear
                >
                  <Option value="all">全部</Option>
                  {this.state.unit_type.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              </div>

              <div className="search-btn">
                <div className="header-btn" onClick={() => this.onSearch()}>
                  查询
                </div>
                <div className="reset" onClick={() => this.onReset()}>
                  重置
                </div>
                <div
                  className="reset"
                  onClick={() => {
                    this.onExport();
                  }}
                >
                  导出
                </div>
              </div>
            </div>
            <div className="btn-item">
              <div
                className="header-btn"
                onClick={() => this.setState({ visible: true })}
              >
                <Icon type="plus" />
                添加
              </div>
              <Button className="import-btn" onClick={() => this.jumpImport()}>
                导入
              </Button>
            </div>
          </div>
          <div className="praise-content">
            <Table
              bordered
              columns={columns}
              dataSource={dataSource}
              pagination={{
                onChange: this.onChange.bind(this),
                total: this.state.total,
                pageSize: 10,
                current: this.state.page,
              }}
            />
          </div>
        </div>
        <Modal
          title={`${this.state.currentSelect.name ? "编辑" : "添加"}表扬表彰`}
          visible={visible}
          width={800}
          onOk={this.handleOk.bind(this)}
          onCancel={this.handleCancel.bind(this)}
          destroyOnClose
        >
          <Form {...formItemLayout}>
            {getFieldDecorator("pms_reward_id", {})(<Input type="hidden" />)}
            {getFieldDecorator("user_id", {})(<Input type="hidden" />)}
            <Form.Item label="姓名" required>
              {getFieldDecorator("user_name", {
                rules: [{ required: true, message: "请输入姓名" }],
              })(
                <PAutoComplete
                  org_id={this.state.selectOrgId}
                  placeholder="请输入"
                  style={{ width: "300px" }}
                />
              )}
            </Form.Item>
            {/* <Form.Item label="出生年月">
              {getFieldDecorator("birthday", {
                rules: [{ required: true, message: "请选择出生年月" }],
              })(
                <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
              )}
            </Form.Item> */}
            <Form.Item label="授予单位">
              {getFieldDecorator("authorized_unit", {
                rules: [{ required: true, message: "请输入授予单位" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="授予单位类别">
              {getFieldDecorator("level", {
                rules: [{ required: true, message: "请选择授予单位类别" }],
              })(
                <Select placeholder="请选择" style={{ width: 120 }}>
                  {this.state.unit_type.map((item) => {
                    return <Option value={item.op_key}>{item.op_value}</Option>;
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="类别">
              {getFieldDecorator("type", {
                rules: [{ required: false, message: "请选择" }],
              })(
                <Select style={{ width: 120 }} placeholder="请选择" allowClear>
                  <Option value={1}>表扬</Option>
                  <Option value={2}>表彰</Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item label="奖励名称（内容）">
              {getFieldDecorator("name", {
                rules: [{ required: true, message: "请输入奖励名称（内容）" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="文件依据">
              {getFieldDecorator("doc_basis", {
                rules: [{ required: false, message: "请输入文件依据" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="发文时间">
              <div>
                {getFieldDecorator("reward_time", {
                  rules: [{ required: true, message: "请选择发文时间" }],
                })(
                  <DateSingle
                    placeholder="请选择"
                    type="month"
                    isWrap={false}
                  />
                )}
                {/* <span style={{ margin: "0px 10px" }}>年</span>
                {getFieldDecorator("month", {
                  rules: [{ required: true, message: "请输入文件依据" }],
                })(<DatePicker mode="month" />)}
                <span style={{ margin: "0px 10px" }}>月</span> */}
              </div>
            </Form.Item>
            <Form.Item label="时任职务">
              {getFieldDecorator("current_job", {
                rules: [{ required: false, message: "请输入时任职务" }],
              })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
            <Form.Item label="备注">
              {getFieldDecorator(
                "remark",
                {}
              )(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(Form.create()(LeaderGroup));
