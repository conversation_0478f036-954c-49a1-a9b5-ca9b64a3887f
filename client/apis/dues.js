/**
 * 1030002-党费系统
 */

import { fileHost } from "./config";
import { http, headers } from "client/tool/axios";
import {staticHttp} from "client/tool/axios-now"







//**********-党费返还


//**********-返还记录查询
export const findHistory = params => {
  return http.get(`${fileHost}/return/find-history`, params, headers);
};


//**********-机关-设置返还账号
export const setAccountInfo = params => {
  return http.get(`${fileHost}/returnAccount/insert`, params, headers);
};

export const getAccountInfo = params => {
  return http.get(`${fileHost}/returnAccount/insert`, params, headers);
};
