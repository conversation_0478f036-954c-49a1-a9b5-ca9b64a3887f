import { activityHost, ucHost, workflowHost } from './config';
import { http, headers, filterParamsValue } from 'client/tool/axios';

//1040140001-添加问券模板
const addTemplate = (prams) => {
  return http.post(`${activityHost}/coupon_template/add`, prams);
}
//1040140003-查询问卷模板列表
const getTemplateList = (prams) => {
  return http.get(`${activityHost}/coupon_template/list`, prams);
}
//1040140005-查询问卷模板详情
const getTemplateDetail = (prams) => {
  return http.get(`${activityHost}/coupon_template/get/${prams}`);
}
//复制从 1320-查看问卷调查统计详情
const getQuestionDetail = (prams) => {
  return http.get(`${activityHost}/activity/count/question/${prams}`);
}
//1040140004-删除问卷模板
const delTemplate = (prams) => {
  return http.delete(`${activityHost}/coupon_template/del/${prams}`);
}
//1040140002-修改问卷模板
const updateTemplate = (prams) => {
  return http.post(`${activityHost}/coupon_template/edit`, prams);
}

//1040140006-查询互动情况列表
const getActivityOrgList = (prams) => {
  return http.post(`${activityHost}/activity/push/find-activity-orglist`, prams)
}

//1031000401-活动列表
const getConditionList = (prams) => {
  return http.get(`${activityHost}/activity/find-condition`, prams)
}

//1040140007-启动考核
const activityPushStart = (prams) => {
  return http.post(`${activityHost}/activity/push/start`, prams)
}

// 10101051317-发布问卷调查(新增调查评价问券)
const insertQuestionnaire = (params) => {
  return http.post(`${activityHost}/questionnaire/insert?view=true`, params)
}

const setUserScope = (params) => {
  return http.post(`${activityHost}/activity/limit/user-scope`, params)
}

export {
  addTemplate,
  getTemplateList,
  getTemplateDetail,
  getActivityOrgList,
  delTemplate,
  getConditionList,
  activityPushStart,
  getQuestionDetail,
  updateTemplate,
  insertQuestionnaire,
  setUserScope
}
