import React from "react";
import { Form, Modal, Input, Button, Table, message as Message } from "antd";
import "./index.less";
import PropTypes from "prop-types";
import { getTopicTaskListAll } from "apis/meeting-manage";

const FormItem = Form.Item;
class MeetingTask extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      meetingTaskDataList: [],
      currentTaskDataList: [],
      meetingTaskSelectedRow: [],
      meetingTaskSelectedRowKeys: [],

      // 备份原选中数组
      backupSelectedRow: [],
      backupSelectedRowKeys: [],
      selectedRowKeys: [],

      // loading
      loading: false,
      keyword: null
    };
  }

  componentWillMount() {
    this.init();
  }

  componentWillReceiveProps(props) {
    const { dataSource, visible } = props;
    if (dataSource && Array.isArray(dataSource) && dataSource.length !== 0) {
      // 键值为：meeting_task_id
      const rowKeys = dataSource.map((row) => {
        return row.topic_id;
      });
      this.setState({
        meetingTaskSelectedRow: dataSource,
        meetingTaskSelectedRowKeys: rowKeys,
        backupSelectedRow: dataSource,
        backupSelectedRowKeys: rowKeys,
        selectedRowKeys: rowKeys
      }, () => {
        // 显示出来的时候重新加载数据
        if (visible) {
          const { backupSelectedRowKeys = [], meetingTaskDataList = [] } = this.state;
          // 将外边选择了的表格项目筛掉
          const filterResult = this.filterSelectedItem(meetingTaskDataList, backupSelectedRowKeys, "topic_id");
          // console.log(filterResult);
          this.setState({
            currentTaskDataList: filterResult
          });
        }
      });
    } else {
      const { meetingTaskDataList } = this.state;
      // console.log(meetingTaskDataList);
      this.setState({
        meetingTaskSelectedRow: [],
        meetingTaskSelectedRowKeys: [],
        backupSelectedRow: [],
        backupSelectedRowKeys: [],
        selectedRowKeys: [],
        currentTaskDataList: meetingTaskDataList
      })
    }
  }

  // 将外边选择了的表格项目筛掉
  filterSelectedItem(dataList = [], selectedKeys = [], key = "key") {
    let result = dataList;
    if (dataList && Array.isArray(dataList)) {
      result = dataList.filter((item) => {
        return selectedKeys.indexOf(item[key]) === -1;
      });
    }
    return result;
  }

  init() {
    this.fetchTopicTaskListAll({ tag: 1 });
  }

  searchByKeyword(params = {}) {
    const { keyword } = this.state;
    if (!params.name) {
      params.name = keyword;
    }
    this.fetchTopicTaskListAll({ ...params, tag: 1 });
  }

  async fetchTopicTaskListAll(params = {}) {
    this.setState({
      loading: true,
    });
    const response = await getTopicTaskListAll(params);
    // console.log(response);
    const { data: body } = response;
    const { code, message, data } = body;
    if (code !== 0) {
      this.setState({
        loading: false,
      });
      Message.error(message);
      return;
    }
    const { backupSelectedRowKeys = [] } = this.state;
    const filterResult = this.filterSelectedItem(data, backupSelectedRowKeys, "topic_id");
    this.setState({
      loading: false,
      meetingTaskDataList: data,
      currentTaskDataList: filterResult
    });
  }

  render() {
    const { visible, form, updateState, onChange } = this.props;
    const { currentTaskDataList, selectedRowKeys, loading, keyword } = this.state;
    const { validateFields, getFieldDecorator } = form;

    const onSelectHandler = (record, selected, selectedRows, e) => {
      // console.log(11111);
      const {
        meetingTaskSelectedRow,
      } = this.state;
      const rows = JSON.parse(JSON.stringify(meetingTaskSelectedRow));
      // console.log(rows);
      if (rows && Array.isArray(rows)) {
        const index = rows.findIndex((row) => {
          return row.topic_id === record.topic_id;
        });
        if (selected) {
          // 选中某个项目
          // console.log("选中", record);
          if (index === -1) {
            rows.push(record);
          }
        }
        else {
          // 反选某个项目
          // console.log("反选", record);
          if (index !== -1) {
            rows.splice(index, 1);
          }
        }
        let meetingTaskSelectedRowKeys = [];
        rows.forEach((row) => {
          meetingTaskSelectedRowKeys.push(row.topic_id);
        });
        this.setState({
          meetingTaskSelectedRow: rows,
          meetingTaskSelectedRowKeys,
          selectedRowKeys: meetingTaskSelectedRowKeys,
        });
        // console.log(rows, meetingTaskSelectedRowKeys);
      }
    }

    const rowSelection = {
      columnTitle: "选择",
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelectHandler,
    };

    const modalProps = {
      title: "选择已有任务",
      visible,
      width: 800,
      onCancel: () => {
        updateState({
          meetingTaskVisible: false
        });
      },
      footer: null
    };

    const columns = [
      {
        title: "序号",
        align: "center",
        width: 65,
        render: (t, r, i) => {
          return i + 1;
        }
      },
      {
        // title: "议题名称",
        title: "任务名称",
        align: "left",
        dataIndex: "name"
      },
      {
        title: "开始时间",
        align: "center",
        dataIndex: "start_time",
        width: 120,
      },
      {
        title: "截止时间",
        align: "center",
        dataIndex: "end_time",
        width: 120,
      },
      {
        title: "备注",
        align: "center",
        dataIndex: "remark",
        width: 120,
        render: (text, record, index) => {
          // return text === 1 ? "非本组织添加" : text === 2 ? "本组织添加" : "其他";
          return text === 1 ? "上级交办任务" : text === 2 ? "本组织新增任务" : "其他";
        }
      },
      {
        title: "待完成任务",
        align: "center",
        dataIndex: "status",
        width: 100,
        render: (text, record, index) => {
          // 1：未完成；2：已完成；3：逾期未完成
          return text === 1 ? "是" : "否";
          // return text === 1 ? "未完成" : text === 2 ? "已完成" : text === 3 ? "逾期未完成" : "其他";
        }
      }
    ];

    return (
      <Modal {...modalProps}>
        <div className="meeting-task-container">
          <Form
            layout="inline"
            onSubmit={(e) => {
              e.preventDefault();
              validateFields((error, values) => {
                if (!error) {
                  // console.log("搜索", values);
                  this.searchByKeyword(values);
                }
              })
            }}
          >
            <FormItem label="任务名称">
              <Input
                value={keyword}
                placeholder="请输入"
                style={{ width: 160, backgroundColor: "#F7F8F9" }}
                onChange={(e) => {
                  const target = e.target;
                  const value = target.value;
                  this.setState({
                    keyword: value
                  });
                }}
              />
              {/* {
                getFieldDecorator("name", {})(
                  <Input
                    placeholder="请输入"
                    style={{ width: 160, backgroundColor: "#F7F8F9" }}
                  />
                )
              } */}
            </FormItem>
            <Button
              htmlType={"submit"}
              style={{ width: 90, marginLeft: 15, height: 36, position: "relative", top: 1 }}
              type={"primary"}
            >
              查询
            </Button>
          </Form>
          <Table
            loading={loading}
            rowKey={"topic_id"}
            columns={columns}
            bordered
            style={{ marginTop: 20, marginBottom: 20 }}
            rowSelection={rowSelection}
            dataSource={currentTaskDataList}
            scroll={{ y: 360 }}
          />
          <div className="buttons-wrapper">
            <Button className={"buttons"} type="primary" onClick={() => {
              // meetingTaskSelectedRow: [],
              // meetingTaskSelectedRowKeys: [],
              const { meetingTaskSelectedRow = [], meetingTaskSelectedRowKeys = [] } = this.state;
              const rows = JSON.parse(JSON.stringify(meetingTaskSelectedRow)),
                rowKeys = JSON.parse(JSON.stringify(meetingTaskSelectedRowKeys));
              this.setState({
                meetingTaskSelectedRow: rows,
                meetingTaskSelectedRowKeys: rowKeys,
                selectedRowKeys: rowKeys,
              }, () => {
                onChange(rows, rowKeys);
                modalProps.onCancel();
              });
            }}>确定</Button>
            <Button className={"buttons"} onClick={() => {
              const { backupSelectedRow, backupSelectedRowKeys } = this.state;
              // console.log("备份", backupSelectedRow, backupSelectedRowKeys)
              this.setState({
                meetingTaskSelectedRow: backupSelectedRow,
                meetingTaskSelectedRowKeys: backupSelectedRowKeys,
                selectedRowKeys: backupSelectedRowKeys,
              }, () => {
                modalProps.onCancel();
              });
            }}>取消</Button>
          </div>
        </div>
      </Modal>
    )
  }
}

MeetingTask.propTypes = {
  visible: PropTypes.bool,
  form: PropTypes.object,
  updateState: PropTypes.func,
  onChange: PropTypes.func
}

MeetingTask.defaultProps = {
  visible: false,
  updateState: () => { },
  onChange: () => { }
}


export default Form.create()(MeetingTask);