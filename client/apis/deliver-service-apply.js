/**
 * 送服务管理相关接口
 * http://************:8090/pages/viewpage.action?pageId=39289661
 */

import { http } from "client/tool/axios";
import { vlHost } from './config';

const MOCK_URL = vlHost;

/**
 * 1063020202070101-预约列表
 * @param {*} params 
 */
export const getReservationList = (params) => {
  return http.get(`${MOCK_URL}/volunteer/reservation/list`, params)
}

/**
 * 1063020202070102-预约审批
 * @param {*} reservation_id 预约id
 * @param {*} status 2.同意 3.拒绝
 * @param {*} opinion 审批意见
 */
export const approveReservation = (reservation_id, status, opinion) => {
  return http.post(`${MOCK_URL}/volunteer/reservation/approve`, { reservation_id, status, opinion })
}

/**
 * 1063020202070103-管理员删除预约审批
 * @param {*} reservation_id 预约id
 */
export const delReservation = (reservation_id) => {
  return http.delete(`${MOCK_URL}/volunteer/reservation/manager-del/${reservation_id}`)
}