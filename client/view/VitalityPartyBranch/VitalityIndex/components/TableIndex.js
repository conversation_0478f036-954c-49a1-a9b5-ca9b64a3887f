import { useState, useEffect, useMemo } from "react";
import { Card, DatePicker, Select, Radio, Table } from "antd";
import { YearsData } from "client/tool/util";
import moment from "moment";
const { MonthPicker } = DatePicker;
const { Option } = Select;
const RadioGroup = Radio.Group;

const TableIndex = ({Target=[],dateVal, setDateVal,searchType, setSearchType}) => {
  const { data: yearList } = YearsData(2019, false);
  // const [dateVal, setDateVal] = useState(moment());
  // const [searchType, setSearchType] = useState("month");
  const data = [
    {
      key: "1",
      name: "总计",
      baseScore: '100.00',
      score:(Target.register+Target.study+Target.life+Target.logon+Target.report+Target.society_org+Target.volunteer_org+Target.live)||0
    },
    {
      key: "2",
      name: "在册党员管理规范度",
      item: "党员登记率",
      address: "按登记人员与在册党员总人数比率计分",
      baseScore: '5.00',
      score:Target.register||0
    },
    {
      key: "3",
      // name: "Joe Black",
      item: '智慧党建平台学习情况',
      address: "依据党组织学习“每日一课”、“人文丰都”积分完成率提供分数",
      baseScore: '10.00',
      score:Target.study||0
    },
    {
      key: "4",
      // name: "Joe Black",
      item: '组织生活参与度',
      address: "依据党组织按完成规定次数的比率提供分数",
      baseScore: '10.00',
      score:Target.life||0
    },
    {
      key: "5",
      name: "外来党员广泛参与",
      item: '外来党员干部注册',
      address: "按照外来党员干部注册人数计分",
      baseScore: '10.00',
      score:Target.logon||0
    },
    {
      key: "6",
      // name: "Joe Black",
      item: '外来党员干部活动报道',
      address: "按照报到完成人次计分",
      baseScore: '10.00',
      score:Target.report||0
    },
    {
      key: "7",
      name: "引领成立各类组织",
      item: '引领成立各种社会组织',
      address: "按照成立的社会组织数目计分",
      baseScore: '15.00',
      score:Target.society_org||0
    },
    {
      key: "8",
      // name: "Joe Black",
      item: '引领成立志愿服务组织',
      address: "按照志愿者人数计分",
      baseScore: '10.00',
      score:Target.volunteer_org||0
    },
    {
      key: "9",
      name: "开展各类特色活动",
      item: "活动开展丰都多彩",
      address: "按照特色活动举办场次计分",
      baseScore: '30.00',
      score:Target.live||0
    },
  ];
  const columns = [
    {
      title: "指标维度",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (text, row, index) => {
        if (index == 0) {
          return {
            children: <div>{text}</div>,
            props: {
              colSpan: 3,
            },
          };
        }

        const obj = {
          children: text,
          props: {},
        };
        if (index === 1) {
          obj.props.rowSpan = 3;
        }
        // These two are merged into above cell
        if (index === 2) {
          obj.props.rowSpan = 0;
        }
        if (index === 3) {
          obj.props.rowSpan = 0;
        }
        if (index === 4) {
          obj.props.rowSpan = 2;
        }
        if (index === 5) {
          obj.props.rowSpan = 0;
        }
        if (index === 6) {
          obj.props.rowSpan = 2;
        }
        if (index === 7) {
          obj.props.rowSpan = 0;
        }
        return obj;

        
      },
    },
    {
      title: "指标项",
      dataIndex: "item",
      key: "item",
      render: (text, row, index) => {
        if (index == 0) {
          return {
            children: <div>{text}</div>,
            props: {
              colSpan: 0,
            },
          };
        }
        if (index > 0) {
          return (<div>{text}</div>)
        }
      },
    },
    {
      title: "指标说明",
      dataIndex: "address",
      key: "address",
      render: (text, row, index) => {
        if (index == 0) {
          return {
            children: <div>{text}</div>,
            props: {
              colSpan: 0,
            },
          };
        }
        if (index > 0) {
          return (<div>{text}</div>)
        }
      },
    },
    {
      title: "基础值",
      key: "baseScore",
      dataIndex: "baseScore",
      align: "center",
      render: (text, row, index) => {
        if (index == 0) {
          return {
            children: <div>{text}</div>,
            props: {
              colSpan: 1,
            },
          };
        }
        if (index > 0) {
          return (<div>{text}</div>)
        }
      },
    },
    {
      title: "指标得分",
      key: "score",
      dataIndex: "score",
      align: "center",
      render: (text, row, index) => {
        if (index == 0) {
          return {
            children: <div style={{ color: '#FE3A3B' }}>{text}</div>,
            props: {
              colSpan: 1,
            },
          };
        }
        if (index > 0) {
          return (<div style={{ color: '#FE3A3B' }}>{text}</div>)
        }
      },
    },
  ];
  return (
    <Card
      title={
        <span className="title-wrap">
          <div className="title">活力党组织指数</div>
          <div>
            <RadioGroup
              value={searchType}
              buttonStyle="solid"
              onChange={({ target: { value } }) => {
                setDateVal(moment().subtract(1,'months'));
                setSearchType(value);
              }}
            >
              <Radio.Button value="month">月度</Radio.Button>
              <Radio.Button value="year">年度</Radio.Button>
            </RadioGroup>
            {searchType === "year" ? (
              <Select
                value={Number(dateVal.format("YYYY"))}
                onChange={(val) => setDateVal(moment(String(val)))}
              >
                {yearList.map((year) => (
                  <Option key={year} value={year}>
                    {year}
                  </Option>
                ))}
              </Select>
            ) : (
              <MonthPicker
                allowClear={false}
                value={dateVal}
                disabledDate={(current) => current > moment().subtract(1,'months')}
                onChange={(val) => setDateVal(val)}
              />
            )}
          </div>
        </span>
      }
      bordered={false}
      className="cardList-wrap"
    >
      <Table bordered columns={columns} dataSource={data} pagination={false} style={{ width: '100%' }} />
    </Card>
  );
};
export default TableIndex;
