.person-modal-new  {
  .ant-form-item {
    .ant-form-item-control-wrapper{
      width: 120px;
      input{
        padding: 4px 11px;
      }
    }
    .form-btn{
      width: 90px;
    }
  }
  .item-container_first .ant-form-item-label{
    width: 70px;
  }
  .item-container_two .ant-form-item-label{
    width: 54px;
  }
  .form-item_btn{
    padding-top: 3px;
    .ant-form-item-children{
      display: flex;
      .form-btn{
        padding:0 30px;
        margin:0 10px;
      }
    }
  }
  .item-container{
    margin-bottom: 15px;
    .ant-select-selection__placeholder,input::-webkit-input-placeholder{
      font-size: 12px;
    }
    .ant-form-item-with-help{
      margin-bottom: 0;
      .ant-form-item-control .ant-form-explain{
        position: absolute;
        width: 160px;
      }
    }
    .form-item_age{
      .ant-form-item-control-wrapper{
        width: 60px;
        input{
          padding: 4px;
          text-align: center;
        }
      }
      .ant-input-group-addon{
        padding: 0 5px;
      }
    }
    .age_one {
      margin-right: 8px;
      .ant-form-item-label{
        width: 54px;
      }
    }
    .item-only{
      margin-left: 15px;
      .ant-form-item-control-wrapper{
        width: 130px;
        text-align: center
      }
    }
    .leader-form_item{
      width: 300px;
      input{
        width: 220px;
      }
    }
  }
}