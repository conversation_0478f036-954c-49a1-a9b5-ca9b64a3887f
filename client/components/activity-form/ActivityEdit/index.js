import React, { Component } from "react";
import {
  Row,
  Col,
  Radio,
  InputNumber,
  DatePicker,
  Upload,
  Form,
  AutoComplete,
  Spin
} from "antd";
import adaptiveLayout from "../gridLayout";
import DatePickerLocale from "config/DatePickerLocale.json";
import "./index.less";
import InputTips from "../sub-components/InputTips";
import { fileHost, activityHost } from "apis/config";
import { headers } from "client/tool/axios";
import moment from "moment";
import { fileDownload } from 'client/components/file-download';
const RadioGroup = Radio.Group;
const FormItem = Form.Item;

const personnelRangeOption = [
  { label: "开放互动", value: 1 },
  { label: "指定参加人员名单", value: 2 },
  { label: "指定参加组织人员", value: 3 }
];
const personnelOption = [
  { label: "不限制", value: 0 },
  { label: "有限制", value: 1 }
];
const visibleOption = [
  { label: "所有人员可见", value: 0 },
  { label: "参加人员可见", value: 1 }
];

class ActivityEdit extends Component {
  constructor() {
    super();
    this.state = {
      personnelRange: 1,
      personnel: 0,
      org: 1,
      visible: 1,
      day: 86400
    };
    this.onChangeStartDate = this._onChangeStartDate.bind(this);
    this.onChangeEndDate = this._onChangeEndDate.bind(this);
    this.onChangeRegisterStartDate = this._onChangeRegisterStartDate.bind(this)
    this.onChangeRegisterEndDate = this._onChangeRegisterEndDate.bind(this)
  }
  componentDidMount() {
    const { handleEvents, content } = this.props;
    // 初始下载模板
    handleEvents.getTemplate();
  }
  _onChangeStartDate(startValue) {
    const { form } = this.props;
    const { getFieldValue } = form;
    const endValue = getFieldValue("end_time");
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > endValue.valueOf();
  }

  _onChangeEndDate(endValue) {
    const { form } = this.props;
    const { getFieldValue } = form;
    const startValue = getFieldValue("start_time");
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf() + this.state.day;
  }
  // 互动报名开始时间
  _onChangeRegisterStartDate(startValue) {
    const { form } = this.props;
    const { getFieldValue } = form;
    const endValue = getFieldValue("end_time");
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > endValue.valueOf();
  }
  // 互动报名结束时间
  _onChangeRegisterEndDate(startValue) {
    const { form } = this.props;
    const { getFieldValue } = form;
    const endValue = getFieldValue("end_time");
    const signValue = getFieldValue("sign_start_time") || moment(new Date())
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > (endValue.valueOf() + this.state.day) || signValue.valueOf() > startValue.valueOf();
  }

  render() {
    const {
      handleEvents,
      content,
      form,
      preview,
      hideCost
    } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    // 关联限制条件字典
    const personnelEnum = {
      1002: {
        name: "性别",
        data: content.sexList
      },
      1010: {
        name: "证件类型",
        data: content.cardTypeList
      },
      1013: {
        name: "政治面貌",
        data: content.politicalList
      },
      1009: {
        name: "技术等级",
        data: content.skillList
      },
      1022: {
        name: "党组织",
        data: content.partyList
      },
      1023: {
        name: "团组织",
        data: content.groupList
      },
      1024: {
        name: "工会组织",
        data: content.unionList
      },
      1025: {
        name: "妇女组织",
        data: content.womenList
      }
    };

    const personnelRender = () => {
      return (
        <div>
          {Object.keys(content.editRule).map(r => {
            let val = "";
            content.editRule[r]
              .split(",")
              .map(Number)
              .map((p, i) => {
                const tmp = personnelEnum[r].data.find(x => {
                  return p === x.op_key;
                });
                if (tmp) {
                  val += tmp.op_value + "、";
                }
              });
            return (
              <div className={"personnel-block"}>
                {personnelEnum[r].name}：{val.substr(0, val.length - 1)}
              </div>
            );
          })}
        </div>
      );
    };
    const props = {
      action: `${activityHost}/activity/limit/user-scope`,
      name: "upfile",
      showUploadList: false,
      data: {
        task_id: content.task_id || "",
        type: 1
      },
      onStart: handleEvents.onStart,
      onSuccess: handleEvents.upUserScopeSuccess,
      headers: headers()
    };

    const replaceAdd = (val) => {
      // console.log('val----', val);
      return val;
      // return val.replace(/\+/g, ' ');
    }
    return (
      <div className="activity-edit">
        {this.props.isPhysical ? (
          <div>
            <FormItem
              label="互动时间"
              {...adaptiveLayout.formWrapper}
              wrapperCol={{
                md: { span: 18 },
                lg: { span: 17 },
                xl: { span: 13 }
              }}
            >
              {getFieldDecorator("start_time", {
                initialValue: content.start_time
                  ? moment(replaceAdd(content.start_time))
                  : null,
                rules: [{ required: true, message: "请选择互动开始时间" }]
              })(
                <DatePicker
                  disabledDate={this.onChangeStartDate}
                  showTime={{ defaultValue: moment('00:00', 'HH:mm') }}
                  locale={DatePickerLocale}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择互动开始时间"
                  disabled={preview}
                />
              )}
              <span style={{ margin: "auto 5px" }}>至</span>
              {getFieldDecorator("end_time", {
                initialValue: content.end_time
                  ? moment(replaceAdd(content.end_time))
                  : null,
                rules: [{ required: true, message: "请选择互动结束时间" }]
              })(
                <DatePicker
                  disabledDate={this.onChangeEndDate}
                  showTime={{ defaultValue: moment('23:59:59', 'HH:mm:ss') }}
                  locale={DatePickerLocale}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择互动结束时间"
                  disabled={preview}
                />
              )}
            </FormItem>
            <FormItem
              label="报名时间"
              {...adaptiveLayout.formWrapper}
              wrapperCol={{
                md: { span: 18 },
                lg: { span: 17 },
                xl: { span: 13 }
              }}
            >
              {getFieldDecorator("sign_start_time", {
                initialValue: content.offline.sign_start_time
                  ? moment(replaceAdd(content.offline.sign_start_time))
                  : null,
                rules: [{ required: true, message: "请选择报名开始时间" }]
              })(
                <DatePicker
                  showTime={{ defaultValue: moment('00:00', 'HH:mm:ss') }}
                  disabledDate={this.onChangeRegisterStartDate}
                  locale={DatePickerLocale}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择报名开始时间"
                  disabled={preview}
                />
              )}
              <span style={{ margin: "auto 5px" }}>至</span>
              {getFieldDecorator("sign_end_time", {
                initialValue: content.offline.sign_end_time
                  ? moment(replaceAdd(content.offline.sign_end_time))
                  : null,
                rules: [{ required: true, message: "请选择报名结束时间" }]
              })(
                <DatePicker
                  showTime={{ defaultValue: moment('23:59:59', 'HH:mm:ss') }}
                  disabledDate={this.onChangeRegisterEndDate}
                  locale={DatePickerLocale}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择报名结束时间"
                  disabled={preview}
                />
              )}
            </FormItem>
            {/*
            <FormItem {...adaptiveLayout.formWrapper} label="互动组织部门">
              <InputTips
                max={30}
                container={container}
                text={getFieldValue(formFields.eventOrganizationDepartment)}
              >
                {getFieldDecorator(formFields.eventOrganizationDepartment, {
                  initialValue:
                    activityFormState.eventOrganizationDepartment.name,
                  rules: [
                    {
                      max: 30,
                      message: "最多输入30个汉字"
                    },
                    {
                      required: true,
                      message: "请输入互动组织部门"
                    }
                  ]
                })(
                  <AutoComplete
                    allowClear
                    backfill={false}
                    disabled={preview}
                    placeholder="请输入互动组织部门"
                    filterOption={(inputValue, option) =>
                      option.props.children.indexOf(inputValue) >= 0
                    }
                    onChange={handleEvents.changeDepartment}
                  >
                    {activityFormState.departmentData.map(item => (
                      <AutoComplete.Option key={item.department_id}>
                        {item.name}
                      </AutoComplete.Option>
                    ))}
                  </AutoComplete>
                )}
              </InputTips>
            </FormItem>
              */}
          </div>
        ) : (
            ""
          )}

        <FormItem
          label="参加人员范围"
          {...adaptiveLayout.formWrapper}
          wrapperCol={{ md: { span: 18 }, lg: { span: 17 }, xl: { span: 13 } }}
        >
          {getFieldDecorator("limit_user_scope", {
            initialValue: content.limit_user_scope
          })(
            <RadioGroup
              onChange={handleEvents.personnelRangeRadio}
              options={personnelRangeOption}
              disabled={preview}
            />
          )}
        </FormItem>
        {content.limit_user_scope === 2 ? (
          <Row className="ant-form-item">
            <Col
              {...adaptiveLayout.formWrapper.labelCol}
              className="ant-form-item-label"
            />
            <Col
              {...adaptiveLayout.formWrapperCol}
              className="custom-form-item-col"
            >
              <div className={"quote"}>
                <Spin spinning={content.upload}>
                  {content._user_count === 0 ? (
                    <span>
                      还未指定人员
                      {preview ? (
                        ""
                      ) : (
                          <Upload {...props} style={{ marginLeft: 20 }}>
                            <a href="javascript:;">上传互动人员名单</a>
                          </Upload>
                        )}
                    </span>
                  ) : (
                      <span style={{ marginLeft: 20 }}>
                        指定了
                      <span style={{ color: "red" }}>
                          {content._user_count}
                        </span>
                        名参加人员
                      {preview ? (
                          ""
                        ) : (
                            <Upload {...props}>
                              <a href="javascript:;">更新互动人员名单</a>
                            </Upload>
                          )}
                      </span>
                    )}{" "}
                  <a
                    onClick={()=>{
                      fileDownload(`/file/file/download/${content.downloadModelName}`)
                    }}
                    href="javascript:void(0);"
                    style={{ marginLeft: 20 }}
                  >
                    下载模板
                  </a>
                </Spin>
              </div>
            </Col>
          </Row>
        ) : (
            ""
          )}

        {/* 指定了参加组织 */}
        {content.limit_user_scope === 3 ? (
          <Row className="ant-form-item">
            <Col
              {...adaptiveLayout.formWrapper.labelCol}
              className="ant-form-item-label"
            />
            <Col
              {...adaptiveLayout.formWrapperCol}
              className="custom-form-item-col"
            >
              <div className={"quote"}>
                {content._org_count === 0 ? (
                  "还未指定组织"
                ) : (
                    <span>
                      指定了
                    <span style={{ color: "red" }}>{content._org_count}</span>
                      个组织
                  </span>
                  )}
                {preview ? (
                  ""
                ) : (
                    <a href="javascript:;" onClick={handleEvents.chooseOrg}>
                      选择组织
                  </a>
                  )}
              </div>
            </Col>
          </Row>
        ) : (
            ""
          )}
        <FormItem
          label="参加人员限制："
          {...adaptiveLayout.formWrapper}
          wrapperCol={{ md: { span: 18 }, lg: { span: 17 }, xl: { span: 13 } }}
        >
          <RadioGroup
            options={personnelOption}
            onChange={handleEvents.personnelRadio}
            disabled={preview || content.limit_user_scope !== 3}
            value={content.limit_user}
          />
        </FormItem>
        {content.limit_user === 0 || content.limit_user_scope === 2 ? (
          ""
        ) : (
            <Row className="ant-form-item">
              <Col
                {...adaptiveLayout.formWrapper.labelCol}
                className="ant-form-item-label"
              />
              <Col
                {...adaptiveLayout.formWrapperCol}
                className="custom-form-item-col"
              >
                {Object.keys(content.editRule).length === 0 ? (
                  <div className={"quote"}>
                    还未选择限制条件{" "}
                    <a href="javascript:;" onClick={handleEvents.choosePerson}>
                      开始选择
                  </a>
                  </div>
                ) : (
                    <div className={"quote"}>
                      选定的限制条件{" "}
                      {preview ? (
                        ""
                      ) : (
                          <a href="javascript:;" onClick={handleEvents.choosePerson}>
                            编辑
                    </a>
                        )}
                      {personnelRender()}
                    </div>
                  )}
              </Col>
            </Row>
          )}
        <FormItem
          // label="互动可见"
          label="可见范围"
          {...adaptiveLayout.formWrapper}
          wrapperCol={{ md: { span: 18 }, lg: { span: 17 }, xl: { span: 13 } }}
        >
          {
            <RadioGroup
              options={visibleOption}
              onChange={handleEvents.visibleRadio}
              value={content.limit_public}
              disabled={preview || content.limit_user_scope === 1}
            />
          }
        </FormItem>
        {this.props.isPhysical || hideCost ? (
          ""
        ) : (
            <FormItem
              label="每次参加消耗积分"
              {...adaptiveLayout.formWrapper}
              wrapperCol={{
                md: { span: 18 },
                lg: { span: 17 },
                xl: { span: 13 }
              }}
            >
              {getFieldDecorator("cost", {
                initialValue: content.cost
              })(
                <InputNumber
                  max={99999}
                  style={{ background: "#F7F8F9", width: 164 }}
                  placeholder={"请输入数字"}
                />
              )}
            </FormItem>
          )}
      </div>
    );
  }
}

export default ActivityEdit;
