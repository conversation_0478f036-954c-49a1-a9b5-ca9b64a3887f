.table-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  border-spacing: 0px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: PingFang SC-Regular;

  .expand-click {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .expand-text {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 22px;
      line-height: 1;
      color: #d33625;
      cursor: pointer;
    }

    .expand-icon {
      margin-top: 4px;
      width: 20px;
      height: 20px;
      background: url("../../images/expand.png") no-repeat center center / 100%
        100%;
      cursor: pointer;
    }

    .rotate {
      transform: rotateZ(-180deg);
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      border: 1px solid #e9e9e9;
      text-align: center;
    }
  }

  .table-header-td {
    display: flex;
    align-items: center;
    height: 100%;
    span {
      position: relative;
    }
  }

  td,
  col {
    padding: 14px 10px;
  }

  colgroup {
    display: table-row;
    vertical-align: inherit;
    border-color: inherit;

    col {
      padding: 0 10px;
    }
  }

  col {
    display: table-cell;
    vertical-align: inherit;
  }

  thead {
    td {
      background: #f3f3f3;
      text-align: center;
      font-size: 20px;
      line-height: 23px;
      font-weight: 400;
      color: #3d3d3d;
    }
  }

  tbody {
    font-weight: 400;
    & > tr {
      margin: 2px;
      border-top: 15px;
      border-color: transparent;
      background-color: #ffffff;
      font-size: 18px;
      color: #3d3d3d;
    }
    .ellipsis {
      display: -webkit-box;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }

  .body-scroll {
    overflow: scroll;
  }

  &-body {
    flex: 1;

    table {
      margin-top: -4px;
    }

    a {
      text-decoration: none;
      outline: none;
      cursor: text;
      &:hover,
      &:visited,
      &:link,
      &:active {
        color: inherit;
      }
    }

    .a-pointer {
      cursor: pointer;
    }

    &-nodata {
      width: 100%;
      padding-top: 20px;
      text-align: center;
      vertical-align: middle;
      color: #999;
    }

    .table-column-max {
      color: #209b34 !important;
      font-weight: bold;
    }

    .table-column-min {
      color: #ee391f !important;
      font-weight: bold;
    }
  }

  &::-webkit-scrollbar {
    width: 5px;
    height: 10px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #cbf9ff;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
    border-radius: 10px;
  }

  & ::-webkit-scrollbar {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.sort-icon {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 13px;
  cursor: pointer;

  &::before {
    content: "";
    position: absolute;
    height: 0px;
    width: 0px;
    border-width: 0 7px 7px 7px;
    border-style: solid;
    border-color: transparent transparent #999999 transparent;
  }

  &::after {
    margin-top: 2px;
    position: absolute;
    top: 50%;
    content: "";
    height: 0px;
    width: 0px;
    border-width: 7px 7px 0 7px;
    border-style: solid;
    border-color: #999999 transparent transparent transparent;
  }
}

.sort-active-top {
  &::before {
    border-color: transparent transparent #2462ff transparent;
  }
}

.sort-active-bottom {
  &::after {
    border-color: #2462ff transparent transparent transparent;
  }
}

.active {
  background-color: rgba(235, 91, 84, 0.07) !important;
}

.custom-block {
  display: inline-block;
  width: 32px;
  height: 12px;
}
