.batch-addition {
  display: flex;
  flex-direction: column;
  .index-view-container {
    .ant-spin-text {
      padding: 10px;
      background-color: #ddd;
    }
    flex: auto;
    display: flex;
    .main-flex-wrap.ant-layout {
      flex-grow: 1;
      &>div {
        display: flex;
      }
    }
    position: relative; //border: 1px solid red;
    .main-left-menu {
      float: none;
      height: auto;
      flex: auto;
    }
    &:after {
      display: block;
      content: '';
      clear: both;
    } // .main-left-menu {
    //   height: 680px;
    // }
    .right-content-wrapper {
      position: relative;
      .ant-table-fixed-header .ant-table-scroll .ant-table-header {
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
    }
  }
}

.batch-footer {
  margin-top: 30px;
}

.batch_addition_uploadPanel {
  width: 80%;
  display: block;
  margin: 0 auto 20px;
  &:focus {
    text-decoration: none;
  }
  .ant-upload {
    width: 100%;
  }
  .batch_addition_uploadBtn {
    width: 100%;
    height: 70px;
    background: #FFF1F0;
    border-radius: 5px;
    border: 1px solid #FFCCC7;
    display: flex;
    justify-content: center;
    cursor: pointer;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    i,
    span {
      line-height: 66px !important;
      font-size: 16px;
      color: #FF7875;
    }
    i {
      font-size: 32px;
    }
  }
}