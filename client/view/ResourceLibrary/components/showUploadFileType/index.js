import React from "react";
import SelfIcon from "components/self-icon";
import { <PERSON><PERSON>, Modal } from "antd";
import { CDN } from "apis/config";
import "./style.less";
import PropTypes from "prop-types";
import FileDownload from "client/components/file-download";
/**
 *
 * @param data
 * @type [{size: 1000byte, name: 文件名, file_name: 文件原始名, path: 文件路径}] 导出的时候会新加 一个 is_del: 0
 *
 * @param updateState
 * @type func 更新父级组件的 state
 *
 * @param loading
 * @type boolean 上传中
 *
 * @param isDelete
 * @type boolean true 删除, false 下载
 *
 */

class ShowUploadFileType extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
      modalProps: {},
    };
  }

  async postFilePreview(params = {}) {
    this.setState({
      modalVisible: true,
      modalProps: params,
    });
  }

  render() {
    const { modalVisible, modalProps } = this.state;
    const {
      data = [],
      updateState,
      loading = false,
      isDelete = false,
      canPreview = false,
      hiddenFileSize = false,
    } = this.props;
    const fileData = data || [];

    // 删除文件
    const deleteFile = (index) => {
      if (data && Array.isArray(data) && data.length !== 0) {
        data.splice(index, 1);
        updateState([...data]);
      }
    };

    const backStr = (fileName) => {
      const fileInfo = fileName.split(".");
      const fileType = fileInfo[fileInfo.length - 1];
      switch (fileType) {
        case "txt":
          return "txt";
        case "jpg":
        case "jpeg":
          return "jpg";
        case "png":
          return "png";
        case "xlsx":
        case "xls":
          return "ex";
        case "mp3":
          return "mp3";
        case "mp4":
          return "mp1";
        case "flv":
          return "flv";
        case "mov":
          return "mov";
        case "mkv":
          return "mkv";
        case "avi":
          return "avi";
        case "doc":
        case "docx":
          return "doc";
        case "ppt":
          return "ppt";
        default:
          return "wenjian";
      }
    };

    return (
      <div className="showUploadFileType">
        {fileData.map((item, index) => {
          const video = /\.(mp4|flv|mov|mkv|avi)$/i.test(item.path);
          const img = /\.(jpg|jpeg|png)$/i.test(item.path);
          return (
            <div
              className={`iconWrap ${backStr(
                item.name || item.file_name || item.filename
              )}`}
              key={item.topic_file_id || index}
              style={{
                display: `${item.is_del === 1 ? "none" : "flex"}`,
              }}
            >
              <div className="iconInfo">
                <SelfIcon
                  type={`gsg-${backStr(
                    item.name || item.file_name || item.filename
                  )}`}
                  className="icon"
                />
              </div>
              <div className="fileInfo">
                <div className="fileName">
                  <span
                    className="fileNameWrapper"
                    title={item.file_name || item.filename}
                  >
                    {item.file_name || item.filename}
                  </span>
                  {canPreview &&
                    (video || img) &&
                    (item.id || item.path || item.file_url) && (
                      <Button
                        type="link"
                        onClick={() => {
                          this.postFilePreview({
                            file_id: item.id || null,
                            file_path: item.path || item.file_url || null,
                            file_type: video ? "video" : "img",
                          });
                        }}
                      >
                        查看
                      </Button>
                    )}

                  <FileDownload
                    filePath={`/file/file/download/${
                      item.id || item.file_id || item.name
                    }`}
                    fileName={item.file_name || item.name || item.filename}
                    type="link"
                    btnName="下载"
                  />
                  {isDelete && (
                    <Button type="link" onClick={() => deleteFile(index)}>
                      删除
                    </Button>
                  )}
                </div>
                {!hiddenFileSize ? (
                  <div className="fileSize">
                    {`${Math.round(item.size / 1000)}KB` || "未知大小"}
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          );
        })}
        <Modal
          footer={null}
          zIndex={1005}
          width={"fit-content"}
          maskClosable={false}
          className="upload-preview-modal"
          visible={modalVisible}
          onCancel={() => this.setState({ modalVisible: false })}
        >
          {modalProps.file_type === "video" ? (
            <video
              className="upload-preview-modal-video"
              src={`${CDN}/${modalProps.file_path}`}
              controls="controls"
              style={{ width: "100%" }}
            >
              您的浏览器不支持 video 标签。
            </video>
          ) : (
            <img
              className="upload-preview-modal-img"
              src={`${CDN}/${modalProps.file_path}`}
              alt=""
            />
          )}
        </Modal>
      </div>
    );
  }
}

ShowUploadFileType.propTypes = {
  data: PropTypes.array,
  updateState: PropTypes.func,
  changeLoadingStatus: PropTypes.func,
  loading: PropTypes.bool,
  isDelete: PropTypes.bool,
  canPreview: PropTypes.bool,
  hiddenFileSize: PropTypes.bool,
};

ShowUploadFileType.defaultProps = {
  data: [],
  updateState: () => {},
  loading: false,
  isDelete: false,
  canPreview: false,
  hiddenFileSize: false,
};

export default ShowUploadFileType;
