import React, { useState, useEffect } from "react";
import SearchHeader from "components/search-header";
import {
  Button,
  Form,
  Input,
  Select,
  Table,
  Switch,
  Tooltip,
  Modal,
  message,
  TreeSelect,
} from "antd";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import {
  findByWhereResource,
  findCategoryList,
  removeResource,
  publishResource,
  findAllSources,
  publishShowStatus,
} from "apis/resource-library";
import "./index.less";

const { Item } = Form;
const { Option } = Select;
const tagsList = ["市内", "市外"];
const { root_oid, oid } = JSON.parse(sessionStorage.getItem("userInfo") || {});
const DocumentManage = (props) => {
  const {
    history,
    form: { getFieldDecorator, getFieldsValue },
  } = props;

  const [delList, setDelList] = useState([]);
  const [selOrg, setSelOrg] = useState([]);
  const [total, setTotal] = useState(0);
  const [catagoryId, setCategoryId] = useState(null);
  const [categoryList, setCategoryList] = useState([]);
  const [sourcesList, setSourcesList] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [orgModalVisible, setOrgModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState({});
  const [query, setQuery] = useState({
    page: 1,
    page_size: 10,
  });

  useEffect(() => {
    _findCategoryList();
    _findAllSources();
  }, []);

  useEffect(() => {
    initDataSource();
  }, [selOrg, query, searchQuery]);

  // 格式化分类数据，选择框只展示一二三级
  const formatData = (value) => {
    value.title = value.resource_category_name;
    value.value = value.resource_category_id;
    value.key = value.resource_category_id;
    if (value.children) {
      value.children.forEach(formatData);
    }
  };

  const initDataSource = () => {
    findByWhereResource({
      ...query,
      ...searchQuery,
      provider_org_id: selOrg[0] ? selOrg[0].org_id : null,
      resource_type: 4,
    }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      setTotal(res.total);
      setDataSource(res.data);
    });
  };

  // 获取来源
  const _findAllSources = () => {
    findAllSources({
      resource_type: 4,
      view_type: 1,
      provider_org_id: selOrg.length ? selOrg[0].org_id : null,
    }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      setSourcesList(res.data);
    });
  };

  // 获取分类
  const _findCategoryList = () => {
    findCategoryList({ category_type: 4 }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(res.message);
        return;
      }
      res.data.forEach(formatData);
      setCategoryList(res.data);
    });
  };

  const onHandleDelect = (isSingle = false, id) => {
    Modal.confirm({
      title: "提示",
      content: isSingle ? "确认删除这条数据吗？" : "确认批量删除选中数据吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        removeResource(isSingle ? [id] : delList).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error("删除失败");
            return;
          }
          message.success("删除成功");
          initDataSource();
        });
      },
    });
  };

  const onHandleSubmit = () => {
    setSearchQuery({ ...searchQuery, ...getFieldsValue() });
  };

  const onHandlePublish = (row) => {
    Modal.confirm({
      title: "提示",
      content:
        row.status === 1
          ? "取消公开后信息提供单位党员可查看， 确认取消公开这条数据吗？"
          : "公开后全系统党员可查看，确认公开这条数据吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        publishResource({
          resource_library_id: row.resource_library_id,
          status: row.status === 1 ? 2 : 1,
        }).then(({ data: res }) => {
          if (res.code !== 0) {
            message.error(`${row.status === 1 ? "取消" : ""}公开失败`);
            return;
          }
          message.success(`${row.status === 1 ? "取消" : ""}公开成功`);
          initDataSource();
        });
      },
    });
  };

  const onHandleChangeStatus = (checked, row) => {
    publishShowStatus({
      resource_library_id: row.resource_library_id,
      show_status: !checked ? 0 : 1, //1显示 0隐藏
    }).then(({ data: res }) => {
      if (res.code !== 0) {
        message.error(`${!checked ? "隐藏" : "显示"}失败`);
        return;
      }
      message.success(`${!checked ? "隐藏" : "显示"}成功`);
    });
  };

  const orgModalProps = {
    visible: orgModalVisible,
    dataSource: selOrg,
    hideModal: () => setOrgModalVisible(false),
    radio: true,
    hasRoot: false,
    onlyDirectSub: true,
    rootOrgId: oid,
    loadOrganizeData: (data) => {
      setOrgModalVisible(false);
      setSelOrg(data);
    },
  };

  const columns = [
    {
      title: "场地名称",
      dataIndex: "name",
      key: "name",
      width: 308,
      render: (text) => {
        return (
          <Tooltip title={text}>
            <div className="document-manage-table-title">{text}</div>
          </Tooltip>
        );
      },
    },
    {
      title: "场地分类",
      dataIndex: "resource_category_name",
      key: "resource_category_name1",
    },
    {
      title: "场地地址",
      dataIndex: "contact_details",
      key: "contact_details",
      // align: "center",
      width: 308,
      render: (text) => {
        return (
          <Tooltip title={text}>
            <div className="document-manage-table-contactDetails">{text}</div>
          </Tooltip>
        );
      },
    },
    {
      title: "场地人数",
      dataIndex: "venue_nums",
      key: "venue_nums",
      align: "center",
      render: (text) => {
        return (
          <Tooltip title={text}>
            <div className="document-manage-table-title">
              {text == 0 ? "-" : `${text}人`}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: "提供服务",
      dataIndex: "tags",
      key: "tags",
      render: (text) => {
        const tags = text ? text.split(",") : [];
        return tags.map((item) => {
          return <span className="tags">{item}</span>;
        });
      },
    },
    {
      title: "排序",
      dataIndex: "seq",
      key: "seq",
      align: "center",
      render: (text) => text || "-",
    },
    {
      title: "上传时间",
      dataIndex: "create_time",
      key: "create_time",
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operate",
      width: "20%",
      key: "operate",
      align: "center",
      render: (text, row) => {
        return (
          <div>
            <Button type="link" onClick={() => onHandlePublish(row)}>
              {row.status === 1 ? "取消公开" : "公开"}
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push("/field-library/document/detail", row)
              }
            >
              详情
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push("/Field-manage/Field-add", {
                  categoryList,
                  type: 2,
                  resource_library_id: row.resource_library_id,
                })
              }
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => onHandleDelect(true, row.resource_library_id)}
            >
              删除
            </Button>
            <Button
              type="link"
              onClick={() =>
                history.push("/field-library/appointment-management", {
                  ...row,
                })
              }
            >
              预约管理
            </Button>
          </div>
        );
      },
    },
  ];

  const pagination = {
    total,
    current: query.page,
    pageSize: query.page_size,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100", "200"],
    showTotal: ($total, range) =>
      `总计${$total}条，当前展示第${range[0]}至${range[range.length - 1]}条`,
    onChange: (page) => setQuery({ ...query, page }),
    onShowSizeChange: (_, page_size) => setQuery({ ...query, page_size }),
  };

  const tableProps = {
    columns,
    dataSource,
    pagination,
    rowKey: "resource_library_id",
    bordered: true,
  };

  return (
    <div className="document-manage">
      <SearchHeader
        onBack={false}
        title={"场地库管理"}
        renderRight={() => {
          return (
            <div className="document-manage-header-button">
              <Button
                type="default"
                className="upload-btn"
                icon="plus"
                onClick={() =>
                  history.push("/Field-manage/Field-add", {
                    categoryList,
                    type: 1,
                  })
                }
              >
                新增资源
              </Button>
            </div>
          );
        }}
      />
      <div className="document-manage-content">
        <div className="document-manage-content-search">
          {root_oid === oid && (
            <div className="document-manage-content-search-org">
              信息提供：{selOrg[0] ? selOrg[0].org_name : ""}
              <Button type="link" onClick={() => setOrgModalVisible(true)}>
                选择组织
              </Button>
              <OrganizeModal {...orgModalProps} />
            </div>
          )}
          <Form layout="inline" className="document-manage-content-search-form">
            <Item label="场地名称">
              {getFieldDecorator("key_word", {
                initialValue: null,
              })(
                <Input
                  allowClear
                  placeholder="请输入场地名称关键词"
                  maxLength={150}
                />
              )}
            </Item>
            <Item label="场地分类">
              {getFieldDecorator("resource_category_id", {
                initialValue: catagoryId,
              })(
                <TreeSelect
                  allowClear
                  style={{ width: "100%" }}
                  dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                  treeData={categoryList}
                  placeholder="请选择场地分类"
                  treeDefaultExpandAll
                  onChange={(val) => setCategoryId(val)}
                />
              )}
            </Item>

            <Item label="提供服务">
              {getFieldDecorator("tags", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择服务" mode="multiple" allowClear>
                  {tagsList &&
                    tagsList.map((item) => {
                      return <Option value={item}>{item}</Option>;
                    })}
                </Select>
              )}
            </Item>
            <Item label="场地地址">
              {getFieldDecorator("contact_details", {
                initialValue: null,
              })(
                <Input
                  allowClear
                  placeholder="请输入地址关键词"
                  maxLength={150}
                />
              )}
            </Item>

            <Item label="排序规则">
              {getFieldDecorator("order_type", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择排序规则" allowClear>
                  <Option value={1}>上传时间正序</Option>
                  <Option value={2}>上传时间倒序</Option>
                  <Option value={3}>排序值</Option>
                </Select>
              )}
            </Item>
            <Item label="公开状态">
              {getFieldDecorator("status", {
                initialValue: undefined,
              })(
                <Select placeholder="请选择状态" allowClear>
                  <Option value={1}>已公开</Option>
                  <Option value={2}>未公开</Option>
                </Select>
              )}
            </Item>
            <Button type="primary" onClick={() => onHandleSubmit()}>
              查询
            </Button>
          </Form>
        </div>
        <div className="document-manage-content-table">
          <Table {...tableProps} />
        </div>
      </div>
    </div>
  );
};

export default Form.create()(DocumentManage);
