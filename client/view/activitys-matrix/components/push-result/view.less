
.com-push-result{


  // .status-icon.disable{
  //   background-color: #999999;
  // }
  // .status-icon.disable.right::after{
  //   border-left-color: #999999;
  // }
  // .status-icon.disable.left::after{
  //   border-right-color: #999999;
  // }


  .ant-spin-container .ant-table-pagination.ant-pagination{
    float: left;

    .ant-pagination-options{
      margin-left: 15px;
      .ant-pagination-options-size-changer{
        margin-right:15px;
      }
    }
  }


  .ant-pagination-total-text{
    float: right;margin: 1px 0 0 15px;
  }


  &.box{
    min-height: 540px;
  }


  .box-hd{
    // font-size:18px;
    font-family:MicrosoftYaHei;
    // font-weight:bold;
    line-height: 30px;
    padding: 8px;
    padding-top: 0px;

    .success{
      background-color: #4FC136;
    }

    span{
      text-align: center;
      display: inline-block;
      min-width: 1.7em;
      line-height: 1.5em;
      background-color: #999999;
      padding:0px;
      box-sizing: content-box;
      border-radius: 3px;
      color: #fff;
      // font-weight: lighter;
    }


  }

}
