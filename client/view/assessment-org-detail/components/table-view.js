import React from "react";
import { Table, message as Message } from "antd";
import PropTypes from "prop-types";

const TableView = props => {
  const {
    dataSource,
    tableLoading,
    dispatch
  } = props;

  const columns = [
    {
      title: "考核周期",
      align: "center",
      dataIndex: "period",
      color: "#333",
      width: 90,
      render(text, record = {}, index) {
        let result = text;
        // 考核周期. 1:季度　2:月　3:年
        if (text === 1) {
          result = `第${record.quarter}季度`;
        }else if(text == 2){
          result = `第${record.month}月`;
        }
        return result;
      }
      // style: "lineEllipsis"
      // render: (val, record, index) => index + 1
    },
    {
      // title: "会议名称",
      title: "任务名称",
      dataIndex: "eval_name",
      align: "left",
      // width: 300,
      render(text, record, index) {
        return text || "--";
      }
      // maxWidth: "35.02%"
    },
    {
      // title: "会议类型",
      title: "扣分类型",
      dataIndex: "deduct_type",
      align: "center",
      width: 120,
      render(text, record, index) {
        // 扣分类型. 1 逾期扣分 2 审核扣分 3 查处扣分 4 数据采集扣分
        return text === 1 ? "逾期扣分"
          : text === 2 ? "审核扣分"
            : text === 3 ? "查处扣分"
              : text === 4 ? "数据采集扣分"
                : text;
      }
    },
    {
      // title: "召开时间",
      title: "扣分（分）",
      dataIndex: "score",
      align: "center",
      width: 120,
    },
    {
      title: "产生时间",
      dataIndex: "deduct_time",
      align: "center",
      width: 140,
      // render: (val, record) => {
      //   return statusRender(record)[0];
      // }
    },
    {
      title: "操作 ",
      dataIndex: "handler",
      align: "center",
      width: 120,
      // width: "9.09%",
      render: (text, record = {}, index) => {
        return (
          <a href="javascript:void(0)" onClick={() => {
            // console.log(record);
            if (record && record.deduct_log_id) {
              // 考核一期，非数据统计类详情
              // console.log(record);
              dispatch({
                type: "assessmentOrgDetail/getCountDeductLogDetail",
                payload: {
                  year: record.year,
                  time: record.quarter,
                  eval_id: record.eval_id,
                  // TODO:手动造数据
                  org_id: record.org_id,
                  // org_id: 3,
                  deduct_type: record.deduct_type,
                  deduct_log_id: record.deduct_log_id
                }
              }).then((data) => {
                // console.log(data);
              }).catch(
                (error) => {
                  console.error(error);
                }
              );
            } else {
              Message.error("扣分记录获取失败");
              dispatch({
                type: "assessmentOrgDetail/save",
                payload: {
                  tableLoading: false
                }
              });
            }
            // dispatch({
            //   type: "assessmentOrgDetail/save",
            //   payload: {
            //     modalVisible: true,
            //     deductLogId: record.deduct_log_id
            //   }
            // });
          }}>
            查看
          </a>
        );
      }
    }
  ];

  const tableProps = {
    columns,
    bordered: true,
    dataSource: dataSource || [],
    loading: tableLoading,
    pagination: false,
    rowKey: "deduct_log_id",
    scroll: {
      y: 500,
      x: true
    },
  };
  // setColums
  return (
    <div>
      {/* <hr className="line-sty" /> */}
      <Table {...tableProps} />
    </div>
  );
};

TableView.propTypes = {
  dataSource: PropTypes.array,
  tableLoading: PropTypes.bool,
  dispatch: PropTypes.func
};
TableView.defaultProps = {
  dataSource: [],
  tableLoading: false,
  dispatch: () => { }
};

export default TableView;
