.activity-list {
  .main {
    padding: 28px;
  }

  .divider-wrapper {
    padding: 0 46px 0 44px;
  }

  .divider-wrapper .divider-text {
    font-size: 16px;
    color: #333;
  }

  .divider-wrapper .ant-divider-inner-text {
    padding: 0 9px 0 0;
  }

  .divider-wrapper .ant-divider.ant-divider-horizontal.ant-divider-with-text-left:before {
    display: none;
  }

  //.text-remain-input, .text-remain-area {
  //  position: relative;
  //}
  //
  //.text-remain-input input {
  //  padding-right: 140px;
  //}

  //.text-remain-after {
  //  font-size: 12px;
  //  width: 140px;
  //  position: absolute;
  //  text-align: center;
  //}
  //
  //.text-remain-input .text-remain-after {
  //  top: 0;
  //  right: 0;
  //}
  //
  //.text-remain-area textarea {
  //  padding-bottom: 40px;
  //}
  //
  //.text-remain-area .text-remain-after {
  //  bottom: 0;
  //  right: 0;
  //}

  .button {
    width: 115px;
    height: 36px;
  }

  .button.submit-button {
    background-color: #F46E65;
    color: #fff;
    margin-right: 48px;
  }

  .button.cancel-button {
    background-color: #E5E5E5;
    color: #666;
  }

  .buttons-wrapper {
    padding-left: 167px;
    margin: 55px 0 60px 0;
  }
}

//文本框剩余字数提示
.text-remain-tooltip {
  font-size: 12px;
  &.ant-tooltip-placement-bottom,
  &.ant-tooltip-placement-bottomLeft,
  &.ant-tooltip-placement-bottomRight {
    .ant-tooltip-arrow {
      border-bottom-color: rgba(247, 248, 249, 1);
    }
  }
  &.ant-tooltip-placement-left,
  &.ant-tooltip-placement-leftTop,
  &.ant-tooltip-placement-leftBottom {
    .ant-tooltip-arrow {
      border-left-color: rgba(247, 248, 249, 1);
    }
  }
  &.ant-tooltip-placement-top,
  &.ant-tooltip-placement-topLeft,
  &.ant-tooltip-placement-topRight {
    .ant-tooltip-arrow {
      border-top-color: rgba(247, 248, 249, 1);
    }
  }
  &.ant-tooltip-placement-right,
  &.ant-tooltip-placement-rightTop,
  &.ant-tooltip-placement-rightBottom {
    .ant-tooltip-arrow {
      border-right-color: rgba(247, 248, 249, 1);
    }
  }
  .ant-tooltip-content {
    .ant-tooltip-inner {
      background-color: rgba(247, 248, 249, 1);
      color: #999;
    }
  }
}