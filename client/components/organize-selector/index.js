import React, { Component, Fragment } from "react";
import { Table, Pagination } from "antd";
import OrganizeModal from "./sub-components/organize-modal";
import "./index.less";
import propTypes from "prop-types";

import { addKeyToTableDataSource } from "tool/util";

class OrganizeSelector extends Component {
  constructor() {
    super();
    this.state = {
      dataSource: [],
      // total: 50,
      pageSize: 10,
      // current: 1,
      visible: false
    }
    this.showModal = this.showModal.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.removeItem = this.removeItem.bind(this);
    this.exportData = this.exportData.bind(this);
    this.batchAddOrgs = this.batchAddOrgs.bind(this);
  }

  // 从外部收到的默认数据时，需要进行初始化
  componentDidMount() {
    const { inputData = [] } = this.props;
    // console.log(inputData);
    if (inputData && Array.isArray(inputData) && inputData.length !== 0) {
      this.setState({
        dataSource: addKeyToTableDataSource(inputData)
      });
    }
  }

  // 批量添加组织
  batchAddOrgs(inputData) {
    if (inputData && Array.isArray(inputData) && inputData.length !== 0) {
      this.setState({
        dataSource: addKeyToTableDataSource(inputData)
      });
    }
  }

  exportData() {
    const { dataSource } = this.state;
    const output = JSON.parse(JSON.stringify(dataSource));
    if (output && Array.isArray(output) && output.length !== 0) {
      output.forEach((item) => {
        delete item.key;
      })
    }
    return output;
  }

  //从表格删除一个选中的组织
  removeItem(index, org) {
    const { dataSource } = this.state;
    const { getSelectedValues } = this.props;
    dataSource.splice(org.key - 1, 1);
    // 重置表格序号
    addKeyToTableDataSource(dataSource);
    // this.organizeModal.removeItem(org.key - 1, org);
    getSelectedValues && getSelectedValues(dataSource);
    this.setState({
      dataSource
    });
    this.props.onChange(this.props.radio ? (dataSource[0] && dataSource[0].org_id) || undefined : dataSource.map(item => item.org_id))
  }

  showModal() {   
    this.setState({
      visible: true
    });
    const { dataSource } = this.state;
    // console.log(dataSource);
    // 当前如果表格有数据，则将表格数据初始化到组件中
    if (dataSource && Array.isArray(dataSource) && dataSource.length !== 0) {
      this.organizeModal.initData(dataSource);
    }
  }

  hideModal() {
    this.setState({
      visible: false
    });
  }

  render() {
    const _this = this;
    const {
      getSelectedValues,
      canEdit,
      distinguishOwner,
      onlyDirectSub = false,
      radio = false,
      isLeaderGroup,
      hasRoot,
      rootOrgId,
      rootOrgType,
      isFfgr,
      breadcrumbClick,
      selectType // 选择结果展示类型，分为table 和 text
    } = this.props;
    // console.log(rootOrgId, isFfgr);
    const baseColumns = [
      {
        title: "序号",
        dataIndex: "key",
        align: "center",
        width: 90
      },
      {
        title: "组织名称",
        dataIndex: "org_name",
        align: "center",
        width: 580
      }
    ];
    const handlerColumns = [
      {
        title: "操作",
        align: "center",
        width: 210,
        render(text, record, index) {
          return (
            <div>
              <a onClick={() => _this.removeItem(index, record)}>删除</a>
            </div>
          )
        }
      }
    ];
    const columns = canEdit ? [...baseColumns, ...handlerColumns] : [...baseColumns];

    const {
      dataSource,
      // total,
      // current,
      pageSize,
      visible } = this.state;

    const tableProps = {
      bordered: true,
      columns,
      dataSource,
      pagination: {
        size: "small",
        showQuickJumper: true,
        showSizeChanger: true,
        pageSize,
        onShowSizeChange: (current, size) => {
          this.setState({
            pageSize: size
          });
        },
        showTotal: (total, range) => {
          return `共${total}条记录，${Math.ceil(total / pageSize)}页`
        }
      },
      scroll: { y: 560 }
    }
    // const paginationProps = {
    //   size: "small",
    //   current,
    //   total,
    //   pageSize,
    //   showQuickJumper: true,
    //   onChange: (page, pageSize) => {
    //     this.setState({
    //       current: page
    //     })
    //   },
    //   style: {
    //     marginTop: "10px"
    //   }
    // }
    const organizeModalProps = {
      breadcrumbClick,
      isFfgr,
      rootOrgId,
      rootOrgType,
      isLeaderGroup,
      hasRoot,
      onlyDirectSub,
      radio,
      distinguishOwner,
      visible,
      dataSource,
      hideModal: () => {
        this.hideModal()
      },
      loadOrganizeData: (data) => {
        getSelectedValues && getSelectedValues(JSON.parse(JSON.stringify(data)));
        addKeyToTableDataSource(data);
        // console.log(data);
        this.setState({
          dataSource: data,
          visible: false
        });
        if (data.length > 0) {
          this.props.onChange(this.props.radio ? data[0].org_id : data.map(item => item.org_id))
        }
      },
      ref: (ref) => {
        this.organizeModal = ref;
      }
    }
    return (
      <div className="organize-selector-wrapper">
        <div className="organize-count">
          {
            canEdit &&
            <a className="selector-trigger" onClick={this.showModal}>选择组织</a>
          }
          <span className="select-count"> 已选({dataSource.length})</span>
        </div>

        {
          (selectType === 'table' && dataSource && Array.isArray(dataSource) && dataSource.length !== 0) &&
          <div className="organize-table">
            {/* <div className="organize-count">
             已选({dataSource.length})
           </div> */}
            <div className="organize-table-wrapper">
              <Table {...tableProps} />
            </div>
          </div>
        }
        {
          (selectType === 'text' && dataSource && Array.isArray(dataSource) && dataSource.length !== 0) &&
          <div className="organize-text">
            {
              dataSource.map((item, index) => {
                return <div key={index} className="o-t-box"><span>{item.org_name}</span><svg onClick={() => {
                  _this.removeItem(index, item)
                }} t="1726716660042" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6168" width="10" height="10"><path d="M920.064 955.392c-10.24 0-20.992-4.096-29.184-11.776L79.36 131.584c-15.872-15.872-15.872-41.984 0-57.856 15.872-15.872 41.984-15.872 57.856 0l811.52 811.52c15.872 15.872 15.872 41.984 0 57.856-7.68 8.192-18.432 12.288-28.672 12.288z" fill="#515151" p-id="6169"></path><path d="M108.544 955.392c-10.24 0-20.992-4.096-29.184-11.776-15.872-15.872-15.872-41.984 0-57.856L890.88 73.728c15.872-15.872 41.984-15.872 57.856 0 15.872 15.872 15.872 41.984 0 57.856L137.216 943.616c-7.68 7.68-18.432 11.776-28.672 11.776z" fill="#515151" p-id="6170"></path></svg></div>
              })
            }
          </div>
        }
        {/* <Pagination {...paginationProps} /> */}
        <OrganizeModal {...organizeModalProps} />
      </div >
    )
  }
}

OrganizeSelector.propTypes = {
  canEdit: propTypes.bool,
  exportData: propTypes.func,
  inputData: propTypes.array,
  getSelectedValues: propTypes.func,
  // 只能选择直接下级，筛选查询功能和列表点击下级按钮禁用
  onlyDirectSub: propTypes.bool,
  // 是否单选模式
  radio: propTypes.bool,
  // 是否是领导班子
  isLeaderGroup: propTypes.bool,
  // 查询组织结果是否包含根节点，当前登录组织为根节点
  hasRoot: propTypes.bool,
  // 根节点组织ID
  rootOrgId: propTypes.number,
  // 根节点组织类型
  rootOrgType: propTypes.number,
  // 是否选择分管单位
  isFfgr: propTypes.bool,
  // 是否是面包屑点击
  breadcrumbClick: propTypes.bool 
}

OrganizeSelector.defaultProps = {
  canEdit: true,
  exportData: (data) => { console.log("默认方法导出数据", data) },
  inputData: [],
  getSelectedValues: data => data,
  // 只能选择直接下级，筛选查询功能和列表点击下级按钮禁用
  onlyDirectSub: false,
  radio: false,
  isLeaderGroup: false,
  hasRoot: false,
  rootOrgId: null,
  rootOrgType: null,
  isFfgr: false,
  breadcrumbClick: false
}

export default OrganizeSelector;
