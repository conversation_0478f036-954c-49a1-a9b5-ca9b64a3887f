.news-list {
  .ant-table-tbody {
    a {
      color: #359AF7;
      text-decoration: underline;
    }
    .status-icon{margin:0;}
  }
  Form {
    margin-top: 40px;
    margin-bottom: 40px;
  }
  .ant-table-wrapper {
    margin: 2%;
  }
  .ant-table-thead>tr>th,
  .ant-table-tbody>tr>td {
    text-align: center;
  }
  .ant-table-thead  .news-list-oper-set{
    text-align: center;
  }
  .ant-table-tbody .news-list-oper-set{
    text-align: right;
  }
  .news-list-title {
    min-width: 180px;
  }
  // .news-list-table {
  //   min-width: 800px;
  // }
  .news-table-bottom {
    margin-top: -92px;
  }
  .new-list-image-submit-opacity{
    text-align: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 999999;
    background-color: #FFFFFF;
    opacity: 0.5;
    line-height: 100%;
  }
  .new-list-image-submit-opacity-spin{
    position: relative;
    top: 50%;
  }
  .notice-modal {
    padding: 28px 50px;
    .ant-form.ant-form-horizontal {
      margin: 0;
    }
    .no-margin {
      .ant-form-item-control {
        margin: 0;
      }
    }
  }
  .add-minus-symbol{
    width: 24px;
   height: 24px;
   display: block;
   float: left;
   background: #EBF2F6;
   border-radius: 12px;
   text-align: center;
   cursor: pointer;
   font-size: 16px;
   font-weight: 900;
   color: #97AAB2;
  }


  .clipping {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 99999;
    iframe{
      width: 100%;
      height: 100%;
    }
  }

}
.minus-a {
  width: 24px;
  height: 24px;
  display: block;
  float: left;
  background-color: #EBF2F6;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  font-weight: 900;
  color: #97AAB2;
  line-height: 19px;
  margin-top: 8px;
  margin-right: 10px;
  margin-left: 20px;
  font-size: 19px;
}
.plus-a {
  width: 24px;
  height: 24px;
  display: block;
  float: left;
  background-color: #EBF2F6;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  font-weight: 900;
  color: #97AAB2;
  line-height: 19px;
  margin-top: 8px;
  font-size: 19px;
}
.plus-a::selection,
.minus-a::selection{
  color: #FF4D4F;
  background-color: transparent;
  opacity:  1
}