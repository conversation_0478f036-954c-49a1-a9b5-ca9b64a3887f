import React, { Component } from "react";
import { message as Message, Checkbox, Radio } from "antd";
import "./organize-list.less";
import SelfIcon from "components/self-icon";
import { getGrouplist, getGroupOrglist } from "apis/organisition";

import propTypes from "prop-types";
import Item from "antd/lib/list/Item";
import LoadingModal from "components/loading-modal";



const RadioGroup = Radio.Group;
class OrganizeList extends Component {
  constructor() {
    super();
    this.state = {
      // groups:[],

      groups: [
        // {
        //   "group_id": 1,
        //   "name": "分组1",
        //   "type": 2,
        //   "orgs":[
        //     {
        //       org_id:1,
        //       type:1,
        //       org_name:"组织名称1"
        //     },
        //     {
        //       org_id:2,
        //       type:1,
        //       org_name:"组织名称2"
        //     },
        //     {
        //       org_id:3,
        //       type:1,
        //       org_name:"组织名称3"
        //     }
        //   ]
        // },
        // {
        //   "group_id": 2,
        //   "name": "分组2",
        //   "type": 2,
        //   "orgs":[
        //     {
        //       org_id:21,
        //       type:2,
        //       org_name:"2组织名称A"
        //     },
        //     {
        //       org_id:22,
        //       type:2,
        //       org_name:"2组织名称B"
        //     },
        //     {
        //       org_id:23,
        //       type:2,
        //       org_name:"2组织名称C"
        //     }
        //   ]
        // },
      ],

      orgsCount: 0,

      //当前分组下的组织
      orgs: [],


      checked: [],

      // 选中的列表，用于存储所有选中组织的org_id，用于控制复选框的状态

      // 记录组织树形选择时顶部面包屑

      isCheckAll: false,

      isSelectedGroup: 0, //是查看分组列表还是分组下的组织列表， 为分组id（数字>0）时查看分组下的组织列表；为0时查看分组列表

      // loading显示控制
      loadingModalVisible: false
    };
    this.removeOrg = this.removeOrg.bind(this);
    this.exportData = this.exportData.bind(this);
    this.initData = this.initData.bind(this);
  }

  componentWillMount() {
    // const { groups } = this.props;
    this.initGroupsData();
    const { dataSource } = this.props;
    this.initData(dataSource);
  }

  componentWillReceiveProps(props) {
    const { dataSource } = props;
    this.initData(dataSource);
  }

  initData(checkedData) {
    // console.log("初始化数据", dataSource);
    let checked = JSON.parse(JSON.stringify(checkedData));

    this.setState({
      checked
    });
  }

  // 通过ref向外部输出组织选择结果数据
  exportData() {
    const { checked } = this.state;
    return JSON.parse(JSON.stringify(checked));
  }

  // 从右侧移除一个组织标签
  removeOrg(org) {
    this.checkboxOrgHandler(org, false);
  }

  // 全选当前列表页的组织
  checkAllHandler(isCheckAll) {

    const { groups } = this.state;
    let checked = [];

    if (isCheckAll) {
      for (var i = 0; i < groups.length; i++) {
        const orgs = groups[i].orgs;
        for (var j = 0; j < orgs.length; j++) {
          const org = orgs[j];
          checked.push(org);
        }
      }
    }

    this.setState({
      checked
    })
  }

  //通过组id找到group数据
  getGroupById(group_id) {
    const { groups } = this.state;
    return groups.find((group) => {
      return group.group_id === group_id
    });
  }

  //这个组织是否被选中
  isOrgsChecked(item) {
    const { checked } = this.state;
    return checked.findIndex((org) => {
      if (item.group_id && org.group_id) {
        return org.org_id === item.org_id && org.group_id === item.group_id
      } else {
        return org.org_id === item.org_id
      }
    }) !== -1;
  }

  //这个分组下面的组织选择了一部分
  isGroupsIndeterminated(group) {
    const { checked, isSelectedGroup } = this.state;
    const currentGroup = group ? group : this.getGroupById(isSelectedGroup);
    let currentGroupOrgChecked = [];

    if (!currentGroup) {
      return false;
    }
    if (!checked.length) {
      return false;
    }
    if (currentGroup.orgs && currentGroup.orgs.length) {
      currentGroup.orgs.forEach((org) => {
        if (checked.find((c_org) => {

          if (org.group_id && c_org.group_id) {
            return c_org.org_id === org.org_id && c_org.group_id === org.group_id
          } else {
            return c_org.org_id === org.org_id
          }

          // return c_org.org_id === org.org_id
        })) {
          currentGroupOrgChecked.push(org);
        }
      });
      // console.log(currentGroupOrgChecked)

      if (currentGroupOrgChecked.length > 0 &&
        currentGroupOrgChecked.length != currentGroup.orgs.length) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
      //todo 异步取值 group.orgs
    }

  }
  //这个分组是否被选中
  isGroupsChecked(group) {
    const { checked, isSelectedGroup } = this.state;
    const currentGroup = group ? group : this.getGroupById(isSelectedGroup);

    if (!currentGroup) {
      return false;
    }
    if (!checked.length) {
      return false;
    }

    if (currentGroup.orgs && currentGroup.orgs.length) {
      if (currentGroup.orgs.length > currentGroup.length) {
        return false;
      } else {
        return currentGroup.orgs.every((org) => {
          return !!checked.find((c_org) => {
            if (org.group_id && c_org.group_id) {
              return c_org.org_id === org.org_id && c_org.group_id === org.group_id
            } else {
              return c_org.org_id === org.org_id
            }
          });
        });
      }
    } else {
      return false;
      //todo 异步取值 group.orgs
    }

  }

  //全局全选是否被选中
  isAllChecked() {
    const { checked, isSelectedGroup, orgsCount } = this.state;
    if (!checked.length) {
      return false;
    }
    if (checked.length >= orgsCount) {
      return true;
    }
  }

  //全局全选是否选了部分
  isAllIndeterminated() {
    const { checked, isSelectedGroup, orgsCount } = this.state;
    if (!checked.length) {
      return false;
    }
    if (checked.length < orgsCount) {
      return true;
    }
  }

  //点击选择分组处理逻辑
  checkboxGroupHandler(group, isCheck) {
    const { checked, isSelectedGroup } = this.state;
    const currentGroup = group ? group : this.getGroupById(isSelectedGroup);

    if (currentGroup) {

      if (isCheck) {
        currentGroup.orgs.forEach((org) => {
          if (!checked.find((c_org) => {
            if (org.group_id && c_org.group_id) {
              return c_org.org_id === org.org_id && c_org.group_id === org.group_id
            } else {
              return c_org.org_id === org.org_id
            }
            // return c_org.org_id === org.org_id
          })) {
            checked.push(org)
          }
        });
      } else {
        currentGroup.orgs.forEach((org) => {
          const index = checked.findIndex((c_org) => {
            return c_org.org_id === org.org_id
          });
          checked.splice(index, 1);
        })
      }

      this.setState({
        checked
      })

    }

  }

  //点击选择组织处理逻辑
  checkboxOrgHandler(item, isCheck) {
    const { groups, checked } = this.state;

    if (isCheck) {
      checked.push(item);
    } else {
      for (var i = checked.length - 1; i >= 0; i--) {
        if (checked[i].group_id && item.group_id) {
          if (checked[i].group_id === item.group_id && checked[i].org_id === item.org_id) {
            checked.splice(i, 1);
          }
        } else {
          if (checked[i].org_id === item.org_id) {
            checked.splice(i, 1);
          }
        }
      }
    }

    this.setState({
      checked
    });

  }

  //初始化分组数据
  async initGroupsData() {
    this.setState({
      loadingModalVisible: true
    });


    let groupsRes = await getGrouplist();
    let groupsIds = [];

    if (!groupsRes.code && groupsRes.data && groupsRes.data.data) {

      let groups = groupsRes.data.data;

      for (var i = 0; i < groups.length; i++) {
        // console.log(groups.data.data[i]);
        groupsIds.push(groups[i].group_id);
      }

      let orgsRes = await getGroupOrglist(groupsIds);
      if (!orgsRes.code && orgsRes.data && orgsRes.data.data) {
        let groupInfo = orgsRes.data.data;
        let orgsCount = 0;
        for (var i = 0; i < groupInfo.length; i++) {
          orgsCount = orgsCount + (groupInfo[i].orgs ? groupInfo[i].orgs.length : 0)
        }
        this.setState({
          groups: groupInfo,
          orgsCount,
          loadingModalVisible: false
        })
      } else {
        message.error(orgsRes.message);
      }
    } else {
      message.error(groupsRes && groupsRes.message);
    }

  }


  viewOrgs(item) {
    if (item.orgs) {
      this.setState({
        orgs: item.orgs,
        isSelectedGroup: item.group_id
      });
    }
  }

  renderGroupList() {
    const { checkAll } = this.props;
    const { groups, isCheckAll, checked } = this.state;

    if (groups && Array.isArray(groups) && groups.length !== 0) {
      return (
        <div>
          {(checkAll) ? (
            <div className="org-item-wrapper" title={"全选"}>
              <div className="org-item-name">
                <div className="checkbox-wrapper">
                  <Checkbox
                    checked={this.isAllChecked()}
                    indeterminate={this.isAllIndeterminated()}
                    onChange={e => {
                      const isCheck = e.target.checked;
                      this.checkAllHandler(isCheck);
                    }}
                  >
                    全选
                  </Checkbox>
                </div>
              </div>
            </div>
          ) : null}

          {groups.map((group, index) => {
            // 当前是否选中需要
            return (
              <div className="org-item-wrapper" title={group.name} key={index}>
                <div
                  className={
                    group.child_org_num !== 0
                      ? "org-item-name"
                      : "org-item-name indent-right"
                  }
                >
                  <div className="checkbox-wrapper">
                    {group.orgs && group.orgs.length ?
                      <Checkbox
                        checked={this.isGroupsChecked(group)}
                        indeterminate={this.isGroupsIndeterminated(group)}
                        onChange={e => {
                          const isCheck = e.target.checked;
                          this.checkboxGroupHandler(group, isCheck);
                        }}
                      >
                        {group.name}
                      </Checkbox>
                      : <span className="no-checkbox">
                        <Checkbox disabled={true}></Checkbox>
                        {group.name}
                      </span>}

                  </div>
                </div>
                {group.child_org_num !== 0 && (
                  <div
                    className="org-item-next"
                    onClick={() => this.viewOrgs(group)}
                  >
                    <span>查看</span>
                    <SelfIcon type="right" />
                  </div>
                )}
              </div>
            );
          })}

        </div>
      );
    } else {
      return <div>暂无数据</div>;
    }
  }

  renderOrgList() {
    const { checkAll } = this.props;
    const { orgs, isCheckAll, checked } = this.state;

    if (orgs && Array.isArray(orgs) && orgs.length !== 0) {
      return (
        <div>
          {(checkAll) ? (
            <div className="org-item-wrapper" title={"全选"}>
              <div className="org-item-name">
                <div className="checkbox-wrapper">
                  <Checkbox
                    checked={this.isGroupsChecked()}
                    indeterminate={this.isGroupsIndeterminated()}
                    onChange={e => {
                      const isCheck = e.target.checked;
                      this.checkboxGroupHandler(null, isCheck);
                    }}
                  >
                    全选
                  </Checkbox>
                </div>
              </div>
            </div>
          ) : null}

          {orgs.map((item, index) => {
            // 当前是否选中需要
            return (
              <div className="org-item-wrapper" title={item.name} key={index}>
                <div
                  className={
                    item.child_org_num !== 0
                      ? "org-item-name"
                      : "org-item-name indent-right"
                  }
                >
                  <div className="checkbox-wrapper">
                    <Checkbox
                      checked={this.isOrgsChecked(item)}
                      onChange={e => {
                        const isCheck = e.target.checked;
                        this.checkboxOrgHandler(item, isCheck);
                      }}
                    >
                      {item.org_name}
                    </Checkbox>
                  </div>
                </div>
              </div>
            );
          })}

        </div>
      );
    } else {
      return <div>暂无数据</div>;
    }
  }

  render() {
    const {
      orgs,
      groups,
      checked,
      isCheckAll,
      loadingModalVisible,
      isSelectedGroup,
    } = this.state;
    const { checkAll } = this.props;


    const renderBreadcrumb = data => {
      //isSelectedGroup
      const selectedGroup = this.getGroupById(isSelectedGroup);

      return (
        <div className="breadcrumb-wrapper">
          <div className="breadcrumb-item">
            <a
              onClick={() => {
                this.setState({
                  isSelectedGroup: 0
                });
              }}
            >
              全部分组
            </a>
            <SelfIcon type="right" />
            {
              selectedGroup ?
                selectedGroup.name :
                ''
            }
          </div>
        </div>
      );

    };

    return (
      <div className="organize-list">
        <LoadingModal modalVisible={loadingModalVisible} />
        <div className="selector-wrapper">
          {/* 左侧选择器 */}
          {isSelectedGroup > 0 && <div>{renderBreadcrumb()}</div>}
          <div className="selector-list">
            {
              isSelectedGroup === 0 ?
                this.renderGroupList() :
                this.renderOrgList()
            }
          </div>
        </div>
        <div className="selected-list">
          <div className="count">已选 ({checked.length})</div>
          {checked &&
            Array.isArray(checked) &&
            checked.length !== 0 &&
            checked.map((org, index) => {
              return (
                <div key={index} className="org-tag">
                  <div className="tag-text">{org.org_name}</div>
                  <div
                    className="tag-close"
                    onClick={() => {
                      this.removeOrg(org);
                    }}
                  >
                    <SelfIcon type="close" />
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    );
  }
}

OrganizeList.propTypes = {
  dataSource: propTypes.array,
  checkAll: propTypes.bool,
};

OrganizeList.defaultTypes = {
  dataSource: [],
  checkAll: true,
};

export default OrganizeList;
