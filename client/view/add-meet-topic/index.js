import React from "react";
import SearchHeader from "components/search-header";
import { addTopic, updateTopic } from "apis/meeting-topic-manage";
import { Form, Input, Carousel, message } from "antd";
import { fileHost, uploadHost } from 'apis/config';
import "./style.less";
import Step1 from "./step1";
import Step2 from "./step2";
import IndexView from "./index-view";

const {user_id, oid: org_id} = JSON.parse(sessionStorage.getItem('userInfo')) || {};
class AddMeetTopic extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isAdd: true, // 是否新增
      // title: "新增会议议题",
      // title: "新增工作任务", // 页面标题
      title: "新增任务", // 页面标题
      topicNameSize: 100,
      topicDescSize: 5000,
      uploadFileUrl: `${uploadHost}/file/upload`,
      step: 0,

      // 完成期限
      tlt: 0,

      // 新增议题 query 参数
      topic_id: null, // 修改议题, 需要的参数
      name: "", // 议题名称
      description: "", // 议题描述
      files: [], // 附件
      contents: [], // 内容（默认一条填写内容）
      start_time: null, // 开始时间
      end_time: null, // 结束时间
      orgs: [], // 组织 id 数组
      status: null, // 上级, 还是下级提交
      uploadLoading: false, // 上传 loading 状态
      submitLoading: false, // 提交 loading 状态
    }
    this.carousel = React.createRef();
    this.next = this.next.bind(this);
    // this.updateState = this.updateState.bind(this);
  }

  componentDidMount() {
    const { state } = this.props.location;
    console.log(state);
    // 批量添加内容 ref
    // const { batchAddContent, addContent } = this.firstChild.reportContent;
    if (state) {
      if (state.topic_id) {
        // 有议题ID，则是编辑议题
        this.setState({
          // title: "编辑会议议题",
          // title: "编辑工作任务",
          title: "编辑任务",
          topic_id: state.topic_id,
          name: state.name,
          description: state.description,
          files: state.files || [],
          contents: state.contents || [],
          tlt: state.tlt,
          start_time: state.start_time,
          end_time: state.end_time,
          orgs: state.orgs || [],
          isAdd: state.name !== undefined ? false : true,
          status: state.status
        });
      } else {
        // 没有议题ID，则是新增议题
        this.setState({
          status: state.status
        });
      }
      if (state.status === 2) {
        // 如果是下级组织增加议题，只能作用于本组织
        let currentOrg = typeof window !== 'undefined' && window.sessionStorage.getItem('current-org');
        try {
          currentOrg = JSON.parse(currentOrg);
        } catch (error) {
          console.error(error);
        }
        this.setState({
          orgs: [{
            org_id: currentOrg.oid,
            org_name: currentOrg.name
          }]
        });
      }
    }
  }

  updateState(payload, callback) {
    // console.log(payload);
    this.setState(payload, () => callback && callback());
  }

  // 新增一个议题
  add_A_Topic(_data) {
    this.setState({
      submitLoading: true
    })
    const { history } = this.props;
    addTopic(_data, user_id, org_id).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        message.success("提交成功");
        this.setState({
          submitLoading: false
        });
        history.goBack();
      } else {
        this.setState({
          submitLoading: false
        });
        message.error("提交失败: " + data.message)
      }
    }).catch(e => {
      console.log(e)
    })
  }

  // 修改一个原有议题
  updateATopic(_data) {
    const { history } = this.props;
    this.setState({
      submitLoading: true
    })
    updateTopic(_data).then(res => {
      const { data } = res;
      if (data && data.code === 0) {
        message.success("修改成功");
        this.setState({
          submitLoading: false
        });
        history.goBack();
      } else {
        message.error(data.message);
        this.setState({
          submitLoading: false
        });
      }
    }).catch(e => {
      console.log(e)
    })
  }

  // 下一步
  next(callback) {
    const { name, description, files, contents } = this.state;
    // 这个回调函数, 用来设置表单错误信息
    callback && callback(this.state);
    // 只有当第一页的内容不为空时, 才能执行下一步
    // && description !== "" && files.length > 0
    if (name !== "" && contents.length > 0) {
      this.setState({
        step: this.state.step + 1
      }, () => {
        // 这个回调函数, 用来设置表单错误信息
        this.carousel.current.innerSlider.slickGoTo(this.state.step);
      });

    }

  }

  // 上一步
  prev() {
    this.setState({
      step: this.state.step - 1
    }, () => {
      this.carousel.current.innerSlider.slickGoTo(this.state.step);
    })
  }

  render() {
    const { history, form } = this.props;
    const {
      title,
      contents
    } = this.state;

    const formItemLayout = {
      labelCol: { span: 2 },
      wrapperCol: { span: 20 }
    }

    const step1 = {
      state: this.state,
      form, contents,
      history,
      formItemLayout,
      next: this.next.bind(this),
      updateState: this.updateState
    }

    const step2 = {
      state: this.state,
      form, _this: this,
      formItemLayout,
      prev: this.prev.bind(this),
      updateState: this.updateState,
      addATopic: this.add_A_Topic.bind(this),
      updateATopic: this.updateATopic.bind(this)
    }

    const indexViewProps = {
      state: this.state,
      form,
      _this: this,
      contents,
      history,
      formItemLayout,
      updateState: this.updateState.bind(this),
      addATopic: this.add_A_Topic.bind(this),
      updateATopic: this.updateATopic.bind(this)
    }

    return (
      <div className="addMeetTopic">
        <SearchHeader
          onBack={() => history.goBack()}
          title={title}
          style={{ fontWeight: "bold" }}
        />
        <div style={{ padding: "32px" }}>
          <IndexView {...indexViewProps} />
          {/* <Carousel
            ref={this.carousel}
            style={{ overflow: "auto" }}
          >
            <div>
              <Step1 {...step1} ref={ref => this.firstChild = ref} />
            </div>
            <div>
              <Step2 {...step2} />
            </div>
          </Carousel> */}
        </div>
      </div >
    )
  }


}

export default Form.create()(AddMeetTopic);