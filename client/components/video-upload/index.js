import React, { Component } from 'react';
import { message } from 'antd';
import './index.less';

import { uploadFile } from 'apis/file';

import { headers } from 'tool/axios';

import propTypes from 'prop-types';
import LoadingModal from 'components/loading-modal';

class VideoUpload extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      taskIdRecord: 0,
      decoding: false, //解码中的标示
      task_id: '',
      tipText: '文件上传中，请勿关闭弹窗...',
    };
    this.uploadHandler = this.uploadHandler.bind(this);
    this.postFileUpload = this.postFileUpload.bind(this);
    this.handleUploadResponse = this.handleUploadResponse.bind(this);
  }

  componentDidMount() {}

  componentWillReceiveProps(props) {
    const { onCleanVideo } = this.props;
    if (props.cleanVideo) {
      // console.log(props.cleanVideo,'props.cleanVideo--')
      this.ipt.value = '';
      onCleanVideo(false);
      // console.log(props.cleanVideo,'props.cleanVideo--')
    }
  }
  // todo: handleUploadResponse
  async handleUploadResponse(response, file, queryHeader) {
    const { onChange } = this.props;
    let { taskIdRecord } = this.state;
    if (!response) {
      message.error('视频上传错误，请重试!');
      this.setState({ loading: false });
      return;
    }
    const res = (response && response.body && response.body.data) || {};
    if (res && res.code !== 0 && res.code !== 1056) {
      this.setState({ loading: false });
      return message.error(`${response.file.name}上传失败，原因：${res.message || '未知原因'}`);
    }
    const task_id = Array.isArray(res.data) ? res.data[0].task_id : '';
    if (task_id) {
      this.setState({ task_id });
    }
    const that = this;
    //todo:第二次调用接口  task_id
    if (res.data && task_id && taskIdRecord === 0) {
      this.setState({ taskIdRecord: taskIdRecord++ });
      setTimeout(async function() {
        const response1 = await that.postFileUpload(file, queryHeader);
        // console.log(response1,'----response')
        that.handleUploadResponse(response1, file);
      }, 2000);
    } else if (!task_id && res.code === 1056) {
      this.setState({ tipText: '视频上传完毕,视频转码进行中...', decoding: true });
      if (window.keepUploadTimer) clearInterval(window.keepUploadTimer);
      window.keepUploadTimer = setInterval(async function() {
        let response1 = await that.postFileUpload(file, queryHeader);
        if (response1 && response1.body && response1.body.data && response1.body.data.code === 0) {
          clearInterval(window.keepUploadTimer);
          const response1 = await that.postFileUpload(file, queryHeader);
          that.handleUploadResponse(response1, file);
        } else {
          that.handleUploadResponse(response1, file);
        }
      }, 3000);
    } else if (res.data && !task_id && res.code != 1056) {
      this.setState({
        loading: false,
        taskIdRecord: 0,
        decoding: false,
      });
      if (typeof onChange === 'function') onChange(res.data[0].path, res.data[0]);
    }
  }

  // todo: 文件上传onchange
  async uploadHandler(e) {
    const target = e.target;
    const files = target.files;
    const file = files[0];
    if (files.length === 0) {
      return;
    }
    // const videoTypes = ["video/mp4", "video/flv", "video/mov", "video/mkv", "video/avi"];
    const videoSuffix = ['.flv',  '.mov', '.mkv', '.avi', '.mp4'];
    let extname = '';
    if (file.name.lastIndexOf('.') > 0) {
      extname = '.' + file.name.split('.')[file.name.split('.').length - 1];
    }
    if (videoSuffix.indexOf(extname.toLowerCase()) === -1) {
      // 上传的是图片，图片质量保真处理，作为新闻类型图片来处理
      // 对符合条件的文件进行处理
      return message.error('请上传正确的视频,支持的格式有.flv,.mp4,.mov,.mkv,.avi');
    }
    const maxSize = Math.round(file.size / 1024 / 1024);
    // console.log(maxSize, '----file.size')
    if (maxSize > 500) {
      return message.error('请上传 < 500M的视频');
    }
    const fileList = [];
    for (let key in files) {
      if (typeof files[key] === 'object') {
        fileList.push(files[key]);
      }
    }
    this.setState({ loading: true });
    // 获取请求头
    const queryHeader = headers();
    //第一次请求

    console.log("🐎", queryHeader)
    const response = await this.postFileUpload(fileList[0], queryHeader);
    if (response) {
      if (response.body) {
        const res = response.body.data;
        const { onChange } = this.props;
        this.setState({
          loading: false,
          taskIdRecord: 0,
          decoding: false,
        });
        if (typeof onChange === 'function') onChange(res.data[0].path, res.data[0]);
      }
    }
    //政务云隐藏下方代码 启动上面注释
    // this.handleUploadResponse(response, fileList[0], queryHeader);
  }
  // todo: 处理文件流 调用api 上传视频
  async postFileUpload(file, queryHeader) {
    const formData = new FormData();
    const { upType = 'file' } = this.props;
    const { decoding, task_id } = this.state;
    formData.append('upType', upType);
    let upName = file.name;
    // 去除文件名中的空格字符
    if (typeof upName === 'string') {
      upName = upName.replace(/\s*/g, '');
    }
    // todo:需要解码
    if (task_id) {
      formData.append('task_id', task_id);
    } else if (decoding) {
      formData.append('upfile', file);
      formData.append('up_name', upName);
      formData.append('task_id', task_id);
    } else {
      formData.append('upfile', file);
      formData.append('up_name', upName);
    }
    const result = await uploadFile(formData, queryHeader);
    return {
      body: result,
      file,
    };
  }

  render() {
    const { loading, tipText } = this.state;
    const { multiple, children, disabled, cleanVideo } = this.props;
    if (disabled) {
      return null;
    }
    return (
      <div>
        <div className="video-upload-container">
          {!disabled ? (
            <input
              ref={(el) => (this.ipt = el)}
              type="file"
              className="file-hidden-trigger"
              multiple={multiple}
              onChange={this.uploadHandler}
            />
          ) : (
            ''
          )}
          {children}
          <LoadingModal noTip={true} modalVisible={loading} />
        </div>
        {// todo:ture 是loading....
        loading && tipText && <div className="video-tip-text-container">{tipText}</div>}
      </div>
    );
  }
}

VideoUpload.propTypes = {
  tipText: propTypes.oneOfType([propTypes.string, propTypes.element]),
  // 限制文件最大可以上传数量
  max: propTypes.number,
  disabled: propTypes.bool,
  multiple: propTypes.bool,
  upType: propTypes.string,
  onChange: propTypes.func,
  uploadedList: propTypes.array,
  children: propTypes.oneOfType([propTypes.string, propTypes.element]),
};

VideoUpload.defaultProps = {
  tipText: "文件上传中...",
  disabled: false,
  multiple: true,
  upType: "video", //政务云使用no-compress-video
  onChange: (data) => {
    console.log("上传完毕", data);
  },
  uploadedList: [],
  children: <span>上传文件</span>,
};

export default VideoUpload;
