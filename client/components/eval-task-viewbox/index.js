
//优先考虑直接调用吕诃组件：audit-task-detail/modal.js
import React from 'react';
import { Form, Button, Spin, Row, Col, Table, Radio, Input, Tooltip, Timeline } from 'antd';
import InputTips from "components/activity-form/sub-components/InputTips";
import UploadList from 'components/upload-list';
import Preview from 'components/preview';

import './index.less';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { TextArea } = Input;

const path = require('path');

const labelCol = {
    span: 3
}
const wrapperCol = {
    span: 21
}

const renderTimeline = (recordList) => {

    if(!recordList || recordList && !recordList.length) return '暂无记录';
    return recordList.map((item, index) => {

        const status = item.operation_status;
        let statusText, statusClass;

        if(index == 0){
            statusClass = 'warning';
        }

        const time = item.create_time && item.create_time.split(' ');
        const firstTime = time && time[0];
        const endTime = time && time[1];

        switch(status){
            case 1:
            case 2:
            case 8:
                statusText = '提交任务';
                break;
            case 3:
                statusText = '审批不通过';
                break;
            case 4:
                statusText = '审批通过';
                break;
            case 5:
                statusText = '不合格';
                break;
            case 6:
                statusText = '合格';
                break;
            case 7:
            case 9:
                statusText = '撤回';
                break;
        }

        const descriptive_opinions = decodeURIComponent(item.descriptive_opinions);

        return (
            <Timeline.Item
                className={statusClass}
                key={`audit-timeline_content_${index}`}
                dot={
                    <div className="record-timeline-key">
                        <div className="icon"></div>
                        <span>{firstTime}<br />{endTime}</span>
                    </div>
                }
            >
                <div className="info"><b>{statusText}</b>{item.create_user_name}（{item.create_org_name}）</div>
                <div className="desc">执行描述：{descriptive_opinions}</div>
                {item.files && (
                    <div className="file">
                        <UploadList
                            canEdit={false}
                            canPreview={false}
                            inputData={item.files.map(f => {
                                return {
                                    ...f, name: f.name, id: f.id, path: f.file_url, size: f.size, file_name: f.file_name
                                }
                            })}
                        />
                    </div>
                )}
            </Timeline.Item>
        );
    });
};

const TaskViewBox = ({
    form, onDownload, onSubmit, onInit, isSubmitLoading, isLoading, isEdit,
    rowSelection, dataSource
}) => {
    const { getFieldDecorator, getFieldValue } = form;
    const summary = dataSource && decodeURIComponent(dataSource.summary || '');
    const files = (dataSource && dataSource.files) || [];
    const recordList = (dataSource && dataSource.recordList) || [];
    let recordHTML = '';

    if(recordList.length){
        recordHTML = (
            <Timeline className = "record-timeline-panel" >
                {renderTimeline(recordList)}
            </Timeline>
        );
    }

    const columns = [{
        title: '提交资料',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        render: (text, record) => {
            return (<div style={{textAlign: 'left'}}>{text}</div>)
        }
    }, {
        title: '文件类型',
        dataIndex: 'fileType',
        key: 'fileType',
        align: 'center',
        render: (text, record) => {
            return (record.name && path.extname(record.name).substr(1));
        }
    }, {
        title: '提交时间',
        dataIndex: 'create_time',
        key: 'create_time',
        align: 'center'
    }, {
        title: '操作',
        dataIndex: 'oper',
        key: 'oper',
        align: 'center',
        render: (text, record) => {
            return (
                <Preview text="查看" file={{
                    file_id: record.id || null,
                    file_path: record.file_url || null
                }} />
            );
        }
    }];

    onInit && onInit(form);

    return (

        <div className='eval-task-viewbox'>
            <Spin spinning={isLoading}>

                <Row className="task-modal-row">
                    <Col {...labelCol} className="label" >执行描述：</Col>
                    <Col {...wrapperCol}>{summary}</Col>
                </Row>
                <Row className="task-modal-row">

                    <Col {...labelCol} className="label" >资料列表：</Col>
                    <Col {...wrapperCol}>
                        {!!files.length && (
                            <div className="download-buttons" >
                                <a style={{marginRight: 20}} onClick={() => {
                                    onDownload();
                                }}>下载文件</a>
                            </div>
                        )}
                        <Table
                            rowKey={(record) => {
                                return (isEdit ? 'audit-q1_' : 'audit-q2_') + record.id;
                            }}
                            rowSelection={rowSelection}
                            columns={columns}
                            dataSource={files}
                            pagination={false}
                            bordered
                        />
                    </Col>

                </Row>
                {isEdit ? (
                    <Form>
                        <Row className="task-modal-row">
                            <Col {...labelCol} className="label formItemLabel" >审核结果：</Col>
                            <Col {...wrapperCol}>
                                <FormItem>
                                    {getFieldDecorator( "operation_status" , {
                                        initialValue: 6
                                    })(
                                        <RadioGroup>
                                            <Radio value={6}>合格</Radio>
                                            <Radio value={5}>不合格</Radio>
                                        </RadioGroup>
                                    )}
                                </FormItem>
                            </Col>
                        </Row>
                        <Row className="task-modal-row">
                            <Col {...labelCol} className="label label-validator" style={{marginTop: 5}} >审核意见：</Col>
                            <Col {...wrapperCol}>
                                <FormItem>
                                    <InputTips max={500} text={getFieldValue('content')}>
                                        {getFieldDecorator( "descriptive_opinions" , {
                                            initialValue: null,
                                            rules: [{
                                                required: true, message: '请输入审批意见'
                                            }, {
                                                max: 500,
                                                message: '最多输入500个汉字'
                                            }]
                                        })(
                                            <TextArea
                                                rows={3}
                                                style={{width: 400}}
                                                placeholder='请输入审批意见'
                                            />
                                        )}
                                    </InputTips>
                                </FormItem>
                            </Col>
                        </Row>
                        <Row className="task-modal-row">
                            <Button
                                style={{ marginLeft: '107px' }}
                                type="primary"
                                htmlType="submit"
                                loading={isSubmitLoading}
                                onClick={() => {
                                    onSubmit(form);
                                }}
                            >提交</Button>
                        </Row>
                    </Form>
                ) : recordHTML}

            </Spin>
        </div>
    )
}

export default Form.create()(TaskViewBox);