import { message } from "antd";

export default function ShiguanOrgTableDetail(props) {
  const { record, index, onUpdateData } = props;

  const formRef = React.useRef(null);
  const [visible, setVisible] = React.useState(false);

  const handleDelete = (org) => {
    const { org_id } = org;
    if (!org_id) return;
    const newData = record.data.filter((item) => item.org_id != org_id);
    onUpdateData({ ...record, data: newData }, index);
  };

  const handleNumChange = (value) => {
    const newData = { ...record, num: value };
    onUpdateData(newData, index);
  };

  const handleAdd = () => {
    formRef.current.validateFields((errs, values) => {
      if (!errs) {
        const maxSeq = record.data.reduce((acc, cur) => {
          return Math.max(acc, cur.seq || 0);
        }, 0);
        const dataSource = [...record.data];
        dataSource.push({
          ...values,
          seq: maxSeq + 1,
        });
        onUpdateData({ ...record, data: dataSource }, index);
        setVisible(false);
      } else {
        message.error("请先选择机构树中的班子");
      }
    });
  };

  const handleSave = (row) => {
    const newData = [...record.data];
    const idx = newData.findIndex((item) => row.org_id === item.org_id);
    if (idx > -1) {
      newData[idx] = row;
    }
    onUpdateData({ ...record, data: newData }, index);
  };

  const columns = [
    {
      dataIndex: "seq",
      key: "seq",
      align: "center",
      width: "20%",
      editable: true,
      title: "测评序号",
    },
    {
      dataIndex: "org_name",
      key: "org_name",
      align: "center",
      width: "20%",
      title: "班子名称",
    },
    {
      title: "操作",
      key: "action",
      width: "20%",
      align: "center",
      render: (_, record) => (
        <Ant.Switch
          checked={record.enable == 1 ? true : false}
          size="small"
          onChange={(r) =>
            onUpdateData({ ...record, enable: r, updatetype: "enable" })
          }
        ></Ant.Switch>
      ),
    },
  ];

  const newColumns = columns.map((col) => {
    if (!col.editable) return col;
    return {
      ...col,
      // onCell: (record) => ({
      //   record,
      //   editable: col.editable,
      //   dataIndex: col.dataIndex,
      //   title: col.title,
      //   handleSave: handleSave,
      // }),
    };
  });

  return (
    <div style={{ marginBottom: 20 }}>
      <Ant.Row type="flex" align="middle" style={{ marginBottom: 10 }}>
        <div style={{ fontSize: 16, fontWeight: 800 }}>
          {record.sequence_name}
        </div>
        <div style={{ marginLeft: 52, marginRight: 20 }}>
          好等次个数：
          <Ant.InputNumber
            value={record.num}
            precision={0}
            size="small"
            onChange={handleNumChange}
          />
        </div>
      </Ant.Row>
      <Ant.Table
        bordered
        // components={{
        //   body: {
        //     row: EditableFormRow,
        //     cell: EditableCell,
        //   },
        // }}
        columns={newColumns}
        dataSource={record.data}
        scroll={{ y: 300 }}
        pagination={false}
      />
    </div>
  );
}
