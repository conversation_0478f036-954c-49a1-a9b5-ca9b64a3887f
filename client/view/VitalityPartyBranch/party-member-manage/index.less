.manage-main-wrap {
  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
  }
}
.member-manage-main {
  .search-dynamic-form {
    padding-right: 250px;
    position: relative;
    min-height: 40px;
    .search-btn-group {
      position: absolute;
      right: -250px;
      top: 4px;
      .ant-btn {
        margin-right: 10px;
      }
    }
  }
  .ant-layout-sider {
    background: none;
    .left-sider-container {
      padding-right: 10px;

      .ant-tabs {
        .ant-tabs-bar {
          margin-bottom: 0;
        }
      }

      .left-sider-content {
        background: #fff;
        border: 1px #e8e8e8 solid;
        border-top: 0;
        .top {
          padding: 20px;
          .ant-select-show-search {
            width: 100%;
          }
        }
        .tree-container {
          border-top: 1px #e8e8e8 solid;
          background: #f7f8f9;
          padding: 10px;
          overflow-y: auto;
          max-height: 835px;
          min-height: 600px;
          .ant-tree {
            .first-item {
              font-weight: bold;
            }
            .anticon {
              color: #999;
              font-size: 16px;
            }

            .ant-tree-switcher,
            .ant-tree-switcher svg,
            .ant-tree-node-content-wrapper,
            .ant-tree-switcher-loading-icon {
              line-height: 30px !important;
              height: 30px !important;
            }

            .ant-tree-node-selected {
              // background: none !important;
            }

            .ant-tree-node-content-wrapper {
              width: calc(100% - 15px);

              .current-item {
                background-color: #fff3f0;
              }
            }
          }
        }
      }
    }
  }
  .main-container {
    border: 1px #e8e8e8 solid;
    background: #fff;
    padding: 20px;
    flex: 1;
    overflow: hidden;
    .search-header-panel {
      background: #f0f3f6;
      padding: 10px 15px !important;
      .checkItem {
        margin: 10px 0;
      }
    }
    .search-header-wrap {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      background-color: #f0f3f6;
      .ant-btn {
        margin-top: 3px;
      }
      .ant-input {
        width: 125px;
      }
    }
    .ant-table {
      .ant-table-title {
        border: 0;
        text-align: right;
        padding: 20px 0 16px;
        .buttons {
          float: left;
          button {
            margin-right: 10px;
          }
        }
      }
      table {
        margin-top: 20px;
        .text-overflow {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .cardLayout {
      padding: 0px;
      margin: 0 0 20px;
      border-bottom: 1px #e6e6e6 solid;
      // height:110px;
      overflow: hidden;
      .card {
        display: flex;
        align-items: center;
        padding: 20px 20px 20px 30px;
        .card-icon {
          float: left;
          font-size: 50px;
        }
        .item {
          position: relative;
          padding: 0 20px;
          text-align: center;
          min-width: 140px;
          &::before {
            content: "";
            position: absolute;
            right: 0;
            top: 10%;
            width: 1px;
            height: 80%;
            background-color: #ccc;
          }
          &:last-child {
            &::before {
              background-color: transparent;
            }
          }
          p {
            margin-bottom: 0;
          }
          b {
            font-size: 22px;
          }
          a {
            margin-top: 7px;
            display: inline-block;
          }
          // .small-icon{
          //   font-size:14px;margin-right:5px;
          // }
        }
      }
    }
  }
}

.member-siderForm {
  .clearDisabled {
    color: rgba(0, 0, 0, 0.65) !important;
    background: #fff !important;
    input {
      color: rgba(0, 0, 0, 0.65) !important;
      background: #fff !important;
    }
    .ant-cascader-input {
      background: none !important;
    }
    .ant-select-selection {
      background: #fff !important;
      cursor: default;
    }
  }
}

.member-manage-label-modal {
  .ant-modal-body {
    padding-top: 15px;
    .form {
      margin-bottom: 10px;
      button {
        margin-top: 4px;
        margin-right: 7px;
      }
      .ant-form-item {
        margin-bottom: 0;
      }
    }
    .buttons {
      margin-top: 20px;
      text-align: center;
      button {
        margin: 0 10px;
      }
    }
  }
}

.member-label-modal-hidden {
  display: none !important;
}

.organizeDataForm {
  padding: 20px 0 80px !important;
  .organizeDataForm-table {
    margin: 15px 30px;
    .ant-table-footer {
      background: #fff;
      padding: 0;
      div {
        width: 50%;
        padding: 10px;
        margin-left: 50%;
        text-align: center;
        border-left: 1px solid #e8e8e8;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        a {
          margin: 0 10px;
        }
      }
    }
  }
  .squareButton {
    height: 32px;
    line-height: 32px;
    border-radius: 0;
    color: #fff;
    background-color: #ff4d4f;
    border-color: #ff4d4f;
  }
  .cancelBtn {
    color: #666666;
    background-color: #f7f8f9;
    border: 1px solid #e5e5e5;
  }
  .bottom {
    padding: 10px 0 10px 30px;
    button {
      margin-right: 10px;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    padding: 21px 0 21px 108px;
    width: 687px;
    background: #fff;
    border-top: 1px #d9d9d9 solid;
    button {
      margin: 0 5px;
    }
  }
}

.organizeDataForm-row {
  padding: 10px 0 10px 30px;
  .organizeDataForm-item-label {
    text-align: right;
    line-height: 40px;
  }
  .ant-form-item {
    margin-bottom: 0;
  }
}

.organizeDataForm-checkbox-row {
  padding: 0 0 0 30px;
  .organizeDataForm-item-label {
    text-align: right;
    line-height: 40px;
    padding: 10px 0;
    label {
      margin-bottom: 0;
    }
  }
  .ant-form-item {
    margin-bottom: 0;
    padding: 10px 0;
    border-bottom: 1px #e5e5e5 solid;
  }
  .organizeCheckboxGroup {
    width: 460px;
    .ant-checkbox-wrapper {
      height: 39px;
      line-height: 39px;
      margin: 0 8px !important;
    }
  }
  .organizeCheckboxPanel_over {
    .ant-form-item-control-wrapper {
      max-height: none;
    }
  }
}

.organizeCheckboxPanel {
  .ant-form-item-control-wrapper {
    max-height: 78px;
    overflow: hidden;
    .ant-form-item-control {
      margin-top: 0;
    }
    .expandBtn {
      float: right;
      margin-top: 8px;
    }
  }
}

.dy-table-text {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  max-width: 300px;
  text-align: inherit;
  white-space: nowrap;
  margin-bottom: 0;
}

.SearchFrom {
  padding: 20px 0 20px 16px;
  background-color: #f0f3f6;
}
