import { logout, getParam } from "tool/util";
import axios from "axios";
console.log("🚀 ~ axios:", axios.create);
const querystring = require("querystring"),
  qs = require("qs"),
  { notification, message } = require("antd"),
  {
    hash,
    reset<PERSON>arms,
    Base64,
    getKey,
    des,
  } = require("client/tool/encryptParams"),
  channel = "SERVER",
  Gateway = `${process.env.getway}`,
  getCsrf = () => {
    return typeof window !== "undefined"
      ? window.sessionStorage.getItem("csrf")
      : "";
  },
  getType = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_type") != null
        ? window.sessionStorage.getItem("_type")
        : "0";
    } else {
      return "-1";
    }
  },
  getOid = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_oid") != null
        ? window.sessionStorage.getItem("_oid")
        : "1";
    } else {
      return "-1";
    }
  },
  getToken = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_tk") != null
        ? window.sessionStorage.getItem("_tk")
        : "-1";
    } else {
      return "-1";
    }
  },
  getUserId = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_uid") != null
        ? window.sessionStorage.getItem("_uid")
        : "-1";
    } else {
      return "-1";
    }
  },
  getOrgName = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_org_name") != null
        ? window.sessionStorage.getItem("_org_name")
        : "-1";
    } else {
      return "-1";
    }
  },
  getOrgType = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_org_type") != null
        ? window.sessionStorage.getItem("_org_type")
        : "-1";
    } else {
      return "-1";
    }
  },
  getName = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_un") != null
        ? window.sessionStorage.getItem("_un")
        : "";
    } else {
      return "-1";
    }
  },
  getMenuId = () => {
    if (typeof window !== "undefined") {
      return window.sessionStorage.getItem("_menu_id") != null
        ? window.sessionStorage.getItem("_menu_id")
        : "";
    } else {
      return "-1";
    }
  },
  getRegionId = () => {
    const { region_id } = getParam();
    return region_id ? region_id : __TOG__.regionId || (process.env.region_id || '-1');
  };
const as = axios.create({
  baseURL: "http://127.0.0.1:3001/api/",
  // baseURL: Gateway,

  /*// 请求前处理数据
transformRequest: [ function ( data, headers ) {
return qs.stringify( data );
} ],*/
  withCredentials: false,
  headers: {
    // 'Access-Control-Allow-Credentials': 'true',
    "Content-Type": "application/text",
    _tk: "-1",
    _uid: "-1",
    _un: "-1",
    _type: "-1",
    _oid: "-1",
    _cl: channel,
    _region_id: "-1",
    "x-csrf-token": getCsrf(),
  },
  // 响应数据格式
  responseType: "json",

  // 超时处理  开发环境忽略超时
  timeout: process.env.IS_DEV ? 3000000 : 1200000,
}),
  handleRequest = ({ type = "get", url, option = {}, data = {} }) => {
    try {
      // 添加formdata判断 
      const headers = {
        // 'Access-Control-Allow-Credentials': 'true',
        "Content-Type": "application/json",
        _tk: getToken(),
        _uid: getUserId(),
        _un: getName(),
        _type: getType(),
        _oid: getOid(),
        _org_type: getOrgType(),
        _menu_id: getMenuId(),
        _org_name: getOrgName(),
        _region_id: getRegionId(),
        "x-csrf-token": getCsrf(),
      };
      // console.log(headers, 'header 信息:')
      option.headers = option.headers || headers;

      switch (type) {
        case "post":
          return as.post(url, data, option);
        case "put":
          return as.put(url, data, option);
        case "delete":
          return as.delete(url, { params: data, headers: option.headers });
        case "get":
        default:
          return as.get(url, { params: data, headers: option.headers });
      }
    } catch (err) {
      notification.error({
        message: "请求错误",
        description: err,
      });
      console.error(err);
    }
  };
const isEncrypted = true;
// as.interceptors.request.use((conf) => {
//   console.log(conf,111111);
//   // const { hash, resetParms, Base64, getKey, des } = util;
//   conf.paramsSerializer = (params) => {
//     return qs.stringify(params, { arrayFormat: "repeat" });
//   };
//   const headerStr = Base64.encode(JSON.stringify(conf.headers));
//   conf.url +=
//     conf.url.indexOf("?") > 0 ? `&_h=${headerStr}` : `?_h=${headerStr}`;

//   // 将原url去除请求地址和网关的部分
//   const queryTarget = conf.url.replace(conf.baseURL, "");
//   const hostURL = conf.baseURL.endsWith("/")
//     ? conf.baseURL.substring(0, conf.baseURL.length - 1)
//     : conf.baseURL;
//   // 获取queryString并处理
//   const action = queryTarget.split("?");
//   const queryParams = qs.stringify(conf.params, { arrayFormat: "repeat" });
//   // 获取请求体
//   const queryBody = conf.data ? JSON.stringify(conf.data) : "";
//   console.log(queryBody,'请求体');
//   // 获取请求hash
//   const hs = hash(
//     decodeURIComponent(`${queryTarget}${queryParams ? "&" + queryParams : ""}`),
//     queryBody
//   );
//   console.log(hs,222);
//   const keyHex = getKey(hs,false);
//   console.log(keyHex,333);

//   let queryString = "";
//   // 请求参数加密控制
//   if (action.length > 1) {
//     queryString = Base64.encode(des(keyHex, action[1]));
//   } else if (queryParams) {
//     queryString = Base64.encode(des(keyHex, queryParams));
//   }

//   if (IsEncrypt) {
//     if (queryString) {
//       conf.params = {
//         query_string: queryString,
//       };
//     }
//     // 请求体加密控制
//     if (queryBody) {
//       const body = Base64.encode(des(keyHex, queryBody));
//       conf.data = body;

//     }
//   }
//   /* const { headers, url } = conf;
//   let _h = JSON.stringify(headers);
//   _h = new Buffer(_h).toString("base64");
//   _h = encodeURIComponent(_h);
//   if (url.indexOf("?") > -1) {
//     conf.url = `${url}&_h=${_h}`;
//   } else {
//     conf.url = `${url}?_h=${_h}`;
//   } */
//   conf.headers._hs = hs;
//   return conf;
// });
//请求拦截器
as.interceptors.request.use(
  (config) => {
    // console.log(config, accessKey);
    // const { url } = config;
    // // 如果是调用积分商城的接口
    // if (url.indexOf("/score/mall") !== -1) {
    //   if (config.headers) {
    //     config.headers.access_key = accessKey;
    //   }
    // }
    // console.log(config);
    //序列化参数时，允许重复参数名存在，例如?id=1&id=2&id=3...
    config.paramsSerializer = (params) => {
      return qs.stringify(params, { arrayFormat: "repeat" });
    };
    //将原url去除请求地址和网关的部分
    let queryTarget = config.url.replace(config.baseURL, "");
    // console.log(queryTarget);
    let hostURL = config.baseURL.endsWith("/")
      ? config.baseURL.substring(0, config.baseURL.length - 1)
      : config.baseURL;

    // 获取queryString并处理
    let action = queryTarget.split("?");
    // console.log(config.params);
    let queryParams = qs.stringify(config.params, { arrayFormat: "repeat" });
    // console.log(queryParams);
    // 获取请求体
    let queryBody = config.data ? JSON.stringify(config.data) : "";
    // console.log(queryParams);
    // console.log(queryBody);
    // 获取请求hash
    let hs = hash(
      `${queryTarget}${queryParams ? "?" + queryParams : ""}`,
      queryBody
    );
    let keyHex = getKey(hs);

    let queryString = "";
    // //请求参数加密控制
    if (action.length > 1) {
      queryString = Base64.encode(des(keyHex, action[1]));
    } else if (queryParams) {
      queryString = Base64.encode(des(keyHex, queryParams));
    }
    if (queryString && !isEncrypted) {
      config.params = {
        query_string: queryString,
        ...config.params,
      };
      // config.params.query_string = queryString;
    }
    // console.log(queryString);
    // console.log(queryBody);
    // 请求体加密控制
    if (queryBody && !isEncrypted) {
      let body = Base64.encode(des(keyHex, queryBody));
      config.data = body;
    }
    // config.params
    // 添加请求头
    config.headers["_hs"] = hs;
    // 返回请求
    return config;
  },
  (error) => {
    console.error(error);
  }
);
//响应拦截器
as.interceptors.response.use(
  (response) => {
    if (response && response.data) {
      //登录页打开时根据region_id获取区县配置 此时无需处理错误
      const isInitConfig = response.config.url.includes(
        "app/config/getConfig/pc"
      );
      if (
        !isInitConfig &&
        (response.data.code === 9905 ||
          response.data.code === 119 ||
          response.data.code === 122 ||
          response.data.code === 141 ||
          response.data.code === 9900 ||
          response.data.code === 9908)
      ) {
        logout().then((response) => {
          let { data: body } = response,
            { code } = body;
          if (code === 0) {
            if (typeof window !== "undefined") {
              // window.sessionStorage.removeItem("_org_type");
              // window.sessionStorage.removeItem("_org_name");
              // window.location.reload();
              //`${error.response.data.message}`
              // message.error('登录状态异常，请重新登录', () => {
              // });
              setTimeout(() => {
                if (window.location.search) {
                  window.location.href = `/login${location.search}&error=loginStatusError`;
                } else {
                  window.location.href = `/login?error=loginStatusError`;
                }
              }, 1000);
            }
          }
        });
      } else {
        return response;
      }
    }
  },
  (error, headers) => {
    if (error.response && error.response.data && error.response.data.code) {
      if (
        error.response.data.code === 9905 ||
        error.response.data.code === 119 ||
        error.response.data.code === 122 ||
        error.response.data.code === 141 ||
        error.response.data.code === 9900 ||
        error.response.data.code === 9908
      ) {
        logout().then((response) => {
          let { data: body } = response,
            { code } = body;
          if (code === 0) {
            if (typeof window !== "undefined") {
              // window.sessionStorage.removeItem("_org_type");
              // window.sessionStorage.removeItem("_org_name");
              // window.location.reload();
              // `${error.response.data.message}`
              // message.error('登录状态异常，请重新登录', () => {
              // });
              if (location.search) {
                window.location.href = `/login${location.search}&error=loginStatusError`;
              } else {
                window.location.href = `/login?error=loginStatusError`;
              }
            }
          }
        });
        // throw new Error('登录状态异常，请重新登录');
      }
    }
    throw new Error(
      `请求异常: ${error.message === "timeout of 8000ms exceeded"
        ? "请求超时"
        : error.message
      }`
    );
  }
);

export const createHeaders = (_userInfo) => {
  const userInfo =
    _userInfo || JSON.parse(sessionStorage.getItem("userInfo") || "{}");

  const _personal_token = sessionStorage.getItem("personal_token") || "";

  const _header = {
    "Access-Control-Allow-Credentials": "*",
    "Content-Type": "application/json",
    _menu_id: userInfo ? escape(userInfo._menu_id || "-1") : "-1",
    _oid: userInfo ? escape(userInfo._oid || "-1") : "-1",
    _tk: userInfo ? escape(userInfo._tk || "-1") : "-1",
    _uid: userInfo ? escape(userInfo._uid || "1") : "-1",
    _un: userInfo ? escape(userInfo._un || "-1") : "-1",
    _type: userInfo ? escape(userInfo._type || "2") : "2",
    _org_name: userInfo ? escape(userInfo._org_name || "-1") : "-1",
    _org_type: userInfo ? escape(userInfo._org_type || "-1") : "-1",
    // _org_type: 102801,
    // _uoid: userInfo ? escape(userInfo.sel_org_id || '-1') : '-1',
    // _region_id: userInfo ? escape(userInfo._region_id || '-1') : '-1',
    _region_id: __TOG__.regionId || process.env.region_id,
    _personal_token,
  };

  return _header;
};
//消息数据剥离标准形式
const fetch = (res, showMsg = true) => {
  let { status, data: body } = res;
  let msgStr;

  if (status === 200) {
    let { code, message: messageInfo, status, data } = body;
    if (code === 0 && status === 200) {
      return data;
    } else {
      msgStr = data || messageInfo;
    }
  } else {
    msgStr = status + "错误";
  }

  if (msgStr && showMsg) {
    message.error(msgStr);
  }
};
//消息数据剥离带分页形式
const fetchList = (res, showMsg = true) => {
  let { status, data: body } = res;
  let msgStr;

  if (status === 200) {
    let {
      code,
      message: messageInfo,
      status,
      data,
      pageNum,
      pageSize,
      total,
    } = body;
    if (code === 0 && messageInfo === "success" && status === 200) {
      return { data, pageNum, pageSize, total };
    } else {
      msgStr = data || messageInfo;
    }
  } else {
    msgStr = status + "错误";
  }

  if (msgStr && showMsg) {
    message.error(msgStr);
  }
};
//消息数据剥离操作处理形式（删除 撤销 推送等 只需要知道成功与否）
const fetchOP = (res, showMsg = true) => {
  let { status, data: body } = res;
  let msgStr;
  let success = false;

  if (status === 200) {
    let { code, message: messageInfo, status, data } = body;
    if (code === 0 && messageInfo === "success" && status === 200) {
      msgStr = "操作成功";
      success = true;
    } else {
      msgStr = data || messageInfo;
      success = false;
    }
  } else {
    msgStr = status + "错误";
    success = false;
  }

  if (msgStr && showMsg) {
    message[success ? "success" : "error"](msgStr);
  }

  return success;
};

module.exports = {
  // 浏览器端的请求
  http: {
    get(url, data = {}, option = {}) {
      return handleRequest({ url, data, option });
    },
    post(url, data = {}, option = {}) {
      return handleRequest({
        url,
        option,
        data,
        type: "post",
      });
    },
    delete(url, data = {}, option = {}) {
      return handleRequest({
        url,
        option,
        data,
        type: "delete",
      });
    },
    put(url, data = {}, option = {}) {
      return handleRequest({
        url,
        option,
        data,
        type: "put",
      });
    },
  },
  headers: () => {
    return {
      "Access-Control-Allow-Credentials": "true",
      // 'Content-Type': 'application/json',
      _tk: getToken(),
      _uid: getUserId(),
      _un: getName(),
      _type: getType(),
      _oid: getOid(),
      _org_type: getOrgType(),
      _menu_id: getMenuId(),
      _org_name: getOrgName(),
      _region_id: getRegionId(),
      "x-csrf-token": getCsrf(),
    };
  },
  formHeaders: () => {
    return {
      "Access-Control-Allow-Credentials": "true",
      _tk: getToken(),
      _uid: getUserId(),
      _un: getName(),
      _type: getType(),
      _oid: getOid(),
      _org_type: getOrgType(),
      _menu_id: getMenuId(),
      _org_name: getOrgName(),
      _region_id: getRegionId(),
      "x-csrf-token": getCsrf(),
    };
  },
  token: getToken(),
  userId: getUserId(),
  userName: getName(),
  type: getType(),
  oId: getOid(),
  oName: getName(),
  csrfToken: getCsrf(),
  /**
   * 过滤参数对象的特殊值zhulu
   * 例如过滤 {a:undefined, b:1, c:0} 中的a:undefined   => {b:1, c:0}
   * @param {Object} params          参数对象
   * @param {Array}  [filterValues=[ undefined     ]] [可选，可定义黑名单要过滤的值，默认undefined]
   * @return {Object} 过滤后的新参数对象
   */
  filterParamsValue: (params, filterValues = [undefined /*,null,false,0*/]) => {
    let new_params = {};
    for (var k in params) {
      if (params.hasOwnProperty(k) && filterValues.indexOf(params[k]) === -1) {
        new_params[k] = params[k];
      }
    }
    return new_params;
  },
  fetch,
  fetchList,
  fetchOP,
  createHeaders,
  getRegionId
};
