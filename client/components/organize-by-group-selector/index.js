import React, { Component } from "react";
import { Table, Pagination } from "antd";
import OrganizeModal from "./sub-components/organize-modal";

import "./index.less";
import propTypes from "prop-types";

import { addKeyToTableDataSource } from "tool/util";

class OrganizeByGroupSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      // total: 50,
      pageSize: 10,
      // current: 1,
      visible: false
    }


    this.showModal = this.showModal.bind(this);
    this.hideModal = this.hideModal.bind(this);
    this.exportData = this.exportData.bind(this);
  }

  exportData() {
    const { dataSource } = this.state;
    const output = JSON.parse(JSON.stringify(dataSource));
    if (output && Array.isArray(output) && output.length !== 0) {
      output.forEach((item) => {
        delete item.key;
      })
    }
    return output;
  }

  showModal() {
    this.setState({
      visible: true
    });
    const { dataSource } = this.state;
    // console.log(dataSource);
    // 当前如果表格有数据，则将表格数据初始化到组件中
    if (dataSource && Array.isArray(dataSource) && dataSource.length !== 0) {
      this.organizeModal.initData(dataSource);
    }
  }

  hideModal() {
    this.setState({
      visible: false
    });
  }

  //从表格删除一个选中的组织
  removeItem(index, org) {
    const { dataSource } = this.state;
    const { getSelectedValues } = this.props;
    dataSource.splice(org.key - 1, 1);
    // 重置表格序号
    addKeyToTableDataSource(dataSource);
    this.organizeModal.removeItem(org.key - 1, org);
    getSelectedValues && getSelectedValues(dataSource);
    this.setState({
      dataSource
    });
  }

  render() {
    const _this = this;
    const { getSelectedValues, canEdit, distinguishOwner, isShowTable } = this.props;
    const {
      dataSource,
      // total,
      // current,
      pageSize,
      visible } = this.state;



      const baseColumns = [
        {
          title: "序号",
          dataIndex: "key",
          align: "center",
          width: 90
        },
        {
          title: "组织名称",
          dataIndex: "org_name",
          align: "center",
          width: 580
        }
      ];
      const handlerColumns = [
        {
          title: "操作",
          align: "center",
          width: 210,
          render(text, record, index) {
            return (
              <div>
                <a onClick={() => _this.removeItem(index, record)}>删除</a>
              </div>
            )
          }
        }
      ];
      const columns = canEdit ? [...baseColumns, ...handlerColumns] : [...baseColumns];


      const tableProps = {
        bordered: true,
        columns,
        dataSource,
        pagination: {
          size: "small",
          showQuickJumper: true,
          showSizeChanger: true,
          pageSize,
          onShowSizeChange: (current, size) => {
            this.setState({
              pageSize: size
            });
          },
          showTotal: (total, range) => {
            return `共${total}条记录，${Math.ceil(total / pageSize)}页`
          }
        },
        scroll: { y: 560 }
      }

      const organizeModalProps = {
        distinguishOwner,
        visible,
        dataSource,
        hideModal: () => {
          this.hideModal()
        },
        loadOrganizeData: (data) => {
          getSelectedValues && getSelectedValues(JSON.parse(JSON.stringify(data)));
          addKeyToTableDataSource(data);
          this.setState({
            dataSource: data,
            visible: false
          });
        },
        ref: (ref) => {
          this.organizeModal = ref;
        }
      }

    return (
      <div className="organize-by-group-selector-wrapper">
        <div className="organize-count">
          {
            canEdit &&
            <a className="selector-trigger" onClick={this.showModal}>分组选择组织</a>
          }
          <span className="select-count"> 已选({dataSource.length})</span>
        </div>
        {
          (isShowTable && dataSource && Array.isArray(dataSource) && dataSource.length !== 0) &&
          <div className="organize-table">
            {/* <div className="organize-count">
            已选({dataSource.length})
          </div> */}

            <div className="organize-table-wrapper">
              <Table {...tableProps} />
            </div>

          </div>
        }
        {/* <Pagination {...paginationProps} /> */}
        <OrganizeModal {...organizeModalProps} />
      </div>
    )
  }
}
OrganizeByGroupSelector.propTypes = {
  canEdit: propTypes.bool,
  exportData: propTypes.func,
  inputData: propTypes.array,
  getSelectedValues: propTypes.func,
  isShowTable: propTypes.bool,
}

OrganizeByGroupSelector.defaultProps = {
  canEdit: true,
  exportData: (data) => { console.log("默认方法导出数据", data) },
  inputData: [],
  getSelectedValues: data => data,
  isShowTable: true,
}

export default OrganizeByGroupSelector;
