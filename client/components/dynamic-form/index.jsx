import React, { PureComponent } from "react";
import { Form, Button, Row, Col, Spin } from "antd";
import { formatParams } from "tool/util";
import {
  formatDefaultValue,
  formatFieldDefaultValue,
  formatInitValue,
  formatSubmitData,
  formatRules,
  getAllDefaultValue,
} from "./utils";
import SectionTitle from "components/activity-form/sub-components/SectionTitle";
import FieldItem from "./FieldItem";
import moment from "moment";
import "./index.less";

const { Item } = Form;

/**
 * 动态表单
 */
class DynamicForm extends PureComponent {
  constructor(props) {
    super(props);
    this.renderList = this.renderList.bind(this);
    this.computeCondition = this.computeCondition.bind(this);
    this.onSubmit = this.onSubmit.bind(this);
  }
  onSubmit(e) {
    e.preventDefault();
    const { form, onSubmit, fieldList, extraValue = {} } = this.props;
    const defaultValue = getAllDefaultValue(fieldList);
    form.validateFieldsAndScroll((err, values) => {
      if (!err) {
        const res = formatSubmitData(fieldList, {
          ...defaultValue,
          ...values,
          ...extraValue,
        });
        onSubmit && onSubmit(res);
      }
    });
  }

  renderList(list, realityShow, displayStyle) {
    const {
      label,
      defaultValue,
      key,
      rules,
      attr = {},
      type,
      layout = {},
    } = list;
    const { labelCol = 8, wrapperCol = 16 } = layout;
    const { getFieldDecorator } = this.props.form;
    const { methods, form, extraAttr, layout: formLayout } = this.props;

    const getMethods = methods(form) || {};
    const itemExtraAttr = extraAttr[key] || {};
    const isInline = formLayout === "inline";
    let defaultVal = defaultValue;
    if (
      type === 6 &&
      !(defaultValue instanceof moment) &&
      defaultValue !== undefined
    ) {
      defaultVal = undefined;
    }
    if (
      [4, 5].indexOf(type) !== -1 &&
      !(defaultValue instanceof Array) &&
      defaultValue !== undefined
    ) {
      defaultVal = undefined;
    }
    return (
      <Item
        label={label}
        key={key}
        style={displayStyle}
        labelCol={isInline ? null : { span: labelCol }}
        wrapperCol={isInline ? null : { span: wrapperCol }}
      >
        {getFieldDecorator(key, {
          initialValue: defaultVal,
          rules: realityShow
            ? formatRules(rules, getMethods, type, realityShow)
            : [],
        })(
          <FieldItem
            {...attr}
            label={label}
            type={type}
            onBlur={getMethods[attr.onBlur]}
            methods={getMethods}
            form={form}
            itemKey={key}
            {...itemExtraAttr} // 前端传入的额外属性高于一切
          />
        )}
      </Item>
    );
  }
  // 计算条件
  computeCondition(condition) {
    const { extraValue, form } = this.props;
    const { getFieldValue } = form;
    if (Array.isArray(condition) && condition.length > 0) {
      for (let i = 0; i < condition.length; i++) {
        const { key, value } = condition[i];
        let formValue = getFieldValue(key);
        // condition中的value 值只有string， 如级联选择器[1, 2, 3] 会被转换为 "1,2,3"
        if (Array.isArray(formValue)) {
          formValue = formValue.join(",");
        }
        if (formValue === value || extraValue[key] === value) {
          return true;
        }
      }
      return false;
    } else {
      return true;
    }
  }

  render() {
    const {
      fieldList = [],
      className,
      layout,
      loading,
      formItemLayout,
      footer,
    } = this.props;
    const isInline = layout === "inline";
    const LayoutRow = isInline ? React.Fragment : Row;
    const itemLayout = isInline ? {} : formItemLayout;
    return (
      <Form
        {...itemLayout}
        layout={layout}
        className={`dynamic-form ${className}`}
        onSubmit={this.onSubmit}
      >
        <Spin spinning={loading}>
          <LayoutRow>
            {fieldList.map((item) => {
              // console.log(item,'0001---2234234');
              const { layout = {}, condition = [], show } = item;
              const { col = 12, offset = 0 } = layout;
              const realityShow = this.computeCondition(condition) && show;
              const displayStyle = !realityShow ? { display: "none" } : {};
              if (Number(item.type) === 11) {
                return (
                  <Col span={24} key={item.key} style={displayStyle}>
                    <SectionTitle title={item.label} />
                  </Col>
                );
              }
              if (isInline) {
                return this.renderList(item, realityShow, displayStyle);
              }
              return (
                <Col
                  span={col}
                  offset={offset}
                  key={item.key}
                  style={displayStyle}
                >
                  {this.renderList(item, realityShow)}
                </Col>
              );
            })}
          </LayoutRow>
          {footer && footer()}
        </Spin>
      </Form>
    );
  }
}

DynamicForm.defaultProps = {
  methods: (form) => {}, // 传入的自定义事件， 格式： (form: Form) => { [string]: (rule, value, callback) => void | (e) => void }
  extraValue: {}, // 表单格外的value值,由前端控制  格式： { [string]: any }
  extraAttr: {}, // 表单组件额外的属性，由前端控制（优先级大于后端返回的attr）， 格式： { [string]: object}
  layout: "horizontal",
  formItemLayout: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  }, // 表单item布局, 格式：{ labelCol: {span: string}, wrapperCol: string }
  className: "", // 类型, 格式: string
  loading: false, // 是否加载状态, 格式： boolean
  onSubmit: (value) => {
    console.log(value);
  }, // 提交事件
  footer: () => {}, // 表单的底部。 格式： ReactNode
};

DynamicForm.formatDefaultValue = formatDefaultValue;
DynamicForm.formatInitValue = formatInitValue;
DynamicForm.formatFieldDefaultValue = formatFieldDefaultValue;

export default Form.create()(DynamicForm);
