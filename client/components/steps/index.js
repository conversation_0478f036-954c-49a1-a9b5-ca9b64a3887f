import React from "react";
import "./style.less";
import Step from "./sub-components/step";

import propTypes from "prop-types";

class Steps extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { stepList, style, current } = this.props;
    return (
      <div className="steps-container" style={style}>
        {/* 步骤进度组件 */}
        {
          stepList && stepList.map((stepItem, index) => {
            return (
              <Step key={index}  {...stepItem} index={index} current={current} noSeparator={index === stepList.length - 1} />
            )
          })
        }
        {
          stepList.length === 0 && "标题"
        }
      </div>
    )
  }
}

Steps.propTypes = {
  stepList: propTypes.array,
  style: propTypes.object,
  // 当前步骤
  current: propTypes.number
}

Steps.defaultProps = {
  stepList: [],
  style: {},
  current: 0
}

export default Steps;