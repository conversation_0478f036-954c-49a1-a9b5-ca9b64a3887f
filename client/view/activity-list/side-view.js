import React, {Component} from 'react';
import {Form, Input, Button, Checkbox, Radio, Divider, Select, Row, Col, Modal} from 'antd';
import SiderContent from 'components/sider-content';
import PeopleCover from 'components/people-cover';

export default ({
                    collapsed,
                    isHideWrap,
                    onClose,
                    modalTitle,
                    workflowList,
                    handleSubmit,
                    handleCancel,
                    approveList,
                    copyToList,
                    form,
                    formData,
                    workflowChangeHandler,
                    onAddHandler,
                    onRemoveHandler
                }
) => {
    const params = {
            collapsed,
            onClose,
            title: modalTitle,
            isHideWrap
        },
        formItemLayout = {
            labelCol: {span: 5},
            wrapperCol: {span: 7}
        },
        formItemWideLayout = {
            ...formItemLayout,
            wrapperCol: {span: 14}
        },
        TextArea = Input.TextArea,
        FormItem = Form.Item,
        RadioGroup = Radio.Group,
        OptionGroup = Select.OptGroup,
        Option = Select.Option,
        CheckboxGroup = Checkbox.Group,
        {getFieldDecorator} = form,
        {
            name,
            hid,
            department_name,
            department_id,
            jurisdiction,
            home_page,
            title,
            keyword,
            description,
            workflow_id,
            mustApprove,
            template,
            is_default,
            parent_column_id
        } = formData,
        {
            page_template,
            list_template,
            doc_template,
            page_template_app,
            list_template_app,
            doc_template_app
        } = template;

    const mustApproveChange = (e) => {
        let target = e.target;
        if (!target.checked) {
            Modal.error({
                title: '系统提示',
                content: (
                    <p>取消审批，会造成此栏目活动在不受监督的情况下被发布。</p>
                ),
                okText: '确认不需要审批'
            })
        }
    };

    const initPeopleCoverProps = (people, index) => {
        return {
            onClose: () => onRemoveHandler(people, index),
            record: people,
            name: people.user_name
        }
    };

    //如果当前工作流程不在下拉列表中，则新增自定义项目
    const ifOtherItem = (workflow_id, workflowList, workflow_name) => {
        // console.log(workflow_id, workflow_name);
        if (workflow_id === -999) {
            return null;
        }
        if (Array.isArray(workflowList) && workflowList.length !== 0) {
            for (let i = 0, len = workflowList.length; i < len; i++) {
                if (workflowList[i].workflowId === workflow_id) {
                    return null;
                }
            }
        }
        return (
            <Option key={workflow_id}
                    value={workflow_id}
                    title={workflow_name || '用户自定义审批'}>
                {/*{workflow.workflowId}*/}
                <span>{workflow_name || '用户自定义审批'}</span>
            </Option>
        );
    };

    return <SiderContent {...params}>
        <Form className="news-columns-siderbar" onSubmit={handleSubmit} style={{marginTop: 30}}>
            <FormItem label='栏目名称' {...formItemLayout}>
                {
                    getFieldDecorator('name', {
                        //校验规则
                        rules: [
                            {
                                required: true, message: '请输入栏目名称'
                            }, {
                                max: 30, message: '最多只能输入30个字'
                            }
                        ],
                        //初始值
                        initialValue: formData.name
                    })(
                        <Input placeholder='请输入'/>
                    )
                }
            </FormItem>
            { parent_column_id === 0 ?
            <FormItem className="item-mt0" label={'默认栏目'} extra="上级组织推送互动会放到此默认栏目" {...{
                labelCol: {span: 5},
                wrapperCol: {span: 9}
            }}>
              {
                getFieldDecorator('is_default', {
                  valuePropName: 'checked',
                  initialValue: is_default
                })(
                  <Checkbox >设为默认栏目</Checkbox>
                )
              }
            </FormItem>
            : ''}



            { /*baseNode === '0'*/1 || 1 ? '' : ''}

            {/*<FormItem label='是否隐藏' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('hid', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请选择是否隐藏'}*/}
            {/*],*/}
            {/*initialValue: formData.hid*/}
            {/*})(*/}
            {/*<RadioGroup>*/}
            {/*<Radio value={0}>不隐藏</Radio>*/}
            {/*<Radio value={1}>隐藏</Radio>*/}
            {/*</RadioGroup>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='浏览权限' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('jurisdiction', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请选择浏览权限'}*/}
            {/*],*/}
            {/*initialValue: formData.jurisdiction*/}
            {/*})(*/}
            {/*<Select>*/}
            {/*<Option value={1}>开放浏览</Option>*/}
            {/*<Option value={2}>平台用户</Option>*/}
            {/*<Option value={3}>仅自己</Option>*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='栏目首页' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('home_page', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请选择是否隐藏'}*/}
            {/*],*/}
            {/*initialValue: formData.home_page*/}
            {/*})(*/}
            {/*<RadioGroup>*/}
            {/*<Radio value={1}>频道页</Radio>*/}
            {/*<Radio value={2}>列表页</Radio>*/}
            {/*</RadioGroup>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<div className={'divider-wrapper'}>*/}
            {/*<Divider orientation={'left'}>*/}
            {/*<span className={'divider-text'}>网站样式</span>*/}
            {/*</Divider>*/}
            {/*</div>*/}
            {/*<FormItem label='频道页模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.page_template', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.page_template : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>频道页模板一</Option>*!/*/}
            {/*/!*<Option value={2}>频道页模板二</Option>*!/*/}
            {/*/!*<Option value={3}>频道页模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='列表模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.list_template', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.list_template : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>列表模板一</Option>*!/*/}
            {/*/!*<Option value={2}>列表模板二</Option>*!/*/}
            {/*/!*<Option value={3}>列表模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='文章模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.doc_template', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.doc_template : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>文章模板一</Option>*!/*/}
            {/*/!*<Option value={2}>文章模板二</Option>*!/*/}
            {/*/!*<Option value={3}>文章模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<div className={'divider-wrapper'}>*/}
            {/*<Divider orientation={'left'}>*/}
            {/*<span className={'divider-text'}>移动端样式</span>*/}
            {/*</Divider>*/}
            {/*</div>*/}
            {/*<FormItem label='频道页模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.page_template_app', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.page_template_app : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>频道页模板一</Option>*!/*/}
            {/*/!*<Option value={2}>频道页模板二</Option>*!/*/}
            {/*/!*<Option value={3}>频道页模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='列表模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.list_template_app', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.list_template_app : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>列表模板一</Option>*!/*/}
            {/*/!*<Option value={2}>列表模板二</Option>*!/*/}
            {/*/!*<Option value={3}>列表模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<FormItem label='文章模板' {...formItemLayout}>*/}
            {/*{*/}
            {/*getFieldDecorator('template.doc_template_app', {*/}
            {/*rules: [],*/}
            {/*initialValue: formData.template ? formData.template.doc_template_app : -999*/}
            {/*})(*/}
            {/*<Select placeholder='请选择'>*/}
            {/*<Option value={-999}>不设置</Option>*/}
            {/*/!*<Option value={1}>文章模板一</Option>*!/*/}
            {/*/!*<Option value={2}>文章模板二</Option>*!/*/}
            {/*/!*<Option value={3}>文章模板三</Option>*!/*/}
            {/*</Select>*/}
            {/*)*/}
            {/*}*/}
            {/*</FormItem>*/}
            {/*<div className={'divider-wrapper'}>*/}
            {/*<Divider orientation={'left'}>*/}
            {/*<span className={'divider-text'}>SEO设置</span>*/}
            {/*</Divider>*/}
            {/*</div>*/}
            {/*<FormItem label='标题' {...formItemWideLayout}>*/}
            {/*<Tooltip*/}
            {/*title={() => <TextRemain maxSize={60} value={form.getFieldValue('title')}/>}*/}
            {/*trigger={'focus'}*/}
            {/*overlayClassName={'text-remain-tooltip'}>*/}
            {/*{*/}
            {/*getFieldDecorator('title', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请输入标题'},*/}
            {/*{max: 60, message: '输入长度超出'}*/}
            {/*],*/}
            {/*initialValue: formData.title*/}
            {/*})(*/}
            {/*<Input placeholder='请输入标题'/>*/}
            {/*)*/}
            {/*}*/}
            {/*</Tooltip>*/}
            {/*</FormItem>*/}
            {/*<FormItem label='关键字' {...formItemWideLayout}>*/}
            {/*<Tooltip title={() => <TextRemain maxSize={200} value={form.getFieldValue('keyword')}/>}*/}
            {/*trigger={'focus'}*/}
            {/*overlayClassName={'text-remain-tooltip'}>*/}
            {/*{*/}
            {/*getFieldDecorator('keyword', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请输入关键字'},*/}
            {/*{max: 200, message: '输入长度超出'}*/}
            {/*],*/}
            {/*initialValue: formData.keyword*/}
            {/*})(*/}
            {/*<Input placeholder='多个关键字用逗号分开'/>*/}
            {/*)*/}
            {/*}*/}
            {/*</Tooltip>*/}
            {/*</FormItem>*/}
            {/*<FormItem label='栏目描述' {...formItemWideLayout}>*/}
            {/*<Tooltip title={() => <TextRemain maxSize={400} value={form.getFieldValue('description')}/>}*/}
            {/*trigger={'focus'}*/}
            {/*overlayClassName={'text-remain-tooltip'}>*/}
            {/*{*/}
            {/*getFieldDecorator('description', {*/}
            {/*rules: [*/}
            {/*{required: true, message: '请输入描述'},*/}
            {/*{max: 400, message: '输入长度超出'}*/}
            {/*],*/}
            {/*initialValue: formData.description*/}
            {/*})(*/}
            {/*<TextArea autosize={{minRows: 4, maxRows: 4}} placeholder='请输入标题'/>*/}
            {/*)*/}
            {/*}*/}
            {/*</Tooltip>*/}
            {/*</FormItem>*/}
            <div className={'divider-wrapper'}>
                <Divider orientation={'left'}>
                    <span className={'divider-text'}>分类默认审批类型</span>
                </Divider>
            </div>
            <Row>
                <Col span={12}>
                    <FormItem label='审批类型'
                              labelCol={{span: 10}}
                              wrapperCol={{span: 14}}>
                        {/*{JSON.stringify(workflowList)}*/}
                        {
                            getFieldDecorator('workflow_name', {
                                //校验规则
                                rules: [],
                                //初始值
                                initialValue: formData.workflow_name || ''
                                // initialValue: formData.workflow_id || -999
                            })(
                                // onChange={workflowChangeHandler}
                                <Select onSelect={workflowChangeHandler}
                                        mode={'combobox'}
                                        allowClear={true}
                                        placeholder={'请选择审批流程'}
                                >
                                    {/*<Option value={-999}>不设置(自定义)</Option>*/}
                                    {
                                        workflowList && workflowList.map((workflow, index) => {
                                            return (
                                                <Option
                                                    key={workflow.workflowId}
                                                    // value={workflow.workflowId}
                                                    value={String(workflow.workflowId)}
                                                    title={workflow.name}
                                                >
                                                    {/*{workflow.workflowId}*/}
                                                    <span>{workflow.name}</span>
                                                </Option>
                                            )
                                        })
                                    }
                                    {/*{*/}
                                    {/*ifOtherItem(formData.workflow_id, workflowList, formData.workflow_name)*/}
                                    {/*}*/}
                                    {/*<Option value={1}>类型1</Option>*/}
                                    {/*<Option value={2}>类型2</Option>*/}
                                </Select>
                            )
                        }
                    </FormItem>
                </Col>
                <Col offset={1} span={11}>
                    <FormItem>
                        {
                            getFieldDecorator('must_approve', {
                                //校验规则
                                rules: [],
                                valuePropName: 'checked',
                                //初始值
                                initialValue: formData.must_approve === 1
                            })(<Checkbox onChange={mustApproveChange}>必须审批才能发布互动</Checkbox>)
                        }
                    </FormItem>
                </Col>
            </Row>
            <FormItem label='审批人员' {...formItemWideLayout}
                // style={!form.getFieldValue('workflow_id') || form.getFieldValue('workflow_id') === -999 ? {display: 'none'} : {}}
            >
                <div className={'approval-process'}>
                    {approveList && approveList.map((approveTurn, idx) => {
                        return approveTurn.user_id && approveTurn.user_id.map((userId, index) => {
                            return (
                                <PeopleCover
                                    key={idx + '-' + index + '-' + userId}
                                    {...initPeopleCoverProps({
                                        user_name: approveTurn.user_name[index],
                                        user_id: userId,
                                        position: index,
                                        type: 'approveList'
                                    }, idx)}/>
                            )
                        });
                    })}
                    <PeopleCover
                        isAdd={true}
                        onAdd={() => onAddHandler('approveList')}
                    />
                </div>
            </FormItem>
            <FormItem label='抄送人员' {...formItemWideLayout}
                // style={!form.getFieldValue('workflow_id') || form.getFieldValue('workflow_id') === -999 ? {display: 'none'} : {}}
            >
                <div>
                    {copyToList && copyToList.map((people, index) => {
                        return (
                            <PeopleCover key={index + '-' + people.user_id} {...initPeopleCoverProps({
                                ...people,
                                type: 'copyToList'
                            }, index)}/>
                        )
                    })}
                    <PeopleCover
                        isAdd={true}
                        onAdd={() => onAddHandler('copyToList')}
                    />
                </div>
            </FormItem>
            <FormItem label="">
                <div className={'buttons-wrapper'}>
                    <Button className={'button submit-button'} onClick={handleSubmit}>提交</Button>
                    <Button className={'button cancel-button'} onClick={handleCancel}>取消</Button>
                </div>
            </FormItem>
        </Form>
    </SiderContent>
}
