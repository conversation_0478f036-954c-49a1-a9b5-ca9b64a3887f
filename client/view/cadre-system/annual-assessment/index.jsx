import { useEffect, useRef, useState } from "react";
import "./index.less";
import moment from "moment";
import {
  Button,
  Form,
  Input,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";

import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";

import {
  addOrUpdateAnnualEval,
  deleteAnnualEval,
  exportAnnualEval,
  queryAnnualEvalDetail,
  queryAnnualEvalList,
  queryByCode,
  queryGroupList,
} from "client/apis/cadre-portrait";
const { Option } = Select; // 从 Select 中导入 Option

function index({ form, history }) {

  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);
  const orgRef = useRef(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear() - 1);

  const [org_ids, setOrgIds] = useState(["1"]);

  const [modalType, setModalType] = useState("add");

  const [org_name, setOrgName] = useState("");

  const [treeData, setTreeData] = useState([]);

  const [dataSource, setDataSource] = useState([]);

  const [visible, setVisible] = useState(false);

  const [levelOption, setLevelOption] = useState([]);

  const [filterData, setFilterData] = useState({
    user_name: "",
    year: "",
    cadre_seq: "",
  });
  const [page, setPage] = useState(1);

  const [pagination, setPagination] = useState({
    total: 0,
  });

  const [seqOption, setSeqOption] = useState([]);

  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  useEffect(() => {
    loadSeq();

    loadData();

    initCode();
  }, []);

  const loadData = async (_page, org_id) => {
    setLoading(true);
    const params = {
      ...getFieldsValue(),
      page: _page || page,
      org_id: org_id || org_ids[0],
    };
    const res = await queryAnnualEvalList(params);

    if (res.data.code === 0) {
      const data = res.data.data.content;

      setDataSource(data);

      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.totalElements,
        };
      });
    } else {
      message.error(res.data.message);
    }

    setLoading(false);
  };

  const loadSeq = async () => {
    const res = await queryGroupList();

    if (res.data.code === 0) {
      setSeqOption(res.data.data);
    } else {
      message.error(res.data.message);
    }
  };

  const initCode = async () => {
    const res = await queryByCode({ code: 95108 });
    if (res.data.code === 0) {
      setLevelOption(res.data.data);
    } else {
      message.error(res.data.message);
    }
  };

  const onChange = (orgs, org) => {
    setPage(1);

    setOrgIds(() => orgs);

    setOrgName(org.name);

    loadData(1, orgs[0]);
  };

  const onExport = async () => {
    setExportLoading(true);

    const params = getFieldsValue();

    const res = await exportAnnualEval({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);

    setModalType("add");
  };

  const onDel = async (record) => {
    const res = await deleteAnnualEval({
      annual_eval_id: record.annual_eval_id,
    });

    if (res.data.code === 0) {
      loadData();
    } else {
      message.error(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);

    setModalType("edit");

    queryAnnualEvalDetail({
      annual_eval_id: record.annual_eval_id,
    }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        data.level = data.level ? String(data.level) : undefined;

        modalFormRef.current.setFieldsValue(data);
        // queueMicrotask(() => {
        // });
      } else {
        message.error(res.data.message);
      }
    });
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=2&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const columns = [
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: '6%',
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "职务",
      dataIndex: "current_job",
      key: "current_job",
      width: 100,
      align: "center",
      render: (text) => {
        return <div style={{ textAlign: 'left' }}>{text}</div>
      }
    },
    {
      title: "综合得分",
      dataIndex: "score",
      key: "score",
      width: '6%',
      align: "center",
    },
    {
      title: "序列排名",
      dataIndex: "rank",
      key: "rank",
      width: '6%',
      align: "center",
    },
    {
      title: "等次",
      dataIndex: "level",
      key: "level",
      width: '12%',
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: '8%',
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);

      loadData(page);
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据

    setPage(1);

    loadData(1);
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;

          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }

        const res = await addOrUpdateAnnualEval({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);

          message.success("操作成功");

          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator, setFieldsValue } = form;

    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout}>
        {getFieldDecorator("annual_eval_id")(<Input type="hidden" />)}
        {getFieldDecorator("user_id")(<Input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 20 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入姓名" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(
            <DateSingle placeholder="请选择" type="month" allowClear={false} isWrap={false} />
          )}
        </Form.Item> */}
        <Form.Item label="考核年度">
          {getFieldDecorator("year", {
            rules: [{ required: true, message: "请输入年度考核" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="综合得分">
          {getFieldDecorator("score", {
            rules: [{ required: false, message: "请输入综合得分" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="排名">
          {getFieldDecorator("rank")(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="等次">
          {getFieldDecorator("level", {
            rules: [{ required: true, message: "请选择等次" }],
          })(
            <Select placeholder="请选择">
              {levelOption.map((item) => {
                return (
                  <Select.Option key={item.op_key}>
                    {item.op_value}
                  </Select.Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
      </Form>
    );
  });


  // 获取当前年份和从2019年至今的年份数组
  const years = [];
  for (let year = 2019; year <= new Date().getFullYear(); year++) {
    years.push({ value: year, label: year.toString() });
  }

  const handleYearChange = (value) => {
    form.setFieldsValue({ year: value });

  };
  return (
    <div className="annual-assessment">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} ref={(ref) => (orgRef.current = ref)} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "200px" }} placeholder="请输入" />
              )}
            </Form.Item>

            <Form.Item label="年度">
              {getFieldDecorator("year", {
                initialValue: selectedYear
              })(
                <Select
                  style={{ width: '150px' }}
                  onChange={handleYearChange}

                >
                  {years.map(year => (
                    <Option key={year.value} value={year.value}>
                      {year.label}
                    </Option>
                  ))}
                </Select>
              )}
            </Form.Item>

            <Form.Item label="干部序列">
              {getFieldDecorator("group_id")(
                <Select style={{ minWidth: "200px" }} placeholder="请选择">
                  {seqOption.map((item) => {
                    return (
                      <Select.Option value={item.group_id} key={item.group_id}>
                        <div style={{ whiteSpace: "normal" }}>
                          {item.group_name}
                        </div>
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
          rowKey="user_id"
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "年度考核"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
