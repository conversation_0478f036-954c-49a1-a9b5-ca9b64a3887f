import React, { Component } from 'react';
import { Layout } from 'antd';
import PropTypes from 'prop-types';
import { Link, Route } from 'dva/router';

import './index.less';

const { Sider } = Layout,
	defaultFunc = () => {
	};


/**
 * 公共侧边栏滑动界面   类似于点击侧边栏出现
 * <AUTHOR>
 * @example
 * 打开
 * open(){
 * 		// 保证出现动画正常 需要先把外壳显示出来在执行内部动画
		dispatch( { type: 'systeManage/toggleHideWrap' } );
		setTimeout( () => {
			dispatch( { type: 'systeManage/toggleCollapsed' } );
		}, 0 )
 * }
 *
 * 关闭
 * close(){
	// 关闭的时候 等待动画完毕的时候执行隐藏外壳
	dispatch( { type: 'systeManage/toggleCollapsed' } );
	setTimeout( () => {
		isSport = false;
		dispatch( { type: 'systeManage/toggleHideWrap' } );
	}, 300 )
 * }
 */
const SiderContent = ( { collapsed, isCloseClickMask = false, title = '', isHideWrap = false, width = 700, children, siderStyle = {}, onClose = defaultFunc } ) => {

	if ( typeof document !== 'undefined' ) {
		if ( !collapsed ) {
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = 'auto';
		}
	}
	return (
		<div className="sider-content-wrap" style={{ display: isHideWrap ? 'block' : 'none' }}>
			<div className="mask"
				 style={{
					 background: `rgba(0,0,0,${!collapsed ? .5 : 0})`,
					 position: `${!collapsed ? 'fixed' : 'absolute'}`
				 }}
				 onClick={e => {
					 if ( !isCloseClickMask ) onClose( e );
				 }}
			/>
			<Layout hasSider className="sider-content">
				<Sider
					className="sider"
					trigger={null}
					collapsible
					width={width}
					collapsedWidth={0}
					collapsed={collapsed}
					style={siderStyle}
				>
					{title && <div className="header">{title}</div>}
					{children}
				</Sider>
			</Layout>
		</div>
	);
}

SiderContent.propTypes = {
	// 控制是否收起 true 为收起
	collapsed: PropTypes.bool.isRequired,
	// 是否显示外框 需要和 collapsed 配合使用
	isHideWrap: PropTypes.bool.isRequired,
	// 是否关闭点击黑色背景关闭边栏  default:false
	isCloseClickMask: PropTypes.bool,
	// 当前可视区的宽度  default:300
	width: PropTypes.number,
	// 自定义侧边栏样式
	siderStyle: PropTypes.object,
	// 需要渲染的子组件
	children: PropTypes.element,
	// 当点击黑色区域时 关闭边栏
	onClose: PropTypes.func,
	// 侧边栏主标题
	title: PropTypes.oneOfType([
		PropTypes.string, PropTypes.element
	])
};

export default SiderContent;