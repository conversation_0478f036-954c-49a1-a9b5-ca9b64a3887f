import React, { PureComponent } from 'react';
import { message, TreeSelect } from 'antd';
import { getOrgTree, locateOrgTree } from 'client/apis/organize';

const { TreeNode } = TreeSelect;

/**
 * 组织选择器
 */
export default class OrgSelector extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      treeData: [],
      treeDefaultExpandedKeys: [],
      myValue: "",

    };
    this.onLoadData = this.onLoadData.bind(this);
    this.locateOrgTree = this.locateOrgTree.bind(this);
    this.initTree = this.initTree.bind(this);
  }
  componentDidMount() {
    this.initTree();
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.value !== this.props.value && nextProps.value !== this.state.cacheVal) {
      this.setState({
        cacheVal: nextProps.value,
      });
    }
  }
  // 初始化组织树
  initTree() {
    const { org_type } = this.props;
    getOrgTree({
      org_id: sessionStorage.getItem('_oid'),
      tree_type: 2,
      show_code: 1,
      load_root: 1,
      org_type: org_type || sessionStorage.getItem('_org_type'),
    }).then((res) => {
      if (res.status === 200) {
        const { data } = res.data;
        this.setState({
          treeData: data,
          treeDefaultExpandedKeys: [data[0] && String(data[0].org_id)],
        });
      }
    });
  }
  // 异步加载组织数据
  onLoadData(treeNode) {
    const { value, dataRef } = treeNode.props;
    return new Promise((resolve) => {
      getOrgTree({
        org_id: value,
        tree_type: 2,
        show_code: 1,
        load_root: 0,
        org_type: sessionStorage.getItem('_org_type'),
      })
        .then((res) => {
          if (res.status === 200) {
            dataRef.children = res.data.data;
            treeNode.props = {...treeNode.props};
            this.setState({ treeData: [...this.state.treeData] });
          }
        })
        .finally(() => {
          resolve();
        });
    });
  }
  // 定位组织（返回Promise）
  locateOrgTree(org_id, otherParams = {}) {
    return new Promise((resolve, reject) => {
      const root_org_id = sessionStorage.getItem('_oid');
      const params = {
        org_id,
        root_org_id,
        tree_type: 2,
        show_code: 1,
        load_root: 1,
        org_type: sessionStorage.getItem('_org_type'),
        ...otherParams
      };

      locateOrgTree(params).then(res => {
        if (res.status === 200) {
          const { data } = res.data;
          this.setState({
            treeData: data,
            treeDefaultExpandedKeys: [data[0] && String(data[0].org_id)],
          });
          resolve();
        } else {
          reject();
        }
      }).catch(() => {
        reject();
      });
    });
  }
  render() {
    const { treeData, treeDefaultExpandedKeys } = this.state;
    const { placeholder, itemKey } = this.props;
    return (
      <TreeSelect
        {...this.props}
        style={{ width: '100%' }}
        treeNodeFilterProp='label'
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder={placeholder}
        treeDefaultExpandedKeys={treeDefaultExpandedKeys}
        loadData={this.onLoadData}
        treeData={formatThreeData(treeData)}
        className={`dynamic-form-org-select-${itemKey}`}
        getPopupContainer={(trigger) => trigger = document.querySelector(`.dynamic-form-org-select-${itemKey}`)}
        showSearch
        allowClear
      />
    );
  }
}

const formatThreeData = (data = []) => {
  return data.map((item) => {
    return {
      label: item.name,
      title: item.name,
      value: String(item.org_id),
      key: String(item.org_id),
      dataRef: item,
      isLeaf: item.child_org_num === 0,
      children: formatThreeData(item.children || []),
    };
  });
};
