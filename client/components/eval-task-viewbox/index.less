.eval-task-viewbox{
  .task-modal-row{
    padding: 0 10px 10px;
    .ant-form-item{
      margin-bottom:0;
    }

    .label{
      text-align:right;
      &.formItemLabel{
        margin-top:9px;
      }

      &.label-validator:before {
        display: inline-block;
        margin-right: 4px;
        content: "*";
        font-family: SimSun;
        line-height: 1;
        font-size: 14px;
        color: #f5222d;
      }
    }

    .download-buttons{
      margin-bottom: 10px;
    }
  }
  .record-timeline-panel{
    padding: 20px 10px 0;
  }
}

.eval-task-viewbox-titleTips {
  .ant-tooltip-arrow {
    border-right-color: #FFF !important;
    border-bottom-color: #FFF !important;
    border-top-color: #FFF !important;
  }
  .ant-tooltip-inner {
    background-color: #FFF;
    span {
      color: #999;
      font-size: 13px;
      span {
        color: #8FC31F;
      }
    }
  }
}

.eval-task-viewbox-file-item{
  height: 55px;
  .eval-task-viewbox-file-list:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .eval-task-viewbox-file-list {
    display:inline-block;
  }
  .eval-task-viewbox-file-list .left {
    float: left;
    width: 40px;
  }
  .eval-task-viewbox-file-list .left .file-icon {
    font-size: 35px;
  }
  .eval-task-viewbox-file-list .right {
    float: right;
    display:inline-block;
    text-align: right;
  }
  .eval-task-viewbox-file-list .content {
    margin-left: 40px;
    margin-right: 65px;
  }
  .eval-task-viewbox-file-list .content p {
    margin: 0;
  }
  .eval-task-viewbox-file-list .content .size {
    color: #999;
  }
}

.record-timeline-panel{
  .warning{
    .ant-timeline-item-head-custom {
      .record-timeline-key{
        .icon{
          margin-left: 0;
          background: #F46E65;
        }
        span{
          display: inline-block;
          width: 94px;
          color:#000;
        }
      }
    }
    .info {
      b {
        color: #F46E65;
      }
    }
  }
  .ant-timeline-item-tail{
    left:104px;height:calc(100% - 21px);top:1.2em;
  }
  .ant-timeline-item-head-custom{
    -webkit-transform: translate(0, -32%);
    transform: translate(0, -32%);
    background: none;
    .record-timeline-key{
      .icon{float:right; margin-left:7px;width:16px;height:16px;background:#999;border-radius:8px;}
      span{color:#666}
    }
  }

  .ant-timeline-item-content{
    padding-bottom:10px;margin-left:120px;
    .info {
      b{font-size:16px;margin-right:30px;}
    }
    .desc{
      color:#666;
    }
    .file{
      margin-top:15px;
    }
  }
}
