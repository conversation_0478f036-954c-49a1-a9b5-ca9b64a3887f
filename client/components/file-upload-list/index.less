.file-upload-component {
  .file-upload-btn {
    position: relative;
  }
  input[type='file'] {
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 10;
    left: 0;
    top: 0;
    cursor: pointer;
  }
  .file-upload-item {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;

    p {
      margin-bottom: 0;
    }
    .file-name {
      margin-right: 8px;
    }
    .ant-btn-link {
      padding: 0 10px;
      line-height: 20px;
      height: 20px;
    }
  }
  .file-icon {
    font-size: 40px;
  }
  .file-upload-info {
    flex: 1;
    margin-left: 5px;
  }
  .file-del-icon {
    cursor: pointer;
  }
  .file-upload-propress {
    width: 200px;
  }
  .file-size {
    font-size: 12px;
    color: #666;
  }
  .file-upload-card {
    display: flex;
    flex-direction: row;
  }
  .file-upload-card-item {
    width: 104px;
    height: 104px;
    margin: 0 8px 8px 0;
  }
  .file-upload-card-img-item {
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    &:hover {
      .obscuration {
        display: flex;
        color: #FFF;
      }
    }
    .obscuration {
      position: absolute;
      display: none;
      width: 86px;
      height: 86px;
      left: 8px;
      top: 8px;
      background-color: rgba(0, 0, 0, 0.5);
      // display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      padding: 0 10px;
      span {
        color: #fff;
        cursor: pointer;
        &:hover {
          color: #f46e65;
        }
      }
    }
  }
  .file-upload-card-progress-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .file-upload-card-btn {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 104px;
    height: 104px;
    text-align: center;
    vertical-align: top;
    background-color: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    -webkit-transition: border-color 0.3s ease;
    transition: border-color 0.3s ease;
    &:hover {
      border-color: #1890ff;
    }
  }
  .file-upload-card-icon {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .tip-text {
    font: 14px;
    color: #c8c8c8;
  }
  .file-upload-card-btn-disable{
    input{
      cursor: not-allowed;
    }
  }
}
