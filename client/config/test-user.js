/**
 * 用于没有登录接口时使用的 临时测试账户 和按钮权限
 */

const menus = [{
        'id': '01',
        'pid': '',
        'name': '角色管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '02',
        'pid': '',
        'name': '部门管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '03',
        'pid': '',
        'name': '人员管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '04',
        'pid': '',
        'name': '个人信息',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0401',
        'pid': '04',
        'name': '密码修改',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0402',
        'pid': '04',
        'name': '手机号修改',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    },
        /*{
        'id': '05',
        'pid': '',
        'name': '单位信息',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    },*/ {
            'id': '06',
            'pid': '',
            'name': '流程审批',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '0601',
            'pid': '06',
            'name': '待我审批',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
		'id': '0602',
		'pid': '06',
		'name': '发起审批',
		'isLeaf': '1',
		'b_info': [
			{
				'id': '按钮编号',
				'name': '按钮名称',
				'url': 'url'
			}
		]
	}, {
            'id': '0603',
            'pid': '06',
            'name': '审批类型管理',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '07',
            'pid': '',
            'name': '标签',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, /* {
		'id': '0701',
		'pid': '07',
		'name': '标签组管理',
		'isLeaf': '1',
		'b_info': [
			{
				'id': '按钮编号',
				'name': '按钮名称',
				'url': 'url'
			}
		]
	},*/ {
            'id': '0702',
            'pid': '07',
            'name': '标签管理',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '08',
            'pid': '',
            'name': '活动栏目管理',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '09',
            'pid': '',
            'name': '活动管理',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '0901',
            'pid': '09',
            'name': '全部活动',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '0902',
            'pid': '09',
            'name': '新建投票',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, /* {
		'id': '0903',
		'pid': '09',
		'name': '新建问卷调查',
		'isLeaf': '1',
		'b_info': [
			{
				'id': '按钮编号',
				'name': '按钮名称',
				'url': 'url'
			}
		]
	}, {
		'id': '0904',
		'pid': '09',
		'name': '新建有奖竞答',
		'isLeaf': '1',
		'b_info': [
			{
				'id': '按钮编号',
				'name': '按钮名称',
				'url': 'url'
			}
		]
	},*/ {
            'id': '10',
            'pid': '',
            'name': '活动奖励物品',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '1001',
            'pid': '10',
            'name': '物品发布',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '1002',
            'pid': '10',
            'name': '物品管理',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '1003',
            'pid': '10',
            'name': '中奖纪录',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '1004',
            'pid': '10',
            'name': '奖品发货',
            'isLeaf': '1',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }, {
            'id': '12',
            'pid': '',
            'name': '系统管理员管理',
            'isLeaf': '2',
            'b_info': [
                {
                    'id': '按钮编号',
                    'name': '按钮名称',
                    'url': 'url'
                }
            ]
        }],
    zoulMenus = [{
        'id': '03',
        'pid': '',
        'name': '人员管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '05',
        'pid': '',
        'name': '单位信息',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '06',
        'pid': '',
        'name': '流程审批',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0602',
        'pid': '06',
        'name': '发起审批',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '09',
        'pid': '',
        'name': '活动管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0902',
        'pid': '09',
        'name': '新建投票',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0903',
        'pid': '09',
        'name': '新建问卷调查',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0904',
        'pid': '09',
        'name': '新建有奖竞答',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }],
    jxfMenus = [{
        'id': '04',
        'pid': '',
        'name': '个人信息',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0401',
        'pid': '04',
        'name': '密码修改',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0402',
        'pid': '04',
        'name': '手机号修改',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '06',
        'pid': '',
        'name': '流程审批',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0603',
        'pid': '06',
        'name': '审批类型管理',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '09',
        'pid': '',
        'name': '活动管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0901',
        'pid': '09',
        'name': '全部活动',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '10',
        'pid': '',
        'name': '活动奖励物品',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '1003',
        'pid': '10',
        'name': '中奖纪录',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }],
    lhMenus = [{
        'id': '02',
        'pid': '',
        'name': '部门管理',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '06',
        'pid': '',
        'name': '流程审批',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0601',
        'pid': '06',
        'name': '待我审批',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '07',
        'pid': '',
        'name': '标签',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0701',
        'pid': '07',
        'name': '标签组管理',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '0702',
        'pid': '06',
        'name': '标签管理',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '10',
        'pid': '',
        'name': '活动奖励物品',
        'isLeaf': '2',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '1001',
        'pid': '10',
        'name': '物品发布',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '1002',
        'pid': '10',
        'name': '物品管理',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }, {
        'id': '1004',
        'pid': '10',
        'name': '奖品发货',
        'isLeaf': '1',
        'b_info': [
            {
                'id': '按钮编号',
                'name': '按钮名称',
                'url': 'url'
            }
        ]
    }];


module.exports = {
    'chuans': {
        userName: 'chuans',
        passWord: '123456',
        menus: menus
    },
    'zoul': {
        userName: 'zoul',
        passWord: '123456',
        menus: zoulMenus
    },
    'jxf': {
        userName: 'jxf',
        passWord: '123456',
        menus: jxfMenus
    },
    'lh': {
        userName: 'lh',
        passWord: '123456',
        menus: menus
    },
    'zj': {
        userName: 'zj',
        passWord: '111111',
        menus: menus
    },
    'lyj': {
        userName: 'lyj',
        passWord: '123456',
        menus: menus
    },

};