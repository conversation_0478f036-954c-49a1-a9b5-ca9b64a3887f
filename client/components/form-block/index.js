import React from 'react';
import PropTypes from 'prop-types';
import './style.less';

const FormBlock = ({ title = '这是title', children }) => {
  return (<div className="form-block-wrapper">
    <div className="form-block-wrapper-header">
      <span className="form-block-wrapper-header-title">{title}</span>
    </div>
    { children }
  </div>)
}

FormBlock.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  children: PropTypes.node
}

export default FormBlock;