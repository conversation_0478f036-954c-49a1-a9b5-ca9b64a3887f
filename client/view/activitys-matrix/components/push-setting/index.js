


import React, { Component } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON>, message, Spin } from "antd";

import PushSettingView from './view';

import PropTypes from "prop-types";


import { getAllOrgParents } from "apis/organisition";

//活动设置
import { activityPushsetup,  activityGetpushsetup } from "apis/activity";


//新闻设置
import { newsPushsetup,  newsGetpushsetup } from "apis/news";

import { map } from "tool/util";

import "./index.less";


const propTypes = {
  form: PropTypes.object,
  currentData: PropTypes.object,
  type: PropTypes.string,
  onSave: PropTypes.func,
  onCancel: PropTypes.func,
  visible: PropTypes.bool,
  // foo : PropTypes.string
};
const defaultProps = {
  // title : "新闻"
  type: "news"// || "activity"
};


const apis = {
  news:{
    pushsetup:newsPushsetup,
    getpushsetup:newsGetpushsetup
  },
  activity:{
    pushsetup:activityPushsetup,
    getpushsetup:activityGetpushsetup
  }
};


export default Object.assign( class PushSetting extends Component {

  constructor(props) {
    super(props);
    this.state = {
      dataList:[],
      selectedRows:[],
      saveing:false,
      visible: false,
      loading: false,
      titleTypes:{news:'新闻', activity:'活动'}
    }
    this.handleSave = this.handleSave.bind(this);
    this.handleCancel = this.handleCancel.bind(this);
    this.handleChange = this.handleChange.bind(this);


  }

  componentWillReceiveProps(nextProps) {
    const { visible } = nextProps;
    if(this.state.visible != visible){
      this.setState({
        visible
      }, () => {
        if(visible){
          this.loadData();
        }
      });
    }
  }

  // componentDidMount(){
  loadData(){
    const { type } = this.props;
    if(type!="news" && type!="activity"){
      throw new Error("type: 'news' || 'activity' ")
    }

    this.setState({
      loading: true
    });
    getAllOrgParents().then(res=>{
      const { status, data:response } = res;
      if(status === 200){
        const { code, message, data } = response;
        if(code === 0 && message == "success"){
          // console.log(data);
          this.setState({
            dataList : data.filter((org)=>org.parent_id!=0 && org.org_name!='root') //去掉root组织
          })
        }
      }
    }).catch(e => {
      this.setState({
        loading: false,
        dataList: []
      })
      message.error(e);
    }).finally(()=>{
      apis[type].getpushsetup().then(res=>{
        const { status, data:response } = res;
        if(status === 200){
          const { code, message, data } = response;
          if(code === 0 && message == "success"){
            //黑名单， 取反
            //   const resultData = Array.from(new Set(data.map((item) => {
            //       return {org_id: (item.org_id || item)};
            //   })));

            // 新闻
            const _array = Array.prototype.concat.call([], this.state.dataList)
            for(var i = 0; i < _array.length ; i++) {
              if(type == 'news') {
                if(data.find(d => d == _array[i].org_id)) {
                  _array.splice(i, 1);
                  i -= 1;
                }
              } else {
                if(data.find(d => d.org_id == _array[i].org_id)) {
                  _array.splice(i, 1)
                  i -= 1;
                }
              }
            }
            this.setState({
              selectedRows : _array,
              // selectedRows: resultData,
              loading: false
            }, console.log(this.state.selectedRows));
          }
        }
      }).catch(e=>{
        message.error(e);
      }).finally(()=>{
        //todo
      })
    })




  }

  async handleSave(){
    const self  = this;
    const { selectedRows } = this.state;
    const { type } = this.props;
    // console.log(selectedRows);
    let postDatas = []
    let _temp = Array.prototype.concat.call([], this.state.dataList);
    for(var i = 0; i < _temp.length ; i++) {
      if(selectedRows.find(d => d.org_id == _temp[i].org_id)){
        _temp.splice(i, 1);
        i -= 1;
      }
    }

    if(type === 'new'){
      //新闻只传org_id数组
      postDatas = _temp.map(s=>s.org_id)
    } else {
      //活动传 {org_id, org_name}
      postDatas = _temp.map(s=>({org_id:s.org_id, org_name:s.org_name}))
    }

    this.setState({
        saveing: true
    });

    console.log(postDatas);
    const result = (await apis[type].pushsetup(postDatas)).data;

    this.setState({
        saveing: false,
        loading: false
    });
    if(result.code != 0){
      return message.error(result.message);
    }
    message.success("保存成功");
    if(this.props.onSave){
      //console.log("handleSave:", this.state.selectedRows)
      this.props.onSave();
    }
  }
  handleCancel(){
    this.setState({
        saveing: false,
        loading: false
    });
    if(this.props.onCancel){
      this.props.onCancel();
    }
  }

  handleChange(selectedRows){
    this.setState({ selectedRows });
  }

  render(){
    // const { title } = this.props;
    const { visible, dataList, loading } = this.state;
    const footer = [];

    if(dataList && dataList.length){
      footer.push(<Button key="save" type="primary" loading={this.state.saveing} onClick={this.handleSave}>保存</Button>);
    }
    footer.push(<Button key="cancel" onClick={this.handleCancel}>取消</Button>);

    return (<Modal
      destroyOnClose
      className="com-push-setting-modal"
      title="接收设置"
      width={800}
      visible={visible}
      onCancel={this.handleCancel}
      footer={footer}
    >
      <PushSettingView
        title={this.state.titleTypes[this.props.type]}
        data={dataList}
        selectedRows={this.state.selectedRows}
        onChange={this.handleChange}
        isLoading={loading}
      />
    </Modal>
    )
  }
},   { propTypes, defaultProps})
