import React from 'react';
import Header from 'components/search-header';
import moment from "moment";
import { Spin, Table, Row, Col, Tabs, Input, Button, Select } from 'antd';
import UploadList from 'components/upload-list';
import { fileDownload } from 'client/components/file-download';
const path = require('path');
const taskTabList = [{
    name: '待审核'
}, {
    name: '已审核'
}, {
    name: '未提交'
}];
const examineStatus = [
    // {key: 4, status: "审核中"},
    {key: 5, status: "不合格"},
    {key: 6, status: "合格"},
    // {key: 7, status: "已撤销"},
    // {key: 8, status: "逾期未提交"},
    // {key: 9, status: "逾期提交"},
    {key: 10, status: "退回重办"}
]
const labelCol = {
    span: 4
}
const wrapperCol = {
    span: 8
}
const { TabPane } = Tabs;
const { Search } = Input;

const renderTab = (data, num) => {
    if(!data) return null;
    return data.map((item, index) => {
        const name = item.name.includes('未提交') ? `${item.name}(${num})` : item.name;
        return (<TabPane tab={name} key={index + 1} />);
    });
};
export default function ({
    headerProps, dataSource, tabsIndex, searchParam = '', notCommitNum, onChangeTab, onChangeSearch, isLoading, isTabsLoading,
    onOpen, onChangeSelect,id,selectVal
}) {

    const eval_entity = dataSource.eval_entity || {};
    const eval_task_entity = dataSource.eval_task_entity || {};
    const eval_file_entities = (dataSource.eval_file_entities && dataSource.eval_file_entities.map((item => {
        return {
            ...item,
            name: item.name,
            file_name: item.file_name,
            id: item.id,
            path: item.file_url,
            size: item.size
        }
    }))) || [];
    const eval_org_entity = dataSource.eval_org_entity || [];
    const eval_task_endtime = moment(eval_task_entity.approval_time).valueOf();
    
    const columns = [{
        title: '考核年份',
        dataIndex: 'eval_year',
        key: 'eval_year',
        align: 'center'
    }, {
        title: '考核时间',
        dataIndex: 'eval_date',
        key: 'eval_date',
        align: 'center',
        render: (text, record) => {
            const dateTypes = {
                1:{
                    1:'第一季度',
                    2:'第二季度',
                    3:'第三季度',
                    4:'第四季度'
                },
                2:{
                    1:'第一月',
                    2:'第二月',
                    3:'第三月',
                    4:'第四月',
                    5:'第五月',
                    6:'第六月',
                    7:'第七月',
                    8:'第八月',
                    9:'第九月',
                    10:'第十月',
                    11:'第十一月',
                    12:'第十二月',
                }
            }
            if ([1, 2].indexOf(record.eval_cycle) != -1) {
                return dateTypes[record.eval_cycle][text];
            } else {
                return `${text}年`
            }
            /* const cycle = record.eval_cycle;
            if(cycle == 1){
                switch(text){
                    case 1:
                        return '第一季度';
                    case 2:
                        return '第二季度';
                    case 3:
                        return '第三季度';
                    case 4:
                        return '第四季度';
                }
            }
            return null; */
        }
    }, {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        align: 'center'
    }, {
        title: '填报截止时间',
        dataIndex: 'end_time',
        key: 'end_time',
        align: 'center'
    }, {
        title: '审核截止时间',
        dataIndex: 'approval_time',
        key: 'approval_time',
        align: 'center'
    }, {
        title: '审核部门',
        dataIndex: 'audit_org_name',
        key: 'audit_org_name',
        align: 'center'
    }];

    const baseBottomColumns = [{
        title: '组织名称',
        dataIndex: 'org_name',
        key: 'org_name',
        align: 'center',
        render: (text, record) => {
            return record.eval_org_entity.org_name;
        }
    }];
    const bottomColumns1 = baseBottomColumns.concat([{
        title: '提交时间',
        dataIndex: 'create_time',
        key: 'create_time',
        align: 'center',
        render: (text, record) => {
            if(text){
                const time = new Date(text);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
            }
            return null;
        }
    }, {
        title: '操作',
        dataIndex: 'oper',
        key: 'oper',
        align: 'center',
        render: (text, record) => {
            const currentTime = (new Date()).getTime();
            const isEnd = currentTime < eval_task_endtime;
            if(isEnd){
                record.isEdit = true;
            }
            return (<a onClick={() => {
                onOpen(record);
            }}>{isEnd ? '审核' : '查看'}</a>)
        }
    }]);

    const bottomColumns2 = baseBottomColumns.concat([{
        title: '审核时间',
        dataIndex: 'create_time',
        key: 'create_time',
        align: 'center',
        render: (text, record) => {
            if(text){
                const time = new Date(text);
                return moment(time).format('YYYY-MM-DD HH:mm:ss');
            }
            return null;
        }
    }, {
        title: '审核状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (text, record) => {
            const status = record.eval_org_entity.status;
            switch(status){
                case 4:
                    return (
                        <span style={{color:'#FF4D4F'}}>审核中</span>
                    );
                case 5:
                    return (
                        <span style={{color:'#FF4D4F'}}>不合格</span>
                    );
                case 6:
                    return (
                        <span style={{color:'#4FC136'}}>合格</span>
                    );
                case 7:
                    return (
                        <span style={{color:'#B5B5B5'}}>已撤销</span>
                    );
                case 8:
                    return (
                        <span style={{color:'#B5B5B5'}}>逾期未提交</span>
                    );
                case 9:
                    return (
                        <span style={{color:'#B5B5B5'}}>逾期提交</span>
                );
                case 10:
                    return (
                        <span style={{color:'#B5B5B5'}}>退回重办</span>
                );
            }
        }
    }, {
        title: '操作',
        dataIndex: 'oper',
        key: 'oper',
        align: 'center',
        render: (text, record) => {
            return (<a onClick={() => {
                onOpen(record);
            }}>查看</a>)
        }
    }]);

    const bottomColumns3 = baseBottomColumns.concat([])
    const bottomColumns = tabsIndex == 1 ? bottomColumns1 : tabsIndex == 2 ? bottomColumns2 : bottomColumns3;
    const dateTypes = {
        1:{
            1:'第一季度',
            2:'第二季度',
            3:'第三季度',
            4:'第四季度'
        },
        2:{
            1:'第一月',
            2:'第二月',
            3:'第三月',
            4:'第四月',
            5:'第五月',
            6:'第六月',
            7:'第七月',
            8:'第八月',
            9:'第九月',
            10:'第十月',
            11:'第十一月',
            12:'第十二月',
        }
    }
    const getEvalData = (data) => {
        if ([1, 2].indexOf(data.eval_cycle) != -1) {
            return dateTypes[data.eval_cycle][data.eval_date];
        }
		}
    return (
        <div className="audit-task-detail-main" >
            <Spin spinning={isLoading}>
            <Header {...headerProps} />
            <div className="audit-task-detail-content">
                <div className="sectionTitle" data-title="任务详情"></div>
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="考核年份">考核年份：</label>
                    </Col>
                    <Col {...wrapperCol}>{eval_task_entity.eval_year || ''}</Col>
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="填报截止时间">填报截止时间：</label>
                    </Col>
                    <Col {...wrapperCol}> { eval_task_entity.end_time } </Col>
                </Row>
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="考核时间">考核时间：</label>
                    </Col>
                    <Col {...wrapperCol}>
                    {
											eval_task_entity.eval_date && getEvalData(eval_task_entity)
                    }
                    </Col>
										<Col {...labelCol} className='audit-task-detail-label'>
                        <label title="审核截止时间">审核截止时间：</label>
                    </Col>
                    <Col {...wrapperCol}> { eval_task_entity.approval_time } </Col>
                </Row>
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="创建时间">创建时间：</label>
                    </Col>
                    <Col {...wrapperCol}>{ eval_task_entity.create_time }</Col>
										<Col {...labelCol} className='audit-task-detail-label'>
                        <label title="审核部门">审核部门：</label>
                    </Col>
                    <Col {...wrapperCol}> { eval_task_entity.audit_org_name } </Col>
                </Row>
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="任务名称">任务名称：</label>
                    </Col>
                    <Col {...wrapperCol}>{eval_entity.title || ''}</Col>
                </Row>
               <Row className="audit-task-detail-row">
                    <Col  {...wrapperCol} className='audit-task-detail-label'>
                        <label title="任务内容">任务内容：</label>
                    </Col>
                    {
                        eval_entity.content && 
                        <Col span="14" className="audit-task-detail_gray">{eval_entity.content}</Col>
                    }
                </Row>
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="任务描述">任务描述：</label>
                    </Col>
                    {
                        eval_entity.summary && 
                        <Col span="14" className="audit-task-detail_gray">{eval_entity.summary}</Col>
                    }
                </Row>
                {/* <Table
                    rowKey={(record) => {
                        return 'eval_task_entity_' + record.audit_org_id
                    }}
                    style={{margin:'20px'}}
                    columns={columns}
                    dataSource={[eval_task_entity]}
                    pagination={false}
                    bordered
                />  */}
                <Row className="audit-task-detail-row">
                    <Col {...labelCol} className='audit-task-detail-label'>
                        <label title="相关资料">相关资料：</label>
                    </Col>
                    <Col span="20">
                        <UploadList
                            canEdit={false}
                            canPreview={true}
                            inputData={eval_file_entities}
                            emptyText="暂无资料"
                        />
                    </Col>
                </Row>
                <div className="audit-task-detail-list">
                    <Tabs onChange={onChangeTab} activeKey={tabsIndex + ""}>
                        {renderTab(taskTabList,notCommitNum)}
                    </Tabs>
                    <div className="tab-search">
                        {
                        tabsIndex == 2 && 
                        <label className="tab-select">审核状态：
                            <Select defaultValue="1" style={{ width: 120 }} onChange={val => onChangeSelect(val)}>
                                <Option value="1">全部</Option>
                                {
                                    examineStatus.map((item)=>{
                                        return <Option value={item.key}>{item.status}</Option>
                                    })
                                }
                            </Select>
                        </label>
                        }
                        
                        <Search
                        className="tab-search_el"
                        placeholder="请输入组织名称"
                        enterButton="搜索"
                        value={searchParam}
                        onChange={onChangeSearch}
                        onSearch={val => onChangeTab(tabsIndex, val)}
                        />
                        <a
                        disabled={eval_org_entity.length ? false : true}
                        onClick={()=>{
                            fileDownload(`/eval/approval/download-collect?eval_id=${id}&type=${tabsIndex}&org_name=${searchParam}&approval_type=${selectVal}`)
                          }}
                          href="javascript:void(0);"
                        >
                            <Button >导出</Button>
                        </a>
                    </div>
                    <Table
                        rowKey={(record) => {
                            return (tabsIndex == 1 ? 'audit-tab1' : 'audit-tab2') + record.eval_org_entity.eval_org_id;
                        }}
                        columns={bottomColumns}
                        dataSource={eval_org_entity}
                        pagination={false}
                        loading={isTabsLoading}
                        bordered
                    />
                </div>
            </div>
            </Spin>
        </div>
    )

};