import React from "react";
import PropTypes from "prop-types";
import { connect } from 'dva';
import { message } from 'antd';
import IndexView from './index-view';
import "./style.less";

import {
    taskList
} from "apis/appraisals";

class AuditTask extends React.Component {
    constructor() {
        super();
        this.state = {
            isLoading: false,
            orgId: null,
            auditData: {
                pageNum: 1,
                pageTotal: 0,
                pageSize: 10,
                dataSource: null
            },
            formData: {
                year: (new Date()).getFullYear(),
                date: -1
            }
        }
    }

    resetAuditData(){
        this.setState({
            auditData: {
                pageNum: 1,
                pageTotal: 0,
                pageSize: 10,
                dataSource: null
            }
        });
    }
    async taskList(payload = {}){
        const { year, date, page } = payload;
        const { formData, auditData, orgId } = this.state;

        this.setState({
            isLoading: true
        });

        this.resetAuditData();

        const params = {
            audit_org_id: orgId,
            eval_year: year || formData.year,
            eval_date: date || formData.date,
            page_index: page || 1
        };

        const result = (await taskList(params)).data;

        this.setState({
            isLoading: false
        });
        if(result.code !== 0){
            return message.error(result.message);
        }

        if(result.data && result.data.length){
            this.setState({
                auditData: Object.assign(auditData, {
                    dataSource: result.data,
                    pageNum: result.pageNum,
                    pageTotal: result.total
                }),
                formData: Object.assign(formData, {
                    year: year || formData.year,
                    date: date || formData.date
                })
            });
        }
    }

    componentDidMount() {
        const { userInfo } = this.props;
        this.setState({
            orgId: userInfo.oid || window.sessionStorage.getItem('_oid')
        }, async () => {
            await this.taskList();
        });
    }

    render() {
        const { auditData, isLoading } = this.state;
        const { history } = this.props;

        const appProps = {
            isLoading,
            dataSource: auditData.dataSource,
            dataPages: {
                pageNum: auditData.pageNum,
                pageTotal: auditData.pageTotal,
                pageSize: auditData.pageSize
            },
            headerProps: {
                title: "任务审核"
            },
            onOpen: (record) => {
                history.push({
                    pathname: '/audit-task-detail',
                    query: {
                      id: record.eval_entity.eval_id
                    }
                })
            },
            onInitSearch: (form) => {
                this._searchForm = form;
            },
            onSubmitSearch: (form) => {
                form.validateFieldsAndScroll((err, values) => {
                    const params = {
                        year: values.taskYear,
                        date: values.quarter,
                        page: 1
                    };

                    this.taskList(params);
                });
            },
            onPageChange: (pageNum) => {
                this.taskList({ page: pageNum });
            }
        };

        return (
            <div className="audit-task">
                <IndexView {...appProps} />
            </div>
        );
    }
}

// ExecuteTask.propTypes = {
//   desc: PropTypes.string
// };
// ExecuteTask.defaultProps = {
//   desc: "接口功能开发升级中，敬请期待…"
// }
const mapStateToProps = ({ userInfo }) => ({ userInfo });
export default connect(mapStateToProps)(AuditTask);
