import React, { PureComponent } from "react";
import { Button, Tag } from "antd";
import PersonModal from "client/components/person-selector/sub-components/person-modal";
import { findUserByList } from "client/apis/organize";

/**
 * 人员选择
 */
export default class UserSelector extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      loading: false,
      myValue: [],
      cacheVal: "", // 缓存外部传入的value，避免重复请求人员数据
    };
    this.tag = Math.random(); // 用于判断异步问题
    this.orgId = Number(sessionStorage.getItem("_oid"));
    this.hideModal = this.hideModal.bind(this);
    this.onChange = this.onChange.bind(this);
    this.onClick = this.onClick.bind(this);
  }
  componentDidMount() {
    const { value } = this.props;
    let ids = typeof value === "string" && value !== "" ? value.split(",") : [];
    this.initDefaultValue(ids);
  }
  componentWillReceiveProps(nextProps) {
    if (
      nextProps.value !== this.props.value &&
      nextProps.value !== this.state.cacheVal
    ) {
      this.setState({
        cacheVal: nextProps.value,
      });
      let ids =
        typeof nextProps.value === "string" && nextProps.value !== ""
          ? nextProps.value.split(",")
          : [];
      this.initDefaultValue(ids);
    }
    if (nextProps.multiple !== this.props.multiple) {
      this.personModalRef.init([]);
    }
  }
  initDefaultValue(ids) {
    const tags = Math.random();
    this.tags = tags;
    if (!(ids && ids.length > 0)) {
      this.setState({
        myValue: [],
      });
      if (this.personModalRef) {
        this.personModalRef.init([]);
      }
      return;
    }
    this.setState({
      loading: true,
      myValue: [],
    });
    findUserByList({ id_list: ids.map((id) => Number(id)) })
      .then((res) => {
        // 多个异步请求，判断最新的this.tags 和当前的不一样，则不执行后面更新
        if (this.tags !== tags) {
          return;
        }
        const { data } = res.data;
        const dataList = [];
        for (const key in data) {
          if (Object.hasOwnProperty.call(data, key)) {
            const item = data[key];
            if (item.user_id) {
              dataList.push({...item, name: item.name || item.user_name});
            }
          }
        }
        this.setState({
          myValue: dataList,
        });
        if (this.personModalRef) {
          this.personModalRef.init([]);
        }
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  }
  onClick() {
    this.setState({ visible: true }, () => {
      const { myValue } = this.state;
      if (this.personModalRef) {
        // 首次打开选择器无法回显
        setTimeout(() => {
          this.personModalRef.init(myValue);
        });
        /*  this.personModalRef.setState({
          selectedRows: [...myValue],
          selectedRowKeys: myValue.map(
            (item) => `${item.user_id}-${item.org_id}`
          ),
        }); */
      }
    });
  }
  hideModal() {
    this.setState({ visible: false });
  }
  onChange(val) {
    const { onChange } = this.props;
    const cacheVal = val.map(({ user_id }) => user_id).join(",");
    this.setState({ myValue: val, cacheVal }, () => {
      onChange && onChange(cacheVal);
    });
  }
  onTagClose(index) {
    const { myValue } = this.state;
    myValue.splice(index, 1);
    /* if (this.personModalRef) {
      this.personModalRef.setState({
        selectedRows: [...myValue],
        selectedRowKeys: myValue.map(
          (item) => `${item.user_id}-${item.org_id}`
        ),
      });
    } */
    this.onChange([...myValue]);
  }
  render() {
    const { rootOrgId, label, multiple = true, disabled } = this.props;
    const { visible, myValue, loading } = this.state;
    return (
      <div>
        <Button
          type="link"
          onClick={this.onClick}
          disabled={disabled}
          loading={loading}
        >
          选择
        </Button>
        {myValue.map((item, index) => (
          <Tag
            key={item.user_id}
            closable={!disabled}
            onClose={this.onTagClose.bind(this, index)}
          >
            {item.name}
          </Tag>
        ))}
        <PersonModal
          visible={visible}
          hideModal={this.hideModal}
          title={`选择${label}`}
          isPartyGroup={false}
          isLeaderGroup={false}
          selectAll={multiple}
          radio={!multiple}
          onChange={this.onChange}
          orgId={this.orgId}
          ref={(ref) => (this.personModalRef = ref)}
        />
      </div>
    );
  }
}
