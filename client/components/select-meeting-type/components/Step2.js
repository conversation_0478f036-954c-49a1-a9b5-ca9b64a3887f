import React from "react";
import HeaderForm from "./HeaderForm";
import ButtonGroup from "./ButtonGroup";
import { Table } from "antd";
import "../index.less";

const Step2 = (props) => {
  const { state } = props;
  const data = [
    {
      name: "上一步",
      onClick: props.prevStep,
      style: {
        width: "115px",
        height: "36px",
      }
    },
    {
      name: "下一步",
      onClick: props.nextStep,
      style: {
        width: "115px",
        height: "36px",
        backgroundColor: "#F46E65",
        marginLeft: "48px",
        border: "none"
      }
    }
  ];
  const buttonGroup = {
    data
  };
  const columns = [
    {
      title: "序号",
      dataIndex: "none",
      align: "center",
      render: (val, record, index) => index + 1 
    },
    {
      title: "报表名称",
      dataIndex: "name",
      align: "center"
    },
    {
      title: "操作",
      dataIndex: "category",
      align: "center",
      render: (val, record, index) => {
        return <a onClick={() => console.log(record)}>查看详情</a>
      }
    }
  ]
  const inputGroup = [
    {
      label: "报表名称",
      getField: "name",
      type: "Input",
    }
  ];

  const headerFormProps = {
    form: props.form,
    data: inputGroup,
    search: (item) => console.log(item)
  }
  const rowSelection = {
    type: "checkbox",
    columnTitle: "选择",
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    onSelect: (record, selected, selectedRows) => {
      console.log(record, selected, selectedRows);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log(selected, selectedRows, changeRows);
    }
  };
  return (
    <div className="step1">
      <HeaderForm {...headerFormProps}/>
      <Table
        bordered
        style={{ marginTop: "20px" }}
        columns={columns}
        dataSource={state.step2.dataSource}
        rowSelection={rowSelection}
        rowKey={(record) => record.name}
      />
      <ButtonGroup {...buttonGroup}/>
    </div>
  )
}

export default Step2;