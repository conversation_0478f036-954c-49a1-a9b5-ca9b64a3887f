import React, {Component} from 'react';
import PropTypes from 'prop-types';
import './index.less';

const TextRemain = ({
                        maxSize,
                        value
                    }) => {
    let remaminNumber = maxSize - value.length;
    if (remaminNumber >= 0) {
        return (
            <div className={'remain-text'}>
                还可以输入<span className={'in-range'}>{remaminNumber}</span>个字符
            </div>
        )
    } else {
        return (
            <div className={'remain-text'}>
                <span className={'out-range'}>输入长度超出限定值{Math.abs(remaminNumber)}个字符</span>
            </div>
        )
    }


};

TextRemain.propTypes = {
    //最大可以输入字符数
    maxSize: PropTypes.number,
    //当前输入的内容
    value: PropTypes.string
};

export default TextRemain;