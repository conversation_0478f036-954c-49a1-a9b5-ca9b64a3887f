import React, { PureComponent } from 'react'
import './index.less';
import { uuid } from 'client/tool/uuid';
import { CDN } from 'apis/config';
import FileUploadItem from './FileUploadItem';
import FileUploadSelect from './FileUploadSelect';
import PreviewModal from './PreviewModal';

class FileUploadList extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      previewData: null,
      visible: false,
    };
    this.onFileChange = this.onFileChange.bind(this)
    this.onItemChange = this.onItemChange.bind(this)
    this.onItemDel = this.onItemDel.bind(this)
    this.onPreview = this.onPreview.bind(this)

  }
  // 上传文件改变
  onFileChange(e) {
    const { onChange, value = [], maxSize } = this.props;
    const { files } = e.target;
    for (let i = 0; i < files.length; i++) {
      const fileItem = files[i];
      if (maxSize && fileItem.size > maxSize * 1024 * 1024) {
        message.error(`单个文件不能大于${maxSize}M`);
        return;
      }
      if (value) {
        value.push({
          identify: uuid(),
          status: 'done',
          name: fileItem.name,
          size: fileItem.size,
          fileData: fileItem,
        });
      }
    }
    if (onChange && value) onChange([...value]);
  }
  // 子集item上传完成
  onItemChange(item, index) {
    const { onChange, value = [] } = this.props;
    value[index] = item;
    if (onChange && value) onChange([...value]);
  };
  // 子集item删除
  onItemDel(index) {
    const { onChange, value = [] } = this.props;
    value.splice(index, 1);
    if (onChange && value) onChange([...value]);
  };

  onPreview(data, index, dataURL) {
    this.setState({
      previewData: {
        url: dataURL || `${CDN}/${data.path}`,
        name: data.name,
      },
      visible: true
    })
  };

  render() {
    const { maxSize, onChange, showTime, ...otherProps } = this.props;
    const { previewData, visible } = this.state;
    const value = Array.isArray(this.props.value) ? this.props.value : [];
    return (
      <div className='file-upload-component'>
        <FileUploadSelect onInputChage={this.onFileChange} currentLength={value.length} {...otherProps} maxSize={maxSize}>
          {value &&
            value.map((item, index) => (
              <FileUploadItem
                {...item}
                listType={otherProps.listType}
                key={item.identify || `${item.path}+${index}`}
                index={index}
                disabled={otherProps.disabled}
                onChange={this.onItemChange}
                onDelete={this.onItemDel}
                onError={this.onItemDel}
                onPreview={this.onPreview}
                showTime={showTime}
              />
            ))}
        </FileUploadSelect>
        <PreviewModal
          src={previewData ? previewData.url : ''}
          name={previewData ? previewData.name : ''}
          visible={visible}
          onCancel={() => this.setState({ visible: false })}
        />
      </div>
    );
  }
}

FileUploadList.defaultProps = {
  listType: 'text',
  text: '上传文件',
  tip: '',
  onChange: () => { },
  disabled: false,
  showTip: true,
  limit: 2, // 最大上传数量
};

export default FileUploadList;
