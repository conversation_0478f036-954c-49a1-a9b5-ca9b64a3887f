import { Form } from 'antd';
import './../formLess/pocForm.less';

const pocFormItem = (props) => {
  const { label, formItem = true, children, ...restProps } = props;

  return (
    <div className="poc-form-item">
      {label && (
        <div className="title">
          {label}
        </div>
      )}
      {formItem ? (
        <Form.Item {...restProps}>
          {children}
        </Form.Item>
      ) : (
        children
      )}
    </div>
  );
};

export default pocFormItem;