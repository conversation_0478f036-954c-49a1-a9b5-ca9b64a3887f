// enums.js
const Gender = {
  Male: 1, // 男性
  Female: 2 // 女性
};

const Nation = {
  Han: 1, // 汉族
  Mongolian: 2, // 蒙古族
  Other: 3 // 其他
};

const Political = {
  CPCMember: 1, // 中共党员
  NonMember: 2 // 非党员
};

const Training = {
  Participated: 1, // 参加过培训
  Outstanding: 2 // 优秀学员
};

const FullTimeEducation = {
  Graduate: 1, // 研究生
  Bachelor: 2, // 大学本科
  College: 3 // 大学专科
};

const Origin = {
  Selective: '1', // 选调生
  VillageOfficial: '2', // 村官
  FiveAspects: '3' // 五方面人才
};

const Society = {
  RelationInSystem: 1, // 有社会关系在体制内
  BothInSystem: 2 // 夫妻双方都在体制内
};

const Attention = {
  KeyFocus: 1 // 重点关注
};

export {
  Gender,
  Nation,
  Political,
  Training,
  FullTimeEducation,
  Origin,
  Society,
  Attention
};