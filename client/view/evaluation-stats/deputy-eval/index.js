import React from 'react';
import {Table} from 'antd';
import EvalTopForm from "../components/eval-top-form";
import './index.less';

const TeamEval = () => {
    const columns = [
        {
            title: '所在单位',
            dataIndex: 'unitName',
            fixed: 'left',
            width: 120,
        },
        {
            title: '干部姓名',
            dataIndex: 'teamName',
            fixed: 'left',
            width: 120,
        },
        {
            title: '分管领导测评',
            children: [
                { title: '好', dataIndex: 'leaderGood' },
                { title: '较好', dataIndex: 'leaderBetter' },
                { title: '一般', dataIndex: 'leaderNormal' },
                { title: '差', dataIndex: 'leaderBad' },
                { title: '不了解', dataIndex: 'leaderUnknown' },
                { title: '总票数', dataIndex: 'leaderTotal' },
                { title: '得分', dataIndex: 'leaderScore' }
            ],
        },
        {
            title: '单位正职',
            children: [
                {
                    title: '学习能力',
                    children: [
                        { title: '好', dataIndex: 'leaderZjStudyGood' },
                        { title: '较好', dataIndex: 'leaderZjStudyBetter' },
                        { title: '一般', dataIndex: 'leaderZjStudyNormal' },
                        { title: '差', dataIndex: 'leaderZjStudyBad' },
                        { title: '不了解', dataIndex: 'leaderZjStudyUnknown' },
                        { title: '得分', dataIndex: 'leaderZjStudyScore' }
                    ]
                },
                {
                    title: '决策能力',
                    children: [
                        { title: '好', dataIndex: 'leaderZjDecisionGood' },
                        { title: '较好', dataIndex: 'leaderZjDecisionBetter' },
                        { title: '一般', dataIndex: 'leaderZjDecisionNormal' },
                        { title: '差', dataIndex: 'leaderZjDecisionBad' },
                        { title: '不了解', dataIndex: 'leaderZjDecisionUnknown' },
                        { title: '得分', dataIndex: 'leaderZjDecisionScore' }
                    ]
                },
                { title: '票数', dataIndex: 'leaderZjTotal' },
                { title: '得分', dataIndex: 'leaderZjOverallScore' }
            ]
        },
        {
            title: '其他领导干部',
            children: [
                {
                    title: '学习能力',
                    children: [
                        { title: '好', dataIndex: 'leaderOtherStudyGood' },
                        { title: '较好', dataIndex: 'leaderOtherStudyBetter' },
                        { title: '一般', dataIndex: 'leaderOtherStudyNormal' },
                        { title: '差', dataIndex: 'leaderOtherStudyBad' },
                        { title: '不了解', dataIndex: 'leaderOtherStudyUnknown' },
                        { title: '得分', dataIndex: 'leaderOtherStudyScore' }
                    ]
                },
                {
                    title: '决策能力',
                    children: [
                        { title: '好', dataIndex: 'leaderOtherDecisionGood' },
                        { title: '较好', dataIndex: 'leaderOtherDecisionBetter' },
                        { title: '一般', dataIndex: 'leaderOtherDecisionNormal' },
                        { title: '差', dataIndex: 'leaderOtherDecisionBad' },
                        { title: '不了解', dataIndex: 'leaderOtherDecisionUnknown' },
                        { title: '得分', dataIndex: 'leaderOtherDecisionScore' }
                    ]
                },
                { title: '票数', dataIndex: 'leaderOtherTotal' },
                { title: '得分', dataIndex: 'leaderOtherOverallScore' }
            ]
        },
        {
            title: '一般干部',  // 新增列
            children: [
                {
                    title: '学习能力',
                    children: [
                        { title: '好', dataIndex: 'leaderGeneralStudyGood' },  // 修改前缀
                        { title: '较好', dataIndex: 'leaderGeneralStudyBetter' },
                        { title: '一般', dataIndex: 'leaderGeneralStudyNormal' },
                        { title: '差', dataIndex: 'leaderGeneralStudyBad' },
                        { title: '不了解', dataIndex: 'leaderGeneralStudyUnknown' },
                        { title: '得分', dataIndex: 'leaderGeneralStudyScore' }
                    ]
                },
                {
                    title: '决策能力',
                    children: [
                        { title: '好', dataIndex: 'leaderGeneralDecisionGood' },  // 修改前缀
                        { title: '较好', dataIndex: 'leaderGeneralDecisionBetter' },
                        { title: '一般', dataIndex: 'leaderGeneralDecisionNormal' },
                        { title: '差', dataIndex: 'leaderGeneralDecisionBad' },
                        { title: '不了解', dataIndex: 'leaderGeneralDecisionUnknown' },
                        { title: '得分', dataIndex: 'leaderGeneralDecisionScore' }
                    ]
                },
                { title: '票数', dataIndex: 'leaderGeneralTotal' },  // 修改
                { title: '得分', dataIndex: 'leaderGeneralOverallScore' }  // 修改
            ]
        },
        {
            title: '测评得分',
            dataIndex: 'assessmentScore',  // 修复重复的 unitName
            fixed: 'right',
            width: 50,
        }
    ]
    const data = [
        {
            teamName: 'XX局',
            leaderGood: 10,
            leaderBetter: 5,
            // ...其他字段
            unityGood: 8,
            // ...团结度字段
            integrityGood: 7,
            unityTotal2: 20,
            unityScore2: 85,
        },
        {
            teamName: 'XX局2',
            leaderGood: 10,
            leaderBetter: 5,
            // ...其他字段
            unityGood: 18,
            // ...团结度字段
            integrityGood: 7,
            // ...廉洁度字段
        }
    ];
    const evalTopFormProps = {
        queryFields: [
            {
                label: '测评时间',
                field: 'time_range',
                type: 'RangePicker',
            },
            {
                label: '班子',
                field: 'team',
                type: 'Input',
            },
            {
                label: '干部',
                field: 'party_cadre',
                type: 'Input',
            }
        ],
        queryHandler: (values) => {
            console.log(values)
        },
        handleExport: () => {
        }
    }
    return (
        <div className="TeamEval-container">
            <EvalTopForm {...evalTopFormProps}></EvalTopForm>
            <Table
                columns={columns}
                dataSource={data}
                bordered
                size="middle"
                pagination={false}
                scroll={{ x: 3200 }}
                components={{
                    header: {
                        cell: ({children, ...props}) => (
                            <th {...props} style={{backgroundColor: '#fafafa', fontWeight: 600}}>
                                {children}
                            </th>
                        )
                    }
                }}
            />
        </div>
    )
}

export default TeamEval;