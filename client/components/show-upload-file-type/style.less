.showUploadFileType {
  .iconWrap {
    &.mp1 .fileInfo .fileName{
      height: 40px;
    }
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: 60px; // &:not(:first-child) {
    //   margin-top: 10px;
    // }
    .iconInfo {
      // width: 32px;
      // height: 40px;
      font-size: 40px;
      .icon {
        width: 100%;
        height: 100%;
      }
    }
    .fileInfo {
      position: relative;
      margin: 0;
      padding-left: 10px;
      min-width: 400px;
      height: 40px;
      .fileName {
        position: absolute;
        margin-bottom: 0;
        top: 0;
        line-height: 25px;
        width: 100%;
        height: 25px;
        font-size: 14px;
        color: #333333;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        .fileNameWrapper {
          // flex-grow: 1;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
      .fileSize {
        position: absolute;
        margin-bottom: 0;
        line-height: 15px;
        top: 25px;
        width: 100%;
        height: 15px;
        font-size: 12px;
        color: #999999;
      }
      .deleteIcon {
        white-space: nowrap;
        margin-left: 20px;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
}