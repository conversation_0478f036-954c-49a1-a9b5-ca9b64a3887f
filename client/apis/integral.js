const { http } = require('client/tool/axios');
import { integrating, organization, access_key } from './config';
import axios from 'axios';
//积分列表，（条件查询）
export const queryIntegral = ({ oid = null, name = null, phone = null, id_card = null, page_size = 10, page_no = 1}) => {
  console.log(oid, name, phone, id_card, page_size, page_no, 'here inside')
  return http.get(`${integrating}/list`,{ oid, name, phone, id_card, page_size, page_no },{headers: {...access_key}})
}

//个人积分详细明细（对内）(scores_user_id || id_card  phone)二选一必填项
export const personalIntegralI = ({ score_user_id,id_card, phone, start_time, end_time, type, page_size, page_no }) => {
  return http.get(`${integrating}/detail/list`,{ score_user_id, id_card, phone, start_time, end_time, type, page_size, page_no }, {headers:{...access_key}})
}

//组织树查询
export const queryOrganization = ({ org_id, show_code}) => {
  return http.get(`${organization}/tree`,{ org_id, show_code })
}

