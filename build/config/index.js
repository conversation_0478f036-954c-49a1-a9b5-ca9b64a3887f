/*
 * @Description:
 * @Author: b<PERSON><PERSON>
 * @Date: 2021-02-01 11:35:31
 * @LastEditTime: 2024-11-26 16:51:04
 * @LastEditors: lhl
 */
const RegionConfig = require("./region.config"); // 各系统接口配置
const CustomizationConfig = require("./customization.config"); // 定制化页面或功能配置
// iconfont 自有字体库
const selfIconPath = "/plugin/js/font_618854_sh5jcbfe01.js";
// 融合商城access_key（党建平台）
const accessKey = "9X7CLlez02SKTPh5wV7Px9irL60Vmsx8";

const defaultConfig = {
  selfIconPath,
  accessKey,
  isEncrypted: false, // 是否启用接口参数加密开关
  ...CustomizationConfig.default,
  ...CustomizationConfig[process.env.CUSTOM_ENV], // 覆盖CustomizationConfig默认配置
};

let envConfig = {};
switch (process.env.ONLINE_ENV) {
  case "test":
    envConfig = {
      cdn: "https://gbxt-pc.aidangqun.com", //默认预发布，开发，测试的oss 域名地址如下
      // getway: process.env.CUSTOM_ENV === "-1" ? 'http://**************:8092' : 'http://124.70.106.125:8092', // 市值4.0版本
      // getway: "https://test.aidangqun.com/fd/owsz", // 市值4.0版本
      // getway: "https://gbxt-pc.aidangqun.com:30443/owsz", // 干部画像
      getway: "https://pms-panzhihua.aidangqun.com/owsz", // 干部画像
      // getway: "http://**************:40100/owsz", // 干部画像 (公司内网地址)
      // getway: "http://gbadmin.cqfd.gov.cn/owsz", // 干部画像
      // getway: "http://**************:18108/owsz", // fengdu
      mobileUrl: "http://**************:7005", // 丰都移动端
      windowUrl: "http://**************:8005", // 支部之窗
      msgCenter: "http://owsmsg.yeyeku.com",
      wechatUrl: "http://owswx.yeyeku.com",
      // screenUrl: "http://localhost:5173"
      screenUrl: "http://**************:8015",
      // cadreMoblie: "https://gbxt-pc.aidangqun.com",
      cadreMoblie: "https://pms-baseline.aidangqun.com",
      /* getway: "https://test-gateway.aidangqun.com/owsz", // 市值4.0版本
      mobileUrl: "https://test-wx.aidangqun.com",
      msgCenter: "https://test-msg.aidangqun.com",
      wechatUrl: "https://test-wx.aidangqun.com", */
    };
    break;
  case "release":
    envConfig = {
      cdn: "https://ows-test.obs.cn-north-4.myhuaweicloud.com", //默认预发布，开发，测试的oss 域名地址如下
      mobileUrl: "http://wx3.yeyeku.com",
      msgCenter: "http://owsmsg.yeyeku.com",
    };
    break;
  case "master":
    envConfig = RegionConfig[process.env.CUSTOM_ENV];
    break;
  default:
    throw "没有匹配的ONLINE_ENV环境";
}
envConfig.online_env = process.env.ONLINE_ENV;
envConfig.region_id = process.env.CUSTOM_ENV;
module.exports = { ...defaultConfig, env: envConfig };
