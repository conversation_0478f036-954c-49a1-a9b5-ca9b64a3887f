// 考勤状态组件
import React, { Component } from "react";
import { Modal, Form, Input, Select, Button } from "antd";
import "./attendance-status.less";

import propTypes from "prop-types";

const FormItem = Form.Item;
const Option = Select.Option;
const TextArea = Input.TextArea;

class AttendanceStatus extends Component {
    constructor(props) {
        super(props);
    }
    render() {
        const { form, visible, hideModal, currentIndex, currentData, refreshList } = this.props;
        // console.log(currentData, currentIndex);
        // console.log(form);
        // 1：已签到（默认） 2：未签到 3：因公请假 4：因私请假 5: 缺席。
        const { sign_status=1, reason } = currentData;
      
        const { getFieldDecorator, resetFields, getFieldValue, validateFields } = form;
        const modalProps = {
            footer: null,
            title: "考勤管理",
            visible,
            wrapClassName: "attendance-status",
            width: 600,
            onCancel: () => {
                hideModal();
            }
        };
        const formLayout = {
            labelCol: {
                span: 8
            },
            wrapperCol: {
                span: 15
            }
        };
        const formProps = {
            onSubmit: (e) => {
                e.preventDefault();
                validateFields((error, values) => {
                    if (!error) {
                        // console.log("更改考勤状态", currentData, currentIndex, values);
                        const { sign_status, reason } = values;
                        // 此处应该要调用变更状态接口，接口调用成功之后，调用外层传入方法，将外层表格状态更新
                        currentData.sign_status = sign_status;
                        currentData.reason = reason;
                        refreshList(currentData, currentIndex);
                        hideModal();
                        resetFields();
                    }
                })
            }
        }
        return (
            <Modal {...modalProps}>
                <Form {...formProps}>
                    <FormItem label="设置考勤状态" {...formLayout}>
                        {
                            getFieldDecorator("sign_status", {
                                initialValue: sign_status
                            })(
                                <Select>
                                    <Option value={1}>已签到</Option>
                                    <Option value={2}>未签到</Option>
                                    <Option value={3}>因公请假</Option>
                                    <Option value={4}>因私请假</Option>
                                    {/* <Option value={5}>缺席</Option> */}
                                </Select>
                            )
                        }
                    </FormItem>
                    {
                        (getFieldValue("sign_status") !== 1 &&
                            getFieldValue("sign_status") !== 2) &&
                        <FormItem label="请假原因" {...formLayout}>
                            {
                                getFieldDecorator("reason", {
                                    initialValue: reason || ""
                                })(
                                    <TextArea rows={4} placeholder="请输入请假原因" />
                                )
                            }
                        </FormItem>
                    }
                    <div className="buttons-wrapper">
                        <Button type="primary" htmlType="submit">确定</Button>
                        <Button onClick={() => hideModal()}>取消</Button>
                    </div>
                </Form>
            </Modal>
        )
    }
}

AttendanceStatus.propTypes = {
    visible: propTypes.bool,
    hideModal: propTypes.func,
    currentIndex: propTypes.number,
    currentData: propTypes.object,
    refreshList: propTypes.func
}

AttendanceStatus.defaultProps = {
    visible: true,
    hideModal: () => { },
    currentIndex: -1,
    currentData: {},
    refreshList: () => { }
}

export default Form.create()(AttendanceStatus);