import React, { useState } from "react";
import { Modal, Carousel, Descriptions } from "antd";
import moment from "moment";
import { CDN } from 'client/apis/config';
import phone from './phone.png';
import phoneBlue from './phoneBlue.png';
import position from './position.png';
import sofa from './sofa.png';
import time from './time.png';

const tabName = [
  { label: "PC端", key: 1 },
  { label: "移动端", key: 2 },
];
const previewModal = (props) => {
  const {
    visible,
    onCancel,
    previewModalData: {
      phone_number,
      phone_number_other,
      open_time,
      contact_details,
      venue_nums,
      name,
      sources,
      tags,
      content,
      picture,
      provider_org_name,
      create_time
    },
  } = props;

  const [tabKey, setTabKey] = useState(1);

  let openTime = open_time

  if (open_time !== undefined && open_time !== false && open_time !== null) {

    // let txtChange = new RegExp(open_time, "g")
    // console.log("途中", txtChange);
    openTime = open_time.replace(/\n/g, '<br>');
    console.log("最后结果", openTime);
  }

  return (
    <Modal
      title="预览"
      className="document-preview-modal"
      width={1513}
      maskClosable={false}
      visible={visible}
      footer={false}
      onCancel={onCancel}
    >
      <div className="preview-modal-tab">
        {tabName.map((item) => {
          return (
            <div
              key={item.key}
              className={`preview-modal-tab-item ${tabKey === item.key ? "active" : "inactive"
                }`}
              onClick={() => {
                if (tabKey !== item.key) {
                  setTabKey(item.key);
                }
              }}
            >
              {item.label}
            </div>
          );
        })}
      </div>
      <div className="preview-modal-content">
        {tabKey === 1 ? (
          <div className="preview-modal-content-pc">

            <Carousel autoplay className="preview-modal-content-pc-Carousel">
              {picture && picture.map((item, index) => {
                return (
                  <img src={`${CDN}/${item.path}`}></img>
                )
              })
              }
            </Carousel>
            <Descriptions title="" column={1} className="preview-modal-content-pc-titleBox">
              <div className="preview-modal-content-pc-titleBox-title">{name}</div>
              {tags && tags.split(",").length !== 0 && (
                <span>
                  {tags.split(",").map((item, index) => {
                    return <span className="preview-modal-content-pc-titleBox-tags">{item}</span>;
                  })}
                </span>
              )}
              <Descriptions.Item label="开放时间" className="preview-modal-content-pc-titleBox-titleText">
                {openTime ? <div dangerouslySetInnerHTML={{
                  __html: openTime,
                }} /> : "-"}
              </Descriptions.Item>
              <Descriptions.Item label="预约电话" className="preview-modal-content-pc-titleBox-titleText">
                {phone_number !== null && phone_number_other !== null
                  ? `${phone_number}、${phone_number_other}`
                  : phone_number
                    ? phone_number
                    : phone_number_other || phone_number == null
                      ? phone_number_other
                      : "-"}
              </Descriptions.Item>

              {/* <Descriptions.Item label="场地地址" className="preview-modal-content-pc-titleBox-titleText">{contact_details || "无"}</Descriptions.Item> */}
              <div className="lastTap"><div>场地地址：</div><div style={{ width: "75%" }}>{contact_details || "-"}</div></div>
              <Descriptions.Item label="场地容纳人数" className={!venue_nums ? "displaynone" : 'preview-modal-content-pc-titleBox-titleText'}>{venue_nums}人</Descriptions.Item>

              <Descriptions.Item label="上传时间" className="preview-modal-content-pc-titleBox-titleText">{create_time}</Descriptions.Item>
            </Descriptions>
            <div className="preview-modal-content-pc-text">
              {content}
            </div>
            <div className="lastMsg">信息提供:{provider_org_name}</div>
          </div>
        ) : (
          <div className="preview-modal-content-mobile">
            <div className="preview-modal-content-phone">
              <Carousel autoplay className="preview-modal-content-phone-Carousel">
                {picture && picture.map((item, index) => {
                  return (
                    <img src={`${CDN}/${item.path}`}></img>
                  )
                })
                }
              </Carousel>
              <div className="preview-modal-content-phone-info">
                <div className="preview-modal-content-phone-info-v1">
                  {name}
                </div>
                {/* <div className="preview-modal-content-phone-info-v2">
                  来源：{sources || "无"}
                </div> */}
                {tags && tags.split(",").length !== 0 && (
                  <div className="preview-modal-content-phone-info-v3">
                    {tags.split(",").map((item, index) => {
                      return <span key={index}>{item}</span>;
                    })}
                  </div>
                )}
              </div>
              <Descriptions title="" column={1} className="preview-modal-content-phone-titleBox">
                <Descriptions.Item label="" className="preview-modal-content-phone-titleBox-titleText">
                  <div className="phone-icon" style={{ background: `url('${time}') no-repeat center/cover` }} />
                  <div style={{ width: "90%" }}>{openTime ? <div dangerouslySetInnerHTML={{
                    __html: openTime,
                  }} /> : "-"}</div>
                </Descriptions.Item>

                <Descriptions.Item label="" className="preview-modal-content-phone-titleBox-titleText">
                  <div className="phone-icon" style={{ background: `url('${phone}') no-repeat center/cover` }} />
                  {phone_number !== null && phone_number_other !== null
                  ? `${phone_number}、${phone_number_other}`
                  : phone_number
                    ? phone_number
                    : phone_number_other || phone_number == null
                      ? phone_number_other
                      : "-"}
                  {phone_number && phone_number_other ? <div className="phone-icon-right" style={{ background: `url('${phoneBlue}') no-repeat center/cover` }} /> : ''}
                </Descriptions.Item>

                <Descriptions.Item label="" className={!venue_nums ? "displaynone" : 'preview-modal-content-phone-titleBox-titleText'}>
                  <div className="phone-icon" style={{ background: `url('${sofa}') no-repeat center/cover` }} />
                  {venue_nums == 0 ? "-" : `${venue_nums}人`}
                </Descriptions.Item>

                <Descriptions.Item label="" className="preview-modal-content-phone-titleBox-titleText position">
                  <div className="phone-icon" style={{ background: `url('${position}') no-repeat center/cover` }} />
                  <div className="position-text">{contact_details || "-"}</div>
                </Descriptions.Item>
                <div className="preview-modal-content-phone-titleBox-text">
                  {content}
                </div>
                <div className="lastTap"><div>信息提供：</div><div style={{ width: "74%" }}>{provider_org_name}</div></div>
              </Descriptions>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default previewModal;
