import React, { Component } from "react";
import IndexView from "./index-view";
import { connect } from "dva";
import { Form, message, Tabs } from "antd/lib/index";
import SearchHeader from "components/search-header";
import PushTo from "client/view/activitys-matrix/components/push-to";

import { CDN } from "apis/config";
import { newsMatrixResult } from "apis/news";
import { map } from "tool/util";
// import OverviewModal from 'components/overview-modal';
// import NoticeModal from './notice-modal';

import "./index.less";

const TabPane = Tabs.TabPane;

let isSport = false;

// 切换边栏显示或者消失
const toggleSide = (dispatch, isShow = false) => {
  if (isShow) {
    // 切换外壳显示动画
    dispatch({ type: "WaterControlBureauResourceMgt/toggleHideWrap" });
    setTimeout(() => {
      dispatch({ type: "WaterControlBureauResourceMgt/toggleCollapsed" });
    }, 0);
  } else {
    // 关闭的时候 等待动画完毕的时候执行隐藏外壳
    dispatch({ type: "WaterControlBureauResourceMgt/toggleCollapsed" });
    setTimeout(() => {
      isSport = false;
      dispatch({ type: "WaterControlBureauResourceMgt/toggleHideWrap" });
    }, 300);
  }
};

class ResourceMgt extends Component {
  constructor(props) {
    super(props);
    this.state = {
      //分页判断是否查询了
      isOldSearch_tab1: null,
      isOldSearch_tab2: null,

      //选择的新闻
      selectNews: [],
      selectedRowKeys: [],
      //默认没有返回按钮，如果从栏目跳转过来，则给与一个返回按钮
      onBack: undefined,

      current: undefined,
      pushtoDialogVisible: false,
      pushtoDialogLoading: false,
      pushing: false,
      status: 5,
      tabsKey: 1,
      oldData: null,
      fatherArr: null,
      disabledList: [],
      rangeType: 0, //推送范围
      visibleType: 1, //可见性 1：所有组织可见；2：接收组织可见
    };
  }

  async componentDidMount() {
    // debugger
    // const { dispatch } = this.props;
    // this.props.form.setFieldsValue({ 'sort_num': ''})

    const self = this;
    const { dispatch, location, form, crownList, history, match } = this.props,
      { setFieldsValue } = form;
    const record = location.state
      ? location.state.record
        ? location.state.record
        : null
      : null;
    let tabsKey = location.state
      ? location.state.tabsKey
        ? location.state.tabsKey
        : 1
      : 1;

    if (match && match.params) {
      tabsKey = match.params.tab === "2" ? 2 : 1;
    }

    // console.log(location);

    history.listen((location, action) => {
      //历史回退时切换回之前标签页（可能不太完善，应该需要解析query state等将参数带回loadnewsList）
      if (action === "POP") {
        const { pathname } = location;
        let match;
        if (
          (match = /(?:WaterControlBureau\/ResourceMgt\/)(\d+)/.exec(pathname))
        ) {
          if (match[1]) {
            tabsKey = parseInt(match[1]) || 1;
            self.setState(
              {
                tabsKey,
                status: tabsKey === 1 ? 5 : 6,
              },
              () => {
                self.loadnewsList();
              }
            );
          }
        }
      }
    });

    if (tabsKey) {
      this.setState({
        tabsKey,
        status: tabsKey === 1 ? 5 : 6,
      });
    }

    await dispatch({ type: "WaterControlBureauResourceMgt/getColumn" });

    let news_column_id = undefined;
    if (record) {
      news_column_id = record.news_column_id;
      const { crownList } = this.props.WaterControlBureauResourceMgt;
      let fatherArr = this.getColmnFather(crownList, news_column_id);
      // console.log('呵呵')
      // console.log(fatherArr)

      setFieldsValue({
        ["cid_tab" + tabsKey]: fatherArr,
      });
      this.setState({
        fatherArr,
        onBack: function () {
          history.goBack();
        },
      });
    }

    if (news_column_id) {
      let searchKey = "isOldSearch_tab" + this.state.tabsKey;

      this.setState({
        [searchKey]: {
          cid: news_column_id,
        },
        oldData: news_column_id,
      });
    }

    this.loadnewsList({ cid: news_column_id });
  }
  // 获取节点的父节点路径用于初始化
  getColmnFather(data, inputValue) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].value === inputValue) {
        return [data[i].value];
      }
      if (
        data[i].children !== null &&
        data[i].children !== undefined &&
        data[i].children !== "" &&
        data[i].children.length > 0
      ) {
        var ret = this.getColmnFather(data[i].children, inputValue);
        if (ret === "" || ret === undefined || ret === null) {
        } else {
          ret.unshift(data[i].value);
          return ret;
        }
      }
    }
    return null;
  }

  //改变栏目下拉框选择
  crownChange(e) {
    // console.log(value);
    // const { setFieldsValue } = this.props.form;
    // setFieldsValue({'cid':value[value.length-1]});
  }
  // 减少数字
  minusIndex(e) {
    e.preventDefault();
    let _number = Number(this.props.form.getFieldValue("sort_num") || 0);
    if (_number === 0) {
      this.props.form.setFieldsValue({ sort_num: 0 });
    } else {
      this.props.form.setFieldsValue({ sort_num: _number - 1 });
    }
  }
  // 增加数字
  addIndex(e) {
    e.preventDefault();
    let _number = Number(this.props.form.getFieldValue("sort_num") || 0);
    this.props.form.setFieldsValue({ sort_num: _number + 1 });
  }
  // 输入改变主要剔除非数字
  numberChange(e) {
    e.preventDefault();
    let isNumber = !isNaN(this.props.form.getFieldValue("sort_num") || 0);
    if (isNumber) {
    } else {
      this.props.form.setFieldsValue({ sort_num: "" });
    }
  }
  // 关闭回调
  edtiIndexCancel(e) {
    e.preventDefault();
    const { dispatch } = this.props;
    this.props.form.resetFields(["sort_num"]);
    dispatch({
      type: "WaterControlBureauResourceMgt/save",
      payload: {
        editIndexShow: false,
        editIndexData: {},
      },
    });
  }
  // edtiIndexCancel (e)
  // 查询新闻
  async loadnewsList(param = {}, isSearch) {
    const { dispatch } = this.props;
    const { validateFields, getFieldsValue } = this.props.form;
    const _this = this;
    const values = getFieldsValue();
    values.flag = this.state.tabsKey

    const status = this.state.status;
    const oldData = this.state.oldData;
    // console.log('values', values);

    const tabsKey = "tab" + this.state.tabsKey;
    const searchKey = "isOldSearch_" + tabsKey;
    const searchParams = map(values, {
      ["top_status"]: {
        key: "top_status",
        value: (top_status) => {
          return top_status ? 1 : undefined;
        },
      },
      keyword: {
        key: "keyword",
        value: (keyword) => (keyword ? keyword : undefined),
      },
      ["column_id"]: {
        key: "column_id",
        value: (column_id) => {
          return column_id;
        },
      },
      ["start_time"]: {
        key: "start_time",
        value: (s) => (s ? s.format("YYYY-MM-DD HH:mm:ss") : undefined),
      },
      ["end_time"]: {
        key: "end_time",
        value: (s) => (s ? s.format("YYYY-MM-DD HH:mm:ss") : undefined),
      },
      ["status"]: {
        key: "status",
        value: (status) => {
          if (this.state.tabsKey == 1) {
            return this.state.status;
          }
          return status ? status : this.state.status || undefined;
        },
      },
      flag: {
        key: "flag",
        value: (flag) => {
          return flag
        },
      }
    });

    if (isSearch) {
      const { crownList } = this.props.WaterControlBureauResourceMgt;
      const fatherArr = this.getColmnFather(crownList, searchParams.column_id);

      this.setState({
        oldData: searchParams.column_id,
        fatherArr,
      });
    } else {
      if (!searchParams.column_id) {
        searchParams.column_id = oldData;
      }
    }

    _this.setState({
      [searchKey]: { status, ...searchParams, ...param },
    });

    await dispatch({
      type: "WaterControlBureauResourceMgt/searchnewsList",
      payload: { page: 1, status, ...searchParams, ...param },
    });
  }

  searchSubmit(e) {
    e.preventDefault();
    this.loadnewsList(null, true);
  }
  //删除新闻
  async deleteNews(row) {
    const { dispatch } = this.props;
    await dispatch({
      type: "WaterControlBureauResourceMgt/deleteNews",
      payload: {
        id: row.resource_id,
      },
    })
      .then(() => {
        message.success("删除成功");
        this.refreashList();
      })
      .catch((error) => {
        message.error(error.message);
      });
  }
  //改变页码
  async changePage(page) {
    console.log("🐇", page)
    const { dispatch, form } = this.props,
      { validateFields, getFieldValue } = form;
    await dispatch({
      type: "WaterControlBureauResourceMgt/setPage",
      payload: { pageNum: page.current },
    });

    this.loadnewsList({ page: page.current });
  }
  // 展示设置排序的modal
  async setIndex(row) {
    const { dispatch } = this.props;
    this.props.form.resetFields(["sort_num"]);
    if (row.sort_num) {
      this.props.form.setFieldsValue({ sort_num: row.sort_num });
    }

    await dispatch({
      type: "WaterControlBureauResourceMgt/save",
      payload: {
        editIndexData: {},
      },
    });
    await dispatch({
      type: "WaterControlBureauResourceMgt/save",
      payload: {
        editIndexShow: true,
        editIndexData: row,
      },
    });
  }
  async toggleFocus(e, row) {
    // 设置为聚焦
    // true的时候去设置聚焦，且必须图片上传成功后才能设置聚焦
    // false的时候去取消聚焦
    if (e.target.checked === true) {
      const { dispatch } = this.props;
      await dispatch({
        type: "WaterControlBureauResourceMgt/save",
        payload: {
          focusIsShow: e.target.checked,
          editPhotosData: row,
          focusIsEdit: false,
          focusDisplayImageUrl: "",
        },
      });
    } else if (e.target.checked === false) {
      this.goSetNotFocus(row);
    }
  }
  // 从操作点击 "聚焦图片" 按钮进入编辑焦点
  async toEditFocusImage(row) {
    const { dispatch } = this.props;
    let _imagesArr = row.imgs ? row.imgs : [];
    let _imageFocusUrl = "";
    for (let i = 0; i < _imagesArr.length; i++) {
      if (_imagesArr[i].type === 5) {
        _imageFocusUrl = `${`${CDN}/`}${_imagesArr[i].url}`;
      }
    }
    await dispatch({
      type: "WaterControlBureauResourceMgt/save",
      payload: {
        focusIsShow: true,
        editPhotosData: row,
        focusIsEdit: true,
        focusDisplayImageUrl: _imageFocusUrl,
      },
    });
  }
  // 设置为聚焦
  async goSetFocus(photosUrl) {
    const { dispatch } = this.props;
    //debugger
    const { editPhotosData } = this.props.newsList;
    if (!editPhotosData || !editPhotosData.news_id) {
      message.error("请先选要更新图片的新闻");
      return;
    }
    if (!photosUrl) {
      message.error("请传入图片");
      return;
    }
    await dispatch({
      type: "WaterControlBureauResourceMgt/setRecommend",
      payload: {
        id: editPhotosData.news_id,
        type: "3",
        focus_img: photosUrl,
      },
    })
      .then(() => {
        // 设置成功拉取数据
        dispatch({
          type: "WaterControlBureauResourceMgt/save",
          payload: {
            focusIsShow: false,
            editPhotosData: {},
            focusIsEdit: undefined,
            focusDisplayImageUrl: "",
            // showImageSubmitOpacity: false
          },
        });
        this.refreashList();
      })
      .catch((error) => {
        dispatch({
          type: "WaterControlBureauResourceMgt/save",
          payload: {
            // showImageSubmitOpacity: false
          },
        });
        message.error(error.message);
      });
  }
  // 设置为非聚焦
  async goSetNotFocus(row) {
    const { dispatch } = this.props;
    await dispatch({
      type: "WaterControlBureauResourceMgt/setRecommend",
      payload: {
        id: row.news_id,
        type: "4",
      },
    })
      .then(() => {
        this.refreashList();
      })
      .catch((error) => {
        message.error(error.message);
      });
  }
  // 设置排序提交
  async setIndexSubmit(e) {
    e.preventDefault();
    const { dispatch } = this.props;
    const { editIndexData } = this.props.WaterControlBureauResourceMgt;
    const { validateFields } = this.props.form;
    validateFields(["sort_num"], async (err, values) => {
      if (!err) {
        await dispatch({
          type: "WaterControlBureauResourceMgt/setResourceIndex",
          payload: {
            nid: editIndexData.resource_id,
            oid: this.props.form.getFieldValue("sort_num"),
          },
        })
          .then(() => {
            message.success("设置成功");
            this.refreashList();
            dispatch({
              type: "WaterControlBureauResourceMgt/save",
              payload: {
                editIndexShow: false,
              },
            });
          })
          .catch((error) => {
            message.error(error.message);
          });
      }
    });
  }
  //移动文章
  async moveNews() {
    const { dispatch, form } = this.props,
      { getFieldValue } = form;
    let cid = getFieldValue("movenews");
    if (this.state.selectNews.length === 0) {
      message.error("请选择所移动的新闻");
    } else if (
      getFieldValue("movenews") == undefined ||
      getFieldValue("movenews").length <= 0
    ) {
      message.error("请选择所要移动到的栏目");
    } else {
      cid ? (cid = cid[cid.length - 1]) : (cid = "");
      await dispatch({
        type: "WaterControlBureauResourceMgt/batchMoveNews",
        payload: {
          ids: this.state.selectedRowKeys,
          id: cid,
        },
      })
        .then(() => {
          message.success("移动成功");
          this.setState({
            selectNews: [],
            selectedRowKeys: [],
          });
          this.refreashList();
        })
        .catch((error) => {
          message.error(error.message);
        });
    }
  }
  //选择新闻
  saveSelectNews(newsList, selectedRowKeys) {
    console.log(newsList, selectedRowKeys)
    const news = [];
    for (let val of newsList) {
      news.push(val);
    }
    this.setState({
      selectNews: news,
      selectedRowKeys,
    });
  }
  //
  /**
   * 上传图片提交
   * @param {*} photoUrl 图片名称或url
   * @param {*} isSetFocus  是否是点击聚焦checkBox设置
   */
  async setFocusInfoSubmit(photoData) {
    const { path } = photoData;
    const photoUrl = path; //只存oss相对路径不带cdn地址

    const { dispatch } = this.props;
    const { editPhotosData, focusIsEdit } = this.props.newsList;
    //debugger
    if (focusIsEdit === true) {
      this.editPhotosSubmit(photoUrl);
    } else if (focusIsEdit === false) {
      this.goSetFocus(photoUrl);
    }
  }
  /**
   * 修改聚焦图片
   * @param {*} photoUrl 图片名称或url
   * @param {*} isSetFocus  是否是点击聚焦checkBox设置
   */
  async editPhotosSubmit(photoUrl) {
    const { dispatch } = this.props;
    const { editPhotosData } = this.props.newsList;
    if (!editPhotosData || !editPhotosData.news_id) {
      message.error("请先选要更新图片的新闻");
      return;
    }
    if (!photoUrl) {
      message.error("请传入图片");
      return;
    }
    await dispatch({
      type: "WaterControlBureauResourceMgt/setPhotos",
      payload: {
        id: editPhotosData.news_id,
        url: photoUrl,
      },
    })
      .then(() => {
        message.success("设置聚焦图片成功");
        dispatch({
          type: "WaterControlBureauResourceMgt/save",
          payload: {
            focusIsShow: false,
            editPhotosData: {},
            focusIsEdit: undefined,
            focusDisplayImageUrl: "",
            //   showImageSubmitOpacity: false
          },
        });
        this.refreashList();
      })
      .catch((error) => {
        dispatch({
          type: "WaterControlBureauResourceMgt/save",
          payload: {
            //   showImageSubmitOpacity: false
          },
        });
        message.error(error.message);
      });
  }
  // 置顶
  async goSetTop(e, row) {
    let _type;
    if (e.target.checked === true) {
      _type = 1;
    } else if (e.target.checked === false) {
      _type = 0;
    }
    if (_type != undefined) {
      const { dispatch } = this.props;
      await dispatch({
        type: "WaterControlBureauResourceMgt/setRecommend",
        payload: {
          id: row.resource_id,
          type: _type,
        },
      })
        .then(() => {
          this.refreashList();
        })
        .catch((error) => {
          message.error(error.message);
          this.refreashList();
        });
    }
  }
  // 刷新列表
  refreashList() {
    this.loadnewsList();
  }

  //推送新闻
  pushto(data) {
    const self = this;
    const { dispatch, newsMatrix } = this.props;
    const { currentData, rangeType, visibleType, selectedData } = data;

    // console.log(currentData, rangeType, visibleType,  selectedData)

    this.setState({
      pushtoDialogLoading: true,
    });

    dispatch({
      type: "newsMatrix/pushto",
      payload: {
        // news_id: currentData.news_id,
        // action: 1,  //1：发起/修改 2：撤回
        // is_org: rangeType,
        // orgs: selectedData, //selectedData.map(org=>org.org_id),
        // visibility: visibleType

        news_id: currentData.news_id,
        push_orgs: !!rangeType,
        org_infos: selectedData, //selectedData.map(org=>org.org_id),
        matrix_visibility: visibleType === 1, //是否全体可见	1 2 => true false
      },
    })
      .catch((e) => {
        message.error(e);
      })
      .finally(() => {
        message.success("推送成功");

        self.setState({
          pushtoDialogLoading: false,
          pushtoDialogVisible: false,
        });

        self.refreashList();
      });
  }

  changeTabs(key) {
    const { history, location } = this.props;

    // console.log(location);

    history.push({
      pathname: `/WaterControlBureau/ResourceMgt/${key}`,
      state: location.state,
      search: location.search,
      hash: location.hash,
    });
    // return ;

    //根据Tabs 区分 status=5 和 status=6 展示
    const status = key === "1" ? 5 : 6;
    const self = this;
    this.props.form.setFieldsValue({
      ["type_tab" + key]: "0",
      ["cid_tab" + key]: this.state.fatherArr,
    });
    this.setState(
      {
        status,
        tabsKey: key,
      },
      () => {
        //https://react.docschina.org/docs/react-component.html#setstate
        self.loadnewsList({ status });
        this.props.form.setFieldsValue({
          ["cid_tab" + key]: this.state.fatherArr,
        });
        if (key == 2) {
          this.props.form.setFieldsValue({
            ["status_tab" + key]: "6",
          });
        }
      }
    );
  }

  render() {
    const { pushtoDialogVisible, disabledList, rangeType, visibleType } =
      this.state;
    const { WaterControlBureauResourceMgt, dispatch, history, newsMatrix, location } = this.props,
      {
        dataList,
        listLoading,
        crownList,
        pageNew,
        modalVisible,
        isHideWrap,
        collapsed,
        submitButtonLoading,
        editIndexShow,
        focusIsShow,
        focusDisplayImageUrl,
        showImageSubmitOpacity,
      } = WaterControlBureauResourceMgt,
      _app = {
        onBack: this.state.onBack,
        dispatch,
        props: this.props,
        listLoading,
        crownList,
        dataList,
        pageNew,
        focusIsShow,
        selectNews: this.state.selectNews,
        selectedRowKeys: this.state.selectedRowKeys,
        statusValue: this.state.status,
        focusDisplayImageUrl,
        showImageSubmitOpacity,
        // initColumValue: this.state.initColumValue,
        //改变栏目下拉框选择
        toggleFocus: this.toggleFocus.bind(this), // 设置为聚焦
        toEditFocusImage: this.toEditFocusImage.bind(this),
        crownChange: this.crownChange.bind(this),
        searchSubmit: this.searchSubmit.bind(this),
        deleteNews: this.deleteNews.bind(this),
        changePage: this.changePage.bind(this),
        moveNews: this.moveNews.bind(this),
        saveSelectNews: this.saveSelectNews.bind(this),
        editIndexShow,
        edtiIndexCancel: this.edtiIndexCancel.bind(this),
        minusIndex: this.minusIndex.bind(this),
        addIndex: this.addIndex.bind(this),
        numberChange: this.numberChange.bind(this),
        setIndex: this.setIndex.bind(this),
        setIndexSubmit: this.setIndexSubmit.bind(this),
        setFocusInfoSubmit: this.setFocusInfoSubmit.bind(this),
        goSetTop: this.goSetTop.bind(this),
        showNews: (row) => {
          const tabsKey = this.state.tabsKey;
          const action = row.status == 4 ? "draft" : "edit";
          history.push({
            pathname: "/WaterControlBureau/ResourcePublish",
            query: {
              id: row.resource_id,
              action,
              record: {
                column_id: this.state.oldData,
              },
              tabsKey,
              status: row.status,
            },
          });
        },
        // todo:打开推送的modal
        showPushtoDialog: ((row) => {
          //   this.setState({
          //     current:row,
          //     pushtoDialogVisible:true
          //   })
          newsMatrixResult({ news_id: row.news_id, pageable: false }).then(
            (data) => {
              const _data = data.data;
              if (_data.code != 0) {
                return message.error(_data.message);
              }
              const { orgs: orgIds, org, matrix_visibility } = _data.data;
              this.setState({
                current: row,
                disabledList: orgIds || [],
                pushtoDialogVisible: true,
                //推送配置信息保存到state，方便传入到pushTo组件
                rangeType: org >> 0, //推送范围 是否推送给指定组织 false=>0 true => 1
                visibleType: matrix_visibility ? 1 : 2, //矩阵是否可见 1：全员可见；0：仅被推送组织可见
              });
            }
          );
        }).bind(this),
        setShowStatus: (async (row) => {
          await dispatch({
            type: "WaterControlBureauResourceMgt/setVisibility",
            payload: {
              news_id: row.news_id,
              visibility: row.visibility ? 0 : 1,
            },
          });

          this.loadnewsList();
        }).bind(this),
      };

    const pushToProps = {
      title: "新闻",
      currentData: this.state.current,
      visible: this.state.pushtoDialogVisible,
      isLoading: this.state.pushtoDialogLoading,
      onSave: this.pushto.bind(this),
      pushing: newsMatrix.pushing,
      disabledList,
      rangeType,
      visibleType,
      onCancel: () =>
        this.setState({
          pushtoDialogLoading: false,
          pushtoDialogVisible: false,
        }),
    };

    return (
      <div className={"news-list"}>
        <div>
          <SearchHeader title="资源管理" onBack={_app.onBack} />
        </div>
        {pushtoDialogVisible ? <PushTo {...pushToProps} /> : null}

        <div>&nbsp;</div>
        <Tabs
          activeKey={this.state.tabsKey + ""}
          onChange={this.changeTabs.bind(this)}
          animated={false}
        >
          <TabPane tab="已发布资源" key="1">
            <IndexView tabsKey={1} {..._app} />
          </TabPane>
          <TabPane tab="未发布资源" key="2">
            <IndexView tabsKey={2} {..._app} />
          </TabPane>
        </Tabs>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  WaterControlBureauResourceMgt: state.WaterControlBureauResourceMgt,
  newsMatrix: state.newsMatrix,
});

export default connect(mapStateToProps)(Form.create()(ResourceMgt));
