.menu-box {
  width: 100%;
  min-height: 100%;
  background: url(./image/bg.png) center / cover no-repeat;
  .inner-box {
    overflow: hidden;
    .title-box {
      margin: 10vh auto 0;
      width: 943px;
      height: 134px;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      background: url(./image/title.png) center / cover no-repeat;
    }
    .menu {
      margin: 10vh auto 0;
      width: 1036px;
      .menu-top,
      .menu-bottom {
        display: flex;
        justify-content: space-around;
      }
      .menu-top {
        margin: 0 auto;
        width: 80%;
        .menu-item:nth-child(2) {
          background: linear-gradient(90deg, #18b5d8 0%, #14c4eb 100%);
        }
      }
      .menu-bottom {
        margin-top: 60px;
        .menu-item {
          background: linear-gradient(
            180deg,
            #3b9ff2 0%,
            rgba(22, 74, 118, 0) 100%
          );
        }
      }
      .menu-item {
        display: flex;
        align-items: center;
        justify-content: center;

        width: 280px;
        height: 160px;
        background: linear-gradient(90deg, #1796ef 0%, #3fa0f2 100%);
        border-radius: 10px 10px 10px 10px;
        opacity: 1;

        font-size: 40px;
        font-family: Source <PERSON>s CN-Bold, Source <PERSON>s CN;
        font-weight: bold;
        color: #ffffff;

        cursor: pointer;
      }
    }
  }
}
