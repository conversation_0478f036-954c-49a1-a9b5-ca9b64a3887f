import React, { Component } from 'react';
import { Icon, message, Upload, Spin } from 'antd';
import Clip from 'components/clip/clipping';
import { uploadBase64File } from 'apis/file';
import { uploadHost, CDN } from 'apis/config';
import { formHeaders } from 'client/tool/axios';

class Image extends Component {
  constructor() {
    super();
    this.state = {
      showClip: false,
      spinning: false,
    };
    this.uploadConfig = {
      supportServerRender: true,
      showUploadList: false,
      name: 'upfile',
      data: (file) => {
        return {
          upType: 'image',
          up_name: file.name,
        };
      },
      accept: 'image/*',
      action: `${uploadHost}/file/upload`,
      headers: formHeaders(),
      onChange: (info) => {
        this.setState({ spinning: true });
        if (info.file.status === 'done') {
          this.setState({ spinning: false });
          if (+info.file.response.code !== 0) {
            message.error(`上传失败：${info.file.response.message}`);
          } else {
            console.log('😈😈😈😈😈: UploadVedio -> constructor -> info', info.file.response.data[0].path);
            this.props.onChange(info.file.response.data[0].path);
          }
        } else if (info.file.status === 'error') {
          message.error('网络错误，上传失败');
        }
      },
    };
    this.onClick = this._onClick.bind(this);
    this.cancel = this._cancel.bind(this);
    this.onSubmit = this._onSubmit.bind(this);
  }
  _onClick() {
    const { size } = this.props
    const showClip = size && typeof size.w == 'number'
    if (!showClip) {
      message.info('请设置图片大小')
    }
    this.setState({
      showClip,
    });
  }
  // 提交图片
  _onSubmit(base64) {
    const self = this;
    return new Promise((r, j) => {
      uploadBase64File({
        upfile: base64,
        upType: 'image',
      })
        .then((data) => {
          const _data = data.data;
          if (_data.code != 0) {
            message.error(_data.message);
            j(_data.message);
            return;
          } else {
            this.props.onChange(_data.data.path);
            r('成功');
          }
        })
        .then(() => {
          self.setState({
            showClip: false,
          });
          r();
        })
        .catch((e) => {
          j(e);
        });
    });
  }
  // 关闭裁剪框
  _cancel() {
    this.setState({
      showClip: false,
    });
  }
  render() {
    const { size, title = '请上传图片', value, ratio = 2, type } = this.props;
    var imgSrc = '';
    if (value) {
      imgSrc = `${CDN}/${value}`;
    }
    const width = size.w / ratio;
    if (type === 'scale') {
      return (
        <Spin wrapperClassName="image-spin" spinning={this.state.spinning} indicator={<Icon type="loading" style={{ fontSize: 24 }} spin />}>
          <Upload {...this.uploadConfig}>
            <div className={`image ${imgSrc ? 'scale-img' : 'scale-no-img'}`} style={{ width }}>
              {imgSrc ? (
                <React.Fragment>
                  <span></span>
                  <img src={imgSrc} alt="" className="view" />
                </React.Fragment>
              ) : (
                <div className="text">
                  <p>
                    <Icon type="plus" />
                  </p>
                  <p>点击上传图片{`：${size.w}*${size.h || '*'} px, jpg或png`}</p>
                </div>
              )}
            </div>
          </Upload>
        </Spin>
      );
    }

    const height = size.h / ratio;
    return (
      <React.Fragment>
        <div className="image" style={{ width, height }} onClick={this.onClick}>
          {imgSrc ? (
            <React.Fragment>
              <span></span>
              <img src={imgSrc} alt="" className="view" />
            </React.Fragment>
          ) : width < 110 ? (
            <div className="text">
              <p>
                <Icon type="plus" />
              </p>
            </div>
          ) : (
            <div className="text">
              <p>
                <Icon type="plus" />
              </p>
              <p>点击上传图片{width < 240 ? `` : `：${size.w}*${size.h}px, jpg或png`}</p>
              {width < 240 ? (
                <p>
                  {size.w}*{size.h}px, jpg或png
                </p>
              ) : null}
            </div>
          )}
          {this.state.showClip ? (
            <Clip src={imgSrc} size={size} title={title} onCancel={this.cancel} onSubmit={this.onSubmit} />
          ) : null}
        </div>
        <div className="image-text-tips">{width < 110 ? `点击上传图片：${size.w}*${size.h}px, jpg或png` : null}</div>
      </React.Fragment>
    );
  }
}

export default Image;
