.imageClip {
  .up-file-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 10px;
    width: 170px;
    height: 150px;
    text-align: center;
    border: 1px solid #d5d5d5;
    color: #cccccc;
    cursor: pointer;
    .tips {
      color: #222;
      font-size: 14px;
    }
    .anticon-plus {
      font-size: 24px;
    }
  }
  .hide-upfile {
    display: none;
  }
  .preview-img-wrap {
    display: flex;
    flex-wrap: wrap;
    height: 100%;
    .img-wrap {
      position: relative;
      padding: 5px;
      height: 100%;
      .img {
        display: inline-block;
        min-width: 150px;
        min-height: 150px;
      }
      .operation-btn {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        .anticon {
          display: none;
          padding: 20px 10px;
          font-size: 20px;
          color: #ddd;
          cursor: pointer;
          &:hover {
            color: #fff;
          }
        }
      }
      &:hover {
        .operation-btn {
          background-color: rgba(0, 0, 0, 0.3);
          .anticon {
            display: inline-block;
          }
        }
      }
    }
  }
}
