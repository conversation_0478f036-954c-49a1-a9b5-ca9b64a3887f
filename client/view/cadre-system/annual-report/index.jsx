import React, { useRef, useState, useEffect } from "react";
import "./index.less";
import {
  Form,
  Input,
  Popconfirm,
  Button,
  Table,
  Modal,
  message,
  Tooltip,
  DatePicker,
} from "antd";
import OrgTree from "client/components/org-tree";
import PAutoComplete from "../components/PAutoComplete";
import DateSingle from "components/date-single/DateSingle";
import {
  queryAnnualReport,
  addOrUpdateAnnualReport,
  deleteAnnualReport,
  exportAnnualReport,
  getAnnualReportList,
  saveAnnualReport,
  deleteAnnualReport1,
} from "client/apis/cadre-portrait";
const { MonthPicker } = DatePicker;
import moment from 'moment';
function index({ form, history }) {
  const { TextArea } = Input;
  const { RangePicker } = DatePicker;
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);
  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [page, setPage] = useState(1);

  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
  const [annualId, setAnnualId] = useState("");
  useEffect(() => {
    loadData();
  }, []);

  // 加载数据
  const loadData = async ({ page: _page, org_id } = {}) => {
    const fields = getFieldsValue();
    const params = {
      // ...getFieldsValue(),
      // ...fields,
      page: _page || page,
      org_id: org_id || org_ids[0],
      name: fields.name,
      // start_time: fields.time && fields.time.length > 0 ? fields.time[0].format("YYYY-MM-DD") : "",
      // end_time: fields.time && fields.time.length > 0 ? fields.time[1].format("YYYY-MM-DD") : ""
    };
    if (fields.time && fields.time.length > 0) {
      params.start_time = fields.time[0].format("YYYY-MM-DD");
      params.end_time = fields.time[1].format("YYYY-MM-DD");
    }
    const res = await getAnnualReportList(params);
    if (res.data.code === 0) {
      const data = res.data.data.content;
      setDataSource(data);
      setPagination((prev) => {
        return {
          ...prev,
          total: res.data.data.totalElements,
        };
      });
    } else {
      message.error(res.data.message);
    }
    setLoading(false);
  };

  const onChange = (orgs, org) => {
    setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    setExportLoading(true);
    const params = getFieldsValue();
    const res = await exportAnnualReport({
      ...params,
      org_id: org_ids[0],
    });

    setExportLoading(false);
  };

  const onAdd = () => {
    setVisible(true);
    setModalType("add");
  };

  const onInputFile = () => {
    history.push(
      `/import-page?type=18&org_id=${org_ids[0]}&org_name=${org_name}`
    );
  };

  const onDel = async (record) => {
    const res = await deleteAnnualReport1({
      annual_report_id: record.annual_report_id,
    });
    if (res.data.code === 0) {
      loadData();
    } else {
      message.error(res.data.message);
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");
    setAnnualId(record.annual_report_id);

    queueMicrotask(() => {
      const _ = { ...record };
      _.user_name = _.user_name ? _.user_name : undefined;
      if (_.report_time) {
        _.report_time = moment(_.report_time.replace(/(\d{4})年(\d{1,2})月/, '$1-$2'),'YYYY-MM');
      } else {
        _.report_time = null;
      }
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "述职时间",
      dataIndex: "report_time",
      key: "report_time",
      width: 100,
      align: "center",
    },
    {
      title: "姓名",
      dataIndex: "user_name",
      key: "user_name",
      width: 100,
      align: "center",
    },
    // {
    //   title: "出生年月",
    //   dataIndex: "birthday",
    //   key: "birthday",
    //   width: 100,
    //   align: "center",
    // },
    {
      title: "时任职务",
      dataIndex: "job",
      key: "job",
      width: 150,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "分管联系工作",
      dataIndex: "charge_range",
      key: "charge_range",
      width: 150,
      align: "center",
      render: (text) => <div style={{ textAlign: "left" }}>{text}</div>,
    },
    {
      title: "主要特点及具体事例",
      dataIndex: "merit",
      key: "merit",
      width: 180,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '180px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "主要不足及具体事例",
      dataIndex: "drawback",
      key: "drawback",
      width: 180,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '180px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "最满意的工作",
      dataIndex: "satisfaction",
      key: "satisfaction",
      width: 180,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '180px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "不满意的工作",
      dataIndex: "dissatisfaction",
      key: "dissatisfaction",
      width: 180,
      align: "center",
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ maxWidth: '180px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </div>
        </Tooltip>
      )
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a>删除</a>
            </Popconfirm>
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据
    setPage(1);
    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;

    form.validateFields(async (err, values) => {
      if (!err) {
        const { user_name: _user_name, start_time: _start_time } = values;

        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;
          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }
        if (annualId) {
          values.annual_report_id = annualId;
        }
        if (values.report_time) {
          values.report_time = values.report_time.format("YYYY年MM月");
        }
        const res = await saveAnnualReport({
          ...values,
        });

        if (res.data.code === 0) {
          setVisible(false);
          message.success("操作成功");
          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("marriage_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名" wrapperCol={{ span: 14 }}>
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [{ required: true, message: "请输入" }],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        {/* <Form.Item label="出生年月">
          {getFieldDecorator("birthday", {
            rules: [{ required: true, message: "请选择出生年月" }],
          })(<DateSingle placeholder="请选择" type="year" allowClear={false} isWrap={false} />)}
        </Form.Item> */}
        <Form.Item label="述职时间">
          {getFieldDecorator("report_time", {
            rules: [{ required: true, message: "请选择征求意见时间" }],
          })(<MonthPicker placeholder="请选择" />)}
        </Form.Item>
        <Form.Item label="时任职务">
          {getFieldDecorator("job", {
            rules: [{ required: false, message: "请输入时任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="分管联系工作">
          {getFieldDecorator("charge_range", {
            rules: [{ required: false, message: "请输入分管联系工作" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="主要特点及具体事例">
          {getFieldDecorator("merit", {
            rules: [{ required: true, message: "请输入主要特点及具体事例" }],
          })(<TextArea placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="主要不足及具体事例">
          {getFieldDecorator("drawback", {
            rules: [{ required: false, message: "请输入" }],
          })(<TextArea placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="最满意的工作">
          {getFieldDecorator("satisfaction", {
            rules: [{ required: false, message: "请输入" }],
          })(<TextArea placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="不满意的工作">
          {getFieldDecorator("dissatisfaction", {
            rules: [{ required: false, message: "请输入" }],
          })(<TextArea placeholder="请输入" />)}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="annual-report">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("name")(
                <Input style={{ width: "150px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="述职时间">
              {getFieldDecorator("time", {
              })
                (<RangePicker />)}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "年度述职"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
