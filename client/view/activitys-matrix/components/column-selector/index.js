

import React, { Component } from "react";

import { Modal, message, Select } from "antd";

import PropTypes from "prop-types";

import { getColumn } from "apis/activity";



const Option = Select.Option;

const propTypes = {
  currentRow: PropTypes.object,
  visible: PropTypes.bool,
  onChange: PropTypes.func,
  onCancel: PropTypes.func,
};

const defaultProps = {
  currentRow: null,
  visible: false,
  onChange: () => { },  //参数当前活动数据，是否显示（1|0），栏目ID
  onCancel: () => { }
};

export default Object.assign(class ColumnSelector extends Component {

  constructor(props) {
    super(props);

    this.state = {
      data: [],
      selectValue: 0
    }

  }

  componentDidUpdate(prevProps, prevState) {
    const self = this;
    if (this.props.currentRow
      && prevProps.currentRow != this.props.currentRow) {

      const { activity_id } = this.props.currentRow;

      // console.log(activity_id)
      getColumn().then(res => {
        const { data: response, status } = res;
        if (status === 200) {
          const { message: msg, code, data } = response;
          if (code === 0 && msg === "success") {
            self.setState({
              data: data
            })
          }
        }
      }).catch(e => {
        message.error(e);
      })

    }
  }

  handleOk() {
    // console.log(this.state)
    this.props.onChange(this.props.currentRow, 1, this.state.selectValue);
  }

  render() {
    console.log('this.props.visible,', this.props.visible);
    const { data } = this.state;

    const handleChange = (value) => {
      this.setState({
        selectValue: value
      })
    }


    // defaultValue="lucy"
    return <Modal
      title="显示设置"
      width={600}
      visible={this.props.visible}
      onOk={this.handleOk.bind(this)}
      onCancel={this.props.onCancel}
    >
      <span>
        {/* 活动栏目： */}
        互动栏目：
      </span>
      <Select style={{ width: "430px" }}
        // placeholder="选择活动栏目"
        placeholder="选择互动栏目"
        onChange={handleChange}>
        {data && data.length ?
          data.map(column => <Option key={column.activity_column_id} value={column.activity_column_id}>{column.name}</Option>) : ''
        }
      </Select>

    </Modal>
  }


}, { propTypes, defaultProps });
