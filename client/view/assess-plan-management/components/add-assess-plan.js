import React from "react";
import SearchHeader from "components/search-header";
import OrganizeSelector from "components/organize-selector";
import SelectMeetType from "components/select-meeting-type";
import OrgTypeSelector from "components/org-type-selector";
import InputTips from "components/activity-form/sub-components/InputTips";
import { Form, Input, Radio, DatePicker, Button, Select, message, Row, Col } from "antd";
import moment from "moment";
import "./style.less";
import { addAssessPlan } from "apis/assess-plan-manage";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const Option = Select.Option;
const { MonthPicker } = DatePicker;

class AddAssessPlan extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      execute_type_props: [ // 执行方式 可选属性
        {
          value: 1,
          name: "自定义时间段"
        },
        {
          value: 2,
          name: "自然月周期"
        },
        {
          value: 3,
          name: "季度周期"
        },
        {
          value: 4,
          name: "年度周期"
        }
      ],
      quarter: [ // 可选季度
        {
          value: 1,
          name: "第一季度"
        },
        {
          value: 2,
          name: "第二季度"
        },
        {
          value: 3,
          name: "第三季度"
        },
        {
          value: 4,
          name: "第四季度"
        },
      ],
      format: "YYYY-MM-DD", // 时间格式
      mode: "date", // 时间选择框, 显示方式
      endTimeInputDisable: false, // 结束时间是否禁用
      datePickerIsOpen: false, // 开始时间选择框 是否打开
      // 请求参数
      name: "", // 计划名称
      execute_type: 1, // 执行方式
      start_time: "start_time", // 开始时间
      end_time: "", // 结束时间
      start_quarter: 1, // 开始季度
      execute_orgs: [], // 执行组织
      meeting_types: [], // 会议类型
      submitLoading: false, // 按钮 loading

      sendOrgTypes: [],  //发放组织类型

      // 组织类型选择
      orgTypeVisible: false,
    }
    this.updateState = this.updateState.bind(this);
    this.radioChange = this.radioChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleOpenChange = this.handleOpenChange.bind(this);
    this.handlePanelChange = this.handlePanelChange.bind(this);
  }

  // 新增考核计划
  async newAddAssessPlan(_data) {
    const { history } = this.props;
    this.setState({
      submitLoading: true
    });
    addAssessPlan(_data).then(res => {
      if (res) {
        const { data } = res;
        if (data && data.code === 0) {
          message.success("添加成功");
          this.setState({
            submitLoading: false
          });
          history.goBack();
        } else {
          message.error(data.message)
          this.setState({
            submitLoading: false
          })
        }
      }
    }).catch(
      () => {
        console.log("捕获到错误");
      }
    )
  }

  // 设置执行组织 number[], execute_orgs
  setExecuteOrgs(_data) {
    // console.log(_data);
    if (_data && _data.length > 0) {
      this.setState({
        execute_orgs: _data
      })
    } else {
      this.setState({
        execute_orgs: []
      })
    }
  }

  updateState(payload, callback) {
    this.setState(payload, () => callback && callback());
  }

  radioChange(e) {
    const { value } = e.target;
    // console.log(value);
    if (value !== 1) {
      this.setState({
        endTimeInputDisable: true
      })
      if (value === 2) {
        this.setState({
          format: "YYYY-MM",
          mode: "month",
          execute_type: value,
          start_time: "start_month"
        })
      }
      if (value === 3 || value === 4) {
        this.setState({
          format: "YYYY",
          mode: "year",
          execute_type: value,
          start_time: "start_year"
        })
      }
    } else {
      this.setState({
        endTimeInputDisable: false,
        mode: "date",
        format: "YYYY-MM-DD",
        execute_type: value,
        start_time: "start_time"
      })
    }
  }

  handleSubmit() {
    // send_type 发放方式
    const {
      start_time,
      start_quarter,
      execute_orgs,
      meeting_types,
      format,
      sendOrgTypes

    } = this.state;

    const { form } = this.props;

    const { validateFields } = form;


    const org_type_list = (sendOrgTypes || []).map(t => {
      return {
        org_type_child: t.op_key,
        org_type_child_name: t.op_value
      }
    });


    validateFields((err, values) => {
      const { name,
        end_time,
        execute_type,
        is_retire,
        party_group,
        period,
        send_type
      } = values;
      const tempStart_time = values[start_time];
      if (name === "") {
        // return message.error("请输入计划名称");
        // return message.error("请输入组织生活名称");
        return message.error("请输入活动类型名称");
      }

      if (!tempStart_time) {
        return message.error("请选择开始时间");
      }

      if (execute_type === 1) {
        if (!end_time) {
          return message.error("请选择结束时间");
        }
      }

      if (send_type === 2) {
        if (execute_orgs.length <= 0) {
          return message.error("请选择执行组织");
        }
      }

      if (meeting_types.length <= 0) {
        // return message.error("请选择会议类型");
        return message.error("请选择活动类型");
      }
      const _end_time = moment(end_time).format(format);
      const queryData = {
        name,
        execute_type,
        [start_time]: moment(tempStart_time).format(format),
        meeting_types,
        send_type
      }

      if (send_type == 1) {
        queryData['auto_send_type_form'] = {
          org_type_list, is_retire, party_group, period
        }
      } else {
        queryData['execute_orgs'] = execute_orgs;
      }

      // console.log(queryData);


      // 如果是自定义时间段
      if (execute_type === 1) {
        const newQueryData = Object.assign({}, queryData, { end_time: _end_time });
        this.newAddAssessPlan(newQueryData)
      }
      if (execute_type === 2) {
        const start_year = moment(queryData.start_month).format("YYYY");
        const start_month = moment(queryData.start_month).format("MM");
        const newQueryData = Object.assign({}, queryData, { start_year, start_month });
        this.newAddAssessPlan(newQueryData);
      }

      if (execute_type === 3) {
        const newQueryData = Object.assign({}, queryData, { start_quarter });
        this.newAddAssessPlan(newQueryData);
      }
      if (execute_type === 4) {
        this.newAddAssessPlan(queryData);
      }
    })
  }

  handlePanelChange(value) {
    const { setFieldsValue, setFields } = this.props.form;
    const { start_time, format, mode } = this.state;
    let selectedDate = moment(value).format("YYYY");
    const now = new Date();
    const nowYear = now.getFullYear();
    const nowMonth = now.getMonth();
    if (Number(selectedDate) < nowYear) {
      setFieldsValue({ [start_time]: null });
      message.error(`请选择大于等于${nowYear}年以后的时间`);
    } else {
      // 如果选择本年，则判断月份必须在本年度本月之后
      if (mode === "month" && Number(selectedDate) === nowYear) {
        const month = moment(value).month();
        // console.log(month, nowMonth);
        if (month < nowMonth) {
          setFieldsValue({ [start_time]: null });
          message.error(`请选择大于等于${nowYear}年${nowMonth + 1}月以后的时间`);
          return;
        }
      }
      setFieldsValue({ [start_time]: value });
      this.setState({
        datePickerIsOpen: false
      });
    }
  }

  handleOpenChange(open) {
    // console.log(open);
    this.setState({
      datePickerIsOpen: open
    })
  }

  disabledDate(current) {
    // const { mode } = this.state;
    return current && current < moment().subtract(1, "days").endOf("day");
  }


  removeSendTypes(item) {
    this.setState({
      sendOrgTypes: this.state.sendOrgTypes.filter(it => it.op_key !== item.op_key)
    });
  }

  render() {
    const { history, form } = this.props;
    const {
      execute_type_props,
      endTimeInputDisable,
      format, mode, start_quarter,
      quarter, execute_type,
      datePickerIsOpen,
      start_time,
      submitLoading,
      sendOrgTypes,
      orgTypeVisible
    } = this.state;
    const { getFieldDecorator, getFieldValue } = form;

    const formItemLayout = {
      labelCol: { span: 2 },
      wrapperCol: { span: 18 },
    };

    const orgTypeProps = {
      defaultSelectorOrgType: 102803,
      lockSelector: true,
      visible: orgTypeVisible,
      input: sendOrgTypes || [],
      onCancel: () => {
        this.setState({
          orgTypeVisible: false
        });
      },
      updateState: (payload, callback) => {
        this.setState({
          ...payload
        }, callback)
      },
      onChange: (data, callback) => {
        // console.log("onChange", data);
        this.setState({
          sendOrgTypes: data
        }, callback);
      }
    }

    return (
      <div className="addAssessPlan">
        <OrgTypeSelector {...orgTypeProps} />
        <SearchHeader
          onBack={false}
          // title="新增考核计划"
          // title="新增组织生活"
          title="新增活动类型"
          onBack={() => history.goBack()}
          style={{ fontWeight: "bold" }}
        />
        <div className="body">
          <Form>
            {/* <FormItem></FormItem> */}
            <FormItem
              // label="计划名称"
              // label="组织生活名称"
              label="活动类型名称"
              {...formItemLayout}
            >
              <InputTips
                max={50}
                // container={container}
                text={getFieldValue("name")}
              >
                {getFieldDecorator("name", {
                  initialValue: "",
                  rules: [
                    {
                      max: 50,
                      message: `最多输入${50}个字`
                    }
                  ]
                })(<Input placeholder="请输入" rows={4} onBlur={(e) => this.updateState({ name: e.target.value })} />)}
              </InputTips>
            </FormItem>
            <FormItem
              // label="执行方式"
              label="任务周期"
              className="no-margin"
              {...formItemLayout}
            >
              {getFieldDecorator("execute_type", {
                initialValue: execute_type
              })(
                <RadioGroup
                  // style={{ marginTop: 6 }}
                  onChange={this.radioChange}
                >
                  {execute_type_props.map((item, index) => {
                    return <Radio value={item.value} key={index}>{item.name}</Radio>
                  })}
                </RadioGroup>
              )}
            </FormItem>
            <FormItem
              label="开始时间"
              {...formItemLayout}
            >
              {getFieldDecorator(start_time, {
              })(
                <DatePicker
                  placeholder="开始时间"
                  onPanelChange={this.handlePanelChange}
                  disabledDate={(current) => {
                    const endTime = getFieldValue("end_time");
                    if (endTime) {
                      try {
                        const disabledEndTime = moment(endTime.format("YYYY-MM-DD")).add(1, "days").startOf("day");
                        return current &&
                          (
                            (current < moment().subtract(1, "days").endOf("day")) ||
                            (current > disabledEndTime)
                          );
                      } catch (e) {
                        console.error(e)
                      }
                    }
                    return current && (current < moment().subtract(1, "days").endOf("day"));
                  }}
                  onOpenChange={this.handleOpenChange}
                  format={format}
                  mode={mode}
                  open={datePickerIsOpen}
                />
              )}
              <Select
                style={{
                  display: `${execute_type === 3 ? "inline-block" : "none"}`,
                  width: "195px",
                  marginLeft: "20px"
                }}
                onChange={(val) => this.setState({ start_quarter: val })}
                defaultValue={start_quarter}
              >
                {quarter.map((item, index) => (<Option value={item.value} key={index}>{item.name}</Option>))}
              </Select>
            </FormItem>
            {
              !endTimeInputDisable &&
              <FormItem
                label="结束时间"
                {...formItemLayout}
              >
                {getFieldDecorator("end_time", {})(
                  <DatePicker
                    placeholder="结束时间"
                    disabled={endTimeInputDisable}
                    disabledDate={(current) => {
                      const startTime = getFieldValue(start_time);
                      // console.log(startTime);
                      if (!startTime) {
                        return current && current < moment().subtract(1, "days").endOf("day");
                      }
                      else {
                        return current && current < startTime;
                      }
                    }}
                  />
                )}
              </FormItem>
            }

            <FormItem
              // label="会议类型"
              label="活动类型"
              className="no-margin"
              {...formItemLayout}
            >
              <SelectMeetType
                getMeetingType={this.updateState}
              />
            </FormItem>

            <FormItem
              label="发放方式"
              {...formItemLayout}
            >
              {getFieldDecorator("send_type", {
                initialValue: 1
              })(
                <RadioGroup>
                  <Radio value={1}>自动发放</Radio>
                  <Radio value={2}>手动发放</Radio>
                </RadioGroup>
              )}
            </FormItem>
            {getFieldValue('send_type') == 2 ?
              <FormItem
                label="执行组织"
                {...formItemLayout}
                className="no-margin"
              >
                <OrganizeSelector getSelectedValues={(data) => this.setExecuteOrgs(data)} />
              </FormItem>
              :
              <Row>
                <Col offset={2} className="auto-send-type">
                  <FormItem
                    label="组织类型"
                    {...formItemLayout}
                  // className="no-margin"
                  >
                    <a href="javascript:void(0)" onClick={() => {
                      this.setState({
                        orgTypeVisible: true,
                      })
                    }}>点击选择</a>
                    <br />
                    {
                      sendOrgTypes.map((item, index) => {
                        return (
                          <Ant.Tag key={item.op_key || index} closable onClose={(e) => {
                            e.preventDefault();
                            this.removeSendTypes(item)
                          }}>
                            {item.op_value}
                          </Ant.Tag>
                        )
                      })}
                  </FormItem>
                  {/* <div class="clearfloat">&nbsp;</div> */}
                  <FormItem
                    label="离退休组织"
                    {...formItemLayout}
                  // className="no-margin"
                  >
                    {getFieldDecorator("is_retire", {
                      initialValue: 2
                    })(
                      <RadioGroup>
                        <Radio value={1}>包含</Radio>
                        <Radio value={2}>不包含</Radio>
                        <Radio value={3}>仅包含</Radio>
                      </RadioGroup>
                    )}
                  </FormItem>
                  <FormItem
                    label="已成立党小组"
                    {...formItemLayout}
                  // className="no-margin"
                  >
                    {getFieldDecorator("party_group", {
                      initialValue: 1
                    })(
                      <RadioGroup>
                        <Radio value={1}>包含</Radio>
                        <Radio value={2}>不包含</Radio>
                        <Radio value={3}>仅包含</Radio>
                      </RadioGroup>
                    )}
                  </FormItem>
                  <FormItem
                    label="已成立支委会"
                    {...formItemLayout}
                  // className="no-margin"
                  >
                    {getFieldDecorator("period", {
                      initialValue: 1
                    })(
                      <RadioGroup>
                        <Radio value={1}>包含</Radio>
                        <Radio value={2}>不包含</Radio>
                        <Radio value={3}>仅包含</Radio>
                      </RadioGroup>
                    )}
                  </FormItem>
                </Col>
              </Row>
            }
            <FormItem
              wrapperCol={{ span: 10, offset: 2 }}
            >
              <Button
                type="primary"
                className="buttonStyle btnBg"
                onClick={this.handleSubmit}
                loading={submitLoading}
              >
                提交
              </Button>
              <Button
                className="buttonStyle"
                style={{ marginLeft: "26px" }}
                onClick={() => history.goBack()}
              >
                取消
              </Button>
            </FormItem>
          </Form>
        </div>
      </div>
    );
  }
}

export default Form.create()(AddAssessPlan);