// 纪实结果检查相关接口调用
import { http } from "client/tool/axios";
import { cloud } from './config';
/* 获取所有话题 */
export const getTopicAll =(params)=>{
    return http.get(`${cloud}/topic/pc_all`,params)
}
/* 新增话题 */
export const addTopic =(params)=>{
    return http.post(`${cloud}/topic/create`,params)
}
/* 删除话题 */
export const deleteTopic =(params)=>{
    return http.post(`${cloud}/topic/del`,params)
}
/* 获取所有标签 */
export const getAllTag =(params)=>{
    return http.get(`${cloud}/tag/getAllTag`,params)
}
/* 删除标签 */
export const deleteTag = (params)=>{
    return http.post(`${cloud}/tag/del`,params)
}