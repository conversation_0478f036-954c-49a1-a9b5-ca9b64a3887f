const { http } = require('client/tool/axios');
import { host } from './config';

//1040020037001-切换组织
export const switchGroupUrl = (data = {}) => {
    return http.get(`${host}/org/find-all-child-org`, data);
}

//1040020037002-添加党小组
export const addPartyGroupUrl = (data = {}) => {
    return http.post(`${host}/org-group/add`, data);
}

//1040020037003-党小组列表
export const PartyGroupListUrl = (data = {}) => {
    return http.get(`${host}/org-group/list/${data.org_id}`);
}

//1040020037004-党小组编辑
export const editPartyGroupUrl = (data = {}) => {
    return http.post(`${host}/org-group/update`, data);
}

//1040020037005-党小组删除
export const deletePartyGroupUrl = (data = {}) => {
    return http.post(`${host}/org-group/delete/${data.org_group_id}/${data.org_id}`);
}

//1040020037006-党小组成员-添加党小组成员-查询所有选择成员
export const searchPartyGroupMemberUrl = (data = {}) => {
    return http.get(`${host}/org-group-member/find-by-user?org_id=${data.org_id}&org_type=${data.org_type}&param=${data.param}&page=${data.page}&row=${data.row}`);
}


//1040020037007-党小组成员-添加党小组成员
export const addPartyGroupMemberUrl = (data = {}) => {
    return http.post(`${host}/org-group-member/add`, data);
}


//1040020037008-党小组成员-党小组成员列表
export const PartyGroupMemberListUrl = (data = {}) => {
    return http.get(`${host}/org-group-member/list/${data.org_group_id}`);
}


//1040020037009-党小组成员-删除
export const deletePartyGroupMemberUrl = (data = {}) => {
    return http.post(`${host}/org-group-member/delete/${data.org_group_member_id}`);
}

