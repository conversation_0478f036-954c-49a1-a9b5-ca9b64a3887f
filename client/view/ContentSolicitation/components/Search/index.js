import React from "react";
import { Button, Form, Input, Select, Modal, message } from "antd";
import { delMy } from "apis/content-solicitation";

const { Item } = Form;
const { Option } = Select;
const search = (props) => {
  const {
    id,
    tabkey,
    initData,
    ContentId,
    clearSearch,
    changeSearch,
    resetContentId,
    form: { getFieldDecorator, resetFields, getFieldsValue },
  } = props;

  const onHandleSubmit = () => {
    changeSearch({ ...getFieldsValue() });
  };

  const onHandleReset = () => {
    resetFields();
    clearSearch();
  };

  const onHandleDel = () => {
    if (ContentId.length === 0) {
      message.warning("请选择题目！");
      return;
    }
    Modal.confirm({
      title: "确定删除所选项？",
      confirm: "",
      okText: "是",
      cancelText: "否",
      onOk() {
        delMy({ content_personal_id: ContentId.join(",") }).then(
          ({ data: res }) => {
            if (res.code !== 0) {
              message.error(res.message);
              return;
            }
            message.success("删除成功！");
            initData(1);
            resetContentId();
          }
        );
      },
    });
  };

  return (
    <Form className="search" layout="inline">
      <Item label="知识标题">
        {getFieldDecorator("content_title", {
          initialValue: "",
        })(<Input placeholder="请输入" />)}
      </Item>
      <Item label="提交人">
        {getFieldDecorator("name", {
          initialValue: "",
        })(<Input placeholder="请输入" />)}
      </Item>
      {tabkey === "2" && (
        <Item label={id === "1" ? "初审状态" : "终审状态"}>
          {getFieldDecorator("status", {
            initialValue: undefined,
          })(
            <Select placeholder="请选择">
              <Option value={id === "1" ? 3 : 4}>审核通过</Option>
              <Option value={id === "1" ? 5 : 6}>审核不通过</Option>
            </Select>
          )}
        </Item>
      )}
      <div className="btn">
        {id === "2" && tabkey === "2" && (
          <Button type="primary" onClick={() => onHandleDel()}>
            批量删除
          </Button>
        )}
        <Button type="primary" onClick={() => onHandleSubmit()}>
          查询
        </Button>
        <Button type="default" onClick={() => onHandleReset()}>
          重置
        </Button>
      </div>
    </Form>
  );
};

export default Form.create()(search);
