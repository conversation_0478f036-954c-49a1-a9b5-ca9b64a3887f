.cadre-search {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .card-title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-family: PingFang SC-Heavy, PingFang SC;
    font-weight: 800;
    color: #000000;
    &::before {
      margin-right: 8px;
      content: "";
      display: inline-block;
      width: 8px;
      height: 24px;
      background: url(../../image/left-header-icon.png) center / cover;
    }
  }
  .btn-box {
    display: flex;
    justify-content: center;
    .reset-btn:hover {
      color: #008eff;
      background-color: #fff;
      border-color: #008eff;
    }
    .reset-btn:focus {
      background-color: #fff;
      border-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.88);
      box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
    }
    .ant-btn-primary {
      color: #ffffff;
      background-color: #008eff;
      border-color: #008eff;
    }
    .reset-btn {
      margin-right: 41px;
    }
    button {
      width: 88px;
      height: 32px;
    }
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #008eff;
    border-color: #008eff;
  }
}
