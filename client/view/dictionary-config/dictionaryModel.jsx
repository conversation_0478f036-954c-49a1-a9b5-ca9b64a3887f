import { Button, Icon, Input, message, Modal, Tree } from 'antd';
import { dictionaryAdd, getDictionaryDetail } from 'client/apis/active-organization';
import { useEffect, useState } from 'react';
const { TreeNode } = Tree;

const DictionaryModel = ({ visible, onClose, dictionaryData }) => {
  const [treeData, setTreeData] = useState(null);
  const [editingKey, setEditingKey] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]); // tree 展开节点
  const [checkedKeys, setCheckedKeys] = useState([]); // tree 选中节点
  // 获取字典详情
  const getDictionaryDetailData = async () => {
    try {
      const res = await getDictionaryDetail({ code: dictionaryData.code });
      const { data, status } = res.data;
      if (status === 200) {
        setTreeData(data);
        const initialCheckedKeys = getCheckedKeys(data);
        setCheckedKeys(initialCheckedKeys);
      } else {
        message.error("获取配置列表失败");
      }
    } catch (error) {
      message.error("获取配置列表失败");
    }
  };

  // 组件加载完成后，获取字典详情
  useEffect(() => {
    if (dictionaryData && dictionaryData.code) {
      getDictionaryDetailData();
    }
  }, [dictionaryData]);

  // 查找节点
  const findNodeByKey = (tree, key) => {
    for (const node of tree) {
      if (node.op_key === key || node.tempKey === key) {
        return node;
      }
      if (node.sublevel_list) {
        const result = findNodeByKey(node.sublevel_list, key);
        if (result) return result;
      }
    }
    return null;
  };
  const handleAdd = (parentKey) => {
    // 如果有节点正在编辑，自动保存当前编辑内容
    if (editingKey !== null) {
      handleInputConfirm(editingKey);
    }
  
    const newTreeData = JSON.parse(JSON.stringify(treeData));
    let targetNode = null;
  
    if (!parentKey) {
      newTreeData.unshift({
        code: '',
        op_value: '',
        seq: newTreeData.length + 1,
        has_sublevel: '',
        can_use: 0, // 默认不选中
        sublevel_list: null,
        tempKey: `temp-${Date.now()}`, // 生成唯一的临时 key
      });
    } else {
      targetNode = findNodeByKey(newTreeData, parentKey);
      if (targetNode) {
        if (!targetNode.sublevel_list) {
          targetNode.sublevel_list = [];
        }
        targetNode.sublevel_list.unshift({
          code: '',
          op_value: '',
          seq: targetNode.sublevel_list.length + 1,
          has_sublevel: '',
          can_use: 0, // 默认不选中
          sublevel_list: null,
          tempKey: `temp-${Date.now()}`, // 生成唯一的临时 key
        });
  
        // 展开父节点所在的分支
        setExpandedKeys((prevKeys) => {
          const newKeys = new Set(prevKeys);
          newKeys.add(parentKey); // 添加父节点的 key
          return Array.from(newKeys);
        });
      } else {
        message.error('父节点未找到');
        return;
      }
    }
  
    setTreeData(newTreeData);
    // 设置新的编辑状态
    const newKey = !parentKey ? newTreeData[0].tempKey : targetNode.sublevel_list[0].tempKey;
    setEditingKey(newKey);
    setSearchValue('');
  };
const handleInputConfirm = (key) => {
  if (!searchValue.trim()) {
    message.warning('请输入节点名称');
    return;
  }

  const newTreeData = JSON.parse(JSON.stringify(treeData));
  const targetNode = findNodeByKey(newTreeData, key);

  if (targetNode) {
    targetNode.op_value = searchValue;
    // 如果 tempKey 存在，清除它
    if (targetNode.tempKey) {
      delete targetNode.tempKey;
    }
  }

  setTreeData(newTreeData);
  setSearchValue(''); // 清空输入框
  setEditingKey(null); // 清除编辑状态

};
  
const renderTreeNodes = (data, isTopLevel = true) => { // 添加 isTopLevel 参数，默认为 true
  return data.map((item) => {
    const isEditing = editingKey === (item.tempKey || item.op_key);
    const isLeafNode = !item.sublevel_list || item.sublevel_list.length === 0;

    // 判断是否显示添加按钮
    const canAddChild = isTopLevel; // 只有顶层节点可以添加子节点

    const title = isEditing ? (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Input
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          placeholder="请输入节点名称"
        />
        <Icon
          type="check-circle"
          style={{ marginLeft: 8, color: '#1890ff' }}
          onClick={() => handleInputConfirm(item.tempKey || item.op_key)}
        />
      </div>
    ) : (
      <span>
        {item.op_value || '未命名'}
        {canAddChild && ( // 如果是顶层节点，则显示添加按钮
          <Icon
            type="plus-circle"
            style={{ marginLeft: 8, color: '#1890ff' }}
            onClick={() => handleAdd(item.tempKey || item.op_key)}
          />
        )}
      </span>
    );

    return (
      <TreeNode key={item.tempKey || item.op_key} title={title} isLeaf={isLeafNode}>
        {item.sublevel_list && item.sublevel_list.length > 0 && renderTreeNodes(item.sublevel_list, false)} {/* 子节点的递归调用，isTopLevel 为 false */}
      </TreeNode>
    );
  });
};
  // 查找父节点
  const findParentByKey = (tree, op_key) => {
    for (const node of tree) {
      if (node.sublevel_list) {
        const result = findParentByKey(node.sublevel_list, op_key);
        if (result) return result;
        if (node.sublevel_list.some((child) => child.op_key === op_key)) return node;
      }
    }
    return null;
  };
  // 处理拖拽事件(可以处理同层级拖拽)
const handleDrop = (info) => {
  const dropKey = info.node.props.eventKey;
    const dragKey = info.dragNode.props.eventKey;
    const dropPos = info.node.props.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
  const loop = (data, op_key, callback, level = 0) => {
      for (let i = 0; i < data.length; i++) {
          const item = data[i];
          if (item.op_key === op_key) {
              return callback(item, data, i, level);
          }
          if (item.sublevel_list && item.sublevel_list.length > 0) {
              const result = loop(item.sublevel_list, op_key, callback, level + 1);
              if (result) return result;
          }
      }
      return null;
  };

  const data = JSON.parse(JSON.stringify(treeData || []));

  // 查找拖拽节点
  const dragResult = loop(data, dragKey, (item, arr, index, level) => ({
      item,
      arr,
      index,
      level,
  }));
  if (!dragResult) {
      return;
  }
  const { item: dragItem, arr: dragArr, index: dragIndex, level: dragLevel } = dragResult;

  // 查找目标节点
  const dropResult = loop(data, dropKey, (item, arr, index, level) => ({
      item,
      arr,
      index,
      level,
  }));
  if (!dropResult) {
      console.warn("Drop position is not valid");
      return;
  }

  const { item: dropItem, arr: dropArr, index: dropIndex, level: dropLevel } = dropResult;

  // 检查是否拖放到自身或子节点
  const isSelfOrChild = (node, targetKey) => {
      if (node.op_key === targetKey) return true;
      if (node.sublevel_list) {
          for (const child of node.sublevel_list) {
              if (isSelfOrChild(child, targetKey)) return true;
          }
      }
      return false;
  };
  if (isSelfOrChild(dropItem, dragKey)) {
      message.warning('不能拖放到自身或子节点');
      return;
  }

  // 同层拖拽判断
  if (dragLevel!== dropLevel) {
      message.warning("只能在同一层级拖拽");
      return;
  }

  // 从原位置移除拖拽节点
  dragArr.splice(dragIndex, 1);

  // 插入到目标位置
  dropArr.splice(dropIndex + dropPosition, 0, dragItem);

// 更新 seq 值
const updatedData = (function updateSeq(data, parentSeq = 0) {
  return data.map((node, index) => {
      const updatedNode = {...node };
      // 父项 seq +1
      updatedNode.seq = (parentSeq === 0? 1 : parentSeq) + index;
      if (updatedNode.sublevel_list) {
          updatedNode.sublevel_list = updateSeq(updatedNode.sublevel_list, updatedNode.seq);
      }
      return updatedNode;
  });
})(data);

  // 更新树数据
  setTreeData(updatedData);
};
//过滤数据
const filterTreeData = (data) => {
  return data.reduce((result, item) => {
    // 递归处理子节点
    const filteredChildren = item.sublevel_list ? filterTreeData(item.sublevel_list) : [];
    const isUserModified = (item.op_key && item.op_key.trim() !== "") || (item.op_value && item.op_value.trim() !== "");

    if (filteredChildren.length > 0 || isUserModified) {
      // 移除 tempKey 字段
      const { tempKey, ...rest } = item;
      result.push({
        ...rest,
        sublevel_list: filteredChildren.length > 0 ? filteredChildren : null,
      });
    }

    return result;
  }, []);
};
// 获取选中的节点 key 列表
const getCheckedKeys = (data) => {
  return data.reduce((result, node) => {
    if (node.can_use === 1) {
      result.push(node.op_key || node.tempKey); // 使用 op_key 或 tempKey 作为唯一标识
    }
    if (node.sublevel_list) {
      result.push(...getCheckedKeys(node.sublevel_list));
    }
    return result;
  }, []);
};
// 监听复选框变化
const handleCheck = (checkedKeys, { node, checked }) => {
  const newTreeData = JSON.parse(JSON.stringify(treeData));
  const targetNode = findNodeByKey(newTreeData, node.props.eventKey);

  if (targetNode) {
    targetNode.can_use = checked ? 1 : 0; // 更新 can_use 字段
  }

  setTreeData(newTreeData);
  setCheckedKeys(checkedKeys); // 更新 checkedKeys 状态
};
//点击确定
  const insertDictionary = async () => {
    if (editingKey !== null) {
      message.warning('请完成所有节点的编辑');
      return;
    }
    // 从 treeData 中过滤出用户输入过的内容
    const filteredData = filterTreeData(treeData || []);
    try {
      const res = await dictionaryAdd({
        code: dictionaryData.code,
        data: filteredData,
      });
  
      if (res.data.status === 200) {
        message.success("保存成功");
        onClose();
      } else {
        message.error("保存失败");
      }
    } catch (error) {
      message.error("保存失败");
    }
  };

  return (
    <Modal
      title="干部类别"
      bodyStyle={{ height: '500px', overflowY: 'auto' }}
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={() => insertDictionary()}>
          确定
        </Button>,
      ]}
    >
      <div>
        (操作说明：点击“添加”输入添加的字典项，勾选代表可使用，不使用的选项去掉勾选。可以拖动，进行顺序调整。)
      </div>
         <Button type="primary" onClick={() => handleAdd(null)} icon="file-add">
              添加
            </Button>
            <Tree
            checkable
            draggable
            expandedKeys={expandedKeys}
            onExpand={(keys) => setExpandedKeys(keys)}
            checkedKeys={checkedKeys}
            onCheck={handleCheck}
            defaultSelectedKeys={(treeData || []).map((node) => node.op_key)}
            onDrop={handleDrop}
          >
            {treeData && renderTreeNodes(treeData, true)} {/* 从根节点开始，isTopLevel 为 true */}
          </Tree>
                      </Modal>
            );
};

export default DictionaryModel;