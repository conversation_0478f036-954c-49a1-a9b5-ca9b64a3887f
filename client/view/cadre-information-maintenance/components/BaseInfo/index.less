.base-info-box {
  .avatar-box {
    margin-left: 20px;
    width: 100px;
    position: relative;
    cursor: pointer;
    .title {
      font-size: 18px;
    }

    img {
      margin-top: 10px;
      width: 100px;
      height: 134px;

      transition: 0.1s linear all;
    }
    .upload {
      display: none;
      font-size: 14px;
      position: absolute;
      left: 50%;
      bottom: 20px;
      transform: translate(-50%);
      color: #fff;
      white-space: nowrap;
    }
    &:hover {
      img {
        filter: brightness(0.7);
      }
      .upload {
        display: inline;
      }
    }
  }
}
.place-text-box {
  width: 100px;
}
.flex {
  width: 100%;
  display: flex;
}
.box_gap {
  width: 100%;
  height: 30px;
  &:last-child {
    height: 0px;
  }
}
