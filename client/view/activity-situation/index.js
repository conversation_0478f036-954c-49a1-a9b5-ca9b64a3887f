import React, {Component} from 'react';
import SearchHeader from 'components/search-header';
import {
	Form,
	Input,
	Button,
	Select
} from 'antd'
import {getActivityOrgList} from '../../apis/questionnaire';
import TableView from './table';

const {Option} = Select;
const statusOptions = [
	'全部',
	'未启动',
	'已结束',
	'未开始',
	'进行中'
];

class Class extends Component {
	constructor (props) {
		super(props);
		const {params: {activity_id}} = props.match;
		this.state = {
			params: {
				status: undefined,
				page_no: 1,
				page_size: 5,
				organization_name: '',
				activity_id
			},
			total: 0,
			pages: 0,
			loading: false,
			dataList: []
		}
	}

	componentDidMount() {
		this.onLoadData().catch(e => e);
	}

	/**
	 * @description 搜索
	 */
	onHandleSubmit () {
		const {form: {validateFields}} = this.props;
		const {params} = this.state;
		validateFields((err, {
			status,
			organization_name
		}) => {
			if (!err) {
				this.setState({
					params: {
						...params,
						page: 1,
						status: status ? status : undefined,
						organization_name
					},
					loading: true
				}, () => this.onLoadData().catch(e => e))
			}
		});
	}

	/**
	 * @description 加载数据
	 * @return {Promise<void>}
	 */
	async onLoadData () {
		const {params} = this.state;
		const {
			data: {
				code = 0,
				data = [],
				total = 0,
				pages = 0
			}
		} = await getActivityOrgList({...params});
		code === 0 && this.setState({
			loading: false,
			pages,
			total,
			dataList: data
		});
	}

	render () {
		const {history = {}, form: {getFieldDecorator}, match: {params: {activity_id = ''}}} = this.props;
		const {params} = this.state;
		const {_oid = ''} = (window.sessionStorage || {});
		return (
			<div className="assessment-list">
				<SearchHeader
          onBack={() => history.goBack()}
          title={'互动情况'}
          style={{ marginBottom: 30 }}
        />
				<div className="content">
					<div className="add">
						<Button
							type="primary"
							size="large"
							onClick={() => history.push({
								pathname: `/questionnaire-survey/${activity_id}?is_three=${_oid === '3'}&activity_type=appraise`
							})}
						>互动情况汇总</Button>
					</div>
					<div className="filter">
						<Form
							layout='inline'
							onSubmit={e => {
								e.preventDefault();
								this.onHandleSubmit()
							}}
						>
							<Form.Item label="状态">
								{getFieldDecorator('status', {
									initialValue: 0
								})(
									<Select
										style={{width: 150}}
									>
										{statusOptions.map((item, index) => (
											<Option
												key={index}
												value={index}
											>{item}</Option>
										))}
									</Select>
								)}
							</Form.Item>
							<Form.Item label="组织名称">
								{getFieldDecorator('organization_name')(
									<Input
										type="text"
										placeholder="组织名称"
										style={{width: 150}}
									/>
								)}
							</Form.Item>
							<Form.Item>
								<Button
									type="primary"
									htmlType="submit"
								>查询</Button>
							</Form.Item>
						</Form>
					</div>
					<div className="table">
						<TableView
							{...this.state}
							history={history}
							onChange={val => this.setState({
								params: {
									...params,
									page_no: val
								},
								loading: true
							}, () => this.onLoadData().catch(e => e))}
						/>
					</div>
				</div>
			</div>
		)
	}
}

export default Form.create()(Class);
