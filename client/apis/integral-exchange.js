/*
 * @Description:
 * @Author: baic<PERSON>
 * @Date: 2020-11-19 17:45:04
 * @LastEditTime: 2021-02-02 09:11:17
 * @LastEditors: baichao
 */
import { crHost } from "./config";
import { http } from "tool/axios-mixmall";
import { oId } from "tool/axios";

const mixmallHost = "https://newgate.goodsogood.com/v1.1/mixmall/gs_mall_channel_mk_app";
import axios from "axios";
// 积分兑换功能接口调用

export default crHost;

// 1020201017-积分商城订单明细列表查询
export const fetchOrderDetails = (params = {}) => {
  // params.access_key = accessKey;
  return http.post(`${crHost}/score/mall/orderDetails`, params);
};

// 1020201018-积分商城订单明细列表导出
// export const exportOrderDetails = (params = {}) => {
//   return http.get(`${crHost}/score/mall/orderDetails/downExcel`, params);
// }

// 融合商城-首页导航(首页分类，走node服务)
export const fetchMixmallCommodityClassification = (params = {}) => {
  return axios.post(`${mixmallHost}/app/channelCustomCategory/getCategoryTree?channelId=100&openId=11`, params);
};

// 请求供应商列表
export const fetchSupplier = (params = {}) => {
  return http.get(`${crHost}/score/mall/supplier`, params);
};

// 1020201019-积分商城订单统计查询
export const fetchOrderStatistics = (params = {}) => {
  return http.post(`${crHost}/score/mall/orderStatistics`, params);
};

//10400518-获取积分兑换配置信息
export const getScoreConfig = (params = {}) => {
  return http.get(`${crHost}/score/app/getScoreConfig`, params);
};
//10400509-积分兑换配置
export const setScoreConfig = (params = {}) => {
  return http.post(`${crHost}/score/app/setScoreConfig`, params);
};
