import React, { PureComponent } from 'react';
import { Modal, Table, message } from 'antd';
import { getLeaderSelectList } from "../../apis/leader-group";

import PropTypes from 'prop-types'

/**
 * 选择领导弹窗
 */
export default class LeaderModal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedRowKeys: [],
      selectedRows: [],
      dataList: [],
      loading: false,
    };
    this.columns = [
      {
        title: '领导姓名',
        dataIndex: 'user_name',
        width: "120px"
      },
      {
        title: "所在组织",
        dataIndex: "org_name",
      }
    ]
    this.handleOk = this.handleOk.bind(this);
    this.initSelected = this.initSelected.bind(this);
    this.onSelectChange = this.onSelectChange.bind(this);
    this.getLeaderSelectList = this.getLeaderSelectList.bind(this);
  }
  componentDidMount() {
    this.getLeaderSelectList();
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.org_id !== this.props.org_id) {
      this.getLeaderSelectList(nextProps.org_id)
    }
  }
  getLeaderSelectList(newOrgid) {
    const current_org = window && window.sessionStorage.getItem("_oid") || ""
    const { org_id = current_org } = this.props;
    this.setState({loading: true});
    getLeaderSelectList(newOrgid || org_id).then(res => {
      const { data } = res;
      if (res.status === 200 && data.code === 0) {
        this.setState({
          dataList: data.data
          /* dataList: data.data.map((item) => {
            item.org_id = Number(newOrgid || org_id);
            return item
          }), */
        }, () => {
          this.props.getLearList(this.state.dataList)
        })
      } else {
        message.error(data.message || "网络异常，请稍后再试")
      }
    }).finally(() => {
      this.setState({loading: false})
    })
  }
  initSelected(arr) {
    this.setState({
      selectedRows: arr,
      selectedRowKeys: arr.map((item) => item.user_id)
    })
  }
  onSelectChange(selectedRowKeys, selectedRows ) {
    this.setState({selectedRowKeys, selectedRows })
  }
  handleOk() {
    const { onSure, onCancel } = this.props;
    const { selectedRows } = this.state;
    onSure(selectedRows)
    onCancel()
  }
  render() {
    const { title, visible, onCancel,  } = this.props;
    const { dataList, selectedRowKeys, loading } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    return (
      <Modal
        title={title}
        visible={visible}
        onOk={this.handleOk}
        onCancel={onCancel}
        width="800px"
      >
        <Table bordered pagination={false} rowKey="user_id" loading={loading} rowSelection={rowSelection} dataSource={dataList} columns={this.columns} />
      </Modal>
    )
  }
}
