/**
 * 用于审批选择后的人头展示
 * <AUTHOR>
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Icon } from 'antd';
import './index.less';

/**
 * 人头展示主方法
 * @param { Function }   [onClose]             点击右上角叉叉的回调
 * @param { Any }        [record]              点叉叉的回调参数
 * @param { String }     [name]                显示的人名
 * @param { Object }     [style]               额外外层样式
 * @param { Boolean }    [isAdd]               是否显示添加按钮
 * @param { Function }   [onAdd]               添加按钮被点击时的处理函数
 * @param { Boolean }    [disabled]            是否禁用
 * @param { Boolean }    [hasBottom]           是否显示底部文字
 */
const PeopleCover = ({ hasBottom = true, style, onClose, record = '', name = '', isAdd = false, onAdd, disabled = false }) => {
    // 如果是添加按钮
    if (isAdd) {
        if (disabled) {
            return null
        }
        return (
            <div className="people-cover-add" style={style} onClick={onAdd}>
                <div className="avator-add">
                    <Icon type="plus" />
                </div>
                {hasBottom && <a>添加</a>}
            </div>
        )
    }


    // 白色文字截取最后俩字
    const whiteName = name.substring(name.length - 2, name.length);
    return (
        <div className="people-cover" style={style}>
            <div className="avator">
                <span className="name">{whiteName}</span>
                {
                    !disabled && <div onClick={() => { onClose(record) }} className="close">
                        <Icon type='close' />
                    </div>
                }
            </div>
            {hasBottom && <span className="bottom-name">{name || 'XXX'}</span>}
        </div>
    )
};

// 参数校验 简单用法   官方文档 https://github.com/facebook/prop-types
PeopleCover.propTypes = {
    onClose: PropTypes.func,
    record: PropTypes.any,
    name: PropTypes.string
    // style: PropTypes.object
    // isAdd: PropTypes.boolean
    // onAdd: PropTypes.func
};

export default PeopleCover;
