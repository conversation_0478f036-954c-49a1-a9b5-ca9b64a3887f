import { fetchOrganisitionTree } from 'apis/organisition';
import { fetchDownloadTemplateUrl, importInit, startImport, importSingle, fetchImportDataByPage, polling } from 'apis/batch-addition';
import { fileHost } from 'apis/config';
import { addKeyToTableDataSource } from 'tool/util';

export default {
  namespace: 'batchAddition',

  state: {
    //标记批量开始导入按钮可见状态，也可以用于控制组织树是否可以选择
    batchEnable: false,
    title: '批量新增',
    targetOid: null,
    //暂存组织树对象
    orgTree: [],
    tableList: [],
    //导入失败的数据集合
    failedList: [],
    //准备导入数据的总数统计
    dataCount: 0,
    //导入成功的总数统计
    succeedCount: 0,
    //导入失败的总数统计
    failedCount: 0,
    //解析文件id
    operKey: null,
    //当前上传的页码
    currentHandlePage: 1,

    //以下为分页表格所需要的参数
    //当前页，默认为1
    pageNum: 1,
    //每页数据条数，默认为10
    pageSize: 10,
    //总数据条数
    total: 50,
    //表格加载状态
    tableLoading: true,
    //模板下载地址
    templateUrl: '',
    //导入目标组织
    targetOid: null,
    //控制表格加载中模态框可见
    loadingVisible: false,
    //控制表格加载中模态框提示文字
    loadingTitle: '',

    //表单选择
    current: 1,
    pageSize: 10,
    total: 0

  },

  effects: {
    //获取解析完成待上传分页数据
    * fetchImportDataByPage({ payload }, { put }) {
      // console.log(payload);
      const response = yield fetchImportDataByPage(payload);
      const { data: body } = response;
      const { code, data, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      // console.log(body);
      const { oper_key, data_count, page, page_size, page_count, data_list } = data;
      if (Array.isArray(data_list) && data_list.length !== 0) {
        data_list.map((item, index) => {
          if (!item.state_txt) {
            item.state_txt = '准备导入';
          }
          if (!item.error_msg) {
            item.error_msg = '';
          }
          //初始化记录编辑状态
          item.isEdit = false;
          return item;
        });
      }
      addKeyToTableDataSource(data_list);
      //获取分页数据时，加载表格数据并记录分页数据的总共条数
      yield put({
        type: 'save',
        payload: {
          // total: data_count || 0,
          // dataCount: data_count || 0,
          tableList: data_list || []
        }
      });
    },
    //获取组织树
    * fetchOrganisitionTree({ payload }, { put }) {
      const { oid } = payload;
      const response = yield fetchOrganisitionTree({ org_id: oid });
      const { data: body } = response;
      const { code, data, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      yield put({
        type: 'save',
        payload: {
          orgTree: data
        }
      });
    },
    //获取下载模板链接
    * fetchDownloadTemplateUrl({ payload }, { put }) {
      const response = yield fetchDownloadTemplateUrl();
      console.log(response);
      const { data: body } = response;
      const { code, data, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      yield put({
        type: 'save',
        payload: {
          templateUrl: `/file/file/download/${data}`
        }
      });
      // return `${fileHost}/file/download/${data}`;
    },
    //初始化准备导入
    *importInit({ payload }, { put }) {
      const { fileId } = payload;
      const response = yield importInit(fileId);
      // console.log(response);
      const { data: body } = response;
      const { code, data, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      //上传文件拿到result_key，通过接口轮询，询问接口解析处理是否完毕
      const { result_key } = data;

      return result_key;
    },

    //轮询数据解析是否完成
    *pollingOperKey({ payload }, { put }) {
      // console.log('轮询');
      const { result_key } = payload;
      const response = yield polling(result_key);
      const { data: body } = response;
      const { data, code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      const { status, oper_key, data_count, message: msg } = data;
      if (status === -1) {
        yield put({
          type: 'save',
          payload: {
            loadingTitle: '',
            loadingVisible: false
          }
        });
        throw new Error(msg);
      } else if (status === 1) {
        // 当轮询结果为已经解析完成时，不保存当前解析得出的结果条数，等到拉取第一页分页数据时，再加载数据导入数量
        yield put({
          type: 'save',
          payload: {
            loadingTitle: '',
            loadingVisible: false,
            total: data_count || 0,
            dataCount: data_count || 0,
            operKey: oper_key,
            succeedCount: 0,
            failedCount: 0
          }
        });
      }
      return data;
    },

    // 导入过程中，如果异常中断
    *retransmission({ payload }, { put, select }) {
      const { operKey, targetOid } = payload;
      const state = yield select(state => state.batchAddition);
      const { total, pageSize } = state;
      let { succeedCount, failedCount, currentHandlePage, failedList } = state;
      console.log('继续传', currentHandlePage, operKey, targetOid);

      let response,
        body,
        code, data, message,
        data_list, succeed_count, failed_count;

      while (currentHandlePage <= Math.ceil(total / pageSize)) {
        yield put({
          type: 'save',
          payload: {
            loadingVisible: true,
            loadingTitle: `正在导入中，已完成【${(currentHandlePage - 1) * pageSize}/${total}】，请稍候，请不要刷新页面...`
          },
        });
        response = yield startImport(operKey, targetOid, currentHandlePage, pageSize);
        body = response.data;
        code = body.code;
        data = body.data;
        message = body.message || '';
        data_list = Array.isArray(data.data_list) ? data.data_list : [];
        succeed_count = data.succeed_count || 0;
        failed_count = data.failed_count || 0;

        failedCount += failed_count;
        succeedCount += succeed_count;
        failedList = Array.isArray(failedList) ? failedList.concat(data_list) : [].concat(data_list);

        if (code !== 0) {
          //循环处理发生异常
          //展现当前已经处理完成的失败结果，并抛出异常中断处理
          // addKeyToTableDataSource(failedList);
          yield put({
            type: 'save',
            payload: {
              loadingTitle: '',
              loadingVisible: false,
              tableList: failedList,
              currentHandlePage,
              succeedCount: succeedCount,
              failedCount: failedCount,
            }
          });
          throw new Error(message);
        }
        // console.log('当前处理===>', currentHandlePage);
        currentHandlePage++;
      }
      yield put({
        type: 'save',
        payload: {
          loadingTitle: `正在导入中，已完成【${total}/${total}】，请稍候，请不要刷新页面...`
        }
      });

      //处理完毕
      addKeyToTableDataSource(failedList);
      yield put({
        type: 'save',
        payload: {
          tableList: failedList,
          succeedCount: succeedCount,
          failedCount: failedCount,
        }
      });
      //返回处理结果，用于页面展示提示消息
      return {
        succeedCount: succeedCount,
        failedCount: failedCount
      }
    },
    //开始导入
    * startImport({ payload }, { put, select }) {
      const { operKey, targetOid } = payload;
      const state = yield select(state => state.batchAddition);
      const { total, pageSize } = state;
      let { succeedCount, failedCount, currentHandlePage, failedList } = state;

      let response,
        body,
        code, data, message,
        data_list, succeed_count, failed_count;

      try {
        while (currentHandlePage <= Math.ceil(total / pageSize)) {
          yield put({
            type: 'save',
            payload: {
              currentHandlePage,
              loadingVisible: true,
              loadingTitle: `正在导入中，已完成【${(currentHandlePage - 1) * pageSize}/${total}】，请稍候，请不要刷新页面...`
            },
          });
          response = yield startImport(operKey, targetOid, currentHandlePage, pageSize);

          body = response.data;
          code = body.code;
          data = body.data;
          message = body.message || '';
          data_list = Array.isArray(data.data_list) ? data.data_list : [];
          succeed_count = data.succeed_count || 0;
          failed_count = data.failed_count || 0;

          failedCount += failed_count;
          succeedCount += succeed_count;
          failedList = Array.isArray(failedList) ? failedList.concat(data_list) : [].concat(data_list);

          if (code !== 0) {
            //循环处理发生异常
            //展现当前已经处理完成的失败结果，并抛出异常中断处理
            // addKeyToTableDataSource(failedList);
            yield put({
              type: 'save',
              payload: {
                loadingTitle: '',
                loadingVisible: false,
                // tableList: failedList,
                // currentHandlePage,
                // succeedCount: succeedCount,
                // failedCount: failedCount,
              }
            });
            throw new Error(message);
          }
          // console.log('当前处理===>', currentHandlePage);
          currentHandlePage++;
        }
      } catch (error) {
        console.log(error);
      }
      yield put({
        type: 'save',
        payload: {
          loadingTitle: `正在导入中，已完成【${total}/${total}】，请稍候，请不要刷新页面...`
        }
      });

      //处理完毕
      addKeyToTableDataSource(failedList);
      yield put({
        type: 'save',
        payload: {
          tableList: failedList,
          succeedCount: succeedCount,
          failedCount: failedCount,
        }
      });
      //返回处理结果，用于页面展示提示消息
      return {
        succeedCount: succeedCount,
        failedCount: failedCount
      }
    },
    //编辑提交单条错误记录
    * editTable({ payload }, { put }) {
      // console.log('编辑单条', payload);
      // return;
      const response = yield importSingle(payload);
      // console.log(response);
      const { data: body } = response;
      const { code, data, message } = body;
      // console.log(body, data, code);
      if (code !== 0) {
        throw new Error(message);
      }
      // console.log(data);
      return payload;
    }
  },

  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    toggleTableStatus(state, { data, isEdit = false }) {
      let tableList = state.tableList;
      const index = tableList.indexOf(data);

      if (isEdit) {
        tableList = tableList.map((val, i) => {
          if (i == index) {
            val.isEdit = true;
          } else {
            val.isEdit = false;
          }
        });
      } else {
        tableList[index].isEdit = false;
      }
      return { ...state };
    }
    // editTable(state, { payload }) {
    //   const { name, phone, certNumber, data } = payload;
    //   const tableList = state.tableList;
    //   const index = tableList.indexOf(data);
    //   const currentData = tableList[index];

    //   currentData.name = name;
    //   currentData.phone = phone;
    //   currentData.id = card;
    //   currentData.isEdit = false;

    //   return { ...state };
    // },
  },
};
