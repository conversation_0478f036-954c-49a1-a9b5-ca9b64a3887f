import { useState, useEffect } from "react";
import { Divider } from "antd";
import { SettleInFlowPath } from "apis/organize";
import SectionTitle from "components/activity-form/sub-components/SectionTitle";

const index = (props) => {
  const [data, setData] = useState([]);
  useEffect(() => {
    onFinish()
  }, [])

  const onFinish = async (values) => {
    const params = {
      ids: props.state.workflow_task_id,
      org_expand_id: props.state.org_id,
    }
    const { data } = await SettleInFlowPath(params);
    if (data.code == 0) {
      setData(data.data)
    }
  };

  const renderStep = (data) => {
    //data1审核通过 2审核不通过 4待审核 5已撤回
    console.log('data123', data[0]);
    return (
      <div>
        {data[0]!==undefined&&data[data.length-1].action !== 5 ? <div className={`step-item step-item-1`}>
          <div className="step-item-icon" />
          <div className="step-item-content">
            <div className="step-item-content-header">
              <span>入驻申请</span>
              {/* <span>{status==1?'待审核':status==2?'审核通过':status==3?'已撤回':'审核不通过'}</span> */}
              <span className="tipTime">{data[0]!==undefined&&data[0].apply_for_time}</span>
            </div>
            <div className="step-item-content-opinion">{data[0]!==undefined&&data[0].user_name} - {data[0]!==undefined&&data[0].user_phone}</div>
          </div>
        </div> :
          <div className={`step-item step-item-5`}>
            <div className="step-item-icon" />
            <div className="step-item-content">
              <div className="step-item-content-header">
                <span>入驻申请</span>
                <span className="tip">{'已撤回'}</span>
                <span className="tipTime">{data[0]!==undefined&&data[0].apply_for_time}</span>
              </div>
              <div className="step-item-content-opinion">{data[0]!==undefined&&data[0].user_name} - {data[0]!==undefined&&data[0].user_phone}</div>
            </div>
          </div>
        }

        {data[data.length-1]!==undefined&&data[data.length-1].action == 5 ? <div className={`step-item step-item-4`}>
          <div className="step-item-icon" />
          <div className="step-item-content">
            <div className="step-item-content-header">
              <span>挂靠组织审核</span>
              <span className="tip">{'待审核'}</span>
              <span className="tipTime">{data[data.length-1]!==undefined&&data[data.length-1].create_time}</span>
            </div>
            <div className="step-item-content-opinion">{data[data.length-1]!==undefined&&data[data.length-1].say}</div>
          </div>
        </div> : <div className={`step-item step-item-${data[data.length-1]!==undefined&&data[data.length-1].action}`}>
          <div className="step-item-icon" />
          <div className="step-item-content">
            <div className="step-item-content-header">
              <span>挂靠组织审核</span>
              <span className="tip">{data[data.length-1]!==undefined&&data[data.length-1].action == 4 ? '待审核' : data[data.length-1]!==undefined&&data[data.length-1].action == 1 ? '审核通过' : data[data.length-1]!==undefined&&data[data.length-1].action == 5 ? '已撤回' : '不通过'}</span>
              <span className="tipTime">{data[data.length-1]!==undefined&&data[data.length-1].create_time}</span>
            </div>
            <div className="step-item-content-opinion">{data[data.length-1]!==undefined&&data[data.length-1].say}</div>
          </div>
        </div>}
      </div>
    );
  };

  return (
    <div className="CheckInAuditDetail-Flow">
      {/* <Divider orientation="left" orientationMargin="0" dashed>
        <Divider type="vertical" className="title-vertical" />
        流程跟踪
      </Divider> */}
      <SectionTitle title="流程跟踪" />
      <div className="CheckInAuditDetail-Flow-Step">
          <div>{renderStep(data)}</div>
      </div>
    </div>
  );
};

export default index;
