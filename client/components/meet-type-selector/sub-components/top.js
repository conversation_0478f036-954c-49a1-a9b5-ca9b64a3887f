import React from "react";
import { Form, Select, Button } from "antd";

const FormItem = Form.Item;
const Option = Select.Option;

export default Form.create()(
  (props) => {
    const {
      form,
      typeList = [],
      categoryList = [],
      onChange,
      categoryChangeHandler
    } = props;
    const { getFieldDecorator, validateFields, resetFields } = form;
    return (
      <div className="top-container">
        <Form layout="inline" onSubmit={(e) => {
          e.preventDefault();
          validateFields((errors, values) => {
            if (!errors) {
              onChange && onChange(values);
            }
          });
        }}>
          <FormItem label="所属类别">
            {
              getFieldDecorator("category", {
                initialValue: -1
              })(
                <Select placeholder="请选择" style={{ width: 150 }} onChange={(value) => {
                  resetFields(["type"]);
                  categoryChangeHandler && categoryChangeHandler(value);
                }}>
                  <Option value={-1}>全部</Option>
                  {
                    categoryList && categoryList.map((item, index) => {
                      return (
                        <Option key={index} value={item.category_id}>
                          {item.category}
                        </Option>
                      )
                    })
                  }
                </Select>
              )
            }
          </FormItem>
          <FormItem label="活动类型">
            {
              getFieldDecorator("type", {
                initialValue: -1
              })(
                <Select placeholder="请选择" style={{ width: 150 }}>
                  <Option value={-1}>全部</Option>
                  {
                    typeList && typeList.map((item, index) => {
                      return (
                        <Option key={index} value={item.type_id}>
                          {item.type}
                        </Option>
                      )
                    })
                  }
                </Select>
              )
            }
          </FormItem>
          <FormItem>
            <Button className="query-btn" type="primary" htmlType="submit">查  询</Button>
          </FormItem>
        </Form>
      </div >
    )
  }
);