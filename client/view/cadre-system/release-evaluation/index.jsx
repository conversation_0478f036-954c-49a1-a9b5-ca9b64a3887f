import React, { useState, useMemo, useEffect, useRef } from "react";
import "./index.less";
import SearchHeader from "client/components/search-header";
import { Form, Icon, Input, Button, DatePicker, message } from "antd";
import SelectTable from "./components/select-table";
import moment from "moment";
import {
  findAppraisalUser,
  initiateEvaluation,
  getEvalDetail,
} from "client/apis/cadre-portrait";
/**
 * @description: 发布测评
 * @return {*}
 */
const index = ({ history, location }) => {
  const query = new URLSearchParams(location.search);

  const org_id = query.get("org_id");
  const evaluation_id = query.get("evaluation_id");
  const [pattern_sub, setPatternSub] = useState(query.get("pattern_sub"));

  const formInputRef = useRef(null);

  const [delEvaluationUserIds, setDelEvaluationUserIds] = useState([]); // 被删除的测评用户id数组

  const [formState, setFormState] = useState({ test_name: "", time: "" });

  const [dataSource, setDataSource] = useState([]);

  /**
   * @description: 副作用
   * @return {*}
   */
  useEffect(() => {
    if (evaluation_id) {
      loadDetail();
    } else {
      loadData();
    }
  }, []);
  /**
   * @description: 加载数据
   * @return {*}
   */
  const loadData = () => {
    findAppraisalUser({ org_id }).then((res) => {
      if (res.data.code === 0) {
        setDataSource(res.data.data);
      } else {
        message.error(res.data.message);
      }
    });
  };
  /**
   * @description: 获取详情
   * @return {*}
   */
  const loadDetail = () => {
    getEvalDetail({ evaluation_id }).then((res) => {
      if (res.data.code === 0) {
        const { end_time, eval_name, org_id, pattern_sub, org_user } =
          res.data.data;

        setPatternSub(pattern_sub);

        formInputRef.current.setFieldsValue({
          eval_name,
          end_time: moment(end_time),
        });
        console.log("🚀 ~ getEvalDetail ~ org_user:", org_user);
        org_user.forEach((item) => {
          item.checked = true;
        });
        setDataSource(org_user);
      }
    });
  };
  // 返回
  const handleBack = () => {
    history.goBack();
  };

  // 测试名输入
  const onDateChange = (value, dateString) => {
    console.log("Selected Time: ", value);
    console.log("Formatted Selected Time: ", dateString);
  };

  const onDateOk = (value) => {
    console.log("onOk: ", value);
  };
  /**
   * @description: 复选框
   * @param {*} org_id
   * @param {*} checked
   * @return {*}
   */
  const onCheckChange = ({ org_id, checked }) => {
    dataSource.forEach((item) => {
      if (item.org_id === org_id) {
        item.checked = checked;
        // 编辑时，取消勾选，删除用户
      }
    });

    setDataSource([...dataSource]);
  };

  const onSelectChange = ({ org_id, user_list, group }) => {
    const findIndex = dataSource.findIndex((item) => item.org_id === org_id);

    if (findIndex !== -1) {
      dataSource[findIndex].user_list = user_list;
      dataSource[findIndex].group = group;
      setDataSource([...dataSource]);
    }
  };
  /**
   * @description: 发布
   * @return {*}
   */
  const onSubmit = () => {
    /**
    evaluation_id	number	否	测评表id
    end_time	string	是	截至时间
    eval_name	string	是	测评名称
    pattern_sub	number	是	组织等级标识: 1:乡镇 2:一级部门 3:二级部门
    org_id	number	是	发起测评组织id
    user_list	list-UserInfo	是	测评用户
    del_evaluation_user_ids	list-number	否	被删除的测评用户id数组
   */

    const { getFieldsValue } = formInputRef.current;

    // 基础信息
    const evaBaseInfo = getFieldsValue();

    if (!evaBaseInfo.eval_name) {
      message.error("请输入测评名称");
      return;
    }

    if (!evaBaseInfo.end_time) {
      message.error("请选择测评截至时间");
      return;
    }
    // datasource下的user_list扁平化
    const user_list = dataSource
      .filter((org) => {
        // 1:乡镇 2:一级部门 3:二级部门
        return org.checked;
      })
      .reduce((acc, cur) => {
        // 给每一个用户添加org_id
        const _ = cur.user_list.map((item) => ({
          ...item,
          org_id: cur.org_id,
          group: cur.group || "1",
        }));
        return [...acc, ..._];
      }, []);
    // 编辑时，取消勾选，删除用户
    const deleteList = [...delEvaluationUserIds];

    if (evaluation_id) {
      // 过滤未选中的
      dataSource
        .filter((item) => !item.checked)
        .forEach((item) => {
          item.user_list.forEach((user) => {
            deleteList.push(user.user_id || user.evaluation_user_id);
          });
        });
    }

    const params = {
      eval_name: evaBaseInfo.eval_name,
      end_time: moment(evaBaseInfo.end_time).format("YYYY-MM-DD HH:mm:ss"),
      evaluation_id,
      pattern_sub: dataSource[0].pattern_sub,
      org_id,
      user_list,
      del_evaluation_user_ids: deleteList,
    };

    initiateEvaluation(params).then((res) => {
      if (res.data.code === 0) {
        message.success("发布成功");

        history.goBack();
      } else {
        message.error(res.data.message);
      }
    });
  };

  /**
   * @description: 更新数据
   * @param {*} data
   * @return {*}
   */
  const onUpdateData = ({ org_id, user_list }) => {
    const findIndex = dataSource.findIndex((item) => item.org_id === org_id);

    if (findIndex !== -1) {
      dataSource[findIndex].user_list = user_list;

      setDataSource([...dataSource]);
    }
  };
  // 删除
  const onDelete = (user_id) => {
    setDelEvaluationUserIds([...delEvaluationUserIds, user_id]);
  };

  const FormCustom = ({ form }) => {
    const { getFieldDecorator } = form;
    return (
      <Form layout="inline">
        <Form.Item label="测评名称">
          {getFieldDecorator("eval_name", {
            rules: [{ required: true, message: "请输入测评名称" }],
          })(<Input placeholder="请输入" style={{ width: "300px" }} />)}
        </Form.Item>
        <Form.Item label="测评截至时间">
          {getFieldDecorator("end_time", {
            rules: [{ required: true, message: "请选择" }],
          })(
            <DatePicker
              style={{ width: "300px" }}
              showTime={false}
              placeholder="请选择"
              onChange={onDateChange}
              onOk={onDateOk}
            />
          )}
        </Form.Item>
      </Form>
    );
  };

  // 测试相关信息
  const FormInput = useMemo(() => Form.create()(FormCustom), []);

  return (
    <div className="release-evaluation">
      <SearchHeader
        onBack={handleBack}
        title={evaluation_id ? "编辑测评" : "发布测评"}
      />
      <div className="evaluation-box">
        <FormInput ref={formInputRef} />
        <div className="table-list-box">
          {dataSource.map(({ user_list, ...item }, index) => {
            return (
              <SelectTable
                {...item}
                key={index}
                dataSource={user_list}
                onDelete={onDelete}
                onUpdateData={onUpdateData}
                onCheckChange={onCheckChange}
                onSelectChange={onSelectChange}
              />
            );
          })}
        </div>
      </div>
      <div className="button-box">
        <Button
          type="primary"
          className="button"
          size="large"
          onClick={onSubmit}
        >
          发布
        </Button>
      </div>
    </div>
  );
};

export default index;
