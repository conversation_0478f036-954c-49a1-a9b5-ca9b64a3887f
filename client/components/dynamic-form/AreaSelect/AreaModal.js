import { getAreaData } from "client/apis/organize";

const AreaModal = {
  namespace: "AreaModal",
  state: {
    loading: false,
    data: [],
  },
  reducers: {
    updateData(state, { payload }) {
      return { ...state, data: payload };
    },
    updateStatus(state, { payload }) {
      return { ...state, dataStatus: payload };
    },
  },
  effects: {
    *initData({ payload }, { put, call, select }) {
      const { loading, data } = yield select((state) => state.AreaModal);
      if (loading) {
        return;
      }
      if (data.length > 0) {
        return;
      }
      yield put({ type: "updateStatus", payload: true });
      try {
        const list = yield call(initArea);
        yield put({ type: "updateData", payload: list });
      } catch (error) {
        console.error(error);
      }
      yield put({ type: "updateStatus", payload: false });
    },
    * updateStateData({payload = {}}, {put, select, call}) {
        const { data } = payload;
        yield put({type: 'updateData', payload: data})
    },
  },
};

// const initArea = () => {
//   return new Promise(async (resolve, reject) => {
//     let result = []
//     let { data } = await getArea()
//     if(data.code === 0) {
//       for(let i = 0; i < data.data.length; i++) {
//         let level1List = data.data[i]
//         let params2 = {
//           pid: level1List.adcode
//         }
//         let { data: level2Data } = await getArea(params2)
//         if(level2Data.code === 0) {
//           level1List.label = level1List.area_name
//           level1List.value = String(level1List.adcode)
//           level1List.children = level2Data.data

//           for(let j = 0; j < level2Data.data.length; j++) {
//             let level2List = level2Data.data[j]
//             level2List.label = level2List.area_name
//             level2List.value = String(level2List.adcode)
//             let params3 = {
//               pid: level2List.adcode
//             }
//             let { data: level3Data } = await getArea(params3)
//             if(level3Data.code === 0) {
//               level2List.label = level3Data.area_name
//               level2List.value = String(level3Data.adcode)
//               for(let k = 0; k < level3Data.length; k ++) {
//                 let level3List = level3Data.data[k]
//                 level3List.label =  level3List.area_name
//                 level3List.value =  String(level2List.adcode)
//               }
//               level2List.children = level3Data.data
//               if(i === (data.data.length - 1) && j === (level2Data.data.length - 1)) {
//                 console.log(data.data)
//                 resolve(data.data)
//               }
//             } else {
//               reject(level3Data.message)
//             }
//           }
//         } else {
//           reject(level2Data.message)
//         }
//       }
//     } else {
//       reject(data.message)
//     }
//     resolve(result)
//   })
// }

const initArea = () => {
  return new Promise(async (resolve, reject) => {
    let result = [];
    let { data } = await getAreaData();
    console.log(data)
    if (data.code === 0) {
      resolve(data.data)
    } else {
      reject(data.message);
    }
    resolve(result);
  });
};



export default AreaModal;
