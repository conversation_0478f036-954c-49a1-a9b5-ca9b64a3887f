import React from "react";
import emuns from "config/emuns";
import formFields from "config/formFields";
import propTypes from "prop-types";
import { Form } from "antd";
import {
  ActivityRules, //活动规则
} from "components/activity-form"; //活动表单

class Rules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { form, isPreview, state, updateState, isContribute } = this.props;
    // console.log(state);
    return (
      <ActivityRules
        form={form}
        preview={isPreview}
        emuns={emuns}
        formFields={formFields}
        activityFormState={state}
        isContribute={isContribute}
      />
    )
  }
}

Rules.propTypes = {
  form: propTypes.object,
  isPreview: propTypes.bool,
  state: propTypes.object,
  updateState: propTypes.func,
  isContribute: propTypes.bool
}

Rules.defaultProps = {
  form: {},
  isPreview: false,
  state: {},
  updateState: () => { },
  isContribute: false
}

export default Form.create({
  onFieldsChange(props, changedFields) {
    const { updateState } = props;
    let result = {};
    for (let key in changedFields) {
      result[key] = changedFields[key].value;
    }
    // console.log(result);
    updateState(result);
  },
  mapPropsToFields: (props) => {
    const { state } = props;
    const result = {};
    for (let key in formFields) {
      if (key === "joinModel" ||
        key === "accountRestrictions" ||
        key === "ipRestrictions" ||
        key === "deviceRestrictions") {
        result[formFields[key]] = Form.createFormField({
          name: formFields[key],
          value: state[key]
        });
      } else {
        result[formFields[key]] = Form.createFormField({
          name: formFields[key],
          value: state[formFields[key]]
        });
      }
    }
    return result;
  }
})(Rules);

