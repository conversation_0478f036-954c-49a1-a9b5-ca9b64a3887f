import { message as Message } from 'antd';
import { getData, editData, topicList } from 'client/apis/baweiyuzhen';

export default {
  namespace: 'baweiyuzhen',

  state: {
    formData: {
      // 顶部banner
      top_banner: {},
      // 公告栏
      bulletin_board: [],
      // 置顶商品
      top_commodity: [],
      // 公益宝贝
      charitys: [],
      // N	公益宝贝版头
      charitys_head: {},
      // N	公益宝贝版末
      charitys_bottom: {},
      // 本期推荐 商品列表
      commodity_list: [],
      // N	商品列表版头
      commodity_list_head: {},
      // N	商品列表版末
      commodity_list_bottom: {},
      topic_list: []
    },
    status: {
      /**手机预览 */
      mobilePreview: false,
      /**置顶商品modal */
      top_commodity: {},
      /**公益宝贝modal */
      charitys: {},
      /**本期推荐modal */
      commodity_list: {},
      /**公告栏 */
      bulletin_board: {},
    },
    video_name: '',
  },

  effects: {
    *getInitData({ payload = {} }, { select, put, call }) {
      const { data_type = 2 } = payload;
      const response = yield getData({ data_type });
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      const res = (yield topicList()).data
      if (res.code !== 0) {
        throw new Error(res.message);
      }
      body.data.topic_list = res.data
      yield put({ type: 'updateForm', payload: body.data });
      return body.data;
    },
    *submit({ payload = {} }, { select, put, call }) {
      const response = yield editData(payload);
      const { data: body } = response;
      const { code, message } = body;
      if (code !== 0) {
        throw new Error(message);
      }
      yield put({ type: 'updateForm', payload: payload });
      return body.data;
    }
  },
  reducers: {
    update(state, { payload }) {
      return { ...state, ...payload };
    },
    updateForm(state, { payload }) {
      let { commodity_list } = state.formData;
      if (payload.commodity_list) {
        commodity_list = payload.commodity_list.map((item) => {
          if (typeof item.price === 'number') {
            item.price = item.price.toFixed(2);
          }
          return item;
        });
      }
      return { ...state, formData: { ...state.formData, ...payload, commodity_list } };
    },
    updateStatus(state, { payload }) {
      return { ...state, status: { ...state.status, ...payload } };
    },
    submitModal(state, { payload }) {
      const { type, data } = payload;
      const list = [...state.formData[type]];
      let { index } = state.status[type];
      if (index === undefined) {
        index = list.length;
      }
      list[index] = data;
      const stateData = {
        [type]: list,
      };
      return { ...state, formData: { ...state.formData, ...stateData } };
    },
  },
};
