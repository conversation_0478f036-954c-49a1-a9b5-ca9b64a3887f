import React, { useState, useEffect, useRef, useMemo } from "react";
import SearchHeader from "components/search-header";
import {
  Button,
  Form,
  Input,
  Radio,
  TreeSelect,
  Checkbox,
  Select,
  message,
  Icon,
} from "antd";
import Editor from "client/components/RichTextEditor";
import InputTips from "components/activity-form/sub-components/InputTips";
import OrganizeModal from "components/organize-selector/sub-components/organize-modal";
import Image from "../../components/image";
import { uploadBase64File } from "apis/file";
import { addResource, editResource, findById } from "apis/resource-library";
import PreviewModal from "./components/previewModal";
import ShowUploadFileType from "../../components/showUploadFileType";
import FileUpload from "components/file-upload";
import "./index.less";

/**
 * 上传文件库、案例库资源
 * path: /case-manage/add
 *       /document-manage/add
 */

const { Item } = Form;
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 10 },
};
const documentTagsList = [
  { label: "中央文件", value: "中央文件" },
  { label: "市委文件", value: "市委文件" },
  { label: "工委文件", value: "工委文件" },
  { label: "部门文件", value: "部门文件" },
];
const caseTagsList = [
  { label: "主题党日", value: "主题党日" },
  { label: "支部活动", value: "支部活动" },
  { label: "评比表彰", value: "评比表彰" },
];
const seq_number = [...Array(100)].map((_, index) => index + 1);
const methods = {
  1: addResource,
  2: editResource,
};
const { name, oid, root_oid } = JSON.parse(
  sessionStorage.getItem("userInfo") || {}
);
const AddCase = (props) => {
  const {
    history,
    form: {
      getFieldDecorator,
      getFieldValue,
      setFieldsValue,
      validateFieldsAndScroll,
    },
    location: {
      state: {
        type, // 1 新增 2编辑
        categoryList,
        resource_library_id,
        resource_type, // 1文件库 3案例库
      },
    },
  } = props;

  const editor = useRef(null);
  const [img, setImg] = useState([]);
  const [files, setFiles] = useState([]);
  const [content, setContent] = useState(null);
  const [selOrg, setSelOrg] = useState(
    oid === root_oid
      ? []
      : [
          {
            org_name: name,
            org_id: oid,
          },
        ]
  );
  const [dataSource, setDataSource] = useState({});
  const [catagoryId, setCategoryId] = useState(null);
  const [orgModalVisible, setOrgModalVisible] = useState(false);
  const [previewModalData, setPreviewModalData] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);

  useEffect(() => {
    if (resource_library_id) {
      findById({ resourceLibraryId: resource_library_id }).then(
        ({ data: res }) => {
          if (res.code !== 0) {
            message.error(res.message);
            return;
          }
          setCategoryId(res.data.resource_category_id);
          setSelOrg([
            {
              org_id: res.data.provider_org_id,
              org_name: res.data.provider_org_name,
            },
          ]);
          setContent(res.data.content);
          setFiles(res.data.resource_files);
          editor.current.setEditorContent(res.data.content);
          setImg({ path: res.data.cover_url });
          setDataSource(res.data);
        }
      );
    }
  }, []);

  const onHandleSubmit = (isPreview = false) => {
    validateFieldsAndScroll((err, res) => {
      if (content === "<p><br></p>") {
        message.warn(`请输入${resource_type === 1 ? "内容" : "案例正文"}`);
        return;
      }
      if (!err) {
        const { name, tags, sources, sort, seq, show_status, summary } = res;
        let params = {
          name,
          sources,
          show_status,
          summary,
          content,
          resource_category_id: catagoryId,
          cover_url: img && img.path ? img.path : null,
          provider_org_id: selOrg[0].org_id,
          provider_org_name: selOrg[0].org_name,
          tags: tags && tags.length ? tags.join(",") : null,
          resource_files: files && files.length ? files : [],
          resource_type: resource_type ? resource_type : 1,
        };
        if (sort === 2) {
          params.seq = seq;
        }
        if (resource_library_id) {
          params.resource_library_id = resource_library_id;
          params.create_time = dataSource.create_time
        }
        if (isPreview) {
          setPreviewModalData(params);
          setPreviewModalVisible(true);
        } else {
          methods[type](params).then(({ data: res }) => {
            if (res.code !== 0) {
              message.error(res.message);
              return;
            }
            message.success(`${type == 1 ? "新增" : "编辑"}成功`);
            history.goBack();
          });
        }
      }
    });
  };

  const checkSelect = (rule, value) => {
    return new Promise((resolve, reject) => {
      if (value && value.length > 3) {
        reject("标签最多选中3个");
      } else {
        resolve();
      }
    });
  };

  const clipEvent = () => {
    const upload = (base64) => {
      return new Promise((resolve, reject) => {
        uploadBase64File({
          upfile: base64,
          upType: "image",
        })
          .then(({ data: res }) => {
            if (res.code != 0) {
              message.error(res.message);
              reject("");
              return;
            } else {
              setImg(res.data);
              resolve();
            }
          })
          .catch((e) => j(e));
      });
    };
    return { upload };
  };

  const previewModalProps = {
    previewModalData,
    visible: previewModalVisible,
    onCancel: () => setPreviewModalVisible(false),
  };

  const orgModalProps = {
    visible: orgModalVisible,
    dataSource: selOrg,
    hideModal: () => setOrgModalVisible(false),
    radio: true,
    hasRoot: false,
    onlyDirectSub: true,
    rootOrgId: oid,
    loadOrganizeData: (data) => {
      setOrgModalVisible(false);
      setSelOrg(data);
      setFieldsValue({ org_name: data && data.length > 0 ? data[0].org_name : ''  });
    },
  };

  const PreviewModalMemo = useMemo(() => {
    return <PreviewModal {...previewModalProps} />;
  }, [previewModalProps]);

  const newClass =
    resource_type === 1
      ? { width: "156px", height: "156px" }
      : { width: "268px", height: "156px" };
  return (
    <div className="add-case">
      <SearchHeader
        title={`${type == 1 ? "上传" : "编辑"}资源`}
        onBack={() => history.goBack()}
      />
      <Form className="add-case-form" {...formItemLayout}>
        <Item label={`${resource_type === 1 ? "文件" : "案例"}分类`} required>
          {getFieldDecorator("resource_category_id", {
            initialValue: catagoryId,
            rules: [
              {
                required: true,
                message: `请选择${resource_type === 1 ? "文件" : "案例"}分类`,
              },
            ],
          })(
            <TreeSelect
              style={{ width: "100%" }}
              dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
              treeData={categoryList}
              placeholder={`请选择${resource_type === 1 ? "文件" : "案例"}分类`}
              treeDefaultExpandAll
              onChange={(val) => setCategoryId(val)}
            />
          )}
        </Item>

        <Item label="标题" required>
          {getFieldDecorator("name", {
            initialValue: dataSource.name ? dataSource.name : "",
            rules: [
              {
                required: true,
                message: "请输入标题",
              },
            ],
          })(<Input allowClear placeholder="请输入标题" maxLength={50} />)}
        </Item>

        <Item label="封面">
          {getFieldDecorator(
            "image_url",
            {}
          )(
            <Image
              size={{
                w: resource_type === 1 ? 114 : 268,
                h: resource_type === 1 ? 114 : 156,
              }}
              wrapClass={newClass}
              events={clipEvent()}
              delete={() => setImg({})}
              src={{ url: img ? img.path : "" }}
            />
          )}
        </Item>

        <Item label="信息提供" required className="select-org">
          {getFieldDecorator("org_name", {
            initialValue: selOrg[0] ? selOrg[0].org_name : "",
            rules: [
              {
                required: true,
                message: "请选择信息提供组织",
              },
            ],
          })(
            <Input
              disabled
              placeholder="这里是组织名称不可编辑，选择组织后自动展示"
            />
          )}
          <Button
            type="link"
            onClick={() => setOrgModalVisible(true)}
            disabled={oid !== root_oid}
          >
            选择组织
          </Button>
        </Item>

        <Item label={`${resource_type === 1 ? "文件" : "案例"}标签`}>
          {getFieldDecorator("tags", {
            initialValue: dataSource.tags ? dataSource.tags.split(",") : null,
            rules: [
              {
                validator: checkSelect,
              },
            ],
          })(
            <Checkbox.Group
              options={resource_type === 1 ? documentTagsList : caseTagsList}
            />
          )}
        </Item>

        <Item label="来源">
          {getFieldDecorator("sources", {
            initialValue: dataSource.sources ? dataSource.sources : null,
          })(<Input allowClear placeholder="请输入来源" maxLength={20} />)}
        </Item>

        <Item label="排序" className="sort-line">
          {getFieldDecorator("sort", {
            initialValue: dataSource.seq ? 2 : 1,
          })(
            <Radio.Group>
              <Radio value={1}>默认排序</Radio>
              <Radio value={2}>TOP100</Radio>
            </Radio.Group>
          )}
        </Item>

        {getFieldValue("sort") === 2 && (
          <Item
            label=" "
            colon={false}
            wrapperCol={{ span: 6 }}
            className="seq-line"
          >
            {getFieldDecorator("seq", {
              initialValue: dataSource.seq ? dataSource.seq : 1,
            })(
              <Select showSearch>
                {seq_number.map((item) => {
                  return (
                    <Select.Option value={item} key={item}>
                      {item}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
          </Item>
        )}

        <Item label=" " colon={false} className="annotation">
          排序规则：默认排序按照上传时间排序，Top100按照排序值正序排序，数字范围1-100
        </Item>

        <Item label="摘要">
          <InputTips max={150} text={getFieldValue("summary")}>
            {getFieldDecorator("summary", {
              initialValue: dataSource.summary ? dataSource.summary : "",
              rules: [
                {
                  max: 150,
                  message: "摘要最长150个字",
                },
              ],
            })(<Input.TextArea placeholder="请输入摘要" rows={4} />)}
          </InputTips>
        </Item>

        <Item label={`${resource_type === 1 ? "内容" : "案例正文"}`} required>
          <div>
            {getFieldDecorator("content", {
              initialValue: content,
              rules: [
                {
                  required: true,
                  message: `请输入${resource_type === 1 ? "内容" : "案例正文"}`,
                },
              ],
            })(<Input style={{ display: "none" }} />)}
            <Editor
              height={301}
              onRef={editor}
              onChange={(html) => {
                setContent(html);
                setFieldsValue({ content: html });
              }}
            />
          </div>
        </Item>

        {resource_type === 3 && (
          <Item label="案例附件">
            <div>
              {getFieldDecorator(
                "files",
                {}
              )(
                <FileUpload
                  // accept={".xls,.xlsx,.png,.jpg,.jpeg,.docx,.doc,.pdf,.mp4"}
                  limitSize={100}
                  children={
                    <div className="case-upload-btn">
                      <Icon type="plus" />
                      <div>点击上传案例文件</div>
                    </div>
                  }
                  uploadedList={files}
                  onChange={(f) => setFiles(f)}
                />
              )}
              <div className="annotation">
                支持上传JPG、PNG、PDF、Word、PPT、Excel、MP4格式文件，文件大小不超过100M
              </div>
              <ShowUploadFileType
                canPreview={true}
                data={files}
                updateState={(data) => setFiles(data)}
                isDelete={true}
              />
            </div>
          </Item>
        )}

        <Item label=" " colon={false} className="btn">
          <Button type="primary" onClick={() => onHandleSubmit()}>
            发布
          </Button>
          <Button type="default" onClick={() => history.goBack()}>
            返回
          </Button>
          <Button type="default" onClick={() => onHandleSubmit(true)}>
            预览
          </Button>
        </Item>
      </Form>
      <OrganizeModal {...orgModalProps} />
      {PreviewModalMemo}
    </div>
  );
};

export default Form.create()(AddCase);
