import React from "react";
import "./index.less";
import MeetingTaskListItem from "./meeting-task-list-item";
import PropTypes from "prop-types";

const MeetingTaskList = (props) => {
  const {
    noTextArea,
    canNotModifyBaseInfo,
    isPreviewDetail,
    isOnlyModifyPerson,
    dataSource,
    isDirectlyWrite,
    onAnsCntChange,
    onFileChange,
    onChange,
    onEdit,
    disabled
  } = props;
  // debugger;
  // console.log(dataSource);
  return (
    <div className="meeting-task-list">
      {/* 列表 */}
      {
        (dataSource && Array.isArray(dataSource)) &&
        dataSource.map((item, index) => {
          return (
            <MeetingTaskListItem
              noTextArea={noTextArea}
              // 唯一键值
              disabled={disabled}
              key={item.topic_id || item.key}
              
              canNotModifyBaseInfo={canNotModifyBaseInfo}
              isPreviewDetail={isPreviewDetail}
              isOnlyModifyPerson={isOnlyModifyPerson}
              isDirectlyWrite={isDirectlyWrite}
              item={item}
              index={index}
              onAnsCntChange={onAnsCntChange}
              onFileChange={onFileChange}
              onDelete={(item, index) => {
                // console.log("删除", item, index, dataSource);
                dataSource.splice(index, 1);
                onChange(dataSource);
              }}
              onEdit={onEdit}
            />
          );
        })
      }
    </div>
  )
}

MeetingTaskList.propTypes = {
  noTextArea: PropTypes.bool,
  onChange: PropTypes.func,
  dataSource: PropTypes.array,
  canNotModifyBaseInfo: PropTypes.bool,
  isDirectlyWrite: PropTypes.bool,
  isPreviewDetail: PropTypes.bool,
  isOnlyModifyPerson: PropTypes.bool,
  onAnsCntChange: PropTypes.func,
  onFileChange: PropTypes.func,
  onEdit: PropTypes.func
};

MeetingTaskList.defaultProps = {
  noTextArea: false,
  onChange: () => { },
  dataSource: [],
  canNotModifyBaseInfo: false,
  isDirectlyWrite: false,
  isPreviewDetail: false,
  isOnlyModifyPerson: false,
  onAnsCntChange: () => { },
  onFileChange: () => { },
  onEdit: () => { }
};

export default MeetingTaskList;