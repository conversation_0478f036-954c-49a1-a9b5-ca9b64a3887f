/**
 * user API 文件包含  用户数据相关的所有接口
 */

const { http, headers } = require("client/tool/axios");
import { host, loginHost } from "./config";
import axios from "axios";

const md5 = require("js-md5");

//登录
export const login = ({ password, phone, uuid, captcha, flag }) => {
  return axios.post(
    `${loginHost}/login`,
    {
      phone: phone,
      password: password,
      uuid,
      captcha,
      flag,
    },
    { headers: headers() }
  );
  // update dingxing 2018-05-03 14:58 调用node登录接口路由
};

// 政务云钉钉登录
export const zwDDLogin = (params) => {
  return http.get(`${loginHost}/third/check/pc`, params, {
    headers: headers(),
  });
};

//选择组织
export const chooseOrg = ({ user_id, name, oid, type, token }) => {
  return axios.post(
    `${loginHost}/choose`,
    {
      user_id,
      name: encodeURIComponent(name),
      oid,
      type,
      token,
    },
    { headers: headers() }
  );
};

export const findBirthdayByPhone = (phone) => {
  return http.get(`${loginHost}/find-birthday-by-phone?phone=${phone}`);
};
export const chooseO = () => {
  return http.post(`${host}/choose`);
};
// 获取系统管理员编辑添加时的按钮
export const getSystemMenu = () => {
  return http.get(`${host}/uc/user/getSys`);
};

/**
 * // 新建管理员账号
 * @param { Number }    scope        管理范围  1-全单位 2-指定部门
 * @param { String }    user_id        用户ID
 * @param { Array }    dep            部门ID列表
 * @param { Array }    menu        菜单ID列表
 */
export const addSystem = ({ scope, user_id, dep, menu, role_id }) => {
  return http.post(`${host}/uc/user/addSys`, {
    scope,
    user_id,
    dep,
    menu,
    role_id,
  });
};

//查询组件中部门的人员
export const getUserList = ({ page = "", name = "", depId = "" }) => {
  return http.get(`${host}/uc/user/user-list`, { page, name, depId });
};
//获取部门下拉列表
export const getDepList = () => {
  return http.get(`${host}/uc/dep/list`);
};

//人员管理中查询员工
export const getAllUserList = ({
  page,
  filterCondition = {
    name: "",
    phone: "",
    status: "",
    tag: "",
    departmentId: "",
  },
}) => {
  let { name, phone, status, tag, departmentId } = filterCondition;
  return http.get(`${host}/uc/user/getUserList`, {
    page,
    name,
    phone,
    status,
    tag,
    departmentId,
  });
};

//获取人员标签列表
export const getTagList = () => {
  return http.get(`${host}/uc/tag/getUserTag`);
};

// 获取系统管理员列表
export const getSystemList = ({ page = "", name = "" }) => {
  return http.get(`${host}/uc/user/sysList`, { page, name });
};

// 移除管理员
export const removeAdmin = ({ user_id = "" }) => {
  return http.post(`${host}/uc/user/cancel/${user_id}`);
};

// 获取编辑详情
export const getEditDetails = ({ userId = "" }) => {
  return http.get(`${host}/uc/user/edit`, { userId });
};

// 通过名字查询管理员
export const searchAdminListByName = ({ name = "" }) => {
  return http.get(`${host}/uc/user/getUser`, { name });
};

// 通过code获取数据字典的相应数据
export const getcodeList = ({ code = "" }) => {
  return http.get(`${host}/uc/op/list`, { code });
};

//手机号码监测重复
export const checkPhone = ({ phone = "" }) => {
  return http.get(`${host}/uc/user/check-phone`, { phone });
};
export const addCheckPhone = (phone) => {
  return http.get(`${host}/uc/user/add-check-phone`, { phone });
};
//身份证监测
export const checkRealName = (data) => {
  return http.get(`${host}/check-real-name`, data);
};
/**
 * 移交系统管理员
 * @param { String }    id    原用户id
 * @param { String }    nid        新用户id
 */
export const changeSystemAdminUser = ({ id = "", nid = "" }) => {
  return http.post(`${host}/uc/user/change`, { id, nid });
};

//获取角色列表
export const getRoleList = ({ name = "", page = "" }) => {
  return http.get(`${host}/uc/role/list`, { name, page });
};
//获取权限列表
export const getAllotList = () => {
  return http.get(`${host}/uc/user/allotList`);
};

//新增角色
export const addRole = ({ name = "", role_type = "", menuIds = "" }) => {
  return http.post(`${host}/uc/role/addRole`, { name, role_type, menuIds });
};

//修改角色
export const updateRole = ({
  name = "",
  role_type = "",
  menuIds = "",
  role_id = "",
}) => {
  return http.post(`${host}/uc/role/updateRole`, {
    role_id,
    name,
    role_type,
    menuIds,
  });
};

//修改员工权限
export const updateUserRole = ({ user_id = "", role_id = "", menu = "" }) => {
  return http.post(`${host}/uc/user/updateUserRole`, {
    user_id,
    role_id,
    menu,
  });
};
//删除角色
export const deleteRole = ({ id = "" }) => {
  return http.delete(`${host}/uc/role/delRole/${id}`);
};
//用户离职
export const leaveUser = ({ user_id = "" }) => {
  return http.post(`${host}/uc/user/leave/${user_id}`);
};
//通过角色id查找列表
export const getmenulistByid = ({ roleId = "" }) => {
  return http.get(`${host}/uc/role/menu-list`, { roleId });
};
//发送验证码
export const sendPhone = ({ phone }) => {
  return http.post(`${host}/uc/user/send?phone=${phone}`);
};
//发送验证码到新手机，不做手机是否存在校验，传递标志flag=1
export const sendNewPhone = ({ phone }) => {
  return http.post(`${host}/uc/user/send?phone=${phone}&flag=1`);
};

//验证验证码  修改手机号
export const updatePhone = ({ phone, content, uuid }) => {
  return http.post(`${host}/uc/user/update-phone`, { phone, content, uuid });
};
//验证验证码
export const updatePhoneCheck = ({ phone, content, uuid }) => {
  return http.post(`${host}/uc/user/check`, { phone, content, uuid });
};

//修改密码
export const updatePwd = ({ phone, password, repeat_password, uuid }) => {
  return http.post(`${host}/uc/user/update-pwd`, {
    phone,
    password: md5(password),
    repeat_password: md5(repeat_password),
    uuid,
  });
};
//新增人员
export const insertInfo = ({
  name,
  phone,
  cert_type,
  cert_number,
  job_number,
  position,
  email,
  dep,
  entry_date,
  tag_id,
  gender,
  census_type,
  nationality,
  marriage,
  education,
  political_type,
}) => {
  return http.post(`${host}/uc/user/addUser`, {
    name,
    phone,
    cert_type,
    cert_number,
    job_number,
    position,
    email,
    dep,
    entry_date,
    tag_id,
    gender,
    census_type,
    nationality,
    marriage,
    education,
    political_type,
  });
};

//修改人员
export const updateInfo = ({
  user_id,
  name,
  phone,
  cert_type,
  cert_number,
  job_number,
  position,
  email,
  dep,
  entry_date,
  tag_id,
  gender,
  census_type,
  nationality,
  marriage,
  education,
  political_type,
  flag,
}) => {
  return http.post(`${host}/uc/user/updateUser`, {
    user_id,
    name,
    phone,
    cert_type,
    cert_number,
    job_number,
    position,
    email,
    dep,
    entry_date,
    tag_id,
    gender,
    census_type,
    nationality,
    marriage,
    education,
    political_type,
    flag,
  });
};
//查询所有部门
export const getDepAll = () => {
  return http.get(`${host}/uc/dep/all`);
};

//选择查询的员工
export const findUser = ({ id }) => {
  return http.get(`${host}/uc/user/info`, { id });
};

//移交组织系统管理员
export const transferAdminHandler = ({ data }) => {
  return http.post(`${host}/org/trans-manager`, data);
};

//验证当前移交管理员对象是否为本组织管理员
export const tansManagerCheck = ({ user_id }) => {
  return http.get(`${host}/org/trans-manager-check`, { user_id });
};

//************-根据区域ID获取顶级党组织ID
export const getRootOrgId = (org_type) => {
  return http.get(`${host}/org/get-root-org-id-by-region-id`, { org_type });
};

/**
 * 解锁用户
 * @param phone 需要解锁的电话号码
 * @returns response
 */
export const unlockUser = ({ phone }) => {
  return http.get(`${loginHost}/unlock`, { phone });
};

export const getThirdUuid = (params) => {
  return http.get(`${loginHost}/third/code`, params);
};
export const ssoLogin = (params) => {
  return http.get(`${loginHost}/third/login`, params);
};
