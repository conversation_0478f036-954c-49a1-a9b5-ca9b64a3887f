.AsmentSta {
  .h1Header {
    width: 100%;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: bold;
    font-family: "FZLTHK--GBK1-0";
    font-weight: bold;
    color: rgba(0, 0, 1, 1);
    background: rgba(247, 248, 249, 1);
    border-radius: 5px 5px 0px 0px;
    padding: 0 25px;
    box-sizing: border-box;
    margin: 0;
  }
  .body {
    color: rgba(90, 90, 91, 1);
    padding: 24px 32px;
    .asmentGrayBg {
      background: rgba(247, 248, 249, 1);
    }
    .line-sty {
      border: 1px solid rgba(228, 228, 228, 1);
      color: rgba(228, 228, 228, 1);
      margin: 20px 0;
    }
    .operationHeader {
      .formWrap {
        // .input-style {
        //   width: 150px;
        // }
        // .range-picker-style {
        //   width: 330px;
        // }
        display: flex;
        align-items: center;
        justify-content: space-between;
        .s-year {
          // width: 121px;
          & input {
            background: rgba(247, 248, 249, 1);
          }
        }
        .jidu {
          width: 211px;
          background: rgba(247, 248, 249, 1);
        }
        .jidu>.ant-select-selection {
          background: rgba(247, 248, 249, 1);
        }
        .last-form-item {
          // margin-right: 0px;
          // right: 0px;
          // position: absolute;
        }
        .org-name {
          // margin-left: 69px;
          display: flex;
          align-content: center;
          align-items: center;
          justify-content: space-around;
        }
        .org-name>label {
          font-size: 20px;
          font-family: MicrosoftYaHei;
          font-weight: 400;
          color: rgba(51, 51, 51, 1);
        }
        .org-name button {
          margin-left: 22px;
        }
        .org-name input {
          width: 199px;
          background: rgba(247, 248, 249, 1);
        }
        .rest-btn {
          // margin-left: 14px;
        }
      }
      margin-bottom: 20px;
      .addMeeting {
        margin-bottom: 10px;
        .addMeetingBtn {
          width: 160px;
          height: 40px;
          font-size: 16px; // background: #F46E65;
          border: none; // margin-right: 15px;
        }
      }
      .queryBtn {
        width: 106px;
        border-radius: 7px;
        font-size: 15px;
        font-family: MicrosoftYaHei;
        font-weight: 400;
        height: 40px;
        /* padding: 0 20px; */
        letter-spacing: 8px;
        text-align: center;
      }
    }
    .operationHeader input {
      width: 127px;
      background: rgba(247, 248, 249, 1);
    }
    .status_1_5_6 {
      color: #f19149;
    }
    .status_3_4 {
      color: #00b7ee;
    }
    .status_7 {
      color: #22ac38;
    }
    .status_8 {
      color: #f46e65;
    }
    .status_9 {
      color: #999999;
    }
    .color_333 {
      color: #333333;
    }
    .bg_f46 {
      background-color: #f46e65;
    }
    .font_fff {
      color: #fff;
    }
    .operatorTable {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
    }
    .operatorWrap {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
    }
  }
}

.flow-log-content {
  height: 422px;
  overflow: auto;
  margin: 46px;
}

.flow-log-close-btn-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-bottom: 40px;
  button {
    width: 115px;
    height: 36px; // background: #f46e65;
    // border: none;
  }
}

.lineEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  max-width: 100px;
}

// .ant-table-tbody>tr>td {
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   text-align: center; // max-width: 100px;
// }

.assessment-org-detail-container {
  padding: 30px;
}
