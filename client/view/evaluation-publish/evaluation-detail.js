import SearchHeader from "client/components/search-header";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, message, Switch } from "antd";
import DropText from "client/view/evaluation-publish/create-evaluation/components/DropText";
import {
  getEvalDetail,
  getInternalQrDetail,
  setInternalQrcode,
} from "apis/evaluation-publish";
import { getParam } from "tool/util";
import ShiguanOrgTable from "client/view/evaluation-publish/create-evaluation/ShiguanOrgTable";
import ShiguanOrgTableDetail from "client/view/evaluation-publish/evaluation-detail/ShiguanOrgTableDetail";
import ShiguanUserTableDetail from "client/view/evaluation-publish/evaluation-detail/ShiguanUserTableDetail";
const { TabPane } = Tabs;

export default function EvaluationDetail(props) {
  const { history, location } = props;
  var param = getParam();

  const [loading, setLoading] = React.useState(true);
  const [type, setType] = React.useState(true);
  const [datasourceList, setDatasourceList] = React.useState([]);
  const [datasourcebList, setDatasourcebList] = React.useState([]);
  const loadData = () => {
    getInternalQrDetail(param).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        setDatasourceList(data.org_list);
        setDatasourcebList(data.info_list);
        setLoading(false);
      }
    });
  };
  React.useEffect(() => {
    loadData();
  }, []);
  const onUpdateData = (r) => {
    console.log("数据更新了", r);
    let param = { pms_evaluation_qrcode_id: r["pms_evaluation_qrcode_id"] };
    if (r.updatetype == "enable") {
      param["enable"] = r.enable ? 1 : 2;
    }
    if (r.updatetype == "sketch") {
      param["sketch"] = r.sketch ? 1 : 2;
    }
    if (r.updatetype == "relationship") {
      param["relationship"] = r.relationship;
    }

    setInternalQrcode(param).then((res) => {
      message.success("更新成功");
      loadData();
    });
  };

  return (
    <div>
      <SearchHeader
        title={param.leader_name}
        onBack={() => {
          history.goBack();
        }}
      />

      <div>
        <Spin spinning={loading}>
          <Tabs>
            <Ant.Tabs.TabPane tab="班子名单" key="BZMD">
              {datasourceList.map((item, index) => {
                return (
                  <ShiguanOrgTableDetail
                    key={item.sequence}
                    index={index}
                    record={item}
                    onUpdateData={onUpdateData}
                  />
                );
              })}
            </Ant.Tabs.TabPane>
            <Ant.Tabs.TabPane tab="干部名单" key="GBMD">
              {datasourcebList.map((item, index) => {
                return (
                  <ShiguanUserTableDetail
                    key={item.sequence}
                    index={index}
                    record={item}
                    onUpdateData={onUpdateData}
                  />
                );
              })}
            </Ant.Tabs.TabPane>
          </Tabs>
        </Spin>
      </div>
    </div>
  );
}
