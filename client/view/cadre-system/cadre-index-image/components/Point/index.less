.coordinate-chart {
  // 图表容器样式
  .echarts-box {
    .echarts {
      width: 100%;
      height: 385px;
    }
  }

  // Select 组件相关样式
  :global {
    .ant-select-arrow {
      width: 16px;
      height: 16px;
    }
  }

  .select-icon {
    margin-left: 4px;
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("../../images/select.png") no-repeat center / 100%;
  }

  :global {
    .ant-select-selection-item {
      min-width: 130px;
      color: #000000;
      font-size: 20px;
      text-align: right;
    }
  }

  .pupop-custom {
    width: 200px;
  }

  .type-selection {
    padding-right: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// tooltip 样式
.tooltips-box {
  .data-name {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .data-item {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }
}
