

import React, { Component } from "react";

import PropTypes from "prop-types";

import { Button, Modal, Form, Table, Input, Tabs, Select, DatePicker, Cascader, Divider, message } from "antd";

import InputWrap from 'components/layout/input-wrap';

import PushTo from './components/push-to';

import PushResult from "./components/push-result";

import DatePickerLocale from 'config/DatePickerLocale.json';
import { activityPush } from "apis/activity";
import StatusIcon from "components/status-icon";

import Utils from "./utils";

import { activitysTypes, statusTypes } from "./dict";

const confirm = Modal.confirm;

// import PushResult from "./components/push-result";
//


const propTypes = {
    activitysMatrix: PropTypes.object,
    dispatch: PropTypes.func,
    form: PropTypes.object,
    editActivity: PropTypes.func,
    isMyActivity: PropTypes.func,
    showColumnSelector: PropTypes.func
};

const defaultProps = {

};

export default Form.create({
    mapPropsToFields(props) {
        const { table_1 } = props.activitysMatrix;
        return {
            type: Form.createFormField({ ...table_1.type }),
            status: Form.createFormField({ ...table_1.status }),
            keyword: Form.createFormField({ ...table_1.keyword }),
        };
    },
    onFieldsChange(props, fields) {

        let fieldsValues = {};
        for (var k in fields) {
            if (fields.hasOwnProperty(k)) {
                fieldsValues[k] = fields[k].value;
            }
        }

        const { table_1 } = props.activitysMatrix;

        props.dispatch({
            type: 'activitysMatrix/save',
            payload: { table_1: Object.assign({}, table_1, fieldsValues) }
        });
    }
})(Object.assign(class SendList extends Component {

    constructor(props) {
        super(props);
        this.state = {
            current: null,
            pushtoDialogVisible: false,
            pushResultsVisible: false,
            disabledList: [],
            rangeType: 0, //推送范围
            visibleType: 1 //可见性 1：所有组织可见；2：接收组织可见
        }
        this.searchSubmit = this.searchSubmit.bind(this);
    }


    searchSubmit(e) {
        e.preventDefault();
        const { dispatch, activitysMatrix } = this.props;

        dispatch({ type: 'activitysMatrix/getMatrixList' })

        return false;
    }

    pushto(data) {
        const self = this;
        const { dispatch, activitysMatrix } = this.props;
        const { currentData, rangeType, visibleType, selectedData } = data;
        // console.log(currentData, selectedData)
        dispatch({
            type: "activitysMatrix/pushto",
            payload: {
                activity_id: currentData.activity_id,
                action: 1,  //1：发起/修改 2：撤回
                is_org: rangeType, //是否推送给指定组织
                orgs: selectedData, //组织id数组	selectedData.map(org=>org.org_id),
                visibility: visibleType // 可见性	1：所有组织可见(矩阵)；2：接收组织可见
            }
        }).then((data) => {
            const _data = data.data
            if (_data.code != 0) {
                console.log(_data.message)
                return message.error(_data.message)
            }
            message.success(_data.messgae)
            self.setState({
                pushtoDialogVisible: false
            })
        }).catch(e => {
            self.setState({
                pushtoDialogVisible: false
            })
        })

    }

    pushCancel(row) {
        const { dispatch, activitysMatrix } = this.props;
        const { activity_id } = row;

        dispatch({
            type: "activitysMatrix/pushto",
            payload: {
                activity_id,
                action: 2,  //1：发起/修改 2：撤回
                is_org: 0,
                orgs: [],
                visibility: 1
            }
        });
    }

    render() {

        const { props, searchSubmit } = this;
        const { dispatch, activitysMatrix, editActivity, isMyActivity, showColumnSelector } = props;
        const { getFieldDecorator } = props.form;
        const tableData = activitysMatrix.table_1;
        const { type, status, keyword } = tableData;

        let TypeChange = () => { },
            stateChange = () => { },
            crownChange = () => { },
            changePage = (pagegation) => {
                // console.log('pagegation--:',pagegation)
                const { dispatch } = this.props,
                    { table_1 } = props.activitysMatrix,
                    { pageSize, current: page } = pagegation;
                dispatch({ type: 'activitysMatrix/getMatrixList', playload: Object.assign(table_1, { pageSize, page }) })
            },
            typelist = [],
            statelist = [],
            crownList = [],
            dataList = (tableData.data || []), //Utils.testData,
            listLoading = activitysMatrix.loading;

        // const isMyActivity = ()=>{};

        const pageNew = {
            pageNum: tableData.page,
            pageSize: tableData.pagesize,
            total: tableData.total
        };

        const formItemLayout = {
            labelCol: { span: 8, offset: 1 },
            wrapperCol: { span: 10 }
        },
            Option = Select.Option,
            //查询结果的表格header
            resultColumns = [{
                // title: <center>互动标题</center>,
                title: <center>互动标题</center>,
                dataIndex: 'title',
                key: 'title',
                render: (text) => {
                    try {
                        return decodeURIComponent(text);
                    } catch (e) {
                        return unescape(text);
                    }
                }
                // render: (text, row) => (
                //     <span>
                //         <span>{text}</span>&nbsp;&nbsp;
                //         {/*<a href='javascript:void (0)' onClick={() => mobileOverview(row)}>手机预览</a>
                //          <Divider type="vertical" />
                //         <a href='javascript:void (0)'>网站预览</a> */}
                //     </span>)
            }, {
                title: '互动类型',
                dataIndex: 'type',
                key: 'type',
                width: 100,
                align: "center",
                render: Utils.columns.type
            }, {
                title: '互动状态',
                dataIndex: 'status',
                key: 'status',
                width: 110,
                align: "center",
                render: Utils.columns.status
            }, {
                title: "互动时间",
                dataIndex: 'time',
                key: 'time',
                width: 200,
                align: "center",
                render: Utils.columns.time
            }, {
                title: '推送时间',
                dataIndex: 'push_time',
                key: 'push_time',
                width: 175,
                align: 'center',
                render: text => {
                    return text ? text.substr(0, text.lastIndexOf('.')) : '';
                }
            }, {
                title: '主办单位',
                dataIndex: 'org_name',
                key: 'org_name',
                width: 200,
                align: "center",
                render: text => {
                    try {
                        return decodeURIComponent(text);
                    } catch (e) {
                        return unescape(text);
                    }
                }
            }, {
                title: <center>操作</center>,
                width: 240,
                align: 'center',
                render: (text, row) => (
                    /*
                    *   "status": "int|互动状态（1：互动中；2：审批中；3：未通过；4：已结束；5：未开始）"
                    *   "status_name": "string|互动状态中文表示"
                    *   编辑和删除操作者（即当前登录用户）与发起互动者身份须一致
                    *   否则列表页面提示：当前互动不是你发起的，无法编辑/删除
                    *   互动管理列表:
                    *   1.互动中删除，表示互动关闭；常规编辑update
                    *   2.审批中删除，审批被撤回；撤回原有审批，重新发起一次（后台处理调用undo）
                    *   3.未通过，同1；常规编辑update
                    *   4.已结束，同1；常规编辑update
                    *   5.未开始，同1；重新发起一次（后台处理undo）
                    *
                    * */
                    <span>
                        {/* {
                        isMyActivity(row.create_user) && '本人'
                    } */}
                        <a href="javascript:void (0)" onClick={() => editActivity(row)}>{
                            //显示编辑功能，当前登录用户是互动发布人
                            //当不是线下互动时，不是互动中和已结束的状态，可以编辑
                            //当时线下互动，只要不是已结束状态，可以编辑
                            isMyActivity(row.create_user) &&
                                (
                                    (row.type !== 4 && row.status !== 1 && row.status !== 4) ||
                                    (row.type === 4 && row.status !== 4)
                                ) ? '编辑' : '查看'
                        }</a>
                        <Divider type="vertical" />
                        <a href="javascript:void(0)" onClick={() => {
                            // todo: 推送
                            activityPush(row.activity_id, { is_page: 0 })
                                .then(data => {
                                    const _data = data.data
                                    if (_data.code != 0) {
                                        message.error(_data.message)
                                    }
                                    const { orgs:orgIds, org, visibility } = _data.data;
                                    

                                    this.setState({
                                        current: row,
                                        disabledList: orgIds || [],
                                        pushtoDialogVisible: true,
                                        //推送配置信息保存到state，方便传入到pushTo组件
                                        rangeType: org>>0,  //推送范围 是否推送给指定组织 false=>0 true => 1
                                        visibleType: visibility //可见性 1：所有组织可见；2：接收组织可见
                                    })
                                })
                        }}>推送</a>
                        <Divider type="vertical" />
                        <a href="javascript:void(0)" onClick={() => {
                            const self = this;
                            confirm({
                                iconType: 'exclamation-circle',
                                title: '提示',
                                content: '撤回后，接收组织的接收列表和互动矩阵中将不再 展示！',
                                onOk() {
                                    self.pushCancel(row)
                                },
                                onCancel() { },
                            });

                        }}>撤回推送</a>
                        <Divider type="vertical" />
                        <a href="javascript:void(0)" onClick={() => {
                            this.setState({
                                current: row,
                                pushResultsVisible: true
                            })
                        }}>推送结果</a>
                    </span>
                )
            }]//,
        // rowSelection = {
        //     onChange: (selectedRowKeys, selectedRows) => {
        //         saveSelectActivity(selectedRows);
        //         // console.log(`选择的keys: ${selectedRowKeys}`, '选择的一行的信息: ', selectedRows);
        //     }
        //     /*getCheckboxProps: record => ({
        //         disabled: record.name === 'Disabled User',
        //         name: record.name
        //     })*/
        // };

        return (
            <div>
                <InputWrap>
                    <Form layout='inline' onSubmit={searchSubmit} style={{ margin: 0 }}>
                        <Form.Item label='互动类型' {...formItemLayout}>
                            {getFieldDecorator('type', {
                                initialValue: type,
                                rules: []
                            })(
                                <Select
                                    onChange={TypeChange} style={{ width: 169 }} placeholder="全部"
                                >
                                    <Option key={0}>全部</Option>
                                    {Object.keys(activitysTypes).map(key => (
                                        <Option key={key}>{activitysTypes[key]}</Option>
                                    ))
                                    }
                                </Select>
                            )}
                        </Form.Item>
                        <Form.Item label='互动状态' {...formItemLayout}>
                            {getFieldDecorator('status', {
                                initialValue: status,
                                rules: []
                            })(
                                <Select
                                    onChange={stateChange} style={{ width: 169 }} placeholder="全部"
                                >
                                    <Option key={0}>全部</Option>
                                    {[1, 4, 5].map(key => (
                                        <Option key={key}>{statusTypes[key]}</Option>
                                    ))
                                    }
                                </Select>
                            )}
                        </Form.Item>
                        {/*
                  <Form.Item label='所在栏目' {...formItemLayout} labelCol={{ span: 11 }}>
                      {getFieldDecorator('cid')(
                          <Cascader options={crownList} onChange={crownChange} style={{ width: 169 }} placeholder="全部"
                              changeOnSelect />
                      )}
                  </Form.Item>
                  */}
                        <Form.Item
                            // label='互动标题'
                            label='互动标题'
                            {...formItemLayout}>
                            {getFieldDecorator('keyword', {
                                initialValue: keyword,
                                rules: [{
                                    required: false,
                                    // message: '请输入互动标题'
                                    message: '请输入互动标题'
                                }, {
                                    max: 30, message: '最多只能输入30个字'
                                }]
                            })(
                                <Input
                                    // placeholder='互动标题'
                                    placeholder='互动标题'
                                    style={{ width: 200 }} maxLength={30} />
                            )}

                        </Form.Item>
                        {/*
                  <Form.Item {...formItemLayout} wrapperCol={{ offset: 6 }}>
                      {getFieldDecorator('timepoint', {
                          rules: [{ required: false, message: '' }]
                      })(
                          <DatePicker
                              showTime
                              format="YYYY-MM-DD HH:mm:ss"
                              placeholder="互动时间"
                              style={{ width: 169 }}
                              locale={DatePickerLocale}
                          />
                      )}

                  </Form.Item>
                  */}
                        <Form.Item wrapperCol={{ offset: 16 }}>
                            <Button htmlType="submit" type="primary">查询</Button>
                        </Form.Item>
                    </Form>
                </InputWrap>
                {
                    this.state.pushtoDialogVisible ?
                        <PushTo
                            title="互动"
                            currentData={this.state.current}
                            rangeType={this.state.rangeType}
                            visibleType={this.state.visibleType}
                            visible={this.state.pushtoDialogVisible}
                            onSave={this.pushto.bind(this)}
                            pushing={activitysMatrix.pushing}
                            disabledList={this.state.disabledList}
                            onCancel={() => this.setState({ pushtoDialogVisible: false })} />
                        : null
                }


                <PushResult
                    title="互动"
                    currentData={this.state.current}
                    visible={this.state.pushResultsVisible}
                    onChange={(data, page, pagesize) => this.props.dispatch({
                        type: "activitysMatrix/pushResults",
                        payload: {
                            activity_id: data.activity_id,
                            page,
                            pagesize
                        }
                    })}
                    onCancel={() => this.setState({ pushResultsVisible: false })} />

                {/*显示的查询结果*/}
                <Table rowKey={'activity_id'} dataSource={dataList} columns={resultColumns}
                    pagination={{
                        pageSize: pageNew.pageSize,
                        total: pageNew.total,
                        current: pageNew.pageNum
                    }}
                    bordered loading={listLoading}
                    //rowSelection={rowSelection}
                    onChange={changePage}
                />

            </div>
        )

    }

}, { propTypes, defaultProps }))
