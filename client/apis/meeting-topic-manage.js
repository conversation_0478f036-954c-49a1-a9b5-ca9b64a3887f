/**
 * 
 * 会议议题管理, 接口
 * 
 */

const { http } = require('client/tool/axios');
import { mtmHost } from './config';

// 议题列表 all
export const queryAllTopicList = (data) => {
  return http.get(`${mtmHost}/topic/list`, data);
}

// 议题列表 page
export const queryTopicList = (data) => { 
  return http.get(`${mtmHost}/topic/page`, data);
}

// 新增议题
export const addTopic = (data, user_id, org_id) => {
  return http.post(`${mtmHost}/topic/add?user_id=${user_id}&org_id=${org_id}`, data);
}

// 修改议题
export const updateTopic = (data) => {
  return http.post(`${mtmHost}/topic/update`, data);
}

// 议题详情
export const topicDetail = (data) => {
  return http.get(`${mtmHost}/result/topic/detail`, data);
}

// 删除议题
export const deleteTopic = (data) => {
  return http.delete(`${mtmHost}/topic/del/${data}`);
}
// 查询任务下的组织
export const queryTaskOrgList = (data) => {
  return http.get(`${mtmHost}/topic/taskOrgList/`, data);
}
// 3000202100906-提交任务
export const getResultTopicSubmit = (data) => {
  return http.post(`${mtmHost}/result/topic/submit`, data);
}